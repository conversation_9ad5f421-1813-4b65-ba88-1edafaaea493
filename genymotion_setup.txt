GENYMOTION - <PERSON><PERSON><PERSON><PERSON> ALTERNATIVA PARA EMULADOR
===========================================

O Genymotion é um emulador Android profissional que funciona melhor que o padrão:

1. BAIXAR GENYMOTION:
   - Site: https://www.genymotion.com/
   - Versão: Genymotion Desktop (gratuita para uso pessoal)
   - Requer conta gratuita

2. INSTALAR:
   - Baixar e instalar Genymotion
   - Criar conta gratuita
   - Fazer login no aplicativo

3. CRIAR DISPOSITIVO VIRTUAL:
   - Clicar em "+"
   - Escolher: Google Pixel 3a
   - Android version: 11.0 (API 30)
   - Configurar nome: "AppiumTest"

4. CONFIGURAÇÕES RECOMENDADAS:
   - RAM: 2048 MB
   - Processors: 2
   - Graphics: Software rendering
   - ADB: Use Genymotion Android tools

5. INICIAR DISPOSITIVO:
   - Clicar em "Start"
   - Aguardar boot completo
   - Verificar com: adb devices

6. CAPABILITIES PARA APPIUM:
   {
     "platformName": "Android",
     "automationName": "UiAutomator2",
     "deviceName": "AppiumTest",
     "appPackage": "br.com.pactosolucoes.treino",
     "appActivity": ".MainActivity"
   }

VANTAGENS DO GENYMOTION:
✅ Funciona com Hyper-V
✅ Mais rápido que emulador padrão
✅ Interface amigável
✅ Suporte profissional
✅ Integração fácil com Appium
✅ Não requer configurações complexas

DESVANTAGENS:
❌ Requer conta (gratuita)
❌ Versão gratuita tem limitações
❌ Download adicional

RECOMENDAÇÃO: Use Genymotion - é a solução mais confiável!
