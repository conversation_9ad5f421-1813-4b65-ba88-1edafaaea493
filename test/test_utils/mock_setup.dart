import 'package:get_it/get_it.dart';
import 'package:mockito/mockito.dart';

import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';

/// Utility class to setup common mocks for tests
class MockSetup {
  static void setupGetIt() {
    GetIt.instance.reset();
  }

  static void tearDownGetIt() {
    GetIt.instance.reset();
  }

  /// Creates a mock ControladorApp with default behaviors
  static MockControladorApp createMockControladorApp() {
    final mock = MockControladorApp();

    // Setup default behaviors
    when(() => mock.statusCarregandoAcademias).thenReturn(ServiceStatus.Done as ServiceStatus Function());
    when(() => mock.desabilitarNovoFluxoLogin).thenReturn(false as bool Function());
    when(() => mock.textopesquisa).thenReturn('' as String Function());
    // ignore: cast_from_null_always_fails
    when(() => mock.mClienteAppSelecionado).thenReturn(null as ClienteApp? Function());

    return mock;
  }

  /// Creates a mock ClienteApp with test data
  static ClienteApp createMockClienteApp({
    List<EmpresaApp>? empresaApps,
    String? nomeDoApp,
    String? corPadrao,
  }) {
    return ClienteApp(
      empresaApps: empresaApps ?? [],
      nomeDoApp: nomeDoApp ?? 'Test App',
      corPadrao: corPadrao ?? '#FF0000',
    );
  }

  /// Creates a list of mock EmpresaApp for testing
  static List<EmpresaApp> createMockEmpresaApps(int count) {
    return List.generate(count, (index) => EmpresaApp(
      chave: 'chave_$index',
      nome: 'Empresa $index',
    ));
  }
}

// Mock classes - these would normally be generated by mockito
class MockControladorApp extends Mock implements ControladorApp {}
class MockControladorCliente extends Mock implements ControladorCliente {}
