CONFIGURAÇÕES PARA EMULADOR COM HYPER-V ATIVO
==============================================

1. CRIAR EMULADOR NO ANDROID STUDIO:
   - Device: Pixel 3a
   - API Level: 30 (Android 11)
   - ABI: x86_64
   - Graphics: Software - GLES 2.0

2. CONFIGURAÇÕES AVANÇADAS:
   - RAM: 2048 MB
   - Internal Storage: 2048 MB
   - SD Card: 512 MB
   - Use Host GPU: NO
   - Multi-Core CPU: 2

3. COMANDO PARA INICIAR (PowerShell):
   & "$env:ANDROID_HOME\emulator\emulator.exe" -avd [NOME_DO_AVD] -gpu swiftshader_indirect -memory 2048 -no-snapshot-save -no-audio -netdelay none -netspeed full

4. CAPABILITIES PARA APPIUM:
   {
     "platformName": "Android",
     "automationName": "UiAutomator2",
     "deviceName": "Android Emulator",
     "appPackage": "br.com.pactosolucoes.treino",
     "appActivity": ".MainActivity",
     "newCommandTimeout": 300,
     "autoGrantPermissions": true
   }

5. ALTERNATIVA - USAR DISPOSITIVO FÍSICO:
   - Conectar celular via USB
   - Ativar modo desenvolvedor
   - Ativar depuração USB
   - Verificar com: adb devices

NOTA: O problema atual é que o Hyper-V está interferindo com a virtualização do emulador Android.
A solução mais confiável é usar um dispositivo físico.
