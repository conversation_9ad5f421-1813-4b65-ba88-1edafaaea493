# Script para baixar e instalar Appium Inspector
Write-Host "🔍 INSTALANDO APPIUM INSPECTOR" -ForegroundColor Green
Write-Host "=" * 50

# URL do download mais recente
$url = "https://github.com/appium/appium-inspector/releases/download/v2025.8.2/Appium-Inspector-2025.8.2-windows-x64.exe"
$outputFile = "Appium-Inspector-2025.8.2.exe"

Write-Host "📥 Baixando Appium Inspector v2025.8.2..." -ForegroundColor Yellow

try {
    # Usar curl se disponível (mais confiável)
    if (Get-Command curl -ErrorAction SilentlyContinue) {
        Write-Host "Usando curl para download..."
        curl -L -o $outputFile $url
    } else {
        # Fallback para Invoke-WebRequest com configurações especiais
        Write-Host "Usando PowerShell para download..."
        $ProgressPreference = 'SilentlyContinue'
        [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
        Invoke-WebRequest -Uri $url -OutFile $outputFile -UseBasicParsing -TimeoutSec 300
    }
    
    if (Test-Path $outputFile) {
        $fileSize = (Get-Item $outputFile).Length / 1MB
        Write-Host "✅ Download concluído! Tamanho: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Green
        
        Write-Host "🚀 Iniciando instalação..." -ForegroundColor Yellow
        Start-Process -FilePath $outputFile -Wait
        
        Write-Host "🎉 Appium Inspector instalado com sucesso!" -ForegroundColor Green
        Write-Host ""
        Write-Host "💡 Para usar o Appium Inspector:" -ForegroundColor Cyan
        Write-Host "1. Certifique-se que o Appium está rodando (npx appium --port 4723)"
        Write-Host "2. Abra o Appium Inspector"
        Write-Host "3. Configure Remote Host: localhost"
        Write-Host "4. Configure Remote Port: 4723"
        Write-Host "5. Configure Remote Path: /"
        Write-Host "6. Adicione suas capabilities e conecte!"
        
        # Limpar arquivo de download
        Remove-Item $outputFile -Force
        Write-Host "🧹 Arquivo de instalação removido" -ForegroundColor Gray
        
    } else {
        Write-Host "❌ Falha no download" -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "❌ Erro durante download/instalação: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Alternativas:" -ForegroundColor Yellow
    Write-Host "1. Baixe manualmente de: https://github.com/appium/appium-inspector/releases"
    Write-Host "2. Ou use o Appium Inspector via web: http://localhost:4723/inspector"
    exit 1
}

Write-Host ""
Write-Host "🎯 Configuração recomendada para seus testes:" -ForegroundColor Cyan
Write-Host "Remote Host: localhost"
Write-Host "Remote Port: 4723"
Write-Host "Remote Path: /"
Write-Host ""
Write-Host "Capabilities para Android:"
Write-Host '{
  "platformName": "Android",
  "automationName": "UiAutomator2",
  "deviceName": "Android Emulator",
  "appPackage": "br.com.pactosolucoes.treino",
  "appActivity": ".MainActivity"
}'
