GUIA: ANDROID x86 NO VIRTUALBOX (ALTERNATIVA)
============================================

Se o emulador do Android Studio não funcionar, use esta alternativa:

1. BAIXAR ANDROID x86:
   - Site: https://www.android-x86.org/
   - Versão recomendada: Android 11 (API 30)

2. INSTALAR VIRTUALBOX:
   - Site: https://www.virtualbox.org/
   - Versão mais recente

3. CRIAR VM NO VIRTUALBOX:
   - Nome: Android_x86
   - Tipo: Linux
   - Versão: Other Linux (64-bit)
   - RAM: 2048 MB
   - Disco: 8 GB (dinâmico)

4. CONFIGURAÇÕES DA VM:
   - Sistema → Processador: 2 CPUs
   - Sistema → Aceleração: VT-x/AMD-V
   - Display → Memória de vídeo: 128 MB
   - Display → Aceleração 3D: Desabilitado
   - Rede → Adaptador 1: NAT

5. INSTALAR ANDROID:
   - Iniciar VM com ISO do Android x86
   - Escolher "Installation"
   - Criar partição e instalar

6. CONFIGURAR PARA APPIUM:
   - Ativar modo desenvolvedor
   - Ativar depuração USB
   - Configurar rede para acessar via ADB

7. CONECTAR VIA ADB:
   - adb connect [IP_DA_VM]:5555
   - adb devices

VANTAGENS:
- Funciona independente do Hyper-V
- Mais estável que emulador
- Controle total sobre configurações

DESVANTAGENS:
- Instalação mais complexa
- Requer mais recursos
- Configuração de rede manual
