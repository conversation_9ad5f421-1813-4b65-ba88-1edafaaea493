import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';

class DioCurl extends Interceptor {
  final bool? printOnSuccess;
  final bool convertFormData;

  DioCurl({this.printOnSuccess, this.convertFormData = true});
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // _renderCurlRepresentation(options);
    return handler.next(options); //continue
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    _renderCurlRepresentation(err.requestOptions, statusCode: err.response?.statusCode);

    return handler.next(err); //continue
  }

  @override
  void onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) {
    if (printOnSuccess != null && printOnSuccess == true) {
      _renderCurlRepresentation(response.requestOptions, statusCode: response.statusCode);
    }

    return handler.next(response); //continue
  }

  void _renderCurlRepresentation(RequestOptions requestOptions, {int? statusCode}) {
    // add a breakpoint here so all errors can break
    try {
      log('STATUS:${statusCode} ${cURLRepresentation(requestOptions)}');
    } catch (err) {
      log('unable to create a CURL representation of the requestOptions');
    }
  }

  
}

String cURLRepresentation(RequestOptions options) {
    List<String> components = ['curl -i'];
    if (options.method.toUpperCase() != 'GET') {
      components.add('-X ${options.method}');
    }

    options.headers.forEach((k, v) {
      if (k != 'Cookie') {
        components.add('-H "$k: $v"');
      }
    });

    if (options.data != null) {
      // FormData can't be JSON-serialized, so keep only their fields attributes
    if (options.data is FormData) {
        options.data = Map.fromEntries(options.data.fields);
      }

      final data = json.encode(options.data).replaceAll('"', '\\"');
      components.add('-d "$data"');
    }

    components.add('"${options.uri.toString()}"');

    return components.join(' \\\n\t');
}
