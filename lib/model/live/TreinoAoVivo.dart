// To parse this JSON data, do
//
//     final treinoAoVivo = treinoAoVivoFromJson(jsonString);

import 'dart:convert';

TreinoAoVivo treinoAoVivoFromJson(String str) => TreinoAoVivo.fromJson(json.decode(str));

String treinoAoVivoToJson(TreinoAoVivo data) => json.encode(data.toJson());

class TreinoAoVivo {
  String? tituloLive;
  String? refUsuarioApp;
  int? horaInicio;
  dynamic urlFotoProfessor;
  String? descricao;
  String? liveYTurl;
  String? documentKey;
  String? clienteApp;
  String? urlThumb;
  int? horarioFim;
  dynamic nome;
  bool? podeEditar;
  String? nomeProfessor;
  String? tipoColaboradorZW;
  String? fotoProfessor;
  DateTime? dataProgramada;

  TreinoAoVivo({
    this.tituloLive,
    this.refUsuarioApp,
    this.horaInicio,
    this.urlFotoProfessor,
    this.descricao,
    this.liveYTurl,
    this.documentKey,
    this.clienteApp,
    this.urlThumb,
    this.horarioFim,
    this.nome,
    this.podeEditar,
    this.nomeProfessor,
    this.fotoProfessor,
    this.dataProgramada,
  });

  factory TreinoAoVivo.fromJson(Map<String, dynamic> json) => TreinoAoVivo(
        tituloLive: json['tituloLive'],
        refUsuarioApp: json['refUsuarioApp'],
        horaInicio: json['horaInicio'],
        urlFotoProfessor: json['urlFotoProfessor'],
        descricao: json['descricao'],
        liveYTurl: json['liveYTurl'],
        documentKey: json['documentKey'],
        clienteApp: json['clienteApp'],
        urlThumb: json['urlThumb'],
        horarioFim: json['horarioFim'],
        nome: json['nome'],
        podeEditar: json['podeEditar'],
        nomeProfessor: json['nomeProfessor'],
        fotoProfessor: json['fotoProfessor'],
        dataProgramada: json['dataProgramada'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['dataProgramada'])
            : null,
      );

  Map<String, dynamic> toJson() => {
        'tituloLive': tituloLive,
        'refUsuarioApp': refUsuarioApp,
        'horaInicio': horaInicio,
        'urlFotoProfessor': urlFotoProfessor,
        'descricao': descricao,
        'liveYTurl': liveYTurl,
        'documentKey': documentKey,
        'clienteApp': clienteApp,
        'urlThumb': urlThumb,
        'horarioFim': horarioFim,
        'nome': nome,
        'podeEditar': podeEditar,
        'nomeProfessor': nomeProfessor,
        'fotoProfessor': fotoProfessor,
        'dataProgramada': dataProgramada?.millisecondsSinceEpoch,
      };
}
