import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/UserDataKeys.dart';
import 'package:app_treino/model/item_link/ItemLinkMenuApp.dart';
import 'package:get_it/get_it.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
part 'ClienteApp.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class ClienteApp {
  factory ClienteApp.fromJson(Map<String, dynamic> json) => _$ClienteAppFromJson(json);
  Map<String, dynamic> toJson() => _$ClienteAppToJson(this);
  ClienteApp clone() => _$ClienteAppFromJson(this.toJson());
  String? descricaoApp;
  int? dataCriacao;
  String? corPadrao;
  String? urlLogoNotificacao;
  String? urlLogoPrincipal;
  String? chaveRede;
  int? numeroEmpresas;
  String? usuarioMaster;
  int? dataUltimaModificacao;
  String? nomeDoApp;
  String? token;
  String? moduloPrincial;
  bool? aplicativoAtivo;
  List<String>? clienteLiberadoEm;
  String? conjuntoKey;
  String? id;
  String? urlLogoBarraSuperior;
  bool? clienteAtivo;
  bool? trocaLogoDesktop;
  List<EmpresaApp>? empresaApps;
  List<ConfiguracaoModulosApp>? configuracaoModulosApp;
  String? documentkey;
  String? linkVendasOnline;
  String? tipoDeApp;
  List<ItemLinkMenuApp>? itensMenuApp;
  String? tituloMenuApp;
  String? tipoConsultaPremium;
  Map<String, String>? idsN2b;
  String get currentN2bIDExternal {
    return '${GetIt.I.get<ControladorApp>().chave}_${GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa}';
  }

  String get currentN2bID {
    String chave_id = '${GetIt.I.get<ControladorApp>().chave}_${GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa}';
    String n2bRef = '';
    idsN2b?.forEach((key, value) {
      if (key == chave_id) {
        n2bRef = value;
      }
    });
    return n2bRef;
  }

  ClienteApp({
    this.descricaoApp,
    this.dataCriacao,
    this.tipoDeApp,
    this.corPadrao,
    this.urlLogoNotificacao,
    this.urlLogoPrincipal,
    this.chaveRede,
    this.numeroEmpresas,
    this.usuarioMaster,
    this.dataUltimaModificacao,
    this.nomeDoApp,
    this.token,
    this.moduloPrincial,
    this.aplicativoAtivo,
    this.clienteLiberadoEm,
    this.conjuntoKey,
    this.id,
    this.urlLogoBarraSuperior,
    this.clienteAtivo,
    this.trocaLogoDesktop,
    this.empresaApps,
    this.configuracaoModulosApp,
    this.documentkey,
    this.tipoConsultaPremium,
    this.idsN2b,
  });

  Future<String> get nomeUnidade {
    return SharedPreferences.getInstance().then((db) {
      return db.getString(UserDataKeys.NOMEEMPRESA.toString()) ?? '';
    });
  }
}

@JsonSerializable()
class ConfiguracaoModulosApp {
  factory ConfiguracaoModulosApp.fromJson(Map<String, dynamic> json) => _$ConfiguracaoModulosAppFromJson(json);
  Map<String, dynamic> toJson() => _$ConfiguracaoModulosAppToJson(this);
  bool? ePremium;
  bool? habilitado;
  @JsonKey(fromJson: parseEnumConfiguracoes)
  ModuloApp? moduloApp;

  ConfiguracaoModulosApp({
    this.ePremium,
    this.habilitado,
    this.moduloApp,
  });
  static parseEnumConfiguracoes(dynamic any) {
 
    if (ModuloApp.values.any((element) => element.toString().replaceAll('ModuloApp.', '') == any.toString())) {
      return ModuloApp.values.firstWhere((element) => element.toString().replaceAll('ModuloApp.', '') == any.toString());
    }
    return null;
  }
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class EmpresaApp {
  factory EmpresaApp.fromJson(Map<String, dynamic> json) => _$EmpresaAppFromJson(json);
  Map<String, dynamic> toJson() => _$EmpresaAppToJson(this);
  String? chave;
  String? nome;

  EmpresaApp({
    this.chave,
    this.nome,
  });
}

enum ModuloApp {
  MODULO_REFEICOES,
  MODULO_AULAS,
  MODULO_CROSS,
  MODULO_NOTIFICACOES,
  MODULO_MARCAR_PRESENCA_QR,
  MODULO_CREDITOS,
  MODULO_TREINO,
  MODULO_VALORESDECONTRATO,
  MODULO_ALUNO_ADICIONAR_CARTAO,
  MODULO_ALUNO_EDITAR_CARTAO,
  MODULO_TRANCAMENTO_DE_CONTRATO,
  MODULO_RENOVACAO_DE_CONTRATO,
  MODULO_CREDITO_DE_CONTRATO,
  MODULO_AVALIACAOFISICA,
  MODULO_AGENDA,
  MODULO_BLOQUEAR_ALUNO_NO_APP,
  MODULO_SELECAO_UNIDADE_AULAS,
  MODULO_COMPARTILHAR_TREINO_REDE_SOCIAL,
  MODULO_HOME_FIT,
  VISITANTE_ACESSO_LIBERADO,
  MODULO_VENDAS_DE_PLANOS,
  MODULO_MOSTRAR_AGENDAR_AVAL_FISICA,
  MODULO_CONTRATO_FERIAS,
  MODULO_DASHBOARD,
  MODULO_FEED,
  MODULO_CONTROLE_ALUNOS,
  MODULO_MANTER_WOD,
  MODULO_VALIDAR_VISITANTE_HABILITADO,
  MODULO_PUB_FEED_LIBERADO,
  MODULO_COMENT_FEED_LIBERADO,
  MODULO_ALUNO_PODE_RETORNAR_TRANCAMENTO_FERIAS,
  MODULO_ALUNO_PODE_RETORNAR_TRANCAMENTO,
  MODULO_VISUALIZAR_DETALHES_ALUNOS_AULAS,
  MODULO_LANCAR_FERIAS_RETRO,
  MODULO_PERMITE_REALIZAR_CONTATO_ALUNO_WHATSAPP,
  MODULO_LANCAR_PARQ,
  BLOQUEAR_WODS_FUTUROS_PARA_ALUNOS,
  BLOQUEAR_WODS_PASSADOS_PARA_ALUNOS,
  MODULO_CARTEIRINHA_SESC,
  MODULO_VITIO,
  MODULO_VITIO_BIOIMPEDANCIA,
  MODULO_ACOES_AVALIACAO_FISICA,
  MODULO_SALVAR_FOTO_ZW,
  HABILITAR_ASSINATURA_CONTRATO_APP,
  PERMITIR_VALIDAR_RESPONSAVEL_ASSINATURA,
  MODULO_LINK_EXTERNO,
  MODULO_CONVIDAR_AMIGO,
  MODULO_ACOMPANHAR_HOPE,
  MODULO_ESCONDER_QUANTIDADE_SALDO_REPOSICOES,
  MODULO_ACOMPANHAR_GERAR_ACESSO,
  MODULO_ALTERAR_REPETICAO_EXECUCAO,
  BLOQUEAR_MARCAR_DESMARCAR_AULA,
  HABILITAR_VALIDACAO_CREF,
  TDAIA,
  UNLIMTED_BIOS,
  CONTABILIZAR_PRESENCA_AO_EXECUTAR_TREINO,
  TREINO_NAO_FIANLIZAR_MEIA_NOITE,
  MODULO_PROFESSOR_REALIZAR_BIOIMPEDANCIA,
  PERMITIR_VALIDAR_CHECKIN_TOTALPASS,
  CHAT_PROF_ALU,
  PERMITIR_AVALIAR_PROFESSOR,
  LIBERAR_VISITANTE_COM_BLOQUEIO,
  CREDITO_BOAS_VINDAS_N2B,
  MODULO_CLUBE_DE_VANTAGENS,
  MODULO_LOCACAO,
  HABILITAR_GERACAO_BOLETO_PELO_ALUNO,
  MODULO_AUTH_PIC_CONTRATO,
  ALTERAR_VENCIMENTO_PARCELAS,
  HABILITAR_PAGAMENTO_PIX_PARCELAS,
  MODULO_PAGAMENTO_COM_BOLETO,
  HABILITAR_PAGAMENTO_CARTAO_PARCELAS,
  PERMITIR_ALUNO_CANCELAR_CONTRATO,
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class SituacaoEduzz {
  factory SituacaoEduzz.fromJson(Map<String, dynamic> json) => _$SituacaoEduzzFromJson(json);
  Map<String, dynamic> toJson() => _$SituacaoEduzzToJson(this);
  SituacaoEduzz clone() => _$SituacaoEduzzFromJson(this.toJson());
  SituacaoEduzz({
    this.telefoneContato,
    this.situacao,
  });

  String? telefoneContato;
  String? situacao;
}
