// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'NotificacaoCRM.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificacaoCRM _$NotificacaoCRMFromJson(Map json) => NotificacaoCRM(
      academia: (json['academia'] as List<dynamic>?)
          ?.map((e) => NotificacaoCRMAcademia.fromJson(
              Map<String, dynamic>.from(e as Map)))
          .toList(),
      professor: (json['professor'] as List<dynamic>?)
          ?.map((e) => NotificacaoCRMAcademia.fromJson(
              Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$NotificacaoCRMToJson(NotificacaoCRM instance) =>
    <String, dynamic>{
      'academia': instance.academia?.map((e) => e.toJson()).toList(),
      'professor': instance.professor?.map((e) => e.toJson()).toList(),
    };

NotificacaoCRMAcademia _$NotificacaoCRMAcademiaFromJson(Map json) =>
    NotificacaoCRMAcademia(
      cod: json['cod'] as num?,
      tipo: json['tipo'] as String?,
      codProfessor: json['codProfessor'] as num?,
      data: json['data'] as String?,
      dataLong: json['dataLong'] as num?,
      titulo: json['titulo'] as String?,
      texto: json['texto'] as String?,
      codCliente: json['codCliente'] as num?,
      telAluno: json['telAluno'] as String?,
      gravidadeCor: json['gravidadeCor'] as String?,
      opcoes: json['opcoes'] as String?,
      resposta: json['resposta'] as String?,
      lida: json['lida'] as bool?,
      pushEnviado: json['pushEnviado'] as bool?,
      excluida: json['excluida'] as bool?,
      isPush: json['isPush'] as bool? ?? false,
      eNotificaoProfessor: json['eNotificaoProfessor'] as bool?,
    );

Map<String, dynamic> _$NotificacaoCRMAcademiaToJson(
        NotificacaoCRMAcademia instance) =>
    <String, dynamic>{
      'cod': instance.cod,
      'tipo': instance.tipo,
      'codProfessor': instance.codProfessor,
      'data': instance.data,
      'dataLong': instance.dataLong,
      'titulo': instance.titulo,
      'texto': instance.texto,
      'codCliente': instance.codCliente,
      'telAluno': instance.telAluno,
      'gravidadeCor': instance.gravidadeCor,
      'opcoes': instance.opcoes,
      'resposta': instance.resposta,
      'lida': instance.lida,
      'pushEnviado': instance.pushEnviado,
      'excluida': instance.excluida,
      'eNotificaoProfessor': instance.eNotificaoProfessor,
      'isPush': instance.isPush,
    };

PushUsuario _$PushUsuarioFromJson(Map json) => PushUsuario(
      admId: json['admId'] as String?,
      cliente: json['cliente'] as String?,
      dataCriada: json['dataCriada'] == null
          ? null
          : DateTime.parse(json['dataCriada'] as String),
      excluir: json['excluir'] as bool?,
      horarios: (json['horarios'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      userNameUsuario: json['userNameUsuario'] as String?,
      chave: json['chave'] as String?,
      titulo: json['titulo'] as String?,
      content: json['content'] as String?,
      refPush: json['refPush'] as String?,
      enviada: json['enviada'] as bool?,
      lida: json['lida'] as bool?,
      ref: json['ref'] as String?,
    );

Map<String, dynamic> _$PushUsuarioToJson(PushUsuario instance) =>
    <String, dynamic>{
      'admId': instance.admId,
      'cliente': instance.cliente,
      'dataCriada': instance.dataCriada?.toIso8601String(),
      'excluir': instance.excluir,
      'horarios': instance.horarios,
      'userNameUsuario': instance.userNameUsuario,
      'chave': instance.chave,
      'titulo': instance.titulo,
      'content': instance.content,
      'refPush': instance.refPush,
      'enviada': instance.enviada,
      'lida': instance.lida,
      'ref': instance.ref,
    };

ListPush _$ListPushFromJson(Map json) => ListPush(
      ids: (json['ids'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$ListPushToJson(ListPush instance) => <String, dynamic>{
      'ids': instance.ids,
    };
