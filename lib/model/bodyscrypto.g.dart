// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bodyscrypto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BodyLoginPasswordCrypto _$BodyLoginPasswordCryptoFromJson(
        Map<String, dynamic> json) =>
    BodyLoginPasswordCrypto(
      userName: json['userName'] as String?,
      pwd: json['pwd'] as String?,
    );

Map<String, dynamic> _$BodyLoginPasswordCryptoToJson(
        BodyLoginPasswordCrypto instance) =>
    <String, dynamic>{
      'userName': instance.userName,
      'pwd': instance.pwd,
    };

BodyLoginCrypto _$BodyLoginCryptoFromJson(Map<String, dynamic> json) =>
    BodyLoginCrypto(
      email: json['email'] as String?,
      password: json['password'] as String?,
      aluno: json['aluno'] as bool?,
    );

Map<String, dynamic> _$BodyLoginCryptoToJson(BodyLoginCrypto instance) =>
    <String, dynamic>{
      'email': instance.email,
      'password': instance.password,
      'aluno': instance.aluno,
    };

BodyDescrobrirUsuariosCrypto _$BodyDescrobrirUsuariosCryptoFromJson(
        Map<String, dynamic> json) =>
    BodyDescrobrirUsuariosCrypto(
      celular: json['celular'] as String?,
      ddi: json['ddi'] as String?,
      ddd: json['ddd'] as String?,
    );

Map<String, dynamic> _$BodyDescrobrirUsuariosCryptoToJson(
        BodyDescrobrirUsuariosCrypto instance) =>
    <String, dynamic>{
      'celular': instance.celular,
      'ddi': instance.ddi,
      'ddd': instance.ddd,
    };

BodyRecuperarSenhaCrypto _$BodyRecuperarSenhaCryptoFromJson(
        Map<String, dynamic> json) =>
    BodyRecuperarSenhaCrypto(
      cliente: json['cliente'] as bool?,
      email: json['email'] as String?,
    );

Map<String, dynamic> _$BodyRecuperarSenhaCryptoToJson(
        BodyRecuperarSenhaCrypto instance) =>
    <String, dynamic>{
      'cliente': instance.cliente,
      'email': instance.email,
    };

BodyAlterarSenhaCrypto _$BodyAlterarSenhaCryptoFromJson(
        Map<String, dynamic> json) =>
    BodyAlterarSenhaCrypto(
      email: json['email'] as String?,
      novaSenha: json['novaSenha'] as String?,
    );

Map<String, dynamic> _$BodyAlterarSenhaCryptoToJson(
        BodyAlterarSenhaCrypto instance) =>
    <String, dynamic>{
      'email': instance.email,
      'novaSenha': instance.novaSenha,
    };

BodyContratoAlunoCrypto _$BodyContratoAlunoCryptoFromJson(
        Map<String, dynamic> json) =>
    BodyContratoAlunoCrypto(
      validar: json['validar'] as bool?,
      matricula: json['matricula'] as String?,
      empresa: json['empresa'] as num?,
    );

Map<String, dynamic> _$BodyContratoAlunoCryptoToJson(
        BodyContratoAlunoCrypto instance) =>
    <String, dynamic>{
      'validar': instance.validar,
      'matricula': instance.matricula,
      'empresa': instance.empresa,
    };

BodyConsultarContratoPorCodigoCrypto
    _$BodyConsultarContratoPorCodigoCryptoFromJson(Map<String, dynamic> json) =>
        BodyConsultarContratoPorCodigoCrypto(
          contrato: json['contrato'] as num?,
          empresa: json['empresa'] as num?,
        );

Map<String, dynamic> _$BodyConsultarContratoPorCodigoCryptoToJson(
        BodyConsultarContratoPorCodigoCrypto instance) =>
    <String, dynamic>{
      'contrato': instance.contrato,
      'empresa': instance.empresa,
    };
