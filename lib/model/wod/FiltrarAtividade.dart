import 'package:app_treino/model/util/string_extension.dart';

class FiltroPersonalRecord {
  String titulo = '';
  TipoFiltroPR? enumerador;
  FiltroPersonalRecord({required this.titulo, this.enumerador});
}

enum TipoFiltroPR {
  TODOS,
  MEUS,
  BARBELL,
  GYMNASTIC,
  ENDURANCE,
  NOTABLES,
  GIRLS,
  OPEN,
  CAMPEONATOS,
  THEHEROES,
  CROSSFITGAMES,
}

extension TipoFiltroPRExtension on TipoFiltroPR {
  String get titulo {
    switch (this) {
      case TipoFiltroPR.TODOS:
        return localizedString('all');
      case TipoFiltroPR.MEUS:
        return localizedString('my_records');
      case TipoFiltroPR.BARBELL:
        return 'Barbell';
      case TipoFiltroPR.GYMNASTIC:
        return 'Gymnastic';
      case TipoFiltroPR.ENDURANCE:
        return 'Endurance';
      case TipoFiltroPR.NOTABLES:
        return 'Notables';
      case TipoFiltroPR.GIRLS:
        return 'Girls';
      case TipoFiltroPR.OPEN:
        return 'Open';
      case TipoFiltroPR.CAMPEONATOS:
        return localizedString('championship');
      case TipoFiltroPR.THEHEROES:
        return 'The Heroes';
      case TipoFiltroPR.CROSSFITGAMES:
        return 'Cross Games';
    }
  }
}
