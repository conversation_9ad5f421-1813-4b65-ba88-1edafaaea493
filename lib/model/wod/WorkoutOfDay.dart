import 'dart:convert';
import 'dart:typed_data';

import 'package:app_treino/appWidgets/formWidgets/DropDownPadrao.dart';
import 'package:app_treino/controlladores/ControladorWod.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/PersonalRecord.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';
part 'WorkoutOfDay.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class WorkoutOfDay {
  factory WorkoutOfDay.fromJson(Map<String, dynamic> json) => _$WorkoutOfDayFromJson(json);
  Map<String, dynamic> toJson() => _$WorkoutOfDayToJson(this);
  WorkoutOfDay clone() => _$WorkoutOfDayFromJson(this.toJson());
  WorkoutOfDay(
      {this.codigo,
      this.nome,
      this.empresa,
      this.descricaoExercicios,
      this.tipoWod,
      this.urlImagem,
      this.origemExercicio,
      this.benchmark,
      this.observacao,
      this.dia,
      this.temResultado,
      this.meuRanking,
      this.tipoWodTabela,
      this.evento,
      this.scoresTreino,
      this.alongamento,
      this.aquecimento,
      this.atividadeWodJSON,
      this.aparelhoWodJSON,
      this.comentarios,
      this.complexEmom,
      this.temResultadoGeral,
      this.contratoCrossfit,
      this.urlImagemRetorno,
      this.keyImagem,
      this.diaApresentar,
      this.rankingWod,
      this.parteTecnicaSkill});

  num? codigo;
  String? nome;
  num? empresa;
  String? descricaoExercicios;
  String? tipoWod;
  String? urlImagem;
  String? origemExercicio;
  String? benchmark;
  String? observacao;
  String? dia;
  bool? temResultado;
  SpotRankingWod? meuRanking;
  TipoWodTabela? tipoWodTabela;
  dynamic evento;
  List<dynamic>? scoresTreino;
  String? alongamento;
  List<SpotRankingWod>? rankingWod;
  String? aquecimento;
  String? parteTecnicaSkill;
  List<AtividadeWod>? atividadeWodJSON;
  List<AparelhoWod>? aparelhoWodJSON;
  List<dynamic>? comentarios;
  String? complexEmom;
  bool? temResultadoGeral;
  bool? contratoCrossfit;
  String? urlImagemRetorno;
  String? keyImagem;
  String? diaApresentar;

  String? get urlImagemWod {
    if (urlImagem?.contains('http') ?? false) {
      return urlImagem;
    } else if (keyImagem?.contains('http') ?? false) {
      return keyImagem;
    } else if (urlImagemRetorno?.contains('http') ?? false) {
      return urlImagemRetorno;
    } else {
      return '';
    }
  }
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class TipoWodTabela extends DropDownItemBasico {
  factory TipoWodTabela.fromJson(Map<String, dynamic> json) => _$TipoWodTabelaFromJson(json);
  Map<String, dynamic> toJson() => _$TipoWodTabelaToJson(this);
  TipoWodTabela({
    this.codigo,
    this.nome,
    this.orderBy,
    this.camposResultado,
  }) {
    this.tituloDropDown = nome;
  }

  num? codigo;
  String? nome;
  String? orderBy;
  String? camposResultado;
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class AparelhoWod {
  factory AparelhoWod.fromJson(Map<String, dynamic> json) => _$AparelhoWodFromJson(json);
  Map<String, dynamic> toJson() => _$AparelhoWodToJson(this);

  AparelhoWod({
    this.codigo,
    this.codAparelho,
    this.nomeAparelho,
    this.codWod,
  });

  int? codigo;
  int? codAparelho;
  String? nomeAparelho;
  int? codWod;
}

enum TipoAtividadeCross {
  REPS_FOR_TIME, ROUNDS_AND_REPS, TIME, WEIGHT, REPS, CALS, DISTANCE, ROUNDS, GENERICO, 
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class AtividadeWod {
  factory AtividadeWod.fromJson(Map<String, dynamic> json) => _$AtividadeWodFromJson(json);
  Map<String, dynamic> toJson() => _$AtividadeWodToJson(this);
  AtividadeWod(
      {this.codAtividade,
      this.nomeAtividade,
      this.linkVideo,
      this.videoId,
      this.codUnidadeMedida,
      this.unidadeMedida,
      this.codCategoriaAtividadeWod,
      this.categoriaAtividadeWod,
      this.descricaoAtividade,
      this.meusPr
      });

  int? codAtividade;
  List<PersonalRecords?>? personalRecordsDoServico;
  String? nomeAtividade;
  String? linkVideo;
  String? videoId;
  int? codUnidadeMedida;
  String? unidadeMedida;
  int? codCategoriaAtividadeWod;
  String? categoriaAtividadeWod;
  String? descricaoAtividade;
  List<MeuResultadoPr?>? meusPr;

  String? get idLinkVideo {
    try {
      return linkVideo!.split('=').last;
    } catch (e) {
      return '';
    }
  }

  TipoAtividadeCross get tipoAtividade {
    switch (unidadeMedida?.toUpperCase()) {
      case 'CALS':
        return TipoAtividadeCross.CALS;
      case 'DISTANCE':
        return TipoAtividadeCross.DISTANCE;
      case 'REPS':
        return TipoAtividadeCross.REPS;
      case 'REPS FOR TIME':
        return TipoAtividadeCross.REPS_FOR_TIME;
      case 'ROUNDS AND REPS':
        return TipoAtividadeCross.ROUNDS_AND_REPS;
      case 'ROUNDS':
        return TipoAtividadeCross.ROUNDS;
      case 'TIME':
        return TipoAtividadeCross.TIME;
      default:
        return TipoAtividadeCross.GENERICO;
    }
  }

  List<PersonalRecords?>? get personalRecordsDoServicoOrdenados {
    if (personalRecordsDoServico?.isEmpty ?? true) {
      return null;
    }
    List<PersonalRecords?> temporaryPersonalRecords = [];
    temporaryPersonalRecords.addAll(personalRecordsDoServico ?? []);
    switch (temporaryPersonalRecords.first!.tipo) {
      case 'Time':
        temporaryPersonalRecords.sort((a, b) => num.parse(a!.time!).compareTo(num.parse(b!.time!)));
        break;
      case 'Reps':
        temporaryPersonalRecords.sort((a, b) => num.parse(b!.reps!).compareTo(num.parse(a!.reps!)));
        break;
      case 'Cals':
        temporaryPersonalRecords.sort((a, b) => num.parse(b!.valorGenerico!).compareTo(num.parse(a!.valorGenerico!)));
        break;
      case 'Distance':
        temporaryPersonalRecords.sort((a, b) => num.parse(b!.valorGenerico!).compareTo(num.parse(a!.valorGenerico!)));
        break;
      case 'Weight':
        temporaryPersonalRecords.sort((a, b) => num.parse(a!.weight!).compareTo(num.parse(b!.weight!)));
        break;
      case 'Rounds and Reps':
        temporaryPersonalRecords.sort((a, b) => num.parse(a!.valorGenerico!).compareTo(num.parse(b!.valorGenerico!)));
        break;
      case 'Reps For Time':
        temporaryPersonalRecords.sort((a, b) => num.parse(a!.reps!).compareTo(num.parse(b!.reps!)));
        break;
      default:
        temporaryPersonalRecords.sort((a, b) => num.parse(a!.valorGenerico!).compareTo(num.parse(b!.valorGenerico!)));
    }
    return temporaryPersonalRecords;
  }

  PersonalRecords? get maiorPr {
    return personalRecordsDoServicoOrdenados?.first;
  }

  MeuResultadoPr? get ultimoPr {
    if (meusPr == null || meusPr!.isEmpty) return null;
    meusPr!.sort((a, b) => a!.dataRegistro!.compareTo(b!.dataRegistro!));
    return meusPr!.last;
  }
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class MeuResultadoPr {
  factory MeuResultadoPr.fromJson(Map<String, dynamic> json) => _$MeuResultadoPrFromJson(json);
  Map<String, dynamic> toJson() => _$MeuResultadoPrToJson(this);
  bool? maiorPr = false;
  num? index;
  num? dataRegistro;
  num? tempo;
  num? peso;
  num? repeticoes;
  num? valorGenerico;
  num? get tempoEmSegundos => (tempo ?? 0) / 1000;

  String? get resultadoFormatado {
    try {
      if (peso != null) return peso.toString();
      if (repeticoes != null) return repeticoes.toString();
      if (tempo != null) {
        var tempoFormatado = '${(NumberFormat('00').format((tempoEmSegundos! / 60).truncateToDouble()))}:${(NumberFormat('00').format(tempoEmSegundos! % 60))}';
        return tempoFormatado;
      }
      return null;
    } catch (e) {
      return null;
    }
  }
  num valorExibirGrafico(AtividadeWod atividadeWod) {
    switch (atividadeWod.unidadeMedida!.toLowerCase()) {
      case 'cals':
        return valorGenerico ?? 0;
      case 'reps for time':
        return repeticoes ?? valorGenerico ?? 0;
      case 'reps':
        return repeticoes ?? valorGenerico ?? 0;
      case 'rounds':
        return valorGenerico ?? 0;
      case 'time':
        return tempo ?? 0;
      case 'weight':
        return peso ?? valorGenerico ?? 0;
      case 'rounds and reps':
        return peso ?? valorGenerico ?? 0;
      default:
        return 0;
    }
  }

  String valorExibirNaTile(AtividadeWod atividadeWod) {
    switch (atividadeWod.unidadeMedida!.toLowerCase()) {
      case 'cals':
        return '$valorGenerico Cals';
      case 'reps for time':
        return '${repeticoes ?? valorGenerico} Reps em ${UtilDataHora.getApenasMinSecs(duration: Duration(milliseconds: tempo as int? ?? 0))}';
      case 'reps':
        return '${repeticoes ?? valorGenerico} Reps';
      case 'rounds':
        return '$valorGenerico Rounds';
      case 'time':
        return 'Time: ${UtilDataHora.getApenasMinSecs(duration: Duration(milliseconds: (tempo ?? 0).toInt()))}';
      case 'weight':
        var unidadeMedida =
            GetIt.I.get<ControladorWod>().isLibra ? 'lbs' : 'kg';
        var resultado = GetIt.I
            .get<ControladorWod>()
            .converterQuiloEmLibra(peso!.toDouble())
            .toStringAsFixed(0);
        return peso == null ? '$valorGenerico' : '$resultado $unidadeMedida';
      case 'rounds and reps':
        return peso == null
            ? '$valorGenerico Rounds and $repeticoes reps'
            : '$peso Rounds and reps';
      case 'distance':
        return '$valorGenerico Distance';
      default:
        return '';
    }
  }

  MeuResultadoPr({this.dataRegistro, this.peso, this.tempo, this.repeticoes, this.valorGenerico, this.index, this.maiorPr});
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class SpotRankingWod {
  factory SpotRankingWod.fromJson(Map<String, dynamic> json) => _$SpotRankingWodFromJson(json);
  Map<String, dynamic> toJson() => _$SpotRankingWodToJson(this);
  SpotRankingWod({
    this.posicao,
    this.nome,
    this.matricula,
    this.foto,
    this.rx,
    this.tipoWOD,
    this.codigoWOD,
    this.campos,
    this.tempo,
    this.peso,
    this.repeticoes,
    this.rounds,
    this.nivel,
    this.nivelDescricao,
    this.nivelSigla,
    this.sexo,
    this.codUsuario,
    this.visitante,
    this.descricao,
    this.primeiroNome,
    this.descricaoSigla,
  });

  num? posicao;
  String? nome;
  String? matricula;
  String? foto;
  bool? rx;
  num? codigoWOD;
  String? tipoWOD;
  String? campos;
  num? tempo;
  double? peso;
  num? repeticoes;
  num? rounds;
  num? nivel;
  String? nivelDescricao;
  String? nivelSigla;
  dynamic sexo;
  num? codUsuario;
  dynamic visitante;
  String? descricao;
  String? primeiroNome;
  String? descricaoSigla;

  get resultadoFormatado {
    try {
      String retorno = '';
      if (campos?.contains('tempo') ?? false) {
        retorno = UtilDataHora.parseToStringMinutosSegundo(Duration(seconds: this.tempo?.toInt() ?? 0));
        retorno += '/';
      }
      if (campos?.contains('repeticoes') ?? false) {
        retorno += '$repeticoes/';
      }
      if (campos?.contains('rounds') ?? false) {
        retorno += 'x $rounds/';
      }
      if (campos?.contains('peso') ?? false) {
        retorno += '$peso kg';
      }
      if (retorno.endsWith('/')) {
        retorno = retorno.substring(0, retorno.length - 1);
      }
      retorno = retorno.replaceAll('/', ' - ');
      return retorno;
    } catch (e) {
      return '00';
    }
  }
}

enum TipoCampoAtividadeCross { DATA_REGISTRO, TIME, REPS, ROUNDS, WEIGHT, CALS }

enum TipoCampoWod { TEMPO, COMENTARIO, PESO, REPETICOES, ROUNDS, NIVEL }

class TipoCampoWodExtension {
  // ignore: missing_return
  static TipoCampoWod getTipoCampoByRetorno(String retorno) {
    switch (retorno) {
      case 'tempo':
        return TipoCampoWod.TEMPO;
      case 'comentario':
        return TipoCampoWod.COMENTARIO;
      case 'peso':
        return TipoCampoWod.PESO;
      case 'repeticoes':
        return TipoCampoWod.REPETICOES;
      case 'nivelCrossfit':
        return TipoCampoWod.NIVEL;
      case 'rounds':
        return TipoCampoWod.ROUNDS;
      default:
        return TipoCampoWod.ROUNDS;
    }
  }

  // ignore: missing_return
  static String getTipoCampoByEnum(TipoCampoWod retorno) {
    switch (retorno) {
      case TipoCampoWod.TEMPO:
        return localizedString('time');
      case TipoCampoWod.COMENTARIO:
        return localizedString('comment');
      case TipoCampoWod.PESO:
        return localizedString('weight');
      case TipoCampoWod.REPETICOES:
        return localizedString('reps');
      case TipoCampoWod.NIVEL:
        return localizedString('level');
        
      case TipoCampoWod.ROUNDS:
        return 'Rounds';
    }
  }
}

enum NivelAlunoWod { SELECIONE, INICIANTE, SCALED, AMADOR, AVANCADO }

extension NivelAlunoWodExtension on NivelAlunoWod {
  String get getNome {
    switch (this) {
      case NivelAlunoWod.SELECIONE:
        return localizedString('select_your_level');
      case NivelAlunoWod.INICIANTE:
        return localizedString('beginner');
      case NivelAlunoWod.SCALED:
        return 'Scaled';
      case NivelAlunoWod.AMADOR:
        return localizedString('amateur');
      case NivelAlunoWod.AVANCADO:
        return localizedString('advanced');
      }
  }

  String get getAbreviacao {
    switch (this) {
      case NivelAlunoWod.INICIANTE:
        return 'IN';
      case NivelAlunoWod.SCALED:
        return 'SC';
      case NivelAlunoWod.AMADOR:
        return 'AM';
      case NivelAlunoWod.AVANCADO:
        return 'RX';
      default:
        return '';
    }
  }
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class WoDmanter {
  factory WoDmanter.fromJson(Map<String, dynamic> json) => _$WoDmanterFromJson(json);
  Map<String, dynamic> toJson() => _$WoDmanterToJson(this);
  WoDmanter clone() => _$WoDmanterFromJson(this.toJson());

  WoDmanter(
      {this.codigo,
      this.dia = '',
      this.nome = '',
      this.tipoWod,
      this.alongamento = '',
      this.aquecimento = '',
      this.complexEmom = '',
      this.wod = '',
      this.atividades = '',
      this.aparelhos = '',
      this.imagemBase64 = '',
      this.operacao = '',
      this.empresa,
      this.parteTecnicaSkill = '',
      WorkoutOfDay? wodParse,
      Uint8List? imagem}) {
    this.atividades = '';
    this.aparelhos = '';
    wodParse?.atividadeWodJSON?.forEach((element) {
      this.atividades = this.atividades! + '${element.codAtividade},';
    });
    wodParse?.aparelhoWodJSON?.forEach((element) {
      this.aparelhos = this.aparelhos! + '${element.codAparelho},';
    });
    if (this.atividades!.isNotEmpty) this.atividades = this.atividades!.substring(0, this.atividades!.length - 1);
    if (this.aparelhos!.isNotEmpty) this.aparelhos = this.aparelhos!.substring(0, this.aparelhos!.length - 1);
    this.codigo = wodParse?.codigo as int?;
    this.dia = wodParse?.dia;
    this.nome = wodParse?.nome;
    this.tipoWod = wodParse?.tipoWodTabela!.codigo as int?;
    this.alongamento = wodParse?.alongamento;
    this.aquecimento = wodParse?.aquecimento;
    this.complexEmom = wodParse?.complexEmom;
    this.wod = wodParse?.descricaoExercicios;
    if (imagem != null) {
      this.imagemBase64 = base64Encode(imagem);
    } else {
      this.imagemBase64 = '';
    }
    this.parteTecnicaSkill = wodParse?.parteTecnicaSkill;
    this.operacao = wodParse?.codigo == null ? 'ADICIONAR' : 'EDITAR';
  }

  int? codigo;
  String? dia;
  String? nome;
  int? tipoWod;
  String? parteTecnicaSkill;
  String? alongamento;
  String? aquecimento;
  String? complexEmom;
  String? wod;
  String? atividades;
  String? aparelhos;
  String? imagemBase64;
  String? operacao;
  int? empresa;
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class AvaliarWod {
  num? usuario;
  String? comentario;
  num? empresa;
  num? nota;
  num? percepcaoEsforco;
  num? wod;

  AvaliarWod(
    this.usuario,
    this.comentario,
    this.empresa,
    this.nota,
    this.percepcaoEsforco,
    this.wod,
  );

  factory AvaliarWod.fromJson(Map<String, dynamic> json) => _$AvaliarWodFromJson(json);
  Map<String, dynamic> toJson() => _$AvaliarWodToJson(this);
  AvaliarWod toJsonBody() => this;
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class WodNiveis {

  num? id;
  String? nome;
  String? nomePadrao;
  String? categoria;

  WodNiveis({
    this.id,
    this.nome,
    this.nomePadrao,
    this.categoria,
  });

  factory WodNiveis.fromJson(Map<String, dynamic> json) => _$WodNiveisFromJson(json);
  Map<String, dynamic> toJson() => _$WodNiveisToJson(this);
}
