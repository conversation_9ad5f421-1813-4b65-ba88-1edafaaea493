// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'WorkoutOfDay.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkoutOfDay _$WorkoutOfDayFromJson(Map json) => WorkoutOfDay(
      codigo: json['codigo'] as num?,
      nome: json['nome'] as String?,
      empresa: json['empresa'] as num?,
      descricaoExercicios: json['descricaoExercicios'] as String?,
      tipoWod: json['tipoWod'] as String?,
      urlImagem: json['urlImagem'] as String?,
      origemExercicio: json['origemExercicio'] as String?,
      benchmark: json['benchmark'] as String?,
      observacao: json['observacao'] as String?,
      dia: json['dia'] as String?,
      temResultado: json['temResultado'] as bool?,
      meuRanking: json['meuRanking'] == null
          ? null
          : SpotRankingWod.fromJson(
              Map<String, dynamic>.from(json['meuRanking'] as Map)),
      tipoWodTabela: json['tipoWodTabela'] == null
          ? null
          : TipoWodTabela.fromJson(
              Map<String, dynamic>.from(json['tipoWodTabela'] as Map)),
      evento: json['evento'],
      scoresTreino: json['scoresTreino'] as List<dynamic>?,
      alongamento: json['alongamento'] as String?,
      aquecimento: json['aquecimento'] as String?,
      atividadeWodJSON: (json['atividadeWodJSON'] as List<dynamic>?)
          ?.map(
              (e) => AtividadeWod.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      aparelhoWodJSON: (json['aparelhoWodJSON'] as List<dynamic>?)
          ?.map(
              (e) => AparelhoWod.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      comentarios: json['comentarios'] as List<dynamic>?,
      complexEmom: json['complexEmom'] as String?,
      temResultadoGeral: json['temResultadoGeral'] as bool?,
      contratoCrossfit: json['contratoCrossfit'] as bool?,
      urlImagemRetorno: json['urlImagemRetorno'] as String?,
      keyImagem: json['keyImagem'] as String?,
      diaApresentar: json['diaApresentar'] as String?,
      rankingWod: (json['rankingWod'] as List<dynamic>?)
          ?.map((e) =>
              SpotRankingWod.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      parteTecnicaSkill: json['parteTecnicaSkill'] as String?,
    );

Map<String, dynamic> _$WorkoutOfDayToJson(WorkoutOfDay instance) =>
    <String, dynamic>{
      'codigo': instance.codigo,
      'nome': instance.nome,
      'empresa': instance.empresa,
      'descricaoExercicios': instance.descricaoExercicios,
      'tipoWod': instance.tipoWod,
      'urlImagem': instance.urlImagem,
      'origemExercicio': instance.origemExercicio,
      'benchmark': instance.benchmark,
      'observacao': instance.observacao,
      'dia': instance.dia,
      'temResultado': instance.temResultado,
      'meuRanking': instance.meuRanking?.toJson(),
      'tipoWodTabela': instance.tipoWodTabela?.toJson(),
      'evento': instance.evento,
      'scoresTreino': instance.scoresTreino,
      'alongamento': instance.alongamento,
      'rankingWod': instance.rankingWod?.map((e) => e.toJson()).toList(),
      'aquecimento': instance.aquecimento,
      'parteTecnicaSkill': instance.parteTecnicaSkill,
      'atividadeWodJSON':
          instance.atividadeWodJSON?.map((e) => e.toJson()).toList(),
      'aparelhoWodJSON':
          instance.aparelhoWodJSON?.map((e) => e.toJson()).toList(),
      'comentarios': instance.comentarios,
      'complexEmom': instance.complexEmom,
      'temResultadoGeral': instance.temResultadoGeral,
      'contratoCrossfit': instance.contratoCrossfit,
      'urlImagemRetorno': instance.urlImagemRetorno,
      'keyImagem': instance.keyImagem,
      'diaApresentar': instance.diaApresentar,
    };

TipoWodTabela _$TipoWodTabelaFromJson(Map json) => TipoWodTabela(
      codigo: json['codigo'] as num?,
      nome: json['nome'] as String?,
      orderBy: json['orderBy'] as String?,
      camposResultado: json['camposResultado'] as String?,
    )..tituloDropDown = json['tituloDropDown'] as String?;

Map<String, dynamic> _$TipoWodTabelaToJson(TipoWodTabela instance) =>
    <String, dynamic>{
      'tituloDropDown': instance.tituloDropDown,
      'codigo': instance.codigo,
      'nome': instance.nome,
      'orderBy': instance.orderBy,
      'camposResultado': instance.camposResultado,
    };

AparelhoWod _$AparelhoWodFromJson(Map json) => AparelhoWod(
      codigo: (json['codigo'] as num?)?.toInt(),
      codAparelho: (json['codAparelho'] as num?)?.toInt(),
      nomeAparelho: json['nomeAparelho'] as String?,
      codWod: (json['codWod'] as num?)?.toInt(),
    );

Map<String, dynamic> _$AparelhoWodToJson(AparelhoWod instance) =>
    <String, dynamic>{
      'codigo': instance.codigo,
      'codAparelho': instance.codAparelho,
      'nomeAparelho': instance.nomeAparelho,
      'codWod': instance.codWod,
    };

AtividadeWod _$AtividadeWodFromJson(Map json) => AtividadeWod(
      codAtividade: (json['codAtividade'] as num?)?.toInt(),
      nomeAtividade: json['nomeAtividade'] as String?,
      linkVideo: json['linkVideo'] as String?,
      videoId: json['videoId'] as String?,
      codUnidadeMedida: (json['codUnidadeMedida'] as num?)?.toInt(),
      unidadeMedida: json['unidadeMedida'] as String?,
      codCategoriaAtividadeWod:
          (json['codCategoriaAtividadeWod'] as num?)?.toInt(),
      categoriaAtividadeWod: json['categoriaAtividadeWod'] as String?,
      descricaoAtividade: json['descricaoAtividade'] as String?,
      meusPr: (json['meusPr'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : MeuResultadoPr.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    )..personalRecordsDoServico =
        (json['personalRecordsDoServico'] as List<dynamic>?)
            ?.map((e) => e == null
                ? null
                : PersonalRecords.fromJson(Map<String, dynamic>.from(e as Map)))
            .toList();

Map<String, dynamic> _$AtividadeWodToJson(AtividadeWod instance) =>
    <String, dynamic>{
      'codAtividade': instance.codAtividade,
      'personalRecordsDoServico':
          instance.personalRecordsDoServico?.map((e) => e?.toJson()).toList(),
      'nomeAtividade': instance.nomeAtividade,
      'linkVideo': instance.linkVideo,
      'videoId': instance.videoId,
      'codUnidadeMedida': instance.codUnidadeMedida,
      'unidadeMedida': instance.unidadeMedida,
      'codCategoriaAtividadeWod': instance.codCategoriaAtividadeWod,
      'categoriaAtividadeWod': instance.categoriaAtividadeWod,
      'descricaoAtividade': instance.descricaoAtividade,
      'meusPr': instance.meusPr?.map((e) => e?.toJson()).toList(),
    };

MeuResultadoPr _$MeuResultadoPrFromJson(Map json) => MeuResultadoPr(
      dataRegistro: json['dataRegistro'] as num?,
      peso: json['peso'] as num?,
      tempo: json['tempo'] as num?,
      repeticoes: json['repeticoes'] as num?,
      valorGenerico: json['valorGenerico'] as num?,
      index: json['index'] as num?,
      maiorPr: json['maiorPr'] as bool?,
    );

Map<String, dynamic> _$MeuResultadoPrToJson(MeuResultadoPr instance) =>
    <String, dynamic>{
      'maiorPr': instance.maiorPr,
      'index': instance.index,
      'dataRegistro': instance.dataRegistro,
      'tempo': instance.tempo,
      'peso': instance.peso,
      'repeticoes': instance.repeticoes,
      'valorGenerico': instance.valorGenerico,
    };

SpotRankingWod _$SpotRankingWodFromJson(Map json) => SpotRankingWod(
      posicao: json['posicao'] as num?,
      nome: json['nome'] as String?,
      matricula: json['matricula'] as String?,
      foto: json['foto'] as String?,
      rx: json['rx'] as bool?,
      tipoWOD: json['tipoWOD'] as String?,
      codigoWOD: json['codigoWOD'] as num?,
      campos: json['campos'] as String?,
      tempo: json['tempo'] as num?,
      peso: (json['peso'] as num?)?.toDouble(),
      repeticoes: json['repeticoes'] as num?,
      rounds: json['rounds'] as num?,
      nivel: json['nivel'] as num?,
      nivelDescricao: json['nivelDescricao'] as String?,
      nivelSigla: json['nivelSigla'] as String?,
      sexo: json['sexo'],
      codUsuario: json['codUsuario'] as num?,
      visitante: json['visitante'],
      descricao: json['descricao'] as String?,
      primeiroNome: json['primeiroNome'] as String?,
      descricaoSigla: json['descricaoSigla'] as String?,
    );

Map<String, dynamic> _$SpotRankingWodToJson(SpotRankingWod instance) =>
    <String, dynamic>{
      'posicao': instance.posicao,
      'nome': instance.nome,
      'matricula': instance.matricula,
      'foto': instance.foto,
      'rx': instance.rx,
      'codigoWOD': instance.codigoWOD,
      'tipoWOD': instance.tipoWOD,
      'campos': instance.campos,
      'tempo': instance.tempo,
      'peso': instance.peso,
      'repeticoes': instance.repeticoes,
      'rounds': instance.rounds,
      'nivel': instance.nivel,
      'nivelDescricao': instance.nivelDescricao,
      'nivelSigla': instance.nivelSigla,
      'sexo': instance.sexo,
      'codUsuario': instance.codUsuario,
      'visitante': instance.visitante,
      'descricao': instance.descricao,
      'primeiroNome': instance.primeiroNome,
      'descricaoSigla': instance.descricaoSigla,
    };

WoDmanter _$WoDmanterFromJson(Map json) => WoDmanter(
      codigo: (json['codigo'] as num?)?.toInt(),
      dia: json['dia'] as String? ?? '',
      nome: json['nome'] as String? ?? '',
      tipoWod: (json['tipoWod'] as num?)?.toInt(),
      alongamento: json['alongamento'] as String? ?? '',
      aquecimento: json['aquecimento'] as String? ?? '',
      complexEmom: json['complexEmom'] as String? ?? '',
      wod: json['wod'] as String? ?? '',
      atividades: json['atividades'] as String? ?? '',
      aparelhos: json['aparelhos'] as String? ?? '',
      imagemBase64: json['imagemBase64'] as String? ?? '',
      operacao: json['operacao'] as String? ?? '',
      empresa: (json['empresa'] as num?)?.toInt(),
      parteTecnicaSkill: json['parteTecnicaSkill'] as String? ?? '',
    );

Map<String, dynamic> _$WoDmanterToJson(WoDmanter instance) => <String, dynamic>{
      'codigo': instance.codigo,
      'dia': instance.dia,
      'nome': instance.nome,
      'tipoWod': instance.tipoWod,
      'parteTecnicaSkill': instance.parteTecnicaSkill,
      'alongamento': instance.alongamento,
      'aquecimento': instance.aquecimento,
      'complexEmom': instance.complexEmom,
      'wod': instance.wod,
      'atividades': instance.atividades,
      'aparelhos': instance.aparelhos,
      'imagemBase64': instance.imagemBase64,
      'operacao': instance.operacao,
      'empresa': instance.empresa,
    };

AvaliarWod _$AvaliarWodFromJson(Map json) => AvaliarWod(
      json['usuario'] as num?,
      json['comentario'] as String?,
      json['empresa'] as num?,
      json['nota'] as num?,
      json['percepcaoEsforco'] as num?,
      json['wod'] as num?,
    );

Map<String, dynamic> _$AvaliarWodToJson(AvaliarWod instance) =>
    <String, dynamic>{
      'usuario': instance.usuario,
      'comentario': instance.comentario,
      'empresa': instance.empresa,
      'nota': instance.nota,
      'percepcaoEsforco': instance.percepcaoEsforco,
      'wod': instance.wod,
    };

WodNiveis _$WodNiveisFromJson(Map json) => WodNiveis(
      id: json['id'] as num?,
      nome: json['nome'] as String?,
      nomePadrao: json['nomePadrao'] as String?,
      categoria: json['categoria'] as String?,
    );

Map<String, dynamic> _$WodNiveisToJson(WodNiveis instance) => <String, dynamic>{
      'id': instance.id,
      'nome': instance.nome,
      'nomePadrao': instance.nomePadrao,
      'categoria': instance.categoria,
    };
