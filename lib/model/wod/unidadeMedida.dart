class UnidadeMedida {
  String? titulo;
  EnumUnidadeMedida? enumerador;

  UnidadeMedida({this.titulo, this.enumerador});
}

class UnidadeMedidaDistancia {
  String? titulo;
  EnumDistancia? enumerador;

  UnidadeMedidaDistancia({this.titulo, this.enumerador});
}

class UnidadeMedidaCorporal {
  String? titulo;
  EnumPesoCorporal? enumerador;

  UnidadeMedidaCorporal({this.titulo, this.enumerador});
}

class UnidadeMedidaAltura {
  String? titulo;
  EnumAltura? enumerador;

  UnidadeMedidaAltura({this.titulo, this.enumerador});
}

enum EnumUnidadeMedida{

  QUILOS,      
  LIBRAS, 
}

enum EnumDistancia{
  QUILOMETRO,
  METRO
}

enum EnumPesoCorporal{
  QUILOS,      
  LIBRAS,
}

enum EnumAltura{
  CENTIMETRO,
  PE
}