// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jpinapp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JPInApp _$JPInAppFromJson(Map json) => JPInApp(
      nomeCampanha: json['nomeCampanha'] as String?,
      linkAbrir: json['linkAbrir'] as String?,
      mostrarNosEventos: (json['mostrarNosEventos'] as List<dynamic>?)
          ?.map((e) => $enumDecodeNullable(_$Eventos<PERSON>ey<PERSON>numMap, e))
          .toList(),
      campanhaAtiva: json['campanhaAtiva'] as bool?,
      urlMedia: json['urlMedia'] as String?,
      inicioFimCampanha: json['inicioFimCampanha'] == null
          ? null
          : CampanhaDateRange.fromJson(
              Map<String, dynamic>.from(json['inicioFimCampanha'] as Map)),
      mostrarEntreHorarios: (json['mostrarEntreHorarios'] as List<dynamic>?)
          ?.map((e) => CapanhaHorariosExibir.fromJson(
              Map<String, dynamic>.from(e as Map)))
          .toList(),
      empresasMostrar: (json['empresasMostrar'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      empresasNaoMostrar: (json['empresasNaoMostrar'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      publico: JPInApp.parseEnumPublico(json['publico']),
      tipoDeExebicao: $enumDecodeNullable(
              _$TipoDeExebicaoEnumMap, json['tipoDeExebicao']) ??
          TipoDeExebicao.DIARIO,
      key: json['key'] as String?,
      vezerPorDia: json['vezerPorDia'] as num? ?? 0,
    );

Map<String, dynamic> _$JPInAppToJson(JPInApp instance) => <String, dynamic>{
      'key': instance.key,
      'nomeCampanha': instance.nomeCampanha,
      'linkAbrir': instance.linkAbrir,
      'urlMedia': instance.urlMedia,
      'mostrarNosEventos': instance.mostrarNosEventos
          ?.map((e) => _$EventosKeyEnumMap[e])
          .toList(),
      'campanhaAtiva': instance.campanhaAtiva,
      'inicioFimCampanha': instance.inicioFimCampanha?.toJson(),
      'mostrarEntreHorarios':
          instance.mostrarEntreHorarios?.map((e) => e.toJson()).toList(),
      'empresasMostrar': instance.empresasMostrar,
      'empresasNaoMostrar': instance.empresasNaoMostrar,
      'tipoDeExebicao': _$TipoDeExebicaoEnumMap[instance.tipoDeExebicao],
      'vezerPorDia': instance.vezerPorDia,
      'publico': _$PublicoEnumMap[instance.publico],
    };

const _$EventosKeyEnumMap = {
  EventosKey.veio_de_banner_inapp: 'veio_de_banner_inapp',
  EventosKey.clickCompraReceita: 'clickCompraReceita',
  EventosKey.abriu_app: 'abriu_app',
  EventosKey.clickCompraDicas: 'clickCompraDicas',
  EventosKey.podomretro: 'podomretro',
  EventosKey.ativar_premium: 'ativar_premium',
  EventosKey.avaliou_treino: 'avaliou_treino',
  EventosKey.share_result_treino: 'share_result_treino',
  EventosKey.avaliou_app: 'avaliou_app',
  EventosKey.treino_cinco_estrelas: 'treino_cinco_estrelas',
  EventosKey.tabbar_home_fit: 'tabbar_home_fit',
  EventosKey.click_atv_hj: 'click_atv_hj',
  EventosKey.avaliou_professor: 'avaliou_professor',
  EventosKey.abrir_ad_premium: 'abrir_ad_premium',
  EventosKey.click_push: 'click_push',
  EventosKey.veioPorDeep: 'veioPorDeep',
  EventosKey.meuDia_Hoje: 'meuDia_Hoje',
  EventosKey.meuDia_Dietas: 'meuDia_Dietas',
  EventosKey.meuDia_Receitas: 'meuDia_Receitas',
  EventosKey.meuDia_Novidades: 'meuDia_Novidades',
  EventosKey.click_unlock_prm: 'click_unlock_prm',
  EventosKey.meuDia_Cafe: 'meuDia_Cafe',
  EventosKey.preLogin_sair: 'preLogin_sair',
  EventosKey.meuDia_Lanche1: 'meuDia_Lanche1',
  EventosKey.meuDia_Almoco: 'meuDia_Almoco',
  EventosKey.meuDia_Jantar: 'meuDia_Jantar',
  EventosKey.meuDia_Ceia: 'meuDia_Ceia',
  EventosKey.meuDia_Lanche2: 'meuDia_Lanche2',
  EventosKey.preLogin_jaSouAluno: 'preLogin_jaSouAluno',
  EventosKey.preLogin_aindaNaoSouAluno: 'preLogin_aindaNaoSouAluno',
  EventosKey.preLogin_professor: 'preLogin_professor',
  EventosKey.naoTenhoUsuario_sair: 'naoTenhoUsuario_sair',
  EventosKey.naoTenhoUsuario_localizarUnidades:
      'naoTenhoUsuario_localizarUnidades',
  EventosKey.localizarAcademia_sair: 'localizarAcademia_sair',
  EventosKey.localizarAcademia_adicMarcador: 'localizarAcademia_adicMarcador',
  EventosKey.localizarAcademia_ligar: 'localizarAcademia_ligar',
  EventosKey.localizarAcademia_pesquisar: 'localizarAcademia_pesquisar',
  EventosKey.loginProfessor_sair: 'loginProfessor_sair',
  EventosKey.loginProfessor_usuario: 'loginProfessor_usuario',
  EventosKey.loginProfessor_senha: 'loginProfessor_senha',
  EventosKey.loginProfessor_fazerLogin: 'loginProfessor_fazerLogin',
  EventosKey.loginProfessor_politicaDePrivacidade:
      'loginProfessor_politicaDePrivacidade',
  EventosKey.login_sair: 'login_sair',
  EventosKey.login_sucesso: 'login_sucesso',
  EventosKey.login_usuario: 'login_usuario',
  EventosKey.login_senha: 'login_senha',
  EventosKey.login_fazerLogin: 'login_fazerLogin',
  EventosKey.login_NaoTenhoUsuario: 'login_NaoTenhoUsuario',
  EventosKey.login_esqueceuSenha: 'login_esqueceuSenha',
  EventosKey.login_politicaDePrivacidade: 'login_politicaDePrivacidade',
  EventosKey.primeiroAcesso_sair: 'primeiroAcesso_sair',
  EventosKey.primeiroAcesso_email: 'primeiroAcesso_email',
  EventosKey.primeiroAcesso_celular: 'primeiroAcesso_celular',
  EventosKey.primeiroAcesso_buscar: 'primeiroAcesso_buscar',
  EventosKey.primeiroAcesso_conectarFacebook: 'primeiroAcesso_conectarFacebook',
  EventosKey.primeiroAcesso_conectarGoogle: 'primeiroAcesso_conectarGoogle',
  EventosKey.verificacao_sair: 'verificacao_sair',
  EventosKey.verificacao_receberSms: 'verificacao_receberSms',
  EventosKey.verificacao_receberEmail: 'verificacao_receberEmail',
  EventosKey.verificacao_NaoEncontrou: 'verificacao_NaoEncontrou',
  EventosKey.verificacaoCodigo_sair: 'verificacaoCodigo_sair',
  EventosKey.verificacaoCodigo_digitarCodigo: 'verificacaoCodigo_digitarCodigo',
  EventosKey.verificacaoCodigo_validar: 'verificacaoCodigo_validar',
  EventosKey.feed_menuLateral: 'feed_menuLateral',
  EventosKey.feed_publicarComCamera: 'feed_publicarComCamera',
  EventosKey.feed_publicarComGaleria: 'feed_publicarComGaleria',
  EventosKey.feed_curtir: 'feed_curtir',
  EventosKey.feed_comentar: 'feed_comentar',
  EventosKey.feed_compartilhar: 'feed_compartilhar',
  EventosKey.feed_removerPublicacao: 'feed_removerPublicacao',
  EventosKey.feed_salvarImagem: 'feed_salvarImagem',
  EventosKey.feed_denunciarPublicacao: 'feed_denunciarPublicacao',
  EventosKey.feed_cancelar: 'feed_cancelar',
  EventosKey.menuLateral_perfil: 'menuLateral_perfil',
  EventosKey.menuLateral_voltouFeed: 'menuLateral_voltouFeed',
  EventosKey.menuLateral_avaliacaoFisica: 'menuLateral_avaliacaoFisica',
  EventosKey.menuLateral_minhaAgenda: 'menuLateral_minhaAgenda',
  EventosKey.menuLateral_dicasDeSaude: 'menuLateral_dicasDeSaude',
  EventosKey.menuLateral_refeicoes: 'menuLateral_refeicoes',
  EventosKey.menuLateral_beberAgua: 'menuLateral_beberAgua',
  EventosKey.menuLateral_loja: 'menuLateral_loja',
  EventosKey.menuLateral_conversor: 'menuLateral_conversor',
  EventosKey.menuLateral_configuracoes: 'menuLateral_configuracoes',
  EventosKey.menuLateral_sobre: 'menuLateral_sobre',
  EventosKey.menuLateral_sair: 'menuLateral_sair',
  EventosKey.menuLateral_academia: 'menuLateral_academia',
  EventosKey.menuLateral_biomonitor: 'menuLateral_biomonitor',
  EventosKey.menulateral_creditos: 'menulateral_creditos',
  EventosKey.aulas_calendario: 'aulas_calendario',
  EventosKey.aulas_marcarAula: 'aulas_marcarAula',
  EventosKey.marcarAula_confirmar: 'marcarAula_confirmar',
  EventosKey.aulas_desmarcarAula: 'aulas_desmarcarAula',
  EventosKey.aulas_favoritar: 'aulas_favoritar',
  EventosKey.aulas_abrirAula: 'aulas_abrirAula',
  EventosKey.detalhesAulas_marcar: 'detalhesAulas_marcar',
  EventosKey.detalhesAulas_desmarcar: 'detalhesAulas_desmarcar',
  EventosKey.detalhesAulas_sair: 'detalhesAulas_sair',
  EventosKey.personalRecords_filtro: 'personalRecords_filtro',
  EventosKey.personalRecords_buscar: 'personalRecords_buscar',
  EventosKey.personalRecords_abrirRecord: 'personalRecords_abrirRecord',
  EventosKey.detalhesRecords_adicionar: 'detalhesRecords_adicionar',
  EventosKey.detalhesRecords_sair: 'detalhesRecords_sair',
  EventosKey.adicionarRecord_data: 'adicionarRecord_data',
  EventosKey.adicionarRecord_informacao: 'adicionarRecord_informacao',
  EventosKey.adicionarRecord_salvar: 'adicionarRecord_salvar',
  EventosKey.cross_adicionar: 'cross_adicionar',
  EventosKey.cross_calendario: 'cross_calendario',
  EventosKey.cross_ranking: 'cross_ranking',
  EventosKey.cross_Wod: 'cross_Wod',
  EventosKey.cross_music: 'cross_music',
  EventosKey.detalhesWod_sair: 'detalhesWod_sair',
  EventosKey.detalhesWod_adicionar: 'detalhesWod_adicionar',
  EventosKey.detalhesWod_enviarComentario: 'detalhesWod_enviarComentario',
  EventosKey.adicionarResultadoWod_Nivel: 'adicionarResultadoWod_Nivel',
  EventosKey.adicionarResultadoWod_valor: 'adicionarResultadoWod_valor',
  EventosKey.adicionarResultadoWod_salvar: 'adicionarResultadoWod_salvar',
  EventosKey.adicionarResultadoWod_compartilhar:
      'adicionarResultadoWod_compartilhar',
  EventosKey.rankings_adicionar: 'rankings_adicionar',
  EventosKey.rankings_abrir: 'rankings_abrir',
  EventosKey.detalheRanking_filtros: 'detalheRanking_filtros',
  EventosKey.detalheRanking_sair: 'detalheRanking_sair',
  EventosKey.filtroRanking_aplicar: 'filtroRanking_aplicar',
  EventosKey.treinoFichas_abrirDetalhesPrograma:
      'treinoFichas_abrirDetalhesPrograma',
  EventosKey.treinoFichas_abrirFicha: 'treinoFichas_abrirFicha',
  EventosKey.detalhesPrograma_fechar: 'detalhesPrograma_fechar',
  EventosKey.treino_sair: 'treino_sair',
  EventosKey.treino_music: 'treino_music',
  EventosKey.treino_comprimir: 'treino_comprimir',
  EventosKey.treino_expandir: 'treino_expandir',
  EventosKey.treino_detalheAtividade: 'treino_detalheAtividade',
  EventosKey.treino_iniciar: 'treino_iniciar',
  EventosKey.treino_online: 'treino_online',
  EventosKey.treinoExecucao_concluir: 'treinoExecucao_concluir',
  EventosKey.treinoExecucao_concluir_automaticamente:
      'treinoExecucao_concluir_automaticamente',
  EventosKey.treinoExecucao_sair: 'treinoExecucao_sair',
  EventosKey.treinoExecucao_pausa: 'treinoExecucao_pausa',
  EventosKey.treinoExecucao_detalheAtividade: 'treinoExecucao_detalheAtividade',
  EventosKey.treinoExecucao_concluirAtividade:
      'treinoExecucao_concluirAtividade',
  EventosKey.treinoExecucao_editarCarga: 'treinoExecucao_editarCarga',
  EventosKey.treinoFichas_desabilitou_descanso:
      'treinoFichas_desabilitou_descanso',
  EventosKey.treinoFichas_habilitou_descanso: 'treinoFichas_habilitou_descanso',
  EventosKey.treinoFichas_souAluno: 'treinoFichas_souAluno',
  EventosKey.treino_mensagemProfessor: 'treino_mensagemProfessor',
  EventosKey.pauseTreino_abandonar: 'pauseTreino_abandonar',
  EventosKey.pauseTreino_continuar: 'pauseTreino_continuar',
  EventosKey.pauseTreino_feed: 'pauseTreino_feed',
  EventosKey.parabensTreino_clickSwipe_nivel: 'parabensTreino_clickSwipe_nivel',
  EventosKey.parabensTreino_salvar: 'parabensTreino_salvar',
  EventosKey.feedPublicou_sucesso: 'feedPublicou_sucesso',
  EventosKey.notificacoes_expandir: 'notificacoes_expandir',
  EventosKey.notificacoes_swipeLeftorRigh_excluir:
      'notificacoes_swipeLeftorRigh_excluir',
  EventosKey.avaliacaoFisica_bioImpedancia: 'avaliacaoFisica_bioImpedancia',
  EventosKey.avaliacaoFisica_agendar: 'avaliacaoFisica_agendar',
  EventosKey.avaliacaoFisica_sair: 'avaliacaoFisica_sair',
  EventosKey.configBioAvaliacaoFisica_iniciar:
      'configBioAvaliacaoFisica_iniciar',
  EventosKey.configBioAvaliacaoFisica_sair: 'configBioAvaliacaoFisica_sair',
  EventosKey.configBioAvaliacaoFisica_altura: 'configBioAvaliacaoFisica_altura',
  EventosKey.configBioAvaliacaoFisica_idade: 'configBioAvaliacaoFisica_idade',
  EventosKey.configBioAvaliacaoFisica_proximo:
      'configBioAvaliacaoFisica_proximo',
  EventosKey.configBioAvaliacaoFisica_alimentacao:
      'configBioAvaliacaoFisica_alimentacao',
  EventosKey.configBioAvaliacaoFisica_roupasLeves:
      'configBioAvaliacaoFisica_roupasLeves',
  EventosKey.configBioAvaliacaoFisica_aparelhosEletronicos:
      'configBioAvaliacaoFisica_aparelhosEletronicos',
  EventosKey.configBioAvaliacaoFisica_acessorios:
      'configBioAvaliacaoFisica_acessorios',
  EventosKey.configBioAvaliacaoFisica_confirmar:
      'configBioAvaliacaoFisica_confirmar',
  EventosKey.agendarAvaliacaoFisica_sair: 'agendarAvaliacaoFisica_sair',
  EventosKey.agendarAvaliacaoFisica_marcar: 'agendarAvaliacaoFisica_marcar',
  EventosKey.detalhesAgendarAvaliacaoFisica_sair:
      'detalhesAgendarAvaliacaoFisica_sair',
  EventosKey.detalhesAgendarAvaliacaoFisica_marcar:
      'detalhesAgendarAvaliacaoFisica_marcar',
  EventosKey.avalicaoFisica_fotoFrontal: 'avalicaoFisica_fotoFrontal',
  EventosKey.avalicaoFisica_fotoFrontalContraido:
      'avalicaoFisica_fotoFrontalContraido',
  EventosKey.avalicaoFisica_costasFrontalContraido:
      'avalicaoFisica_costasFrontalContraido',
  EventosKey.avalicaoFisica_lateral: 'avalicaoFisica_lateral',
  EventosKey.avalicaoFisica_costas: 'avalicaoFisica_costas',
  EventosKey.agenda_sair: 'agenda_sair',
  EventosKey.dicasDeSaude_sair: 'dicasDeSaude_sair',
  EventosKey.dicasDeSaude_destaque: 'dicasDeSaude_destaque',
  EventosKey.dicasDeSaude_Novidade: 'dicasDeSaude_Novidade',
  EventosKey.dicasDeSaude_maisAcessada: 'dicasDeSaude_maisAcessada',
  EventosKey.dicasDeSaude_ultimasAcessada: 'dicasDeSaude_ultimasAcessada',
  EventosKey.detalhesSaude_sair: 'detalhesSaude_sair',
  EventosKey.detalhesSaude_curtir: 'detalhesSaude_curtir',
  EventosKey.detalhesSaude_descurtir: 'detalhesSaude_descurtir',
  EventosKey.detalhesSaude_outrasDicas: 'detalhesSaude_outrasDicas',
  EventosKey.configRefeicoes_inicio: 'configRefeicoes_inicio',
  EventosKey.configRefeicoes_ganhoMassa: 'configRefeicoes_ganhoMassa',
  EventosKey.configRefeicoes_emagrecimento: 'configRefeicoes_emagrecimento',
  EventosKey.configRefeicoes_frutosDoMar: 'configRefeicoes_frutosDoMar',
  EventosKey.configRefeicoes_frutose: 'configRefeicoes_frutose',
  EventosKey.configRefeicoes_gluten: 'configRefeicoes_gluten',
  EventosKey.configRefeicoes_lactose: 'configRefeicoes_lactose',
  EventosKey.configRefeicoes_proximo: 'configRefeicoes_proximo',
  EventosKey.configRefeicoes_1Lanche: 'configRefeicoes_1Lanche',
  EventosKey.configRefeicoes_2Lanche: 'configRefeicoes_2Lanche',
  EventosKey.configRefeicoes_3Lanche: 'configRefeicoes_3Lanche',
  EventosKey.configRefeicoes_clickView_sair: 'configRefeicoes_clickView_sair',
  EventosKey.refeicoes_config: 'refeicoes_config',
  EventosKey.refeicoes_sair: 'refeicoes_sair',
  EventosKey.refeicoes_jantar: 'refeicoes_jantar',
  EventosKey.refeicoes_lanche: 'refeicoes_lanche',
  EventosKey.refeicoes_almoco: 'refeicoes_almoco',
  EventosKey.refeicoes_ceia: 'refeicoes_ceia',
  EventosKey.refeicoes_cafe: 'refeicoes_cafe',
  EventosKey.refeicoes_cafe_hoje: 'refeicoes_cafe_hoje',
  EventosKey.refeicoes_almoco_hoje: 'refeicoes_almoco_hoje',
  EventosKey.refeicoes_lanche_hoje: 'refeicoes_lanche_hoje',
  EventosKey.refeicoes_jantar_hoje: 'refeicoes_jantar_hoje',
  EventosKey.detalhesRefeicoes_sair: 'detalhesRefeicoes_sair',
  EventosKey.detalhesRefeicoes_infoNutricao: 'detalhesRefeicoes_infoNutricao',
  EventosKey.refeicoesInfoNutricao_sair: 'refeicoesInfoNutricao_sair',
  EventosKey.refeicoesOutras_sair: 'refeicoesOutras_sair',
  EventosKey.refeicoesOutras_subistituir: 'refeicoesOutras_subistituir',
  EventosKey.beberAgua_sair: 'beberAgua_sair',
  EventosKey.beberAgua_adicionar: 'beberAgua_adicionar',
  EventosKey.beberAgua_estatisticas: 'beberAgua_estatisticas',
  EventosKey.beberAgua_config: 'beberAgua_config',
  EventosKey.configuracoBeberAgua_sair: 'configuracoBeberAgua_sair',
  EventosKey.configuracoBeberAgua_slide_quantidade:
      'configuracoBeberAgua_slide_quantidade',
  EventosKey.configuracoBeberAgua_slide_frequencia:
      'configuracoBeberAgua_slide_frequencia',
  EventosKey.configuracoBeberAgua_input_inicial:
      'configuracoBeberAgua_input_inicial',
  EventosKey.configuracoBeberAgua_input_final:
      'configuracoBeberAgua_input_final',
  EventosKey.configuracoBeberAgua_domingo: 'configuracoBeberAgua_domingo',
  EventosKey.configuracoBeberAgua_segunda: 'configuracoBeberAgua_segunda',
  EventosKey.configuracoBeberAgua_terca: 'configuracoBeberAgua_terca',
  EventosKey.configuracoBeberAgua_quarta: 'configuracoBeberAgua_quarta',
  EventosKey.configuracoBeberAgua_quinta: 'configuracoBeberAgua_quinta',
  EventosKey.configuracoBeberAgua_sexta: 'configuracoBeberAgua_sexta',
  EventosKey.configuracoBeberAgua_sabado: 'configuracoBeberAgua_sabado',
  EventosKey.beberAguaEstatisticas_sair: 'beberAguaEstatisticas_sair',
  EventosKey.shopping_sair: 'shopping_sair',
  EventosKey.conversor_sair: 'conversor_sair',
  EventosKey.configuracoes_sair: 'configuracoes_sair',
  EventosKey.configuracoes_Notificacoes: 'configuracoes_Notificacoes',
  EventosKey.configuracoes_som: 'configuracoes_som',
  EventosKey.configuracoes_Narracao: 'configuracoes_Narracao',
  EventosKey.configuracoes_pularDescanso: 'configuracoes_pularDescanso',
  EventosKey.configuracoes_idioma: 'configuracoes_idioma',
  EventosKey.configuracoes_unidadeDeMedidaLibras:
      'configuracoes_unidadeDeMedidaLibras',
  EventosKey.configuracoes_unidadeDeMedidaQuilos:
      'configuracoes_unidadeDeMedidaQuilos',
  EventosKey.sobre_sair: 'sobre_sair',
  EventosKey.sobre_politicaDePrivacidade: 'sobre_politicaDePrivacidade',
  EventosKey.sobre_atendimento: 'sobre_atendimento',
  EventosKey.sobre_versao: 'sobre_versao',
  EventosKey.sobre_biblioteca_de_codigoAberto:
      'sobre_biblioteca_de_codigoAberto',
  EventosKey.sobre_tutorial: 'sobre_tutorial',
  EventosKey.politicaDePrivacidade_sair: 'politicaDePrivacidade_sair',
  EventosKey.atendimento_sair: 'atendimento_sair',
  EventosKey.perfil_configuracaoPerfil: 'perfil_configuracaoPerfil',
  EventosKey.perfil_sair: 'perfil_sair',
  EventosKey.perfil_contrato: 'perfil_contrato',
  EventosKey.perfil_conta: 'perfil_conta',
  EventosKey.perfil_avaliacaoFisicaPerfil: 'perfil_avaliacaoFisicaPerfil',
  EventosKey.perfil_minhasPublicacoesPerfil: 'perfil_minhasPublicacoesPerfil',
  EventosKey.conta_sair: 'conta_sair',
  EventosKey.conta_senha: 'conta_senha',
  EventosKey.conta_telefone: 'conta_telefone',
  EventosKey.conta_email: 'conta_email',
  EventosKey.conta_pagamento: 'conta_pagamento',
  EventosKey.contrato_sair: 'contrato_sair',
  EventosKey.contrato_renovarContrato: 'contrato_renovarContrato',
  EventosKey.contrato_trancarContrato: 'contrato_trancarContrato',
  EventosKey.contrato_ferias: 'contrato_ferias',
  EventosKey.contaSenha_sair: 'contaSenha_sair',
  EventosKey.contaSenha_senhaAtual: 'contaSenha_senhaAtual',
  EventosKey.contaSenha_NovaSenha: 'contaSenha_NovaSenha',
  EventosKey.contaSenha_confirmarNovaSenha: 'contaSenha_confirmarNovaSenha',
  EventosKey.contaSenha_confirmar: 'contaSenha_confirmar',
  EventosKey.contaTelefone_sair: 'contaTelefone_sair',
  EventosKey.contaTelefone_telefone: 'contaTelefone_telefone',
  EventosKey.contaTelefone_confirmar: 'contaTelefone_confirmar',
  EventosKey.contaEmail_sair: 'contaEmail_sair',
  EventosKey.contaEmail_email: 'contaEmail_email',
  EventosKey.contaEmail_confirmar: 'contaEmail_confirmar',
  EventosKey.contaPagamento_sair: 'contaPagamento_sair',
  EventosKey.contaPagamento_NumeroCartao: 'contaPagamento_NumeroCartao',
  EventosKey.contaPagamento_titularCartao: 'contaPagamento_titularCartao',
  EventosKey.contaPagamento_validadeMes: 'contaPagamento_validadeMes',
  EventosKey.contaPagamento_validadeAno: 'contaPagamento_validadeAno',
  EventosKey.contaPagamento_cVV: 'contaPagamento_cVV',
  EventosKey.contaPagamento_confirmar: 'contaPagamento_confirmar',
  EventosKey.minhasPublicacoes_sair: 'minhasPublicacoes_sair',
  EventosKey.contratoFerias_sair: 'contratoFerias_sair',
  EventosKey.contratoFerias_dataInicio: 'contratoFerias_dataInicio',
  EventosKey.contratoFerias_dataFim: 'contratoFerias_dataFim',
  EventosKey.contratoFerias_motivo: 'contratoFerias_motivo',
  EventosKey.contratoFerias_confirmar: 'contratoFerias_confirmar',
  EventosKey.contratoTrancamento_sair: 'contratoTrancamento_sair',
  EventosKey.contratoTrancamento_proximo: 'contratoTrancamento_proximo',
  EventosKey.contratoTrancamento_periodo: 'contratoTrancamento_periodo',
  EventosKey.contratoRenovacao_sair: 'contratoRenovacao_sair',
  EventosKey.contratoRenovacao_confirmar: 'contratoRenovacao_confirmar',
  EventosKey.feedNewPost_sair: 'feedNewPost_sair',
  EventosKey.feedNewPost_publicar: 'feedNewPost_publicar',
  EventosKey.feedNewPost_legendaPost: 'feedNewPost_legendaPost',
  EventosKey.feedNewPost_clickSwipeButton_share:
      'feedNewPost_clickSwipeButton_share',
  EventosKey.feed_usuarioAtualizou: 'feed_usuarioAtualizou',
  EventosKey.feed_usuarioRolouPraBaixo: 'feed_usuarioRolouPraBaixo',
  EventosKey.tabbar_feed: 'tabbar_feed',
  EventosKey.tabbar_aulas: 'tabbar_aulas',
  EventosKey.tabbar_cross: 'tabbar_cross',
  EventosKey.tabbar_treino: 'tabbar_treino',
  EventosKey.tabbar_notificacoes: 'tabbar_notificacoes',
  EventosKey.tabbar_refeicoes: 'tabbar_refeicoes',
  EventosKey.tabbar_perfil: 'tabbar_perfil',
  EventosKey.premium_avancarParaDetalhes: 'premium_avancarParaDetalhes',
  EventosKey.premium_desistiuAntesDosDetalhes:
      'premium_desistiuAntesDosDetalhes',
  EventosKey.premium_clicouEmComprar: 'premium_clicouEmComprar',
  EventosKey.premium_clicouRestaurar: 'premium_clicouRestaurar',
  EventosKey.nome_academia: 'nome_academia',
  EventosKey.chave_academia: 'chave_academia',
  EventosKey.novoLoginBeta_sair: 'novoLoginBeta_sair',
  EventosKey.novoLoginBeta_celular: 'novoLoginBeta_celular',
  EventosKey.novoLoginBeta_buscar: 'novoLoginBeta_buscar',
  EventosKey.novoLoginBeta_acessarComUsuario: 'novoLoginBeta_acessarComUsuario',
  EventosKey.validacaoBeta_sair: 'validacaoBeta_sair',
  EventosKey.validacaoBeta_clicouEmValidar: 'validacaoBeta_clicouEmValidar',
  EventosKey.primeiroAcesso_comecar: 'primeiroAcesso_comecar',
  EventosKey.unidade_academia: 'unidade_academia',
  EventosKey.iniciouNovoLogin: 'iniciouNovoLogin',
  EventosKey.buscouSucesso: 'buscouSucesso',
  EventosKey.buscouNaoEncontrou: 'buscouNaoEncontrou',
  EventosKey.buscouDuplicado: 'buscouDuplicado',
  EventosKey.logouNovoLoginTelefone: 'logouNovoLoginTelefone',
  EventosKey.logouNovoLoginUsuario: 'logouNovoLoginUsuario',
  EventosKey.logouVelhoLogin: 'logouVelhoLogin',
  EventosKey.iniciouVelhoLogin: 'iniciouVelhoLogin',
  EventosKey.nutri_banner_inicio: 'nutri_banner_inicio',
  EventosKey.nutri_tabbar_inicio: 'nutri_tabbar_inicio',
  EventosKey.nutri_anamnese_obterPlano: 'nutri_anamnese_obterPlano',
  EventosKey.nutri_anamnese_pg1_objetivo: 'nutri_anamnese_pg1_objetivo',
  EventosKey.nutri_anamnese_pg2_sexo: 'nutri_anamnese_pg2_sexo',
  EventosKey.nutri_anamnese_pg3_altura: 'nutri_anamnese_pg3_altura',
  EventosKey.nutri_anamnese_pg4_metaPeso: 'nutri_anamnese_pg4_metaPeso',
  EventosKey.nutri_anamnese_pg5_qtRefeicoes: 'nutri_anamnese_pg5_qtRefeicoes',
  EventosKey.nutri_anamnese_pg6_variedade: 'nutri_anamnese_pg6_variedade',
  EventosKey.nutri_anamnese_pg7_atvFisica: 'nutri_anamnese_pg7_atvFisica',
  EventosKey.nutri_anamnese_pg8_restricao: 'nutri_anamnese_pg8_restricao',
  EventosKey.nutri_anamnese_ob_emagrecer: 'nutri_anamnese_ob_emagrecer',
  EventosKey.nutri_anamnese_ob_manterPeso: 'nutri_anamnese_ob_manterPeso',
  EventosKey.nutri_anamnese_ob_ganharMassa: 'nutri_anamnese_ob_ganharMassa',
  EventosKey.nutri_anamnese_sx_feminino: 'nutri_anamnese_sx_feminino',
  EventosKey.nutri_anamnese_sx_masculino: 'nutri_anamnese_sx_masculino',
  EventosKey.nutri_anamnese_qt_4: 'nutri_anamnese_qt_4',
  EventosKey.nutri_anamnese_qt_5: 'nutri_anamnese_qt_5',
  EventosKey.nutri_anamnese_qt_6: 'nutri_anamnese_qt_6',
  EventosKey.nutri_anamnese_variedade_nenhuma:
      'nutri_anamnese_variedade_nenhuma',
  EventosKey.nutri_anamnese_variedade_pouca: 'nutri_anamnese_variedade_pouca',
  EventosKey.nutri_anamnese_variedade_alta: 'nutri_anamnese_variedade_alta',
  EventosKey.nutri_anamnese_atv_nenhuma: 'nutri_anamnese_atv_nenhuma',
  EventosKey.nutri_anamnese_atv_1a2: 'nutri_anamnese_atv_1a2',
  EventosKey.nutri_anamnese_atv_3a5: 'nutri_anamnese_atv_3a5',
  EventosKey.nutri_anamnese_atv_todosDias: 'nutri_anamnese_atv_todosDias',
  EventosKey.nutri_anamnese_onboard_finalizou:
      'nutri_anamnese_onboard_finalizou',
  EventosKey.nutri_calendario_mudouDia: 'nutri_calendario_mudouDia',
  EventosKey.nutri_meuplano_verMais: 'nutri_meuplano_verMais',
  EventosKey.nutri_consumoagua_adicionou: 'nutri_consumoagua_adicionou',
  EventosKey.nutri_consumoAgua_removeu: 'nutri_consumoAgua_removeu',
  EventosKey.nutri_consumoAgua_abriuDetalhes: 'nutri_consumoAgua_abriuDetalhes',
  EventosKey.nutri_peso_aumentou: 'nutri_peso_aumentou',
  EventosKey.nutri_peso_diminuiu: 'nutri_peso_diminuiu',
  EventosKey.nutri_peso_abriuDetalhes: 'nutri_peso_abriuDetalhes',
  EventosKey.nutri_minhasRefeicoes_verMais: 'nutri_minhasRefeicoes_verMais',
  EventosKey.nutri_minhasRefeicoes_verDetalhes:
      'nutri_minhasRefeicoes_verDetalhes',
  EventosKey.nutri_minhasRefeicoes_registrar: 'nutri_minhasRefeicoes_registrar',
  EventosKey.nutri_minhasRefeicoes_cancelarRegistro:
      'nutri_minhasRefeicoes_cancelarRegistro',
  EventosKey.nutri_minhasRefeicoes_favoritar: 'nutri_minhasRefeicoes_favoritar',
  EventosKey.nutri_respiracao_acessar: 'nutri_respiracao_acessar',
  EventosKey.nutri_respiracao_iniciar: 'nutri_respiracao_iniciar',
  EventosKey.nutri_maisReceitas_acessar: 'nutri_maisReceitas_acessar',
  EventosKey.nutri_maisReceitas_detalhes: 'nutri_maisReceitas_detalhes',
  EventosKey.nutri_maisReceitas_filtros: 'nutri_maisReceitas_filtros',
  EventosKey.nutri_maisPlanos_acessar: 'nutri_maisPlanos_acessar',
  EventosKey.nutri_maisPlanos_acessarNovidade:
      'nutri_maisPlanos_acessarNovidade',
  EventosKey.nutri_maisPlanos_detalhes: 'nutri_maisPlanos_detalhes',
  EventosKey.nutri_maisPlanos_comecar: 'nutri_maisPlanos_comecar',
  EventosKey.nutri_dicasRapidas_acessar: 'nutri_dicasRapidas_acessar',
  EventosKey.nutri_dicasRapidas_detalhes: 'nutri_dicasRapidas_detalhes',
  EventosKey.nutri_listaCompras_acessar: 'nutri_listaCompras_acessar',
  EventosKey.nutri_listaCompras_definirPeriodo:
      'nutri_listaCompras_definirPeriodo',
  EventosKey.nutri_listaCompras_marcarAlimento:
      'nutri_listaCompras_marcarAlimento',
  EventosKey.nutri_listaCompras_exportar: 'nutri_listaCompras_exportar',
  EventosKey.nutri_minhaHidratacao_acessar: 'nutri_minhaHidratacao_acessar',
  EventosKey.nutri_minhaHidratacao_salvarAlteracoes:
      'nutri_minhaHidratacao_salvarAlteracoes',
  EventosKey.nutri_metaPeso_acessar: 'nutri_metaPeso_acessar',
  EventosKey.nutri_metaPeso_salvarAlteracoes: 'nutri_metaPeso_salvarAlteracoes',
  EventosKey.nutri_lembretes_acessar: 'nutri_lembretes_acessar',
  EventosKey.nutri_lembretes_salvarAlteracoes:
      'nutri_lembretes_salvarAlteracoes',
  EventosKey.nutri_planoClassico_comecar: 'nutri_planoClassico_comecar',
  EventosKey.nutri_confirmarReceitas_confirmarTodas:
      'nutri_confirmarReceitas_confirmarTodas',
  EventosKey.nutri_confirmarReceitas_finalizar:
      'nutri_confirmarReceitas_finalizar',
  EventosKey.nutri_confirmarReceitas_gostei: 'nutri_confirmarReceitas_gostei',
  EventosKey.nutri_confirmarReceitas_naoGostei:
      'nutri_confirmarReceitas_naoGostei',
  EventosKey.nutri_confirmarReceitas_iniciarAgora:
      'nutri_confirmarReceitas_iniciarAgora',
  EventosKey.nutri_confirmarReceitas_agendar: 'nutri_confirmarReceitas_agendar',
  EventosKey.nutri_premium_abriuPremium: 'nutri_premium_abriuPremium',
  EventosKey.nutri_premium_aproveitarOferta: 'nutri_premium_aproveitarOferta',
  EventosKey.nutri_premium_conferirPlanos: 'nutri_premium_conferirPlanos',
  EventosKey.nutri_premium_comprarMensal: 'nutri_premium_comprarMensal',
  EventosKey.nutri_premium_selecionouMensal: 'nutri_premium_selecionouMensal',
  EventosKey.nutri_premium_comprouPlanoMensal:
      'nutri_premium_comprouPlanoMensal',
  EventosKey.nutri_premium_selecionouAnual: 'nutri_premium_selecionouAnual',
  EventosKey.nutri_premium_comprarAnual: 'nutri_premium_comprarAnual',
  EventosKey.nutri_premium_comprouPlanoAnual: 'nutri_premium_comprouPlanoAnual',
  EventosKey.usou_wod: 'usou_wod',
  EventosKey.cross_pressionou_calendario: 'cross_pressionou_calendario',
  EventosKey.cross_pressionou_notificacoes: 'cross_pressionou_notificacoes',
  EventosKey.cross_pressionou_dia: 'cross_pressionou_dia',
  EventosKey.cross_pressionou_wod: 'cross_pressionou_wod',
  EventosKey.cross_pressionou_timer: 'cross_pressionou_timer',
  EventosKey.cross_pressionou_pr: 'cross_pressionou_pr',
  EventosKey.cross_ranking_pressionou_ver_mais:
      'cross_ranking_pressionou_ver_mais',
  EventosKey.wod_pressionou_exercicio: 'wod_pressionou_exercicio',
  EventosKey.wod_pressionou_postar_resultado: 'wod_pressionou_postar_resultado',
  EventosKey.wod_exercicio_pressionou_novo_record:
      'wod_exercicio_pressionou_novo_record',
  EventosKey.wod_exercicio_pressionou_ver_mais:
      'wod_exercicio_pressionou_ver_mais',
  EventosKey.wod_ranking_pressionou_ver_mais: 'wod_ranking_pressionou_ver_mais',
  EventosKey.novo_pr_pressionou_sair: 'novo_pr_pressionou_sair',
  EventosKey.novo_pr_pressionou_salvar: 'novo_pr_pressionou_salvar',
  EventosKey.meu_resultado_selecionou_categoria:
      'meu_resultado_selecionou_categoria',
  EventosKey.meu_resultado_pressionou_postar: 'meu_resultado_pressionou_postar',
  EventosKey.meu_resultado_saiu_sem_confirmar_alteracoes:
      'meu_resultado_saiu_sem_confirmar_alteracoes',
  EventosKey.ranking_pressionou_editar_resultado:
      'ranking_pressionou_editar_resultado',
  EventosKey.ranking_selecionou_categoria: 'ranking_selecionou_categoria',
  EventosKey.cronometro_pressionou_iniciar_timer:
      'cronometro_pressionou_iniciar_timer',
  EventosKey.emom_pressionou_iniciar_timer: 'emom_pressionou_iniciar_timer',
  EventosKey.tabata_pressionou_iniciar_timer: 'tabata_pressionou_iniciar_timer',
  EventosKey.timer_pressionou_minimizar: 'timer_pressionou_minimizar',
  EventosKey.timer_pressionou_play_pause: 'timer_pressionou_play_pause',
  EventosKey.timer_pressionou_stop: 'timer_pressionou_stop',
  EventosKey.records_pessoais_selecionou_tipo:
      'records_pessoais_selecionou_tipo',
  EventosKey.records_pessoais_pressionou_exercicio:
      'records_pessoais_pressionou_exercicio',
  EventosKey.treino_pressionou_notificacoes: 'treino_pressionou_notificacoes',
  EventosKey.treino_pressionou_treino_do_dia: 'treino_pressionou_treino_do_dia',
  EventosKey.treino_pressionou_sua_ficha: 'treino_pressionou_sua_ficha',
  EventosKey.ficha_pressionou_exercicio: 'ficha_pressionou_exercicio',
  EventosKey.ficha_pressionou_comecar_treino: 'ficha_pressionou_comecar_treino',
  EventosKey.ficha_exercicio_pressionou_add_foto:
      'ficha_exercicio_pressionou_add_foto',
  EventosKey.ficha_exercicio_pressionou_ver_video:
      'ficha_exercicio_pressionou_ver_video',
  EventosKey.ficha_exercicio_pressionou_editar:
      'ficha_exercicio_pressionou_editar',
  EventosKey.ficha_pressionou_pausar: 'ficha_pressionou_pausar',
  EventosKey.ficha_pressionou_finalizar: 'ficha_pressionou_finalizar',
  EventosKey.ficha_pressionou_concluir_serie: 'ficha_pressionou_concluir_serie',
  EventosKey.alterar_carga_salvou: 'alterar_carga_salvou',
  EventosKey.alterar_carga_checked_alterar_series:
      'alterar_carga_checked_alterar_series',
  EventosKey.controlar_descanso_pelo_app: 'controlar_descanso_pelo_app',
  EventosKey.nao_controlar_descanso_pelo_app: 'nao_controlar_descanso_pelo_app',
  EventosKey.treino_pausado_pressionou_stop: 'treino_pausado_pressionou_stop',
  EventosKey.treino_pausado_pressionou_play: 'treino_pausado_pressionou_play',
  EventosKey.treino_pausado_pressionou_home: 'treino_pausado_pressionou_home',
  EventosKey.pressionou_finalizar_execucao_treino:
      'pressionou_finalizar_execucao_treino',
  EventosKey.avaliacao_treino_selecionou_estrelas:
      'avaliacao_treino_selecionou_estrelas',
  EventosKey.avaliacao_treino_enviou_avaliacao:
      'avaliacao_treino_enviou_avaliacao',
  EventosKey.aulas_pressionou_dia: 'aulas_pressionou_dia',
  EventosKey.aulas_pressionou_more_vertical: 'aulas_pressionou_more_vertical',
  EventosKey.aulas_pressionou_calendario: 'aulas_pressionou_calendario',
  EventosKey.aulas_pressionou_checkin: 'aulas_pressionou_checkin',
  EventosKey.aulas_pressionou_cancelar_aula: 'aulas_pressionou_cancelar_aula',
  EventosKey.aulas_checkin_presionou_cancelar:
      'aulas_checkin_presionou_cancelar',
  EventosKey.aulas_checkin_pressionou_qrcode: 'aulas_checkin_pressionou_qrcode',
  EventosKey.aulas_pressionou_trocar_unidade: 'aulas_pressionou_trocar_unidade',
  EventosKey.aulas_calendario_pressionou_dia: 'aulas_calendario_pressionou_dia',
  EventosKey.aulas_calendario_pressionou_mes: 'aulas_calendario_pressionou_mes',
  EventosKey.aulas_pressionou_aula: 'aulas_pressionou_aula',
  EventosKey.aula_detalhes_pressionou_cancelar:
      'aula_detalhes_pressionou_cancelar',
  EventosKey.aula_detalhes_pressionou_checkin:
      'aula_detalhes_pressionou_checkin',
  EventosKey.em_casa_pressionou_ranking: 'em_casa_pressionou_ranking',
  EventosKey.em_casa_pressionou_em_alta: 'em_casa_pressionou_em_alta',
  EventosKey.em_casa_pressionou_filtro: 'em_casa_pressionou_filtro',
  EventosKey.em_casa_pressionou_treino: 'em_casa_pressionou_treino',
  EventosKey.em_casa_pressionou_ao_vivo: 'em_casa_pressionou_ao_vivo',
  EventosKey.ranking_tutorial_pressionou_acessar:
      'ranking_tutorial_pressionou_acessar',
  EventosKey.ranking_pressionou_medalhas: 'ranking_pressionou_medalhas',
  EventosKey.detalhes_treino_pressionou_video:
      'detalhes_treino_pressionou_video',
  EventosKey.detalhes_treino_pressionou_completar:
      'detalhes_treino_pressionou_completar',
  EventosKey.detalhes_do_treino_pressionou_compartilhar:
      'detalhes_do_treino_pressionou_compartilhar',
  EventosKey.compartilhar_pressionou_share: 'compartilhar_pressionou_share',
  EventosKey.compartilhar_escolheu_instagram: 'compartilhar_escolheu_instagram',
  EventosKey.compartilhar_salvou_na_galeria: 'compartilhar_salvou_na_galeria',
  EventosKey.agenda_pressionou_dia: 'agenda_pressionou_dia',
  EventosKey.pressionou_confirmar_agendamento:
      'pressionou_confirmar_agendamento',
  EventosKey.agenda_pressionou_marcar_disponivel:
      'agenda_pressionou_marcar_disponivel',
  EventosKey.agenda_pressionou_desmarcar: 'agenda_pressionou_desmarcar',
  EventosKey.confirmou_desmarcar_agendamento: 'confirmou_desmarcar_agendamento',
  EventosKey.pressionou_remarcar_agendamento: 'pressionou_remarcar_agendamento',
  EventosKey.reagendar_pressionou_dia: 'reagendar_pressionou_dia',
  EventosKey.reagendar_selecionou_horario: 'reagendar_selecionou_horario',
  EventosKey.reagendar_confirmou_agendamento: 'reagendar_confirmou_agendamento',
  EventosKey.minhas_aulas_pressionou_calendario:
      'minhas_aulas_pressionou_calendario',
  EventosKey.minhas_aulas_pressionou_detalhes:
      'minhas_aulas_pressionou_detalhes',
  EventosKey.minhas_aulas_pressionou_aula: 'minhas_aulas_pressionou_aula',
  EventosKey.aula_confirmou_presenca_aluno: 'aula_confirmou_presenca_aluno',
  EventosKey.presenca_qrcode_continuar: 'presenca_qrcode_continuar',
  EventosKey.drawer_selecionou_estou_em: 'drawer_selecionou_estou_em',
  EventosKey.drawer_pressionou_fichas: 'drawer_pressionou_fichas',
  EventosKey.drawer_pressionou_cadastrar_wod: 'drawer_pressionou_cadastrar_wod',
  EventosKey.drawer_pressionou_logout: 'drawer_pressionou_logout',
  EventosKey.drawer_confirmou_logout: 'drawer_confirmou_logout',
  EventosKey.fichas_pressionou_pular_tutorial:
      'fichas_pressionou_pular_tutorial',
  EventosKey.fichas_pressionou_filtrar: 'fichas_pressionou_filtrar',
  EventosKey.fichas_pressionou_nova_ficha: 'fichas_pressionou_nova_ficha',
  EventosKey.fichas_pressionou_ficha: 'fichas_pressionou_ficha',
  EventosKey.filtrar_treinos_selecionou_sexo: 'filtrar_treinos_selecionou_sexo',
  EventosKey.filtrar_treinos_selecionou_nivel:
      'filtrar_treinos_selecionou_nivel',
  EventosKey.filtrar_treinos_selecionou_categoria:
      'filtrar_treinos_selecionou_categoria',
  EventosKey.filtrar_treinos_selecionou_objetivos:
      'filtrar_treinos_selecionou_objetivos',
  EventosKey.ficha_selecionou_sexo: 'ficha_selecionou_sexo',
  EventosKey.ficha_selecionou_tipo: 'ficha_selecionou_tipo',
  EventosKey.ficha_selecionou_nivel: 'ficha_selecionou_nivel',
  EventosKey.ficha_selecionou_categoria: 'ficha_selecionou_categoria',
  EventosKey.ficha_pressionou_avancar: 'ficha_pressionou_avancar',
  EventosKey.ficha_salvou: 'ficha_salvou',
  EventosKey.dados_ficha_pressionou_editar: 'dados_ficha_pressionou_editar',
  EventosKey.dados_ficha_gerar_padrao_serie: 'dados_ficha_gerar_padrao_serie',
  EventosKey.padrao_serie_pressionou_salvar: 'padrao_serie_pressionou_salvar',
  EventosKey.dados_ficha_pressionou_switcher: 'dados_ficha_pressionou_switcher',
  EventosKey.dados_ficha_atividades_adicionar:
      'dados_ficha_atividades_adicionar',
  EventosKey.dados_ficha_escolher_aluno: 'dados_ficha_escolher_aluno',
  EventosKey.dados_ficha_atividade_removeu: 'dados_ficha_atividade_removeu',
  EventosKey.dados_ficha_pressionou_atividade:
      'dados_ficha_pressionou_atividade',
  EventosKey.selecionar_atividades_selecionou:
      'selecionar_atividades_selecionou',
  EventosKey.selecionar_atividades_adicionou: 'selecionar_atividades_adicionou',
  EventosKey.ficha_salvar_como_pre_definida: 'ficha_salvar_como_pre_definida',
  EventosKey.ficha_apenas_adicionar_ao_aluno: 'ficha_apenas_adicionar_ao_aluno',
  EventosKey.ficha_salva_adicionar_a_aluno: 'ficha_salva_adicionar_a_aluno',
  EventosKey.selecione_o_aluno_selecionou_categoria:
      'selecione_o_aluno_selecionou_categoria',
  EventosKey.selecione_o_aluno_selecionou: 'selecione_o_aluno_selecionou',
  EventosKey.selecione_o_aluno_adicionou_ficha:
      'selecione_o_aluno_adicionou_ficha',
  EventosKey.tentou_selecionar_mais_alunos: 'tentou_selecionar_mais_alunos',
  EventosKey.pressionou_add_a_outro_aluno: 'pressionou_add_a_outro_aluno',
  EventosKey.wods_pressionou_add_wod: 'wods_pressionou_add_wod',
  EventosKey.wods_pressionou_wod: 'wods_pressionou_wod',
  EventosKey.wods_pressionou_clonar: 'wods_pressionou_clonar',
  EventosKey.wods_pressionou_apagar: 'wods_pressionou_apagar',
  EventosKey.wod_pressionou_continuar: 'wod_pressionou_continuar',
  EventosKey.wod_selecionou_data: 'wod_selecionou_data',
  EventosKey.wod_selecionou_tipo: 'wod_selecionou_tipo',
  EventosKey.wod_pressionou_selecionar_imagem:
      'wod_pressionou_selecionar_imagem',
  EventosKey.wod_pressionou_adicionar_aparelho:
      'wod_pressionou_adicionar_aparelho',
  EventosKey.wod_pressionou_remover_aparelho: 'wod_pressionou_remover_aparelho',
  EventosKey.wod_pressionou_salvar: 'wod_pressionou_salvar',
  EventosKey.wod_pressionou_adicionar_atividade:
      'wod_pressionou_adicionar_atividade',
  EventosKey.wod_pressionou_remover_atividade:
      'wod_pressionou_remover_atividade',
  EventosKey.aparelhos_cross_pressionou_adicionar:
      'aparelhos_cross_pressionou_adicionar',
  EventosKey.aparelhos_cross_selecionou: 'aparelhos_cross_selecionou',
  EventosKey.atividades_pressionou_adicionar: 'atividades_pressionou_adicionar',
  EventosKey.atividades_selecionou: 'atividades_selecionou',
  EventosKey.wod_pressionou_trocar_imagem: 'wod_pressionou_trocar_imagem',
  EventosKey.camera_pressionou_capturar: 'camera_pressionou_capturar',
  EventosKey.camera_pressionou_fotos: 'camera_pressionou_fotos',
  EventosKey.recortar_imagem_pressionou_avancar:
      'recortar_imagem_pressionou_avancar',
  EventosKey.aplicar_filtro_concluiu: 'aplicar_filtro_concluiu',
  EventosKey.aplicar_filtro_selecionou_filtro:
      'aplicar_filtro_selecionou_filtro',
  EventosKey.inicio_meus_programas_detalhes: 'inicio_meus_programas_detalhes',
  EventosKey.inicio_aulas_ver_mais: 'inicio_aulas_ver_mais',
  EventosKey.inicio_gerencie_ver_aulas: 'inicio_gerencie_ver_aulas',
  EventosKey.prescricoes_selecionou_total_fazer:
      'prescricoes_selecionou_total_fazer',
  EventosKey.prescricoes_selecionou_renovacoes:
      'prescricoes_selecionou_renovacoes',
  EventosKey.prescricoes_selecionou_sem_treino:
      'prescricoes_selecionou_sem_treino',
  EventosKey.prescricoes_pressionou_aluno: 'prescricoes_pressionou_aluno',
  EventosKey.aluno_reconhecimento_facial: 'aluno_reconhecimento_facial',
  EventosKey.aluno_pressionou_mensagem: 'aluno_pressionou_mensagem',
  EventosKey.aluno_avaliacao_fisica_detalhes: 'aluno_avaliacao_fisica_detalhes',
  EventosKey.aluno_programa_treino_detalhes: 'aluno_programa_treino_detalhes',
  EventosKey.aluno_historico_ver_mais: 'aluno_historico_ver_mais',
  EventosKey.aluno_observacoes_ver_mais: 'aluno_observacoes_ver_mais',
  EventosKey.aluno_frequencia_selecionou: 'aluno_frequencia_selecionou',
  EventosKey.aluno_cadastrar_nova_ficha: 'aluno_cadastrar_nova_ficha',
  EventosKey.aluno_programa_treino_renovar: 'aluno_programa_treino_renovar',
  EventosKey.aluno_programa_treino_criar: 'aluno_programa_treino_criar',
  EventosKey.registro_facial_continuar_registro:
      'registro_facial_continuar_registro',
  EventosKey.programa_selecionou_inicio: 'programa_selecionou_inicio',
  EventosKey.programa_selecionou_termino: 'programa_selecionou_termino',
  EventosKey.programa_selecionou_objetivo: 'programa_selecionou_objetivo',
  EventosKey.programa_desselecionou_objetivo: 'programa_desselecionou_objetivo',
  EventosKey.programa_pressionou_salvar: 'programa_pressionou_salvar',
  EventosKey.coach_programa_treino_pulou: 'coach_programa_treino_pulou',
  EventosKey.alunos_selecionou_vencidos: 'alunos_selecionou_vencidos',
  EventosKey.alunos_selecionou_minha_carteira:
      'alunos_selecionou_minha_carteira',
  EventosKey.alunos_selecionou_sem_treino: 'alunos_selecionou_sem_treino',
  EventosKey.alunos_selecionou_inativos: 'alunos_selecionou_inativos',
  EventosKey.alunos_pressionou_aluno: 'alunos_pressionou_aluno',
  EventosKey.criar_treino_on_detalhes_definir:
      'criar_treino_on_detalhes_definir',
  EventosKey.criar_treino_on_pressionou_cam: 'criar_treino_on_pressionou_cam',
  EventosKey.criar_treino_on_selecionou_capa: 'criar_treino_on_selecionou_capa',
  EventosKey.detalhes_pressionou_detalhe: 'detalhes_pressionou_detalhe',
  EventosKey.criar_treino_on_pressionou_proximo:
      'criar_treino_on_pressionou_proximo',
  EventosKey.detalhes_pressionou_criar_tag: 'detalhes_pressionou_criar_tag',
  EventosKey.criar_tag_pressionou_salvar: 'criar_tag_pressionou_salvar',
  EventosKey.criar_treino_on_adicionar_video: 'criar_treino_on_adicionar_video',
  EventosKey.criar_treino_on_pressionou_salvar:
      'criar_treino_on_pressionou_salvar',
  EventosKey.adicionar_video_pressionou_salvar:
      'adicionar_video_pressionou_salvar',
  EventosKey.criar_treino_on_remover_video: 'criar_treino_on_remover_video',
  EventosKey.selecione_academia_selecionou_academia:
      'selecione_academia_selecionou_academia',
  EventosKey.acessar_com_telefone_selecionou_pais:
      'acessar_com_telefone_selecionou_pais',
  EventosKey.acessar_com_telefone_continuar: 'acessar_com_telefone_continuar',
  EventosKey.acessar_com_telefone_acessar_com_email:
      'acessar_com_telefone_acessar_com_email',
  EventosKey.acessar_com_telefone_ainda_nao_e_aluno:
      'acessar_com_telefone_ainda_nao_e_aluno',
  EventosKey.bem_vindo_entrar_com_outro_usuario:
      'bem_vindo_entrar_com_outro_usuario',
  EventosKey.bem_vindo_entrou_com_usuario_logado:
      'bem_vindo_entrou_com_usuario_logado',
  EventosKey.acessar_com_telefone_usuarios_logados:
      'acessar_com_telefone_usuarios_logados',
  EventosKey.acessar_com_email_continuar: 'acessar_com_email_continuar',
  EventosKey.acessar_com_email_acessar_com_telefone:
      'acessar_com_email_acessar_com_telefone',
  EventosKey.acessar_com_email_esqueci_senha: 'acessar_com_email_esqueci_senha',
  EventosKey.recuperacao_senha_continuar: 'recuperacao_senha_continuar',
  EventosKey.login_cpf_continuar: 'login_cpf_continuar',
  EventosKey.login_cpf_acessar_com_email: 'login_cpf_acessar_com_email',
  EventosKey.inicio_pressionou_mensagens: 'inicio_pressionou_mensagens',
  EventosKey.inicio_pressionou_notificacoes: 'inicio_pressionou_notificacoes',
  EventosKey.inicio_pressionou_dia: 'inicio_pressionou_dia',
  EventosKey.inicio_pressionou_treino_do_dia: 'inicio_pressionou_treino_do_dia',
  EventosKey.inicio_proximas_aulas_agendar: 'inicio_proximas_aulas_agendar',
  EventosKey.inicio_cross_ver_mais: 'inicio_cross_ver_mais',
  EventosKey.inicio_ativar_monitor_de_passos: 'inicio_ativar_monitor_de_passos',
  EventosKey.feed_pressionou_notificacoes: 'feed_pressionou_notificacoes',
  EventosKey.feed_pressionou_nova_publicacao: 'feed_pressionou_nova_publicacao',
  EventosKey.feed_popup_pressionou_comentar: 'feed_popup_pressionou_comentar',
  EventosKey.feed_popup_pressionou_remover: 'feed_popup_pressionou_remover',
  EventosKey.feed_popup_pressionou_denunciar: 'feed_popup_pressionou_denunciar',
  EventosKey.feed_popup_pressionou_bloquear: 'feed_popup_pressionou_bloquear',
  EventosKey.bloquear_confirmou_bloqueio: 'bloquear_confirmou_bloqueio',
  EventosKey.pressionou_popup_postagem: 'pressionou_popup_postagem',
  EventosKey.post_pressionou_like: 'post_pressionou_like',
  EventosKey.feed_pressionou_comentar: 'feed_pressionou_comentar',
  EventosKey.notificacoes_pressionou_notificacao:
      'notificacoes_pressionou_notificacao',
  EventosKey.nova_publicacao_pressionou_enviar:
      'nova_publicacao_pressionou_enviar',
  EventosKey.nova_publicacao_adicionar_foto: 'nova_publicacao_adicionar_foto',
  EventosKey.proximas_aulas_cancelar_aula: 'proximas_aulas_cancelar_aula',
  EventosKey.comentarios_pressionou_enviar_comentario:
      'comentarios_pressionou_enviar_comentario',
  EventosKey.denuncia_pressionou_denunciar_post:
      'denuncia_pressionou_denunciar_post',
  EventosKey.drawer_pressionou_configurar_app:
      'drawer_pressionou_configurar_app',
  EventosKey.drawer_pressionou_campanhas_inapp:
      'drawer_pressionou_campanhas_inapp',
  EventosKey.drawer_pressionou_contratos: 'drawer_pressionou_contratos',
  EventosKey.drawer_pressionou_minha_conta: 'drawer_pressionou_minha_conta',
  EventosKey.drawer_pressionou_avaliacao_fisica:
      'drawer_pressionou_avaliacao_fisica',
  EventosKey.drawer_pressionou_publicacoes: 'drawer_pressionou_publicacoes',
  EventosKey.drawer_pressionou_avaliar_professor:
      'drawer_pressionou_avaliar_professor',
  EventosKey.drawer_pressionou_sobre_o_app: 'drawer_pressionou_sobre_o_app',
  EventosKey.escolha_tema_selecionou_claro: 'escolha_tema_selecionou_claro',
  EventosKey.escolha_tema_selecionou_escuro: 'escolha_tema_selecionou_escuro',
  EventosKey.escolha_tema_selecionou_automatico:
      'escolha_tema_selecionou_automatico',
  EventosKey.escolha_tema_pressionou_ok: 'escolha_tema_pressionou_ok',
  EventosKey.configuracoes_pressionou_tema: 'configuracoes_pressionou_tema',
  EventosKey.configuracoes_descanso_switcher: 'configuracoes_descanso_switcher',
  EventosKey.configuracoes_unidade_libras: 'configuracoes_unidade_libras',
  EventosKey.configuracoes_unidade_quilos: 'configuracoes_unidade_quilos',
  EventosKey.campanhas_cadastrar_campanha: 'campanhas_cadastrar_campanha',
  EventosKey.campanhas_pressionou_campanha: 'campanhas_pressionou_campanha',
  EventosKey.campanha_validar_campanha: 'campanha_validar_campanha',
  EventosKey.campanha_switcher_campanha_ativar:
      'campanha_switcher_campanha_ativar',
  EventosKey.campanha_upload_de_imagem: 'campanha_upload_de_imagem',
  EventosKey.campanha_definiu_periodicidade: 'campanha_definiu_periodicidade',
  EventosKey.campanha_definiu_inicio: 'campanha_definiu_inicio',
  EventosKey.campanha_definiu_fim: 'campanha_definiu_fim',
  EventosKey.campanha_definiu_hora_inicio: 'campanha_definiu_hora_inicio',
  EventosKey.campanha_definiu_hora_fim: 'campanha_definiu_hora_fim',
  EventosKey.campanha_pressionou_novo_range: 'campanha_pressionou_novo_range',
  EventosKey.campanha_deletou_range_horarios: 'campanha_deletou_range_horarios',
  EventosKey.campanha_definiu_range_horarios: 'campanha_definiu_range_horarios',
  EventosKey.contrato_pressionou_trancamento: 'contrato_pressionou_trancamento',
  EventosKey.contrato_pressionou_renovar: 'contrato_pressionou_renovar',
  EventosKey.contrato_pressionou_credito: 'contrato_pressionou_credito',
  EventosKey.trancamento_selecionou_produto: 'trancamento_selecionou_produto',
  EventosKey.trancamento_selecionou_motivo: 'trancamento_selecionou_motivo',
  EventosKey.trancamento_pressionou_avancar: 'trancamento_pressionou_avancar',
  EventosKey.trancamento_confirmou_trancamento:
      'trancamento_confirmou_trancamento',
  EventosKey.perfil_pressionou_minha_conta: 'perfil_pressionou_minha_conta',
  EventosKey.perfil_pressionou_foto: 'perfil_pressionou_foto',
  EventosKey.perfil_pressionou_graduacao: 'perfil_pressionou_graduacao',
  EventosKey.perfil_pressionou_premium: 'perfil_pressionou_premium',
  EventosKey.perfil_pressionou_avaliacao_fisica:
      'perfil_pressionou_avaliacao_fisica',
  EventosKey.perfil_pressionou_meus_contratos:
      'perfil_pressionou_meus_contratos',
  EventosKey.minha_conta_trocar_foto_perfil: 'minha_conta_trocar_foto_perfil',
  EventosKey.creditos_contrato_selecionou_extrato:
      'creditos_contrato_selecionou_extrato',
  EventosKey.minha_conta_acesso_editar: 'minha_conta_acesso_editar',
  EventosKey.minha_conta_contato_editar: 'minha_conta_contato_editar',
  EventosKey.alterar_senha_pressionou_salvar: 'alterar_senha_pressionou_salvar',
  EventosKey.dados_contato_pressionou_salvar: 'dados_contato_pressionou_salvar',
  EventosKey.minhas_assinaturas_pressionou_ticket:
      'minhas_assinaturas_pressionou_ticket',
  EventosKey.avaliacao_fisica_selecionou_perimetria:
      'avaliacao_fisica_selecionou_perimetria',
  EventosKey.avaliar_professor_selecionar_professor:
      'avaliar_professor_selecionar_professor',
  EventosKey.avaliar_professor_selecionou_estrelas:
      'avaliar_professor_selecionou_estrelas',
  EventosKey.avaliar_professor_pressionou_confirmar:
      'avaliar_professor_pressionou_confirmar',
  EventosKey.selecione_professor_selecionou_professor:
      'selecione_professor_selecionou_professor',
  EventosKey.ajuda_pressionou_video: 'ajuda_pressionou_video',
  EventosKey.ajuda_pressionou_refazer_tutorial:
      'ajuda_pressionou_refazer_tutorial',
  EventosKey.ajuda_pressionou_informacao: 'ajuda_pressionou_informacao',
  EventosKey.refazer_tutorial_pressionou_refazer:
      'refazer_tutorial_pressionou_refazer',
  EventosKey.wod_postar_resultado_home: 'wod_postar_resultado_home',
  EventosKey.entrou_fila_de_espera: 'entrou_fila_de_espera',
  EventosKey.acessou_acompanhamento_aluno: 'acessou_acompanhamento_aluno',
  EventosKey.acessou_pagamento_parcelas_atrasadas:
      'acessou_pagamento_parcelas_atrasadas',
  EventosKey.pagamento_parcelas_atrasadas_aprovado:
      'pagamento_parcelas_atrasadas_aprovado',
  EventosKey.pagamento_parcelas_atrasadas_negado:
      'pagamento_parcelas_atrasadas_negado',
  EventosKey.click_aprovouTreinoRevisadoIA: 'click_aprovouTreinoRevisadoIA',
  EventosKey.abriuTelaRevisarTreinoDeAlunoIA: 'abriuTelaRevisarTreinoDeAlunoIA',
  EventosKey.click_revisarTreinoIA: 'click_revisarTreinoIA',
  EventosKey.click_aprovarTreinoIA: 'click_aprovarTreinoIA',
  EventosKey.abriu_treinos_ia_revisar: 'abriu_treinos_ia_revisar',
  EventosKey.click_obterMeuTreinoIA: 'click_obterMeuTreinoIA',
  EventosKey.click_alunoTemRestricaoIA: 'click_alunoTemRestricaoIA',
  EventosKey.click_alunoNaoTemRestricaoIA: 'click_alunoNaoTemRestricaoIA',
  EventosKey.click_AlunoAvancarQuestinarioIA: 'click_AlunoAvancarQuestinarioIA',
  EventosKey.click_alunoConfirmouSemRestricoes:
      'click_alunoConfirmouSemRestricoes',
  EventosKey.click_AlunoObjEmagrecimento: 'click_AlunoObjEmagrecimento',
  EventosKey.click_AlunoObjEmaGanhoDeMassa: 'click_AlunoObjEmaGanhoDeMassa',
  EventosKey.click_AlunoObjQualidadeVida: 'click_AlunoObjQualidadeVida',
  EventosKey.click_AlunoObjControlarDoenca: 'click_AlunoObjControlarDoenca',
  EventosKey.click_AlunoGeneroMasculino: 'click_AlunoGeneroMasculino',
  EventosKey.click_AlunoGeneroFeminino: 'click_AlunoGeneroFeminino',
  EventosKey.aluno_escolheuIdadeIA: 'aluno_escolheuIdadeIA',
  EventosKey.aluno_escolheuAlturaIA: 'aluno_escolheuAlturaIA',
  EventosKey.aluno_escolheuNivelIA: 'aluno_escolheuNivelIA',
  EventosKey.click_alunoSelecionouExperienciaIA:
      'click_alunoSelecionouExperienciaIA',
  EventosKey.alunoSelecionouDiasPorSemanaIA: 'alunoSelecionouDiasPorSemanaIA',
  EventosKey.lgn_apple: 'lgn_apple',
  EventosKey.lgn_google: 'lgn_google',
  EventosKey.lgn_facebook: 'lgn_facebook',
  EventosKey.lgn_com_user: 'lgn_com_user',
  EventosKey.USUARIO_FEZ_LOGIN_APP_TREINO: 'USUARIO_FEZ_LOGIN_APP_TREINO',
  EventosKey.CHECKIN_AULA_APP_TREINO: 'CHECKIN_AULA_APP_TREINO',
  EventosKey.TREINO_CONCLUIDO_APP_TREINO: 'TREINO_CONCLUIDO_APP_TREINO',
  EventosKey.lgn_solicitarsms: 'lgn_solicitarsms',
  EventosKey.lgn_vincularApple: 'lgn_vincularApple',
  EventosKey.lgn_validousms: 'lgn_validousms',
  EventosKey.lgn_userPass: 'lgn_userPass',
  EventosKey.lgn_telefone: 'lgn_telefone',
  EventosKey.lgn_novoSMS: 'lgn_novoSMS',
  EventosKey.lgn_novoEmail: 'lgn_novoEmail',
  EventosKey.lgn_recent_user: 'lgn_recent_user',
  EventosKey.lgn_solicitar_email: 'lgn_solicitar_email',
  EventosKey.lgn_por_senha: 'lgn_por_senha',
  EventosKey.lgn_validarsms: 'lgn_validarsms',
  EventosKey.lgn_veio_de_link_email: 'lgn_veio_de_link_email',
  EventosKey.lgn_resolicitarsms: 'lgn_resolicitarsms',
  EventosKey.alunoSelecionouMinutosIA: 'alunoSelecionouMinutosIA',
  EventosKey.fechou_banner_inapp: 'fechou_banner_inapp',
  EventosKey.log_out_preso_splash: 'log_out_preso_splash',
  EventosKey.sem_health: 'sem_health',
  EventosKey.sem_health_baixar_google_fit: 'sem_health_baixar_google_fit',
  EventosKey.stuck_to_home: 'stuck_to_home',
  EventosKey.aluno_escolheuPesoIA: 'aluno_escolheuPesoIA',
  EventosKey.abriu_tela_chat: 'abriu_tela_chat',
};

const _$TipoDeExebicaoEnumMap = {
  TipoDeExebicao.DIARIO: 'DIARIO',
  TipoDeExebicao.SEMANAL: 'SEMANAL',
  TipoDeExebicao.MENSAL: 'MENSAL',
  TipoDeExebicao.PORCLICK: 'PORCLICK',
};

const _$PublicoEnumMap = {
  Publico.TODOS: 'TODOS',
  Publico.APENASALUNOS: 'APENASALUNOS',
  Publico.PREMIUM: 'PREMIUM',
  Publico.NAOPREMIUM: 'NAOPREMIUM',
  Publico.APENASCOLABORADORES: 'APENASCOLABORADORES',
  Publico.APENASCOLABORADORESDEEMPRESASCOMPREMIUM:
      'APENASCOLABORADORESDEEMPRESASCOMPREMIUM',
};

CapanhaHorariosExibir _$CapanhaHorariosExibirFromJson(Map json) =>
    CapanhaHorariosExibir(
      horaInicio: json['horaInicio'] as String?,
      horaFim: json['horaFim'] as String?,
    );

Map<String, dynamic> _$CapanhaHorariosExibirToJson(
        CapanhaHorariosExibir instance) =>
    <String, dynamic>{
      'horaInicio': instance.horaInicio,
      'horaFim': instance.horaFim,
    };

CampanhaDateRange _$CampanhaDateRangeFromJson(Map json) => CampanhaDateRange(
      start: json['start'] == null
          ? null
          : DateTime.parse(json['start'] as String),
      end: json['end'] == null ? null : DateTime.parse(json['end'] as String),
    );

Map<String, dynamic> _$CampanhaDateRangeToJson(CampanhaDateRange instance) =>
    <String, dynamic>{
      'start': instance.start?.toIso8601String(),
      'end': instance.end?.toIso8601String(),
    };

RegistroExibicaoInApp _$RegistroExibicaoInAppFromJson(Map json) =>
    RegistroExibicaoInApp(
      dia: json['dia'] as String?,
      key: json['key'] as String?,
    );

Map<String, dynamic> _$RegistroExibicaoInAppToJson(
        RegistroExibicaoInApp instance) =>
    <String, dynamic>{
      'key': instance.key,
      'dia': instance.dia,
    };

SelecaoTipoDeExebicao _$SelecaoTipoDeExebicaoFromJson(Map json) =>
    SelecaoTipoDeExebicao(
      nome: json['nome'] as String?,
      tipo: SelecaoTipoDeExebicao.parseEnumPublico(json['tipo']),
    );

Map<String, dynamic> _$SelecaoTipoDeExebicaoToJson(
        SelecaoTipoDeExebicao instance) =>
    <String, dynamic>{
      'nome': instance.nome,
      'tipo': _$TipoDeExebicaoEnumMap[instance.tipo],
    };
