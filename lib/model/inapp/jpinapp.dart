import 'package:app_treino/config/EventosKey.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:json_annotation/json_annotation.dart';
part 'jpinapp.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class JPInApp {
  factory JPInApp.fromJson(Map<String, dynamic> json) => _$JPInAppFromJson(json);
  Map<String, dynamic> toJson() => _$JPInAppToJson(this);
  JPInApp clone() => _$JPInAppFromJson(this.toJson());
  String? key;
  String? nomeCampanha;
  String? linkAbrir;
  String? urlMedia;
  List<EventosKey?>? mostrarNosEventos;
  bool? campanhaAtiva;
  CampanhaDateRange? inicioFimCampanha;
  List<CapanhaHorariosExibir>? mostrarEntreHorarios;

  List<String>? empresasMostrar = [];
  List<String>? empresasNaoMostrar = [];
  TipoDeExebicao? tipoDeExebicao;
  @JsonKey(defaultValue: 0)
  num vezerPorDia = 0;
  @JsonKey(fromJson: parseEnumPublico)
  Publico? publico;

  static parseEnumPublico(any) {
    if (Publico.values.any((element) => element.toString().replaceAll('Publico.', '') == any.toString())) {
      return Publico.values.firstWhere((element) => element.toString().replaceAll('Publico.', '') == any.toString());
    }
    return null;
  }

  static JPInApp get expample {
    return JPInApp(
      campanhaAtiva: true,
      key: 'App3',
      vezerPorDia: 0,
      publico: Publico.TODOS,
      inicioFimCampanha: CampanhaDateRange(start: DateTime.utc(2021, 1, 03, 16, 0, 0), end: DateTime.utc(2021, 9, 03, 16, 0, 0)),
      linkAbrir: '/telaAdPremium',
      urlMedia: 'https://i.imgur.com/PHjW1i5.jpeg',
      mostrarNosEventos: [
        EventosKey.abrir_ad_premium,
      ],
      nomeCampanha: 'Campanha X',
      mostrarEntreHorarios: [CapanhaHorariosExibir(horaInicio: '06:00', horaFim: '23:00')],
    );
  }

  JPInApp(
      {this.nomeCampanha,
      this.linkAbrir,
      this.mostrarNosEventos,
      this.campanhaAtiva,
      this.urlMedia,
      this.inicioFimCampanha,
      this.mostrarEntreHorarios,
      this.empresasMostrar,
      this.empresasNaoMostrar,
      this.publico,
      this.tipoDeExebicao = TipoDeExebicao.DIARIO,
      this.key,
      this.vezerPorDia = 0});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class CapanhaHorariosExibir {
  String? horaInicio, horaFim;
  CapanhaHorariosExibir({this.horaInicio, this.horaFim});

  factory CapanhaHorariosExibir.fromJson(Map<String, dynamic> json) => _$CapanhaHorariosExibirFromJson(json);
  Map<String, dynamic> toJson() => _$CapanhaHorariosExibirToJson(this);
  CapanhaHorariosExibir clone() => _$CapanhaHorariosExibirFromJson(this.toJson());

  bool get estouNoRange {
    try {
      DateTime now = DateTime.now();
      DateTime open = DateFormat('HH:mm').parse(horaInicio!);
      DateTime close = DateFormat('HH:mm').parse(horaFim!);
      open = DateTime(now.year, now.month, now.day, open.hour, open.minute);
      close = DateTime(now.year, now.month, now.day, close.hour, close.minute);
      return now.isAfter(open) && now.isBefore(close);
    } catch (e) {
      return false;
    }
  }
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class CampanhaDateRange {
  factory CampanhaDateRange.fromJson(Map<String, dynamic> json) => _$CampanhaDateRangeFromJson(json);
  Map<String, dynamic> toJson() => _$CampanhaDateRangeToJson(this);
  CampanhaDateRange clone() => _$CampanhaDateRangeFromJson(this.toJson());

  DateTime? start, end;

  CampanhaDateRange({this.start, this.end});
  bool valido() {
    return start != null && end != null;
  }

  bool estaNoRange() {
    return DateTime.now().isAfter(start!) && DateTime.now().isBefore(end!);
  }
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class RegistroExibicaoInApp {
  factory RegistroExibicaoInApp.fromJson(Map<String, dynamic> json) => _$RegistroExibicaoInAppFromJson(json);
  Map<String, dynamic> toJson() => _$RegistroExibicaoInAppToJson(this);
  RegistroExibicaoInApp clone() => _$RegistroExibicaoInAppFromJson(this.toJson());
  String? key;
  String? dia;
  RegistroExibicaoInApp({this.dia, this.key});
}

enum Publico { TODOS, APENASALUNOS, PREMIUM, NAOPREMIUM, APENASCOLABORADORES, APENASCOLABORADORESDEEMPRESASCOMPREMIUM }

enum TipoDeExebicao { DIARIO, SEMANAL, MENSAL, PORCLICK }

@JsonSerializable(explicitToJson: true, anyMap: true)
class SelecaoTipoDeExebicao {
  factory SelecaoTipoDeExebicao.fromJson(Map<String, dynamic> json) => _$SelecaoTipoDeExebicaoFromJson(json);
  Map<String, dynamic> toJson() => _$SelecaoTipoDeExebicaoToJson(this);
  SelecaoTipoDeExebicao clone() => _$SelecaoTipoDeExebicaoFromJson(this.toJson());
  String? nome;
  @JsonKey(fromJson: parseEnumPublico)
  TipoDeExebicao? tipo;
  SelecaoTipoDeExebicao({this.nome, this.tipo});
  static List<SelecaoTipoDeExebicao> get obter {
    return TipoDeExebicao.values.map((e) => SelecaoTipoDeExebicao(nome: e.amigavel, tipo: e)).toList();
  }

  static parseEnumPublico(any) {
    if (TipoDeExebicao.values.any((element) => element.toString().replaceAll('TipoDeExebicao.', '') == any.toString())) {
      return TipoDeExebicao.values.firstWhere((element) => element.toString().replaceAll('TipoDeExebicao.', '') == any.toString());
    }
    return null;
  }
}

extension TipoDeExebicaoExtension on TipoDeExebicao? {
  String get amigavel {
    switch (this) {
      case TipoDeExebicao.DIARIO:
        return 'Diário';
      case TipoDeExebicao.SEMANAL:
        return 'Semanal';
      case TipoDeExebicao.MENSAL:
        return 'Mensal';
      case TipoDeExebicao.PORCLICK:
        return 'Por Click';
      default:
        return '';
    }
  }

  num get dias {
    switch (this) {
      case TipoDeExebicao.DIARIO:
        return 1;
      case TipoDeExebicao.SEMANAL:
        return 7;
      case TipoDeExebicao.MENSAL:
        return 30;
      case TipoDeExebicao.PORCLICK:
        return 0;
      default:
        return 0;
    }
  }
}

extension PublicoExtension on Publico {
  String get amigavel {
    switch (this) {
      case Publico.TODOS:
        return 'Todos alunos e colaboradores';
      case Publico.PREMIUM:
        return 'Apenas usuários com premium';
      case Publico.NAOPREMIUM:
        return 'Apenas usuários sem premium';
      case Publico.APENASCOLABORADORES:
        return 'Apenas usuários do tipo colaborador';
      case Publico.APENASCOLABORADORESDEEMPRESASCOMPREMIUM:
        return 'Apenas usuários do tipo colaborador de empresas que vendem premium';
      case Publico.APENASALUNOS:
        return 'Apenas Alunos';
      }
  }
}
