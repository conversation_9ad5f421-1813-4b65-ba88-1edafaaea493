import 'dart:convert';
import 'dart:typed_data';

import 'package:app_treino/ServiceProvider/authServices/ClienteAppService.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/componets/CardPadrao.dart';
import 'package:app_treino/appWidgets/componentWidgets/image_widget.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/config/personal_icon_icons.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/inapp/jpinapp.dart';
import 'package:app_treino/controlladores/ControladorDBExtend.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:sembast/sembast.dart';
import 'package:url_launcher/url_launcher_string.dart';
part 'controladorJPinApp.g.dart';

class ControladorJPinApp = _ControladorJPinAppBase with _$ControladorJPinApp;

abstract class _ControladorJPinAppBase extends UtilDataBase with Store {
  List<JPInApp> campanhas = [];
  var mControllApp = GetIt.I.get<ControladorApp>();
  var mCliente = GetIt.I.get<ControladorCliente>();
  var mService = GetIt.I.get<ClienteAppService>();
  String? campanha = '', campanhaInApp = '';
  bool viewMode = false;
  @observable
  ServiceStatus statusConsultaCampanhas = ServiceStatus.Done;
  bool isShowBanner = false;
  Future<void> verificarExibicao(JPInApp campanha, Function() continuar) async {
    campanha.tipoDeExebicao = campanha.tipoDeExebicao ?? TipoDeExebicao.SEMANAL;
    campanha.vezerPorDia = campanha.vezerPorDia;
    if (campanha.tipoDeExebicao == TipoDeExebicao.PORCLICK && campanha.vezerPorDia == 0) {
      continuar.call();
    } else {
      StoreRef<int, Map<String, dynamic>> storeView = intMapStoreFactory.store('inappView');
      storeView.find(getDb, finder: Finder(filter: Filter.equals('key', campanha.key))).then((value) {
        List<String> diasValidar = [];
        for (var i = campanha.tipoDeExebicao.dias - 1; i >= 0; i--) {
          diasValidar.add(UtilDataHora.getDiaMes(dateTime: DateTime.now().subtract(Duration(days: i as int))));
        }
        if (campanha.tipoDeExebicao != TipoDeExebicao.PORCLICK &&
            value.where((sv) => diasValidar.any((dia) => dia.contains(RegistroExibicaoInApp.fromJson(sv.value).dia!) == true) == true).length < campanha.vezerPorDia) {
          if (!viewMode) storeView.add(getDb, RegistroExibicaoInApp(key: campanha.key, dia: UtilDataHora.getDiaMes(dateTime: DateTime.now())).toJson());
          continuar.call();
        } else {
          if (((value.where((json) => RegistroExibicaoInApp.fromJson(json.value).dia!.contains(UtilDataHora.getDiaMes(dateTime: DateTime.now()))).length) < campanha.vezerPorDia)) {
            if (!viewMode) storeView.add(getDb, RegistroExibicaoInApp(key: campanha.key, dia: UtilDataHora.getDiaMes(dateTime: DateTime.now())).toJson());
            continuar.call();
          }
        }
      });
    }
  }

  void tratarInApp(EventosKey? evento, {Function(String falha)? naoPodeExibir, JPInApp? inAppExibir}) {
    bool empresaVendePremium = mControllApp.getConfiguracaoApp(ModuloApp.MODULO_REFEICOES).ePremium! && mControllApp.getConfiguracaoApp(ModuloApp.MODULO_REFEICOES).habilitado!;
    (inAppExibir != null ? [inAppExibir] : campanhas).where((c) => c.mostrarNosEventos!.any((e) => e == evento)).forEach((campanha) {
      verificarExibicao(campanha, () {
        switch (campanha.publico) {
          case Publico.APENASALUNOS:
            if (mCliente.isUsuarioColaborador) {
              return;
            }
            break;
          case Publico.PREMIUM:
            if (!empresaVendePremium) {
              naoPodeExibir?.call('Empresa atual não vende premium');
              return;
            }
            if (!UtilitarioApp.usuarioTemPremiumLiberado) {
              naoPodeExibir?.call('Usuário logado já possui o premium');
              return;
            }
            break;
          case Publico.NAOPREMIUM:
            if (!empresaVendePremium) {
              naoPodeExibir?.call('Empresa atual não vende premium');
              return;
            }
            if (mCliente.isUsuarioColaborador && !viewMode) {
              return;
            }
            if (UtilitarioApp.usuarioTemPremiumLiberado) {
              naoPodeExibir?.call('Usuário logado já possui o premium');
              return;
            }
            break;
          case Publico.APENASCOLABORADORES:
            if (!mCliente.isUsuarioColaborador) {
              naoPodeExibir?.call('Usuário logado não é colaborador');
              return;
            }
            break;
          case Publico.APENASCOLABORADORESDEEMPRESASCOMPREMIUM:
            if (!mCliente.isUsuarioColaborador) {
              naoPodeExibir?.call('Usuário logado não é colaborador');
              return;
            }
            if (!empresaVendePremium) {
              naoPodeExibir?.call('Empresa atual não vende premium');
              return;
            }
            break;
          default:
            break;
        }
        if (campanha.empresasMostrar?.isNotEmpty ?? false) {
          if (!campanha.empresasMostrar!.any((x) => x == '${mControllApp.chave}_${mCliente.mUsuarioLogado?.codEmpresa ?? 0}')) {
            naoPodeExibir?.call('Empresa logada não está listada nas empresas liberadas para a campanha');
            return;
          }
        }
        if (campanha.campanhaAtiva! && campanha.inicioFimCampanha!.estaNoRange() && campanha.mostrarNosEventos!.contains(evento)) {
          var context = GetIt.I.get<NavigationService>().context;
          var nav = GetIt.I.get<NavigationService>();

          if ((campanha.mostrarEntreHorarios?.isEmpty ?? true) || campanha.mostrarEntreHorarios!.any((x) => x.estouNoRange == true)) {
            precacheImage(ImageWidget.imageProvider(campanha.urlMedia!), context).then((value) {
              if (isShowBanner) {
                return;
              }
              isShowBanner = true;
              showDialog(
                  
                  builder: (context) => Center(
                        child: Stack(
                          alignment: Alignment.topRight,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: CardPadrao(
                                child: GestureDetector(
                                  onTap: () {
                                    // Purchases.setCampaign(campanha.nomeCampanha!);
                                    isShowBanner = false;
                                    campanhaInApp = campanha.nomeCampanha;
                                    analytic(EventosKey.veio_de_banner_inapp, opicionais: {'id': campanha.key, 'nome': campanha.nomeCampanha, 'publico': campanha.publico?.amigavel});
                                    nav.goBack();
                                    if (campanha.linkAbrir?.contains('http') ?? false) {
                                      DSalerta().exibirAlertaSimplificado(
                                        context: context,
                                        titulo: localizedString('push.link.titulo'),
                                        subtitulo: localizedString('push.link.disclaimer'),
                                        // tituloBotaoSecundario: localizedString('push.link.voltar'),
                                        tituloBotao: localizedString('push.link.acessar'),
                                        onTap: () {
                                          nav.goBack();
                                          launchUrlString(campanha.linkAbrir!);
                                        },
                                      );
                                    } else if (campanha.linkAbrir?.isNotEmpty ?? false) {
                                      nav.navigateTo(campanha.linkAbrir!);
                                      campanhaInApp = campanha.nomeCampanha;
                                    
                                    }
                                  },
                                  child: ImageWidget(imageUrl: campanha.urlMedia!, height: 280, width: 280, fit: BoxFit.cover),
                                ),
                              ),
                            ),
                            CardPadrao(
                              radius: 50,
                              child: GestureDetector(
                                child: const Padding(
                                  padding: EdgeInsets.all(5.0),
                                  child: Icon(
                                    PersonalIcon.x,
                                    size: 15,
                                  ),
                                ),
                                onTap: () {
                                  isShowBanner = false;
                                  analytic(EventosKey.fechou_banner_inapp, opicionais: {'id': campanha.key, 'nome': campanha.nomeCampanha, 'publico': campanha.publico?.amigavel});
                                  nav.goBack();
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                  context: context,
                  barrierDismissible: false);
            });
          } else {
            naoPodeExibir?.call('fora do horário permitido para exebição');
          }
        } else {
          naoPodeExibir?.call('Campanha está inativa ou fora do horário permitido para exebição');
        }
      });
    });
  }

  void consultarCampanhas({Function()? carregando, Function()? sucesso, Function(String)? falha, bool force = true}) {
    carregando?.call();
    statusConsultaCampanhas = ServiceStatus.Waiting;
    mService.consultarCampanhasInApp().then((value) {
      campanhas = value;
      sucesso?.call();
      statusConsultaCampanhas = ServiceStatus.Done;
    }).catchError((onError) {
      statusConsultaCampanhas = ServiceStatus.Done;
      falha?.call(onError.message ?? '');
    });
  }

  void validarCampanha(JPInApp inApp, Function(String x)? falha, Function() ok) {
    if (inApp.linkAbrir?.isEmpty ?? true) {
      falha?.call('Verifique a link que o inapp deve abrir');
    } else if (inApp.urlMedia?.isEmpty ?? true) {
      falha?.call('Verifique a imagem que o inapp deve exibir');
    } else if (!(inApp.inicioFimCampanha?.valido() ?? false)) {
      falha?.call('Verifique o inicio e fim da campanha');
    } else if (inApp.nomeCampanha?.isEmpty ?? true) {
      falha?.call('Verifique o nome da campanha');
    } else if (inApp.mostrarNosEventos?.isEmpty ?? true) {
      falha?.call('Verifique os eventos que disparam a campanha');
    } else {
      ok.call();
    }
  }

  void salvarCampanha(JPInApp inApp, {Function()? carregando, Function()? sucesso, Function(String x)? falha, bool force = true}) {
    carregando?.call();
    if (inApp.linkAbrir?.isEmpty ?? true) {
      falha!.call('Verifique a link que o inapp deve abrir');
    } else if (inApp.urlMedia?.isEmpty ?? true) {
      falha!.call('Verifique a imagem que o inapp deve exibir');
    } else if (!(inApp.inicioFimCampanha?.valido() ?? false)) {
      falha!.call('Verifique o inicio e fim da campanha');
    } else if (inApp.nomeCampanha?.isEmpty ?? true) {
      falha!.call('Verifique o nome da campanha');
    } else if (inApp.mostrarNosEventos?.isEmpty ?? true) {
      falha!.call('Verifique os eventos que disparam a campanha');
    } else {
      statusConsultaCampanhas = ServiceStatus.Waiting;
      mService.salvarCampanhaInApp(inApp).then((value) {
        if (campanhas.any((element) => element.key == value.key)) {
          var index = campanhas.indexWhere((element) => element.key == value.key);
          campanhas.removeAt(index);
          campanhas.insert(index, value);
        } else {
          campanhas.add(value);
        }
        sucesso?.call();
        statusConsultaCampanhas = ServiceStatus.Done;
      }).catchError((onError) {
        falha?.call(onError.message ?? '');
        statusConsultaCampanhas = ServiceStatus.Done;
      });
    }
  }

  void uploadImagem(Uint8List fotoBase64, {Function()? carregando, Function(String url)? sucesso, Function(String)? falha}) {
    carregando?.call();
    mService.uploadImagem(base64Encode(fotoBase64)).then((value) {
      sucesso?.call(value);
    }).catchError((onError) {
      falha?.call('Não foi possível salvar a imagem');
    });
  }
}
