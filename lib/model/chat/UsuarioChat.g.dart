// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'UsuarioChat.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UsuarioChat _$UsuarioChatFromJson(Map json) => UsuarioChat()
  ..refUsuario = json['refUsuario'] as String?
  ..nome = json['nome'] as String?
  ..urlFoto = json['urlFoto'] as String?;

Map<String, dynamic> _$UsuarioChatToJson(UsuarioChat instance) =>
    <String, dynamic>{
      'refUsuario': instance.refUsuario,
      'nome': instance.nome,
      'urlFoto': instance.urlFoto,
    };

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$UsuarioChat on _UsuarioChatBase, Store {
  @override
  String toString() {
    return '''

    ''';
  }
}
