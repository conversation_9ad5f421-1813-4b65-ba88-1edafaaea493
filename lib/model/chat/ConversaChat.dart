import 'package:app_treino/model/chat/MensagemChat.dart';
import 'package:app_treino/model/chat/UsuarioChat.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:mobx/mobx.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ConversaChat.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class ConversaChat {
  List<String?>? usuarios = [];
  UsuarioChat? usuarioMostrar;
  @observable
  List<MensagemChat>? mensagens = [];
  String? conversaID;
  bool? eConversaComNutri;
  @observable
  MensagemChat? ultimaMensagem;
  @JsonKey(fromJson: UtilDataHora.parseNunToDate)
  DateTime? ultimaInteracao;
  @observable
  bool? ultimaMensagemLida;
  ConversaChat({this.conversaID, this.eConversaComNutri, this.mensagens, this.ultimaMensagem, this.ultimaMensagemLida, this.usuarios, this.usuarioMostrar, this.ultimaInteracao});

  factory ConversaChat.fromJson(Map<String, dynamic> json) => _$ConversaChatFromJson(json);
  Map<String, dynamic> toJson() => _$ConversaChatToJson(this);

//   @override
//   bool operator ==(Object other) => identical(this, other) || other is ConversaChat && runtimeType == other.runtimeType && usuarios.toString() == other.usuarios.toString();

//   @override
//   int get hashCode => property.hashCode;
}
