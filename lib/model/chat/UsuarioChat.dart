// ignore_for_file: unused_element

import 'package:mobx/mobx.dart';
import 'package:json_annotation/json_annotation.dart';

part 'UsuarioChat.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class UsuarioChat extends _UsuarioChatBase with _$UsuarioChat {
  UsuarioChat();

  factory UsuarioChat.fromJson(Map<String, dynamic> json) => _$UsuarioChatFromJson(json);
  Map<String, dynamic> toJson() => _$UsuarioChatToJson(this);
}

abstract class _UsuarioChatBase with Store {
  String? refUsuario;
  String? nome;
  String? urlFoto;
  _UsuarioChatBase();
}
