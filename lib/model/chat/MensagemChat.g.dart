// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'MensagemChat.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MensagemChat _$MensagemChatFromJson(Map json) => MensagemChat(
      conteudo: json['conteudo'] as String?,
      dataEnviada: UtilDataHora.parseNunToDate(json['dataEnviada'] as num?),
      dataLida: UtilDataHora.parseNunToDate(json['dataLida'] as num?),
      dataRecebida: UtilDataHora.parseNunToDate(json['dataRecebida'] as num?),
      eInterno: json['eInterno'] as bool?,
      eParaTodosColaboradores: json['eParaTodosColaboradores'] as bool?,
      refMensagem: json['refMensagem'] as String?,
      usuario: json['usuario'] as String?,
    );

Map<String, dynamic> _$MensagemChatToJson(MensagemChat instance) =>
    <String, dynamic>{
      'conteudo': instance.conteudo,
      'dataEnviada': UtilDataHora.parseDateToNum(instance.dataEnviada),
      'dataLida': UtilDataHora.parseDateToNum(instance.dataLida),
      'dataRecebida': UtilDataHora.parseDateToNum(instance.dataRecebida),
      'eInterno': instance.eInterno,
      'eParaTodosColaboradores': instance.eParaTodosColaboradores,
      'refMensagem': instance.refMensagem,
      'usuario': instance.usuario,
    };
