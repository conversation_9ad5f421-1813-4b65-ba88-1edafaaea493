// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ConversaChat.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConversaChat _$ConversaChatFromJson(Map json) => ConversaChat(
      conversaID: json['conversaID'] as String?,
      eConversaComNutri: json['eConversaComNutri'] as bool?,
      mensagens: (json['mensagens'] as List<dynamic>?)
          ?.map(
              (e) => MensagemChat.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      ultimaMensagem: json['ultimaMensagem'] == null
          ? null
          : MensagemChat.fromJson(
              Map<String, dynamic>.from(json['ultimaMensagem'] as Map)),
      ultimaMensagemLida: json['ultimaMensagemLida'] as bool?,
      usuarios: (json['usuarios'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .toList(),
      usuarioMostrar: json['usuarioMostrar'] == null
          ? null
          : UsuarioChat.fromJson(
              Map<String, dynamic>.from(json['usuarioMostrar'] as Map)),
      ultimaInteracao:
          UtilDataHora.parseNunToDate(json['ultimaInteracao'] as num?),
    );

Map<String, dynamic> _$ConversaChatToJson(ConversaChat instance) =>
    <String, dynamic>{
      'usuarios': instance.usuarios,
      'usuarioMostrar': instance.usuarioMostrar?.toJson(),
      'mensagens': instance.mensagens?.map((e) => e.toJson()).toList(),
      'conversaID': instance.conversaID,
      'eConversaComNutri': instance.eConversaComNutri,
      'ultimaMensagem': instance.ultimaMensagem?.toJson(),
      'ultimaInteracao': instance.ultimaInteracao?.toIso8601String(),
      'ultimaMensagemLida': instance.ultimaMensagemLida,
    };
