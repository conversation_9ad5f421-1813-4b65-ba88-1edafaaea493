import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:get_it/get_it.dart';
import 'package:json_annotation/json_annotation.dart';

part 'MensagemChat.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class MensagemChat {
  factory MensagemChat.fromJson(Map<String, dynamic> json) => _$MensagemChatFromJson(json);
  Map<String, dynamic> toJson() => _$MensagemChatToJson(this);
  String? conteudo;
  @Json<PERSON><PERSON>(fromJson: UtilDataHora.parseNunToDate, toJson: UtilDataHora.parseDateToNum)
  DateTime? dataEnviada;
  @JsonKey(fromJson: UtilDataHora.parseNunToDate, toJson: UtilDataHora.parseDateToNum)
  DateTime? dataLida;
  @Json<PERSON>ey(fromJson: UtilDataHora.parseNunToDate, toJson: UtilDataHora.parseDateToNum)
  DateTime? dataRecebida;
  bool? eInterno;
  bool? eParaTodosColaboradores;
  String? refMensagem;
  bool get emissor {
    return GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid == usuario;
  }

  String? usuario;
  MensagemChat({this.conteudo, this.dataEnviada, this.dataLida, this.dataRecebida, this.eInterno, this.eParaTodosColaboradores, this.refMensagem, this.usuario});
}
