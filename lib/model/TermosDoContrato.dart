import 'package:app_treino/model/contrato/ContratoUsuario.dart';
import 'package:json_annotation/json_annotation.dart';

part 'TermosDoContrato.g.dart';


@JsonSerializable(explicitToJson: true,anyMap: true)
class TermosContratoAssinadoFirebase{
  ContratoAssinatura contratoAssinado;

 factory TermosContratoAssinadoFirebase.fromJson(Map<String, dynamic> json) => _$TermosContratoAssinadoFirebaseFromJson(json);
  Map<String, dynamic> toJson() => _$TermosContratoAssinadoFirebaseToJson(this);
  TermosContratoAssinadoFirebase clone() => _$TermosContratoAssinadoFirebaseFromJson(this.toJson());
  num codigoContrato;
   TermosContratoAssinadoFirebase({
    required this.contratoAssinado,
    required this.codigoContrato,
  });
  
}
