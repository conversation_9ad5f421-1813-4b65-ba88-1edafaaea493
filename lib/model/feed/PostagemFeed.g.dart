// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'PostagemFeed.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PostagemFeed _$PostagemFeedFromJson(Map json) => PostagemFeed(
      comentarios: (json['comentarios'] as List<dynamic>?)
          ?.map((e) =>
              ComentarioPostagem.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      comentariosApresentar: (json['comentariosApresentar'] as List<dynamic>?)
          ?.map((e) =>
              ComentarioPostagem.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      dataEdicao: json['dataEdicao'] as String?,
      linkDeAcessoExterno: json['linkDeAcessoExterno'],
      descricao: (json['descricao'] as List<dynamic>?)
          ?.map((e) =>
              DescricaoPostagem.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      dataMilis: (json['dataMilis'] as num?)?.toInt(),
      reacoes: (json['reacoes'] as List<dynamic>?)
          ?.map((e) => PostReacao.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      refUsuario: json['refUsuario'] as String?,
      refClienteApp: json['refClienteApp'] as String?,
      dataPostagem: json['dataPostagem'] == null
          ? null
          : DateTime.parse(json['dataPostagem'] as String),
      media: (json['media'] as List<dynamic>?)
          ?.map((e) => Media.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      postExcluido: json['postExcluido'] as bool?,
      codigoUnidade: (json['codigoUnidade'] as num?)?.toInt(),
      quantidadeComentarios: (json['quantidadeComentarios'] as num?)?.toInt(),
      contemConteudoPago: json['contemConteudoPago'] as bool?,
      docKey: json['docKey'] as String?,
      nomeUsuario: json['nomeUsuario'] as String?,
      urlFotoUser: json['urlFotoUser'] as String?,
      minhaReacao: json['minhaReacao'] == null
          ? null
          : PostReacao.fromJson(
              Map<String, dynamic>.from(json['minhaReacao'] as Map)),
    );

Map<String, dynamic> _$PostagemFeedToJson(PostagemFeed instance) =>
    <String, dynamic>{
      'comentarios': instance.comentarios?.map((e) => e.toJson()).toList(),
      'comentariosApresentar':
          instance.comentariosApresentar?.map((e) => e.toJson()).toList(),
      'dataEdicao': instance.dataEdicao,
      'linkDeAcessoExterno': instance.linkDeAcessoExterno,
      'descricao': instance.descricao?.map((e) => e.toJson()).toList(),
      'dataMilis': instance.dataMilis,
      'reacoes': instance.reacoes?.map((e) => e.toJson()).toList(),
      'refUsuario': instance.refUsuario,
      'refClienteApp': instance.refClienteApp,
      'dataPostagem': instance.dataPostagem?.toIso8601String(),
      'media': instance.media?.map((e) => e.toJson()).toList(),
      'postExcluido': instance.postExcluido,
      'codigoUnidade': instance.codigoUnidade,
      'quantidadeComentarios': instance.quantidadeComentarios,
      'contemConteudoPago': instance.contemConteudoPago,
      'docKey': instance.docKey,
      'nomeUsuario': instance.nomeUsuario,
      'urlFotoUser': instance.urlFotoUser,
      'minhaReacao': instance.minhaReacao?.toJson(),
    };
