import 'package:json_annotation/json_annotation.dart';
part 'DescricaoPostagem.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class DescricaoPostagem {
  factory DescricaoPostagem.fromJson(Map<String, dynamic> json) => _$DescricaoPostagemFromJson(json);
  Map<String, dynamic> toJson() => _$DescricaoPostagemToJson(this);
  String? descricao;
  DateTime? dataDescricao;
  int? dataMilis;

  DescricaoPostagem({
    this.descricao,
    this.dataDescricao,
    this.dataMilis,
  });
}
