import 'package:json_annotation/json_annotation.dart';
part 'PostReacao.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class PostReacao {
  factory PostReacao.fromJson(Map<String, dynamic> json) => _$PostReacaoFromJson(json);
  Map<String, dynamic> toJson() => _$PostReacaoToJson(this);
  DateTime? dataRegistro;
  String? tipoReacao;
  String? refUsuario;
  int? dataMilis;
  String? nomeUsuario;
  String? urlFotoUser;

  PostReacao({
    this.dataRegistro,
    this.tipoReacao,
    this.refUsuario,
    this.dataMilis,
    this.nomeUsuario,
    this.urlFotoUser,
  });
}
