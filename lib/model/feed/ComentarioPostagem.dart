import 'package:json_annotation/json_annotation.dart';
part 'ComentarioPostagem.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class ComentarioPostagem {
  factory ComentarioPostagem.fromJson(Map<String, dynamic> json) => _$ComentarioPostagemFromJson(json);
  Map<String, dynamic> toJson() => _$ComentarioPostagemToJson(this);
  DateTime? dataComentario;
  dynamic refComentarioPai;
  String? comentario;
  String? refKey;
  String? refUsuario;
  String? nomeUsuario;
  String? urlFotoUser;
  int? dataMilis;
  bool? emModeracao;

  ComentarioPostagem({
    this.dataComentario,
    this.refComentarioPai,
    this.comentario,
    this.refKey,
    this.refUsuario,
    this.nomeUsuario,
    this.urlFotoUser,
    this.dataMilis,
    this.emModeracao,
  });
}
