import 'package:app_treino/model/feed/ComentarioPostagem.dart';
import 'package:app_treino/model/feed/DescricaoPostagem.dart';
import 'package:app_treino/model/feed/PostReacao.dart';
import 'package:app_treino/model/geral/Media.dart';
import 'package:json_annotation/json_annotation.dart';
part 'PostagemFeed.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class PostagemFeed {
  factory PostagemFeed.fromJson(Map<String, dynamic> json) => _$PostagemFeedFromJson(json);
  Map<String, dynamic> toJson() => _$PostagemFeedToJson(this);

  PostagemFeed clone() => _$PostagemFeedFromJson(this.toJson());
  List<ComentarioPostagem>? comentarios;
  List<ComentarioPostagem>? comentariosApresentar;
  String? dataEdicao;
  dynamic linkDeAcessoExterno;
  List<DescricaoPostagem>? descricao;
  int? dataMilis;
  List<PostReacao>? reacoes;
  String? refUsuario;
  String? refClienteApp;
  DateTime? dataPostagem;
  List<Media>? media;
  bool? postExcluido;
  int? codigoUnidade;
  int? quantidadeComentarios;
  bool? contemConteudoPago;
  String? docKey;
  String? nomeUsuario;
  String? urlFotoUser;
  PostReacao? minhaReacao;

  PostagemFeed({
    this.comentarios,
    this.comentariosApresentar,
    this.dataEdicao,
    this.linkDeAcessoExterno,
    this.descricao,
    this.dataMilis,
    this.reacoes,
    this.refUsuario,
    this.refClienteApp,
    this.dataPostagem,
    this.media,
    this.postExcluido,
    this.codigoUnidade,
    this.quantidadeComentarios,
    this.contemConteudoPago,
    this.docKey,
    this.nomeUsuario,
    this.urlFotoUser,
    this.minhaReacao,
  });
}
