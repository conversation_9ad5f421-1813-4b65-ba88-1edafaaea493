import 'package:json_annotation/json_annotation.dart';
part 'termo_de_aceite.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class TermoDeAceite {
  factory TermoDeAceite.fromJson(Map<String, dynamic> json) => _$TermoDeAceiteFromJson(json);
  Map<String, dynamic> toJson() => _$TermoDeAceiteToJson(this);
  int? codigo;
  String? descricao;

  TermoDeAceite({
    this.codigo,
    this.descricao,
  });
}
