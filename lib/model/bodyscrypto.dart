import 'package:json_annotation/json_annotation.dart';
part 'bodyscrypto.g.dart';

@JsonSerializable()
class BodyLoginPasswordCrypto {
  String? userName;
  String? pwd;
  BodyLoginPasswordCrypto({
    this.userName,
    this.pwd,
  });
  factory BodyLoginPasswordCrypto.fromJson(Map<String, dynamic> json) => _$BodyLoginPasswordCryptoFromJson(json);
  Map<String, dynamic> toJson() => _$BodyLoginPasswordCryptoToJson(this);
  BodyLoginPasswordCrypto clone() => _$BodyLoginPasswordCryptoFromJson(this.toJson());
}

@JsonSerializable()
class BodyLoginCrypto {
  String? email;
  String? password;
  bool? aluno;
  BodyLoginCrypto({
    this.email,
    this.password,
    this.aluno,
  });
  factory BodyLoginCrypto.fromJson(Map<String, dynamic> json) => _$BodyLoginCryptoFromJson(json);
  Map<String, dynamic> toJson() => _$BodyLoginCryptoToJson(this);
  BodyLoginCrypto clone() => _$BodyLoginCryptoFromJson(this.toJson());
}

@JsonSerializable()
class BodyDescrobrirUsuariosCrypto {
  String? celular;
  String? ddi;
  String? ddd;
  BodyDescrobrirUsuariosCrypto({
    this.celular,
    this.ddi,
    this.ddd,
  });
  factory BodyDescrobrirUsuariosCrypto.fromJson(Map<String, dynamic> json) => _$BodyDescrobrirUsuariosCryptoFromJson(json);
  Map<String, dynamic> toJson() => _$BodyDescrobrirUsuariosCryptoToJson(this);
  BodyDescrobrirUsuariosCrypto clone() => _$BodyDescrobrirUsuariosCryptoFromJson(this.toJson());
}

@JsonSerializable()
class BodyRecuperarSenhaCrypto {
  // {"cliente":true,"email":"<EMAIL>"}
  bool? cliente;
  String? email;
  BodyRecuperarSenhaCrypto({
    this.cliente,
    this.email,
  });
  factory BodyRecuperarSenhaCrypto.fromJson(Map<String, dynamic> json) => _$BodyRecuperarSenhaCryptoFromJson(json);
  Map<String, dynamic> toJson() => _$BodyRecuperarSenhaCryptoToJson(this);
  BodyRecuperarSenhaCrypto clone() => _$BodyRecuperarSenhaCryptoFromJson(this.toJson());
}

@JsonSerializable()
class BodyAlterarSenhaCrypto {
  String? email;
  String? novaSenha;
  BodyAlterarSenhaCrypto({
    this.email,
    this.novaSenha,
  });
  factory BodyAlterarSenhaCrypto.fromJson(Map<String, dynamic> json) => _$BodyAlterarSenhaCryptoFromJson(json);
  Map<String, dynamic> toJson() => _$BodyAlterarSenhaCryptoToJson(this);
  BodyAlterarSenhaCrypto clone() => _$BodyAlterarSenhaCryptoFromJson(this.toJson());
}

@JsonSerializable()
class BodyContratoAlunoCrypto {
  // "{"validar":false,"matricula":"006942","empresa":"1"}"
  bool? validar;
  String? matricula;
  num? empresa;
  BodyContratoAlunoCrypto({
    this.validar,
    this.matricula,
    this.empresa,
  });
  factory BodyContratoAlunoCrypto.fromJson(Map<String, dynamic> json) => _$BodyContratoAlunoCryptoFromJson(json);
  Map<String, dynamic> toJson() => _$BodyContratoAlunoCryptoToJson(this);
  BodyContratoAlunoCrypto clone() => _$BodyContratoAlunoCryptoFromJson(this.toJson());
}

@JsonSerializable()
class BodyConsultarContratoPorCodigoCrypto {
  num? contrato;
  num? empresa;
  BodyConsultarContratoPorCodigoCrypto({
    this.contrato,
    this.empresa,
  });
  factory BodyConsultarContratoPorCodigoCrypto.fromJson(Map<String, dynamic> json) => _$BodyConsultarContratoPorCodigoCryptoFromJson(json);
  Map<String, dynamic> toJson() => _$BodyConsultarContratoPorCodigoCryptoToJson(this);
  BodyConsultarContratoPorCodigoCrypto clone() => _$BodyConsultarContratoPorCodigoCryptoFromJson(this.toJson());
}
