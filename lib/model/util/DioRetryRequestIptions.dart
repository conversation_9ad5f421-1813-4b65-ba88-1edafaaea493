import 'dart:convert';

import 'package:dio/dio.dart';

class DioRetryRequestOptions {
  final String method;
  final String url;
  final Map<String, dynamic> headers;
  final dynamic data;

  DioRetryRequestOptions({
    required this.method,
    required this.url,
    required this.headers,
    this.data,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'method': method,
      'url': url,
      'headers': headers,
      'data': data,
    };
  }

  factory DioRetryRequestOptions.fromMap(Map<String, dynamic> map) {
    return DioRetryRequestOptions(
      method: map['method'] as String,
      url: map['url'] as String,
      headers: Map<String, dynamic>.from(map['headers'] as Map<String, dynamic>),
      data: map['data'] != null ? map['data'] as dynamic : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory DioRetryRequestOptions.fromJson(String source) => DioRetryRequestOptions.fromMap(json.decode(source));

  static Map<String, dynamic> parse(RequestOptions options) {
    return DioRetryRequestOptions(
      method: options.method,
      url: options.uri.toString(),
      headers: options.headers,
      data: options.data,
    ).toMap();
  }
}
