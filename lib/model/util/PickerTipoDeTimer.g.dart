// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'PickerTipoDeTimer.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PickerTipoDeTimer _$PickerTipoDeTimerFromJson(Map json) => PickerTipoDeTimer(
      tipo: json['tipo'] as String?,
      orientacao: PickerTipoDeTimer._parse(json['orientacao']),
    );

Map<String, dynamic> _$PickerTipoDeTimerToJson(PickerTipoDeTimer instance) =>
    <String, dynamic>{
      'tipo': instance.tipo,
      'orientacao': _$TipoTimerEnumMap[instance.orientacao],
    };

const _$TipoTimerEnumMap = {
  TipoTimer.Execucao: 'Execucao',
  TipoTimer.Descanso: 'Descanso',
  TipoTimer.Progressivo: 'Progressivo',
  TipoTimer.Regressivo: 'Regressivo',
};

ClassTimeLine _$ClassTimeLineFromJson(Map json) => ClassTimeLine(
      duracao: json['duracao'] == null
          ? null
          : Duration(microseconds: (json['duracao'] as num).toInt()),
      roundAtual: (json['roundAtual'] as num?)?.toInt(),
      tipoTimer: $enumDecodeNullable(_$TipoTimerEnumMap, json['tipoTimer']),
    );

Map<String, dynamic> _$ClassTimeLineToJson(ClassTimeLine instance) =>
    <String, dynamic>{
      'duracao': instance.duracao?.inMicroseconds,
      'tipoTimer': _$TipoTimerEnumMap[instance.tipoTimer],
      'roundAtual': instance.roundAtual,
    };
