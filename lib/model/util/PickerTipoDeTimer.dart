import 'package:json_annotation/json_annotation.dart';
part 'PickerTipoDeTimer.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class PickerTipoDeTimer {
  factory PickerTipoDeTimer.fromJson(Map<String, dynamic> json) => _$PickerTipoDeTimerFromJson(json);
  Map<String, dynamic> toJson() => _$PickerTipoDeTimerToJson(this);
  PickerTipoDeTimer clone() => _$PickerTipoDeTimerFromJson(this.toJson());
  String? tipo;
  @JsonKey(fromJson: _parse)
  TipoTimer? orientacao;
  PickerTipoDeTimer({
    this.tipo,
    this.orientacao,
  });

  static _parse(any) {
    if (TipoTimer.values.any((element) => element.toString().replaceAll('TipoTimer.', '') == any.toString())) {
      return TipoTimer.values.firstWhere((element) => element.toString().replaceAll('TipoTimer.', '') == any.toString());
    }
    return null;
  }
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class ClassTimeLine {
  factory ClassTimeLine.fromJson(Map<String, dynamic> json) => _$ClassTimeLineFromJson(json);
  Map<String, dynamic> toJson() => _$ClassTimeLineToJson(this);
  ClassTimeLine clone() => _$ClassTimeLineFromJson(this.toJson());

  Duration? duracao;
  TipoTimer? tipoTimer;
  int? roundAtual;

  ClassTimeLine({this.duracao, this.roundAtual, this.tipoTimer});
}

enum TipoTimer { Execucao, Descanso, Progressivo, Regressivo }
