import 'dart:io';

import 'package:app_treino/model/util/string_extension.dart';
import 'package:intl/intl.dart';

class UtilDataHora {


  /// Verifica se o aluno é maior de idade com base na data de nascimento fornecida.
  ///
  /// Retorna `true` se o aluno tiver 18 anos ou mais na data atual, caso contrário, retorna `false`.
  ///
  /// Parâmetros:
  /// - [dataNascimento]: A data de nascimento do aluno. Pode ser nula.
  ///
  /// Retorna:
  /// - `true` se o aluno for maior de idade, `false` caso contrário.
  static bool alunoMaiorDeIdade({DateTime? dataNascimento}) {
    if (dataNascimento == null) return false;
    final hoje = DateTime.now();
    final idade = hoje.year - dataNascimento.year;
    if (hoje.month < dataNascimento.month || (hoje.month == dataNascimento.month && hoje.day < dataNascimento.day)) {
      return idade > 18;
    }
    return idade >= 18;
  }

  static num daysBetween(
      {DateTime? inicio, num? inicioMilis, DateTime? fim, num? fimMilis}) {
    try {
      var dateA = inicio ?? DateTime.fromMillisecondsSinceEpoch(inicioMilis!.toInt());
      var dateB = fim ?? DateTime.fromMillisecondsSinceEpoch(fimMilis!.toInt());
      return dateA.difference(dateB).inDays + 1;
    } catch (e) {
      return 0;
    }
  }

    static String getDaTaAnoMesDia({int? dataMilis, DateTime? dateTime}) {
    try {
      var formatter = DateFormat('yyyy-MM-dd');
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '--/--/----';
    }
  }

  /// Retorna o último dia do mês da data fornecida.
  ///
  /// O parâmetro [date] é a data para a qual se deseja obter o último dia do mês.
  /// Retorna um objeto [DateTime] representando o último dia do mês da data fornecida.
  static DateTime getUltimoDiaDoMes(DateTime date) {
    DateTime nextMonth = DateTime(date.year, date.month + 1, 1);
    DateTime lastDayOfMonth = nextMonth.subtract(const Duration(days: 1));
    return lastDayOfMonth;
  }

  /// Retorna o primeiro dia do mês da data fornecida.
  ///
  /// O método recebe um objeto [DateTime] representando uma data e retorna um novo objeto [DateTime]
  /// com o primeiro dia do mês correspondente à data fornecida.
  ///
  /// Exemplo de uso:
  /// ```dart
  /// DateTime data = DateTime.now();
  /// DateTime primeiroDiaDoMes = UtilDataHora.getPrimeiroDiaDoMes(data);
  /// print(primeiroDiaDoMes); // Saída: 2022-01-01 00:00:00.000
  /// ```
  static DateTime getPrimeiroDiaDoMes(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  static bool dataPassouUmDia({int? dataMilis, DateTime? dateTime}) {
    try {
      var dateA = dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!);
      if (DateTime.now().day != dateA.day) {
        return true;
      } else {
        return false;
      }
      //return DateTime.now().isAfter(dateA);
    } catch (e) {
      return false;
    }
  }

  static bool dataMenorSomenteUmDia({int? dataMilis, DateTime? dateTime}) {
    try {
      var dateA = dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!);
      if (DateTime.now().day == dateA.add(const Duration(days: 1)).day) {
        return true;
      } else {
        return false;
      }
      //return DateTime.now().isAfter(dateA);
    } catch (e) {
      return false;
    }
  }

  static DateTime? formatHHMMString(String time) {
    try {
      return DateFormat('HH:mm').parse(time);
    } catch (e) {
      return null;
    }
  }

  static String parseToStringMinutosSegundo(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return '$twoDigitMinutes:$twoDigitSeconds';
  }

  static String getDiaMesCap({int? dataMilis, DateTime? dateTime}) {
    try {
      var formatter = new DateFormat.MMMd();
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '--';
    }
  }

  static String getDiaMesCompleto(
      {int? dataMilis, DateTime? dateTime, String? locale}) {
    try {
      var formatter = new DateFormat.MMMMd(locale);
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '--';
    }
  }

  static String getDiamesPorExtenso({DateTime? dateTime}) {
    try {
      var formatter = new DateFormat.MMMMd(localizedString('locale_calendar'));
      return formatter.format(dateTime!);
    } catch (e) {
      return '';
    }
  }

  static String getDiaDaSemanaEmesPorExtenso({DateTime? dateTime}) {
    try {
      var formatter = new DateFormat.MMMMEEEEd(Platform.localeName);
      return formatter.format(dateTime!);
    } catch (e) {
      return '';
    }
  }

  static String getDiaDaSemanaEmesPorExtensoAmericano({DateTime? dateTime}) {
    try {
      var formatter = new DateFormat.MMMMEEEEd('en_US');
      return formatter.format(dateTime!);
    } catch (e) {
      return '';
    }
  }

  static bool dataJaPassouDeHojeHora0({int? dataMilis, DateTime? dateTime}) {
    try {
      var dateA =
          dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!.toInt());
      dateA = DateTime(dateA.year, dateA.month, dateA.day, 0, 0, 0, 0, 0);
      return DateTime.now().isBefore(dateA);
    } catch (e) {
      return false;
    }
  }

  static DateTime? getDataComHorasPersonalizada(
      {int? dataMilis,
      DateTime? dateTime,
      String? dataString,
      int horas = 0,
      int minutos = 0,
      int segundos = 0,
      int milesimos = 0,
      int micro = 0}) {
    try {
      var dateA = dateTime ??
          parseStringToDate(dataString!) ??
          DateTime.fromMillisecondsSinceEpoch(dataMilis!.toInt());
      dateA = DateTime(dateA.year, dateA.month, dateA.day, horas, minutos,
          segundos, milesimos, micro);
      return dateA;
    } catch (e) {
      return null;
    }
  }

  static bool dataMenorQueDeAgora(
      {int? dataMilis, DateTime? dateTime, String? dataString}) {
    try {
      var dateA = dateTime ?? parseStringToDate(dataString!);
      dateA = DateTime(dateA!.year, dateA.month, dateA.day, 0, 0, 0, 0, 0);
      return dateA.isBefore(DateTime.now());
    } catch (e) {
      return false;
    }
  }

  static bool dataEigualDaDeHoje(
      {int? dataMilis, DateTime? dateTime, String? dataString}) {
    try {
      var dateA = dateTime ??
          parseStringToDate(dataString!) ??
          DateTime.fromMillisecondsSinceEpoch(dataMilis!.toInt());
      dateA = DateTime(dateA.year, dateA.month, dateA.day, 0, 0, 0, 0, 0);
      return getDaTaMesDiaAno(dateTime: DateTime.now()) ==
          getDaTaMesDiaAno(dateTime: dateA);
    } catch (e) {
      return false;
    }
  }

  static bool diaEMesSaoIguaisDeHoje(
      {int? dataMilis, DateTime? dateTime, String? dataString}) {
    try {
      DateTime input = dateTime ??
          parseStringToDate(dataString!) ??
          DateTime.fromMillisecondsSinceEpoch(dataMilis!.toInt());
      input = DateTime(0, input.month, input.day, 0, 0, 0, 0, 0);
      return input.day == DateTime.now().day &&
          input.month == DateTime.now().month;
    } catch (e) {
      return false;
    }
  }

  static bool horaDataEstaOcorrendoAgora({int? dataMilis, DateTime? dateTime}) {
    try {
      var dateA = dateTime?.add(const Duration(minutes: 10)) ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!.toInt()).add(const Duration(minutes: 10));
      var dAgora = DateTime.now();
      var horaEstaOcorrendo = (dAgora.hour == dateA.hour && dateA == dAgora);
      // Hora é a mesma?
      if (dAgora.hour == dateA.hour) {
        return horaEstaOcorrendo && dAgora.minute <= dateA.minute;
      } else {
        return horaEstaOcorrendo;
      }
    } catch (e) {
      return false;
    }
  }

  static bool dataPassouDeAgora({int? dataMilis, DateTime? dateTime}) {
    try {
      var dateA =
          dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!.toInt());
      return DateTime.now().isAfter(dateA);
    } catch (e) {
      return false;
    }
  }

  static bool horaDataPassou({int? dataMilis, DateTime? dateTime}) {
    try {
      var dateA =
          dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!.toInt());
      return DateTime.now().hour > dateA.hour;
    } catch (e) {
      return false;
    }
  }

  static bool dataJaPassouDeHoje({int? dataMilis, DateTime? dateTime, bool ateUltimoMinutoDoDia = false}) {
    try {
      DateTime dateA = dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!);
      if(ateUltimoMinutoDoDia){
        dateA =new DateTime(dateA.year, dateA.month, dateA.day, 23, 59, 59, dateA.millisecond, dateA.microsecond);

      }
      return DateTime.now().isAfter(dateA);
    } catch (e) {
      return false;
    }
  }

   static bool fazDoisDiasOuMais({int? dataMilliseconds}) {
    try {
      if (dataMilliseconds == null) {
        return true;
      }
      var date = DateTime.fromMillisecondsSinceEpoch(dataMilliseconds.toInt());
      return DateTime.now().subtract(const Duration(days: 2)).isAfter(date);
    } catch (e) {
      return false;
    }
  }

  static num? getAnosAteHoje({int? dataMilis, DateTime? dateTime}) {
    if (dataMilis == null && dateTime == null) return null;
    return DateTime.now().year - (dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!)).year;
  }

  static DateTime? parseStringToDate(String? date, {bool minutos = false}) {
    try {
      return DateTime.parse(date!);
    } catch (e) {
      if (date!.contains('-')) date = date.replaceAll('-', '');
      try {
        if (minutos) {
          return DateFormat('d/M/yyyy HH:mm').parse(date);
        } else {
          return DateFormat('d/M/yyyy').parse(date);
        }
      } catch (e) {
        return null;
      }
    }
  }
static String getDaTaMesDiaAnoFormatado({int? dataMilis, DateTime? dateTime, String? locale}) {
    try {
      if (dateTime == null && dataMilis == null) {
        return '00-00-0000';
      }
      var formatter = DateFormat('dd-MM-yyyy', locale);
      return formatter.format(
          dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '00-00-0000';
    }
  }
  static String getDaTaMesDiaAno(
      {int? dataMilis, DateTime? dateTime, String? locale}) {
    try {
      var formatter = DateFormat.yMd(locale);
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '00/00/0000';
    }
  }

  static String getMesLocalizado(
      {int? dataMilis, DateTime? dateTime, String? dataString}) {
    try {
      if (dataString != null) dateTime = parseStringToDate(dataString);
      var formatter = DateFormat('MMM');
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '--/--/----';
    }
  }

  static int getWeek(DateTime date) {
    int year = date.year;
    DateTime stateDate = DateTime(year, 1, 1);
    int weekday = stateDate.weekday;
    int days = date.difference(stateDate).inDays;
    int week = ((weekday + days) / 7).ceil();
    return week;
  }

  static String getDiaDaSemanaLocalizado({int? dataMilis, DateTime? dateTime}) {
    try {
      var formatter = DateFormat('EEEE, dd/MM');
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '--/--/----';
    }
  }

  static String getDiaDaSemanaLocalizadoResumido(
      {int? dataMilis, DateTime? dateTime, String? locale}) {
    try {
      var formatter = DateFormat('EE', locale);
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '--/--/----';
    }
  }

  static String getDiaDaSemanaLocalizadoResumido2(
      {int? dataMilis, DateTime? dateTime}) {
    try {
      var formatter = DateFormat('E');
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '--/--/----';
    }
  }

  static String getDataAnoMesDia({int? dataMilis, DateTime? dateTime}) {
    try {
      var formatter = DateFormat('yyyy/MM/dd');
      return formatter.format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '--/--/----';
    }
  }

  static String getDia({int? dataMilis, DateTime? dateTime}) {
    try {
      var formatter = DateFormat.d();
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '--';
    }
  }

  static String getDiaAbreviacao({int? dataMilis, DateTime? dateTime}) {
    try {
      var formatter = DateFormat.E('pt');
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '--';
    }
  }

  static num getWeekOfMonth(DateTime? dateTime) {
    String date = dateTime.toString();
    String firstDay = date.substring(0, 7) + '01' + date.substring(10);
    int weekDay = DateTime.parse(firstDay).weekday;
    int currentWeek;
    if (weekDay == 7) {
      currentWeek = (DateTime.now().day / 7).ceil();
    } else {
      currentWeek = ((DateTime.now().day + weekDay) / 7).ceil();
    }
    return currentWeek;
  }

  static String getMes({int? dataMilis, DateTime? dateTime, String? locale}) {
    try {
      var formatter = DateFormat.MMM(locale);
      return formatter.format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '--';
    }
  }

  static String getDiaMes({int? dataMilis, DateTime? dateTime}) {
    try {
      var formatter = DateFormat.MMMd();
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '--';
    }
  }

  static String getDiaMesAno(
      {int? dataMilis, DateTime? dateTime, String? dateString, bool? formatarPorLocalidade}) {
        formatarPorLocalidade == null ? formatarPorLocalidade =false : formatarPorLocalidade =formatarPorLocalidade;
    try {
      if (dateString != null) dateTime = parseStringToDate(dateString);
      var formatter =formatarPorLocalidade ? DateFormat.yMd(Platform.localeName) : DateFormat.yMd('pt');
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '00/00/00';
    }
  }

  static String getDiaMesAnoAmericano({
    int? dataMilis,
    DateTime? dateTime,
    String? dateString,
  }) {
    try {
      if (dateString != null) dateTime = parseStringToDate(dateString);
      var formatter = DateFormat('MM/dd/yyyy', 'pt');
      return formatter.format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '00/00/00';
    }
  }

  static String getDiaMesAnoAbreviado(
      {int? dataMilis, DateTime? dateTime, String? dateString}) {
    try {
      if (dateString != null) dateTime = parseStringToDate(dateString);
      var formatter = DateFormat.yMd('pt');
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!))
          .substring(0, 8);
    } catch (e) {
      return '00/00/00';
    }
  }

  static List<DateTime> getTodosDiasDaSemana({DateTime? time}) {
    final firstDayOfWeek = time!.subtract(Duration(days: time.weekday - 1));
    return List.generate(7, (index) => index)
        .map((value) => firstDayOfWeek.add(Duration(days: value)))
        .toList();
  }

  static getDate(DateTime d) => DateTime(d.year, d.month, d.day);

  static num getSemanaMesAno(DateTime? dateTime) {
    return num.parse(
        "${getWeek(dateTime!)}${getMesAno(dateTime: dateTime).replaceAll('/', '')}");
  }

  static String getSemanaMesAnoString(DateTime? dateTime) {
    return '${getMesAno(dateTime: dateTime)} - ${getWeekOfMonth(dateTime)}ª semana';
  }
  static String getMesAbreviado({int? dataMilis, DateTime? dateTime, String? dateString}) {
    try {
      if (dateString != null) dateTime = parseStringToDate(dateString);
      var formatter = DateFormat.MMM();
      return capitalize(formatter.format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!)));
    } catch (e) {
      return '00/0000';
    }
  }
  static String getMesAnoNome(
      {int? dataMilis, DateTime? dateTime, String? dateString}) {
    try {
      if (dateString != null) dateTime = parseStringToDate(dateString);
      var formatter = DateFormat.MMMM();
      return capitalize(formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!)));
    } catch (e) {
      return '00/0000';
    }
  }

  static String getMesAno(
      {int? dataMilis, DateTime? dateTime, String? dateString}) {
    try {
      if (dateString != null) dateTime = parseStringToDate(dateString);
      var formatter = DateFormat.yM('pt');
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '00/0000';
    }
  }

  static String getMesEAnoAbreviado(
      {int? dataMilis, DateTime? dateTime, String? dateString}) {
    try {
      var stringRetorno = getMesAno(dataMilis: dataMilis, dateTime: dateTime);
      stringRetorno =
          stringRetorno.substring(0, 3) + stringRetorno.substring(5);
      return stringRetorno;
    } catch (e) {
      return '00/00';
    }
  }

  static String getDiaMesAnoHorasMinutos(
      {int? dataMilis, DateTime? dateTime, String? dateString}) {
    try {
      if (dateString != null) {
        dateTime = parseStringToDate(dateString, minutos: true);
      }
      var formatter = DateFormat.yMd('pt').add_Hm();
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '00/00/00 00:00:00';
    }
  }

  static String getDiaMesAnoHorasMinutosSegundos(
      {int? dataMilis, DateTime? dateTime, String? dateString}) {
    try {
      if (dateString != null) {
        dateTime = parseStringToDate(dateString, minutos: true);
      }
      var formatter = DateFormat.yMd('pt').add_Hms();
      return formatter.format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '00/00/00 00:00:00';
    }
  }

  static String getApenasHora({int? dataMilis, DateTime? dateTime}) {
    try {
      var formatter = DateFormat.Hm();
      return formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
    } catch (e) {
      return '--:--';
    }
  }

    static String getApenasHoraHistorico({int? dataMilis, DateTime? dateTime}) {
    try {
      var formatter = DateFormat.Hm();
      var hora = formatter
          .format(dateTime ?? DateTime.fromMillisecondsSinceEpoch(dataMilis!));
      if (hora == '00:00') {
        return '';
      } else {
        return 'às ${hora}';
      }
       
    } catch (e) {
      return '--:--';
    }
  }

  static String getApenasMinSecs({Duration? duration, num? segundos}) {
    try {
      return format(duration ?? Duration(seconds: segundos?.toInt() ?? 0));
    } catch (e) {
      return '--:--';
    }
  }

  static DateTime formatMMSS({Duration? duration, num? segundos}) {
    duration = duration ?? Duration(seconds: segundos!.toInt());
    var date = DateTime.now();
    date = date.subtract(Duration(
        hours: date.hour,
        minutes: date.minute,
        seconds: date.second,
        milliseconds: date.millisecond));
    date.add(duration);
    return date;
  }

  static bool isValidDate(String input) {
    try {
      final date = parseStringToDate(input);
      return input == getDiaMesAno(dateTime: date);
    } catch (e) {
      return false;
    }
  }

  static format(Duration d) => d.toString().split('.').first.replaceFirst('0:', '');
  static DateTime? parseNunToDate(num? value) =>
      value != null ? DateTime.fromMillisecondsSinceEpoch(value.toInt()) : null;
  static num? parseDateToNum(DateTime? date) => date?.millisecondsSinceEpoch;

  static String getDiaMesPorExtensoAno(String dateString) {
    String dia = dateString.substring(0, 2);
    String mes = dateString.substring(3, 5);
    String ano = dateString.substring(6, 10);
    String mesExtenso;

    switch (mes) {
      case '01':
        mesExtenso = localizedString('january');
        break;
      case '02':
        mesExtenso = localizedString('february');
        break;

      case '03':
        mesExtenso = localizedString('march');
        break;

      case '04':
        mesExtenso = localizedString('april');
        break;

      case '05':
        mesExtenso = localizedString('may');
        break;

      case '06':
        mesExtenso = localizedString('june');
        break;

      case '07':
        mesExtenso = localizedString('july');
        break;

      case '08':
        mesExtenso = localizedString('august');
        break;

      case '09':
        mesExtenso = localizedString('september');
        break;

      case '10':
        mesExtenso = localizedString('october');
        break;

      case '11':
        mesExtenso = localizedString('november');
        break;

      case '12':
        mesExtenso = localizedString('december');
        break;
      default:
        mesExtenso = '';
    }
    return localizedString('data_por_extenso',
        namedArgs: {'dia': dia, 'mesExtenso': mesExtenso, 'ano': ano});
  }

  static String? tratarDataComLocale(String dateString) {
    String dia = dateString.substring(0, 2);
    String mes = dateString.substring(3, 5);
    String ano = dateString.substring(6, 10);

    return localizedString('data_aula_tratada',
        namedArgs: {'dia': dia, 'mes': mes, 'ano': ano});
  }

  static num minutosEntreHorarios({String? inicio, String? fim}) {
    try {
      DateTime dateFim = DateFormat.Hm().parse(fim!);
      DateTime dateInicio = DateFormat.Hm().parse(inicio!);
      return (dateFim.millisecondsSinceEpoch -
              dateInicio.millisecondsSinceEpoch) ~/
          60000;
    } catch (e) {
      return 0;
    }
  }

  static num calcularTempoNaAcademia(int entradaEmMilissegundos) {
    try {
      DateTime entrada = DateTime.fromMillisecondsSinceEpoch(entradaEmMilissegundos);
      DateTime agora = DateTime.now();

      int diferencaEmMinutos = agora.difference(entrada).inMinutes;
      return diferencaEmMinutos;
    } catch (e) {
      return 0;
    }
  }

  static num calcularTempoRestanteParaCompleto(int entradaEmMilissegundos) {
    try {
      DateTime entrada = DateTime.fromMillisecondsSinceEpoch(entradaEmMilissegundos);
      DateTime completar60Minutos = entrada.add(const Duration(minutes: 60));
      DateTime agora = DateTime.now();

      Duration diferenca = completar60Minutos.difference(agora);
      int tempoRestanteEmMinutos = diferenca.inMinutes;

      if (tempoRestanteEmMinutos < 0) {
        tempoRestanteEmMinutos = 0;
      }

      return tempoRestanteEmMinutos;
    } catch (e) {
      return 0;
    }
  }

static String dataExibirChat(DateTime? dateTime) {
    if (dateTime == null) {
      return '';
    }
    final now = DateTime.now();
    final formatterHoraMin = DateFormat('HH:mm', Platform.localeName);
    final formatterMesDiaHoraMin = DateFormat('MMM d, HH:mm', Platform.localeName);
    final formatterDiaMesAnoHoraMin = DateFormat('d MMM yyyy, HH:mm', Platform.localeName);

    if (dateTime.year == now.year && dateTime.month == now.month && dateTime.day == now.day) {
      return formatterHoraMin.format(dateTime);
    } else if (dateTime.year == now.year && dateTime.month == now.month) {
      return formatterMesDiaHoraMin.format(dateTime);
    } else {
      return formatterDiaMesAnoHoraMin.format(dateTime);
    }
  }

  /// Converte data no formato YYYY-MM-DD para DD/MM/YYYY
  static String converterFormatoDatasInvertidas(String? dataString) {
  if (dataString == null || dataString.isEmpty) {
    return '';
  }
  
  if (dataString.contains('-')) {
    try {
      final partes = dataString.split('-');
      if (partes.length >= 3) {
        return '${partes[2]}/${partes[1]}/${partes[0]}';
      }
    } catch (e) {
  
    }
  }
  
  return dataString; // Retorna a data original se não conseguir converter
}
}
