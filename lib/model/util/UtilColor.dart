// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';

extension HexColor on Color {
  /// String is in the format "aabbcc" or "ffaabbcc" with an optional leading "#".
  static Color? fromHex(String? hexString) {
    if (hexString == null) return null;
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  static String colorToHex(Color color) {
    String hexString = '#' + color.toARGB32().toRadixString(16);
    hexString = hexString.substring(3, hexString.length);
    return hexString;
  }

  static colorToHexString(Color color) {
    return 'FF${color.toARGB32().toRadixString(16).substring(2, 8)}';
  }

  static Map<int, Color> getSwatch(Color color) {
    final hslColor = HSLColor.fromColor(color);
    final lightness = hslColor.lightness;

    /// if [500] is the default color, there are at LEAST five
    /// steps below [500]. (i.e. 400, 300, 200, 100, 50.) A
    /// divisor of 5 would mean [50] is a lightness of 1.0 or
    /// a color of #ffffff. A value of six would be near white
    /// but not quite.
    final lowDivisor = 6;

    /// if [500] is the default color, there are at LEAST four
    /// steps above [500]. A divisor of 4 would mean [900] is
    /// a lightness of 0.0 or color of #000000
    final highDivisor = 5;

    final lowStep = (1.0 - lightness) / lowDivisor;
    final highStep = lightness / highDivisor;

    return {
      50: (hslColor.withLightness(lightness + (lowStep * 5))).toColor(),
      100: (hslColor.withLightness(lightness + (lowStep * 4))).toColor(),
      200: (hslColor.withLightness(lightness + (lowStep * 3))).toColor(),
      300: (hslColor.withLightness(lightness + (lowStep * 2))).toColor(),
      400: (hslColor.withLightness(lightness + lowStep)).toColor(),
      500: (hslColor.withLightness(lightness)).toColor(),
      600: (hslColor.withLightness(lightness - highStep)).toColor(),
      700: (hslColor.withLightness(lightness - (highStep * 2))).toColor(),
      800: (hslColor.withLightness(lightness - (highStep * 3))).toColor(),
      900: (hslColor.withLightness(lightness - (highStep * 4))).toColor(),
    };
  }

  /// Prefixes a hash sign if [leadingHashSign] is set to `true` (default is `true`).
  String toHex({bool leadingHashSign = true}) => '${leadingHashSign ? '#' : ''}'
      '${alpha.toRadixString(16).padLeft(2, '0')}'
      '${red.toRadixString(16).padLeft(2, '0')}'
      '${green.toRadixString(16).padLeft(2, '0')}'
      '${blue.toRadixString(16).padLeft(2, '0')}';
}
Color lighten(Color color, [double amount = .1]) {
  assert(amount >= 0 && amount <= 1);

  final hsl = HSLColor.fromColor(color);
  final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));

  return hslLight.toColor();
}

List<Color> retornarCorGradientPadrao(context, {required ThemeMode themeMode, required bool escurecerCor}) {
  // CORES NOVAS
  if (Theme.of(context).primaryColor == const Color(0xFF1EC3FA)) {
    if (themeMode == ThemeMode.dark) {
       return [const Color.fromRGBO(3, 94, 125, 1),const Color.fromRGBO(4, 132, 175, 1),const Color.fromRGBO(5, 170, 225, 1)];
    } else {
      return escurecerCor 
      ? [const Color.fromRGBO(3, 94, 125, 1),const Color.fromRGBO(4, 132, 175, 1),const Color.fromRGBO(5, 170, 225, 1)]
      : [const Color.fromRGBO(5, 170, 225, 1),const Color.fromRGBO(30, 195, 250, 1),const Color.fromRGBO(80, 209, 251, 1)];
    }
  }
  if (Theme.of(context).primaryColor == const Color(0xFF1E60FA)) {
    if (themeMode == ThemeMode.dark) {
       return [const Color.fromRGBO(3, 39, 125, 1), const Color.fromRGBO(4, 55, 175, 1),const Color.fromRGBO(5, 71, 225, 1)];
    } else {
      return escurecerCor 
      ? [const Color.fromRGBO(3, 39, 125, 1), const Color.fromRGBO(4, 55, 175, 1),const Color.fromRGBO(5, 71, 225, 1)] 
      : [const Color.fromRGBO(30, 96, 250, 1),const Color.fromRGBO(80, 131, 251, 1),const Color.fromRGBO(130, 167, 252, 1)];
    }
  }

  if (Theme.of(context).primaryColor == const Color(0xFF05E173)) {
    if (themeMode == ThemeMode.dark) {
       return [const Color.fromRGBO(2, 75, 38, 1),const Color.fromRGBO(3, 125, 64, 1),const Color.fromRGBO(4, 175, 89, 1)];
    } else {
      return [const Color.fromRGBO(4, 175, 89, 1),const Color.fromRGBO(5, 225, 115, 1),const Color.fromRGBO(80, 251, 166, 1)];
    }
  }

  if (Theme.of(context).primaryColor == const Color(0xFFFAC31E)) {
    if (themeMode == ThemeMode.dark) {
       return [const Color.fromRGBO(125, 94, 3, 1),const Color.fromRGBO(175, 132, 4, 1),const Color.fromRGBO(250, 195, 30, 1)];
    } else {
      return [const Color.fromRGBO(225, 170, 5, 1),const Color.fromRGBO(250, 195, 30, 1),const Color.fromRGBO(251, 209, 80, 1)];
    }
  }
  if (Theme.of(context).primaryColor == const Color(0xFFFA8425)) {
    if (themeMode == ThemeMode.dark) {
       return [const Color.fromRGBO(125, 58, 3, 1),const Color.fromRGBO(175, 81, 4, 1),const Color.fromRGBO(225, 104, 5, 1)];
    } else {
      return [const Color.fromRGBO(225, 104, 5, 1),const Color.fromRGBO(250, 132, 37, 1),const Color.fromRGBO(251, 157, 80, 1)];
    }
  }

  if (Theme.of(context).primaryColor == const Color(0xFFFA1E55)) {
    if (themeMode == ThemeMode.dark) {
       return [const Color.fromRGBO(125, 3, 33, 1),const Color.fromRGBO(175, 4, 46, 1),const Color.fromRGBO(225, 5, 60, 1)];
    } else {
      return [const Color.fromRGBO(225, 5, 60, 1),const Color.fromRGBO(250, 30, 85, 1),const Color.fromRGBO(251, 80, 123, 1)];
    }
  }

  if (Theme.of(context).primaryColor == const Color(0xFFE105AA)) {
    if (themeMode == ThemeMode.dark) {
       return [const Color.fromRGBO(125, 3, 94, 1),const Color.fromRGBO(175, 4, 132, 1),const Color.fromRGBO(225, 5, 170, 1)];
    } else {
      return [const Color.fromRGBO(225, 5, 170, 1),const Color.fromRGBO(250, 30, 195, 1),const Color.fromRGBO(251, 80, 209, 1)];
    }
  }

  if (Theme.of(context).primaryColor == const Color(0xFFC31EFA)) {
    if (themeMode == ThemeMode.dark) {
       return [const Color.fromRGBO(83, 38, 83, 1),const Color.fromRGBO(100, 46, 100, 1),const Color.fromRGBO(120, 56, 120, 1)];
    } else {
      return [const Color.fromRGBO(195, 30, 250, 1),const Color.fromRGBO(209, 80, 251, 1),const Color.fromRGBO(222, 130, 252, 1)];
    }
  }


  // CORES ANTIGAS
  if (Theme.of(context).primaryColor == const Color(0xFFFF765E)) {
    if (themeMode == ThemeMode.dark) {

       return [const Color(0xFFAA1A00), const Color(0xFFF72500)];
    } else {
      return [ const Color(0xFFF72500),  const Color(0xFFFF4B2B), const Color(0xFFFF765E)];
    }
  } else if (Theme.of(context).primaryColor == const Color(0xFFE25260)) {
    if (themeMode ==  ThemeMode.dark) {
      return [const Color(0xFF801620), const Color(0xFF9A1B27), const Color(0xFFBA202F)];
    } else {
       return [const Color(0xFFBA202F), const Color(0xFFDB2C3D),const Color(0xFFE25260) ];
    }
  } else if (Theme.of(context).primaryColor == const Color(0xFF48D567)) {
    if (themeMode ==  ThemeMode.dark) {
      return [const Color(0xFF1D7E33), const Color(0xFF22933B), const Color(0xFF28AB45)];
    } else {
      return [ const Color(0xFF28AB45), const Color(0xFF2EC750), const Color(0xFF48D567)];
    }    
  } else if (Theme.of(context).primaryColor == const Color(0xFFF0B924)) {
    if (themeMode ==  ThemeMode.dark) {
      return [ const Color(0xFF946F0A), const Color(0xFFB2860C), const Color(0xFFD6A10F)];
    } else {
      return [ const Color(0xFFD6A10F), const Color(0xFFF0B924), const Color(0xFFF3C750)];
    }    
  } else if (Theme.of(context).primaryColor == const Color(0xFF1998FC)) {
    if (themeMode ==  ThemeMode.dark) {
      return [ const Color(0xFF02589C), const Color(0xFF026ABC), const Color(0xFF0380E3)];
    } else {
      return [ const Color(0xFF0380E3), const Color(0xff1998FC), const Color(0xFF47ADFD)];
    }    
  } else if (Theme.of(context).primaryColor == const Color(0xFFBA68BA)) {
    if (themeMode ==  ThemeMode.dark) {
      return [ const Color(0xFF532653), const Color(0xFF642E64), const Color(0xFF783878)];
    } else {
      return [ const Color(0xFF783878), const Color(0xFF914391), const Color(0xFFAA4EAA)];
    }    
  } else if (Theme.of(context).primaryColor == const Color(0xFFFF2970)) {
    if (themeMode ==  ThemeMode.dark) {
      return [ const Color(0xFFA90038), const Color(0xFFCC0044), const Color(0xFFF60052)];
    } else {
      return [ const Color(0xFFF60052),const Color(0xFFFF2970),const Color(0xFFFF5B92)];
    }
  }
  return [
    Theme.of(context).primaryColor,
    Theme.of(context).primaryColor.withValues(alpha:0.7)
  ];
}

String heartGraphSvg = '''<svg width="160" height="161" viewBox="0 0 160 161" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M136.479 16.5H105.474C101.899 16.5 99 18.879 99 21.8136V46.1864C99 49.121 101.899 51.5 105.474 51.5H136.479C140.055 51.5 142.953 49.121 142.953 46.1864V21.8136C142.953 18.879 140.055 16.5 136.479 16.5Z" fill="#E7E7E7" fill-opacity="0.9"/>
<path d="M116.535 40.373C117.012 40.373 117.396 39.9886 117.396 39.5111V24.9962C117.396 24.5187 117.012 24.1343 116.535 24.1343C116.057 24.1343 115.673 24.5187 115.673 24.9962V39.5111C115.673 39.9886 116.057 40.373 116.535 40.373Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M105.042 47.3253C105.52 47.3253 105.904 46.9408 105.904 46.4634V24.256C105.904 23.7785 105.52 23.394 105.042 23.394C104.565 23.394 104.18 23.7785 104.18 24.256V46.4634C104.18 46.9408 104.565 47.3253 105.042 47.3253Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M113.661 44.8647C114.138 44.8647 114.523 44.4802 114.523 44.0027V32.3771C114.523 31.8996 114.138 31.5151 113.661 31.5151C113.183 31.5151 112.799 31.8996 112.799 32.3771V44.0027C112.799 44.4802 113.183 44.8647 113.661 44.8647Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M119.407 41.9108C119.885 41.9108 120.269 41.5264 120.269 41.0489V29.67C120.269 29.1925 119.885 28.8081 119.407 28.8081C118.93 28.8081 118.545 29.1925 118.545 29.67V41.0489C118.545 41.5264 118.93 41.9108 119.407 41.9108Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M107.915 41.911C108.392 41.911 108.777 41.5266 108.777 41.0491V36.8043C108.777 36.3268 108.392 35.9424 107.915 35.9424C107.437 35.9424 107.053 36.3268 107.053 36.8043V41.0491C107.053 41.5266 107.437 41.911 107.915 41.911Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M110.788 39.1429C111.266 39.1429 111.65 38.7585 111.65 38.281V31.8834C111.65 31.4059 111.266 31.0215 110.788 31.0215C110.311 31.0215 109.927 31.4059 109.927 31.8834V38.281C109.927 38.7585 110.311 39.1429 110.788 39.1429Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M134.038 40.373C134.516 40.373 134.9 39.9886 134.9 39.5111V24.9962C134.9 24.5187 134.516 24.1343 134.038 24.1343C133.561 24.1343 133.177 24.5187 133.177 24.9962V39.5111C133.177 39.9886 133.561 40.373 134.038 40.373Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M122.55 46.6493C123.028 46.6493 123.412 46.2649 123.412 45.7874V24.2599C123.412 23.7824 123.028 23.3979 122.55 23.3979C122.073 23.3979 121.688 23.7824 121.688 24.2599V45.7874C121.688 46.2649 122.073 46.6493 122.55 46.6493Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M131.167 44.1889C131.644 44.1889 132.029 43.8045 132.029 43.327V32.3771C132.029 31.8996 131.644 31.5151 131.167 31.5151C130.689 31.5151 130.305 31.8996 130.305 32.3771V43.327C130.305 43.8045 130.689 44.1889 131.167 44.1889Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M136.913 41.235C137.39 41.235 137.775 40.8506 137.775 40.3731V29.67C137.775 29.1925 137.39 28.8081 136.913 28.8081C136.435 28.8081 136.051 29.1925 136.051 29.67V40.3731C136.051 40.8506 136.435 41.235 136.913 41.235Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M125.424 41.2352C125.901 41.2352 126.286 40.8508 126.286 40.3733V36.8043C126.286 36.3268 125.901 35.9424 125.424 35.9424C124.946 35.9424 124.562 36.3268 124.562 36.8043V40.3733C124.562 40.8508 124.946 41.2352 125.424 41.2352Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M128.296 39.1429C128.774 39.1429 129.158 38.7585 129.158 38.281V31.8834C129.158 31.4059 128.774 31.0215 128.296 31.0215C127.819 31.0215 127.434 31.4059 127.434 31.8834V38.281C127.434 38.7585 127.819 39.1429 128.296 39.1429Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M103.88 111.245C107.775 108.472 110.208 102.838 110.208 95.0254C110.208 79.7517 100.947 62.0049 89.523 55.3915C83.1012 51.6726 77.394 52.3143 73.6017 56.258C69.8094 47.9256 64.1022 40.6751 57.6804 36.9562C52.5663 33.9961 47.8866 33.776 44.2764 35.7994C39.8256 38.2958 37 44.202 37 52.6374C37 60.4499 39.4333 68.8994 43.3284 76.1873H43.319L73.6017 134.5V134.481V133.802V134.5L103.884 111.259L103.875 111.24L103.88 111.245Z" fill="#F0F0F0"/>
<path d="M74.8385 106.227C74.8385 106.227 74.801 106.227 74.7823 106.227C74.1028 106.199 73.5358 105.686 73.4468 105.008L67.9875 65.3025L63.428 79.162C63.2968 79.5667 62.9875 79.8867 62.5939 80.0373C62.2003 80.1879 61.7551 80.155 61.3896 79.9385L45.6773 70.8274C45.0072 70.4368 44.7729 69.5756 45.1618 68.8979C45.5508 68.225 46.4083 67.9897 47.0831 68.3803L61.3005 76.6254L67.219 58.6386C67.4252 58.0127 68.0344 57.6127 68.6811 57.6739C69.3324 57.7351 69.8573 58.2339 69.9463 58.8881L75.1525 96.7722L79.5761 76.4889C79.7214 75.8159 80.3165 75.3547 81.0147 75.3783C81.6989 75.4112 82.2612 75.9336 82.3456 76.616L83.9482 89.7554L102.257 100.372C102.927 100.763 103.161 101.624 102.772 102.302C102.383 102.975 101.526 103.21 100.851 102.82L81.9332 91.8543C81.5489 91.6331 81.2959 91.2425 81.2397 90.8049L80.5555 85.1952L76.2115 105.121C76.071 105.77 75.4946 106.231 74.8385 106.231V106.227Z" fill="<<-CorPadrao->>"/>
<path d="M71.155 29.0063C65.9818 26.0079 61.251 25.7954 57.6076 27.8589H57.6029L44.2485 35.5838V35.5932C47.8919 33.5534 52.6039 33.7753 57.763 36.7595C64.2354 40.5086 69.9876 47.8133 73.8099 56.2181C74.5254 55.4721 75.3115 54.8441 76.1541 54.3388L87.0136 48.0635C83.1914 39.8334 77.5239 32.694 71.155 29.0063Z" fill="#838383"/>
<path d="M106.847 108.784C106.128 109.685 105.333 110.458 104.458 111.081L104.467 111.1L73.9661 134.5L87.3491 126.756L117.85 103.356L117.841 103.337C121.063 101.045 123.288 96.8058 123.979 91.01C123.1 98.0508 111.377 105.964 106.843 108.779L106.847 108.784Z" fill="#838383"/>
<path d="M103.179 47.7837C97.848 44.7477 93.0059 44.655 89.3332 46.9076L86.9755 48.2425L76.1406 54.4026C79.7898 52.2565 84.5662 52.4002 89.8122 55.3899C101.3 61.9346 110.613 79.497 110.613 94.612C110.613 100.619 109.129 105.319 106.635 108.406C111.163 105.639 122.866 97.8613 123.745 90.9411C123.895 89.6988 123.98 88.3871 123.98 87.0059C123.98 71.8908 114.666 54.3284 103.179 47.7837Z" fill="#DCDDDF"/>
<path d="M73.8473 134.495C73.81 134.495 73.7773 134.486 73.7446 134.472C73.7259 134.463 73.7072 134.449 73.6885 134.435C73.6651 134.416 73.6465 134.392 73.6324 134.369L43.3436 76.1608C43.3436 76.1608 43.325 76.114 43.3203 76.0906C39.2469 68.4598 37 60.0949 37 52.5389C37 44.3564 39.6953 38.1564 44.3994 35.5239C48.2112 33.3918 53.0553 33.7986 58.0396 36.6788C64.1684 40.2184 69.9421 47.1572 73.9174 55.7465C77.9487 51.7861 83.7505 51.5383 89.8886 55.0872C101.361 61.7174 110.695 79.5554 110.695 94.8544C110.695 102.424 108.438 108.218 104.346 111.182C104.332 111.205 104.309 111.234 104.286 111.252L73.9968 134.453C73.9548 134.486 73.9034 134.5 73.8567 134.5L73.8473 134.495ZM73.8473 133.331C73.9781 133.331 74.0809 133.434 74.0809 133.565V133.789L103.931 110.93C103.945 110.902 103.968 110.873 103.996 110.855C108.013 107.998 110.228 102.317 110.228 94.8544C110.228 79.705 101.002 62.0447 89.6551 55.494C83.5964 51.9919 77.8974 52.2911 74.0155 56.3169C73.9641 56.373 73.88 56.4011 73.81 56.3871C73.7352 56.373 73.6698 56.3263 73.6371 56.2515C69.6945 47.6107 63.9255 40.6252 57.806 37.0903C52.9712 34.2989 48.2906 33.8874 44.6283 35.9354C40.0784 38.479 37.4671 44.5294 37.4671 52.5389C37.4671 60.0435 39.7047 68.3523 43.7687 75.9364C43.7827 75.9597 43.7921 75.9878 43.7921 76.0112L73.6651 133.415C73.7072 133.364 73.7726 133.331 73.8427 133.331H73.8473Z" fill="#363636"/>
<path d="M74.9077 106.413H74.847C74.0482 106.38 73.3942 105.786 73.2868 105.001L67.9848 66.5475L63.7526 79.3637C63.5985 79.8312 63.2434 80.2053 62.781 80.3783C62.3185 80.556 61.8093 80.5139 61.3796 80.2661L45.7167 71.2138C45.3383 70.9941 45.0674 70.6434 44.9552 70.2226C44.8431 69.8018 44.8992 69.3576 45.1187 68.9788C45.3383 68.6001 45.6886 68.3289 46.1091 68.2167C46.5295 68.1045 46.9733 68.1606 47.3516 68.3804L61.2768 76.4273L67.0879 58.8278C67.3262 58.1077 68.0456 57.6449 68.7883 57.7103C69.5497 57.7804 70.1523 58.3602 70.2598 59.1177L75.2721 95.4576L79.4015 76.5863C79.5744 75.8008 80.2751 75.2911 81.0738 75.3004C81.8773 75.3379 82.5266 75.941 82.6247 76.7359L84.2083 89.6737L102.356 100.161C102.735 100.381 103.006 100.732 103.118 101.153C103.23 101.573 103.174 102.018 102.954 102.396C102.735 102.775 102.384 103.046 101.964 103.159C101.544 103.271 101.1 103.215 100.721 102.995L81.8633 92.1004C81.4195 91.8432 81.1206 91.3897 81.0598 90.88L80.546 86.6672L76.5053 105.122C76.3418 105.866 75.6691 106.408 74.9077 106.408V106.413ZM68.0783 65.285C68.0783 65.285 68.0923 65.285 68.1016 65.285C68.2091 65.2944 68.2978 65.3785 68.3118 65.4861L73.7539 104.935C73.8333 105.496 74.3004 105.922 74.8657 105.945H74.9124C75.4589 105.945 75.9354 105.562 76.0522 105.029L80.3825 85.2317C80.4059 85.1195 80.5086 85.04 80.6207 85.0494C80.7328 85.054 80.8263 85.1429 80.8403 85.2551L81.5223 90.8286C81.5643 91.1933 81.7792 91.5159 82.0969 91.6983L100.955 102.597C101.226 102.752 101.539 102.794 101.843 102.714C102.141 102.635 102.394 102.438 102.553 102.167C102.707 101.896 102.749 101.583 102.669 101.279C102.59 100.975 102.394 100.727 102.123 100.568L83.872 90.0197C83.8066 89.9823 83.7645 89.9168 83.7552 89.8467L82.1576 76.792C82.0875 76.2262 81.6251 75.7961 81.0552 75.768C80.4946 75.754 79.9808 76.1234 79.8593 76.6845L75.4496 96.8369C75.4262 96.9491 75.3281 97.0193 75.2113 97.0193C75.0992 97.0193 75.0058 96.9304 74.9871 96.8182L69.7973 59.1785C69.7226 58.6361 69.2928 58.2246 68.7463 58.1732C68.2137 58.1218 67.6999 58.4538 67.5317 58.9728L61.6318 76.8434C61.6085 76.9089 61.5571 76.965 61.4917 76.9884C61.4263 77.0118 61.3516 77.0071 61.2908 76.9744L47.1181 68.7825C46.8471 68.6282 46.5342 68.5861 46.2305 68.6656C45.9269 68.7451 45.6793 68.9414 45.5205 69.208C45.3617 69.4745 45.3243 69.7924 45.4037 70.0963C45.4831 70.4003 45.6793 70.6481 45.9502 70.8071L61.6132 79.8593C61.9168 80.037 62.2812 80.065 62.6128 79.9388C62.9398 79.8125 63.1967 79.5507 63.3042 79.214L67.8494 65.444C67.8821 65.3458 67.9708 65.285 68.0689 65.285H68.0783Z" fill="#363636"/>
<path d="M73.8473 56.3871C73.8473 56.3871 73.8193 56.3871 73.81 56.3871C73.7352 56.373 73.6698 56.3263 73.6371 56.2515C69.6945 47.6107 63.9255 40.6252 57.806 37.0903C52.9712 34.2989 48.2906 33.8874 44.6283 35.9354C44.5675 35.9728 44.4928 35.9728 44.4274 35.9494C44.362 35.9214 44.3106 35.8653 44.2919 35.7998C44.2592 35.6969 44.3013 35.58 44.3947 35.5239L57.6472 27.8604C57.6472 27.8604 57.6846 27.8417 57.7079 27.837C61.5197 25.7142 66.3499 26.1304 71.3295 29.0059C77.4162 32.5221 83.1853 39.4375 87.1606 47.9801C87.2166 48.097 87.1606 48.2373 87.0485 48.2887C86.9317 48.3448 86.7915 48.2887 86.7402 48.1765C82.8069 39.7227 77.1032 32.8821 71.1006 29.4081C66.2471 26.6073 61.5524 26.2005 57.8854 28.2718C57.8668 28.2812 57.8481 28.2906 57.8294 28.2952L47.1601 34.4579C50.43 33.7051 54.1904 34.4579 58.0396 36.6788C64.1684 40.2184 69.9421 47.1572 73.9174 55.7465C74.576 55.1012 75.2954 54.5448 76.0522 54.0913C76.1596 54.0258 76.3044 54.0586 76.3745 54.1708C76.4399 54.283 76.4025 54.4233 76.2951 54.4934C75.4776 54.9797 74.7115 55.5922 74.0155 56.3123C73.9734 56.359 73.9127 56.3824 73.8473 56.3824V56.3871Z" fill="#363636"/>
<path d="M73.8473 134.495C73.7726 134.495 73.6978 134.458 73.6511 134.392C73.5811 134.29 73.6044 134.154 73.7025 134.079L103.926 110.93C103.94 110.902 103.963 110.873 103.991 110.855C104.823 110.261 105.608 109.508 106.318 108.62C106.397 108.517 106.547 108.503 106.645 108.582C106.747 108.662 106.761 108.811 106.682 108.91C105.967 109.807 105.178 110.574 104.332 111.182C104.318 111.205 104.3 111.229 104.276 111.248L76.8416 132.26L87.0204 126.383L117.216 103.252C117.23 103.224 117.253 103.196 117.281 103.177C120.49 100.895 122.625 96.6125 123.284 91.1185C123.298 90.9922 123.415 90.9034 123.545 90.9127C123.672 90.9268 123.765 91.0437 123.751 91.1746C123.078 96.7621 120.906 101.134 117.627 103.505C117.613 103.528 117.594 103.551 117.571 103.57L87.282 126.771L73.9641 134.463C73.9267 134.486 73.8847 134.495 73.8473 134.495Z" fill="#363636"/>
<path d="M106.5 109.003C106.448 109.003 106.397 108.984 106.355 108.952C106.252 108.872 106.238 108.723 106.318 108.624C108.873 105.417 110.223 100.657 110.223 94.8544C110.223 79.705 100.997 62.0447 89.6504 55.494C84.7268 52.6512 79.9854 52.2958 76.2904 54.4981C76.1783 54.5635 76.0382 54.5261 75.9681 54.4186C75.9027 54.3064 75.9401 54.1661 76.0475 54.096C79.892 51.8049 84.8062 52.1602 89.8793 55.0919C101.352 61.7221 110.685 79.56 110.685 94.8591C110.685 100.769 109.298 105.632 106.677 108.919C106.631 108.975 106.565 109.008 106.495 109.008L106.5 109.003Z" fill="#363636"/>
<path d="M123.517 91.3803C123.517 91.3803 123.499 91.3803 123.489 91.3803C123.363 91.3663 123.27 91.2494 123.284 91.1185C123.438 89.8467 123.513 88.5188 123.513 87.1721C123.513 72.0228 114.287 54.3672 102.94 47.8118C97.942 44.9222 93.1352 44.6089 89.4121 46.9281C89.3935 46.9374 89.3794 46.9468 89.3608 46.9514L87.0625 48.2793C86.9504 48.3448 86.8102 48.3027 86.7448 48.1952C86.6794 48.083 86.7168 47.9427 86.8289 47.8772L89.1739 46.5259C89.1739 46.5259 89.2019 46.5119 89.2113 46.5072C93.0838 44.1226 98.0401 44.4452 103.174 47.4143C114.647 54.0445 123.98 71.8825 123.98 87.1815C123.98 88.5468 123.9 89.8888 123.746 91.1793C123.732 91.2962 123.63 91.385 123.513 91.385L123.517 91.3803Z" fill="#363636"/>
<rect x="7" y="96.5" width="52.806" height="40.9701" rx="6" fill="#E7E7E7" fill-opacity="0.9"/>
<path d="M24.5902 107.43C24.5902 110.025 22.487 112.128 19.8923 112.128C17.2976 112.128 15.1943 110.025 15.1943 107.43C15.1943 104.836 17.2976 102.731 19.8923 102.731C22.487 102.731 24.5902 104.836 24.5902 107.43Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M24.5902 124.851C24.5902 127.446 22.487 129.55 19.8923 129.55C17.2976 129.55 15.1943 127.446 15.1943 124.851C15.1943 122.257 17.2976 120.153 19.8923 120.153C22.487 120.153 24.5902 122.257 24.5902 124.851Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M39.0896 105.477H32.4194C31.4492 105.477 30.6621 104.69 30.6621 103.72C30.6621 102.749 31.4492 101.963 32.4194 101.963H39.0896C40.0598 101.963 40.8469 102.749 40.8469 103.72C40.8469 104.69 40.0598 105.477 39.0896 105.477Z" fill="#838383"/>
<path d="M45.7599 112.898H32.4194C31.4483 112.898 30.6621 112.111 30.6621 111.141C30.6621 110.17 31.4483 109.384 32.4194 109.384H45.7599C46.7301 109.384 47.5172 110.17 47.5172 111.141C47.5172 112.111 46.7301 112.898 45.7599 112.898Z" fill="#838383"/>
<path d="M39.0896 122.898H32.4194C31.4492 122.898 30.6621 122.112 30.6621 121.141C30.6621 120.169 31.4492 119.383 32.4194 119.383H39.0896C40.0598 119.383 40.8469 120.169 40.8469 121.141C40.8469 122.112 40.0598 122.898 39.0896 122.898Z" fill="#838383"/>
<path d="M45.7599 130.319H32.4194C31.4483 130.319 30.6621 129.533 30.6621 128.561C30.6621 127.591 31.4483 126.804 32.4194 126.804H45.7599C46.7301 126.804 47.5172 127.591 47.5172 128.561C47.5172 129.533 46.7301 130.319 45.7599 130.319Z" fill="#838383"/>
<path d="M66.8444 119.18L66.8442 119.18L66.8525 119.187C67.0044 119.323 67.125 119.487 67.2074 119.669C67.2897 119.85 67.3321 120.047 67.3321 120.244C67.3321 120.442 67.2897 120.638 67.2074 120.82C67.125 121.002 67.0044 121.166 66.8525 121.302L66.8524 121.302L66.8451 121.309L52.5722 135.118L52.5715 135.119C52.4325 135.255 52.2667 135.363 52.0834 135.436C51.9001 135.51 51.7032 135.549 51.5042 135.549C51.3051 135.549 51.1082 135.51 50.9249 135.436C50.7417 135.363 50.5758 135.255 50.4368 135.119L50.4361 135.118L43.7194 128.62L43.7195 128.62L43.7163 128.617C43.5714 128.481 43.456 128.32 43.3762 128.142C43.2964 127.965 43.2537 127.774 43.2502 127.581C43.2468 127.388 43.2826 127.196 43.356 127.016C43.4294 126.836 43.5389 126.671 43.6788 126.53C43.8187 126.39 43.9862 126.278 44.1719 126.2C44.3576 126.122 44.5577 126.08 44.7607 126.076C44.9637 126.073 45.1653 126.108 45.3538 126.18C45.5416 126.252 45.7125 126.358 45.857 126.493L51.33 131.805L51.504 131.974L51.6781 131.805L64.7092 119.18L64.7098 119.18C64.8488 119.044 65.0146 118.936 65.1979 118.862C65.3812 118.788 65.5781 118.75 65.7771 118.75C65.9761 118.75 66.173 118.788 66.3563 118.862C66.5396 118.936 66.7054 119.044 66.8444 119.18Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
</svg>''';

String moedasConsultasSvg = '''<svg width="257" height="256" viewBox="0 0 257 256" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1_43289)">
<path d="M145.565 135.031L155.014 134.955C161.974 134.316 168.415 128.662 172.734 119.444C181.128 101.534 179.496 75.1395 169.113 60.6064C164.237 53.7961 158.079 50.4945 151.78 51.3222H145.424V52.0465H151.827C157.9 51.2376 163.813 54.4358 168.528 61.0203C178.779 75.3652 180.383 101.44 172.093 119.143C167.887 128.126 161.654 133.629 154.986 134.24L145.565 134.316V135.031Z" fill="#363636"/>
<path d="M131.58 125.615C121.197 111.082 119.565 84.687 127.949 66.7771C132.278 57.5493 138.728 51.896 145.66 51.2564C151.752 50.7014 157.702 54.0219 162.408 60.6158C172.801 75.1489 174.423 101.543 166.03 119.453C161.71 128.681 155.251 134.335 148.319 134.965C147.876 135.002 147.442 135.021 147.008 135.021C141.378 135.021 135.946 131.72 131.57 125.605L131.58 125.615ZM161.824 61.0297C157.269 54.6615 151.554 51.4445 145.726 51.9713C139.049 52.5827 132.815 58.0855 128.6 67.0781C120.31 84.7811 121.904 110.847 132.165 125.201C136.72 131.578 142.444 134.795 148.263 134.259C154.939 133.648 161.173 128.145 165.388 119.152C173.678 101.449 172.084 75.3746 161.824 61.0297ZM133.494 121.513C124.413 108.805 122.989 85.7406 130.326 70.0882C134.107 62.0174 139.756 57.0696 145.829 56.5146C151.167 56.0255 156.373 58.9415 160.494 64.7077C169.576 77.4158 171 100.481 163.663 116.133C159.881 124.204 154.232 129.152 148.159 129.707C147.772 129.744 147.395 129.763 147.008 129.763C142.076 129.763 137.314 126.875 133.494 121.513ZM145.886 57.2295C140.077 57.7563 134.635 62.5536 130.967 70.3986C123.734 85.844 125.129 108.589 134.07 121.109C138.04 126.659 143.019 129.462 148.093 129.001C153.902 128.474 159.343 123.677 163.012 115.832C170.245 100.386 168.849 77.6416 159.909 65.1215C156.222 59.9668 151.667 57.173 146.971 57.173C146.612 57.173 146.254 57.1919 145.886 57.2201V57.2295Z" fill="#363636"/>
<path d="M166.04 119.453C174.423 101.543 172.801 75.1488 162.418 60.6158C159.316 56.2794 155.675 53.3634 151.818 52.0371H151.847C157.92 51.2375 163.833 54.4357 168.548 61.0203C178.799 75.3652 180.402 101.44 172.113 119.143C167.907 128.126 161.673 133.629 155.006 134.24L151.639 134.269C157.297 132.416 162.428 127.204 166.058 119.453H166.04Z" fill="#838383"/>
<path d="M130.967 70.3986C134.635 62.563 140.077 57.7657 145.886 57.2295C146.244 57.2013 146.603 57.1825 146.971 57.1825C151.667 57.1825 156.222 59.9668 159.909 65.131C168.859 77.651 170.254 100.396 163.012 115.841C159.343 123.677 153.902 128.474 148.093 129.01C143.019 129.471 138.04 126.668 134.07 121.118C125.12 108.598 123.724 85.8535 130.967 70.408V70.3986Z" fill="#F0F0F0"/>
<path d="M132.165 125.201C136.38 131.089 141.576 134.278 146.942 134.316H147.376C147.668 134.306 147.97 134.297 148.263 134.269C154.939 133.657 161.173 128.154 165.388 119.162C173.678 101.459 172.084 75.384 161.824 61.0391C158.061 55.7809 153.515 52.6768 148.772 52.0465H145.424V52.0183C138.86 52.7614 132.74 58.2266 128.6 67.0875C120.31 84.7905 121.904 110.856 132.165 125.21V125.201ZM130.326 70.0976C134.107 62.0268 139.756 57.079 145.829 56.524C151.167 56.0349 156.373 58.9509 160.494 64.7171C169.576 77.4253 171 100.49 163.663 116.142C159.881 124.213 154.232 129.161 148.159 129.716C147.772 129.754 147.395 129.772 147.008 129.772C142.076 129.772 137.314 126.885 133.494 121.523C124.413 108.815 122.989 85.75 130.326 70.0976Z" fill="#DCDDDF"/>
<path d="M154.661 115.458L152.864 111.167L152.72 111.293C150.975 112.844 147.497 114.925 143.849 111.36L143.412 110.932L145.155 105.956L145.502 106.257C147.651 108.097 150.402 106.958 152.047 105.122C154.736 102.113 155.565 98.2511 154.22 95.0363C153.113 92.3861 151.068 92.42 147.775 95.1157C143.75 98.4829 140.167 100.685 137.191 93.559C134.169 86.3397 137.764 80.0937 138.934 78.3583L139.049 78.1837L137.405 74.2671L140.217 71.1305L141.857 75.0383L141.99 74.9373C144.54 73.0386 146.728 73.0104 148.862 74.8664L149.327 75.2634L147.809 79.9823L147.488 79.8025C145.943 78.9307 143.769 79.5478 142.217 81.2876C139.671 84.142 138.894 87.4297 140.102 90.3139C141.406 93.4232 143.452 92.0791 145.982 89.9966C150.161 86.6109 154.164 84.1885 157.279 91.6241C159.87 97.7988 158.351 104.12 155.705 107.636L155.579 107.806L157.473 112.322L154.661 115.458Z" fill="<<-CorPadrao->>"/>
<path d="M154.922 115.691L157.734 112.555C157.825 112.45 157.851 112.309 157.801 112.177C157.801 112.177 155.989 107.851 155.986 107.842C158.69 104.242 160.248 97.7856 157.607 91.479C156.389 88.5685 154.948 86.9419 153.216 86.5039C150.749 85.883 148.079 87.8374 145.752 89.7117C144.324 90.8805 142.846 91.9978 141.812 91.7473C141.266 91.6129 140.813 91.0903 140.429 90.1688C139.272 87.4164 140.022 84.2698 142.478 81.5206C143.925 79.9014 145.913 79.325 147.314 80.1106L147.635 80.2904C147.727 80.346 147.84 80.3531 147.937 80.3162C148.034 80.2793 148.114 80.1984 148.145 80.0959L149.664 75.3769C149.711 75.2379 149.666 75.0939 149.557 74.9944L149.101 74.5941C146.911 72.6988 144.586 72.6584 142.009 74.4868L140.548 70.9941C140.504 70.8799 140.402 70.7979 140.272 70.7766C140.152 70.752 140.029 70.799 139.943 70.8919L137.132 74.0286C137.041 74.1337 137.015 74.2748 137.065 74.4066C137.065 74.4066 138.63 78.142 138.624 78.1542C137.42 79.9327 133.756 86.3157 136.847 93.6898C138.045 96.5476 139.418 98.1295 141.065 98.5295C143.335 99.0843 145.732 97.2845 148.002 95.3919C149.849 93.8716 151.298 93.229 152.302 93.4808C152.96 93.6432 153.485 94.1983 153.889 95.1726C155.186 98.2643 154.376 101.988 151.778 104.892C150.246 106.605 147.695 107.687 145.735 105.997L145.388 105.696C145.298 105.619 145.178 105.595 145.065 105.618C144.948 105.652 144.859 105.737 144.819 105.842L143.076 110.818C143.025 110.948 143.061 111.096 143.158 111.19L143.595 111.618C147.292 115.235 150.813 113.369 152.72 111.766L154.322 115.598C154.365 115.712 154.467 115.794 154.597 115.816C154.663 115.831 154.728 115.816 154.781 115.796C154.834 115.776 154.883 115.747 154.917 115.704L154.922 115.691ZM152.619 110.898L152.484 111.02C150.461 112.819 147.364 114.291 144.103 111.102L143.828 110.834L145.328 106.565C147.654 108.499 150.575 107.305 152.326 105.348C155.109 102.243 155.961 98.2312 154.566 94.8846C154.072 93.7028 153.375 92.9912 152.486 92.7762C151.237 92.4663 149.617 93.1342 147.556 94.8363C145.434 96.6124 143.196 98.3012 141.252 97.8336C139.849 97.4917 138.628 96.0433 137.53 93.4194C134.573 86.3671 138.091 80.2613 139.241 78.5535L139.359 78.3877C139.424 78.2926 139.445 78.1637 139.392 78.0528L137.838 74.3438L140.118 71.7926L141.538 75.1799C141.579 75.2853 141.669 75.3619 141.777 75.3811C141.888 75.409 142.001 75.3864 142.087 75.3231L142.22 75.2221C144.652 73.4089 146.631 73.3897 148.65 75.1285L148.936 75.3723L147.616 79.4516C145.929 78.5531 143.616 79.2029 141.962 81.0425C139.315 84.0053 138.518 87.4219 139.785 90.4346C140.265 91.5813 140.879 92.2339 141.66 92.4297C143.02 92.7675 144.597 91.5923 146.223 90.2572C148.409 88.4873 150.911 86.6472 153.056 87.1896C154.561 87.5629 155.841 89.0594 156.962 91.7448C159.494 97.7911 158.019 103.964 155.436 107.407L155.309 107.576C155.236 107.674 155.218 107.812 155.262 107.926L157.066 112.235L154.787 114.786L153.212 111.024C153.172 110.919 153.07 110.837 152.962 110.818C152.895 110.803 152.818 110.812 152.757 110.835C152.713 110.852 152.677 110.866 152.644 110.909L152.619 110.898Z" fill="#363636"/>
<path opacity="0.2" d="M102.185 218.156L140.35 196.869C143.312 195.119 143 191.15 140.596 189.767L135.258 186.691C132.844 185.299 128.939 185.299 126.535 186.691L86.3795 209.05C83.9653 210.442 83.9653 212.691 86.3795 214.073L93.4618 218.146C95.8761 219.539 99.7803 219.539 102.185 218.146V218.156Z" fill="#838383"/>
<path opacity="0.2" d="M148.469 164.699L125.232 151.746C123.431 150.683 123.619 148.266 125.081 147.419L128.335 145.547C129.806 144.7 132.182 144.7 133.644 145.547L158.088 159.158C159.559 160.005 159.559 161.369 158.088 162.215L153.778 164.699C152.307 165.545 149.931 165.545 148.469 164.699Z" fill="#838383"/>
<path d="M112.19 200.321L99.7319 200.218C90.5654 199.371 82.0591 191.921 76.3725 179.768C65.3105 156.167 67.4607 121.372 81.1538 102.202C87.576 93.2188 95.6957 88.873 103.995 89.9641H112.378V90.9142H103.938C95.9314 89.8513 88.1324 94.056 81.9271 102.748C68.4037 121.664 66.3007 156.045 77.2307 179.373C82.7758 191.216 90.9993 198.468 99.7885 199.277L112.209 199.38V200.321H112.19Z" fill="#363636"/>
<path d="M110.284 200.321C109.709 200.321 109.134 200.293 108.558 200.246C99.4202 199.409 90.9139 191.959 85.2084 179.796C74.1464 156.186 76.2966 121.391 89.9897 102.23C96.195 93.5386 104.041 89.1646 112.067 89.8889C121.205 90.726 129.711 98.176 135.417 110.339C146.479 133.949 144.328 168.743 130.635 187.905C124.873 195.966 117.706 200.321 110.284 200.321ZM90.763 102.766C77.2491 121.683 75.1366 156.054 86.0666 179.392C91.6212 191.244 99.8446 198.506 108.643 199.305C116.32 200.001 123.864 195.759 129.871 187.359C143.385 168.442 145.498 134.071 134.568 110.734C129.013 98.8815 120.79 91.6197 111.991 90.8201C104.305 90.124 96.7703 94.3663 90.763 102.766ZM110.294 193.379C109.784 193.379 109.285 193.36 108.775 193.313C100.769 192.58 93.3281 186.061 88.3394 175.422C78.6731 154.784 80.5498 124.373 92.5171 107.62C97.9491 100.01 104.824 96.1724 111.859 96.8214C119.866 97.5551 127.316 104.074 132.295 114.713C141.961 135.35 140.085 165.762 128.108 182.515C123.063 189.569 116.782 193.389 110.284 193.389L110.294 193.379ZM110.35 97.6868C104.164 97.6868 98.1471 101.355 93.2904 108.166C81.4928 124.665 79.6539 154.662 89.1976 175.027C94.0354 185.355 101.203 191.686 108.87 192.382C115.556 192.993 122.12 189.297 127.353 181.969C139.151 165.461 140.99 135.473 131.446 115.117C126.608 104.789 119.441 98.4582 111.774 97.7621C111.303 97.7151 110.822 97.6962 110.35 97.6962V97.6868Z" fill="#363636"/>
<path d="M85.2082 179.787C74.1462 156.176 76.2963 121.382 89.9895 102.221C94.0823 96.5017 98.873 92.6638 103.965 90.9048H103.937C95.9307 89.8513 88.1316 94.0654 81.9264 102.748C68.403 121.664 66.3 156.045 77.23 179.373C82.7751 191.216 90.9985 198.468 99.7878 199.277L104.23 199.315C96.77 196.869 90.0083 190.002 85.2176 179.787H85.2082Z" fill="#838383"/>
<path d="M131.437 115.108C126.599 104.779 119.432 98.4488 111.765 97.7527C111.294 97.7057 110.813 97.6869 110.341 97.6869C104.155 97.6869 98.1381 101.355 93.2814 108.166C81.4838 124.665 79.6448 154.662 89.1885 175.027C94.0264 185.355 101.194 191.686 108.861 192.382C115.547 192.993 122.11 189.297 127.344 181.969C139.142 165.461 140.981 135.473 131.437 115.117V115.108Z" fill="#F0F0F0"/>
<path d="M134.559 110.734C129.09 99.0601 121.017 91.8547 112.369 90.8765V90.9235H107.965C101.704 91.7513 95.7057 95.8431 90.7547 102.776C77.2407 121.692 75.1283 156.063 86.0583 179.401C91.6129 191.253 99.8363 198.515 108.635 199.315C109.031 199.352 109.418 199.362 109.804 199.371H110.37C117.443 199.333 124.299 195.129 129.854 187.368C143.368 168.452 145.48 134.08 134.55 110.743L134.559 110.734ZM128.109 182.505C123.064 189.56 116.783 193.379 110.285 193.379C109.776 193.379 109.276 193.36 108.767 193.313C100.76 192.579 93.3198 186.061 88.331 175.422C78.6647 154.784 80.5414 124.373 92.5088 107.62C97.9407 100.01 104.816 96.1723 111.851 96.8214C119.857 97.5551 127.307 104.074 132.287 114.713C141.953 135.35 140.076 165.762 128.1 182.515L128.109 182.505Z" fill="#DCDDDF"/>
<path d="M108.513 176.034L108.324 169.91L108.098 169.995C105.212 171.095 99.9406 172.017 97.1303 165.922L96.7908 165.188L101.299 159.883L101.581 160.41C103.364 163.692 107.287 163.58 110.182 162.084C114.916 159.648 117.774 155.274 117.632 150.683C117.519 146.902 114.982 145.98 109.645 147.767C103.09 150.015 97.6395 151.059 97.3283 140.882C97.0171 130.582 104.392 124.58 106.665 122.981L106.881 122.831L106.712 117.243L111.653 114.694L111.823 120.272L112.04 120.216C116.086 119.068 118.792 120.065 120.546 123.348L120.923 124.053L116.812 129.142L116.51 128.775C115.011 126.979 112.049 126.715 109.305 128.126C104.807 130.44 102.308 134.128 102.43 138.238C102.562 142.678 105.721 141.982 109.833 140.609C116.585 138.398 122.668 137.298 122.989 147.908C123.262 156.732 118.406 163.796 113.483 166.881L113.247 167.032L113.445 173.485L108.503 176.034H108.513Z" fill="<<-CorPadrao->>"/>
<path d="M108.512 176.504C108.427 176.504 108.343 176.485 108.277 176.438C108.135 176.353 108.05 176.212 108.041 176.043L107.871 170.578C104.759 171.66 99.5438 172.299 96.6958 166.119L96.3563 165.385C96.2809 165.216 96.3092 165.028 96.4223 164.887L100.921 159.582C101.024 159.459 101.175 159.403 101.326 159.422C101.477 159.441 101.619 159.525 101.694 159.666L101.977 160.193C103.608 163.194 107.249 163.062 109.955 161.67C114.529 159.318 117.283 155.114 117.151 150.702C117.113 149.31 116.726 148.388 115.991 147.871C114.868 147.09 112.784 147.203 109.785 148.209C106.107 149.479 102.298 150.57 99.7607 148.821C97.9312 147.56 96.9787 144.964 96.8561 140.891C96.5355 130.356 104.08 124.223 106.39 122.595C106.4 122.586 106.24 117.252 106.24 117.252C106.24 117.074 106.334 116.904 106.494 116.82L111.436 114.271C111.577 114.195 111.747 114.205 111.888 114.28C112.03 114.365 112.115 114.506 112.124 114.675L112.275 119.661C116.321 118.626 119.159 119.764 120.961 123.122L121.338 123.828C121.423 123.997 121.404 124.194 121.291 124.345L117.188 129.434C117.103 129.547 116.962 129.613 116.821 129.613C116.679 129.613 116.547 129.547 116.453 129.443L116.151 129.076C114.793 127.458 112.077 127.233 109.531 128.55C105.202 130.779 102.788 134.306 102.911 138.229C102.948 139.555 103.269 140.402 103.872 140.825C105.023 141.625 107.381 140.947 109.691 140.176C113.435 138.953 117.66 137.806 120.404 139.725C122.337 141.079 123.337 143.76 123.469 147.908C123.743 156.91 118.782 164.134 113.737 167.295L113.916 173.475C113.916 173.654 113.822 173.823 113.661 173.908L108.72 176.457C108.654 176.495 108.578 176.513 108.503 176.513L108.512 176.504ZM108.324 169.44C108.418 169.44 108.503 169.468 108.588 169.515C108.71 169.6 108.795 169.741 108.795 169.891L108.955 175.262L112.973 173.193L112.784 167.041C112.784 166.872 112.86 166.721 113.001 166.627L113.237 166.477C118.047 163.457 122.79 156.553 122.526 147.918C122.413 144.08 121.536 141.643 119.867 140.468C117.481 138.803 113.52 139.884 109.993 141.041C107.352 141.926 104.853 142.622 103.335 141.568C102.467 140.966 102.015 139.875 101.967 138.229C101.835 133.93 104.438 130.083 109.097 127.684C112.002 126.189 115.161 126.48 116.811 128.371L120.366 123.969L120.131 123.536C118.471 120.441 116.019 119.548 112.162 120.629L111.945 120.686C111.804 120.723 111.653 120.695 111.539 120.611C111.426 120.526 111.351 120.394 111.351 120.244L111.2 115.409L107.183 117.478L107.343 122.774C107.343 122.934 107.267 123.084 107.145 123.169L106.928 123.32C104.712 124.881 97.488 130.751 97.7898 140.816C97.9029 144.569 98.7234 146.921 100.289 148.002C102.458 149.498 106.023 148.463 109.474 147.278C112.822 146.159 115.123 146.083 116.528 147.062C117.509 147.748 118.037 148.952 118.094 150.636C118.235 155.415 115.283 159.949 110.389 162.469C107.315 164.05 103.146 164.153 101.194 160.682L97.3277 165.235L97.5446 165.696C100.063 171.161 104.58 170.794 107.918 169.534L108.144 169.449C108.201 169.43 108.258 169.421 108.314 169.421L108.324 169.44Z" fill="#363636"/>
</g>
<rect x="138.1" y="166.4" width="78.3644" height="60.8" rx="6" fill="#E7E7E7" fill-opacity="0.9"/>
<path d="M164.205 182.62C164.205 186.471 161.084 189.592 157.233 189.592C153.382 189.592 150.261 186.471 150.261 182.62C150.261 178.77 153.382 175.647 157.233 175.647C161.084 175.647 164.205 178.77 164.205 182.62Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M164.205 208.474C164.205 212.324 161.084 215.447 157.233 215.447C153.382 215.447 150.261 212.324 150.261 208.474C150.261 204.623 153.382 201.502 157.233 201.502C161.084 201.502 164.205 204.623 164.205 208.474Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<path d="M185.722 179.722H175.824C174.384 179.722 173.216 178.554 173.216 177.115C173.216 175.673 174.384 174.507 175.824 174.507H185.722C187.162 174.507 188.33 175.673 188.33 177.115C188.33 178.554 187.162 179.722 185.722 179.722Z" fill="#838383"/>
<path d="M195.621 190.735H175.824C174.383 190.735 173.216 189.567 173.216 188.127C173.216 186.686 174.383 185.519 175.824 185.519H195.621C197.061 185.519 198.229 186.686 198.229 188.127C198.229 189.567 197.061 190.735 195.621 190.735Z" fill="#838383"/>
<path d="M185.723 205.575H175.824C174.384 205.575 173.216 204.408 173.216 202.967C173.216 201.526 174.384 200.359 175.824 200.359H185.723C187.162 200.359 188.33 201.526 188.33 202.967C188.33 204.408 187.162 205.575 185.723 205.575Z" fill="#838383"/>
<path d="M195.621 216.587H175.824C174.383 216.587 173.216 215.421 173.216 213.979C173.216 212.54 174.383 211.372 175.824 211.372H195.621C197.061 211.372 198.229 212.54 198.229 213.979C198.229 215.421 197.061 216.587 195.621 216.587Z" fill="#838383"/>
<path d="M236.467 191.612C236.083 191.228 235.625 190.923 235.122 190.715C234.618 190.507 234.078 190.4 233.532 190.4C232.986 190.4 232.446 190.507 231.942 190.715C231.438 190.923 230.981 191.228 230.597 191.612L199.799 222.181L186.86 209.314C186.461 208.932 185.99 208.631 185.474 208.43C184.957 208.228 184.406 208.129 183.852 208.139C183.297 208.148 182.749 208.266 182.241 208.485C181.732 208.704 181.272 209.021 180.886 209.416C180.501 209.812 180.198 210.279 179.994 210.79C179.791 211.302 179.691 211.848 179.701 212.398C179.71 212.948 179.829 213.491 180.05 213.995C180.271 214.499 180.591 214.955 180.99 215.337L196.864 231.072C197.248 231.456 197.705 231.761 198.209 231.969C198.713 232.177 199.253 232.284 199.799 232.284C200.345 232.284 200.885 232.177 201.389 231.969C201.893 231.761 202.35 231.456 202.734 231.072L236.467 197.636C236.887 197.252 237.222 196.786 237.451 196.268C237.68 195.75 237.798 195.19 237.798 194.624C237.798 194.058 237.68 193.498 237.451 192.98C237.222 192.462 236.887 191.996 236.467 191.612Z" fill="<<-CorPadrao->>" stroke="#363636" stroke-width="0.5"/>
<defs>
<clipPath id="clip0_1_43289">
<rect width="108.8" height="168" fill="white" transform="translate(69.3 51.2)"/>
</clipPath>
</defs>
</svg>''';

heartGraphImage(context) {
  return heartGraphSvg.replaceAll('<<-CorPadrao->>', '#${Theme.of(context).primaryColor.toARGB32().toRadixString(16).substring(2, 8)}');
}

moedasConsultaImage(context) {
  return moedasConsultasSvg.replaceAll('<<-CorPadrao->>', '#${Theme.of(context).primaryColor.toARGB32().toRadixString(16).substring(2, 8)}');
}
