import 'dart:convert';
import 'dart:developer';
import 'dart:io';

class StringExtractor {
  final Map<String, dynamic> translations = {};
  final Map<String, String> jsonMapLocalize = {};
  Map<String, dynamic> loadTranslations() {
    final directory = Directory('assets/langs');
    try {
      final List<FileSystemEntity> files = directory.listSync();
      for (final file in files) {
        if (file is File && file.path.endsWith('.json')) {
          final String content = file.readAsStringSync();
          final String langCode = file.path.split('/').last.split('.').first;
          translations[langCode] = const JsonCodec().decode(content);
        }
      }
    } catch (e) {
      log('Erro ao carregar traduções: $e');
    }

    return translations;
  }

  bool stringExistsInTranslations(String text) {
    for (final langData in translations.values) {
      if (langData is Map && langData.containsValue(text)) {
        return true;
      }
    }
    return false;
  }

  extractStrings(String filePath) {
    loadTranslations();
    final List<String> foundStrings = [];

    try {
      // Lê o conteúdo do arquivo
      final String content = File(filePath).readAsStringSync();

      // Regex para encontrar strings entre aspas simples ou duplas
      final RegExp stringPattern = RegExp(
        r'''(?:\'([^\']*)\')|(\"([^\"]*)\")''',
        multiLine: true,
      );

      // Encontra todas as correspondências
      final matches = stringPattern.allMatches(content);

      // Adiciona as strings encontradas à lista
      for (final match in matches) {
        final String? string = match.group(1) ?? match.group(3);
        if (string != null && string.isNotEmpty && !stringExistsInTranslations(string)) {
          foundStrings.add(string);
        }
      }
    } catch (e) {
      log('Erro ao extrair strings: $e');
    }
    createJson(foundStrings);
  }

  void createJson(List<String> strings) {
    for (var i = 0; i < strings.length; i++) {
      jsonMapLocalize[createKey(strings[i])] = strings[i];
    }

    final jsonString = const JsonEncoder.withIndent('  ').convert(jsonMapLocalize);
    File('extracted_strings.json').writeAsStringSync(jsonString);
  }

  String createKey(String text) {
    // Remove acentos
    var withoutAccents = text
        .replaceAll(RegExp(r'[áàãâä]'), 'a')
        .replaceAll(RegExp(r'[éèêë]'), 'e')
        .replaceAll(RegExp(r'[íìîï]'), 'i')
        .replaceAll(RegExp(r'[óòõôö]'), 'o')
        .replaceAll(RegExp(r'[úùûü]'), 'u')
        .replaceAll(RegExp(r'[ýÿ]'), 'y')
        .replaceAll(RegExp(r'[ñ]'), 'n')
        .replaceAll(RegExp(r'[ç]'), 'c');

    // Substitui espaços por underscore e remove caracteres especiais
    var key = withoutAccents.toLowerCase().replaceAll(RegExp(r'\s+'), '_').replaceAll(RegExp(r'[^a-z0-9_]'), '');

    // Limita a 20 caracteres
    return key.length > 20 ? key.substring(0, 20) : key;
  }
}

// Exemplo de uso
void main() {
  final strings = StringExtractor().extractStrings('/Users/<USER>/Documents/projetos/pacto/apps/app_treinox/lib/screens/prescricaoDeTreino/treinoIA/TilePrescricaoIA.dart');

  log('Strings encontradas:');
  strings.forEach((string) => log('- $string'));
}
