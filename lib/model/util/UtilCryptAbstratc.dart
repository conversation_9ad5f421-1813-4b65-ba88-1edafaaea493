import 'dart:convert';
import 'dart:typed_data';
import 'package:app_treino/model/util/UtilCrypt.dart';
import 'package:app_treino/model/util/custom_padding.dart';
import 'package:pointycastle/export.dart';

abstract class CriptografiaPacto {
  Uint8List _createUint8ListFromString(String value) {
    return Uint8List.fromList(utf8.encode(value));
  }

  Uint8List _padKey(Uint8List key) {
    final keyLength = 16; // AES-128
    if (key.length > keyLength) {
      return key.sublist(0, keyLength);
    } else if (key.length < keyLength) {
      final paddedKey = Uint8List(keyLength);
      for (int i = 0; i < key.length; i++) {
        paddedKey[i] = key[i];
      }
      return paddedKey;
    }
    return key;
  }

  String encryptPacto(String plaintext, CryptType type, {bool usarWorkaround = true}) {
    final paddedKey = _padKey(_createUint8ListFromString(type.encrypterKey));
    final iv = Uint8List(16);

    final cipher = PaddedBlockCipherImpl(
      PKCS7Padding(),
      CBCBlockCipher(AESEngine()),
    )..init(
        true,
        PaddedBlockCipherParameters(
          ParametersWithIV(KeyParameter(paddedKey), iv),
          null,
        ),
      );

    final input = _createUint8ListFromString(plaintext);
    final output = cipher.process(input);
    var retorno = base64Encode(output);
    if (usarWorkaround) {
      retorno = retorno.replaceAll('+', '*');
    }
    retorno = retorno.replaceAll('/', '@');
    return retorno;
  }

  String decryptPacto(String ciphertext, CryptType type, {bool usarWorkaround = true}) {
    try {
      final paddedKey = _padKey(_createUint8ListFromString(type.encrypterKey));
      final iv = Uint8List(16);

      final cipher = PaddedBlockCipherImpl(
        PKCS5Padding(),
        CBCBlockCipher(AESEngine()),
      )..init(
          false,
          PaddedBlockCipherParameters(
            ParametersWithIV(KeyParameter(paddedKey), iv),
            null,
          ),
        );
      if (usarWorkaround) {
        ciphertext = ciphertext.replaceAll('*', '+');
        ciphertext = ciphertext.replaceAll('\n', '');
      }
      ciphertext = ciphertext.replaceAll('@', '/');

      final input = base64Decode(ciphertext);
      final output = cipher.process(input);
      return utf8.decode(output);
    } catch (e) {
      return ciphertext;
    }
  }
}
