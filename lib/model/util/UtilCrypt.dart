import 'dart:convert';
import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/jwtcrypt.dart';
import 'package:app_treino/model/util/UtilCryptAbstratc.dart';
import 'package:flutter/foundation.dart' hide Key;
import 'package:get_it/get_it.dart';
import 'package:jaguar_jwt/jaguar_jwt.dart';
import 'package:encrypt/encrypt.dart';
import 'package:dio/dio.dart';

// Extensão para o enum CryptType, adicionando métodos para obter as chaves JWT e de criptografia.
extension CryptTypeEx on CryptType {
  // Retorna a chave JWT com base no tipo de criptografia.
  String get jwtKey {
    switch (this) {
      case CryptType.FB:
        return 'lxeojxplailaxngio@\$41877X\$\$=';
      case CryptType.OTHER:
        return 'Jr00oXsMOognIuc7ve1Gj2e7WrBmcon7bZLcL-AiKd0';
    }
  }

  // Retorna a chave de criptografia com base no tipo de criptografia.
  String get encrypterKey {
    switch (this) {
      case CryptType.FB:
        return 'kS2uu8qQSX#&VZzqXMhD!#w2*g2fN3JP';
      case CryptType.OTHER:
        return 'icbvhmZoTFDP3TmhP3jKtain97ED21kWBwa7X0NxP7c=';
    }
  }

  // final encrypter = ;
  // final decrypted = encrypter.decryptBytes(encrypt.Encrypted(Uint8List.fromList(actualCipherText)), iv: ivSpec);
  // Retorna um objeto Encrypter configurado com a chave de criptografia e o modo de criptografia.
  Encrypter get encrypter {
    switch (this) {
      case CryptType.FB:
        return Encrypter(AES(Key.fromUtf8(this.encrypterKey), mode: AESMode.ecb));
      case CryptType.OTHER:
        final bytes = Uint8List(16);
        final chaveBytes = utf8.encode(this.encrypterKey);
        for (int i = 0; i < bytes.length && i < chaveBytes.length; i++) {
          bytes[i] = chaveBytes[i];
        }
        return Encrypter(AES(Key(bytes), mode: AESMode.cbc));
    }
  }
}

// Enum que define os tipos de criptografia.
enum CryptType { FB, OTHER }

// Classe UtilCrypt responsável por operações de criptografia e JWT.
class UtilCrypt extends CriptografiaPacto {
  // Garante que a classe UtilCrypt seja um Singleton, ou seja, só existe uma instância.
  static final UtilCrypt _singleton = UtilCrypt._internal();

  factory UtilCrypt() {
    return _singleton;
  }

  UtilCrypt._internal();

  Map<CryptType, String?> mTokenMap = {}; // Mapa para armazenar tokens JWT.
  final iv = IV.fromLength(16); // Vetor de inicialização para criptografia.

  // Criptografa os parâmetros da requisição Retrofit, incluindo a URL e o corpo da requisição.
  RequestOptions encryptPath(RequestOptions options) {
    if (options.path.contains('${ConfigURL.FIREBASE}')) {
      // Divide a URL para obter o último segmento (que será criptografado).
      String path = options.path.split('/').last;
      options.headers.addAll({'axpxp': 'f'});
      options.headers.addAll({'Authorization': 'Bearer ${UtilCrypt().generateJWT()}'});
      // Cria um novo mapa para os parâmetros da query.
      Map<String, dynamic> queryParameters = {};

      // Criptografa os parâmetros da query.
      for (final entry in options.queryParameters.entries) {
        queryParameters[Uri.encodeQueryComponent(encrypt(entry.key))] = entry.value.toString().isNotEmpty ? Uri.encodeQueryComponent(encrypt(entry.value)) : '';
      }

      // Criptografa o corpo da requisição se ele existir.
      if (kDebugMode) {
        options.headers['original'] = options.path;
        options.headers['originalQuerys'] = options.queryParameters;
      }
      if (options.data != null) {
        options.contentType = 'text/plain';
        (options.data as Map<String, dynamic>).removeWhere((key, value) => value == null);
        options.data = encrypt(options.data);
      }
      // Define os parâmetros da query criptografados.
      options.queryParameters = queryParameters;
      // Reconstrua a URL com o último segmento criptografado.
      String pathx = options.path.split('/').where((x) => x != path).join('/');
      pathx = '$pathx/${Uri.encodeQueryComponent(encrypt(path))}';
      options.path = pathx;
    } else if (options.headers['encrypted'] != null) {
      //   options.headers.addAll({'Authorization': 'Bearer ${UtilCrypt().generateJWT(type: CryptType.OTHER)}'});
      options.headers.addAll({'axpxpt': 'f'});
      options.headers.remove('encrypted');
      if (kDebugMode) {
        options.headers['original'] = options.path;
        options.headers['originalQuerys'] = options.queryParameters;
      }
      if (options.data != null) {
        options.contentType = 'text/plain';
        (options.data as Map<String, dynamic>).removeWhere((key, value) => value == null);
        options.data = encrypt(options.data, type: CryptType.OTHER);
      }
    }
    return options;
  }

  // Descriptografa um texto criptografado.
  T decrypt<T>(String text, {CryptType type = CryptType.FB, bool usarWorkaround = true}) {
    try {
      if (type == CryptType.OTHER) {
        var t = decryptPacto(text, type, usarWorkaround: true);
        if (T is Map) {
          return const JsonCodec().decode(t) as T;
        }
        return t as T;
      } else {
        if (usarWorkaround) {
          text = text.replaceAll('*', '+');
          text = text.replaceAll('\n', '');
        }
        text = text.replaceAll('@', '/');
        var t = type.encrypter.decrypt64(text, iv: iv);
        if (T is Map) {
          return const JsonCodec().decode(t) as T;
        }
        return t as T;
      }
    } catch (e) {
      return text as T;
    }
  }

  // Criptografa um objeto, String ou Map.
  String encrypt(crypt, {CryptType type = CryptType.FB, bool usarWorkaround = true}) {
    String textoCrypto = '';
    if (type == CryptType.OTHER) {
      if (crypt is Map<String, dynamic>) {
        crypt = const JsonCodec().encode(crypt);
      }
      return encryptPacto(crypt, type, usarWorkaround: usarWorkaround);
    }
    if (crypt is Map<String, dynamic>) {
      textoCrypto = type.encrypter.encrypt(const JsonCodec().encode(crypt)).base64;
    } else if (crypt is String) {
      textoCrypto = type.encrypter.encrypt(crypt).base64;
    } else if (crypt is Object) {
      textoCrypto = type.encrypter.encrypt(const JsonCodec().encode(crypt)).base64;
    } else {
      textoCrypto = '';
    }
    if (usarWorkaround) {
      textoCrypto = textoCrypto.replaceAll('+', '*');
    }
    textoCrypto = textoCrypto.replaceAll('/', '@');
    return textoCrypto;
  }

  // Verifica se o JWT é válido.
  bool validJWT({CryptType type = CryptType.FB}) {
    String currentToken = mTokenMap[type] ?? '';
    final JwtClaim decClaimSet;
    if (type == CryptType.FB) {
      decClaimSet = verifyJwtHS256Signature(decrypt(currentToken, type: type), type.jwtKey);
    } else {
      decClaimSet = verifyJwtHS256Signature(currentToken, type.jwtKey);
    }

    // Verifica se o usuário está autenticado e se o token é temporário.
    if ((GetIt.I.get<ControladorCliente>().mUsuarioAuth?.uid?.isNotEmpty ?? false) && decClaimSet['isTemporary'] == true) {
      return false;
    }
    // Verifica se o token ainda é válido.
    return decClaimSet.expiry!.isAfter(DateTime.now());
  }

  // // Gera um novo JWT (desabilitado por enquanto).
  // String gerarJWTNotSafe({CryptType type = CryptType.FB}) {
  //   var jwt = JwtCrypt.make();
  //   return issueJwtHS256(JwtClaim(otherClaims: {"userTeam": "jp"}, maxAge: const Duration(days: 1)), type.jwtKey);
  // }

  // Gera um novo JWT.
  String generateJWT({CryptType type = CryptType.FB}) {
    mTokenMap[type] ??= ''; // Inicializa o token no mapa se ele não existir.
    // Se o token já existir e for válido, retorna o token atual.
    if (mTokenMap[type]!.isNotEmpty && validJWT(type: type)) {
      return mTokenMap[type]!;
    }
    if (type == CryptType.FB) {
      // Cria um novo objeto JwtCrypt.
      var jwt = JwtCrypt.make();
      // Gera um novo JWT com a chave JWT e tempo de expiração configurado.
      mTokenMap[type] = encrypt(issueJwtHS256(JwtClaim(otherClaims: jwt.toJson(), maxAge: Duration(minutes: jwt.isTemporary ? 100 : 240)), type.jwtKey), type: type);
    } else {
//         "exp": 1719153508,
//   "content": "{\"p\":\"ZW,TREINO,OAMD,API\",\"d\":\"AppTreino\",\"i\":\"e6719eb96f4175f32fac6c7b1cf9cd55\"}"
// }
      var jwt = issueJwtHS256(
          JwtClaim(expiry: DateTime.now().add(const Duration(minutes: 90)), payload: {'P': 'ZW,TREINO,OAMD,API', 'd': 'AppTreino', 'i': 'e6719eb96f4175f32fac6c7b1cf9cd55'}), type.jwtKey);
      mTokenMap[type] = jwt;
    }
    return mTokenMap[type]!;
  }
}
