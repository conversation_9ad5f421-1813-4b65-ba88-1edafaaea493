import 'package:easy_localization/easy_localization.dart';

String localizedString(String string,{List<String>? args, num? plural, Map<String, String>? namedArgs})=> plural == null ? '$string'.tr(args: args, namedArgs: namedArgs):'$string'.plural(plural);

String capitalize(String? s) {
  s = (s ?? '').toLowerCase();
  if (s.isEmpty) return '';
  return s[0].toUpperCase() + s.substring(1);
}

String camelCase(String? s) {
  if (s == null) return '';
  s = s.toLowerCase();
  String retorno = '';
  var d = s.split(' ');
  for (final element in d) {
    retorno += capitalize(element) + ' ';
  }
  return retorno.trim();
}

String formarMaxChars(String? name, int size) {
  if (name?.isEmpty ?? true) {
    return '';
  } else {
    var add = 0;
    String? retorno = '';
    name?.split(' ').forEach((element) {
      if (add <= 1) {
        add++;
        retorno = retorno! + element + ' ';
      }
    });
    return (retorno?.isNotEmpty ?? false) ? retorno! : name!;
  }
}

String removeSpecialChars(String s) {
  return s;
}
