// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'Agendamento.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Agendamento _$AgendamentoFromJson(Map json) => Agendamento(
      id: json['id'] as num?,
      horaFinal: json['horaFinal'] as String?,
      data: json['data'] as String?,
      hora: json['hora'] as String?,
      status: json['status'] as String?,
      statusCor: json['statusCor'] as String?,
      nomeProfessor: json['nomeProfessor'] as String?,
      dataLong: json['dataLong'] as num?,
      nome: json['nome'] as String?,
    );

Map<String, dynamic> _$AgendamentoToJson(Agendamento instance) =>
    <String, dynamic>{
      'id': instance.id,
      'nome': instance.nome,
      'data': instance.data,
      'hora': instance.hora,
      'status': instance.status,
      'statusCor': instance.statusCor,
      'nomeProfessor': instance.nomeProfessor,
      'horaFinal': instance.horaFinal,
      'dataLong': instance.dataLong,
    };

NovoAgendamento _$NovoAgendamentoFromJson(Map json) => NovoAgendamento(
      id: json['id'] as num?,
      dia: json['dia'] as String?,
      horarioInicial: json['horarioInicial'] as String?,
      horarioFinal: json['horarioFinal'] as String?,
      status: json['status'] as String?,
      professor: json['professor'] == null
          ? null
          : ProfessorNovoAgendamento.fromJson(
              Map<String, dynamic>.from(json['professor'] as Map)),
      tipoAgendamento: json['tipoAgendamento'] == null
          ? null
          : TipoAgendamento.fromJson(
              Map<String, dynamic>.from(json['tipoAgendamento'] as Map)),
    )
      ..tipoEvento = json['tipoEvento'] as bool?
      ..horarioDisponibilidadeCod = json['horarioDisponibilidadeCod'] as num?;

Map<String, dynamic> _$NovoAgendamentoToJson(NovoAgendamento instance) =>
    <String, dynamic>{
      'id': instance.id,
      'dia': instance.dia,
      'horarioInicial': instance.horarioInicial,
      'horarioFinal': instance.horarioFinal,
      'status': instance.status,
      'professor': instance.professor?.toJson(),
      'tipoAgendamento': instance.tipoAgendamento?.toJson(),
      'tipoEvento': instance.tipoEvento,
      'horarioDisponibilidadeCod': instance.horarioDisponibilidadeCod,
    };

TipoAgendamento _$TipoAgendamentoFromJson(Map json) => TipoAgendamento(
      id: json['id'] as num?,
      nome: json['nome'] as String?,
      comportamentoEnum: json['comportamentoEnum'] as String?,
      comportamento: json['comportamento'] as String?,
      ativo: json['ativo'] as bool?,
      tipoDuracao: json['tipoDuracao'] as String?,
      corEnum: json['corEnum'] as String?,
      cor: json['cor'] as String?,
      numeroAgendamentos: json['numeroAgendamentos'] as num?,
      dias: json['dias'] as num?,
      intervaloMinimoCasoFalta: json['intervaloMinimoCasoFalta'] as num?,
      somenteCarteiraProfessor: json['somenteCarteiraProfessor'] as bool?,
      permitir_app: json['permitir_app'] as bool?,
    );

Map<String, dynamic> _$TipoAgendamentoToJson(TipoAgendamento instance) =>
    <String, dynamic>{
      'id': instance.id,
      'nome': instance.nome,
      'comportamentoEnum': instance.comportamentoEnum,
      'comportamento': instance.comportamento,
      'ativo': instance.ativo,
      'tipoDuracao': instance.tipoDuracao,
      'corEnum': instance.corEnum,
      'cor': instance.cor,
      'numeroAgendamentos': instance.numeroAgendamentos,
      'dias': instance.dias,
      'intervaloMinimoCasoFalta': instance.intervaloMinimoCasoFalta,
      'somenteCarteiraProfessor': instance.somenteCarteiraProfessor,
      'permitir_app': instance.permitir_app,
    };

ProfessorNovoAgendamento _$ProfessorNovoAgendamentoFromJson(Map json) =>
    ProfessorNovoAgendamento(
      nome: json['nome'] as String?,
      id: json['id'] as num?,
      imageURI: json['imageURI'] as String?,
    );

Map<String, dynamic> _$ProfessorNovoAgendamentoToJson(
        ProfessorNovoAgendamento instance) =>
    <String, dynamic>{
      'id': instance.id,
      'nome': instance.nome,
      'imageURI': instance.imageURI,
    };

HorarioReagendamento _$HorarioReagendamentoFromJson(Map json) =>
    HorarioReagendamento(
      dia: json['dia'] as String?,
      horarios: (json['horarios'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$HorarioReagendamentoToJson(
        HorarioReagendamento instance) =>
    <String, dynamic>{
      'horarios': instance.horarios,
      'dia': instance.dia,
    };

PersitenciaDeAgendamento _$PersitenciaDeAgendamentoFromJson(Map json) =>
    PersitenciaDeAgendamento(
      dia: json['dia'] as String?,
      empresa: json['empresa'] as num?,
      horarioFinal: json['horarioFinal'] as String?,
      horarioInicial: json['horarioInicial'] as String?,
      matricula: json['matricula'] as String?,
      professorId: json['professorId'] as num?,
      status: json['status'] as String?,
      tipoAgendamentoId: json['tipoAgendamentoId'] as num?,
      horarioDisponibilidadeCod: json['horarioDisponibilidadeCod'] as num?,
      tipoEvento: json['tipoEvento'] as bool?,
    );

Map<String, dynamic> _$PersitenciaDeAgendamentoToJson(
        PersitenciaDeAgendamento instance) =>
    <String, dynamic>{
      'dia': instance.dia,
      'empresa': instance.empresa,
      'horarioFinal': instance.horarioFinal,
      'horarioInicial': instance.horarioInicial,
      'matricula': instance.matricula,
      'professorId': instance.professorId,
      'status': instance.status,
      'tipoAgendamentoId': instance.tipoAgendamentoId,
      'horarioDisponibilidadeCod': instance.horarioDisponibilidadeCod,
      'tipoEvento': instance.tipoEvento,
    };
