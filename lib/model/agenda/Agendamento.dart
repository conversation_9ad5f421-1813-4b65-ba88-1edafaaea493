import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:get_it/get_it.dart';
import 'package:json_annotation/json_annotation.dart';
part 'Agendamento.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class Agendamento {
  factory Agendamento.fromJson(Map<String, dynamic> json) => _$AgendamentoFromJson(json);
  Map<String, dynamic> toJson() => _$AgendamentoToJson(this);
  Agendamento clone() => _$AgendamentoFromJson(this.toJson());

  Agendamento({this.id,this.horaFinal, this.data, this.hora, this.status, this.statusCor, this.nomeProfessor, this.dataLong, this.nome});

  num? id;
  String? nome;
  String? data;
  String? hora;
  String? status;
  String? statusCor;
  String? nomeProfessor;
  String? horaFinal;
  num? dataLong;
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class NovoAgendamento {
  factory NovoAgendamento.fromJson(Map<String, dynamic> json) => _$NovoAgendamentoFromJson(json);
  Map<String, dynamic> toJson() => _$NovoAgendamentoToJson(this);
  Agendamento clone() => _$AgendamentoFromJson(this.toJson());
  num? id;
  String? dia;
  String? horarioInicial;
  String? horarioFinal;
  String? status;
  ProfessorNovoAgendamento? professor;
  TipoAgendamento? tipoAgendamento;
  bool? tipoEvento;
  num? horarioDisponibilidadeCod;
  NovoAgendamento({this.id, this.dia, this.horarioInicial, this.horarioFinal, this.status, this.professor, this.tipoAgendamento});
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class TipoAgendamento {
  factory TipoAgendamento.fromJson(Map<String, dynamic> json) => _$TipoAgendamentoFromJson(json);
  Map<String, dynamic> toJson() => _$TipoAgendamentoToJson(this);
  TipoAgendamento clone() => _$TipoAgendamentoFromJson(this.toJson());

  num? id;
  String? nome;
  String? comportamentoEnum;
  String? comportamento;
  bool? ativo;
  String? tipoDuracao;
  String? corEnum;
  String? cor;
  num? numeroAgendamentos;
  num? dias;
  num? intervaloMinimoCasoFalta;
  bool? somenteCarteiraProfessor;
  bool? permitir_app;
  TipoAgendamento(
      {this.id,
      this.nome,
      this.comportamentoEnum,
      this.comportamento,
      this.ativo,
      this.tipoDuracao,
      this.corEnum,
      this.cor,
      this.numeroAgendamentos,
      this.dias,
      this.intervaloMinimoCasoFalta,
      this.somenteCarteiraProfessor,
      this.permitir_app,
  });
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class ProfessorNovoAgendamento {
  factory ProfessorNovoAgendamento.fromJson(Map<String, dynamic> json) => _$ProfessorNovoAgendamentoFromJson(json);
  Map<String, dynamic> toJson() => _$ProfessorNovoAgendamentoToJson(this);
  ProfessorNovoAgendamento clone() => _$ProfessorNovoAgendamentoFromJson(this.toJson());

  num? id;
  String? nome;
  String? imageURI;
  ProfessorNovoAgendamento({this.nome, this.id, this.imageURI});
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class HorarioReagendamento {
  factory HorarioReagendamento.fromJson(Map<String, dynamic> json) => _$HorarioReagendamentoFromJson(json);
  Map<String, dynamic> toJson() => _$HorarioReagendamentoToJson(this);
  HorarioReagendamento clone() => _$HorarioReagendamentoFromJson(this.toJson());

  List<String>? horarios;
  String? dia;
  HorarioReagendamento({this.dia, this.horarios});
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class PersitenciaDeAgendamento {
  factory PersitenciaDeAgendamento.fromJson(Map<String, dynamic> json) => _$PersitenciaDeAgendamentoFromJson(json);
  Map<String, dynamic> toJson() => _$PersitenciaDeAgendamentoToJson(this);
  PersitenciaDeAgendamento clone() => _$PersitenciaDeAgendamentoFromJson(this.toJson());

  static PersitenciaDeAgendamento apartirDeNovoAgendamento(NovoAgendamento agendamento, String status) {
    final cliente = GetIt.I.get<ControladorCliente>();
    return PersitenciaDeAgendamento(
        dia: agendamento.dia,
        status: status,
        professorId: agendamento.professor!.id,
        tipoAgendamentoId: agendamento.tipoAgendamento!.id,
        matricula: cliente.mUsuarioLogado!.matricula,
        empresa: cliente.mUsuarioLogado!.codEmpresa,
        horarioInicial: agendamento.horarioInicial,
        horarioFinal: agendamento.horarioFinal,
        tipoEvento: agendamento.tipoEvento ?? false,
        horarioDisponibilidadeCod: agendamento.horarioDisponibilidadeCod
        );
  }

  PersitenciaDeAgendamento({
    this.dia,
    this.empresa,
    this.horarioFinal,
    this.horarioInicial,
    this.matricula,
    this.professorId,
    this.status,
    this.tipoAgendamentoId,
    this.horarioDisponibilidadeCod,
    this.tipoEvento
  });

  String? dia;
  num? empresa;
  String? horarioFinal;
  String? horarioInicial;
  String? matricula;
  num? professorId;
  String? status;
  num? tipoAgendamentoId;
  num? horarioDisponibilidadeCod;
  bool? tipoEvento;
}
