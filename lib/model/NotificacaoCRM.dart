import 'package:json_annotation/json_annotation.dart';
part 'NotificacaoCRM.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class NotificacaoCRM {
  List<NotificacaoCRMAcademia>? academia;
  List<NotificacaoCRMAcademia>? professor;
  NotificacaoCRM({this.academia, this.professor});
  factory NotificacaoCRM.fromJson(Map<String, dynamic> json) => _$NotificacaoCRMFromJson(json);
  Map<String, dynamic> toJson() => _$NotificacaoCRMToJson(this);
  NotificacaoCRM clone() => _$NotificacaoCRMFromJson(this.toJson());
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class NotificacaoCRMAcademia {
  num? cod;
  String? tipo;
  num? codProfessor;
  String? data;
  num? dataLong;
  String? titulo;
  String? texto;
  num? codCliente;
  String? telAluno;
  String? gravidadeCor;
  String? opcoes;
  String? resposta;
  bool? lida;
  bool? pushEnviado;
  bool? excluida;
  bool? eNotificaoProfessor;
  bool isPush;
  NotificacaoCRMAcademia(
      {this.cod,
      this.tipo,
      this.codProfessor,
      this.data,
      this.dataLong,
      this.titulo,
      this.texto,
      this.codCliente,
      this.telAluno,
      this.gravidadeCor,
      this.opcoes,
      this.resposta,
      this.lida,
      this.pushEnviado,
      this.excluida,
      this.isPush = false,
      this.eNotificaoProfessor});

  factory NotificacaoCRMAcademia.fromJson(Map<String, dynamic> json) => _$NotificacaoCRMAcademiaFromJson(json);
  Map<String, dynamic> toJson() => _$NotificacaoCRMAcademiaToJson(this);
  NotificacaoCRMAcademia clone() => _$NotificacaoCRMAcademiaFromJson(this.toJson());
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class PushUsuario {

  String? admId;
  String? cliente;
  DateTime? dataCriada;
  bool? excluir;
  List<String>? horarios;
  String? userNameUsuario;
  String? chave;
  String? titulo;
  String? content;
  String? refPush;
  bool? enviada;
  bool? lida;
  String? ref;

  PushUsuario({
    this.admId,
    this.cliente,
    this.dataCriada,
    this.excluir,
    this.horarios,
    this.userNameUsuario,
    this.chave,
    this.titulo,
    this.content,
    this.refPush,
    this.enviada,
    this.lida,
    this.ref,
  });

  factory PushUsuario.fromJson(Map<String, dynamic> json) => _$PushUsuarioFromJson(json);
  Map<String, dynamic> toJson() => _$PushUsuarioToJson(this);
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class ListPush {

  List<String>? ids;

  ListPush({
    this.ids,
  });

  factory ListPush.fromJson(Map<String, dynamic> json) => _$ListPushFromJson(json);
  Map<String, dynamic> toJson() => _$ListPushToJson(this);
}
