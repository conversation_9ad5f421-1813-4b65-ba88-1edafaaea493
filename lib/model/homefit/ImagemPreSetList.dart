import 'dart:collection';

class ImagensPreTreinoSet {
  LinkedHashMap<String, String> imagens = LinkedHashMap();
  //antigamente esse map apontava de uma path local nos assets da imagem para uma url do firebase contendo a mesma imagem do path local
  //ao realizar uma atividade que troca imagens estaticas do firebase para imagens locais o map foi mantido porém apontando de path para path, para evitar erros em outras partes do codigo .

  ImagensPreTreinoSet() {
    imagens['assets/images/treinoDefault/1.webp'] =
        'assets/images/treinoDefault/1.webp';
    imagens['assets/images/treinoDefault/2.webp'] =
        'assets/images/treinoDefault/2.webp';
    imagens['assets/images/treinoDefault/3.webp'] =
        'assets/images/treinoDefault/3.webp';
    imagens['assets/images/treinoDefault/4.webp'] =
        'assets/images/treinoDefault/4.webp';
    imagens['assets/images/treinoDefault/5.webp'] =
        'assets/images/treinoDefault/5.webp';
    imagens['assets/images/treinoDefault/6.webp'] =
        'assets/images/treinoDefault/6.webp';
    imagens['assets/images/treinoDefault/7.webp'] =
        'assets/images/treinoDefault/7.webp';
    imagens['assets/images/treinoDefault/8.webp'] =
        'assets/images/treinoDefault/8.webp';
    imagens['assets/images/treinoDefault/9.webp'] =
        'assets/images/treinoDefault/9.webp';
    imagens['assets/images/treinoDefault/10.webp'] =
        'assets/images/treinoDefault/10.webp';
    imagens['assets/images/treinoDefault/11.webp'] =
        'assets/images/treinoDefault/11.webp';
    imagens['assets/images/treinoDefault/12.webp'] =
        'assets/images/treinoDefault/12.webp';
    imagens['assets/images/treinoDefault/13.webp'] =
        'assets/images/treinoDefault/13.webp';
    imagens['assets/images/treinoDefault/18.webp'] =
        'assets/images/treinoDefault/18.webp';
    imagens['assets/images/treinoDefault/19.webp'] =
        'assets/images/treinoDefault/19.webp';
    imagens['assets/images/treinoDefault/20.webp'] =
        'assets/images/treinoDefault/20.webp';
    imagens['assets/images/treinoDefault/21.webp'] =
        'assets/images/treinoDefault/21.webp';
    imagens['assets/images/treinoDefault/22.webp'] =
        'assets/images/treinoDefault/22.webp';
    imagens['assets/images/treinoDefault/23.webp'] =
        'assets/images/treinoDefault/23.webp';
    imagens['assets/images/treinoDefault/24.webp'] =
        'assets/images/treinoDefault/24.webp';
    imagens['assets/images/treinoDefault/25.webp'] =
        'assets/images/treinoDefault/25.webp';
  }

  // Getter retorna o mapa já inicializado
  LinkedHashMap<String, String> get listaImagensShow => imagens;
}