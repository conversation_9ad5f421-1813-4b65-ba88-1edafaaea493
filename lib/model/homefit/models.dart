import 'dart:convert';

class TreinoSimples {
  List<Treino>? sucesso;

  TreinoSimples({this.sucesso});

  TreinoSimples.fromJson(Map<String, dynamic> json) {
    if (json['sucesso'] != null) {
      sucesso = [];
      json['sucesso'].forEach((v) {
        sucesso!.add(new Treino.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.sucesso != null) {
      data['sucesso'] = this.sucesso!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Treino {
  String? nomeDoTreino;
  String? descricao;
  String? documentKey;
  List<Atividades>? atividades;
  String? clienteApp;
  int? dataCricao;
  int? ultimaExecucao;
  String? nivel;
  String? refUsuarioApp;
  String? nomeProfessor;
  String? tipoColaboradorZW;
  String? fotoProfessor;
  bool? podeEditar;
  List<Media>? media;
  List<Objetivo>? objetivo;
  List<Objetivo>? dificuldade;
  List<Objetivo>? grupoMuscular;
  List<Objetivo>? equipamentos;
  List<Objetivo>? tags;

  Treino(
      {this.nomeDoTreino,
      this.descricao,
      this.tipoColaboradorZW,
      this.documentKey,
      this.atividades,
      this.clienteApp,
      this.dataCricao,
      this.ultimaExecucao,
      this.nivel,
      this.refUsuarioApp,
      this.nomeProfessor,
      this.fotoProfessor,
      this.podeEditar,
      this.media,
      this.objetivo,
      this.dificuldade,
      this.grupoMuscular,
      this.equipamentos,
      this.tags});

  Treino.fromJson(Map<String, dynamic> json) {
    nomeDoTreino = json['nomeDoTreino'];
    descricao = json['descricao'] ?? '';
    documentKey = json['documentKey'];
    if (json['atividades'] != null) {
      atividades = [];
      json['atividades'].forEach((v) {
        atividades!.add(new Atividades.fromJson(v));
      });
    }
    clienteApp = json['clienteApp'];
    dataCricao = json['dataCricao'];
    ultimaExecucao = json['ultimaExecucao'];
    nivel = json['nivel'];
    refUsuarioApp = json['refUsuarioApp'];
    nomeProfessor = json['nomeProfessor'];
    fotoProfessor = json['fotoProfessor'];
    podeEditar = json['podeEditar'];
    if (json['media'] != null) {
      media = [];
      json['media'].forEach((v) {
        media!.add(new Media.fromJson(v));
      });
    }
    if (json['objetivo'] != null) {
      objetivo = [];
      json['objetivo'].forEach((v) {
        objetivo!.add(new Objetivo.fromJson(v));
      });
    }
    if (json['dificuldade'] != null) {
      dificuldade = [];
      json['dificuldade'].forEach((v) {
        dificuldade!.add(new Objetivo.fromJson(v));
      });
    }
    if (json['grupoMuscular'] != null) {
      grupoMuscular = [];
      json['grupoMuscular'].forEach((v) {
        grupoMuscular!.add(new Objetivo.fromJson(v));
      });
    }
    if (json['equipamentos'] != null) {
      equipamentos = [];
      json['equipamentos'].forEach((v) {
        equipamentos!.add(new Objetivo.fromJson(v));
      });
    }
    if (json['tags'] != null) {
      tags = [];
      json['tags'].forEach((v) {
        tags!.add(new Objetivo.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['nomeDoTreino'] = this.nomeDoTreino;
    data['descricao'] = this.descricao;
    data['documentKey'] = this.documentKey;
    if (this.atividades != null) {
      data['atividades'] = this.atividades!.map((v) => v.toJson()).toList();
    }
    data['clienteApp'] = this.clienteApp;
    data['dataCricao'] = this.dataCricao;
    data['ultimaExecucao'] = this.ultimaExecucao;
    data['nivel'] = this.nivel;
    data['refUsuarioApp'] = this.refUsuarioApp;
    data['nomeProfessor'] = this.nomeProfessor;
    data['fotoProfessor'] = this.fotoProfessor;
    if (this.descricao != null) data['descricao'] = this.descricao;
    data['podeEditar'] = this.podeEditar ?? false;
    if (this.media != null) {
      data['media'] = this.media!.map((v) => v.toJson()).toList();
    }
    if (this.objetivo != null) {
      data['objetivo'] = this.objetivo!.map((v) => v.toJson()).toList();
    }
    if (this.grupoMuscular != null) {
      data['grupoMuscular'] = this.grupoMuscular!.map((v) => v.toJson()).toList();
    }
    if (this.dificuldade != null) {
      data['dificuldade'] = this.dificuldade!.map((v) => v.toJson()).toList();
    }
    if (this.equipamentos != null) {
      data['equipamentos'] = this.equipamentos!.map((v) => v.toJson()).toList();
    }
    if (this.tags != null) {
      data['tags'] = this.tags!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Media {
  String? tipo;
  String? url;
  String? duracao;

  Media({this.tipo, this.url, this.duracao});

  Media.fromJson(Map<String, dynamic> json) {
    tipo = json['tipo'];
    url = json['url'];
    duracao = json['duracao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.tipo != null) data['tipo'] = this.tipo;
    if (this.url != null) data['url'] = this.url;
    if (this.duracao != null) data['duracao'] = this.duracao;
    return data;
  }
}

class Objetivo {
  String? descricao;

  Objetivo({this.descricao});

  Objetivo.fromJson(Map<String, dynamic> json) {
    descricao = json['descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.descricao != null) data['descricao'] = this.descricao;
    return data;
  }
}

UsuarioApp usuarioAppFromJson(String str) => UsuarioApp.fromJson(const JsonCodec().decode(str)['sucesso']);

String usuarioAppToJson(UsuarioApp data) => const JsonCodec().encode(data.toJson());

class UsuarioApp {
  String? nome;
  String? iddocumento;
  String? urlFoto;

  UsuarioApp({
    this.nome,
    this.iddocumento,
    this.urlFoto,
  });

  factory UsuarioApp.fromJson(Map<String, dynamic> json) => UsuarioApp(
        nome: json['nome'],
        iddocumento: json['iddocumento'],
        urlFoto: json['urlFoto'],
      );

  Map<String, dynamic> toJson() => {
        'nome': nome,
        'iddocumento': iddocumento,
        'urlFoto': urlFoto,
      };
}

class Atividades {
  String? descricao;
  String? idYouTube;
  String? nome;
  String? duracao;
  int? orderm;

  Atividades({this.descricao, this.idYouTube, this.nome});

  Atividades.fromJson(Map<String, dynamic> json) {
    descricao = json['descricao'];
    idYouTube = json['idYouTube'];
    duracao = json['duracao'];
    nome = json['nome'];
    orderm = json['orderm'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.descricao != null) data['descricao'] = this.descricao;
    data['idYouTube'] = this.idYouTube;
    data['nome'] = this.nome;
    data['duracao'] = this.duracao;
    data['orderm'] = this.orderm;
    return data;
  }
}

RequestExecucaoTreino requestExecucaoTreinoFromJson(String str) => RequestExecucaoTreino.fromJson(json.decode(str));

String requestExecucaoTreinoToJson(RequestExecucaoTreino data) => json.encode(data.toJson());

class RequestExecucaoTreino {
  List<ExecucaoTreino>? sucesso;

  RequestExecucaoTreino({
    this.sucesso,
  });

  factory RequestExecucaoTreino.fromJson(Map<String, dynamic> json) => RequestExecucaoTreino(
        sucesso: List<ExecucaoTreino>.from(json['sucesso'].map((x) => ExecucaoTreino.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        'sucesso': List<dynamic>.from(sucesso!.map((x) => x.toJson())),
      };
}

class ExecucaoTreino {
  String? treinoKey;
  String? urlFoto;
  String? nome;
  int? dataExec;
  String? refUsuario;

  ExecucaoTreino({
    this.treinoKey,
    this.urlFoto,
    this.nome,
    this.dataExec,
    this.refUsuario,
  });

  factory ExecucaoTreino.fromJson(Map<String, dynamic> json) => ExecucaoTreino(
        treinoKey: json['treinoKey'],
        urlFoto: json['urlFoto'],
        nome: json['nome'],
        dataExec: json['dataExec'],
        refUsuario: json['refUsuario'],
      );

  Map<String, dynamic> toJson() => {
        'treinoKey': treinoKey,
        'urlFoto': urlFoto,
        'nome': nome,
        'dataExec': dataExec,
        'refUsuario': refUsuario,
      };
}

RankingInfo rankingInfoFromJson(String str) => RankingInfo.fromJson(json.decode(str));

String rankingInfoToJson(RankingInfo data) => json.encode(data.toJson());

// To parse this JSON data, do
//
//     final rankingInfo = rankingInfoFromJson(jsonString);

RankingInfoRequest rankingInfoRequestFromJson(String str) => RankingInfoRequest.fromJson(json.decode(str));

class RankingInfoRequest {
  List<RankingInfo>? sucesso;

  RankingInfoRequest({
    this.sucesso,
  });

  factory RankingInfoRequest.fromJson(Map<String, dynamic> json) => RankingInfoRequest(
        sucesso: List<RankingInfo>.from(json['sucesso'].map((x) => RankingInfo.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        'sucesso': List<dynamic>.from(sucesso!.map((x) => x.toJson())),
      };
}

class RankingInfo {
  Nivel? nivel;
  int? pontuacao;
  String? nome;
  String? urlFoto;
  String? refUsuario;
  int? metaTreinos;

  RankingInfo({this.nivel, this.pontuacao, this.nome, this.urlFoto, this.refUsuario, this.metaTreinos});

  factory RankingInfo.fromJson(Map<String, dynamic> json) => RankingInfo(
        nivel: nivelValues.map[json['nivel']],
        pontuacao: json['pontuacao'],
        nome: json['nome'],
        urlFoto: json['urlFoto'],
        refUsuario: json['refUsuario'],
        metaTreinos: json['metaTreinos'],
      );

  Map<String, dynamic> toJson() => {
        'nivel': nivelValues.reverse![nivel!],
        'pontuacao': pontuacao,
        'nome': nome,
        'urlFoto': urlFoto,
        'refUsuario': refUsuario,
        'metaTreinos': metaTreinos,
      };
}

enum Nivel { BRONZE, PRATA, OUTRO }

enum STATUSREQUEST { FALHA, SUCESSO, CARREGANDO, INVALIDO, SEM_DADOS, ALTERADO }

final nivelValues = EnumValues({'BRONZE': Nivel.BRONZE, 'PRATA': Nivel.PRATA, 'OUTRO': Nivel.OUTRO});

class EnumValues<T> {
  Map<String, T> map;
  Map<T, String>? reverseMap;

  EnumValues(this.map);

  Map<T, String>? get reverse {
    reverseMap ??= map.map((k, v) => new MapEntry(v, k));
    return reverseMap;
  }
}

class UploadImagem {
  String? sucesso;

  UploadImagem({this.sucesso});

  UploadImagem.fromJson(Map<String, dynamic> json) {
    sucesso = json['sucesso'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['sucesso'] = this.sucesso;
    return data;
  }
}

class RetornoFiltro {
  List<Filtro>? sucesso;

  RetornoFiltro({this.sucesso});

  RetornoFiltro.fromJson(Map<String, dynamic> json) {
    if (json['sucesso'] != null) {
      sucesso = [];
      json['sucesso'].forEach((v) {
        sucesso!.add(new Filtro.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.sucesso != null) {
      data['sucesso'] = this.sucesso!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ProdutoHomeFit {
  RetornoHomeFit? retorno;

  ProdutoHomeFit({this.retorno});

  ProdutoHomeFit.fromJson(Map<String, dynamic> json) {
    retorno = json['retorno'] != null ? new RetornoHomeFit.fromJson(json['retorno']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.retorno != null) {
      data['retorno'] = this.retorno!.toJson();
    }
    return data;
  }
}

class RetornoHomeFit {
  bool? alunoTemNoContrato;
  bool? empresaVendeHomeFit;

  RetornoHomeFit({this.alunoTemNoContrato, this.empresaVendeHomeFit});

  RetornoHomeFit.fromJson(Map<String, dynamic> json) {
    alunoTemNoContrato = json['alunoTemNoContrato'];
    empresaVendeHomeFit = json['empresaVendeHomeFit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['alunoTemNoContrato'] = this.alunoTemNoContrato;
    data['empresaVendeHomeFit'] = this.empresaVendeHomeFit;
    return data;
  }
}

class Filtro {
  String? nome;
  bool? ativo;
  bool? selecionado;
  String? refFiltro;

  Filtro({this.nome, this.ativo, this.selecionado, this.refFiltro});

  Filtro.fromJson(Map<String, dynamic> json) {
    nome = json['nome'];
    ativo = json['ativo'];
    selecionado = json['selecionado'];
    refFiltro = json['refFiltro'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['nome'] = this.nome;
    data['ativo'] = this.ativo;
    data['selecionado'] = this.selecionado;
    data['refFiltro'] = this.refFiltro;
    return data;
  }
}

class RetornoVideoAjuda {
  List<VideoAjuda>? sucesso;

  RetornoVideoAjuda({this.sucesso});

  RetornoVideoAjuda.fromJson(Map<String, dynamic> json) {
    if (json['sucesso'] != null) {
      sucesso = [];
      json['sucesso'].forEach((v) {
        sucesso!.add(new VideoAjuda.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.sucesso != null) {
      data['sucesso'] = this.sucesso!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class VideoAjuda {
  String? refVideo;
  String? nome;
  String? url;
  String? tela;
  String? refFiltro;

  VideoAjuda({this.refVideo, this.nome, this.url, this.tela, this.refFiltro});

  VideoAjuda.fromJson(Map<String, dynamic> json) {
    refVideo = json['refVideo'];
    nome = json['nome'];
    url = json['url'];
    tela = json['tela'];
    refFiltro = json['refFiltro'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['refVideo'] = this.refVideo;
    data['nome'] = this.nome;
    data['url'] = this.url;
    data['tela'] = this.tela;
    data['refFiltro'] = this.refFiltro;
    return data;
  }
}
