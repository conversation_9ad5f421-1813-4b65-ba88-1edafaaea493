// To parse this JSON data, do
//
//     final gdataApi = gdataApiFromJson(jsonString);

import 'dart:convert';

GdataApi gdataApiFromJson(String str) => GdataApi.fromJson(const JsonCodec().decode(str));

String gdataApiToJson(GdataApi data) => json.encode(data.toJson());

class GdataApi {
  String? kind;
  String? etag;
  PageInfo? pageInfo;
  List<Item>? items;

  GdataApi({
    this.kind,
    this.etag,
    this.pageInfo,
    this.items,
  });

  factory GdataApi.fromJson(Map<String, dynamic> json) => GdataApi(
        kind: json['kind'],
        etag: json['etag'],
        pageInfo: PageInfo.fromJson(json['pageInfo']),
        items: List<Item>.from(json['items'].map((x) => Item.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        'kind': kind,
        'etag': etag,
        'pageInfo': pageInfo!.toJson(),
        'items': List<dynamic>.from(items!.map((x) => x.toJson())),
      };
}

class Item {
  String? kind;
  String? etag;
  String? id;
  Snippet? snippet;

  Item({
    this.kind,
    this.etag,
    this.id,
    this.snippet,
  });

  factory Item.fromJson(Map<String, dynamic> json) => Item(
        kind: json['kind'],
        etag: json['etag'],
        id: json['id'],
        snippet: Snippet.fromJson(json['snippet']),
      );

  Map<String, dynamic> toJson() => {
        'kind': kind,
        'etag': etag,
        'id': id,
        'snippet': snippet!.toJson(),
      };
}

class Snippet {
  DateTime? publishedAt;
  String? channelId;
  String? title;
  String? description;
  Thumbnails? thumbnails;
  String? channelTitle;
  String? categoryId;
  String? liveBroadcastContent;
  Localized? localized;

  Snippet({
    this.publishedAt,
    this.channelId,
    this.title,
    this.description,
    this.thumbnails,
    this.channelTitle,
    this.categoryId,
    this.liveBroadcastContent,
    this.localized,
  });

  factory Snippet.fromJson(Map<String, dynamic> json) => Snippet(
        publishedAt: DateTime.parse(json['publishedAt']),
        channelId: json['channelId'],
        title: json['title'],
        description: json['description'],
        thumbnails: Thumbnails.fromJson(json['thumbnails']),
        channelTitle: json['channelTitle'],
        categoryId: json['categoryId'],
        liveBroadcastContent: json['liveBroadcastContent'],
        localized: Localized.fromJson(json['localized']),
      );

  Map<String, dynamic> toJson() => {
        'publishedAt': publishedAt!.toIso8601String(),
        'channelId': channelId,
        'title': title,
        'description': description,
        'thumbnails': thumbnails!.toJson(),
        'channelTitle': channelTitle,
        'categoryId': categoryId,
        'liveBroadcastContent': liveBroadcastContent,
        'localized': localized!.toJson(),
      };
}

class Localized {
  String? title;
  String? description;

  Localized({
    this.title,
    this.description,
  });

  factory Localized.fromJson(Map<String, dynamic> json) => Localized(
        title: json['title'],
        description: json['description'],
      );

  Map<String, dynamic> toJson() => {
        'title': title,
        'description': description,
      };
}

class Thumbnails {
  Default? thumbnailsDefault;
  Default? medium;
  Default? high;
  Default? standard;

  Thumbnails({
    this.thumbnailsDefault,
    this.medium,
    this.high,
    this.standard,
  });

  factory Thumbnails.fromJson(Map<String, dynamic> json) => Thumbnails(
        thumbnailsDefault: Default.fromJson(json['default']),
        medium: Default.fromJson(json['medium']),
        high: Default.fromJson(json['high']),
        standard: Default.fromJson(json['standard']),
      );

  Map<String, dynamic> toJson() => {
        'default': thumbnailsDefault!.toJson(),
        'medium': medium!.toJson(),
        'high': high!.toJson(),
        'standard': standard!.toJson(),
      };
}

class Default {
  String? url;
  int? width;
  int? height;

  Default({
    this.url,
    this.width,
    this.height,
  });

  factory Default.fromJson(Map<String, dynamic> json) => Default(
        url: json['url'],
        width: json['width'],
        height: json['height'],
      );

  Map<String, dynamic> toJson() => {
        'url': url,
        'width': width,
        'height': height,
      };
}

class PageInfo {
  int? totalResults;
  int? resultsPerPage;

  PageInfo({
    this.totalResults,
    this.resultsPerPage,
  });

  factory PageInfo.fromJson(Map<String, dynamic> json) => PageInfo(
        totalResults: json['totalResults'],
        resultsPerPage: json['resultsPerPage'],
      );

  Map<String, dynamic> toJson() => {
        'totalResults': totalResults,
        'resultsPerPage': resultsPerPage,
      };
}
