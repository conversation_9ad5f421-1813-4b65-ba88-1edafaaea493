// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'RefeicaoPlanner.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RefeicaoPlanner _$RefeicaoPlannerFromJson(Map json) => RefeicaoPlanner(
      nome: json['nome'] as String?,
      valorEnergetico: parseStringToNum(json['valorEnergetico']),
      tempoPreparo: parseStringToNum(json['tempoPreparo']),
      responsavelRef: json['responsavelRef'] as String?,
      dificuldade: json['dificuldade'] as String?,
      likes: (json['likes'] as List<dynamic>?)
          ?.map((e) => Like.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      proteina: parseStringToNum(json['proteina']),
      restricoes: (json['restricoes'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      gordura: parseStringToNum(json['gordura']),
      refClienteApp: json['refClienteApp'] as String?,
      docKey: json['docKey'] as String?,
      media: (json['media'] as List<dynamic>?)
          ?.map((e) => Media.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      modoPrepaparo: json['modoPrepaparo'] as String?,
      tipoRefeicao: (json['tipoRefeicao'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      deslikes: (json['deslikes'] as List<dynamic>?)
          ?.map((e) => Like.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      descricao: json['descricao'] as String?,
      notas: (json['notas'] as List<dynamic>?)
          ?.map((e) => Nota.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      ingredientes: (json['ingredientes'] as List<dynamic>?)
          ?.map(
              (e) => Ingrediente.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      objetivo: (json['objetivo'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      carboidratoLiquido: parseStringToNum(json['carboidratoLiquido']),
      docRef: json['docRef'] as String?,
    );

Map<String, dynamic> _$RefeicaoPlannerToJson(RefeicaoPlanner instance) =>
    <String, dynamic>{
      'nome': instance.nome,
      'valorEnergetico': instance.valorEnergetico,
      'tempoPreparo': instance.tempoPreparo,
      'responsavelRef': instance.responsavelRef,
      'dificuldade': instance.dificuldade,
      'likes': instance.likes?.map((e) => e.toJson()).toList(),
      'proteina': instance.proteina,
      'restricoes': instance.restricoes,
      'gordura': instance.gordura,
      'refClienteApp': instance.refClienteApp,
      'docKey': instance.docKey,
      'media': instance.media?.map((e) => e.toJson()).toList(),
      'modoPrepaparo': instance.modoPrepaparo,
      'tipoRefeicao': instance.tipoRefeicao,
      'deslikes': instance.deslikes?.map((e) => e.toJson()).toList(),
      'descricao': instance.descricao,
      'notas': instance.notas?.map((e) => e.toJson()).toList(),
      'ingredientes': instance.ingredientes?.map((e) => e.toJson()).toList(),
      'objetivo': instance.objetivo,
      'carboidratoLiquido': instance.carboidratoLiquido,
      'docRef': instance.docRef,
    };

Like _$LikeFromJson(Map json) => Like(
      tipoReacao: json['tipoReacao'] as String?,
      refUsuario: json['refUsuario'] as String?,
      dataRegistro: json['dataRegistro'] as String?,
      dataMilis: json['dataMilis'] as String?,
      nomeUsuario: json['nomeUsuario'] as String?,
      urlFotoUser: json['urlFotoUser'] as String?,
    );

Map<String, dynamic> _$LikeToJson(Like instance) => <String, dynamic>{
      'tipoReacao': instance.tipoReacao,
      'refUsuario': instance.refUsuario,
      'dataRegistro': instance.dataRegistro,
      'dataMilis': instance.dataMilis,
      'nomeUsuario': instance.nomeUsuario,
      'urlFotoUser': instance.urlFotoUser,
    };

Ingrediente _$IngredienteFromJson(Map json) => Ingrediente(
      quantidade: parseStringToNum(json['quantidade']),
      unidadeDeMedida: json['unidadeDeMedida'] as String?,
      nome: json['nome'] as String?,
    );

Map<String, dynamic> _$IngredienteToJson(Ingrediente instance) =>
    <String, dynamic>{
      'quantidade': instance.quantidade,
      'unidadeDeMedida': instance.unidadeDeMedida,
      'nome': instance.nome,
    };

Media _$MediaFromJson(Map json) => Media(
      autor: json['autor'],
      dataUpload: parseStringToNum(json['dataUpload']),
      url: json['url'] as String?,
      tipo: json['tipo'] as String?,
      posicao: parseStringToNum(json['posicao']),
    );

Map<String, dynamic> _$MediaToJson(Media instance) => <String, dynamic>{
      'autor': instance.autor,
      'dataUpload': instance.dataUpload,
      'url': instance.url,
      'tipo': instance.tipo,
      'posicao': instance.posicao,
    };

Nota _$NotaFromJson(Map json) => Nota(
      refUsuario: json['refUsuario'] as String?,
      nota: parseStringToNum(json['nota']),
    );

Map<String, dynamic> _$NotaToJson(Nota instance) => <String, dynamic>{
      'refUsuario': instance.refUsuario,
      'nota': instance.nota,
    };

ResponsavelRefeicao _$ResponsavelRefeicaoFromJson(Map json) =>
    ResponsavelRefeicao(
      nome: json['nome'] as String?,
      descricao: json['descricao'] as String?,
      urlFoto: json['urlFoto'] as String?,
      refKey: json['refKey'] as String?,
    );

Map<String, dynamic> _$ResponsavelRefeicaoToJson(
        ResponsavelRefeicao instance) =>
    <String, dynamic>{
      'descricao': instance.descricao,
      'nome': instance.nome,
      'urlFoto': instance.urlFoto,
      'refKey': instance.refKey,
    };

PlanoUsuarioExibir _$PlanoUsuarioExibirFromJson(Map json) => PlanoUsuarioExibir(
      planoMontado: json['planoMontado'] == null
          ? null
          : PlanoMontado.fromJson(
              Map<String, dynamic>.from(json['planoMontado'] as Map)),
      proximosDias: (json['proximosDias'] as List<dynamic>?)
          ?.map(
              (e) => ProximosDia.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$PlanoUsuarioExibirToJson(PlanoUsuarioExibir instance) =>
    <String, dynamic>{
      'planoMontado': instance.planoMontado?.toJson(),
      'proximosDias': instance.proximosDias?.map((e) => e.toJson()).toList(),
    };

PlanoMontado _$PlanoMontadoFromJson(Map json) => PlanoMontado(
      dataCriacaoPlano: parseStringToNum(json['dataCriacaoPlano']),
      estrelas:
          json['estrelas'] == null ? 5 : parseStringToNum(json['estrelas']),
      objetivo: $enumDecodeNullable(_$ObjetivoEnumMap, json['objetivo']),
      refUsuarioApp: json['refUsuarioApp'] as String?,
      refeicoesAGerar: (json['refeicoesAGerar'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      restricoes: (json['restricoes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
    );

Map<String, dynamic> _$PlanoMontadoToJson(PlanoMontado instance) =>
    <String, dynamic>{
      'dataCriacaoPlano': instance.dataCriacaoPlano,
      'estrelas': instance.estrelas,
      'refUsuarioApp': instance.refUsuarioApp,
      'refeicoesAGerar': instance.refeicoesAGerar,
      'objetivo': _$ObjetivoEnumMap[instance.objetivo],
      'restricoes': instance.restricoes,
    };

const _$ObjetivoEnumMap = {
  Objetivo.DEFINICAO: 'DEFINICAO',
  Objetivo.EMAGRECIMENTO: 'EMAGRECIMENTO',
  Objetivo.GANHO_MASSA: 'GANHO_MASSA',
};

DurationRange _$DurationRangeFromJson(Map json) => DurationRange(
      start: json['start'] == null
          ? null
          : Duration(microseconds: (json['start'] as num).toInt()),
      end: json['end'] == null
          ? null
          : Duration(microseconds: (json['end'] as num).toInt()),
    );

Map<String, dynamic> _$DurationRangeToJson(DurationRange instance) =>
    <String, dynamic>{
      'start': instance.start?.inMicroseconds,
      'end': instance.end?.inMicroseconds,
    };

ProximosDia _$ProximosDiaFromJson(Map json) => ProximosDia(
      data:
          json['data'] == null ? null : DateTime.parse(json['data'] as String),
      refeicoesRef: (json['refeicoesRef'] as List<dynamic>?)
          ?.map((e) =>
              RefeicaoPlanner.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$ProximosDiaToJson(ProximosDia instance) =>
    <String, dynamic>{
      'data': instance.data?.toIso8601String(),
      'refeicoesRef': instance.refeicoesRef?.map((e) => e.toJson()).toList(),
    };
