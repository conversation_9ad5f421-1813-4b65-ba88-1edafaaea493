// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'DicasPlanner.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DicaPlanner _$Dica<PERSON>lannerFromJson(Map json) => DicaPlanner(
      codigo: json['codigo'] as num?,
      titulo: json['titulo'] as String?,
      categoria: json['categoria'] as String?,
      nivel: json['nivel'] as String?,
      tempo: json['tempo'] as String?,
      tipo: json['tipo'] as String?,
      dataCriacao: json['dataCriacao'] as num?,
      tituloSuperior: json['tituloSuperior'] as String?,
      descricaoSuperior: json['descricaoSuperior'] as String?,
      tituloInferior: json['tituloInferior'] as String?,
      descricaoInferior: json['descricaoInferior'] as String?,
      notaDicaNutri: json['notaDicaNutri'] as num?,
      destaque: json['destaque'] as bool?,
      dataCriacaoApresentar: json['dataCriacaoApresentar'] as String?,
      urlImagem: json['urlImagem'] as String?,
      responsavel: json['responsavel'] == null
          ? null
          : Responsavel.fromJson(
              Map<String, dynamic>.from(json['responsavel'] as Map)),
      likeDicasJson: json['likeDicasJson'] == null
          ? null
          : LikeDicasJson.fromJson(
              Map<String, dynamic>.from(json['likeDicasJson'] as Map)),
      comentariosJson: (json['comentariosJson'] as List<dynamic>?)
          ?.map((e) =>
              ComentarioNutriJson.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$DicaPlannerToJson(DicaPlanner instance) =>
    <String, dynamic>{
      'codigo': instance.codigo,
      'titulo': instance.titulo,
      'categoria': instance.categoria,
      'nivel': instance.nivel,
      'tempo': instance.tempo,
      'tipo': instance.tipo,
      'dataCriacao': instance.dataCriacao,
      'tituloSuperior': instance.tituloSuperior,
      'descricaoSuperior': instance.descricaoSuperior,
      'tituloInferior': instance.tituloInferior,
      'descricaoInferior': instance.descricaoInferior,
      'notaDicaNutri': instance.notaDicaNutri,
      'destaque': instance.destaque,
      'dataCriacaoApresentar': instance.dataCriacaoApresentar,
      'urlImagem': instance.urlImagem,
      'responsavel': instance.responsavel?.toJson(),
      'likeDicasJson': instance.likeDicasJson?.toJson(),
      'comentariosJson':
          instance.comentariosJson?.map((e) => e.toJson()).toList(),
    };

LikeDicasJson _$LikeDicasJsonFromJson(Map json) => LikeDicasJson(
      codigoLike: json['codigoLike'] as num?,
      curtida: json['curtida'] as bool?,
      totalCurtidas: json['totalCurtidas'] as num?,
      relevancia: json['relevancia'] as num?,
    );

Map<String, dynamic> _$LikeDicasJsonToJson(LikeDicasJson instance) =>
    <String, dynamic>{
      'codigoLike': instance.codigoLike,
      'curtida': instance.curtida,
      'totalCurtidas': instance.totalCurtidas,
      'relevancia': instance.relevancia,
    };

Responsavel _$ResponsavelFromJson(Map json) => Responsavel(
      codigo: json['codigo'] as num?,
      nome: json['nome'] as String?,
      criador: json['criador'] as num?,
      foto: json['foto'] as String?,
      dicas: json['dicas'] == null
          ? null
          : DicaPlanner.fromJson(
              Map<String, dynamic>.from(json['dicas'] as Map)),
    );

Map<String, dynamic> _$ResponsavelToJson(Responsavel instance) =>
    <String, dynamic>{
      'codigo': instance.codigo,
      'nome': instance.nome,
      'criador': instance.criador,
      'foto': instance.foto,
      'dicas': instance.dicas?.toJson(),
    };

ComentarioNutriJson _$ComentarioNutriJsonFromJson(Map json) =>
    ComentarioNutriJson(
      chave: json['chave'] as String?,
      codDicasNutri: json['codDicasNutri'] as num?,
      codDicasNutriComentarioPai: json['codDicasNutriComentarioPai'] as num?,
      codUsuario: json['codUsuario'] as num?,
      codigo: json['codigo'] as num?,
      dataEdicao: json['dataEdicao'] as num?,
      dataEdicaoApresentar: json['dataEdicaoApresentar'] as String?,
      dataRegistro: json['dataRegistro'] as num?,
      dataRegistroApresentar: json['dataRegistroApresentar'] as String?,
      matricula: json['matricula'] as String?,
      nomePessoa: json['nomePessoa'] as String?,
      nota: json['nota'] as num?,
      texto: json['texto'] as String?,
      urlFotoPessoa: json['urlFotoPessoa'] as String?,
    );

Map<String, dynamic> _$ComentarioNutriJsonToJson(
        ComentarioNutriJson instance) =>
    <String, dynamic>{
      'codigo': instance.codigo,
      'chave': instance.chave,
      'dataEdicao': instance.dataEdicao,
      'dataRegistro': instance.dataRegistro,
      'matricula': instance.matricula,
      'nomePessoa': instance.nomePessoa,
      'nota': instance.nota,
      'texto': instance.texto,
      'urlFotoPessoa': instance.urlFotoPessoa,
      'dataRegistroApresentar': instance.dataRegistroApresentar,
      'dataEdicaoApresentar': instance.dataEdicaoApresentar,
      'codDicasNutri': instance.codDicasNutri,
      'codDicasNutriComentarioPai': instance.codDicasNutriComentarioPai,
      'codUsuario': instance.codUsuario,
    };
