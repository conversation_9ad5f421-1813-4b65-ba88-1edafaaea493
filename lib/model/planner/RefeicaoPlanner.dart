import 'package:json_annotation/json_annotation.dart';
part 'RefeicaoPlanner.g.dart';

parseStringToNum(dynamic numx) => numx is String ? num.parse(numx) : numx;

@JsonSerializable(explicitToJson: true,anyMap: true)
class RefeicaoPlanner {
  factory RefeicaoPlanner.fromJson(Map<String, dynamic> json) => _$RefeicaoPlannerFromJson(json);
  Map<String, dynamic> toJson() => _$RefeicaoPlannerToJson(this);
  RefeicaoPlanner clone() => _$RefeicaoPlannerFromJson(this.toJson());
  RefeicaoPlanner({
    this.nome,
    this.valorEnergetico,
    this.tempoPreparo,
    this.responsavelRef,
    this.dificuldade,
    this.likes,
    this.proteina,
    this.restricoes,
    this.gordura,
    this.refClienteApp,
    this.docKey,
    this.media,
    this.modoPrepaparo,
    this.tipoRefeicao,
    this.deslikes,
    this.descricao,
    this.notas,
    this.ingredientes,
    this.objetivo,
    this.carboidratoLiquido,
    this.docRef,
  });

  String? nome;
  @JsonKey(fromJson: parseStringToNum)
  num? valorEnergetico;
  @JsonKey(fromJson: parseStringToNum)
  num? tempoPreparo;
  String? responsavelRef;
  String? dificuldade;
  List<Like>? likes;
  @JsonKey(fromJson: parseStringToNum)
  num? proteina;
  List<String>? restricoes;
  @JsonKey(fromJson: parseStringToNum)
  num? gordura;
  String? refClienteApp;
  String? docKey;
  List<Media>? media;
  String? modoPrepaparo;
  List<String>? tipoRefeicao;
  List<Like>? deslikes;
  String? descricao;
  List<Nota>? notas;
  List<Ingrediente>? ingredientes;
  List<String>? objetivo;
  @JsonKey(fromJson: parseStringToNum)
  num? carboidratoLiquido;
  String? docRef;
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class Like {
  factory Like.fromJson(Map<String, dynamic> json) => _$LikeFromJson(json);
  Map<String, dynamic> toJson() => _$LikeToJson(this);
  Like clone() => _$LikeFromJson(this.toJson());
  Like({
    this.tipoReacao,
    this.refUsuario,
    this.dataRegistro,
    this.dataMilis,
    this.nomeUsuario,
    this.urlFotoUser,
  });

  String? tipoReacao;
  String? refUsuario;
  String? dataRegistro;
  String? dataMilis;
  String? nomeUsuario;
  String? urlFotoUser;
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class Ingrediente {
  factory Ingrediente.fromJson(Map<String, dynamic> json) => _$IngredienteFromJson(json);
  Map<String, dynamic> toJson() => _$IngredienteToJson(this);
  Ingrediente clone() => _$IngredienteFromJson(this.toJson());
  Ingrediente({
    this.quantidade,
    this.unidadeDeMedida,
    this.nome,
  });
  @JsonKey(fromJson: parseStringToNum)
  num? quantidade;
  String? unidadeDeMedida;
  String? nome;
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class Media {
  factory Media.fromJson(Map<String, dynamic> json) => _$MediaFromJson(json);
  Map<String, dynamic> toJson() => _$MediaToJson(this);
  Media clone() => _$MediaFromJson(this.toJson());
  Media({
    this.autor,
    this.dataUpload,
    this.url,
    this.tipo,
    this.posicao,
  });

  dynamic autor;
  @JsonKey(fromJson: parseStringToNum)
  num? dataUpload;
  String? url;
  String? tipo;
  @JsonKey(fromJson: parseStringToNum)
  num? posicao;
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class Nota {
  factory Nota.fromJson(Map<String, dynamic> json) => _$NotaFromJson(json);
  Map<String, dynamic> toJson() => _$NotaToJson(this);
  Nota clone() => _$NotaFromJson(this.toJson());
  Nota({
    this.refUsuario,
    this.nota,
  });

  String? refUsuario;
  @JsonKey(fromJson: parseStringToNum)
  num? nota;
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class ResponsavelRefeicao {
  String? descricao, nome, urlFoto, refKey;

  factory ResponsavelRefeicao.fromJson(Map<String, dynamic> json) => _$ResponsavelRefeicaoFromJson(json);
  Map<String, dynamic> toJson() => _$ResponsavelRefeicaoToJson(this);
  ResponsavelRefeicao clone() => _$ResponsavelRefeicaoFromJson(this.toJson());

  ResponsavelRefeicao({this.nome, this.descricao, this.urlFoto, this.refKey});
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class PlanoUsuarioExibir {
  PlanoMontado? planoMontado;
  List<ProximosDia>? proximosDias;
  PlanoUsuarioExibir({this.planoMontado, this.proximosDias});

  factory PlanoUsuarioExibir.fromJson(Map<String, dynamic> json) => _$PlanoUsuarioExibirFromJson(json);
  Map<String, dynamic> toJson() => _$PlanoUsuarioExibirToJson(this);
  PlanoUsuarioExibir clone() => _$PlanoUsuarioExibirFromJson(this.toJson());
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class PlanoMontado {
  @JsonKey(fromJson: parseStringToNum)
  num? dataCriacaoPlano;
  @JsonKey(fromJson: parseStringToNum, defaultValue: 5)
  num? estrelas;
  String? refUsuarioApp;
  @JsonKey(defaultValue: [])
  List<String>? refeicoesAGerar;
  Objetivo? objetivo;
  @JsonKey(defaultValue: [])
  List<String>? restricoes;
  PlanoMontado({this.dataCriacaoPlano, this.estrelas, this.objetivo, this.refUsuarioApp, this.refeicoesAGerar, this.restricoes});

  factory PlanoMontado.fromJson(Map<String, dynamic> json) => _$PlanoMontadoFromJson(json);
  Map<String, dynamic> toJson() => _$PlanoMontadoToJson(this);
  PlanoMontado clone() => _$PlanoMontadoFromJson(this.toJson());
}

enum Objetivo { DEFINICAO, EMAGRECIMENTO, GANHO_MASSA }

@JsonSerializable(explicitToJson: true,anyMap: true)
class DurationRange {
  factory DurationRange.fromJson(Map<String, dynamic> json) => _$DurationRangeFromJson(json);
  Map<String, dynamic> toJson() => _$DurationRangeToJson(this);
  DurationRange clone() => _$DurationRangeFromJson(this.toJson());
  Duration? start;
  Duration? end;
  DurationRange({this.start, this.end});
  bool estaNoRange(Duration durationRange) {
    return durationRange.inMilliseconds >= start!.inMilliseconds && durationRange.inMilliseconds <= end!.inMilliseconds;
  }
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class ProximosDia {
  DateTime? data;
  List<RefeicaoPlanner>? refeicoesRef;

  List<RefeicaoPlanner>? get refeicoesComMaisProximaEmPrimeiro {
    var horaAgora = Duration(hours: DateTime.now().hour, minutes: DateTime.now().minute);
    var mapaRefeicaoEordem = {
      'CAFE_MANHA': DurationRange(start: const Duration(hours: 00, minutes: 00), end: const Duration(hours: 9, minutes: 0)),
      'LANCHE_MANHA': DurationRange(start: const Duration(hours: 9, minutes: 1), end: const Duration(hours: 11, minutes: 20)),
      'ALMOCO': DurationRange(start: const Duration(hours: 11, minutes: 21), end: const Duration(hours: 14, minutes: 10)),
      'LANCHE_TARDE': DurationRange(start: const Duration(hours: 14, minutes: 11), end: const Duration(hours: 17, minutes: 00)),
      'JANTAR': DurationRange(start: const Duration(hours: 17, minutes: 1), end: const Duration(hours: 21, minutes: 30)),
      'CEIA': DurationRange(start: const Duration(hours: 21, minutes: 30), end: const Duration(hours: 23, minutes: 59)),
    };
    var filtro = refeicoesRef!.where((r) {
      return mapaRefeicaoEordem[r.tipoRefeicao!.first]!.estaNoRange(horaAgora);
    }).toList();
    if (filtro.isNotEmpty) {
      int index = refeicoesRef!.lastIndexWhere((element) => element.docKey == filtro.first.docKey);
      var temp = refeicoesRef![index];
      refeicoesRef!.removeAt(index);
      refeicoesRef!.insert(0, temp);
    }
    return refeicoesRef;
  }

  ProximosDia({this.data, this.refeicoesRef});

  factory ProximosDia.fromJson(Map<String, dynamic> json) => _$ProximosDiaFromJson(json);
  Map<String, dynamic> toJson() => _$ProximosDiaToJson(this);
  ProximosDia clone() => _$ProximosDiaFromJson(this.toJson());
}
