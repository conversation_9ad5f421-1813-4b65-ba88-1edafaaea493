import 'package:json_annotation/json_annotation.dart';
part 'DicasPlanner.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class DicaPlanner {
  factory DicaPlanner.fromJson(Map<String, dynamic> json) => _$DicaPlannerFromJson(json);
  Map<String, dynamic> toJson() => _$DicaPlannerToJson(this);
  DicaPlanner clone() => _$DicaPlannerFromJson(this.toJson());
  DicaPlanner({
    this.codigo,
    this.titulo,
    this.categoria,
    this.nivel,
    this.tempo,
    this.tipo,
    this.dataCriacao,
    this.tituloSuperior,
    this.descricaoSuperior,
    this.tituloInferior,
    this.descricaoInferior,
    this.notaDicaNutri,
    this.destaque,
    this.dataCriacaoApresentar,
    this.urlImagem,
    this.responsavel,
    this.likeDicasJson,
    this.comentariosJson,
  });

  num? codigo;
  String? titulo;
  String? categoria;
  String? nivel;
  String? tempo;
  String? tipo;
  num? dataCriacao;
  String? tituloSuperior;
  String? descricaoSuperior;
  String? tituloInferior;
  String? descricaoInferior;
  num? notaDicaNutri;
  bool? destaque;
  String? dataCriacaoApresentar;
  String? urlImagem;
  Responsavel? responsavel;
  LikeDicasJson? likeDicasJson;
  List<ComentarioNutriJson>? comentariosJson;
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class LikeDicasJson {
  factory LikeDicasJson.fromJson(Map<String, dynamic> json) => _$LikeDicasJsonFromJson(json);
  Map<String, dynamic> toJson() => _$LikeDicasJsonToJson(this);
  LikeDicasJson clone() => _$LikeDicasJsonFromJson(this.toJson());
  LikeDicasJson({
    this.codigoLike,
    this.curtida,
    this.totalCurtidas,
    this.relevancia,
  });

  num? codigoLike;
  bool? curtida;
  num? totalCurtidas;
  num? relevancia;
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class Responsavel {
  factory Responsavel.fromJson(Map<String, dynamic> json) => _$ResponsavelFromJson(json);
  Map<String, dynamic> toJson() => _$ResponsavelToJson(this);
  Responsavel clone() => _$ResponsavelFromJson(this.toJson());
  Responsavel({
    this.codigo,
    this.nome,
    this.criador,
    this.foto,
    this.dicas,
  });

  num? codigo;
  String? nome;
  num? criador;
  String? foto;
  DicaPlanner? dicas;
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class ComentarioNutriJson {
  factory ComentarioNutriJson.fromJson(Map<String, dynamic> json) => _$ComentarioNutriJsonFromJson(json);
  Map<String, dynamic> toJson() => _$ComentarioNutriJsonToJson(this);
  ComentarioNutriJson clone() => _$ComentarioNutriJsonFromJson(this.toJson());

  num? codigo;
  String? chave;
  num? dataEdicao;
  num? dataRegistro;
  String? matricula;
  String? nomePessoa;
  num? nota;
  String? texto;
  String? urlFotoPessoa;
  String? dataRegistroApresentar;
  String? dataEdicaoApresentar;
  num? codDicasNutri;
  num? codDicasNutriComentarioPai;
  num? codUsuario;

  ComentarioNutriJson(
      {this.chave,
      this.codDicasNutri,
      this.codDicasNutriComentarioPai,
      this.codUsuario,
      this.codigo,
      this.dataEdicao,
      this.dataEdicaoApresentar,
      this.dataRegistro,
      this.dataRegistroApresentar,
      this.matricula,
      this.nomePessoa,
      this.nota,
      this.texto,
      this.urlFotoPessoa});
}
