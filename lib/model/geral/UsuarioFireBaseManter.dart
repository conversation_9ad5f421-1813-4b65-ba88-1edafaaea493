import 'package:app_treino/model/treinoAluno/BodyCriarTreinoIA.dart';
import 'package:json_annotation/json_annotation.dart';
part 'UsuarioFireBaseManter.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class UsuarioFireBaseManter {
  factory UsuarioFireBaseManter.fromJson(Map<String, dynamic> json) => _$UsuarioFireBaseManterFromJson(json);
  Map<String, dynamic> toJson() => _$UsuarioFireBaseManterToJson(this);
  UsuarioFireBaseManter clone() => _$UsuarioFireBaseManterFromJson(this.toJson());
  UsuarioFireBaseManter(
      {this.refUsuario,
      this.widgetsSaude,
      this.userDeviceId,
      this.codigoCliente,
      this.codigoProfessor,
      this.nomeNoAplicativo,
      this.nomeUnidade,
      this.usuarioMovel,
      this.app,
      this.codigoDaEmpresa,
      this.email,
      this.fcmKey,
      this.idClienteApp,
      this.iddocumento,
      this.nome,
      this.nomeDaVersaoApp,
      this.plataforma,
      this.urlFoto,
      this.tipoPerfil,
      this.telefone,
      this.tipoDePlano,
      this.altura,
      this.atividadeFisica,
      this.dataNascimento,
      this.genero,
      this.metaCalorias,
      this.metaHidratacao,
      this.metaPassos,
      this.chave,
      this.pesoAtual,
      this.tamanhoCopo,
      this.codUsuario,
      this.matricula});
  TipoDePremium? tipoDePlano;
  WidgetsSaude? widgetsSaude;
  String? refUsuario;
  String? app;
  String? userDeviceId;
  num? codigoCliente;
  num? codigoProfessor;
  String? nomeNoAplicativo;
  String? nomeUnidade;
  String? usuarioMovel;
  String? telefone;
  int? codigoDaEmpresa;
  String? chave;
  String? email;
  String? fcmKey;
  String? idClienteApp;
  String? iddocumento;
  String? nome;
  String? nomeDaVersaoApp;
  String? plataforma;
  String? urlFoto;
  String? tipoPerfil;
  num? altura;
  int? tempoDisponivelParaTreinar;
  int? atividadeFisica;
  DateTime? dataNascimento;
  String? genero;
  num? pesoAtual;
  num? metaHidratacao;
  int? tamanhoCopo;
  int? metaPassos;
  num? metaCalorias;
  num? codUsuario;
  String? matricula;
  @override
  int get hashCode => toJson().hashCode;
  BodyTypeIA? get genderBodyType {
    return genero == 'Masculino' ? BodyTypeIA.Masculino : BodyTypeIA.Feminino;
  }
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class WidgetsSaude {
  factory WidgetsSaude.fromJson(Map<String, dynamic> json) => _$WidgetsSaudeFromJson(json);
  Map<String, dynamic> toJson() => _$WidgetsSaudeToJson(this);
  WidgetsSaude({this.calorias, this.passos, this.agua, this.cronometro, this.personalRecords, this.peso});
  bool? calorias;
  bool? passos;
  bool? agua;
  bool? cronometro;
  bool? personalRecords;
  bool? peso;
}

enum TipoDePremium { MENSAL, SEMESTRAL, ANUAL, NENHUM }
