import 'package:json_annotation/json_annotation.dart';
part 'AvaliacaoNPS.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class AvaliacaoNPS {
  factory AvaliacaoNPS.fromJson(Map<String, dynamic> json) => _$AvaliacaoNPSFromJson(json);
  Map<String, dynamic> toJson() => _$AvaliacaoNPSToJson(this);
  String? comentario;
  String? problema;
  String? reacao;
  String? clienteApp;
  String? userDocumentID;
  String? versaoApp;
  num? nota;

  AvaliacaoNPS({
    this.nota,
    this.comentario,
    this.problema,
    this.reacao,
    this.clienteApp,
    this.userDocumentID,
    this.versaoApp,
  });
}
