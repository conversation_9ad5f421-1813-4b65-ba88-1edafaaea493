import 'package:app_treino/appWidgets/componentWidgets/TextWidgets.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:flutter/material.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/model/util/UtilColor.dart';
import 'package:get_it/get_it.dart';
class TelaAcessoNegadoBodyFit extends StatefulWidget {
  @override
  _TelaAcessoNegadoBodyFitState createState() => _TelaAcessoNegadoBodyFitState();
}

class _TelaAcessoNegadoBodyFitState extends State<TelaAcessoNegadoBodyFit> {
  final _controladorCliente = GetIt.I.get<ControladorCliente>();
  final _controlladorApp = GetIt.I.get<ControladorApp>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(
                height: 190,
              ),
              Image.asset(
                'assets/images/icones/icone_lock.png',
                height: 100,
              ),
              const SizedBox(
                height: 24,
              ),
              TextHeadLine1('Opa, acesso negado!'),
              const SizedBox(
                height: 24,
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextBody1(
                  'Não conseguimos confirmar o pagamento da sua assinatura, mas fique tranquilo(a). Fale agora com a nossa equipe para atualizar seus dados e voltar a utilizar o melhor app do Brasil!',
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 47),
              Center(
                child: SizedBox(
                  width: 179,
                  height: 48,
                  child: ElevatedButton(
                      child: const Text(
                        'Falar com a equipe',
                        style: TextStyle(color: Colors.white),
                      ),
                      style: ButtonStyle(
                          foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
                          backgroundColor: WidgetStateProperty.all<Color>(Theme.of(context).primaryColor),
                          shape: WidgetStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(32),
                          ))),
                      onPressed: () {
                        UtilitarioApp().launchWhatsApp(
                            phone: GetIt.I.get<ControladorCliente>().contatoBodyFit,
                            message:
                                'Olá, meu nome é ${_controladorCliente.mUsuarioLogado?.nome ?? ''} e estou com um pagamento pendente no app Treino e gostaria de regularizar meu cadastro.');
                      }),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 32.0),
                child: Container(
                  decoration: BoxDecoration(
                      borderRadius: const BorderRadius.all(Radius.circular(50.0)),
                      border: Border.all(color: Theme.of(context).primaryColor)),
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: const BorderRadius.all(Radius.circular(50.0)),
                        color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.dark
                            ? HexColor.fromHex('#1C1C1E')
                            : Colors.white),
                    child: TextButton(
                      onPressed: () {
                        analytic(EventosKey.drawer_pressionou_logout);
                        UtilitarioApp().showDialogComImagemECallBack(
                            vaiTerIconeFechar: false,
                            acaoPrimaria: () {
                              analytic(EventosKey.drawer_confirmou_logout);
                              Navigator.pop(context);
                              _controladorCliente.deslogarUsuario(sucesso: () {
                                GetIt.I.get<ControladorApp>().limparTodosControladores();
                                FocusScope.of(context).requestFocus(FocusNode());
                                Navigator.of(context).pushNamedAndRemoveUntil(
                                    _controlladorApp.desabilitarNovoFluxoLogin ? '/buscarCadastroPorTelefone' : '/telaSelecaoMetodoLogin', (Route<dynamic> route) => false);
                              });
                            },
                            acaoSecundaria: () {
                              Navigator.pop(context);
                            },
                            context: context,
                            tipoAlerta: TipoAlerta.alerta,
                            subtituloMensagem:
                                'Ao clicar em sair você não estará mais logado no app e os dados não salvos serão perdidos.',
                            tituloBotaoPrimario: 'Sair',
                            tituloBotaoSecundario: 'cancel',
                            tituloMensagem: 'Deseja deslogar?');
                      },
                      child: TextBody1(
                        'Deslogar',
                        customColor: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
