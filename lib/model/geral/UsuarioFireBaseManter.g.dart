// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'UsuarioFireBaseManter.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UsuarioFireBaseManter _$UsuarioFireBaseManterFromJson(Map json) =>
    UsuarioFireBaseManter(
      refUsuario: json['refUsuario'] as String?,
      widgetsSaude: json['widgetsSaude'] == null
          ? null
          : WidgetsSaude.fromJson(
              Map<String, dynamic>.from(json['widgetsSaude'] as Map)),
      userDeviceId: json['userDeviceId'] as String?,
      codigoCliente: json['codigoCliente'] as num?,
      codigoProfessor: json['codigoProfessor'] as num?,
      nomeNoAplicativo: json['nomeNoAplicativo'] as String?,
      nomeUnidade: json['nomeUnidade'] as String?,
      usuarioMovel: json['usuarioMovel'] as String?,
      app: json['app'] as String?,
      codigoDaEmpresa: (json['codigoDaEmpresa'] as num?)?.toInt(),
      email: json['email'] as String?,
      fcmKey: json['fcmKey'] as String?,
      idClienteApp: json['idClienteApp'] as String?,
      iddocumento: json['iddocumento'] as String?,
      nome: json['nome'] as String?,
      nomeDaVersaoApp: json['nomeDaVersaoApp'] as String?,
      plataforma: json['plataforma'] as String?,
      urlFoto: json['urlFoto'] as String?,
      tipoPerfil: json['tipoPerfil'] as String?,
      telefone: json['telefone'] as String?,
      tipoDePlano:
          $enumDecodeNullable(_$TipoDePremiumEnumMap, json['tipoDePlano']),
      altura: json['altura'] as num?,
      atividadeFisica: (json['atividadeFisica'] as num?)?.toInt(),
      dataNascimento: json['dataNascimento'] == null
          ? null
          : DateTime.parse(json['dataNascimento'] as String),
      genero: json['genero'] as String?,
      metaCalorias: json['metaCalorias'] as num?,
      metaHidratacao: json['metaHidratacao'] as num?,
      metaPassos: (json['metaPassos'] as num?)?.toInt(),
      chave: json['chave'] as String?,
      pesoAtual: json['pesoAtual'] as num?,
      tamanhoCopo: (json['tamanhoCopo'] as num?)?.toInt(),
      codUsuario: json['codUsuario'] as num?,
      matricula: json['matricula'] as String?,
    )..tempoDisponivelParaTreinar =
        (json['tempoDisponivelParaTreinar'] as num?)?.toInt();

Map<String, dynamic> _$UsuarioFireBaseManterToJson(
        UsuarioFireBaseManter instance) =>
    <String, dynamic>{
      'tipoDePlano': _$TipoDePremiumEnumMap[instance.tipoDePlano],
      'widgetsSaude': instance.widgetsSaude?.toJson(),
      'refUsuario': instance.refUsuario,
      'app': instance.app,
      'userDeviceId': instance.userDeviceId,
      'codigoCliente': instance.codigoCliente,
      'codigoProfessor': instance.codigoProfessor,
      'nomeNoAplicativo': instance.nomeNoAplicativo,
      'nomeUnidade': instance.nomeUnidade,
      'usuarioMovel': instance.usuarioMovel,
      'telefone': instance.telefone,
      'codigoDaEmpresa': instance.codigoDaEmpresa,
      'chave': instance.chave,
      'email': instance.email,
      'fcmKey': instance.fcmKey,
      'idClienteApp': instance.idClienteApp,
      'iddocumento': instance.iddocumento,
      'nome': instance.nome,
      'nomeDaVersaoApp': instance.nomeDaVersaoApp,
      'plataforma': instance.plataforma,
      'urlFoto': instance.urlFoto,
      'tipoPerfil': instance.tipoPerfil,
      'altura': instance.altura,
      'tempoDisponivelParaTreinar': instance.tempoDisponivelParaTreinar,
      'atividadeFisica': instance.atividadeFisica,
      'dataNascimento': instance.dataNascimento?.toIso8601String(),
      'genero': instance.genero,
      'pesoAtual': instance.pesoAtual,
      'metaHidratacao': instance.metaHidratacao,
      'tamanhoCopo': instance.tamanhoCopo,
      'metaPassos': instance.metaPassos,
      'metaCalorias': instance.metaCalorias,
      'codUsuario': instance.codUsuario,
      'matricula': instance.matricula,
    };

const _$TipoDePremiumEnumMap = {
  TipoDePremium.MENSAL: 'MENSAL',
  TipoDePremium.SEMESTRAL: 'SEMESTRAL',
  TipoDePremium.ANUAL: 'ANUAL',
  TipoDePremium.NENHUM: 'NENHUM',
};

WidgetsSaude _$WidgetsSaudeFromJson(Map json) => WidgetsSaude(
      calorias: json['calorias'] as bool?,
      passos: json['passos'] as bool?,
      agua: json['agua'] as bool?,
      cronometro: json['cronometro'] as bool?,
      personalRecords: json['personalRecords'] as bool?,
      peso: json['peso'] as bool?,
    );

Map<String, dynamic> _$WidgetsSaudeToJson(WidgetsSaude instance) =>
    <String, dynamic>{
      'calorias': instance.calorias,
      'passos': instance.passos,
      'agua': instance.agua,
      'cronometro': instance.cronometro,
      'personalRecords': instance.personalRecords,
      'peso': instance.peso,
    };
