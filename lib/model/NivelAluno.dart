import 'package:json_annotation/json_annotation.dart';

part 'NivelAluno.g.dart';

@JsonSerializable()
class NivelAluno {
  final int id;
  final String nome;
  final int ordem;
  final bool ativo;

  NivelAluno({
    required this.id,
    required this.nome,
    required this.ordem,
    required this.ativo,
  });

  // From JSON
  factory NivelAluno.fromJson(Map<String, dynamic> json) => _$NivelAlunoFromJson(json);

  // To JSON
  Map<String, dynamic> toJson() => _$NivelAlunoToJson(this);
}
