// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ContratoOperacao.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ContratoOperacao _$ContratoOperacaoFromJson(Map json) => ContratoOperacao(
      codigo: json['codigo'] as num?,
      contrato: json['contrato'] as num?,
      tipoOperacao: json['tipoOperacao'] as String?,
      tipoOperacaoApresentar: json['tipoOperacaoApresentar'] as String?,
      operacaoPaga: json['operacaoPaga'] as bool?,
      dataRegistro: json['dataRegistro'] as String?,
      dataInicio: json['dataInicio'] as String?,
      dataFim: json['dataFim'] as String?,
      dataInicioRetorno: json['dataInicioRetorno'] as String?,
      dataFinalRetorno: json['dataFinalRetorno'] as String?,
      quantidadeDiasOperacao: json['quantidadeDiasOperacao'] as num?,
      quantidadeDiasRestantesContrato:
          json['quantidadeDiasRestantesContrato'] as num?,
      observacao: json['observacao'] as String?,
      descricaoCalculo: json['descricaoCalculo'] as String?,
      valor: json['valor'] as num?,
    );

Map<String, dynamic> _$ContratoOperacaoToJson(ContratoOperacao instance) =>
    <String, dynamic>{
      'codigo': instance.codigo,
      'contrato': instance.contrato,
      'tipoOperacao': instance.tipoOperacao,
      'tipoOperacaoApresentar': instance.tipoOperacaoApresentar,
      'operacaoPaga': instance.operacaoPaga,
      'dataRegistro': instance.dataRegistro,
      'dataInicio': instance.dataInicio,
      'dataFim': instance.dataFim,
      'dataInicioRetorno': instance.dataInicioRetorno,
      'dataFinalRetorno': instance.dataFinalRetorno,
      'quantidadeDiasOperacao': instance.quantidadeDiasOperacao,
      'quantidadeDiasRestantesContrato':
          instance.quantidadeDiasRestantesContrato,
      'observacao': instance.observacao,
      'descricaoCalculo': instance.descricaoCalculo,
      'valor': instance.valor,
    };
