import 'package:json_annotation/json_annotation.dart';

part 'BodyCancelarContrato.g.dart';

@JsonSerializable()
class BodyCancelarContrato {
  String? matricula;
  int? codigoContrato;
  String? observacao;
  
  BodyCancelarContrato({
    this.matricula,
    this.codigoContrato,
    this.observacao,
  });
  
  factory BodyCancelarContrato.fromJson(Map<String, dynamic> json) => 
      _$BodyCancelarContratoFromJson(json);
  Map<String, dynamic> toJson() => _$BodyCancelarContratoToJson(this);
  BodyCancelarContrato clone() => 
      _$BodyCancelarContratoFromJson(this.toJson());
}