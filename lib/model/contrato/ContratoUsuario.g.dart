// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ContratoUsuario.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ContratoUsuario _$ContratoUsuarioFromJson(Map json) => ContratoUsuario(
      codigo: json['codigo'] as num?,
      codigoCliente: json['codigoCliente'] as num?,
      valorFinal: json['valorFinal'] as num?,
      contratoEnviado: json['contratoEnviado'] as bool?,
      reciboEnviado: json['reciboEnviado'] as bool?,
      situacaoContrato: json['situacaoContrato'] as String?,
      dataLancamento: json['dataLancamento'] as String?,
      vigenciaDe: json['vigenciaDe'] as String?,
      vigenciaAteAjustada: json['vigenciaAteAjustada'] as String?,
      situacao: json['situacao'] as String?,
      nomePlano: json['nomePlano'] as String?,
      modalidades: (json['modalidades'] as List<dynamic>?)
          ?.map((e) =>
              ModalidadeContrato.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      valorMensal: json['valorMensal'] as num?,
      valorBase: json['valorBase'] as num?,
      valorAnuidade: json['valorAnuidade'] as num?,
      valorMatricula: json['valorMatricula'] as num?,
      diaVencimentoAnuidade: json['diaVencimentoAnuidade'] as num?,
      mesVencimentoAnuidade: json['mesVencimentoAnuidade'] as String?,
      valorAdesao: json['valorAdesao'] as num?,
      codigoPlano: json['codigoPlano'] as num?,
      maxVezesParcelarAdesao: json['maxVezesParcelarAdesao'] as num?,
      msgValidacao: json['msgValidacao'] as String?,
      numeroMeses: json['numeroMeses'] as num?,
      crossfit: json['crossfit'] as bool?,
      situacaoSubordinada: json['situacaoSubordinada'] as String?,
      permiteMarcarAula: json['permiteMarcarAula'] as bool?,
      vendaCreditoTreino: json['vendaCreditoTreino'] as bool?,
      nrParcelas: json['nrParcelas'] as num?,
      horarioDescricao: json['horarioDescricao'] as String?,
      permiteRenovar: json['permiteRenovar'] as bool?,
      simulacao: json['simulacao'] as bool?,
      bolsa: json['bolsa'] as bool?,
      maxVezesParcelarProduto: json['maxVezesParcelarProduto'] as num?,
      porcentagemUsadaContrato: json['porcentagemUsadaContrato'] as num?,
      valorPrimeiraParcela: json['valorPrimeiraParcela'] as num?,
      renovacaoautomaticasimnao: json['renovacaoautomaticasimnao'] as bool?,
      permiterenovacaoautomatica: json['permiterenovacaoautomatica'] as bool?,
      parcelas: (json['parcelas'] as List<dynamic>?)
          ?.map((e) =>
              ParcelaContrato.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$ContratoUsuarioToJson(ContratoUsuario instance) =>
    <String, dynamic>{
      'codigo': instance.codigo,
      'codigoCliente': instance.codigoCliente,
      'valorFinal': instance.valorFinal,
      'contratoEnviado': instance.contratoEnviado,
      'reciboEnviado': instance.reciboEnviado,
      'situacaoContrato': instance.situacaoContrato,
      'dataLancamento': instance.dataLancamento,
      'vigenciaDe': instance.vigenciaDe,
      'vigenciaAteAjustada': instance.vigenciaAteAjustada,
      'situacao': instance.situacao,
      'nomePlano': instance.nomePlano,
      'modalidades': instance.modalidades?.map((e) => e.toJson()).toList(),
      'parcelas': instance.parcelas?.map((e) => e.toJson()).toList(),
      'valorMensal': instance.valorMensal,
      'valorBase': instance.valorBase,
      'valorAnuidade': instance.valorAnuidade,
      'valorMatricula': instance.valorMatricula,
      'diaVencimentoAnuidade': instance.diaVencimentoAnuidade,
      'mesVencimentoAnuidade': instance.mesVencimentoAnuidade,
      'valorAdesao': instance.valorAdesao,
      'codigoPlano': instance.codigoPlano,
      'maxVezesParcelarAdesao': instance.maxVezesParcelarAdesao,
      'msgValidacao': instance.msgValidacao,
      'numeroMeses': instance.numeroMeses,
      'crossfit': instance.crossfit,
      'renovacaoautomaticasimnao': instance.renovacaoautomaticasimnao,
      'permiterenovacaoautomatica': instance.permiterenovacaoautomatica,
      'situacaoSubordinada': instance.situacaoSubordinada,
      'permiteMarcarAula': instance.permiteMarcarAula,
      'vendaCreditoTreino': instance.vendaCreditoTreino,
      'nrParcelas': instance.nrParcelas,
      'horarioDescricao': instance.horarioDescricao,
      'permiteRenovar': instance.permiteRenovar,
      'simulacao': instance.simulacao,
      'bolsa': instance.bolsa,
      'maxVezesParcelarProduto': instance.maxVezesParcelarProduto,
      'porcentagemUsadaContrato': instance.porcentagemUsadaContrato,
      'valorPrimeiraParcela': instance.valorPrimeiraParcela,
    };

ContratoAssinatura _$ContratoAssinaturaFromJson(Map json) => ContratoAssinatura(
      assinado: json['assinado'] as bool?,
      possuiResponsavel: json['possuiResponsavel'] as bool?,
      situacaocontrato: json['situacaocontrato'] as String?,
      contrato: (json['contrato'] as num?)?.toInt(),
      contratotextopadrao: json['contratotextopadrao'] as String?,
      aditivo: (json['aditivo'] as num?)?.toInt(),
      ehAditivo: json['ehAditivo'] as bool?,
    );

Map<String, dynamic> _$ContratoAssinaturaToJson(ContratoAssinatura instance) =>
    <String, dynamic>{
      'assinado': instance.assinado,
      'situacaocontrato': instance.situacaocontrato,
      'contrato': instance.contrato,
      'contratotextopadrao': instance.contratotextopadrao,
      'possuiResponsavel': instance.possuiResponsavel,
      'aditivo': instance.aditivo,
      'ehAditivo': instance.ehAditivo,
    };

AssinaturaDigitalRequest _$AssinaturaDigitalRequestFromJson(Map json) =>
    AssinaturaDigitalRequest(
      assinatura: json['assinatura'] as String,
      documentos: json['documentos'] as String,
      ip: json['ip'] as String,
      tipoAutenticacao: json['tipoAutenticacao'] as String,
      dadosAutenticacao: json['dadosAutenticacao'] as String,
    );

Map<String, dynamic> _$AssinaturaDigitalRequestToJson(
        AssinaturaDigitalRequest instance) =>
    <String, dynamic>{
      'assinatura': instance.assinatura,
      'documentos': instance.documentos,
      'ip': instance.ip,
      'tipoAutenticacao': instance.tipoAutenticacao,
      'dadosAutenticacao': instance.dadosAutenticacao,
    };

DadosAlteracaoVencimentoParcela _$DadosAlteracaoVencimentoParcelaFromJson(
        Map json) =>
    DadosAlteracaoVencimentoParcela(
      codigoContrato: json['codigoContrato'] as num? ?? 0,
      numeroDias: json['numeroDias'] as num? ?? 0,
      observacao: json['observacao'] as String? ?? '',
      alterarVencimentoParcelas:
          json['alterarVencimentoParcelas'] as bool? ?? false,
      dataTrancamentoRetroativo:
          json['dataTrancamentoRetroativo'] as String? ?? '',
    );

Map<String, dynamic> _$DadosAlteracaoVencimentoParcelaToJson(
        DadosAlteracaoVencimentoParcela instance) =>
    <String, dynamic>{
      'codigoContrato': instance.codigoContrato,
      'numeroDias': instance.numeroDias,
      'observacao': instance.observacao,
      'alterarVencimentoParcelas': instance.alterarVencimentoParcelas,
      'dataTrancamentoRetroativo': instance.dataTrancamentoRetroativo,
    };
