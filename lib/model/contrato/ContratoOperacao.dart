import 'package:json_annotation/json_annotation.dart';
part 'ContratoOperacao.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class ContratoOperacao {
  factory ContratoOperacao.fromJson(Map<String, dynamic> json) => _$ContratoOperacaoFromJson(json);
  Map<String, dynamic> toJson() => _$ContratoOperacaoToJson(this);
  ContratoOperacao clone() => _$ContratoOperacaoFromJson(this.toJson());

  @override
  bool operator ==(Object other) => other is ContratoOperacao && other.codigo == codigo;

  ContratoOperacao({
    this.codigo,
    this.contrato,
    this.tipoOperacao,
    this.tipoOperacaoApresentar,
    this.operacaoPaga,
    this.dataRegistro,
    this.dataInicio,
    this.dataFim,
    this.dataInicioRetorno,
    this.dataFinalRetorno,
    this.quantidadeDiasOperacao,
    this.quantidadeDiasRestantesContrato,
    this.observacao,
    this.descricaoCalculo,
    this.valor,
  });

  num? codigo;
  num? contrato;
  String? tipoOperacao;
  String? tipoOperacaoApresentar;
  bool? operacaoPaga;
  String? dataRegistro;
  String? dataInicio;
  String? dataFim;
  String? dataInicioRetorno;
  String? dataFinalRetorno;
  num? quantidadeDiasOperacao;
  num? quantidadeDiasRestantesContrato;
  String? observacao;
  String? descricaoCalculo;
  num? valor;

  @override
  int get hashCode => codigo.hashCode;
}
