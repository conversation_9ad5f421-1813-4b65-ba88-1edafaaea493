import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/contrato/ModalidadeContrato.dart';
import 'package:app_treino/model/contrato/ParcelaContrato.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:get_it/get_it.dart';
import 'package:json_annotation/json_annotation.dart';
part 'ContratoUsuario.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class ContratoUsuario {
  factory ContratoUsuario.fromJson(Map<String, dynamic> json) => _$ContratoUsuarioFromJson(json);
  Map<String, dynamic> toJson() => _$ContratoUsuarioToJson(this);
  ContratoUsuario clone() => _$ContratoUsuarioFromJson(this.toJson());

  @override
  bool operator ==(Object other) => other is ContratoUsuario && other.codigo == codigo;

  @override
  int get hashCode => codigo.hashCode;

  ContratoUsuario(
      {this.codigo,
      this.codigoCliente,
      this.valorFinal,
      this.contratoEnviado,
      this.reciboEnviado,
      this.situacaoContrato,
      this.dataLancamento,
      this.vigenciaDe,
      this.vigenciaAteAjustada,
      this.situacao,
      this.nomePlano,
      this.modalidades,
      this.valorMensal,
      this.valorBase,
      this.valorAnuidade,
      this.valorMatricula,
      this.diaVencimentoAnuidade,
      this.mesVencimentoAnuidade,
      this.valorAdesao,
      this.codigoPlano,
      this.maxVezesParcelarAdesao,
      this.msgValidacao,
      this.numeroMeses,
      this.crossfit,
      this.situacaoSubordinada,
      this.permiteMarcarAula,
      this.vendaCreditoTreino,
      this.nrParcelas,
      this.horarioDescricao,
      this.permiteRenovar,
      this.simulacao,
      this.bolsa,
      this.maxVezesParcelarProduto,
      this.porcentagemUsadaContrato,
      this.valorPrimeiraParcela,
      this.renovacaoautomaticasimnao,
      this.permiterenovacaoautomatica,
      this.parcelas});

  num? codigo;
  num? codigoCliente;
  num? valorFinal;
  bool? contratoEnviado;
  bool? reciboEnviado;
  String? situacaoContrato;
  String? dataLancamento;
  String? vigenciaDe;
  String? vigenciaAteAjustada;
  String? situacao;
  String? nomePlano;
  List<ModalidadeContrato>? modalidades;
  List<ParcelaContrato>? parcelas;
  num? valorMensal;
  num? valorBase;
  num? valorAnuidade;
  num? valorMatricula;
  num? diaVencimentoAnuidade;
  String? mesVencimentoAnuidade;
  num? valorAdesao;
  num? codigoPlano;
  num? maxVezesParcelarAdesao;
  String? msgValidacao;
  num? numeroMeses;
  bool? crossfit;
  bool? renovacaoautomaticasimnao;
  bool? permiterenovacaoautomatica;
  String? situacaoSubordinada;
  bool? permiteMarcarAula;
  bool? vendaCreditoTreino;
  num? nrParcelas;
  String? horarioDescricao;
  bool? permiteRenovar;
  bool? simulacao;
  bool? bolsa;
  num? maxVezesParcelarProduto;
  num? porcentagemUsadaContrato;
  num? valorPrimeiraParcela;
  num? get obterValorMensal {
    try {
      var mensal = 0;
      for (final element in parcelas!) {
        mensal += element.valor as int;
      }
      return mensal;
    } catch (e) {
      return valorMensal;
    }
  }

  int get duracaoTotalDiasContrato {
    try {
      return (UtilDataHora.parseStringToDate(vigenciaAteAjustada!)!.millisecondsSinceEpoch - UtilDataHora.parseStringToDate(dataLancamento!)!.millisecondsSinceEpoch) ~/ 86400000;
    } catch (e) {
      return -1;
    }
  }
}
enum TipoCheckContrato { termos, assinatura, selfie, all }
@JsonSerializable(explicitToJson: true,anyMap: true)
class ContratoAssinatura {
  factory ContratoAssinatura.fromJson(Map<String, dynamic> json) => _$ContratoAssinaturaFromJson(json);
  Map<String, dynamic> toJson() => _$ContratoAssinaturaToJson(this);
  ContratoAssinatura clone() => _$ContratoAssinaturaFromJson(this.toJson());

  bool? assinado;
  String? situacaocontrato;
  int? contrato;
  String? contratotextopadrao;
  bool? possuiResponsavel;
  bool get validarResponsavel {
    try {
      return GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.PERMITIR_VALIDAR_RESPONSAVEL_ASSINATURA).habilitado ?? false;
    } catch (e) {
      return false;
    }
  }

  bool get pessoaMaiorDeIdade {
    try {
      final dataNascimento = GetIt.I.get<ControladorCliente>().mDadosDoUsuario?.dataNascimento;
      if (dataNascimento == null) return true; // Assume maior de idade se não houver data
      return UtilDataHora.alunoMaiorDeIdade(dataNascimento: UtilDataHora.getDataComHorasPersonalizada(dataString: dataNascimento));
    } catch (e) {
      return true; // Em caso de erro, assume maior de idade para não bloquear
    }
  }

  bool get liberadoAassinar {
    try {
      return moduloAssinaturaDeContratoHabilitado && (validarResponsavel ? (pessoaMaiorDeIdade || (possuiResponsavel == false)) : true);
    } catch (e) {
      return false; // Em caso de erro, bloqueia assinatura por segurança
    }
  }

  bool get moduloAssinaturaDeContratoHabilitado => GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.HABILITAR_ASSINATURA_CONTRATO_APP).habilitado ?? false;
  int? aditivo;
  bool? ehAditivo;

  ContratoAssinatura({this.assinado, this.possuiResponsavel, this.situacaocontrato, this.contrato, this.contratotextopadrao, this.aditivo, this.ehAditivo});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class AssinaturaDigitalRequest {
  final String assinatura;
  final String documentos;
  final String ip;
  final String tipoAutenticacao;
  final String dadosAutenticacao;

  AssinaturaDigitalRequest({
    required this.assinatura,
    required this.documentos,
    required this.ip,
    required this.tipoAutenticacao,
    required this.dadosAutenticacao,
  });

  factory AssinaturaDigitalRequest.fromJson(Map<String, dynamic> json) => _$AssinaturaDigitalRequestFromJson(json);
  Map<String, dynamic> toJson() => _$AssinaturaDigitalRequestToJson(this);
  AssinaturaDigitalRequest clone() => _$AssinaturaDigitalRequestFromJson(this.toJson());    
}  
@JsonSerializable(explicitToJson: true, anyMap: true)
class DadosAlteracaoVencimentoParcela {
  factory DadosAlteracaoVencimentoParcela.fromJson(Map<String, dynamic> json) => _$DadosAlteracaoVencimentoParcelaFromJson(json);
  Map<String, dynamic> toJson() => _$DadosAlteracaoVencimentoParcelaToJson(this);
  DadosAlteracaoVencimentoParcela clone() => _$DadosAlteracaoVencimentoParcelaFromJson(this.toJson());

  num codigoContrato;
  num numeroDias;
  String observacao;
  bool alterarVencimentoParcelas;
  String dataTrancamentoRetroativo;

  DadosAlteracaoVencimentoParcela({
    this.codigoContrato = 0,
    this.numeroDias = 0,
    this.observacao = '',
    this.alterarVencimentoParcelas = false,
    this.dataTrancamentoRetroativo = '',
  });
}
