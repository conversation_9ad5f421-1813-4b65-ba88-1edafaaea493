// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'CarenciaOuTrancamento.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CarenciaOuTrancamento _$CarenciaOuTrancamentoFromJson(Map json) =>
    CarenciaOuTrancamento(
      contrato: json['contrato'] as num?,
      qtdCarenciaPermitida: json['qtdCarenciaPermitida'] as num?,
      qtdCarenciaUtilizado: json['qtdCarenciaUtilizado'] as num?,
      qtdMinimaCarencia: json['qtdMinimaCarencia'] as num?,
      qtdRestanteContrato: json['qtdRestanteContrato'] as num?,
      tipoOperacao: json['tipoOperacao'] as String?,
      justificativas: (json['justificativas'] as List<dynamic>?)
          ?.map((e) => JustificativaContrato.fromJson(
              Map<String, dynamic>.from(e as Map)))
          .toList(),
      produtos: (json['produtos'] as List<dynamic>?)
          ?.map((e) =>
              ProdutoContrato.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$CarenciaOuTrancamentoToJson(
        CarenciaOuTrancamento instance) =>
    <String, dynamic>{
      'contrato': instance.contrato,
      'qtdCarenciaPermitida': instance.qtdCarenciaPermitida,
      'qtdCarenciaUtilizado': instance.qtdCarenciaUtilizado,
      'qtdMinimaCarencia': instance.qtdMinimaCarencia,
      'qtdRestanteContrato': instance.qtdRestanteContrato,
      'tipoOperacao': instance.tipoOperacao,
      'justificativas':
          instance.justificativas?.map((e) => e.toJson()).toList(),
      'produtos': instance.produtos?.map((e) => e.toJson()).toList(),
    };
