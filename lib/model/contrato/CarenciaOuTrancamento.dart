import 'package:app_treino/model/contrato/JustificativaContrato.dart';
import 'package:app_treino/model/contrato/ProdutoContrato.dart';
import 'package:json_annotation/json_annotation.dart';
part 'CarenciaOuTrancamento.g.dart';

@JsonSerializable(explicitToJson: true,anyMap: true)
class CarenciaOuTrancamento {
  factory CarenciaOuTrancamento.fromJson(Map<String, dynamic> json) => _$CarenciaOuTrancamentoFromJson(json);
  Map<String, dynamic> toJson() => _$CarenciaOuTrancamentoToJson(this);
  CarenciaOuTrancamento clone() => _$CarenciaOuTrancamentoFromJson(this.toJson());
  CarenciaOuTrancamento({
    this.contrato,
    this.qtdCarenciaPermitida,
    this.qtdCarenciaUtilizado,
    this.qtdMinimaCarencia,
    this.qtdRestanteContrato,
    this.tipoOperacao,
    this.justificativas,
    this.produtos,
  });

  num? contrato;
  num? qtdCarenciaPermitida;
  num? qtdCarenciaUtilizado;
  num? qtdMinimaCarencia;
  num? qtdRestanteContrato;
  String? tipoOperacao;
  List<JustificativaContrato>? justificativas;
  List<ProdutoContrato>? produtos;
}
