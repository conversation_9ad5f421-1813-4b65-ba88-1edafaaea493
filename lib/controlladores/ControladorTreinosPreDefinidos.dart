import 'dart:convert';
import 'dart:developer';
import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorDBExtend.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/personal/AlunoDeProfessor.dart';
import 'package:app_treino/model/personal/ConfiguracoesBaseFicha.dart';
import 'package:app_treino/model/personal/ObjetivoAluno.dart';
import 'package:app_treino/model/personal/PadraoDeSerie.dart';
import 'package:app_treino/model/personal/ProgramaFicha.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:diacritic/diacritic.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorTreinosPreDefinidos.g.dart';

class ControladorTreinosPreDefinidos = _ControladorTreinosPreDefinidosBase with _$ControladorTreinosPreDefinidos;

abstract class _ControladorTreinosPreDefinidosBase extends UtilDataBase with Store {
  final _service = GetIt.I.get<PersonalService>();

  final _controladorCliente = GetIt.I.get<ControladorCliente>();

  bool veioMenuLateral = false;

  @observable
  ConfiguracoesBaseFicha? mConfiguracoesBaseFicha;

  @observable
  ServiceStatus statusConsultaConfBaseFicha = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultaFichaPreDefinida = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultaAtividades = ServiceStatus.Waiting;

  @observable
  ObservableList<ProgramaFicha?> mFichasPredefinidas = ObservableList<ProgramaFicha?>();
  @observable
  ObservableList<ProgramaFicha?> mFichasPredefinidasFiltradas = ObservableList<ProgramaFicha?>();
  @observable
  num quantidadeFiltrosAplicados = 0;
  @observable
  ObservableList<Nivel> filtroNiveis = ObservableList<Nivel>();
  @observable
  ObservableList<Categoria> filtroCategorias = ObservableList<Categoria>();
  @observable
  ObservableList<ObjetivoAluno> filtroObjetivos = ObservableList<ObjetivoAluno>();
  @observable
  ObservableList<String> filtroSexo = ObservableList<String>();
  @observable
  ObservableList<AtividadesFicha> todasAtividades = ObservableList<AtividadesFicha>();
  @observable
  ObservableList<AtividadesFicha> todasAtividadesFiltro = ObservableList<AtividadesFicha>();
  @observable
  ObservableList<AtividadesFicha> atividadesAdicionarNoTreino = ObservableList<AtividadesFicha>();
  @observable
  ObservableList<AlunoDeProfessor> mAlunosAdicionarFicha = ObservableList<AlunoDeProfessor>();
  @observable
  bool isPesquisa = false;
  @observable
  ProgramaFicha? treinoModelo;
  @observable
  bool mudouAlgo = false;
  @observable
  bool semAtividade = false;

  @observable
  bool mudouAlgoParaoSerie = false;
  @observable
  bool isFiltroAtivo = false;

  @observable
  String filtroAtividades = 'TODAS';

  bool jaConsultouAtividades = false;

  PadraoDeSerie padraoDeSerieAnaerobico = PadraoDeSerie();
  PadraoDeSerie padraoDeSerieAerobico = PadraoDeSerie();

  bool get treinoSemSeries {
    return treinoModelo!.atividadesFicha!.where((element) => element.series == null || element.series!.isEmpty).isNotEmpty;
  }

  String getCodigoSetCom(AtividadesFicha exercicio) {
    var ids = exercicio.setid!.split('|').where((element) => element.isNotEmpty && element != '${exercicio.ordem}');
    var retorno = '';
    for (final element in ids) {
      retorno += '$element, ';
    }
    if (retorno.isEmpty) return '';
    retorno = retorno.substring(0, retorno.length - 2);
    return retorno;
  }

  bool exercicoEstaNoTreino(AtividadesFicha exercicio) {
    try {
      return (atividadesAdicionarNoTreino.where((element) => element.cod != null && element.cod == exercicio.cod).isNotEmpty);
    } catch (e) {
      return false;
    }
  }

  manterAordemDosExercicios(num? ordemAntiga, num? ordemNova) {
    treinoModelo!.atividadesFicha!.sort((a, b) {
      return (a.ordem == null || b.ordem == null) ? 0 : a.ordem!.compareTo(b.ordem!);
    });
    if (ordemAntiga == null && ordemNova == null) {
      for (final exercicio in treinoModelo!.atividadesFicha!) {
        exercicio.ordem = treinoModelo!.atividadesFicha!.indexOf(exercicio) + 1;
      }
      mudouAlgo = !mudouAlgo;
    } else {
      manterAordemDosExercicios(null, null);
      for (final exercicio in treinoModelo!.atividadesFicha!) {
        if (exercicio.setid != null && exercicio.setid!.split('|').where((element) => element.isNotEmpty).contains('${ordemAntiga! + 1}')) {
          var setIds = exercicio.setid!.split('|').where((element) => element.isNotEmpty).map((e) {
            if (e == '${ordemAntiga + 1}') return '${ordemNova! + 1}';
            return e;
          }).toList();
          exercicio.setid = '';
          for (final ordem in setIds) {
            exercicio.setid = exercicio.setid! + '$ordem|';
          }
        }
      }
    }
  }

  void salvarTreinoModelo({Function()? suceso, Function(String mensagem)? erro}) {
    treinoModelo!.username = _controladorCliente.mUsuarioLogado!.username;
    treinoModelo!.programasFicha = [(ProgramaSimplesAluno(diaSemana: treinoModelo!.diaSemana))];
    log('message', error: const JsonCodec().encode(treinoModelo!.toJsonPredefindo()));
    _service.persistirFichaPreDef(treinoModelo!.toJsonPredefindo(), _controladorCliente.mUsuarioLogado!.username!).then((value) {
      mFichasPredefinidas.insert(0, treinoModelo);
      value.tipoExecucao = treinoModelo!.tipoExecucao ?? 0;
      value.diaSemana = value.diaSemana ?? [];
      value.codigoFichaModelo = value.codigo;
      mFichasPredefinidasFiltradas.insert(0, value);
      //treinoModelo = value;
      treinoModelo!.codigoFichaModelo = value.codigoFichaModelo;
      suceso?.call();
    }).catchError((onError) {
      erro!('Não foi possível gravar sua ficha pré definida. Tente novamente');
    });
  }

  void removerTreinoModelo(ProgramaFicha treino, {Function()? sucesso, Function(String? erro)? erro}) {
    _service.deletarTreinoModelo(_controladorCliente.mUsuarioLogado!.username!, treino.codigoFichaModelo!).then((value) {
      mFichasPredefinidasFiltradas.removeWhere((element) => element!.codigoFichaModelo == treino.codigoFichaModelo);
      mFichasPredefinidas.removeWhere((element) => element!.codigoFichaModelo == treino.codigoFichaModelo);
      sucesso!();
    }).catchError((onError) {
      erro!(onError.message);
    });
  }

  void exerciciosAdicionaAoTreino(AtividadesFicha exercicio) {
    if (atividadesAdicionarNoTreino.where((element) => element.cod == exercicio.cod || element.cod == element.atividade).isEmpty) {
      atividadesAdicionarNoTreino.add(exercicio);
    } else {
      atividadesAdicionarNoTreino.removeWhere((element) => element.cod == exercicio.cod || element.cod == element.atividade);
    }
  }

  void aplicarPadraoDeSerie({bool eAddAtividdade = false}) {
    for (final exercicio in this.treinoModelo!.atividadesFicha!) {
      if (!eAddAtividdade) exercicio.series?.clear();
      if (padraoDeSerieAnaerobico.ativo && padraoDeSerieAnaerobico.series != null && (exercicio.tipo == 0 || exercicio.tipoAtividade == 0)) {
        if ((exercicio.series == null || exercicio.series!.isEmpty)) {
          exercicio.series = [];
          for (var i = 0; i < padraoDeSerieAnaerobico.series!; i++) {
            exercicio.series!.add(Series(
                // atividadeFicha:exercicio.codigo,
                repeticaoComp: padraoDeSerieAnaerobico.repeticaoApp,
                repeticaoApp: padraoDeSerieAnaerobico.repeticaoApp,
                descanso: padraoDeSerieAnaerobico.descanso,
                ordem: i + 1));
          }
        }
      } else if (padraoDeSerieAerobico.ativo && padraoDeSerieAerobico.series != null && (exercicio.tipo == 1 || exercicio.tipoAtividade == 1)) {
        if ((exercicio.series == null || exercicio.series!.isEmpty)) {
          exercicio.series = [];
          for (var i = 0; i < padraoDeSerieAerobico.series!; i++) {
            exercicio.series!.add(Series(
                // atividadeFicha:exercicio.codigo,
                duracao: padraoDeSerieAerobico.tempo,
                descanso: padraoDeSerieAerobico.descanso,
                ordem: i + 1));
          }
        }
      }
    }
    this.mudouAlgo = !this.mudouAlgo;
  }

  aplicarFiltro() {
    todasAtividadesFiltro.clear();
    if (filtroAtividades == 'TODAS') {
      todasAtividadesFiltro.addAll(todasAtividades);
      semAtividade = false;
    } else {
      todasAtividadesFiltro.addAll(todasAtividades.where((element) {
        if (element.treinoLivre != null) {
          bool? add = false;
          if (filtroAtividades == 'SQ') {
            add = element.treinoLivre;
          } else if (filtroAtividades != 'TODAS') {
            add = !element.treinoLivre!;
          } else if (filtroAtividades == 'TODAS') {
            return true;
          }
          return add!;
        } else {
          return false;
        }
      }));
      if (todasAtividadesFiltro.isEmpty) {
        semAtividade = true;
      } else {
        semAtividade = false;
      }
    }
  }

  void pesquisarAtividadePorNomeFiltro(String termo) {
    if (termo.isNotEmpty) {
      todasAtividadesFiltro.clear();
      todasAtividadesFiltro.addAll(todasAtividades.where((element) {
        var add = removeDiacritics(element.nome!).toLowerCase().contains(removeDiacritics(termo).toLowerCase());
        if (filtroAtividades == 'SQ') {
          add = element.treinoLivre! && add;
        } else if (filtroAtividades != 'TODAS') {
          add = !element.treinoLivre! && add;
        }
        return add;
      }));
    } else {
      todasAtividadesFiltro.addAll(todasAtividades);
    }
    if (todasAtividadesFiltro.isEmpty) {
      semAtividade = true;
    } else {
      semAtividade = false;
    }
  }

  void confirmarAdicaoExerciciosDeAtividades() {
    if (treinoModelo!.atividadesFicha == null) treinoModelo!.atividadesFicha = [];
    List<AtividadesFicha> temp = atividadesAdicionarNoTreino.map((element) => element.clone()).toList();
    for (final element in temp) {
      element.atividade = element.cod;
      element.cod = null;
      element.tipoAtividade = element.tipo;
      element.ordem = temp.indexOf(element) + treinoModelo!.atividadesFicha!.length;
    }
    treinoModelo!.atividadesFicha!.addAll(temp);
    aplicarPadraoDeSerie(eAddAtividdade: true);
    atividadesAdicionarNoTreino.clear();
    manterAordemDosExercicios(null, null);
    mudouAlgo = !mudouAlgo;
  }

  Future<void> carregarListaComTodasAtividades({Function()? suceso, Function(String? mensagem)? falha, bool force = false}) async {
    if (jaConsultouAtividades) {
      statusConsultaAtividades = ServiceStatus.Done;
      suceso?.call();
      return;
    }
    else {
      if (_controladorCliente.isUsuarioColaborador || force) {
        statusConsultaAtividades = ServiceStatus.Waiting;
        try {
          var todasAtividadesOffline = await buscarListaNoBanco('todasAtividade');
          todasAtividadesOffline ??= [];
          todasAtividades.clear();
          todasAtividadesFiltro.clear();
          todasAtividades.addAll(todasAtividadesOffline.map((dynamic i) => AtividadesFicha.fromJson(i as Map<String, dynamic>)).toList());
          if(todasAtividades.isEmpty){
            todasAtividades.addAll(await _service.consultarTodasAtividades());
          }
          for (final element in todasAtividades) {
            element.tipoAtividade = element.tipo;
          }
          todasAtividadesFiltro.addAll(todasAtividades);
          if (todasAtividadesFiltro.isEmpty) {
            semAtividade = true;
          } else {
            semAtividade = false;
          }
          if (semAtividade) {
            statusConsultaAtividades = ServiceStatus.Empty;
          }
          else {
            statusConsultaAtividades = ServiceStatus.Done;
          }
          suceso?.call();
        } catch (error) {
          _service.consultarTodasAtividades().then((value) async {
            todasAtividades.clear();
            todasAtividadesFiltro.clear();
            todasAtividades.addAll(value);
            for (final element in todasAtividades) {
              element.tipoAtividade = element.tipo;
            }
            todasAtividadesFiltro.addAll(todasAtividades);
            if (todasAtividadesFiltro.isEmpty) {
              semAtividade = true;
              statusConsultaAtividades = ServiceStatus.Empty;
            }
            else {
              semAtividade = false;
              statusConsultaAtividades = ServiceStatus.Done;
            }
            await inserirAtividadesNoBanco('todasAtividade', const JsonCodec().decode(const JsonCodec().encode(todasAtividades)));
            suceso?.call();
          }).catchError((onError) {
            statusConsultaAtividades = ServiceStatus.Error;
            falha?.call(onError.message);
          });
        }
    }
    }
    
  }

  AtividadesFicha atividadeBaseViaAtividade(AtividadesFicha atividade) {
    try {
      var atividadeRtorno = AtividadesFicha.fromJson(todasAtividades.firstWhere((element) => atividade.atividade == element.cod).toJson());
      atividadeRtorno.atividade = atividade.atividade;
      atividadeRtorno.tipoAtividade = atividadeRtorno.tipo;
      atividadeRtorno.metodoExecucao = atividade.metodoExecucao;
      atividadeRtorno.series = atividade.series == null ? [] : atividade.series;

      atividadeRtorno.ordem = atividade.ordem;
      return atividadeRtorno;
    } catch (e) {
      return atividade;
    }
  }

  void validaSeTudoCarregou({Function()? sucesso, Function(String? v)? falha}) {
    if (todasAtividades.isNotEmpty && mConfiguracoesBaseFicha != null) {
      sucesso?.call();
    } else {
      consultarDadosBasico(
          force: true,
          sucesso: () async {
            await carregarListaComTodasAtividades(suceso: sucesso, falha: falha);
          },
          erro: falha);
    }
    falha?.toString();
  }

  void removerExercicioDoTreino(AtividadesFicha? atividade) {
    this.treinoModelo!.atividadesFicha!.remove(atividade);
    this.mudouAlgo = !this.mudouAlgo;
  }

  void updateQuantidadeNoFiltro() {
    quantidadeFiltrosAplicados = filtroNiveis.length + filtroCategorias.length + filtroObjetivos.length + filtroSexo.length;
    isFiltroAtivo = quantidadeFiltrosAplicados != 0;
    mFichasPredefinidasFiltradas.clear();
    mFichasPredefinidasFiltradas.addAll(mFichasPredefinidas.where((treinoModelo) {
      bool temSexoSelecionado = filtroSexo.isEmpty;
      if (treinoModelo!.sexo != null && !temSexoSelecionado) {
        temSexoSelecionado = filtroSexo.contains(treinoModelo.sexo);
      }
      bool temNivel = filtroNiveis.isEmpty;
      if (treinoModelo.nivel != null && !temNivel) {
        temNivel = filtroNiveis.where((nivel) => nivel.codigo == treinoModelo.nivel!.codigo).isNotEmpty;
      }
      bool temCategoria = filtroCategorias.isEmpty;
      if (treinoModelo.categoria != null && !temCategoria) {
        temCategoria = filtroCategorias.where((categoria) => categoria.codigo == treinoModelo.categoria).isNotEmpty;
      }
      return temSexoSelecionado && temNivel && temCategoria;
    }));
    statusConsultaFichaPreDefinida = ServiceStatus.Done;
  }

  void adicionarFiltroPorSexo(String objetivo) {
    if (!filtroSexo.contains(objetivo)) {
      filtroSexo.add(objetivo);
    } else {
      filtroSexo.remove(objetivo);
    }
    updateQuantidadeNoFiltro();
  }

  void adicionarFiltroPorObjetivo(ObjetivoAluno objetivo) {
    if (!filtroObjetivos.contains(objetivo)) {
      filtroObjetivos.add(objetivo);
    } else {
      filtroObjetivos.remove(objetivo);
    }
    updateQuantidadeNoFiltro();
  }

  void adicionarFiltroPorNivel(Nivel nivel) {
    if (!filtroNiveis.contains(nivel)) {
      filtroNiveis.add(nivel);
    } else {
      filtroNiveis.remove(nivel);
    }
    updateQuantidadeNoFiltro();
  }

  void adicionarFiltroPorCategoria(Categoria categoria) {
    if (!filtroCategorias.contains(categoria)) {
      filtroCategorias.add(categoria);
    } else {
      filtroCategorias.remove(categoria);
    }
    updateQuantidadeNoFiltro();
  }

  limparFiltros() {
    filtroNiveis.clear();
    filtroCategorias.clear();
    filtroObjetivos.clear();
    filtroSexo.clear();
    mFichasPredefinidasFiltradas.addAll(mFichasPredefinidasFiltradas);
    updateQuantidadeNoFiltro();
    isFiltroAtivo = false;
  }

  Future<void> consultarDadosBasico({bool force = false, Function()? sucesso, Function(String? mensagem)? erro}) async {
    statusConsultaConfBaseFicha = ServiceStatus.Waiting;
    var dadosBasicoFichas = await buscarNoBanco('baseDaFicha');
    if (GetIt.I.get<ControladorCliente>().isUsuarioColaborador) {
      if (force || mConfiguracoesBaseFicha == null) {
      _service.consultarInfoBasePrograma().then((value) async {
        mConfiguracoesBaseFicha = value;
        statusConsultaConfBaseFicha = ServiceStatus.Done;
        sucesso?.call();
        await inserirNoBanco('baseDaFicha', mConfiguracoesBaseFicha!.toJson());
      }).catchError((onError) {
        if (dadosBasicoFichas == null) {
          erro?.call(onError.message);
          statusConsultaConfBaseFicha = ServiceStatus.Error;
        } else {
          mConfiguracoesBaseFicha = ConfiguracoesBaseFicha.fromJson(dadosBasicoFichas);
          statusConsultaConfBaseFicha = ServiceStatus.Done;
        }
      });
    } else {
      statusConsultaConfBaseFicha = ServiceStatus.Done;
      sucesso?.call();
    }
    }
  }

  String? categoriaDoTreino(num? codigo) {
    if (codigo == null) return 'Sem Categoria';
    try {
      return mConfiguracoesBaseFicha!.categorias!.firstWhere((element) => element.codigo == codigo, orElse: () {
        return Categoria(nome: 'Sem Categoria');
      },).nome;
    } catch (e) {
      return 'Sem Categoria';
    }
  }

  void consultarFichasPreDefinidas({required Function()? carregando, Function()? sucesso, Function(String? mensagem)? erro}) {
    carregando?.call();
    _service.consultarFichasPreDefinidas(userName: _controladorCliente.mUsuarioLogado!.username, max: 100).then((value) {
      filtroSexo.clear();
      filtroNiveis.clear();
      filtroCategorias.clear();
      filtroObjetivos.clear();
      mFichasPredefinidas.clear();
      mFichasPredefinidasFiltradas.clear();
      for (final ProgramaFicha ficha in value) {
        ficha.tipoExecucao ??= 0;
      }
      mFichasPredefinidas.addAll(value);
      mFichasPredefinidasFiltradas.addAll(value);
      updateQuantidadeNoFiltro();
      sucesso!();
    }).catchError((onError) {
      erro!(onError.message);
      
    });
  }

  void filtrarPorTermo(String termo) {
    statusConsultaFichaPreDefinida = ServiceStatus.Waiting;
     mFichasPredefinidasFiltradas.clear();
      _service.consultarFichasPreDefinidas(userName: _controladorCliente.mUsuarioLogado!.username, nomeFicha: termo, codigoProfessor: _controladorCliente.mUsuarioLogado!.codigoProfessor).then((value) {
        filtroSexo.clear();
        filtroNiveis.clear();
        filtroCategorias.clear();
        filtroObjetivos.clear();
        mFichasPredefinidas.clear();
        mFichasPredefinidasFiltradas.clear();
        mFichasPredefinidas.addAll(value);
        mFichasPredefinidasFiltradas.addAll(value);
        updateQuantidadeNoFiltro();
        statusConsultaFichaPreDefinida = termo.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      });
  }

  void selecionarAlunoParaAdicionarTreinoModelo(AlunoDeProfessor aluno) {
    if (mAlunosAdicionarFicha.where((element) => element.codigo == aluno.codigo).isEmpty) {
      mAlunosAdicionarFicha.add(aluno);
    } else {
      mAlunosAdicionarFicha.removeWhere((element) => element.codigo == aluno.codigo);
    }
  }

  void persistirFichaEmUmAluno({Function()? sucesso, Function(String mensagem)? erro}) {
    _service.consultarUltimoProgramaDoAluno(_controladorCliente.mUsuarioLogado!.username!, mAlunosAdicionarFicha.first.codigo!).then((planoDeTreino) {
      if (UtilDataHora.dataJaPassouDeHoje(dataMilis: planoDeTreino.dataTerminoPrevisto as int?) || planoDeTreino.codigo == null) {
        return _service.consultarProgramaBase(mAlunosAdicionarFicha.first.codigo!, _controladorCliente.mUsuarioLogado!.username!);
      } else {
        treinoModelo!.programasFicha = [];
        treinoModelo!.programasFicha!.add(ProgramaSimplesAluno(programa: planoDeTreino.codigo));
        treinoModelo!.username = _controladorCliente.mUsuarioLogado!.username;
        treinoModelo!.usarComoPredefinida = false;
        treinoModelo!.codigoFichaModelo = null;
        if (treinoModelo!.nivel?.nome == null) treinoModelo!.nivel = null; 
        return _service.persistirFicha(treinoModelo!.toJsonPredefindo(), _controladorCliente.mUsuarioLogado!.username!);
      }
    }).then((value) {
      if (value is ProgramaDeTreino) {
        value.nome = 'Plano A';
        return _service.manterPrograma(value.toJson());
      } else {
        mAlunosAdicionarFicha.clear();
        return sucesso!.call();
      }
    }).then((value) {
      if (value is ProgramaFicha) persistirFichaEmUmAluno(sucesso: sucesso, erro: erro);
    }).catchError((onError) {
      erro!('Não foi possível gravar seu treino. Tente novamente');
    });
  }

  void persisitrFichaNosAlunosSelecionados({Function()? sucesso, Function(String? mensagem)? erro}) {
    var body = const JsonCodec().decode(const JsonCodec().encode(mAlunosAdicionarFicha.map((element) => {'matricula': element.codigo}).toList()));
    _service.consultarProgramasBaisocosAluno(body, _controladorCliente.mUsuarioLogado!.username!).then((value) {
      if (treinoModelo!.programasFicha == null) treinoModelo!.programasFicha = [];
      treinoModelo!.programasFicha!.addAll(value);
      treinoModelo!.codigoFichaModelo = null;
      return _service.persistirFicha(treinoModelo!.toJsonPredefindo(), _controladorCliente.mUsuarioLogado!.username!);
    }).then((value) {
      treinoModelo = null;
      sucesso?.call();
      mAlunosAdicionarFicha.clear();
    }).catchError((onError) {
      erro?.call(onError.message);
    });
  }

  bool alunoEstaNaListaAddTreinoModelo(AlunoDeProfessor aluno) {
    return mAlunosAdicionarFicha.where((element) => element.codigo == aluno.codigo).isNotEmpty;
  }

  limparTudo() {
    jaConsultouAtividades = false;
    deletarDoBanco('todasAtividade');
    mConfiguracoesBaseFicha = null;
    mFichasPredefinidas.clear();
    mFichasPredefinidasFiltradas.clear();
    quantidadeFiltrosAplicados = 0;
    filtroNiveis.clear();
    filtroCategorias.clear();
    filtroObjetivos.clear();
    filtroSexo.clear();
    todasAtividades.clear();
    todasAtividadesFiltro.clear();
    atividadesAdicionarNoTreino.clear();
    mAlunosAdicionarFicha.clear();
    isPesquisa = false;
    treinoModelo = null;
    mudouAlgo = false;
    mudouAlgoParaoSerie = false;
    isFiltroAtivo = false;
    filtroAtividades = 'TODAS';
    padraoDeSerieAnaerobico = PadraoDeSerie();
    padraoDeSerieAerobico = PadraoDeSerie();
  }
  
}
