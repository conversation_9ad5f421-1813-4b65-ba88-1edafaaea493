import 'package:app_treino/model/util/CountryPhoneList.dart';
import 'package:mobx/mobx.dart';
part 'ControladorPesquisaDePais.g.dart';

class ControladorPesquiaDePais = _ControladorPesquiaDePaisBase with _$ControladorPesquiaDePais;

abstract class _ControladorPesquiaDePaisBase with Store {
  void laodItens() {
    mPaises.clear();
    mPaisesPesquisa.clear();
    mPaises.addAll(CountryPhone().consultarListaPaises());
    mPaisesPesquisa.addAll(mPaises);
  }

  @observable
  ObservableList<CountryPhone> mPaises = ObservableList<CountryPhone>();

  @observable
  ObservableList<CountryPhone> mPaisesPesquisa = ObservableList<CountryPhone>();

  void pesquisarPaisPorTexto(String pesquisa) {
    var termo = pesquisa.toLowerCase();
    mPaisesPesquisa.clear();
    if (termo.isNotEmpty) {
      mPaisesPesquisa.addAll(mPaises.where((element) => element.abrev!.toLowerCase().contains(termo) || element.nome!.toLowerCase().contains(termo) || element.codigoPais!.contains(termo)).toList());
    } else {
      mPaisesPesquisa.addAll(CountryPhone().consultarListaPaises());
    }
  }
}
