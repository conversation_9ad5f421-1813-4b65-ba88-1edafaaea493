// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorAgendamento.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorAgendamento on _ControladorAgendamentoBase, Store {
  late final _$statusConsultaAgendamentosAtom = Atom(
      name: '_ControladorAgendamentoBase.statusConsultaAgendamentos',
      context: context);

  @override
  ServiceStatus get statusConsultaAgendamentos {
    _$statusConsultaAgendamentosAtom.reportRead();
    return super.statusConsultaAgendamentos;
  }

  @override
  set statusConsultaAgendamentos(ServiceStatus value) {
    _$statusConsultaAgendamentosAtom
        .reportWrite(value, super.statusConsultaAgendamentos, () {
      super.statusConsultaAgendamentos = value;
    });
  }

  late final _$statusConsultaHorariosAtom = Atom(
      name: '_ControladorAgendamentoBase.statusConsultaHorarios',
      context: context);

  @override
  ServiceStatus get statusConsultaHorarios {
    _$statusConsultaHorariosAtom.reportRead();
    return super.statusConsultaHorarios;
  }

  @override
  set statusConsultaHorarios(ServiceStatus value) {
    _$statusConsultaHorariosAtom
        .reportWrite(value, super.statusConsultaHorarios, () {
      super.statusConsultaHorarios = value;
    });
  }

  late final _$statusAtom =
      Atom(name: '_ControladorAgendamentoBase.status', context: context);

  @override
  ServiceStatus get status {
    _$statusAtom.reportRead();
    return super.status;
  }

  @override
  set status(ServiceStatus value) {
    _$statusAtom.reportWrite(value, super.status, () {
      super.status = value;
    });
  }

  late final _$statusTipoAgendamentoAtom = Atom(
      name: '_ControladorAgendamentoBase.statusTipoAgendamento',
      context: context);

  @override
  ServiceStatus get statusTipoAgendamento {
    _$statusTipoAgendamentoAtom.reportRead();
    return super.statusTipoAgendamento;
  }

  @override
  set statusTipoAgendamento(ServiceStatus value) {
    _$statusTipoAgendamentoAtom.reportWrite(value, super.statusTipoAgendamento,
        () {
      super.statusTipoAgendamento = value;
    });
  }

  late final _$professoresAgendamentoAtom = Atom(
      name: '_ControladorAgendamentoBase.professoresAgendamento',
      context: context);

  @override
  List<ProfessorNovoAgendamento> get professoresAgendamento {
    _$professoresAgendamentoAtom.reportRead();
    return super.professoresAgendamento;
  }

  @override
  set professoresAgendamento(List<ProfessorNovoAgendamento> value) {
    _$professoresAgendamentoAtom
        .reportWrite(value, super.professoresAgendamento, () {
      super.professoresAgendamento = value;
    });
  }

  late final _$listaAgendamentosPorTipoAtom = Atom(
      name: '_ControladorAgendamentoBase.listaAgendamentosPorTipo',
      context: context);

  @override
  List<String> get listaAgendamentosPorTipo {
    _$listaAgendamentosPorTipoAtom.reportRead();
    return super.listaAgendamentosPorTipo;
  }

  @override
  set listaAgendamentosPorTipo(List<String> value) {
    _$listaAgendamentosPorTipoAtom
        .reportWrite(value, super.listaAgendamentosPorTipo, () {
      super.listaAgendamentosPorTipo = value;
    });
  }

  late final _$tipoDeAgendamentoAtom = Atom(
      name: '_ControladorAgendamentoBase.tipoDeAgendamento', context: context);

  @override
  List<TipoAgendamento> get tipoDeAgendamento {
    _$tipoDeAgendamentoAtom.reportRead();
    return super.tipoDeAgendamento;
  }

  @override
  set tipoDeAgendamento(List<TipoAgendamento> value) {
    _$tipoDeAgendamentoAtom.reportWrite(value, super.tipoDeAgendamento, () {
      super.tipoDeAgendamento = value;
    });
  }

  late final _$mAgendamentosPorProfessorAtom = Atom(
      name: '_ControladorAgendamentoBase.mAgendamentosPorProfessor',
      context: context);

  @override
  List<NovoAgendamento> get mAgendamentosPorProfessor {
    _$mAgendamentosPorProfessorAtom.reportRead();
    return super.mAgendamentosPorProfessor;
  }

  @override
  set mAgendamentosPorProfessor(List<NovoAgendamento> value) {
    _$mAgendamentosPorProfessorAtom
        .reportWrite(value, super.mAgendamentosPorProfessor, () {
      super.mAgendamentosPorProfessor = value;
    });
  }

  late final _$professoresAgrupadoAtom = Atom(
      name: '_ControladorAgendamentoBase.professoresAgrupado',
      context: context);

  @override
  Map<String, List<ProfessorNovoAgendamento>> get professoresAgrupado {
    _$professoresAgrupadoAtom.reportRead();
    return super.professoresAgrupado;
  }

  @override
  set professoresAgrupado(Map<String, List<ProfessorNovoAgendamento>> value) {
    _$professoresAgrupadoAtom.reportWrite(value, super.professoresAgrupado, () {
      super.professoresAgrupado = value;
    });
  }

  @override
  String toString() {
    return '''
statusConsultaAgendamentos: ${statusConsultaAgendamentos},
statusConsultaHorarios: ${statusConsultaHorarios},
status: ${status},
statusTipoAgendamento: ${statusTipoAgendamento},
professoresAgendamento: ${professoresAgendamento},
listaAgendamentosPorTipo: ${listaAgendamentosPorTipo},
tipoDeAgendamento: ${tipoDeAgendamento},
mAgendamentosPorProfessor: ${mAgendamentosPorProfessor},
professoresAgrupado: ${professoresAgrupado}
    ''';
  }
}
