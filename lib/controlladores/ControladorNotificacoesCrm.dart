import 'dart:convert';

import 'package:app_treino/ServiceProvider/authServices/ServiceAuth.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/NotificacaoCRM.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/controlladores/ControladorDBExtend.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorNotificacoesCrm.g.dart';

class ControladorNotificacoesCrm = _ControladorNotificacoesCrmBase
    with _$ControladorNotificacoesCrm;

abstract class _ControladorNotificacoesCrmBase extends UtilDataBase with Store {
  void limparTudo() {
    statusConsultaCrm = ServiceStatus.Waiting;
    mNotificacoesCrm.clear();
  }

  @observable
  ServiceStatus statusConsultaCrm = ServiceStatus.Waiting;

  int get quantidadeRecebidasSessao {
    return mNotificacoesCrm.where((element) => !element.lida!).length;
  }

  void Function(void Function())? setStateBell;
  @observable
  ObservableList<NotificacaoCRMAcademia> mNotificacoesCrm =
      ObservableList<NotificacaoCRMAcademia>();

  final _mService = GetIt.I.get<ServiceAuth>();

  bool eParaResponder(NotificacaoCRMAcademia not) {
    return (eDiscursiva(not) || eMultiplcaEscolha(not)) &&
        (not.resposta?.isEmpty ?? true);
  }

  bool eDiscursiva(NotificacaoCRMAcademia notificacao) {
    return (notificacao.opcoes?.toLowerCase().contains('confirmar') ?? false) &&
        (notificacao.opcoes?.toLowerCase().contains('texto') ?? false) &&
        (notificacao.resposta?.isEmpty ?? true);
  }

  bool eMultiplcaEscolha(NotificacaoCRMAcademia notificacao) {
    return !((notificacao.opcoes?.toLowerCase().contains('confirmar') ??
                false) &&
            (notificacao.opcoes?.toLowerCase().contains('texto') ?? false)) &&
        (notificacao.opcoes?.split(';').isNotEmpty ?? false);
  }

  DateTime? dataAgendamento(NotificacaoCRMAcademia not) {
    DateTime? retorna = DateTime.now();
    not.texto?.split(' ').forEach((tryParse) {
      if (UtilDataHora.parseStringToDate(tryParse) != null) {
        retorna = UtilDataHora.parseStringToDate(tryParse);
      }
    });
    return retorna;
  }

  void enviarRespostaAoCrm(
      NotificacaoCRMAcademia notificacaoMarcar, String resposta,
      {Function()? carregando,
      Function()? sucesso,
      Function(String? mensagem)? falha}) {
    carregando?.call();
    _mService
        .responderNotificacao(notificacaoMarcar.cod!, resposta)
        .then((value) {
      int index = mNotificacoesCrm
          .indexWhere((element) => element.cod == notificacaoMarcar.cod);
      mNotificacoesCrm.removeAt(index);
      notificacaoMarcar.lida = true;
      notificacaoMarcar.resposta = resposta;
      mNotificacoesCrm.insert(index, notificacaoMarcar);
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  void marcarNotificacaoComoLida(NotificacaoCRMAcademia notificacaoMarcar,
      {Function()? carregando,
      Function()? sucesso,
      Function(String? mensagem)? falha}) {
    carregando?.call();
    //statusConsultaCrm = ServiceStatus.Waiting;
    _mService
        .marcarNotificacaoCrmLida(
            (GetIt.I.get<ControladorCliente>().mUsuarioLogado!.username ?? ''),
            notificacaoMarcar.cod!,
            false,
            GetIt.I.get<ControladorCliente>().mUsuarioLogado!.matricula)
        .then((value) {
      statusConsultaCrm = ServiceStatus.Done;
      int index = mNotificacoesCrm
          .indexWhere((element) => element.cod == notificacaoMarcar.cod);
      mNotificacoesCrm.removeAt(index);
      notificacaoMarcar.lida = true;
      mNotificacoesCrm.insert(index, notificacaoMarcar);
      setStateBell?.call(() {});
      sucesso?.call();
    }).catchError((onError) {
      statusConsultaCrm = ServiceStatus.Done;
      falha?.call(onError.message);
    });
  }

  void consultarNotificacoesCrm(
      {Function()? carregando,
      Function()? sucesso,
      Function(String? mensagem)? falha}) {
    carregando?.call();
    statusConsultaCrm = ServiceStatus.Waiting;
    mNotificacoesCrm.clear();
    _mService.consultarNotificacoes(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.username ?? '', GetIt.I.get<ControladorCliente>().mUsuarioLogado!.matricula).then((value) {
      if (value.academia != null) {
        for (final element in value.academia!) {
          element.eNotificaoProfessor = false;
          mNotificacoesCrm.add(element); 
        }
      }
      if (value.professor != null) {
        for (final element in value.professor!) {
          element.eNotificaoProfessor = true;
          mNotificacoesCrm.add(element);
        }
      }
      mNotificacoesCrm.sort((b, a) => UtilDataHora.parseStringToDate(a.data!)!.compareTo(UtilDataHora.parseStringToDate(b.data!)!));
      consultarPush(
        carregando: () {}, 
        sucesso: () {
          mNotificacoesCrm.sort((b, a) => UtilDataHora.parseStringToDate(a.data!)!.compareTo(UtilDataHora.parseStringToDate(b.data!)!));
          statusConsultaCrm = mNotificacoesCrm.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
          setStateBell?.call(() {});
          sucesso?.call();
        },
        falha: (mensagem) {
          sucesso?.call();
        }
      );
      
    }).catchError((onError) {
      statusConsultaCrm = ServiceStatus.Error;
      falha?.call(onError.message);
    });
  }

  Future<void> marcaTodasComoLida() async {
    for (final element in mNotificacoesCrm) {
      if(!element.lida!){
        await Future.delayed(const Duration(milliseconds: 5000)).then((value) => marcarNotificacaoComoLida(element));
      }
    }
  }

  @observable
  List<PushUsuario> mPushUsuario = [];
  @observable
  List<String> listPush = [];

  /// Consulta o push de um determinado objeto.
  ///
  /// Este método é responsável por realizar a consulta do push de um objeto específico.
  /// Retorna o push do objeto consultado.
  ///
  /// Parâmetros:
  ///   - objeto: O objeto a ser consultado.
  ///
  /// Retorno:
  ///   - O push do objeto consultado.
  ///
  /// Exemplo de uso:
  ///   consultarPush(objeto);
  ///
  void consultarPush({Function()? carregando, Function()? sucesso, Function(String? mensagem)? falha}) {
    carregando?.call();
    mPushUsuario.clear();
    _mService.consultarPushs(GetIt.I.get<ControladorApp>().chave ?? '', GetIt.I.get<ControladorCliente>().mUsuarioLogado!.username ?? '', GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codUsuario ?? 0).then((value) async {
      mPushUsuario.addAll(value);
      mPushUsuario.sort((b, a) => a.dataCriada!.compareTo(b.dataCriada!));
      await marcarPushComoLida(
        falha: (mensagem) {},
        sucesso: () {
          sucesso?.call();
        }
      );
      for (final push in mPushUsuario) {
        DateTime localDate = push.dataCriada!.toLocal();
        NotificacaoCRMAcademia notificacao = NotificacaoCRMAcademia(
          data: UtilDataHora.getDiaMesAnoHorasMinutos(dateTime: localDate),
          titulo: push.titulo,
          texto: push.content,
          lida: push.lida,
          eNotificaoProfessor: false,
          dataLong: push.dataCriada!.millisecondsSinceEpoch,
          isPush: true,
        );
        if (mNotificacoesCrm.any((n) => n.titulo == notificacao.titulo && n.texto == notificacao.texto)) {
          continue;
        }
        mNotificacoesCrm.add(notificacao);
      }
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.toString());
    });
  }

  Future<void> marcarPushComoLida({Function()? sucesso, Function(String? mensagem)? falha}) async {
    listPush.clear();
    for (final push in mPushUsuario) {
      if (!push.lida!) {
        listPush.add(push.refPush ?? '');
      }
    }
    var json = jsonEncode({'ids': listPush});
    var body = jsonDecode(json);
    await _mService.marcaPushComoLida(body).then((value) {
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.toString());
    });
  }
}
