// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorConfiguracao.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorConfiguracao on _ControladorConfiguracaoBase, Store {
  late final _$isMetroDistanciaAtom = Atom(
      name: '_ControladorConfiguracaoBase.isMetroDistancia', context: context);

  @override
  bool get isMetroDistancia {
    _$isMetroDistanciaAtom.reportRead();
    return super.isMetroDistancia;
  }

  @override
  set isMetroDistancia(bool value) {
    _$isMetroDistanciaAtom.reportWrite(value, super.isMetroDistancia, () {
      super.isMetroDistancia = value;
    });
  }

  late final _$isLibraCorporalAtom = Atom(
      name: '_ControladorConfiguracaoBase.isLibraCorporal', context: context);

  @override
  bool get isLibraCorporal {
    _$isLibraCorporalAtom.reportRead();
    return super.isLibraCorporal;
  }

  @override
  set isLibraCorporal(bool value) {
    _$isLibraCorporalAtom.reportWrite(value, super.isLibraCorporal, () {
      super.isLibraCorporal = value;
    });
  }

  late final _$isFtAlturaAtom =
      Atom(name: '_ControladorConfiguracaoBase.isFtAltura', context: context);

  @override
  bool get isFtAltura {
    _$isFtAlturaAtom.reportRead();
    return super.isFtAltura;
  }

  @override
  set isFtAltura(bool value) {
    _$isFtAlturaAtom.reportWrite(value, super.isFtAltura, () {
      super.isFtAltura = value;
    });
  }

  late final _$countDownExercicioAtom = Atom(
      name: '_ControladorConfiguracaoBase.countDownExercicio',
      context: context);

  @override
  int? get countDownExercicio {
    _$countDownExercicioAtom.reportRead();
    return super.countDownExercicio;
  }

  @override
  set countDownExercicio(int? value) {
    _$countDownExercicioAtom.reportWrite(value, super.countDownExercicio, () {
      super.countDownExercicio = value;
    });
  }

  late final _$horasEncerrarTreinoAutomaticamenteAtom = Atom(
      name: '_ControladorConfiguracaoBase.horasEncerrarTreinoAutomaticamente',
      context: context);

  @override
  int? get horasEncerrarTreinoAutomaticamente {
    _$horasEncerrarTreinoAutomaticamenteAtom.reportRead();
    return super.horasEncerrarTreinoAutomaticamente;
  }

  @override
  set horasEncerrarTreinoAutomaticamente(int? value) {
    _$horasEncerrarTreinoAutomaticamenteAtom
        .reportWrite(value, super.horasEncerrarTreinoAutomaticamente, () {
      super.horasEncerrarTreinoAutomaticamente = value;
    });
  }

  late final _$previewFotoExecucaoHabilitadoAtom = Atom(
      name: '_ControladorConfiguracaoBase.previewFotoExecucaoHabilitado',
      context: context);

  @override
  bool get previewFotoExecucaoHabilitado {
    _$previewFotoExecucaoHabilitadoAtom.reportRead();
    return super.previewFotoExecucaoHabilitado;
  }

  @override
  set previewFotoExecucaoHabilitado(bool value) {
    _$previewFotoExecucaoHabilitadoAtom
        .reportWrite(value, super.previewFotoExecucaoHabilitado, () {
      super.previewFotoExecucaoHabilitado = value;
    });
  }

  late final _$descansoHabilitadoAtom = Atom(
      name: '_ControladorConfiguracaoBase.descansoHabilitado',
      context: context);

  @override
  bool get descansoHabilitado {
    _$descansoHabilitadoAtom.reportRead();
    return super.descansoHabilitado;
  }

  @override
  set descansoHabilitado(bool value) {
    _$descansoHabilitadoAtom.reportWrite(value, super.descansoHabilitado, () {
      super.descansoHabilitado = value;
    });
  }

  late final _$meusDadosHabilitadoAtom = Atom(
      name: '_ControladorConfiguracaoBase.meusDadosHabilitado',
      context: context);

  @override
  bool get meusDadosHabilitado {
    _$meusDadosHabilitadoAtom.reportRead();
    return super.meusDadosHabilitado;
  }

  @override
  set meusDadosHabilitado(bool value) {
    _$meusDadosHabilitadoAtom.reportWrite(value, super.meusDadosHabilitado, () {
      super.meusDadosHabilitado = value;
    });
  }

  late final _$agiteParaReportarHabilitadoAtom = Atom(
      name: '_ControladorConfiguracaoBase.agiteParaReportarHabilitado',
      context: context);

  @override
  bool get agiteParaReportarHabilitado {
    _$agiteParaReportarHabilitadoAtom.reportRead();
    return super.agiteParaReportarHabilitado;
  }

  @override
  set agiteParaReportarHabilitado(bool value) {
    _$agiteParaReportarHabilitadoAtom
        .reportWrite(value, super.agiteParaReportarHabilitado, () {
      super.agiteParaReportarHabilitado = value;
    });
  }

  late final _$statusAtividadeHabilitadoAtom = Atom(
      name: '_ControladorConfiguracaoBase.statusAtividadeHabilitado',
      context: context);

  @override
  bool get statusAtividadeHabilitado {
    _$statusAtividadeHabilitadoAtom.reportRead();
    return super.statusAtividadeHabilitado;
  }

  @override
  set statusAtividadeHabilitado(bool value) {
    _$statusAtividadeHabilitadoAtom
        .reportWrite(value, super.statusAtividadeHabilitado, () {
      super.statusAtividadeHabilitado = value;
    });
  }

  late final _$avisoSonoroHabilitadoAtom = Atom(
      name: '_ControladorConfiguracaoBase.avisoSonoroHabilitado',
      context: context);

  @override
  bool get avisoSonoroHabilitado {
    _$avisoSonoroHabilitadoAtom.reportRead();
    return super.avisoSonoroHabilitado;
  }

  @override
  set avisoSonoroHabilitado(bool value) {
    _$avisoSonoroHabilitadoAtom.reportWrite(value, super.avisoSonoroHabilitado,
        () {
      super.avisoSonoroHabilitado = value;
    });
  }

  late final _$volumeHabilitadoAtom = Atom(
      name: '_ControladorConfiguracaoBase.volumeHabilitado', context: context);

  @override
  bool get volumeHabilitado {
    _$volumeHabilitadoAtom.reportRead();
    return super.volumeHabilitado;
  }

  @override
  set volumeHabilitado(bool value) {
    _$volumeHabilitadoAtom.reportWrite(value, super.volumeHabilitado, () {
      super.volumeHabilitado = value;
    });
  }

  late final _$descansoExercicioAsyncAction = AsyncAction(
      '_ControladorConfiguracaoBase.descansoExercicio',
      context: context);

  @override
  Future<void> descansoExercicio(bool habilitado) {
    return _$descansoExercicioAsyncAction
        .run(() => super.descansoExercicio(habilitado));
  }

  late final _$consultarDescansoExercicioAsyncAction = AsyncAction(
      '_ControladorConfiguracaoBase.consultarDescansoExercicio',
      context: context);

  @override
  Future consultarDescansoExercicio() {
    return _$consultarDescansoExercicioAsyncAction
        .run(() => super.consultarDescansoExercicio());
  }

  late final _$previewFotoExecucaoAsyncAction = AsyncAction(
      '_ControladorConfiguracaoBase.previewFotoExecucao',
      context: context);

  @override
  Future<void> previewFotoExecucao(bool habilitado) {
    return _$previewFotoExecucaoAsyncAction
        .run(() => super.previewFotoExecucao(habilitado));
  }

  late final _$consultarPreviewFotoExecucaoAsyncAction = AsyncAction(
      '_ControladorConfiguracaoBase.consultarPreviewFotoExecucao',
      context: context);

  @override
  Future consultarPreviewFotoExecucao() {
    return _$consultarPreviewFotoExecucaoAsyncAction
        .run(() => super.consultarPreviewFotoExecucao());
  }

  late final _$consultarMeusDadosAsyncAction = AsyncAction(
      '_ControladorConfiguracaoBase.consultarMeusDados',
      context: context);

  @override
  Future consultarMeusDados() {
    return _$consultarMeusDadosAsyncAction
        .run(() => super.consultarMeusDados());
  }

  late final _$meusDadosAsyncAction =
      AsyncAction('_ControladorConfiguracaoBase.meusDados', context: context);

  @override
  Future<void> meusDados(bool habilitado) {
    return _$meusDadosAsyncAction.run(() => super.meusDados(habilitado));
  }

  late final _$consultarStatusAtividadesAsyncAction = AsyncAction(
      '_ControladorConfiguracaoBase.consultarStatusAtividades',
      context: context);

  @override
  Future consultarStatusAtividades() {
    return _$consultarStatusAtividadesAsyncAction
        .run(() => super.consultarStatusAtividades());
  }

  late final _$statusAtividadeAsyncAction = AsyncAction(
      '_ControladorConfiguracaoBase.statusAtividade',
      context: context);

  @override
  Future<void> statusAtividade(bool habilitado) {
    return _$statusAtividadeAsyncAction
        .run(() => super.statusAtividade(habilitado));
  }

  late final _$avisoSonoroAsyncAction =
      AsyncAction('_ControladorConfiguracaoBase.avisoSonoro', context: context);

  @override
  Future<void> avisoSonoro(bool habilitado) {
    return _$avisoSonoroAsyncAction.run(() => super.avisoSonoro(habilitado));
  }

  late final _$consultarAvisoSonoroAsyncAction = AsyncAction(
      '_ControladorConfiguracaoBase.consultarAvisoSonoro',
      context: context);

  @override
  Future consultarAvisoSonoro() {
    return _$consultarAvisoSonoroAsyncAction
        .run(() => super.consultarAvisoSonoro());
  }

  late final _$consultarAgiteParaReportarAsyncAction = AsyncAction(
      '_ControladorConfiguracaoBase.consultarAgiteParaReportar',
      context: context);

  @override
  Future consultarAgiteParaReportar() {
    return _$consultarAgiteParaReportarAsyncAction
        .run(() => super.consultarAgiteParaReportar());
  }

  late final _$agiteParaReportarAsyncAction = AsyncAction(
      '_ControladorConfiguracaoBase.agiteParaReportar',
      context: context);

  @override
  Future<void> agiteParaReportar(bool habilitado) {
    return _$agiteParaReportarAsyncAction
        .run(() => super.agiteParaReportar(habilitado));
  }

  @override
  String toString() {
    return '''
isMetroDistancia: ${isMetroDistancia},
isLibraCorporal: ${isLibraCorporal},
isFtAltura: ${isFtAltura},
countDownExercicio: ${countDownExercicio},
horasEncerrarTreinoAutomaticamente: ${horasEncerrarTreinoAutomaticamente},
previewFotoExecucaoHabilitado: ${previewFotoExecucaoHabilitado},
descansoHabilitado: ${descansoHabilitado},
meusDadosHabilitado: ${meusDadosHabilitado},
agiteParaReportarHabilitado: ${agiteParaReportarHabilitado},
statusAtividadeHabilitado: ${statusAtividadeHabilitado},
avisoSonoroHabilitado: ${avisoSonoroHabilitado},
volumeHabilitado: ${volumeHabilitado}
    ''';
  }
}
