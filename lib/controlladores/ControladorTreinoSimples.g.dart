// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorTreinoSimples.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorTreinoSimples on _ControladorTreinoSimplesBase, Store {
  Computed<String?>? _$urlVideoComputed;

  @override
  String? get urlVideo =>
      (_$urlVideoComputed ??= Computed<String?>(() => super.urlVideo,
              name: '_ControladorTreinoSimplesBase.urlVideo'))
          .value;
  Computed<int>? _$indexComputed;

  @override
  int get index => (_$indexComputed ??= Computed<int>(() => super.index,
          name: '_ControladorTreinoSimplesBase.index'))
      .value;
  Computed<List<bool>>? _$urlsValidosComputed;

  @override
  List<bool> get urlsValidos =>
      (_$urlsValidosComputed ??= Computed<List<bool>>(() => super.urlsValidos,
              name: '_ControladorTreinoSimplesBase.urlsValidos'))
          .value;
  Computed<List<bool>>? _$urlsInvalidasComputed;

  @override
  List<bool> get urlsInvalidas => (_$urlsInvalidasComputed ??=
          Computed<List<bool>>(() => super.urlsInvalidas,
              name: '_ControladorTreinoSimplesBase.urlsInvalidas'))
      .value;
  Computed<bool>? _$temNomePreenchidoComputed;

  @override
  bool get temNomePreenchido => (_$temNomePreenchidoComputed ??= Computed<bool>(
          () => super.temNomePreenchido,
          name: '_ControladorTreinoSimplesBase.temNomePreenchido'))
      .value;
  Computed<bool>? _$temDescricaoPreenchidaComputed;

  @override
  bool get temDescricaoPreenchida => (_$temDescricaoPreenchidaComputed ??=
          Computed<bool>(() => super.temDescricaoPreenchida,
              name: '_ControladorTreinoSimplesBase.temDescricaoPreenchida'))
      .value;
  Computed<bool>? _$temUrlValidaComputed;

  @override
  bool get temUrlValida =>
      (_$temUrlValidaComputed ??= Computed<bool>(() => super.temUrlValida,
              name: '_ControladorTreinoSimplesBase.temUrlValida'))
          .value;
  Computed<bool>? _$temDetalhesSelecionadosComputed;

  @override
  bool get temDetalhesSelecionados => (_$temDetalhesSelecionadosComputed ??=
          Computed<bool>(() => super.temDetalhesSelecionados,
              name: '_ControladorTreinoSimplesBase.temDetalhesSelecionados'))
      .value;
  Computed<bool>? _$temImagemSelecionadaComputed;

  @override
  bool get temImagemSelecionada => (_$temImagemSelecionadaComputed ??=
          Computed<bool>(() => super.temImagemSelecionada,
              name: '_ControladorTreinoSimplesBase.temImagemSelecionada'))
      .value;
  Computed<bool>? _$podeAdicionarNovoVideoComputed;

  @override
  bool get podeAdicionarNovoVideo => (_$podeAdicionarNovoVideoComputed ??=
          Computed<bool>(() => super.podeAdicionarNovoVideo,
              name: '_ControladorTreinoSimplesBase.podeAdicionarNovoVideo'))
      .value;

  late final _$usuarioAppAtom =
      Atom(name: '_ControladorTreinoSimplesBase.usuarioApp', context: context);

  @override
  UsuarioApp get usuarioApp {
    _$usuarioAppAtom.reportRead();
    return super.usuarioApp;
  }

  @override
  set usuarioApp(UsuarioApp value) {
    _$usuarioAppAtom.reportWrite(value, super.usuarioApp, () {
      super.usuarioApp = value;
    });
  }

  late final _$treinoCriadoAtom = Atom(
      name: '_ControladorTreinoSimplesBase.treinoCriado', context: context);

  @override
  Treino? get treinoCriado {
    _$treinoCriadoAtom.reportRead();
    return super.treinoCriado;
  }

  @override
  set treinoCriado(Treino? value) {
    _$treinoCriadoAtom.reportWrite(value, super.treinoCriado, () {
      super.treinoCriado = value;
    });
  }

  late final _$carregandoInfoAtom = Atom(
      name: '_ControladorTreinoSimplesBase.carregandoInfo', context: context);

  @override
  bool get carregandoInfo {
    _$carregandoInfoAtom.reportRead();
    return super.carregandoInfo;
  }

  @override
  set carregandoInfo(bool value) {
    _$carregandoInfoAtom.reportWrite(value, super.carregandoInfo, () {
      super.carregandoInfo = value;
    });
  }

  late final _$errorINVOKEAtom =
      Atom(name: '_ControladorTreinoSimplesBase.errorINVOKE', context: context);

  @override
  String? get errorINVOKE {
    _$errorINVOKEAtom.reportRead();
    return super.errorINVOKE;
  }

  @override
  set errorINVOKE(String? value) {
    _$errorINVOKEAtom.reportWrite(value, super.errorINVOKE, () {
      super.errorINVOKE = value;
    });
  }

  late final _$eProfessorAtom =
      Atom(name: '_ControladorTreinoSimplesBase.eProfessor', context: context);

  @override
  bool? get eProfessor {
    _$eProfessorAtom.reportRead();
    return super.eProfessor;
  }

  @override
  set eProfessor(bool? value) {
    _$eProfessorAtom.reportWrite(value, super.eProfessor, () {
      super.eProfessor = value;
    });
  }

  late final _$logoAcademiaBase64Atom = Atom(
      name: '_ControladorTreinoSimplesBase.logoAcademiaBase64',
      context: context);

  @override
  String get logoAcademiaBase64 {
    _$logoAcademiaBase64Atom.reportRead();
    return super.logoAcademiaBase64;
  }

  @override
  set logoAcademiaBase64(String value) {
    _$logoAcademiaBase64Atom.reportWrite(value, super.logoAcademiaBase64, () {
      super.logoAcademiaBase64 = value;
    });
  }

  late final _$mListaUrlsVideosAtom = Atom(
      name: '_ControladorTreinoSimplesBase.mListaUrlsVideos', context: context);

  @override
  ObservableList<Atividades> get mListaUrlsVideos {
    _$mListaUrlsVideosAtom.reportRead();
    return super.mListaUrlsVideos;
  }

  @override
  set mListaUrlsVideos(ObservableList<Atividades> value) {
    _$mListaUrlsVideosAtom.reportWrite(value, super.mListaUrlsVideos, () {
      super.mListaUrlsVideos = value;
    });
  }

  late final _$estaFiltrandoAtom = Atom(
      name: '_ControladorTreinoSimplesBase.estaFiltrando', context: context);

  @override
  bool get estaFiltrando {
    _$estaFiltrandoAtom.reportRead();
    return super.estaFiltrando;
  }

  @override
  set estaFiltrando(bool value) {
    _$estaFiltrandoAtom.reportWrite(value, super.estaFiltrando, () {
      super.estaFiltrando = value;
    });
  }

  late final _$filtroAtom =
      Atom(name: '_ControladorTreinoSimplesBase.filtro', context: context);

  @override
  Filtro get filtro {
    _$filtroAtom.reportRead();
    return super.filtro;
  }

  @override
  set filtro(Filtro value) {
    _$filtroAtom.reportWrite(value, super.filtro, () {
      super.filtro = value;
    });
  }

  late final _$listaVideoAjudaAtom = Atom(
      name: '_ControladorTreinoSimplesBase.listaVideoAjuda', context: context);

  @override
  ObservableList<VideoAjuda> get listaVideoAjuda {
    _$listaVideoAjudaAtom.reportRead();
    return super.listaVideoAjuda;
  }

  @override
  set listaVideoAjuda(ObservableList<VideoAjuda> value) {
    _$listaVideoAjudaAtom.reportWrite(value, super.listaVideoAjuda, () {
      super.listaVideoAjuda = value;
    });
  }

  late final _$listaItensSelecionadosStringAtom = Atom(
      name: '_ControladorTreinoSimplesBase.listaItensSelecionadosString',
      context: context);

  @override
  ObservableList<String?> get listaItensSelecionadosString {
    _$listaItensSelecionadosStringAtom.reportRead();
    return super.listaItensSelecionadosString;
  }

  @override
  set listaItensSelecionadosString(ObservableList<String?> value) {
    _$listaItensSelecionadosStringAtom
        .reportWrite(value, super.listaItensSelecionadosString, () {
      super.listaItensSelecionadosString = value;
    });
  }

  late final _$filtrosAlunoAtom = Atom(
      name: '_ControladorTreinoSimplesBase.filtrosAluno', context: context);

  @override
  ObservableList<EstruturaDetalhes> get filtrosAluno {
    _$filtrosAlunoAtom.reportRead();
    return super.filtrosAluno;
  }

  @override
  set filtrosAluno(ObservableList<EstruturaDetalhes> value) {
    _$filtrosAlunoAtom.reportWrite(value, super.filtrosAluno, () {
      super.filtrosAluno = value;
    });
  }

  late final _$listaDetalhesAtom = Atom(
      name: '_ControladorTreinoSimplesBase.listaDetalhes', context: context);

  @override
  ObservableList<EstruturaDetalhes> get listaDetalhes {
    _$listaDetalhesAtom.reportRead();
    return super.listaDetalhes;
  }

  @override
  set listaDetalhes(ObservableList<EstruturaDetalhes> value) {
    _$listaDetalhesAtom.reportWrite(value, super.listaDetalhes, () {
      super.listaDetalhes = value;
    });
  }

  late final _$listaOriginalAtom = Atom(
      name: '_ControladorTreinoSimplesBase.listaOriginal', context: context);

  @override
  ObservableList<Treino> get listaOriginal {
    _$listaOriginalAtom.reportRead();
    return super.listaOriginal;
  }

  @override
  set listaOriginal(ObservableList<Treino> value) {
    _$listaOriginalAtom.reportWrite(value, super.listaOriginal, () {
      super.listaOriginal = value;
    });
  }

  late final _$estadoFiltrosAtom = Atom(
      name: '_ControladorTreinoSimplesBase.estadoFiltros', context: context);

  @override
  Map<String, bool> get estadoFiltros {
    _$estadoFiltrosAtom.reportRead();
    return super.estadoFiltros;
  }

  @override
  set estadoFiltros(Map<String, bool> value) {
    _$estadoFiltrosAtom.reportWrite(value, super.estadoFiltros, () {
      super.estadoFiltros = value;
    });
  }

  late final _$filtrosSelecionadosAtom = Atom(
      name: '_ControladorTreinoSimplesBase.filtrosSelecionados',
      context: context);

  @override
  ObservableList<String> get filtrosSelecionados {
    _$filtrosSelecionadosAtom.reportRead();
    return super.filtrosSelecionados;
  }

  @override
  set filtrosSelecionados(ObservableList<String> value) {
    _$filtrosSelecionadosAtom.reportWrite(value, super.filtrosSelecionados, () {
      super.filtrosSelecionados = value;
    });
  }

  late final _$statusConsultaAtom = Atom(
      name: '_ControladorTreinoSimplesBase.statusConsulta', context: context);

  @override
  ServiceStatus get statusConsulta {
    _$statusConsultaAtom.reportRead();
    return super.statusConsulta;
  }

  @override
  set statusConsulta(ServiceStatus value) {
    _$statusConsultaAtom.reportWrite(value, super.statusConsulta, () {
      super.statusConsulta = value;
    });
  }

  late final _$usuarioPodeAcessarAtom = Atom(
      name: '_ControladorTreinoSimplesBase.usuarioPodeAcessar',
      context: context);

  @override
  bool? get usuarioPodeAcessar {
    _$usuarioPodeAcessarAtom.reportRead();
    return super.usuarioPodeAcessar;
  }

  @override
  set usuarioPodeAcessar(bool? value) {
    _$usuarioPodeAcessarAtom.reportWrite(value, super.usuarioPodeAcessar, () {
      super.usuarioPodeAcessar = value;
    });
  }

  late final _$listaFiltrosAtom = Atom(
      name: '_ControladorTreinoSimplesBase.listaFiltros', context: context);

  @override
  ObservableList<Filtro> get listaFiltros {
    _$listaFiltrosAtom.reportRead();
    return super.listaFiltros;
  }

  @override
  set listaFiltros(ObservableList<Filtro> value) {
    _$listaFiltrosAtom.reportWrite(value, super.listaFiltros, () {
      super.listaFiltros = value;
    });
  }

  late final _$mQuemExecutouAtom = Atom(
      name: '_ControladorTreinoSimplesBase.mQuemExecutou', context: context);

  @override
  ObservableList<ExecucaoTreino> get mQuemExecutou {
    _$mQuemExecutouAtom.reportRead();
    return super.mQuemExecutou;
  }

  @override
  set mQuemExecutou(ObservableList<ExecucaoTreino> value) {
    _$mQuemExecutouAtom.reportWrite(value, super.mQuemExecutou, () {
      super.mQuemExecutou = value;
    });
  }

  late final _$isLoadingExecucoesAtom = Atom(
      name: '_ControladorTreinoSimplesBase.isLoadingExecucoes',
      context: context);

  @override
  bool get isLoadingExecucoes {
    _$isLoadingExecucoesAtom.reportRead();
    return super.isLoadingExecucoes;
  }

  @override
  set isLoadingExecucoes(bool value) {
    _$isLoadingExecucoesAtom.reportWrite(value, super.isLoadingExecucoes, () {
      super.isLoadingExecucoes = value;
    });
  }

  late final _$mRankingAcademiaAtom = Atom(
      name: '_ControladorTreinoSimplesBase.mRankingAcademia', context: context);

  @override
  ObservableList<RankingInfo> get mRankingAcademia {
    _$mRankingAcademiaAtom.reportRead();
    return super.mRankingAcademia;
  }

  @override
  set mRankingAcademia(ObservableList<RankingInfo> value) {
    _$mRankingAcademiaAtom.reportWrite(value, super.mRankingAcademia, () {
      super.mRankingAcademia = value;
    });
  }

  late final _$statusCarregarRankingAtom = Atom(
      name: '_ControladorTreinoSimplesBase.statusCarregarRanking',
      context: context);

  @override
  STATUSREQUEST get statusCarregarRanking {
    _$statusCarregarRankingAtom.reportRead();
    return super.statusCarregarRanking;
  }

  @override
  set statusCarregarRanking(STATUSREQUEST value) {
    _$statusCarregarRankingAtom.reportWrite(value, super.statusCarregarRanking,
        () {
      super.statusCarregarRanking = value;
    });
  }

  late final _$mListaTreinosSimplesAtom = Atom(
      name: '_ControladorTreinoSimplesBase.mListaTreinosSimples',
      context: context);

  @override
  ObservableList<Treino> get mListaTreinosSimples {
    _$mListaTreinosSimplesAtom.reportRead();
    return super.mListaTreinosSimples;
  }

  @override
  set mListaTreinosSimples(ObservableList<Treino> value) {
    _$mListaTreinosSimplesAtom.reportWrite(value, super.mListaTreinosSimples,
        () {
      super.mListaTreinosSimples = value;
    });
  }

  late final _$statusCarregarTreinosAtom = Atom(
      name: '_ControladorTreinoSimplesBase.statusCarregarTreinos',
      context: context);

  @override
  STATUSREQUEST get statusCarregarTreinos {
    _$statusCarregarTreinosAtom.reportRead();
    return super.statusCarregarTreinos;
  }

  @override
  set statusCarregarTreinos(STATUSREQUEST value) {
    _$statusCarregarTreinosAtom.reportWrite(value, super.statusCarregarTreinos,
        () {
      super.statusCarregarTreinos = value;
    });
  }

  late final _$urlsVideosAtom =
      Atom(name: '_ControladorTreinoSimplesBase.urlsVideos', context: context);

  @override
  ObservableList<String> get urlsVideos {
    _$urlsVideosAtom.reportRead();
    return super.urlsVideos;
  }

  @override
  set urlsVideos(ObservableList<String> value) {
    _$urlsVideosAtom.reportWrite(value, super.urlsVideos, () {
      super.urlsVideos = value;
    });
  }

  late final _$urlsStatusAtom =
      Atom(name: '_ControladorTreinoSimplesBase.urlsStatus', context: context);

  @override
  ObservableList<VideoValidationStatus> get urlsStatus {
    _$urlsStatusAtom.reportRead();
    return super.urlsStatus;
  }

  @override
  set urlsStatus(ObservableList<VideoValidationStatus> value) {
    _$urlsStatusAtom.reportWrite(value, super.urlsStatus, () {
      super.urlsStatus = value;
    });
  }

  late final _$detalhesTreinoSelecionadosAtom = Atom(
      name: '_ControladorTreinoSimplesBase.detalhesTreinoSelecionados',
      context: context);

  @override
  ObservableList<String> get detalhesTreinoSelecionados {
    _$detalhesTreinoSelecionadosAtom.reportRead();
    return super.detalhesTreinoSelecionados;
  }

  @override
  set detalhesTreinoSelecionados(ObservableList<String> value) {
    _$detalhesTreinoSelecionadosAtom
        .reportWrite(value, super.detalhesTreinoSelecionados, () {
      super.detalhesTreinoSelecionados = value;
    });
  }

  late final _$imagemTreinoAtom = Atom(
      name: '_ControladorTreinoSimplesBase.imagemTreino', context: context);

  @override
  String? get imagemTreino {
    _$imagemTreinoAtom.reportRead();
    return super.imagemTreino;
  }

  @override
  set imagemTreino(String? value) {
    _$imagemTreinoAtom.reportWrite(value, super.imagemTreino, () {
      super.imagemTreino = value;
    });
  }

  late final _$selectedImagePathAtom = Atom(
      name: '_ControladorTreinoSimplesBase.selectedImagePath',
      context: context);

  @override
  String? get selectedImagePath {
    _$selectedImagePathAtom.reportRead();
    return super.selectedImagePath;
  }

  @override
  set selectedImagePath(String? value) {
    _$selectedImagePathAtom.reportWrite(value, super.selectedImagePath, () {
      super.selectedImagePath = value;
    });
  }

  late final _$imageUIntAtom =
      Atom(name: '_ControladorTreinoSimplesBase.imageUInt', context: context);

  @override
  Uint8List? get imageUInt {
    _$imageUIntAtom.reportRead();
    return super.imageUInt;
  }

  @override
  set imageUInt(Uint8List? value) {
    _$imageUIntAtom.reportWrite(value, super.imageUInt, () {
      super.imageUInt = value;
    });
  }

  late final _$dadosTreinoAtom =
      Atom(name: '_ControladorTreinoSimplesBase.dadosTreino', context: context);

  @override
  Treino? get dadosTreino {
    _$dadosTreinoAtom.reportRead();
    return super.dadosTreino;
  }

  @override
  set dadosTreino(Treino? value) {
    _$dadosTreinoAtom.reportWrite(value, super.dadosTreino, () {
      super.dadosTreino = value;
    });
  }

  late final _$removerTreinoAsyncAction = AsyncAction(
      '_ControladorTreinoSimplesBase.removerTreino',
      context: context);

  @override
  Future<void> removerTreino(
      {required Treino treino,
      Function? callback,
      required BuildContext context}) {
    return _$removerTreinoAsyncAction.run(() => super
        .removerTreino(treino: treino, callback: callback, context: context));
  }

  late final _$getQuemRealizouOTreinoAsyncAction = AsyncAction(
      '_ControladorTreinoSimplesBase.getQuemRealizouOTreino',
      context: context);

  @override
  Future<void> getQuemRealizouOTreino(
      Treino treino, dynamic Function() onComplete) {
    return _$getQuemRealizouOTreinoAsyncAction
        .run(() => super.getQuemRealizouOTreino(treino, onComplete));
  }

  late final _$atualizarTreinoAsyncAction = AsyncAction(
      '_ControladorTreinoSimplesBase.atualizarTreino',
      context: context);

  @override
  Future<void> atualizarTreino(Treino treino) {
    return _$atualizarTreinoAsyncAction
        .run(() => super.atualizarTreino(treino));
  }

  late final _$marcarExeucaoAsyncAction = AsyncAction(
      '_ControladorTreinoSimplesBase.marcarExeucao',
      context: context);

  @override
  Future<void> marcarExeucao(
      {required Treino treino,
      required dynamic Function(STATUSREQUEST) callback}) {
    return _$marcarExeucaoAsyncAction
        .run(() => super.marcarExeucao(treino: treino, callback: callback));
  }

  late final _$setUrlVideoAsyncAction = AsyncAction(
      '_ControladorTreinoSimplesBase.setUrlVideo',
      context: context);

  @override
  Future<void> setUrlVideo(int index, String value, BuildContext context) {
    return _$setUrlVideoAsyncAction
        .run(() => super.setUrlVideo(index, value, context));
  }

  late final _$adicionarVideoSemPoupapAsyncAction = AsyncAction(
      '_ControladorTreinoSimplesBase.adicionarVideoSemPoupap',
      context: context);

  @override
  Future<void> adicionarVideoSemPoupap(
      {String? urlVideo, required BuildContext context, String? descricao}) {
    return _$adicionarVideoSemPoupapAsyncAction.run(() => super
        .adicionarVideoSemPoupap(
            urlVideo: urlVideo, context: context, descricao: descricao));
  }

  late final _$_ControladorTreinoSimplesBaseActionController =
      ActionController(name: '_ControladorTreinoSimplesBase', context: context);

  @override
  void salvarEstadoFiltros() {
    final _$actionInfo = _$_ControladorTreinoSimplesBaseActionController
        .startAction(name: '_ControladorTreinoSimplesBase.salvarEstadoFiltros');
    try {
      return super.salvarEstadoFiltros();
    } finally {
      _$_ControladorTreinoSimplesBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void restaurarEstadoFiltros() {
    final _$actionInfo =
        _$_ControladorTreinoSimplesBaseActionController.startAction(
            name: '_ControladorTreinoSimplesBase.restaurarEstadoFiltros');
    try {
      return super.restaurarEstadoFiltros();
    } finally {
      _$_ControladorTreinoSimplesBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void filtrarLista() {
    final _$actionInfo = _$_ControladorTreinoSimplesBaseActionController
        .startAction(name: '_ControladorTreinoSimplesBase.filtrarLista');
    try {
      return super.filtrarLista();
    } finally {
      _$_ControladorTreinoSimplesBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void limparFiltros() {
    final _$actionInfo = _$_ControladorTreinoSimplesBaseActionController
        .startAction(name: '_ControladorTreinoSimplesBase.limparFiltros');
    try {
      return super.limparFiltros();
    } finally {
      _$_ControladorTreinoSimplesBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void initializeUrlStatus() {
    final _$actionInfo = _$_ControladorTreinoSimplesBaseActionController
        .startAction(name: '_ControladorTreinoSimplesBase.initializeUrlStatus');
    try {
      return super.initializeUrlStatus();
    } finally {
      _$_ControladorTreinoSimplesBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setNome(String value) {
    final _$actionInfo = _$_ControladorTreinoSimplesBaseActionController
        .startAction(name: '_ControladorTreinoSimplesBase.setNome');
    try {
      return super.setNome(value);
    } finally {
      _$_ControladorTreinoSimplesBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setDescricao(String value) {
    final _$actionInfo = _$_ControladorTreinoSimplesBaseActionController
        .startAction(name: '_ControladorTreinoSimplesBase.setDescricao');
    try {
      return super.setDescricao(value);
    } finally {
      _$_ControladorTreinoSimplesBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void removerUrl(int index) {
    final _$actionInfo = _$_ControladorTreinoSimplesBaseActionController
        .startAction(name: '_ControladorTreinoSimplesBase.removerUrl');
    try {
      return super.removerUrl(index);
    } finally {
      _$_ControladorTreinoSimplesBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void limparUrl(int index) {
    final _$actionInfo = _$_ControladorTreinoSimplesBaseActionController
        .startAction(name: '_ControladorTreinoSimplesBase.limparUrl');
    try {
      return super.limparUrl(index);
    } finally {
      _$_ControladorTreinoSimplesBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void adicionarNovoVideo() {
    final _$actionInfo = _$_ControladorTreinoSimplesBaseActionController
        .startAction(name: '_ControladorTreinoSimplesBase.adicionarNovoVideo');
    try {
      return super.adicionarNovoVideo();
    } finally {
      _$_ControladorTreinoSimplesBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void limparTudo() {
    final _$actionInfo = _$_ControladorTreinoSimplesBaseActionController
        .startAction(name: '_ControladorTreinoSimplesBase.limparTudo');
    try {
      return super.limparTudo();
    } finally {
      _$_ControladorTreinoSimplesBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
usuarioApp: ${usuarioApp},
treinoCriado: ${treinoCriado},
carregandoInfo: ${carregandoInfo},
errorINVOKE: ${errorINVOKE},
eProfessor: ${eProfessor},
logoAcademiaBase64: ${logoAcademiaBase64},
mListaUrlsVideos: ${mListaUrlsVideos},
estaFiltrando: ${estaFiltrando},
filtro: ${filtro},
listaVideoAjuda: ${listaVideoAjuda},
listaItensSelecionadosString: ${listaItensSelecionadosString},
filtrosAluno: ${filtrosAluno},
listaDetalhes: ${listaDetalhes},
listaOriginal: ${listaOriginal},
estadoFiltros: ${estadoFiltros},
filtrosSelecionados: ${filtrosSelecionados},
statusConsulta: ${statusConsulta},
usuarioPodeAcessar: ${usuarioPodeAcessar},
listaFiltros: ${listaFiltros},
mQuemExecutou: ${mQuemExecutou},
isLoadingExecucoes: ${isLoadingExecucoes},
mRankingAcademia: ${mRankingAcademia},
statusCarregarRanking: ${statusCarregarRanking},
mListaTreinosSimples: ${mListaTreinosSimples},
statusCarregarTreinos: ${statusCarregarTreinos},
urlsVideos: ${urlsVideos},
urlsStatus: ${urlsStatus},
detalhesTreinoSelecionados: ${detalhesTreinoSelecionados},
imagemTreino: ${imagemTreino},
selectedImagePath: ${selectedImagePath},
imageUInt: ${imageUInt},
dadosTreino: ${dadosTreino},
urlVideo: ${urlVideo},
index: ${index},
urlsValidos: ${urlsValidos},
urlsInvalidas: ${urlsInvalidas},
temNomePreenchido: ${temNomePreenchido},
temDescricaoPreenchida: ${temDescricaoPreenchida},
temUrlValida: ${temUrlValida},
temDetalhesSelecionados: ${temDetalhesSelecionados},
temImagemSelecionada: ${temImagemSelecionada},
podeAdicionarNovoVideo: ${podeAdicionarNovoVideo}
    ''';
  }
}
