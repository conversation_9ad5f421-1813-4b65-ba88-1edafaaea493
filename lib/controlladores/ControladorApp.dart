// ignore_for_file: invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member, invalid_return_type_for_catch_error

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:app_treino/config/IconsTabbar.dart';
import 'package:app_treino/controlladores/ControladorAgendamento.dart';
import 'package:app_treino/controlladores/ControladorAppLoading.dart';
import 'package:app_treino/controlladores/ControladorExecucaoTreino.dart';
import 'package:app_treino/controlladores/ControladorSplash.dart';
import 'package:app_treino/controlladores/ControladorTreinoSimples.dart';
import 'package:app_treino/controlladores/ControladorVendaDePlano.dart';
import 'package:app_treino/fabricaGetIt.dart';
import 'package:app_treino/model/doClienteApp/UrlDiscorver.dart';
import 'package:app_treino/model/doClienteApp/UsuarioKeep.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/EmpresaRede.dart';
import 'package:app_treino/screens/_treino6/TelaPerfilAluno.dart';
import 'package:app_treino/screens/_treino6/TelaPerfilProfessor.dart';
import 'package:app_treino/screens/_treino6/TelaTreino.dart';
import 'package:app_treino/screens/_treino6/modulo_nutri_fitstream/TelaNutricaoFitstream.dart';
import 'package:app_treino/screens/agenda_treino6/NovaTelaAgendaAluno.dart';
import 'package:app_treino/screens/aulasETurmas/TelaAulaTurmasProfessor.dart';
import 'package:app_treino/screens/aulasETurmas/TelaAulasTurmas.dart';
import 'package:app_treino/screens/meuDia/TelaInicioAluno.dart';
import 'package:app_treino/screens/novaDashboardColaborador/TelaNovaDashboard.dart';
import 'package:app_treino/screens/_treino6/TelaNewCross.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaListaAlunos.dart';
import 'package:app_treino/screens/vitio/moduloSaude/TelaNovaHomeSaude.dart';
import 'package:app_treino/util/n2b/fix_saldo.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/foundation.dart';
import 'package:app_treino/ServiceProvider/authServices/ClienteAppService.dart';
import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/controlladores/ControladorAulaTurma.dart';
import 'package:app_treino/controlladores/ControladorAvaliacaoFisica.dart';
import 'package:app_treino/controlladores/ControladorBeberAgua.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorContratoUsuario.dart';
import 'package:app_treino/controlladores/ControladorDBExtend.dart';
import 'package:app_treino/controlladores/ControladorDashBoardPersonal.dart';
import 'package:app_treino/controlladores/ControladorEdicaoiExercicio.dart';
import 'package:app_treino/controlladores/ControladorFeed.dart';
import 'package:app_treino/controlladores/ControladorIndicacao.dart';
import 'package:app_treino/controlladores/ControladorManterAluno.dart';
import 'package:app_treino/controlladores/ControladorManterExercicio.dart';
import 'package:app_treino/controlladores/ControladorManterPerfil.dart';
import 'package:app_treino/controlladores/ControladorNPS.dart';
import 'package:app_treino/controlladores/ControladorNotificacoes.dart';
import 'package:app_treino/controlladores/ControladorNotificacoesCrm.dart';
import 'package:app_treino/controlladores/ControladorObservacoesAluno.dart';
import 'package:app_treino/controlladores/ControladorPlanoDeTreino.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoDeTreino.dart';
import 'package:app_treino/controlladores/ControladorTreinoAluno.dart';
import 'package:app_treino/controlladores/ControladorTreinosPreDefinidos.dart';
import 'package:app_treino/controlladores/ControladorWod.dart';
import 'package:app_treino/controlladores/controladorPlanner.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/UserDataKeys.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/doClienteApp/ConfiguracoesTreinoWeb.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/Usuario.dart';
import 'package:app_treino/model/transicao/DadosTransicao.dart';
import 'package:app_treino/model/util/UtilColor.dart';
import 'package:diacritic/diacritic.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';

import 'package:shared_preferences/shared_preferences.dart';

import 'package:app_treino/controlladores/ControladorVendasOnline.dart';
part 'ControladorApp.g.dart';

class ControladorApp = _ControladorAppBase with _$ControladorApp;

abstract class _ControladorAppBase extends UtilDataBase with Store {
  bool desabilitarNovoFluxoLogin = false;
  bool userDataInHeader = false;
  final ScreenshotController mainScreenShotController = ScreenshotController();
  UrlDiscorver? mUrls;
  @observable
  ObservableList<ClienteApp> mClientesApp = ObservableList<ClienteApp>();
  @observable
  ObservableList<ClienteApp> mClientesAppPesquisa = ObservableList<ClienteApp>();
  @observable
  ObservableList<EmpresaApp> mEmpresasAppPequisa = ObservableList<EmpresaApp>();
  @observable
  ObservableList<ConfiguracaoTreino> mConfiguracoesTreinoWeb = ObservableList<ConfiguracaoTreino>();
  @observable
  bool pularSenhaLogin = false, mostrarLogHttp = false, utilizaDescovery = true;
  bool get vendasHabilitadas {
    return (mClienteAppSelecionado?.linkVendasOnline?.isNotEmpty ?? false) && (getConfiguracaoApp(ModuloApp.MODULO_VENDAS_DE_PLANOS).habilitado ?? false);
  }

  String? get n2bExternalPartnerId {
    return mClienteAppSelecionado?.currentN2bIDExternal;
  }

  String? get n2bExternalPartnerIdOrigemUsuario {
    try {
      String chave_id = '${GetIt.I.get<ControladorApp>().chave}_${GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa}';
      return mClienteAppSelecionado?.idsN2b?.entries.firstWhere((element) => element.key == chave_id).value;
    } catch (e) {
      return null;
    }
  }

  String? get n2bPartnerId {
    return mClienteAppSelecionado?.currentN2bID;
  }

  UsuarioKeep? userRelogin;
  @observable
  bool veioDoSplash = true;
  @observable
  String? chave;
  @observable
  Color? defaultColor;
  @observable
  ServiceStatus statusCarregamentoListaClienteApp = ServiceStatus.Done;
  @observable
  ServiceStatus statusCarregandoAcademias = ServiceStatus.Waiting;
  bool pesquisandoOnline = false;
  @observable
  int indexCardTreino = 0;

  @observable
  AppLifecycleState estadoApp = AppLifecycleState.resumed;

  Color? corSeparadorComTexto() => themeMode == ThemeMode.dark ? Colors.grey[900] : Colors.grey[300];

  String termoJaPesquisado = '';

  Timer? _debounce;

  @observable
  ThemeMode? themeMode = WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.light ? ThemeMode.light : ThemeMode.dark;

  bool themeModeSystem = true;

  @observable
  Locale? appLocale;

  @action
  void setAppLocale(Locale locale) {
    appLocale = locale;
  }

  @observable
  ClienteApp? mClienteAppSelecionado;
  bool jaSetouLinks = false;

  PackageInfo? packageInfo;

  String _userAgent = '';

  @observable
  ObservableList<Widget> telaTabbar = ObservableList<Widget>();

  @observable
  int indexTabbar = 0;

  @observable
  PageController controller = PageController(keepPage: true, initialPage: 0);

  @observable
  ObservableList<BottomNavigationBarItem> itensBottomBar = ObservableList<BottomNavigationBarItem>();

  List<BottomNavigationBarItem> get getItensBottomBar {
    return itensBottomBar.map((element) {
      return BottomNavigationBarItem(
          icon: element.icon, activeIcon: element.activeIcon, backgroundColor: element.backgroundColor, label: localizedString(element.label!), tooltip: element.tooltip);
    }).toList();
  }

  @action
  void updateEstadoApp(AppLifecycleState novoEstado) {
    estadoApp = novoEstado;
  }

  @action
  irParaMenuAulas() {
    for (int i = 0; i < itensBottomBar.length; i++) {
      if (itensBottomBar[i].label == localizedString('navibar.aulas')) {
        GetIt.I.get<ControladorApp>().indexTabbar = i;
        GetIt.I.get<ControladorApp>().controller.animateToPage(i, duration: const Duration(milliseconds: 1), curve: Curves.ease);
        return;
      }
    }
  }

  @action
  irParaSaude() {
    for (int i = 0; i < itensBottomBar.length; i++) {
      if (itensBottomBar[i].label == localizedString('Saúde')) {
        GetIt.I.get<ControladorApp>().indexTabbar = i;
        GetIt.I.get<ControladorApp>().controller.animateToPage(i, duration: const Duration(milliseconds: 1), curve: Curves.ease);
        return;
      }
    }
  }

  /**
   * Prepara as telas e a tabbar baseada nas configurações do Mobile center. 
   * A ordem dos modulos (Treino e WOD) é definida pelo tipo do APP (Flavor), se é buildado o Treino, a parte de fichas aparece primeiro, se for buildado o meu Box a parte de Wod aparece primeiro.
   * Se for um colaborador logado, a ordem é alterada porém segue a mesma lógica. 
   * Se a quantidade de elementos não for suficiente, adicionamos a tela de agenda e a tela de perfil pra completar no mínimo 3 itens.
   */
  prepararTelasBaseadoNasConfiguracoes({BuildContext? context, required bool eColaborador, required Flavor tipoApp}) {
    controller.dispose();
    controller = PageController(keepPage: true, initialPage: 0);
    telaTabbar.clear();
    itensBottomBar.clear();
    //INDEX 1
    //COLABORADOR: TELA DASHBOARD
    //ALUNO: TELA INICIO ALUNO
    telaTabbar.add(eColaborador ? TelaDashboard() : TelaInicioAluno());
    itensBottomBar
        .add(BottomNavigationBarItem(icon: IconsTabbar().icone(icone: IconeTabbar.inicio), activeIcon: IconsTabbar().icone(icone: IconeTabbar.inicio_ativo), label: ('navibar.inicio')));
    //INDEX 2
    //COLABORADOR: TELA AULAS PROFESSOR
    //ALUNO: TELA AULAS ALUNO
    if (getConfiguracaoApp(ModuloApp.MODULO_AULAS).habilitado!) {
      telaTabbar.add(eColaborador ? TelaAulaTurmasProfessor() : TelaAulasTurmas());
      itensBottomBar
          .add(BottomNavigationBarItem(icon: IconsTabbar().icone(icone: IconeTabbar.aulas), activeIcon: IconsTabbar().icone(icone: IconeTabbar.aulas_ativo), label: ('navibar.aulas')));
    }
    switch (tipoApp) {
      case Flavor.BOX:
        //INDEX 3
        //COLABORADOR: TELA LISTA ALUNO
        //ALUNO: TELA CROSSFIT
        if (eColaborador) {
          telaTabbar.add(TelaListaAlunos());
          itensBottomBar.add(BottomNavigationBarItem(
              icon: IconsTabbar().icone(icone: IconeTabbar.listaAlunos), activeIcon: IconsTabbar().icone(icone: IconeTabbar.listaAlunos_ativo), label: ('navibar.alunos')));
        } else if (getConfiguracaoApp(ModuloApp.MODULO_CROSS).habilitado!) {
          telaTabbar.add(TelaNewCross());
          itensBottomBar
              .add(BottomNavigationBarItem(icon: IconsTabbar().icone(icone: IconeTabbar.cross), activeIcon: IconsTabbar().icone(icone: IconeTabbar.cross_ativo), label: ('navibar.cross')));
        }
        //INDEX 4
        //COLABORADOR: TELA CROSSFIT
        //ALUNO: TELA TREINO
        if (eColaborador) {
          if (getConfiguracaoApp(ModuloApp.MODULO_CROSS).habilitado!) {
            telaTabbar.add(TelaNewCross());
            itensBottomBar
                .add(BottomNavigationBarItem(icon: IconsTabbar().icone(icone: IconeTabbar.cross), activeIcon: IconsTabbar().icone(icone: IconeTabbar.cross_ativo), label: ('navibar.cross')));
          }
        } else {
          if (getConfiguracaoApp(ModuloApp.MODULO_TREINO).habilitado!) {
            telaTabbar.add(TelaTreino());
            itensBottomBar.add(
                BottomNavigationBarItem(icon: IconsTabbar().icone(icone: IconeTabbar.treino), activeIcon: IconsTabbar().icone(icone: IconeTabbar.treino_ativo), label: ('navibar.treino')));
          }
        }
      default:
        //INDEX 3
        //COLABORADOR: TELA AULAS PROFESSOR
        //ALUNO: TELA AULAS ALUNO
        if (getConfiguracaoApp(ModuloApp.MODULO_TREINO).habilitado! && !eColaborador) {
          telaTabbar.add(TelaTreino());
          itensBottomBar.add(
              BottomNavigationBarItem(icon: IconsTabbar().icone(icone: IconeTabbar.treino), activeIcon: IconsTabbar().icone(icone: IconeTabbar.treino_ativo), label: ('navibar.treino')));
        }
        if (getConfiguracaoApp(ModuloApp.MODULO_CONTROLE_ALUNOS).habilitado! && eColaborador) {
          telaTabbar.add(TelaListaAlunos());
          itensBottomBar.add(BottomNavigationBarItem(
              icon: IconsTabbar().icone(icone: IconeTabbar.listaAlunos), activeIcon: IconsTabbar().icone(icone: IconeTabbar.listaAlunos_ativo), label: ('navibar.alunos')));
        }
        //INDEX 4
        //COLABORADOR: TELA CROSSFIT
        //ALUNO: TELA CROSSFIT
        if (getConfiguracaoApp(ModuloApp.MODULO_CROSS).habilitado!) {
          telaTabbar.add(TelaNewCross());
          itensBottomBar
              .add(BottomNavigationBarItem(icon: IconsTabbar().icone(icone: IconeTabbar.cross), activeIcon: IconsTabbar().icone(icone: IconeTabbar.cross_ativo), label: ('navibar.cross')));
        }
    }
    //INDEX 5
    //COLABORADOR: TELA VITIO
    //ALUNO: TELA VITIO
    if ((getConfiguracaoApp(ModuloApp.MODULO_VITIO_BIOIMPEDANCIA).habilitado ?? false)) {
      telaTabbar.add(const TelaNovaHomeSaude());
      //const TelaNovaHomeSaude()
      itensBottomBar.add(BottomNavigationBarItem(icon: IconsTabbar().icone(icone: IconeTabbar.saude), activeIcon: IconsTabbar().icone(icone: IconeTabbar.saude_ativo), label: ('Saúde')));
    }
    if (GetIt.I.get<ControladorApp>().chave == '2baf2084c71d676019c4440a0e52a989') {
      telaTabbar.add(const TelaNutricaoFitstream());
      itensBottomBar.add(BottomNavigationBarItem(
          icon: IconsTabbar().icone(icone: IconeTabbar.nutricao_fitstream), activeIcon: IconsTabbar().icone(icone: IconeTabbar.nutricao_fitstream_ativo), label: ('navibar.nutri')));
    }
    //INDEX 6 (INDEX SE NAO OUVER A QUANTIDADE DE ELEMENTOS)
    //COLABORADOR: TELA PERFIL PROFESSOR
    //ALUNO: TELA AGENDA : TELA PERFIL
    if (itensBottomBar.length <= 3) {
      if (getConfiguracaoApp(ModuloApp.MODULO_AGENDA).habilitado! && (!eColaborador)) {
        telaTabbar.add(NovaTelaAgendaAluno(itemBar: true));
        itensBottomBar
            .add(BottomNavigationBarItem(icon: IconsTabbar().icone(icone: IconeTabbar.agenda), activeIcon: IconsTabbar().icone(icone: IconeTabbar.agenda_ativo), label: ('navibar.agenda')));
      } else {
        telaTabbar.add(eColaborador ? TelaPerfilProfessor() : TelaPerfilAluno());
        itensBottomBar
            .add(BottomNavigationBarItem(icon: IconsTabbar().icone(icone: IconeTabbar.perfil), activeIcon: IconsTabbar().icone(icone: IconeTabbar.perfil_ativo), label: ('navibar.perfil')));
      }
    }
  }

  Future<String> gerarHeaderDeOrigin() async {
    ControladorAppLoading _controladorAppLoading = GetIt.I.get<ControladorAppLoading>();
    if (_userAgent.isNotEmpty && GetIt.I.get<ControladorCliente>().mUsuarioAuth == null) {
      return _userAgent;
    }
    if (_userAgent.isEmpty || !userDataInHeader) {
      packageInfo = await PackageInfo.fromPlatform();
      String version = packageInfo!.version;
      String buildNumber = packageInfo!.buildNumber;
      String last = '';
      if ((GetIt.I.get<ControladorApp>().chave?.isNotEmpty ?? false) && GetIt.I.get<ControladorCliente>().mUsuarioLogado != null) {
        // ignore: unnecessary_statements
        last = '${GetIt.I.get<ControladorApp>().chave}:${GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa}:${GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codUsuario}';
        userDataInHeader = true;
      } else if (_controladorAppLoading.mLoadedUsuario != null) {
        last = '${GetIt.I.get<ControladorApp>().chave}:${_controladorAppLoading.mLoadedUsuario?.codEmpresa}:${_controladorAppLoading.mLoadedUsuario?.codUsuario}';
      }
      _userAgent = "${F.app}_${Platform.isIOS ? 'IOS' : 'ANDROID'} / v:${version} (BuildNumber:$buildNumber) ${last}";
    }
    return kDebugMode ? '${_userAgent} DEBUG' : _userAgent;
  }

  String? get nomeDoAppApresentar {
    var cliente = GetIt.I.get<ControladorCliente>();
    String? nome;
    if (cliente.isUsuarioColaborador) {
      if ((cliente.mUsuarioLogado!.empresas?.isNotEmpty ?? false) && ((cliente.mUsuarioLogado!.empresas?.length ?? 0) > 1)) {
        nome = cliente.mUsuarioLogado!.empresas!.firstWhere((element) => element.codigo == cliente.mUsuarioLogado!.codEmpresa).nome;
      }
    }
    return nome != null ? camelCase(nome) : null;
  }

  @observable
  ObservableList<Empresa> mListaEmpresasZw = ObservableList<Empresa>();

  final _mServiceCliente = GetIt.I.get<ClienteAppService>();

  ConfiguracaoModulosApp getConfiguracaoApp(ModuloApp? modulo) {
    late ConfiguracaoModulosApp configuracao;
    if (F.isPersonalizado && GetIt.I.get<ControladorApp>().mClienteAppSelecionado?.documentkey == 'qvD3JGjyQfNpRsymVTVi') {
      // Modulos default false
      var modulos = [ModuloApp.MODULO_VITIO_BIOIMPEDANCIA, ModuloApp.CHAT_PROF_ALU];
      if (modulos.contains(modulo)) {
        return ConfiguracaoModulosApp(ePremium: false, habilitado: false, moduloApp: modulo);
      }
    }
    if ((mClienteAppSelecionado?.configuracaoModulosApp ?? []).any((conf) => conf.moduloApp == modulo)) {
      configuracao = mClienteAppSelecionado!.configuracaoModulosApp!
          .firstWhere((conf) => conf.moduloApp == modulo, orElse: () => ConfiguracaoModulosApp(ePremium: false, habilitado: false, moduloApp: modulo));
    } else {
      configuracao = ConfiguracaoModulosApp(ePremium: false, habilitado: false, moduloApp: modulo);
    }
    return configuracao;
  }

  bool getConfigWebTreino(String modulo) {
    try {
      return mConfiguracoesTreinoWeb.firstWhere((element) => element.id == modulo).valor == 'true';
    } catch (e) {
      return false;
    }
  }

  void consultarSeHaMultiEmpresas({Function()? carregando, Function()? sucesso, Function(String?)? falha}) {
    _mServiceCliente.consultarListaEmpresasZw().then((value) {
      mListaEmpresasZw.clear();
      mListaEmpresasZw.addAll(value);
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  List<EmpresaRede> empresasRede = [];

  void consultarDadosRede({Function()? onLoading, Function()? onSuccess, Function(String?)? onError}) {
    if (getConfiguracaoApp(ModuloApp.MODULO_SELECAO_UNIDADE_AULAS).habilitado!) {
      empresasRede.clear();
      _mServiceCliente.consultarDadosRede().then((value) {
        empresasRede.addAll(value);
        onSuccess?.call();
      }).catchError((error) {
        onError?.call(error.toString());
      });
    }
  }

  @observable
  EmpresaFinanceiro empresaFinanceira = EmpresaFinanceiro();

  void consultarEmpresaFinanceiro(String codEmpresa, {Function()? carregando, Function()? sucesso, Function(String?)? falha}) {
    carregando?.call();
    _mServiceCliente.consultarEmpresaFinanceiro(codEmpresa).then((value) {
      empresaFinanceira = value;
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.toString());
    });
  }

  Future<void> temAppSelecionado({Function()? tem, Function()? naoTem, Function()? empresaDesativada}) async {
    var testeNoSql = await buscarNoBanco('clienteAppSelecionado');
    if (testeNoSql != null) {
      mClienteAppSelecionado = ClienteApp.fromJson(testeNoSql);
      var db = await SharedPreferences.getInstance();
      db.setString('urlLogoApp', mClienteAppSelecionado!.urlLogoPrincipal!);
      defaultColor = HexColor.fromHex(mClienteAppSelecionado!.corPadrao);
      await inserirNoBanco('clienteAppSelecionado', mClienteAppSelecionado!.toJson(), false).catchError((onError) {});
      chave = db.getString(UserDataKeys.CHAVE.toString());
      var userRecord = await buscarNoBanco('usuarioLogado');
      if (userRecord != null) {
        GetIt.I.get<ControladorCliente>().mUsuarioLogado = Usuario.fromJson(userRecord);
      }
      consultarClienteAppCompleto(mClienteAppSelecionado!, sucesso: () async {
        if (!(mClienteAppSelecionado?.aplicativoAtivo ?? true)) {
          empresaDesativada?.call();
          return;
        }
        defaultColor = HexColor.fromHex(mClienteAppSelecionado!.corPadrao);
        GetIt.I.get<ControladorCliente>().consultarClienteappN2b = false;
        loadDadosBaseApp(discovery: tem!);
      }, falha: () {
        loadDadosBaseApp(discovery: tem!);
      });
    } else {
      naoTem!();
    }
  }

  void pesquisarClienteAppNome(String termo, {Function()? carregando, Function()? vazioMesmo}) {
    mClientesAppPesquisa.clear();
    //statusCarregamentoListaClienteApp = ServiceStatus.Waiting;
    if (termo.isNotEmpty && !termo.contains('\n')) {
      Iterable<ClienteApp>? temp = mClientesApp
          .where((element) => removeDiacritics(element.nomeDoApp!.toUpperCase()).contains(removeDiacritics(termo.toUpperCase().trim())) || (element.conjuntoKey?.contains(termo) ?? false));
      if (temp.isEmpty && termoJaPesquisado != termo && !pesquisandoOnline) {
        termoJaPesquisado = termo;
        if (_debounce?.isActive ?? false) _debounce!.cancel();
        _debounce = Timer(const Duration(milliseconds: 200), () {
          pesquisandoOnline = true;
          consultarClientesApp(
            sucesso: () {
              pesquisarClienteAppNome(termo, carregando: carregando, vazioMesmo: vazioMesmo);
            },
          );
        });
      } else {
        mClientesAppPesquisa.addAll(temp);
        temp = null;
        statusCarregamentoListaClienteApp = ServiceStatus.Done;
        if (mClientesAppPesquisa.isEmpty) {
          statusCarregamentoListaClienteApp = ServiceStatus.Empty;
        }
      }
    } else {
      statusCarregamentoListaClienteApp = ServiceStatus.Done;
      mClientesAppPesquisa.clear();
    }
  }

  void consultarClientePorNome(String termo) {
    statusCarregamentoListaClienteApp = ServiceStatus.Waiting;
    if (termo.isNotEmpty) {
      _mServiceCliente.consultarCiientesAppNome(10, termo).then((value) {
        mClientesAppPesquisa.clear();
        mClientesAppPesquisa.addAll(value);
        statusCarregamentoListaClienteApp = mClientesAppPesquisa.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      }).catchError((onError) {
        statusCarregamentoListaClienteApp = ServiceStatus.Error;
      });
    } else {
      statusCarregamentoListaClienteApp = ServiceStatus.Done;
      mClientesAppPesquisa.clear();
    }
  }

  void loadDadosBaseApp({Function? discovery, bool chamarDiscovery = true}) {
    SharedPreferences.getInstance().then((db) async {
      chave = db.getString(UserDataKeys.CHAVE.toString());
      if (db.getString('TEMA') != null) {
        themeModeSystem = false;
        var tema = db.getString('TEMA');
        themeMode = tema == 'dark'
            ? ThemeMode.dark
            : tema == 'ligth'
                ? ThemeMode.light
                : ThemeMode.system;
        themeModeSystem = ThemeMode.system == themeMode;
      } else {
        themeModeSystem = true;
      }
      pularSenhaLogin = db.getBool('SkipLogin') ?? false;
      modoHomologacao = db.getBool('showHtpLog') ?? false;
      utilizaDescovery = db.getBool('utlizaDiscovery') ?? true;
      setThemeModeSystem();
      if (chave == null) return;
      if (chamarDiscovery) {
        mConfiguracoesTreinoWeb.clear();
        consultarConfiguracoesTreinoWeb(() {}, (x) {});
        consultarUrlTreino(
          suceso: () {
            discovery?.call();
          },
          erro: (mensagem) {
            discovery?.call();
          },
        );
      } else {
        discovery?.call();
      }
      FirebaseAnalytics.instance.setUserProperty(name: 'chave_academia', value: chave!);
      GetIt.I.get<ControladorTreinosPreDefinidos>().consultarDadosBasico();
      GetIt.I.get<ControladorTreinosPreDefinidos>().carregarListaComTodasAtividades(suceso: () {
        GetIt.I.get<ControladorTreinosPreDefinidos>().jaConsultouAtividades = true;
      });

      for (final element in ConfiguracoesTreinoWeb.fromJson((await buscarNoBanco(chave!)) as Map<String, dynamic>).configuracoes!) {
        mConfiguracoesTreinoWeb.add(element);
      }
    });
  }

  FutureOr<UrlDiscorver?> consultarUrlTreino({Function()? suceso, Function(String? mensagem)? erro}) {
    return _mServiceCliente.descobrirURLTreino().then((value) {
      mUrls = value;
      return SharedPreferences.getInstance().then((db) {
        if (utilizaDescovery) {
          db.setString(ConfigURL.TREINO.toString(), value.serviceUrls!.treinoUrl!);
          db.setString(ConfigURL.OAMD.toString(), value.serviceUrls!.oamdUrl!);
          db.setString(ConfigURL.URLZW.toString(), value.serviceUrls!.zwUrl!.replaceAll('/app', ''));
          db.setString(ConfigURL.PERSONAGEM.toString(), value.serviceUrls!.personagemMsUrl!);
          db.setString(ConfigURL.TOKENWEB.toString(), value.serviceUrls!.autenticacaoUrl!);
          db.setString(ConfigURL.GRADUACAO.toString(), value.serviceUrls!.graduacaoMsUrl!);
          db.setString(ConfigURL.ACESSOMS.toString(), value.serviceUrls!.acessoSistemaMsUrl!);
          db.setString(ConfigURL.ADMCORE.toString(), value.serviceUrls!.admCoreUrl!);
          final contato = value.serviceUrls?.contatoMsUrl;
          if (contato != null && contato.isNotEmpty) {
            db.setString(ConfigURL.CONTATOMSURL.toString(), contato);
          } else {
            db.remove(ConfigURL.CONTATOMSURL.toString());
          }
        }
        suceso?.call();
        return value;
      });
    }).catchError((onError) {
      erro?.call(onError?.toString() ?? '');
      return null;
    });
  }

  Future<void> setEmpresaSelecionada(EmpresaApp empresa, Function() salvo, Function(String? falha)? falha) async {
    await inserirNoBanco('clienteAppSelecionado', mClienteAppSelecionado!.toJson()).catchError((onError) {});

    try {
      defaultColor = HexColor.fromHex(mClienteAppSelecionado!.corPadrao);
    } catch (e) {}

    GetIt.I.get<ControladorVendasOnline>().limparDados();
    SharedPreferences.getInstance().then((db) {
      chave = empresa.chave;
      db.setString(UserDataKeys.CHAVE.toString(), empresa.chave!);
      db.setString(UserDataKeys.NOMEEMPRESA.toString(), empresa.nome ?? '-');
      consultarUrlTreino(suceso: () {
        consultarConfiguracoesTreinoWeb(() {
          salvo();
        }, (mensagem) {
          falha?.call(mensagem);
        });
      }, erro: (mensagem) {
        falha?.call(mensagem);
      });
    });
  }

  void setarParametrosLinkMenu(Function() sucesso) {
    var _controladorCliente = GetIt.I.get<ControladorCliente>();

    SharedPreferences.getInstance().then((db) async {
      chave = db.getString(UserDataKeys.CHAVE.toString());
    });

    if (!jaSetouLinks) {
      for (final item in mClienteAppSelecionado!.itensMenuApp!) {
        bool temHttps = item.url!.startsWith('https://');
        if (!temHttps) {
          item.url = 'https://${item.url}';
        }
        String urlComEmail = item.url!.replaceAll('?email=', '?email=${_controladorCliente.mUsuarioLogado!.username}');
        String urlComChave = urlComEmail.replaceAll('&chave=', '&chave=$chave');
        String url = urlComChave.replaceAll('&matricula=', '&matricula=${_controladorCliente.mUsuarioLogado!.matricula}');
        item.url = url;
      }
      sucesso.call();
    }
  }

  void consultarClienteAppCompleto(ClienteApp clienteApp, {Function()? sucesso, Function()? falha}) {
    statusCarregandoAcademias = ServiceStatus.Waiting;
    mClienteAppSelecionado = null;
    _mServiceCliente.consultarDadosDoClienteAppNovo(clienteApp.documentkey!.toString()).then((value) async {
      veioDoSplash = true;
      mClienteAppSelecionado = value;
      FirebaseAnalytics.instance.setUserProperty(name: 'vendePremium', value: getConfiguracaoApp(ModuloApp.MODULO_REFEICOES).ePremium.toString());
      var db = await SharedPreferences.getInstance();
      db.setString('urlLogoApp', mClienteAppSelecionado!.urlLogoPrincipal!);
      statusCarregandoAcademias = ServiceStatus.Done;
      sucesso!.call();
    }).catchError((onError) {
      statusCarregandoAcademias = ServiceStatus.Error;
      falha!.call();
    });
  }

  void pesquisarEmpresaPorNome(String nome) {
    mEmpresasAppPequisa.clear();
    mEmpresasAppPequisa.addAll(mClienteAppSelecionado!.empresaApps!.where((element) => removeDiacritics(element.nome!.toLowerCase()).contains(removeDiacritics(nome.toLowerCase()))));
  }

  @observable
  String textopesquisa = '';

  bool filtrarUnidades(EmpresaApp element) {
    if (textopesquisa.isEmpty) {
      return true;
    }
    if (textopesquisa.length == 32) {
      return element.chave?.toLowerCase() == textopesquisa.toLowerCase();
    }
    return removeDiacritics(element.nome!.toLowerCase()).contains(removeDiacritics(textopesquisa.toLowerCase()));
  }

  void consultarConfiguracoesTreinoWeb(Function()? sucesso, Function(String? mensagem)? erro, {bool isHome = false}) {
    if (isHome && mConfiguracoesTreinoWeb.isNotEmpty) {
      sucesso?.call();
      return;
    }
    _mServiceCliente.consultarConfiguracoesTWeb().then((value) async {
      mConfiguracoesTreinoWeb.clear();
      await inserirNoBanco(value.key!, const JsonCodec().decode(const JsonCodec().encode(value.toJson())), false);
      for (final element in value.configuracoes!) {
        mConfiguracoesTreinoWeb.add(element);
      }
      sucesso?.call();
    }).catchError((onError) {
      erro?.call(onError.message);
    });
  }

  void consultarClientesApp({Function()? sucesso, Function(String? erro)? erro, bool force = true}) {
    if (force) {
      _mServiceCliente.consultarCiientesAppNome(10, '').then((value) {
        mClientesApp.clear();
        mClientesApp.addAll(value);
        statusCarregamentoListaClienteApp = ServiceStatus.Done;
        pesquisandoOnline = false;
        sucesso!();
      }).catchError((onError) {
        pesquisandoOnline = false;
        erro?.call(onError.message);
      });
    } else {
      if (mClientesApp.isNotEmpty) {
        statusCarregamentoListaClienteApp = ServiceStatus.Done;
        pesquisandoOnline = false;
        sucesso!();
      } else {
        consultarClientesApp(sucesso: sucesso, erro: erro, force: true);
      }
    }
  }

  Map<String, Function()> listenerTheme = {};
  setThemeModeSystem() {
    if (themeModeSystem) this.themeMode = WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.light ? ThemeMode.light : ThemeMode.dark;
    DSLib.theme = this.themeMode;
    WidgetsBinding.instance.handleLocaleChanged();
    for (final c in listenerTheme.values) {
      c();
    }
  }

  void setThemeModDefault(ThemeMode? themeMode) {
    SharedPreferences.getInstance().then((dataBase) {
      dataBase.setString(
          'TEMA',
          themeMode == ThemeMode.dark
              ? 'dark'
              : themeMode == ThemeMode.light
                  ? 'ligth'
                  : 'sys');
      themeModeSystem = ThemeMode.system == themeMode;
      if (themeModeSystem) {
        this.themeMode = WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.light ? ThemeMode.light : ThemeMode.dark;
      } else {
        this.themeMode = themeMode;
      }
      setThemeModeSystem();
    });
  }

  Future<void> consultarSePossuiDadosDeInstalacaoPrevia(Function() consultou) async {
    if (kIsWeb) {
      consultou.call();
      return;
    }
    File file;
    if (Platform.isAndroid) {
      final Directory directory = await getApplicationSupportDirectory();
      file = File('${directory.path.replaceAll("/app_flutter", "")}/migracaoData.txt');
    } else {
      final Directory directory = await getApplicationSupportDirectory();
      file = File('${directory.path}/migracaoData.txt');
    }

    if (await file.exists()) {
      var dadosObjet = DadosTransicao.fromJson(const JsonCodec().decode(await file.readAsString()));
      ClienteApp clienteApp = ClienteApp(documentkey: dadosObjet.clienteApp);
      consultarClienteAppCompleto(clienteApp,
          sucesso: () {
            this.chave = dadosObjet.chave;
            consultarUrlTreino(suceso: () {
              GetIt.I.get<ControladorCliente>().logarUsuarioPorSenha(
                  carregando: () {},
                  falha: (v) => consultou.call(),
                  sucesso: () {
                    setEmpresaSelecionada(EmpresaApp(nome: '', chave: dadosObjet.chave), () async {
                      file.deleteSync();
                      consultou.call();
                    }, (x) {});
                  },
                  pass: '',
                  user: dadosObjet.userName);
            }, erro: (mensagem) {
              consultou.call();
            });
          },
          falha: () => consultou.call());
    } else {
      consultou.call();
    }
  }

  limparTodosControladores() {
    FixSaldosN2b().resetVariables();
    SharedPreferences.getInstance().then((value) {
      value.remove('showRenv');
      value.remove('data_consulta_contrato');
      value.remove('usuario_selecionou_libras');
      value.remove('usuario_selecionou_pesoCorporal');
      value.remove('usuario_selecionou_distancia');
      value.remove('usuario_selecionou_altura');
      value.remove('contrato_assinado');
      value.remove('CPFVITIO');
    });
    GetIt.I.get<ControladorExecucaoTreino>().finalizarTreinoEmExecucao();
    GetIt.I.get<ControladorEdicaoiExercicio>().limparTudo();
    GetIt.I.get<ControladorFeed>().limparTudo();
    GetIt.I.get<ControladorInicacao>().limparTudo();
    GetIt.I.get<ControladorManterAluno>().limparTudo();
    GetIt.I.get<ControladorManterExercicio>().limparTudo();
    GetIt.I.get<ControladorManterPerfil>().limparTudo();
    GetIt.I.get<ControladorNotificacoes>().limparTudo();
    GetIt.I.get<ControladorNPS>().limparTudo();
    GetIt.I.get<ControladorObservacoesAluno>().limparTudo();
    GetIt.I.get<ControladorPlanoDeTreino>().limparTudo();
    GetIt.I.get<ControladorPrescricaoDeTreino>().limparTudo();
    GetIt.I.get<ControladorTreinoAluno>().limparTudo();
    //GetIt.I.get<ControladorTreinoAoVivo>().limparTudo();
    GetIt.I.get<ControladorTreinosPreDefinidos>().limparTudo();
    GetIt.I.get<ControladorAulaTurma>().limparTudo();
    GetIt.I.get<ControladorAvaliacaoFisica>().limparTudo();
    GetIt.I.get<ControladorBeberAgua>().limparTudo();
    GetIt.I.get<ControladorNotificacoesCrm>().limparTudo();
    GetIt.I.get<ControladorPlanner>().limparTudo();
    GetIt.I.get<ControladorContratoUsuario>().limparTudo();
    GetIt.I.get<ControladorWod>().limparTudo();
    GetIt.I.get<ControladorTreinoSimples>().limparTudo();
    GetIt.I.get<ControladorAgendamento>().limparTudo();
    GetIt.I.get<ControladorDashBoardPersonal>().limparTudo();
    GetIt.I.get<ControladorVendaDePlano>().limparDados();
    GetIt.I.get<ControladorSplash>().limparTudo();
    limparToken();
  }

  limparToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(UserDataKeys.TOKENDEAUTHAPI.toString());
  }

  Future<ClienteApp?> carregarDadosClienteApp() async {
  if (mClienteAppSelecionado?.documentkey != null) {
    try {
        final clienteAppAtualizado = await _mServiceCliente.consultarDadosDoClienteAppNovo(mClienteAppSelecionado!.documentkey!.toString());
      mClienteAppSelecionado = clienteAppAtualizado;
      // Salvar dados atualizados
      await inserirNoBanco('clienteAppSelecionado', mClienteAppSelecionado!.toJson(), false);
      var db = await SharedPreferences.getInstance();
      if (mClienteAppSelecionado?.urlLogoPrincipal != null) {
        db.setString('urlLogoApp', mClienteAppSelecionado!.urlLogoPrincipal!);
      }
      return clienteAppAtualizado;
    } catch (e) {
      return null;
    }
  }
  return null;
}
}
