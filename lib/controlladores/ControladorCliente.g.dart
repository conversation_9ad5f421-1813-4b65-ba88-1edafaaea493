// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorCliente.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorCliente on _ControladorClienteBase, Store {
  late final _$mUsuarioLogadoAtom =
      Atom(name: '_ControladorClienteBase.mUsuarioLogado', context: context);

  @override
  Usuario? get mUsuarioLogado {
    _$mUsuarioLogadoAtom.reportRead();
    return super.mUsuarioLogado;
  }

  @override
  set mUsuarioLogado(Usuario? value) {
    _$mUsuarioLogadoAtom.reportWrite(value, super.mUsuarioLogado, () {
      super.mUsuarioLogado = value;
    });
  }

  late final _$mUsuarioAuthAtom =
      Atom(name: '_ControladorClienteBase.mUsuarioAuth', context: context);

  @override
  UserAuthFirebase? get mUsuarioAuth {
    _$mUsuarioAuthAtom.reportRead();
    return super.mUsuarioAuth;
  }

  @override
  set mUsuarioAuth(UserAuthFirebase? value) {
    _$mUsuarioAuthAtom.reportWrite(value, super.mUsuarioAuth, () {
      super.mUsuarioAuth = value;
    });
  }

  late final _$usuarioLogarAtom =
      Atom(name: '_ControladorClienteBase.usuarioLogar', context: context);

  @override
  UsuarioAppTelefone? get usuarioLogar {
    _$usuarioLogarAtom.reportRead();
    return super.usuarioLogar;
  }

  @override
  set usuarioLogar(UsuarioAppTelefone? value) {
    _$usuarioLogarAtom.reportWrite(value, super.usuarioLogar, () {
      super.usuarioLogar = value;
    });
  }

  late final _$mConsultaDadosUsuarioAtom = Atom(
      name: '_ControladorClienteBase.mConsultaDadosUsuario', context: context);

  @override
  ServiceStatus get mConsultaDadosUsuario {
    _$mConsultaDadosUsuarioAtom.reportRead();
    return super.mConsultaDadosUsuario;
  }

  @override
  set mConsultaDadosUsuario(ServiceStatus value) {
    _$mConsultaDadosUsuarioAtom.reportWrite(value, super.mConsultaDadosUsuario,
        () {
      super.mConsultaDadosUsuario = value;
    });
  }

  late final _$mStatusConsultaConveniosPagamentoAtom = Atom(
      name: '_ControladorClienteBase.mStatusConsultaConveniosPagamento',
      context: context);

  @override
  ServiceStatus get mStatusConsultaConveniosPagamento {
    _$mStatusConsultaConveniosPagamentoAtom.reportRead();
    return super.mStatusConsultaConveniosPagamento;
  }

  @override
  set mStatusConsultaConveniosPagamento(ServiceStatus value) {
    _$mStatusConsultaConveniosPagamentoAtom
        .reportWrite(value, super.mStatusConsultaConveniosPagamento, () {
      super.mStatusConsultaConveniosPagamento = value;
    });
  }

  late final _$mStatusHisoricoPresencaAtom = Atom(
      name: '_ControladorClienteBase.mStatusHisoricoPresenca',
      context: context);

  @override
  ServiceStatus get mStatusHisoricoPresenca {
    _$mStatusHisoricoPresencaAtom.reportRead();
    return super.mStatusHisoricoPresenca;
  }

  @override
  set mStatusHisoricoPresenca(ServiceStatus value) {
    _$mStatusHisoricoPresencaAtom
        .reportWrite(value, super.mStatusHisoricoPresenca, () {
      super.mStatusHisoricoPresenca = value;
    });
  }

  late final _$mHistoricoPresencaAtom = Atom(
      name: '_ControladorClienteBase.mHistoricoPresenca', context: context);

  @override
  HistoricoPresenca? get mHistoricoPresenca {
    _$mHistoricoPresencaAtom.reportRead();
    return super.mHistoricoPresenca;
  }

  @override
  set mHistoricoPresenca(HistoricoPresenca? value) {
    _$mHistoricoPresencaAtom.reportWrite(value, super.mHistoricoPresenca, () {
      super.mHistoricoPresenca = value;
    });
  }

  late final _$mStatusPontuacaoAlunoAtom = Atom(
      name: '_ControladorClienteBase.mStatusPontuacaoAluno', context: context);

  @override
  ServiceStatus get mStatusPontuacaoAluno {
    _$mStatusPontuacaoAlunoAtom.reportRead();
    return super.mStatusPontuacaoAluno;
  }

  @override
  set mStatusPontuacaoAluno(ServiceStatus value) {
    _$mStatusPontuacaoAlunoAtom.reportWrite(value, super.mStatusPontuacaoAluno,
        () {
      super.mStatusPontuacaoAluno = value;
    });
  }

  late final _$mStatusBrindesAtom =
      Atom(name: '_ControladorClienteBase.mStatusBrindes', context: context);

  @override
  ServiceStatus get mStatusBrindes {
    _$mStatusBrindesAtom.reportRead();
    return super.mStatusBrindes;
  }

  @override
  set mStatusBrindes(ServiceStatus value) {
    _$mStatusBrindesAtom.reportWrite(value, super.mStatusBrindes, () {
      super.mStatusBrindes = value;
    });
  }

  late final _$mBrindesAtom =
      Atom(name: '_ControladorClienteBase.mBrindes', context: context);

  @override
  List<Brindes>? get mBrindes {
    _$mBrindesAtom.reportRead();
    return super.mBrindes;
  }

  @override
  set mBrindes(List<Brindes>? value) {
    _$mBrindesAtom.reportWrite(value, super.mBrindes, () {
      super.mBrindes = value;
    });
  }

  late final _$contratoTermosAtom =
      Atom(name: '_ControladorClienteBase.contratoTermos', context: context);

  @override
  bool get contratoTermos {
    _$contratoTermosAtom.reportRead();
    return super.contratoTermos;
  }

  @override
  set contratoTermos(bool value) {
    _$contratoTermosAtom.reportWrite(value, super.contratoTermos, () {
      super.contratoTermos = value;
    });
  }

  late final _$contratoAssinaturaAtom = Atom(
      name: '_ControladorClienteBase.contratoAssinatura', context: context);

  @override
  bool get contratoAssinatura {
    _$contratoAssinaturaAtom.reportRead();
    return super.contratoAssinatura;
  }

  @override
  set contratoAssinatura(bool value) {
    _$contratoAssinaturaAtom.reportWrite(value, super.contratoAssinatura, () {
      super.contratoAssinatura = value;
    });
  }

  late final _$acessoUnidadeFotoAtom =
      Atom(name: '_ControladorClienteBase.acessoUnidadeFoto', context: context);

  @override
  bool get acessoUnidadeFoto {
    _$acessoUnidadeFotoAtom.reportRead();
    return super.acessoUnidadeFoto;
  }

  @override
  set acessoUnidadeFoto(bool value) {
    _$acessoUnidadeFotoAtom.reportWrite(value, super.acessoUnidadeFoto, () {
      super.acessoUnidadeFoto = value;
    });
  }

  late final _$mUsuariosEncontradosAtom = Atom(
      name: '_ControladorClienteBase.mUsuariosEncontrados', context: context);

  @override
  ObservableList<UsuarioAppTelefone> get mUsuariosEncontrados {
    _$mUsuariosEncontradosAtom.reportRead();
    return super.mUsuariosEncontrados;
  }

  @override
  set mUsuariosEncontrados(ObservableList<UsuarioAppTelefone> value) {
    _$mUsuariosEncontradosAtom.reportWrite(value, super.mUsuariosEncontrados,
        () {
      super.mUsuariosEncontrados = value;
    });
  }

  late final _$mUsuariosEncontradosCPFAtom = Atom(
      name: '_ControladorClienteBase.mUsuariosEncontradosCPF',
      context: context);

  @override
  ObservableList<UsuarioAppTelefone> get mUsuariosEncontradosCPF {
    _$mUsuariosEncontradosCPFAtom.reportRead();
    return super.mUsuariosEncontradosCPF;
  }

  @override
  set mUsuariosEncontradosCPF(ObservableList<UsuarioAppTelefone> value) {
    _$mUsuariosEncontradosCPFAtom
        .reportWrite(value, super.mUsuariosEncontradosCPF, () {
      super.mUsuariosEncontradosCPF = value;
    });
  }

  late final _$mUsuariosSalvosAtom =
      Atom(name: '_ControladorClienteBase.mUsuariosSalvos', context: context);

  @override
  ObservableList<UsuarioKeep> get mUsuariosSalvos {
    _$mUsuariosSalvosAtom.reportRead();
    return super.mUsuariosSalvos;
  }

  @override
  set mUsuariosSalvos(ObservableList<UsuarioKeep> value) {
    _$mUsuariosSalvosAtom.reportWrite(value, super.mUsuariosSalvos, () {
      super.mUsuariosSalvos = value;
    });
  }

  late final _$nomeEmpresaSelecionadaAtom = Atom(
      name: '_ControladorClienteBase.nomeEmpresaSelecionada', context: context);

  @override
  String? get nomeEmpresaSelecionada {
    _$nomeEmpresaSelecionadaAtom.reportRead();
    return super.nomeEmpresaSelecionada;
  }

  @override
  set nomeEmpresaSelecionada(String? value) {
    _$nomeEmpresaSelecionadaAtom
        .reportWrite(value, super.nomeEmpresaSelecionada, () {
      super.nomeEmpresaSelecionada = value;
    });
  }

  late final _$mListaEmpresasAtom =
      Atom(name: '_ControladorClienteBase.mListaEmpresas', context: context);

  @override
  List<Empresa> get mListaEmpresas {
    _$mListaEmpresasAtom.reportRead();
    return super.mListaEmpresas;
  }

  @override
  set mListaEmpresas(List<Empresa> value) {
    _$mListaEmpresasAtom.reportWrite(value, super.mListaEmpresas, () {
      super.mListaEmpresas = value;
    });
  }

  late final _$consultaSituacaoAtom =
      Atom(name: '_ControladorClienteBase.consultaSituacao', context: context);

  @override
  SituacaoCliente? get consultaSituacao {
    _$consultaSituacaoAtom.reportRead();
    return super.consultaSituacao;
  }

  @override
  set consultaSituacao(SituacaoCliente? value) {
    _$consultaSituacaoAtom.reportWrite(value, super.consultaSituacao, () {
      super.consultaSituacao = value;
    });
  }

  late final _$situacaoAtom =
      Atom(name: '_ControladorClienteBase.situacao', context: context);

  @override
  String? get situacao {
    _$situacaoAtom.reportRead();
    return super.situacao;
  }

  @override
  set situacao(String? value) {
    _$situacaoAtom.reportWrite(value, super.situacao, () {
      super.situacao = value;
    });
  }

  late final _$erroValidarTotalPassAtom = Atom(
      name: '_ControladorClienteBase.erroValidarTotalPass', context: context);

  @override
  bool get erroValidarTotalPass {
    _$erroValidarTotalPassAtom.reportRead();
    return super.erroValidarTotalPass;
  }

  @override
  set erroValidarTotalPass(bool value) {
    _$erroValidarTotalPassAtom.reportWrite(value, super.erroValidarTotalPass,
        () {
      super.erroValidarTotalPass = value;
    });
  }

  late final _$cpfAlunoValidarTotalPassAtom = Atom(
      name: '_ControladorClienteBase.cpfAlunoValidarTotalPass',
      context: context);

  @override
  String get cpfAlunoValidarTotalPass {
    _$cpfAlunoValidarTotalPassAtom.reportRead();
    return super.cpfAlunoValidarTotalPass;
  }

  @override
  set cpfAlunoValidarTotalPass(String value) {
    _$cpfAlunoValidarTotalPassAtom
        .reportWrite(value, super.cpfAlunoValidarTotalPass, () {
      super.cpfAlunoValidarTotalPass = value;
    });
  }

  late final _$dadosEmpresaAtom =
      Atom(name: '_ControladorClienteBase.dadosEmpresa', context: context);

  @override
  NotificarRecursoEmpresa get dadosEmpresa {
    _$dadosEmpresaAtom.reportRead();
    return super.dadosEmpresa;
  }

  @override
  set dadosEmpresa(NotificarRecursoEmpresa value) {
    _$dadosEmpresaAtom.reportWrite(value, super.dadosEmpresa, () {
      super.dadosEmpresa = value;
    });
  }

  late final _$vaiValidarOCodigoPorEmailAtom = Atom(
      name: '_ControladorClienteBase.vaiValidarOCodigoPorEmail',
      context: context);

  @override
  bool get vaiValidarOCodigoPorEmail {
    _$vaiValidarOCodigoPorEmailAtom.reportRead();
    return super.vaiValidarOCodigoPorEmail;
  }

  @override
  set vaiValidarOCodigoPorEmail(bool value) {
    _$vaiValidarOCodigoPorEmailAtom
        .reportWrite(value, super.vaiValidarOCodigoPorEmail, () {
      super.vaiValidarOCodigoPorEmail = value;
    });
  }

  late final _$codigoValidacaoEmailAtom = Atom(
      name: '_ControladorClienteBase.codigoValidacaoEmail', context: context);

  @override
  String get codigoValidacaoEmail {
    _$codigoValidacaoEmailAtom.reportRead();
    return super.codigoValidacaoEmail;
  }

  @override
  set codigoValidacaoEmail(String value) {
    _$codigoValidacaoEmailAtom.reportWrite(value, super.codigoValidacaoEmail,
        () {
      super.codigoValidacaoEmail = value;
    });
  }

  late final _$_ControladorClienteBaseActionController =
      ActionController(name: '_ControladorClienteBase', context: context);

  @override
  String nomeEmpresa() {
    final _$actionInfo = _$_ControladorClienteBaseActionController.startAction(
        name: '_ControladorClienteBase.nomeEmpresa');
    try {
      return super.nomeEmpresa();
    } finally {
      _$_ControladorClienteBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic atualizarNomeEmpresa() {
    final _$actionInfo = _$_ControladorClienteBaseActionController.startAction(
        name: '_ControladorClienteBase.atualizarNomeEmpresa');
    try {
      return super.atualizarNomeEmpresa();
    } finally {
      _$_ControladorClienteBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
mUsuarioLogado: ${mUsuarioLogado},
mUsuarioAuth: ${mUsuarioAuth},
usuarioLogar: ${usuarioLogar},
mConsultaDadosUsuario: ${mConsultaDadosUsuario},
mStatusConsultaConveniosPagamento: ${mStatusConsultaConveniosPagamento},
mStatusHisoricoPresenca: ${mStatusHisoricoPresenca},
mHistoricoPresenca: ${mHistoricoPresenca},
mStatusPontuacaoAluno: ${mStatusPontuacaoAluno},
mStatusBrindes: ${mStatusBrindes},
mBrindes: ${mBrindes},
contratoTermos: ${contratoTermos},
contratoAssinatura: ${contratoAssinatura},
acessoUnidadeFoto: ${acessoUnidadeFoto},
mUsuariosEncontrados: ${mUsuariosEncontrados},
mUsuariosEncontradosCPF: ${mUsuariosEncontradosCPF},
mUsuariosSalvos: ${mUsuariosSalvos},
nomeEmpresaSelecionada: ${nomeEmpresaSelecionada},
mListaEmpresas: ${mListaEmpresas},
consultaSituacao: ${consultaSituacao},
situacao: ${situacao},
erroValidarTotalPass: ${erroValidarTotalPass},
cpfAlunoValidarTotalPass: ${cpfAlunoValidarTotalPass},
dadosEmpresa: ${dadosEmpresa},
vaiValidarOCodigoPorEmail: ${vaiValidarOCodigoPorEmail},
codigoValidacaoEmail: ${codigoValidacaoEmail}
    ''';
  }
}
