// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorNutricao.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ReceitaSubstituida _$ReceitaSubstituidaFromJson(Map json) => ReceitaSubstituida(
      usuarioApp: json['usuarioApp'] as String?,
      receitaPara: json['receitaPara'] as String?,
      receitas: (json['receitas'] as List<dynamic>?)
          ?.map((e) =>
              ReceitaNovaAntiga.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$ReceitaSubstituidaToJson(ReceitaSubstituida instance) =>
    <String, dynamic>{
      'usuarioApp': instance.usuarioApp,
      'receitaPara': instance.receitaPara,
      'receitas': instance.receitas?.map((e) => e.toJson()).toList(),
    };

AlimentoCriado _$AlimentoCriadoFromJson(Map json) => AlimentoCriado(
      gordura: json['gordura'] as num?,
      carboidratos: json['carboidratos'] as num?,
      proteinas: json['proteinas'] as num?,
      nome: json['nome'] as String?,
      calorias: json['calorias'] as num?,
    );

Map<String, dynamic> _$AlimentoCriadoToJson(AlimentoCriado instance) =>
    <String, dynamic>{
      'gordura': instance.gordura,
      'carboidratos': instance.carboidratos,
      'proteinas': instance.proteinas,
      'nome': instance.nome,
      'calorias': instance.calorias,
    };

HistoricoRespiracao _$HistoricoRespiracaoFromJson(Map json) =>
    HistoricoRespiracao(
      dia: json['dia'] == null ? null : DateTime.parse(json['dia'] as String),
      realizado: json['realizado'] as bool?,
    );

Map<String, dynamic> _$HistoricoRespiracaoToJson(
        HistoricoRespiracao instance) =>
    <String, dynamic>{
      'dia': instance.dia?.toIso8601String(),
      'realizado': instance.realizado,
    };

ItemListaCompras _$ItemListaComprasFromJson(Map json) => ItemListaCompras(
      quantidade: json['quantidade'] as String?,
      nome: json['nome'] as String?,
      comprado: json['comprado'] as bool?,
      id: json['id'] as String?,
    );

Map<String, dynamic> _$ItemListaComprasToJson(ItemListaCompras instance) =>
    <String, dynamic>{
      'quantidade': instance.quantidade,
      'nome': instance.nome,
      'comprado': instance.comprado,
      'id': instance.id,
    };

ReceitaNovaAntiga _$ReceitaNovaAntigaFromJson(Map json) => ReceitaNovaAntiga(
      atual: json['atual'] as String?,
      novo: json['novo'] as String?,
    );

Map<String, dynamic> _$ReceitaNovaAntigaToJson(ReceitaNovaAntiga instance) =>
    <String, dynamic>{
      'atual': instance.atual,
      'novo': instance.novo,
    };

ReceitaFavorita _$ReceitaFavoritaFromJson(Map json) => ReceitaFavorita(
      favorita: json['favorita'] as bool?,
      refReceita: json['refReceita'] as String?,
    );

Map<String, dynamic> _$ReceitaFavoritaToJson(ReceitaFavorita instance) =>
    <String, dynamic>{
      'favorita': instance.favorita,
      'refReceita': instance.refReceita,
    };

DadosSubstituicao _$DadosSubstituicaoFromJson(Map json) => DadosSubstituicao(
      objetivo: json['objetivo'] as String?,
      tipoRefeicao: json['tipoRefeicao'] as String?,
      caloriasMaximas: json['caloriasMaximas'] as num?,
      caloriasMinimas: json['caloriasMinimas'] as num?,
      restricao: (json['restricao'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .toList(),
    );

Map<String, dynamic> _$DadosSubstituicaoToJson(DadosSubstituicao instance) =>
    <String, dynamic>{
      'objetivo': instance.objetivo,
      'tipoRefeicao': instance.tipoRefeicao,
      'caloriasMaximas': instance.caloriasMaximas,
      'caloriasMinimas': instance.caloriasMinimas,
      'restricao': instance.restricao,
    };

PesoUsuario _$PesoUsuarioFromJson(Map json) => PesoUsuario(
      peso: (json['peso'] as num?)?.toDouble(),
      dataInclusao: json['dataInclusao'] == null
          ? null
          : DateTime.parse(json['dataInclusao'] as String),
    );

Map<String, dynamic> _$PesoUsuarioToJson(PesoUsuario instance) =>
    <String, dynamic>{
      'peso': instance.peso,
      'dataInclusao': instance.dataInclusao?.toIso8601String(),
    };

HistoricoHidratacao _$HistoricoHidratacaoFromJson(Map json) =>
    HistoricoHidratacao(
      ml: json['ml'] as num?,
      dataInclusao: json['dataInclusao'] == null
          ? null
          : DateTime.parse(json['dataInclusao'] as String),
    )..adicao = json['adicao'] as bool?;

Map<String, dynamic> _$HistoricoHidratacaoToJson(
        HistoricoHidratacao instance) =>
    <String, dynamic>{
      'ml': instance.ml,
      'dataInclusao': instance.dataInclusao?.toIso8601String(),
      'adicao': instance.adicao,
    };

AulasFavoritas _$AulasFavoritasFromJson(Map json) => AulasFavoritas(
      codigo: json['codigo'] as num?,
      dataInclusao: json['dataInclusao'] == null
          ? null
          : DateTime.parse(json['dataInclusao'] as String),
      favorita: json['favorita'] as bool?,
    );

Map<String, dynamic> _$AulasFavoritasToJson(AulasFavoritas instance) =>
    <String, dynamic>{
      'codigo': instance.codigo,
      'dataInclusao': instance.dataInclusao?.toIso8601String(),
      'favorita': instance.favorita,
    };

PlanoNutricional _$PlanoNutricionalFromJson(Map json) => PlanoNutricional(
      altura: json['altura'] as num?,
      atividadeFisica: json['atividadeFisica'] as String?,
      dataNascimento: json['dataNascimento'] == null
          ? null
          : DateTime.parse(json['dataNascimento'] as String),
      genero: json['genero'] as String?,
      metaPeso: (json['metaPeso'] as num?)?.toDouble(),
      objetivo: json['objetivo'] as String?,
      pesoAtual: (json['pesoAtual'] as num?)?.toDouble(),
      quantidadeRefeicoes: json['quantidadeRefeicoes'] as num?,
      variedade: json['variedade'] as String?,
      restricao: (json['restricao'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .toList(),
      usuarioApp: json['usuarioApp'] as String?,
      metaHidratacao: json['metaHidratacao'] as num?,
      dataInicioPlano: json['dataInicioPlano'] == null
          ? null
          : DateTime.parse(json['dataInicioPlano'] as String),
      dataFimPlano: json['dataFimPlano'] == null
          ? null
          : DateTime.parse(json['dataFimPlano'] as String),
      dataConfiguracaoPlano: json['dataConfiguracaoPlano'] == null
          ? null
          : DateTime.parse(json['dataConfiguracaoPlano'] as String),
      dataConfirmacaoReceitas: json['dataConfirmacaoReceitas'] == null
          ? null
          : DateTime.parse(json['dataConfirmacaoReceitas'] as String),
      pesoInicial: (json['pesoInicial'] as num?)?.toDouble(),
      refeicoes: (json['refeicoes'] as List<dynamic>?)
          ?.map((e) =>
              RefeicoesDoPlano.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      horarioRefeicao: json['horarioRefeicao'] == null
          ? null
          : HorarioRefeicao.fromJson(
              Map<String, dynamic>.from(json['horarioRefeicao'] as Map)),
      lembretesNotificacoes: json['lembretesNotificacoes'] == null
          ? null
          : LembretesNotificacoes.fromJson(
              Map<String, dynamic>.from(json['lembretesNotificacoes'] as Map)),
      dataInicioListaCompras: json['dataInicioListaCompras'] == null
          ? null
          : DateTime.parse(json['dataInicioListaCompras'] as String),
      dataFimListaCompras: json['dataFimListaCompras'] == null
          ? null
          : DateTime.parse(json['dataFimListaCompras'] as String),
      nomePlano: json['nomePlano'] as String?,
      tamanhoCopo: json['tamanhoCopo'] as num?,
      lembreteHidratacao: json['lembreteHidratacao'] as bool?,
      duracao: json['duracao'] as num?,
    );

Map<String, dynamic> _$PlanoNutricionalToJson(PlanoNutricional instance) =>
    <String, dynamic>{
      'altura': instance.altura,
      'atividadeFisica': instance.atividadeFisica,
      'dataNascimento': instance.dataNascimento?.toIso8601String(),
      'genero': instance.genero,
      'metaPeso': instance.metaPeso,
      'objetivo': instance.objetivo,
      'pesoAtual': instance.pesoAtual,
      'pesoInicial': instance.pesoInicial,
      'quantidadeRefeicoes': instance.quantidadeRefeicoes,
      'variedade': instance.variedade,
      'restricao': instance.restricao,
      'usuarioApp': instance.usuarioApp,
      'metaHidratacao': instance.metaHidratacao,
      'dataInicioPlano': instance.dataInicioPlano?.toIso8601String(),
      'dataFimPlano': instance.dataFimPlano?.toIso8601String(),
      'dataConfiguracaoPlano':
          instance.dataConfiguracaoPlano?.toIso8601String(),
      'dataConfirmacaoReceitas':
          instance.dataConfirmacaoReceitas?.toIso8601String(),
      'dataInicioListaCompras':
          instance.dataInicioListaCompras?.toIso8601String(),
      'dataFimListaCompras': instance.dataFimListaCompras?.toIso8601String(),
      'nomePlano': instance.nomePlano,
      'duracao': instance.duracao,
      'refeicoes': instance.refeicoes?.map((e) => e.toJson()).toList(),
      'horarioRefeicao': instance.horarioRefeicao?.toJson(),
      'lembretesNotificacoes': instance.lembretesNotificacoes?.toJson(),
      'tamanhoCopo': instance.tamanhoCopo,
      'lembreteHidratacao': instance.lembreteHidratacao,
    };

HorarioRefeicao _$HorarioRefeicaoFromJson(Map json) => HorarioRefeicao(
      cafeManha: json['cafeManha'] as String?,
      lancheManha: json['lancheManha'] as String?,
      almoco: json['almoco'] as String?,
      lancheTarde: json['lancheTarde'] as String?,
      jantar: json['jantar'] as String?,
      ceia: json['ceia'] as String?,
    );

Map<String, dynamic> _$HorarioRefeicaoToJson(HorarioRefeicao instance) =>
    <String, dynamic>{
      'cafeManha': instance.cafeManha,
      'lancheManha': instance.lancheManha,
      'almoco': instance.almoco,
      'lancheTarde': instance.lancheTarde,
      'jantar': instance.jantar,
      'ceia': instance.ceia,
    };

LembretesNotificacoes _$LembretesNotificacoesFromJson(Map json) =>
    LembretesNotificacoes(
      cafeManha: json['cafeManha'] as bool?,
      lancheManha: json['lancheManha'] as bool?,
      almoco: json['almoco'] as bool?,
      lancheTarde: json['lancheTarde'] as bool?,
      jantar: json['jantar'] as bool?,
      ceia: json['ceia'] as bool?,
      considerarTempoPreparo: json['considerarTempoPreparo'] as bool?,
    );

Map<String, dynamic> _$LembretesNotificacoesToJson(
        LembretesNotificacoes instance) =>
    <String, dynamic>{
      'cafeManha': instance.cafeManha,
      'lancheManha': instance.lancheManha,
      'almoco': instance.almoco,
      'lancheTarde': instance.lancheTarde,
      'jantar': instance.jantar,
      'ceia': instance.ceia,
      'considerarTempoPreparo': instance.considerarTempoPreparo,
    };

RefeicoesDoPlano _$RefeicoesDoPlanoFromJson(Map json) => RefeicoesDoPlano(
      receitaPara: json['receitaPara'] as String?,
      receitaParaODia: json['receitaParaODia'] == null
          ? null
          : DateTime.parse(json['receitaParaODia'] as String),
      refReceita: json['refReceita'] as String?,
      dataRefeicao: json['dataRefeicao'] == null
          ? null
          : DateTime.parse(json['dataRefeicao'] as String),
      receita: json['receita'] == null
          ? null
          : Refeicoes.fromJson(
              Map<String, dynamic>.from(json['receita'] as Map)),
      id: json['id'] as String?,
      ordem: json['ordem'] as num?,
      fotoRefeicao: json['fotoRefeicao'] as String?,
      ingredientes: json['ingredientes'] == null
          ? null
          : IngredientesAdicionados.fromJson(
              Map<String, dynamic>.from(json['ingredientes'] as Map)),
      legenda: json['legenda'] as String?,
    );

Map<String, dynamic> _$RefeicoesDoPlanoToJson(RefeicoesDoPlano instance) =>
    <String, dynamic>{
      'receitaPara': instance.receitaPara,
      'receitaParaODia': instance.receitaParaODia?.toIso8601String(),
      'refReceita': instance.refReceita,
      'dataRefeicao': instance.dataRefeicao?.toIso8601String(),
      'receita': instance.receita?.toJson(),
      'id': instance.id,
      'ordem': instance.ordem,
      'fotoRefeicao': instance.fotoRefeicao,
      'ingredientes': instance.ingredientes?.toJson(),
      'legenda': instance.legenda,
    };

Refeicoes _$RefeicoesFromJson(Map json) => Refeicoes(
      tipoRefeicao: (json['tipoRefeicao'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      fibra: (json['fibra'] as num?)?.toDouble(),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      media: (json['media'] as List<dynamic>?)
          ?.map((e) => Media.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      gordura: (json['gordura'] as num?)?.toDouble(),
      carboidrato: (json['carboidrato'] as num?)?.toDouble(),
      proteina: (json['proteina'] as num?)?.toDouble(),
      id: json['id'] as String?,
      peso: (json['peso'] as num?)?.toDouble(),
      valorEnergetico: (json['valorEnergetico'] as num?)?.toDouble(),
      restricoes: (json['restricoes'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      rendimento: json['rendimento'] as num?,
      alimentos: (json['alimentos'] as List<dynamic>?)
          ?.map((e) => Alimentos.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      docKey: json['docKey'] as String?,
      objetivo: (json['objetivo'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      nome: json['nome'] as String?,
      descricao: json['descricao'] as String?,
      selecionado: json['selecionado'] as bool? ?? false,
      favorita: json['favorita'] as bool?,
    );

Map<String, dynamic> _$RefeicoesToJson(Refeicoes instance) => <String, dynamic>{
      'tipoRefeicao': instance.tipoRefeicao,
      'fibra': instance.fibra,
      'tags': instance.tags,
      'media': instance.media?.map((e) => e.toJson()).toList(),
      'gordura': instance.gordura,
      'carboidrato': instance.carboidrato,
      'proteina': instance.proteina,
      'id': instance.id,
      'peso': instance.peso,
      'valorEnergetico': instance.valorEnergetico,
      'restricoes': instance.restricoes,
      'rendimento': instance.rendimento,
      'alimentos': instance.alimentos?.map((e) => e.toJson()).toList(),
      'docKey': instance.docKey,
      'objetivo': instance.objetivo,
      'nome': instance.nome,
      'descricao': instance.descricao,
      'selecionado': instance.selecionado,
      'favorita': instance.favorita,
    };

Media _$MediaFromJson(Map json) => Media(
      tipo: json['tipo'] as String?,
      posicao: (json['posicao'] as num?)?.toInt(),
      url: json['url'] as String?,
      principal: json['principal'] as bool?,
      dataUpload: (json['dataUpload'] as num?)?.toInt(),
    );

Map<String, dynamic> _$MediaToJson(Media instance) => <String, dynamic>{
      'tipo': instance.tipo,
      'posicao': instance.posicao,
      'url': instance.url,
      'principal': instance.principal,
      'dataUpload': instance.dataUpload,
    };

IngredientesAdicionados _$IngredientesAdicionadosFromJson(Map json) =>
    IngredientesAdicionados(
      ingredientes: (json['ingredientes'] as List<dynamic>?)
          ?.map(
              (e) => Ingredientes.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      refeicoes: (json['refeicoes'] as List<dynamic>?)
          ?.map((e) => Refeicoes.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$IngredientesAdicionadosToJson(
        IngredientesAdicionados instance) =>
    <String, dynamic>{
      'ingredientes': instance.ingredientes?.map((e) => e.toJson()).toList(),
      'refeicoes': instance.refeicoes?.map((e) => e.toJson()).toList(),
    };

Ingredientes _$IngredientesFromJson(Map json) => Ingredientes(
      proteina: (json['proteina'] as num?)?.toDouble(),
      docKey: json['docKey'] as String?,
      fibra: (json['fibra'] as num?)?.toDouble(),
      valorEnergetico: (json['valorEnergetico'] as num?)?.toDouble(),
      quantidadeMedidaPrimitiva:
          (json['quantidadeMedidaPrimitiva'] as num?)?.toDouble(),
      gordura: (json['gordura'] as num?)?.toDouble(),
      unidadeDeMedida: json['unidadeDeMedida'] as String?,
      quantidadeMedidaCaseira:
          (json['quantidadeMedidaCaseira'] as num?)?.toDouble(),
      medidaCaseiraNome: json['medidaCaseiraNome'] as String?,
      isMedidaCaseira: json['isMedidaCaseira'] as bool?,
      carboidrato: (json['carboidrato'] as num?)?.toDouble(),
      medidaPrimitiva: json['medidaPrimitiva'] as String?,
      restricoes: (json['restricoes'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      nome: json['nome'] as String?,
      tipo: json['tipo'] as String?,
      selecionado: json['selecionado'] as bool? ?? false,
    );

Map<String, dynamic> _$IngredientesToJson(Ingredientes instance) =>
    <String, dynamic>{
      'valorEnergetico': instance.valorEnergetico,
      'proteina': instance.proteina,
      'restricoes': instance.restricoes,
      'gordura': instance.gordura,
      'isMedidaCaseira': instance.isMedidaCaseira,
      'unidadeDeMedida': instance.unidadeDeMedida,
      'docKey': instance.docKey,
      'fibra': instance.fibra,
      'carboidrato': instance.carboidrato,
      'medidaCaseiraNome': instance.medidaCaseiraNome,
      'medidaPrimitiva': instance.medidaPrimitiva,
      'nome': instance.nome,
      'quantidadeMedidaCaseira': instance.quantidadeMedidaCaseira,
      'quantidadeMedidaPrimitiva': instance.quantidadeMedidaPrimitiva,
      'tipo': instance.tipo,
      'selecionado': instance.selecionado,
    };

Alimentos _$AlimentosFromJson(Map json) => Alimentos(
      isMedidaCaseira: json['isMedidaCaseira'] as bool?,
      unidadeDeMedida: json['unidadeDeMedida'] as String?,
      nome: json['nome'] as String?,
      medidaCaseiraNome: json['medidaCaseiraNome'] as String?,
      quantidadeMedidaCaseira: json['quantidadeMedidaCaseira'] as num?,
      modoPrepaparo: json['modoPrepaparo'] as String?,
      ingredientes: (json['ingredientes'] as List<dynamic>?)
          ?.map(
              (e) => Ingredientes.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$AlimentosToJson(Alimentos instance) => <String, dynamic>{
      'isMedidaCaseira': instance.isMedidaCaseira,
      'unidadeDeMedida': instance.unidadeDeMedida,
      'nome': instance.nome,
      'medidaCaseiraNome': instance.medidaCaseiraNome,
      'quantidadeMedidaCaseira': instance.quantidadeMedidaCaseira,
      'modoPrepaparo': instance.modoPrepaparo,
      'ingredientes': instance.ingredientes?.map((e) => e.toJson()).toList(),
    };

DicasNutri _$DicasNutriFromJson(Map json) => DicasNutri(
      codigo: json['codigo'] as num?,
      titulo: json['titulo'] as String?,
      categoria: json['categoria'] as String?,
      nivel: json['nivel'] as String?,
      tempo: json['tempo'] as String?,
      tipo: json['tipo'] as String?,
      dataCriacao: json['dataCriacao'] as num?,
      tituloSuperior: json['tituloSuperior'] as String?,
      descricaoSuperior: json['descricaoSuperior'] as String?,
      tituloInferior: json['tituloInferior'] as String?,
      descricaoInferior: json['descricaoInferior'] as String?,
      notaDicaNutri: json['notaDicaNutri'] as num?,
      destaque: json['destaque'] as bool?,
      dataCriacaoApresentar: json['dataCriacaoApresentar'] as String?,
      urlImagem: json['urlImagem'] as String?,
      responsavel: json['responsavel'] == null
          ? null
          : Responsavel.fromJson(
              Map<String, dynamic>.from(json['responsavel'] as Map)),
    );

Map<String, dynamic> _$DicasNutriToJson(DicasNutri instance) =>
    <String, dynamic>{
      'codigo': instance.codigo,
      'titulo': instance.titulo,
      'categoria': instance.categoria,
      'nivel': instance.nivel,
      'tempo': instance.tempo,
      'tipo': instance.tipo,
      'dataCriacao': instance.dataCriacao,
      'tituloSuperior': instance.tituloSuperior,
      'descricaoSuperior': instance.descricaoSuperior,
      'tituloInferior': instance.tituloInferior,
      'descricaoInferior': instance.descricaoInferior,
      'notaDicaNutri': instance.notaDicaNutri,
      'destaque': instance.destaque,
      'dataCriacaoApresentar': instance.dataCriacaoApresentar,
      'urlImagem': instance.urlImagem,
      'responsavel': instance.responsavel?.toJson(),
    };

Responsavel _$ResponsavelFromJson(Map json) => Responsavel(
      codigo: json['codigo'] as num?,
      nome: json['nome'] as String?,
      criador: json['criador'] as num?,
      foto: json['foto'] as String?,
      dicas: json['dicas'] == null
          ? null
          : DicasNutri.fromJson(
              Map<String, dynamic>.from(json['dicas'] as Map)),
    );

Map<String, dynamic> _$ResponsavelToJson(Responsavel instance) =>
    <String, dynamic>{
      'codigo': instance.codigo,
      'nome': instance.nome,
      'criador': instance.criador,
      'foto': instance.foto,
      'dicas': instance.dicas?.toJson(),
    };

RetornoConclusao _$RetornoConclusaoFromJson(Map json) => RetornoConclusao(
      valor: json['valor'] as num?,
    );

Map<String, dynamic> _$RetornoConclusaoToJson(RetornoConclusao instance) =>
    <String, dynamic>{
      'valor': instance.valor,
    };

PlanoNutricao _$PlanoNutricaoFromJson(Map json) => PlanoNutricao(
      quantidadeRefeicoes: json['quantidadeRefeicoes'] as num?,
      nota: json['nota'] as num?,
      descricao: json['descricao'] as String?,
      receitas: (json['receitas'] as List<dynamic>?)
          ?.map((e) =>
              RefeicoesDoPlano.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      variedade: json['variedade'] as String?,
      duracao: json['duracao'] as num?,
      orientacoes: json['orientacoes'] as String?,
      nomePlano: json['nomePlano'] as String?,
      nutrientes: json['nutrientes'] == null
          ? null
          : Nutrientes.fromJson(
              Map<String, dynamic>.from(json['nutrientes'] as Map)),
      refPlano: json['refPlano'] as String?,
      corGradienteFinal: json['corGradienteFinal'] as String?,
      corGradienteInicial: json['corGradienteInicial'] as String?,
      urlImagem: json['urlImagem'] as String?,
      subtitulo: json['subtitulo'] as String?,
      avaliacaoDoPlano: (json['avaliacaoDoPlano'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$PlanoNutricaoToJson(PlanoNutricao instance) =>
    <String, dynamic>{
      'quantidadeRefeicoes': instance.quantidadeRefeicoes,
      'nota': instance.nota,
      'descricao': instance.descricao,
      'receitas': instance.receitas?.map((e) => e.toJson()).toList(),
      'variedade': instance.variedade,
      'duracao': instance.duracao,
      'orientacoes': instance.orientacoes,
      'nomePlano': instance.nomePlano,
      'nutrientes': instance.nutrientes?.toJson(),
      'refPlano': instance.refPlano,
      'corGradienteFinal': instance.corGradienteFinal,
      'corGradienteInicial': instance.corGradienteInicial,
      'urlImagem': instance.urlImagem,
      'subtitulo': instance.subtitulo,
      'avaliacaoDoPlano': instance.avaliacaoDoPlano,
    };

Nutrientes _$NutrientesFromJson(Map json) => Nutrientes(
      proteinas: json['proteinas'] as num?,
      carboidratos: json['carboidratos'] as num?,
      kcal: json['kcal'] as num?,
      gorduras: json['gorduras'] as num?,
    );

Map<String, dynamic> _$NutrientesToJson(Nutrientes instance) =>
    <String, dynamic>{
      'proteinas': instance.proteinas,
      'carboidratos': instance.carboidratos,
      'kcal': instance.kcal,
      'gorduras': instance.gorduras,
    };

Novidades _$NovidadesFromJson(Map json) => Novidades(
      dataFim: json['dataFim'] == null
          ? null
          : DateTime.parse(json['dataFim'] as String),
      subitituloReceitas: json['subitituloReceitas'] as String?,
      excluido: json['excluido'] as bool?,
      descricao: json['descricao'] as String?,
      receitas: (json['receitas'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      titulo: json['titulo'] as String?,
      dataPostagem: json['dataPostagem'] == null
          ? null
          : DateTime.parse(json['dataPostagem'] as String),
      urlImagem: json['urlImagem'] as String?,
    );

Map<String, dynamic> _$NovidadesToJson(Novidades instance) => <String, dynamic>{
      'dataFim': instance.dataFim?.toIso8601String(),
      'subitituloReceitas': instance.subitituloReceitas,
      'excluido': instance.excluido,
      'descricao': instance.descricao,
      'receitas': instance.receitas,
      'titulo': instance.titulo,
      'dataPostagem': instance.dataPostagem?.toIso8601String(),
      'urlImagem': instance.urlImagem,
    };

PlanosNutricionaisFitStream _$PlanosNutricionaisFitStreamFromJson(Map json) =>
    PlanosNutricionaisFitStream(
      thumb: json['thumb'] as String?,
      criador: json['criador'] as String?,
      fotoCriador: json['fotoCriador'] as String?,
      codEmpresa: (json['codEmpresa'] as num?)?.toInt(),
      chave: json['chave'] as String?,
      quantidadeContents: (json['quantidadeContents'] as num?)?.toInt(),
      nome: json['nome'] as String?,
      descricao: json['descricao'] as String?,
      tipoObjetivo: json['tipoObjetivo'] as String?,
      ref: json['ref'] as String?,
      dataCriado: json['dataCriado'] as String?,
      dataEditado: json['dataEditado'] as String?,
      extracontent: (json['extracontent'] as List<dynamic>?)
          ?.map(
              (e) => Extracontent.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    )..content = (json['content'] as List<dynamic>?)
        ?.map((e) => Content.fromJson(Map<String, dynamic>.from(e as Map)))
        .toList();

Map<String, dynamic> _$PlanosNutricionaisFitStreamToJson(
        PlanosNutricionaisFitStream instance) =>
    <String, dynamic>{
      'thumb': instance.thumb,
      'criador': instance.criador,
      'fotoCriador': instance.fotoCriador,
      'codEmpresa': instance.codEmpresa,
      'chave': instance.chave,
      'quantidadeContents': instance.quantidadeContents,
      'nome': instance.nome,
      'descricao': instance.descricao,
      'tipoObjetivo': instance.tipoObjetivo,
      'ref': instance.ref,
      'dataCriado': instance.dataCriado,
      'dataEditado': instance.dataEditado,
      'extracontent': instance.extracontent?.map((e) => e.toJson()).toList(),
      'content': instance.content?.map((e) => e.toJson()).toList(),
    };

Content _$ContentFromJson(Map json) => Content(
      ordem: (json['ordem'] as num?)?.toInt(),
      titulo: json['titulo'] as String?,
      refPlano: json['refPlano'] as String?,
      ref: json['ref'] as String?,
      text: json['text'] as String?,
      concluido: json['concluido'] as bool?,
      like: json['like'] as bool?,
      thumb: json['thumb'] as String?,
      media: (json['media'] as List<dynamic>?)
          ?.map((e) => Media.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$ContentToJson(Content instance) => <String, dynamic>{
      'ordem': instance.ordem,
      'titulo': instance.titulo,
      'refPlano': instance.refPlano,
      'ref': instance.ref,
      'text': instance.text,
      'concluido': instance.concluido,
      'like': instance.like,
      'thumb': instance.thumb,
      'media': instance.media?.map((e) => e.toJson()).toList(),
    };

Extracontent _$ExtracontentFromJson(Map json) => Extracontent(
      tipo: json['tipo'] as String?,
      titulo: json['titulo'] as String?,
      duracao: (json['duracao'] as num?)?.toInt(),
      url: json['url'] as String?,
      descricao: json['descricao'] as String?,
    );

Map<String, dynamic> _$ExtracontentToJson(Extracontent instance) =>
    <String, dynamic>{
      'tipo': instance.tipo,
      'titulo': instance.titulo,
      'duracao': instance.duracao,
      'url': instance.url,
      'descricao': instance.descricao,
    };

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorNutricao on _ControladorNutricaoBase, Store {
  late final _$indexTelaInicialAtom =
      Atom(name: '_ControladorNutricaoBase.indexTelaInicial', context: context);

  @override
  num get indexTelaInicial {
    _$indexTelaInicialAtom.reportRead();
    return super.indexTelaInicial;
  }

  @override
  set indexTelaInicial(num value) {
    _$indexTelaInicialAtom.reportWrite(value, super.indexTelaInicial, () {
      super.indexTelaInicial = value;
    });
  }

  late final _$dataSelecionadaCalendarioAtom = Atom(
      name: '_ControladorNutricaoBase.dataSelecionadaCalendario',
      context: context);

  @override
  DateTime get dataSelecionadaCalendario {
    _$dataSelecionadaCalendarioAtom.reportRead();
    return super.dataSelecionadaCalendario;
  }

  @override
  set dataSelecionadaCalendario(DateTime value) {
    _$dataSelecionadaCalendarioAtom
        .reportWrite(value, super.dataSelecionadaCalendario, () {
      super.dataSelecionadaCalendario = value;
    });
  }

  late final _$progressoRefeicoesConsumidasCardAtom = Atom(
      name: '_ControladorNutricaoBase.progressoRefeicoesConsumidasCard',
      context: context);

  @override
  double get progressoRefeicoesConsumidasCard {
    _$progressoRefeicoesConsumidasCardAtom.reportRead();
    return super.progressoRefeicoesConsumidasCard;
  }

  @override
  set progressoRefeicoesConsumidasCard(double value) {
    _$progressoRefeicoesConsumidasCardAtom
        .reportWrite(value, super.progressoRefeicoesConsumidasCard, () {
      super.progressoRefeicoesConsumidasCard = value;
    });
  }

  late final _$gridSelecionadoAtom =
      Atom(name: '_ControladorNutricaoBase.gridSelecionado', context: context);

  @override
  bool get gridSelecionado {
    _$gridSelecionadoAtom.reportRead();
    return super.gridSelecionado;
  }

  @override
  set gridSelecionado(bool value) {
    _$gridSelecionadoAtom.reportWrite(value, super.gridSelecionado, () {
      super.gridSelecionado = value;
    });
  }

  late final _$pesoAtualUsuarioAtom =
      Atom(name: '_ControladorNutricaoBase.pesoAtualUsuario', context: context);

  @override
  num? get pesoAtualUsuario {
    _$pesoAtualUsuarioAtom.reportRead();
    return super.pesoAtualUsuario;
  }

  @override
  set pesoAtualUsuario(num? value) {
    _$pesoAtualUsuarioAtom.reportWrite(value, super.pesoAtualUsuario, () {
      super.pesoAtualUsuario = value;
    });
  }

  late final _$paginaAtualAnamneseAtom = Atom(
      name: '_ControladorNutricaoBase.paginaAtualAnamnese', context: context);

  @override
  num get paginaAtualAnamnese {
    _$paginaAtualAnamneseAtom.reportRead();
    return super.paginaAtualAnamnese;
  }

  @override
  set paginaAtualAnamnese(num value) {
    _$paginaAtualAnamneseAtom.reportWrite(value, super.paginaAtualAnamnese, () {
      super.paginaAtualAnamnese = value;
    });
  }

  late final _$quantidadeMlConsumidoNoDiaAtom = Atom(
      name: '_ControladorNutricaoBase.quantidadeMlConsumidoNoDia',
      context: context);

  @override
  num get quantidadeMlConsumidoNoDia {
    _$quantidadeMlConsumidoNoDiaAtom.reportRead();
    return super.quantidadeMlConsumidoNoDia;
  }

  @override
  set quantidadeMlConsumidoNoDia(num value) {
    _$quantidadeMlConsumidoNoDiaAtom
        .reportWrite(value, super.quantidadeMlConsumidoNoDia, () {
      super.quantidadeMlConsumidoNoDia = value;
    });
  }

  late final _$botaoContinuarAnamneseLiberadoAtom = Atom(
      name: '_ControladorNutricaoBase.botaoContinuarAnamneseLiberado',
      context: context);

  @override
  bool get botaoContinuarAnamneseLiberado {
    _$botaoContinuarAnamneseLiberadoAtom.reportRead();
    return super.botaoContinuarAnamneseLiberado;
  }

  @override
  set botaoContinuarAnamneseLiberado(bool value) {
    _$botaoContinuarAnamneseLiberadoAtom
        .reportWrite(value, super.botaoContinuarAnamneseLiberado, () {
      super.botaoContinuarAnamneseLiberado = value;
    });
  }

  late final _$planoNutricionalAlunoAtom = Atom(
      name: '_ControladorNutricaoBase.planoNutricionalAluno', context: context);

  @override
  PlanoNutricional get planoNutricionalAluno {
    _$planoNutricionalAlunoAtom.reportRead();
    return super.planoNutricionalAluno;
  }

  @override
  set planoNutricionalAluno(PlanoNutricional value) {
    _$planoNutricionalAlunoAtom.reportWrite(value, super.planoNutricionalAluno,
        () {
      super.planoNutricionalAluno = value;
    });
  }

  late final _$listaHistoricoHidratacaoAtom = Atom(
      name: '_ControladorNutricaoBase.listaHistoricoHidratacao',
      context: context);

  @override
  ObservableList<HistoricoHidratacao> get listaHistoricoHidratacao {
    _$listaHistoricoHidratacaoAtom.reportRead();
    return super.listaHistoricoHidratacao;
  }

  @override
  set listaHistoricoHidratacao(ObservableList<HistoricoHidratacao> value) {
    _$listaHistoricoHidratacaoAtom
        .reportWrite(value, super.listaHistoricoHidratacao, () {
      super.listaHistoricoHidratacao = value;
    });
  }

  late final _$listaPesoUsuarioAtom =
      Atom(name: '_ControladorNutricaoBase.listaPesoUsuario', context: context);

  @override
  ObservableList<PesoUsuario> get listaPesoUsuario {
    _$listaPesoUsuarioAtom.reportRead();
    return super.listaPesoUsuario;
  }

  @override
  set listaPesoUsuario(ObservableList<PesoUsuario> value) {
    _$listaPesoUsuarioAtom.reportWrite(value, super.listaPesoUsuario, () {
      super.listaPesoUsuario = value;
    });
  }

  late final _$listaNovidadesAtom =
      Atom(name: '_ControladorNutricaoBase.listaNovidades', context: context);

  @override
  ObservableList<Novidades> get listaNovidades {
    _$listaNovidadesAtom.reportRead();
    return super.listaNovidades;
  }

  @override
  set listaNovidades(ObservableList<Novidades> value) {
    _$listaNovidadesAtom.reportWrite(value, super.listaNovidades, () {
      super.listaNovidades = value;
    });
  }

  late final _$listaPlanoNutricaoAtom = Atom(
      name: '_ControladorNutricaoBase.listaPlanoNutricao', context: context);

  @override
  ObservableList<PlanoNutricao> get listaPlanoNutricao {
    _$listaPlanoNutricaoAtom.reportRead();
    return super.listaPlanoNutricao;
  }

  @override
  set listaPlanoNutricao(ObservableList<PlanoNutricao> value) {
    _$listaPlanoNutricaoAtom.reportWrite(value, super.listaPlanoNutricao, () {
      super.listaPlanoNutricao = value;
    });
  }

  late final _$listaPlanoNutricaoBackupAtom = Atom(
      name: '_ControladorNutricaoBase.listaPlanoNutricaoBackup',
      context: context);

  @override
  ObservableList<PlanoNutricao> get listaPlanoNutricaoBackup {
    _$listaPlanoNutricaoBackupAtom.reportRead();
    return super.listaPlanoNutricaoBackup;
  }

  @override
  set listaPlanoNutricaoBackup(ObservableList<PlanoNutricao> value) {
    _$listaPlanoNutricaoBackupAtom
        .reportWrite(value, super.listaPlanoNutricaoBackup, () {
      super.listaPlanoNutricaoBackup = value;
    });
  }

  late final _$listaAlimentosCriadosAtom = Atom(
      name: '_ControladorNutricaoBase.listaAlimentosCriados', context: context);

  @override
  ObservableList<AlimentoCriado> get listaAlimentosCriados {
    _$listaAlimentosCriadosAtom.reportRead();
    return super.listaAlimentosCriados;
  }

  @override
  set listaAlimentosCriados(ObservableList<AlimentoCriado> value) {
    _$listaAlimentosCriadosAtom.reportWrite(value, super.listaAlimentosCriados,
        () {
      super.listaAlimentosCriados = value;
    });
  }

  late final _$statusConsultarListaRefeicoesAtom = Atom(
      name: '_ControladorNutricaoBase.statusConsultarListaRefeicoes',
      context: context);

  @override
  ServiceStatus get statusConsultarListaRefeicoes {
    _$statusConsultarListaRefeicoesAtom.reportRead();
    return super.statusConsultarListaRefeicoes;
  }

  @override
  set statusConsultarListaRefeicoes(ServiceStatus value) {
    _$statusConsultarListaRefeicoesAtom
        .reportWrite(value, super.statusConsultarListaRefeicoes, () {
      super.statusConsultarListaRefeicoes = value;
    });
  }

  late final _$statusConsultarRefeicoesAtom = Atom(
      name: '_ControladorNutricaoBase.statusConsultarRefeicoes',
      context: context);

  @override
  ServiceStatus get statusConsultarRefeicoes {
    _$statusConsultarRefeicoesAtom.reportRead();
    return super.statusConsultarRefeicoes;
  }

  @override
  set statusConsultarRefeicoes(ServiceStatus value) {
    _$statusConsultarRefeicoesAtom
        .reportWrite(value, super.statusConsultarRefeicoes, () {
      super.statusConsultarRefeicoes = value;
    });
  }

  late final _$statusConsultarListaIngredientesAtom = Atom(
      name: '_ControladorNutricaoBase.statusConsultarListaIngredientes',
      context: context);

  @override
  ServiceStatus get statusConsultarListaIngredientes {
    _$statusConsultarListaIngredientesAtom.reportRead();
    return super.statusConsultarListaIngredientes;
  }

  @override
  set statusConsultarListaIngredientes(ServiceStatus value) {
    _$statusConsultarListaIngredientesAtom
        .reportWrite(value, super.statusConsultarListaIngredientes, () {
      super.statusConsultarListaIngredientes = value;
    });
  }

  late final _$statusListaComprasAtom = Atom(
      name: '_ControladorNutricaoBase.statusListaCompras', context: context);

  @override
  ServiceStatus get statusListaCompras {
    _$statusListaComprasAtom.reportRead();
    return super.statusListaCompras;
  }

  @override
  set statusListaCompras(ServiceStatus value) {
    _$statusListaComprasAtom.reportWrite(value, super.statusListaCompras, () {
      super.statusListaCompras = value;
    });
  }

  late final _$statusConsultarPlanosNutricionaisAtom = Atom(
      name: '_ControladorNutricaoBase.statusConsultarPlanosNutricionais',
      context: context);

  @override
  ServiceStatus get statusConsultarPlanosNutricionais {
    _$statusConsultarPlanosNutricionaisAtom.reportRead();
    return super.statusConsultarPlanosNutricionais;
  }

  @override
  set statusConsultarPlanosNutricionais(ServiceStatus value) {
    _$statusConsultarPlanosNutricionaisAtom
        .reportWrite(value, super.statusConsultarPlanosNutricionais, () {
      super.statusConsultarPlanosNutricionais = value;
    });
  }

  late final _$statusConsultarNovidadesAtom = Atom(
      name: '_ControladorNutricaoBase.statusConsultarNovidades',
      context: context);

  @override
  ServiceStatus get statusConsultarNovidades {
    _$statusConsultarNovidadesAtom.reportRead();
    return super.statusConsultarNovidades;
  }

  @override
  set statusConsultarNovidades(ServiceStatus value) {
    _$statusConsultarNovidadesAtom
        .reportWrite(value, super.statusConsultarNovidades, () {
      super.statusConsultarNovidades = value;
    });
  }

  late final _$statusCarregandoDicasAtom = Atom(
      name: '_ControladorNutricaoBase.statusCarregandoDicas', context: context);

  @override
  ServiceStatus get statusCarregandoDicas {
    _$statusCarregandoDicasAtom.reportRead();
    return super.statusCarregandoDicas;
  }

  @override
  set statusCarregandoDicas(ServiceStatus value) {
    _$statusCarregandoDicasAtom.reportWrite(value, super.statusCarregandoDicas,
        () {
      super.statusCarregandoDicas = value;
    });
  }

  late final _$statusTelaPrincipalAtom = Atom(
      name: '_ControladorNutricaoBase.statusTelaPrincipal', context: context);

  @override
  StatusTelaInicial get statusTelaPrincipal {
    _$statusTelaPrincipalAtom.reportRead();
    return super.statusTelaPrincipal;
  }

  @override
  set statusTelaPrincipal(StatusTelaInicial value) {
    _$statusTelaPrincipalAtom.reportWrite(value, super.statusTelaPrincipal, () {
      super.statusTelaPrincipal = value;
    });
  }

  late final _$statusGerarRefeicoesPlanoAtom = Atom(
      name: '_ControladorNutricaoBase.statusGerarRefeicoesPlano',
      context: context);

  @override
  ServiceStatus get statusGerarRefeicoesPlano {
    _$statusGerarRefeicoesPlanoAtom.reportRead();
    return super.statusGerarRefeicoesPlano;
  }

  @override
  set statusGerarRefeicoesPlano(ServiceStatus value) {
    _$statusGerarRefeicoesPlanoAtom
        .reportWrite(value, super.statusGerarRefeicoesPlano, () {
      super.statusGerarRefeicoesPlano = value;
    });
  }

  late final _$statusConsultarReceitaSubstituicaoAtom = Atom(
      name: '_ControladorNutricaoBase.statusConsultarReceitaSubstituicao',
      context: context);

  @override
  ServiceStatus get statusConsultarReceitaSubstituicao {
    _$statusConsultarReceitaSubstituicaoAtom.reportRead();
    return super.statusConsultarReceitaSubstituicao;
  }

  @override
  set statusConsultarReceitaSubstituicao(ServiceStatus value) {
    _$statusConsultarReceitaSubstituicaoAtom
        .reportWrite(value, super.statusConsultarReceitaSubstituicao, () {
      super.statusConsultarReceitaSubstituicao = value;
    });
  }

  late final _$listaRefeicoesAtom =
      Atom(name: '_ControladorNutricaoBase.listaRefeicoes', context: context);

  @override
  ObservableList<Refeicoes> get listaRefeicoes {
    _$listaRefeicoesAtom.reportRead();
    return super.listaRefeicoes;
  }

  @override
  set listaRefeicoes(ObservableList<Refeicoes> value) {
    _$listaRefeicoesAtom.reportWrite(value, super.listaRefeicoes, () {
      super.listaRefeicoes = value;
    });
  }

  late final _$listaRefeicoesBackupAtom = Atom(
      name: '_ControladorNutricaoBase.listaRefeicoesBackup', context: context);

  @override
  ObservableList<Refeicoes> get listaRefeicoesBackup {
    _$listaRefeicoesBackupAtom.reportRead();
    return super.listaRefeicoesBackup;
  }

  @override
  set listaRefeicoesBackup(ObservableList<Refeicoes> value) {
    _$listaRefeicoesBackupAtom.reportWrite(value, super.listaRefeicoesBackup,
        () {
      super.listaRefeicoesBackup = value;
    });
  }

  late final _$listaIngredientesAtom = Atom(
      name: '_ControladorNutricaoBase.listaIngredientes', context: context);

  @override
  ObservableList<Ingredientes> get listaIngredientes {
    _$listaIngredientesAtom.reportRead();
    return super.listaIngredientes;
  }

  @override
  set listaIngredientes(ObservableList<Ingredientes> value) {
    _$listaIngredientesAtom.reportWrite(value, super.listaIngredientes, () {
      super.listaIngredientes = value;
    });
  }

  late final _$listaIngredientesBackupAtom = Atom(
      name: '_ControladorNutricaoBase.listaIngredientesBackup',
      context: context);

  @override
  ObservableList<Ingredientes> get listaIngredientesBackup {
    _$listaIngredientesBackupAtom.reportRead();
    return super.listaIngredientesBackup;
  }

  @override
  set listaIngredientesBackup(ObservableList<Ingredientes> value) {
    _$listaIngredientesBackupAtom
        .reportWrite(value, super.listaIngredientesBackup, () {
      super.listaIngredientesBackup = value;
    });
  }

  late final _$listaIngredientesAdicionadosAtom = Atom(
      name: '_ControladorNutricaoBase.listaIngredientesAdicionados',
      context: context);

  @override
  ObservableList<Ingredientes> get listaIngredientesAdicionados {
    _$listaIngredientesAdicionadosAtom.reportRead();
    return super.listaIngredientesAdicionados;
  }

  @override
  set listaIngredientesAdicionados(ObservableList<Ingredientes> value) {
    _$listaIngredientesAdicionadosAtom
        .reportWrite(value, super.listaIngredientesAdicionados, () {
      super.listaIngredientesAdicionados = value;
    });
  }

  late final _$listaRefeicoesAdicionadosAtom = Atom(
      name: '_ControladorNutricaoBase.listaRefeicoesAdicionados',
      context: context);

  @override
  ObservableList<Refeicoes> get listaRefeicoesAdicionados {
    _$listaRefeicoesAdicionadosAtom.reportRead();
    return super.listaRefeicoesAdicionados;
  }

  @override
  set listaRefeicoesAdicionados(ObservableList<Refeicoes> value) {
    _$listaRefeicoesAdicionadosAtom
        .reportWrite(value, super.listaRefeicoesAdicionados, () {
      super.listaRefeicoesAdicionados = value;
    });
  }

  late final _$listaRefeicoesParaConfirmacaoAtom = Atom(
      name: '_ControladorNutricaoBase.listaRefeicoesParaConfirmacao',
      context: context);

  @override
  ObservableList<ReceitasAgrupadas> get listaRefeicoesParaConfirmacao {
    _$listaRefeicoesParaConfirmacaoAtom.reportRead();
    return super.listaRefeicoesParaConfirmacao;
  }

  @override
  set listaRefeicoesParaConfirmacao(ObservableList<ReceitasAgrupadas> value) {
    _$listaRefeicoesParaConfirmacaoAtom
        .reportWrite(value, super.listaRefeicoesParaConfirmacao, () {
      super.listaRefeicoesParaConfirmacao = value;
    });
  }

  late final _$refeicoesListaComprasAtom = Atom(
      name: '_ControladorNutricaoBase.refeicoesListaCompras', context: context);

  @override
  ObservableList<ListaCompra> get refeicoesListaCompras {
    _$refeicoesListaComprasAtom.reportRead();
    return super.refeicoesListaCompras;
  }

  @override
  set refeicoesListaCompras(ObservableList<ListaCompra> value) {
    _$refeicoesListaComprasAtom.reportWrite(value, super.refeicoesListaCompras,
        () {
      super.refeicoesListaCompras = value;
    });
  }

  late final _$listaDicasAtom =
      Atom(name: '_ControladorNutricaoBase.listaDicas', context: context);

  @override
  ObservableList<DicasNutri> get listaDicas {
    _$listaDicasAtom.reportRead();
    return super.listaDicas;
  }

  @override
  set listaDicas(ObservableList<DicasNutri> value) {
    _$listaDicasAtom.reportWrite(value, super.listaDicas, () {
      super.listaDicas = value;
    });
  }

  late final _$listaComprasAtom =
      Atom(name: '_ControladorNutricaoBase.listaCompras', context: context);

  @override
  ObservableList<ItemListaCompras> get listaCompras {
    _$listaComprasAtom.reportRead();
    return super.listaCompras;
  }

  @override
  set listaCompras(ObservableList<ItemListaCompras> value) {
    _$listaComprasAtom.reportWrite(value, super.listaCompras, () {
      super.listaCompras = value;
    });
  }

  late final _$retornoCalendarioAtom = Atom(
      name: '_ControladorNutricaoBase.retornoCalendario', context: context);

  @override
  ObservableList<CalendarioNutricaoClass> get retornoCalendario {
    _$retornoCalendarioAtom.reportRead();
    return super.retornoCalendario;
  }

  @override
  set retornoCalendario(ObservableList<CalendarioNutricaoClass> value) {
    _$retornoCalendarioAtom.reportWrite(value, super.retornoCalendario, () {
      super.retornoCalendario = value;
    });
  }

  late final _$listaRefeicoesParaSubstituicaoAtom = Atom(
      name: '_ControladorNutricaoBase.listaRefeicoesParaSubstituicao',
      context: context);

  @override
  ObservableList<Refeicoes> get listaRefeicoesParaSubstituicao {
    _$listaRefeicoesParaSubstituicaoAtom.reportRead();
    return super.listaRefeicoesParaSubstituicao;
  }

  @override
  set listaRefeicoesParaSubstituicao(ObservableList<Refeicoes> value) {
    _$listaRefeicoesParaSubstituicaoAtom
        .reportWrite(value, super.listaRefeicoesParaSubstituicao, () {
      super.listaRefeicoesParaSubstituicao = value;
    });
  }

  late final _$listaRefeicoesFavoritasBackupAtom = Atom(
      name: '_ControladorNutricaoBase.listaRefeicoesFavoritasBackup',
      context: context);

  @override
  ObservableList<Refeicoes> get listaRefeicoesFavoritasBackup {
    _$listaRefeicoesFavoritasBackupAtom.reportRead();
    return super.listaRefeicoesFavoritasBackup;
  }

  @override
  set listaRefeicoesFavoritasBackup(ObservableList<Refeicoes> value) {
    _$listaRefeicoesFavoritasBackupAtom
        .reportWrite(value, super.listaRefeicoesFavoritasBackup, () {
      super.listaRefeicoesFavoritasBackup = value;
    });
  }

  late final _$listaRefeicoesFavoritasAtom = Atom(
      name: '_ControladorNutricaoBase.listaRefeicoesFavoritas',
      context: context);

  @override
  ObservableList<Refeicoes> get listaRefeicoesFavoritas {
    _$listaRefeicoesFavoritasAtom.reportRead();
    return super.listaRefeicoesFavoritas;
  }

  @override
  set listaRefeicoesFavoritas(ObservableList<Refeicoes> value) {
    _$listaRefeicoesFavoritasAtom
        .reportWrite(value, super.listaRefeicoesFavoritas, () {
      super.listaRefeicoesFavoritas = value;
    });
  }

  late final _$listaHistoricoRespiracaoAtom = Atom(
      name: '_ControladorNutricaoBase.listaHistoricoRespiracao',
      context: context);

  @override
  ObservableList<HistoricoRespiracao> get listaHistoricoRespiracao {
    _$listaHistoricoRespiracaoAtom.reportRead();
    return super.listaHistoricoRespiracao;
  }

  @override
  set listaHistoricoRespiracao(ObservableList<HistoricoRespiracao> value) {
    _$listaHistoricoRespiracaoAtom
        .reportWrite(value, super.listaHistoricoRespiracao, () {
      super.listaHistoricoRespiracao = value;
    });
  }

  late final _$dataInicialListaComprasAtom = Atom(
      name: '_ControladorNutricaoBase.dataInicialListaCompras',
      context: context);

  @override
  DateTime? get dataInicialListaCompras {
    _$dataInicialListaComprasAtom.reportRead();
    return super.dataInicialListaCompras;
  }

  @override
  set dataInicialListaCompras(DateTime? value) {
    _$dataInicialListaComprasAtom
        .reportWrite(value, super.dataInicialListaCompras, () {
      super.dataInicialListaCompras = value;
    });
  }

  late final _$dataFinalListaComprasAtom = Atom(
      name: '_ControladorNutricaoBase.dataFinalListaCompras', context: context);

  @override
  DateTime? get dataFinalListaCompras {
    _$dataFinalListaComprasAtom.reportRead();
    return super.dataFinalListaCompras;
  }

  @override
  set dataFinalListaCompras(DateTime? value) {
    _$dataFinalListaComprasAtom.reportWrite(value, super.dataFinalListaCompras,
        () {
      super.dataFinalListaCompras = value;
    });
  }

  late final _$carboidratosConsumidoNoDiaAtom = Atom(
      name: '_ControladorNutricaoBase.carboidratosConsumidoNoDia',
      context: context);

  @override
  num get carboidratosConsumidoNoDia {
    _$carboidratosConsumidoNoDiaAtom.reportRead();
    return super.carboidratosConsumidoNoDia;
  }

  @override
  set carboidratosConsumidoNoDia(num value) {
    _$carboidratosConsumidoNoDiaAtom
        .reportWrite(value, super.carboidratosConsumidoNoDia, () {
      super.carboidratosConsumidoNoDia = value;
    });
  }

  late final _$carboidratosQuantidadeTotalNoDiaAtom = Atom(
      name: '_ControladorNutricaoBase.carboidratosQuantidadeTotalNoDia',
      context: context);

  @override
  num get carboidratosQuantidadeTotalNoDia {
    _$carboidratosQuantidadeTotalNoDiaAtom.reportRead();
    return super.carboidratosQuantidadeTotalNoDia;
  }

  @override
  set carboidratosQuantidadeTotalNoDia(num value) {
    _$carboidratosQuantidadeTotalNoDiaAtom
        .reportWrite(value, super.carboidratosQuantidadeTotalNoDia, () {
      super.carboidratosQuantidadeTotalNoDia = value;
    });
  }

  late final _$carboidratosConsumidoNoDiaProgressoAtom = Atom(
      name: '_ControladorNutricaoBase.carboidratosConsumidoNoDiaProgresso',
      context: context);

  @override
  num get carboidratosConsumidoNoDiaProgresso {
    _$carboidratosConsumidoNoDiaProgressoAtom.reportRead();
    return super.carboidratosConsumidoNoDiaProgresso;
  }

  @override
  set carboidratosConsumidoNoDiaProgresso(num value) {
    _$carboidratosConsumidoNoDiaProgressoAtom
        .reportWrite(value, super.carboidratosConsumidoNoDiaProgresso, () {
      super.carboidratosConsumidoNoDiaProgresso = value;
    });
  }

  late final _$proteinasConsumidoNoDiaAtom = Atom(
      name: '_ControladorNutricaoBase.proteinasConsumidoNoDia',
      context: context);

  @override
  num get proteinasConsumidoNoDia {
    _$proteinasConsumidoNoDiaAtom.reportRead();
    return super.proteinasConsumidoNoDia;
  }

  @override
  set proteinasConsumidoNoDia(num value) {
    _$proteinasConsumidoNoDiaAtom
        .reportWrite(value, super.proteinasConsumidoNoDia, () {
      super.proteinasConsumidoNoDia = value;
    });
  }

  late final _$proteinasQuantidadeTotalNoDiaAtom = Atom(
      name: '_ControladorNutricaoBase.proteinasQuantidadeTotalNoDia',
      context: context);

  @override
  num get proteinasQuantidadeTotalNoDia {
    _$proteinasQuantidadeTotalNoDiaAtom.reportRead();
    return super.proteinasQuantidadeTotalNoDia;
  }

  @override
  set proteinasQuantidadeTotalNoDia(num value) {
    _$proteinasQuantidadeTotalNoDiaAtom
        .reportWrite(value, super.proteinasQuantidadeTotalNoDia, () {
      super.proteinasQuantidadeTotalNoDia = value;
    });
  }

  late final _$proteinasConsumidoNoDiaProgressoAtom = Atom(
      name: '_ControladorNutricaoBase.proteinasConsumidoNoDiaProgresso',
      context: context);

  @override
  num get proteinasConsumidoNoDiaProgresso {
    _$proteinasConsumidoNoDiaProgressoAtom.reportRead();
    return super.proteinasConsumidoNoDiaProgresso;
  }

  @override
  set proteinasConsumidoNoDiaProgresso(num value) {
    _$proteinasConsumidoNoDiaProgressoAtom
        .reportWrite(value, super.proteinasConsumidoNoDiaProgresso, () {
      super.proteinasConsumidoNoDiaProgresso = value;
    });
  }

  late final _$gordurasConsumidoNoDiaAtom = Atom(
      name: '_ControladorNutricaoBase.gordurasConsumidoNoDia',
      context: context);

  @override
  num get gordurasConsumidoNoDia {
    _$gordurasConsumidoNoDiaAtom.reportRead();
    return super.gordurasConsumidoNoDia;
  }

  @override
  set gordurasConsumidoNoDia(num value) {
    _$gordurasConsumidoNoDiaAtom
        .reportWrite(value, super.gordurasConsumidoNoDia, () {
      super.gordurasConsumidoNoDia = value;
    });
  }

  late final _$gordurasQuantidadeTotalNoDiaAtom = Atom(
      name: '_ControladorNutricaoBase.gordurasQuantidadeTotalNoDia',
      context: context);

  @override
  num get gordurasQuantidadeTotalNoDia {
    _$gordurasQuantidadeTotalNoDiaAtom.reportRead();
    return super.gordurasQuantidadeTotalNoDia;
  }

  @override
  set gordurasQuantidadeTotalNoDia(num value) {
    _$gordurasQuantidadeTotalNoDiaAtom
        .reportWrite(value, super.gordurasQuantidadeTotalNoDia, () {
      super.gordurasQuantidadeTotalNoDia = value;
    });
  }

  late final _$gordurasConsumidoNoDiaProgressoAtom = Atom(
      name: '_ControladorNutricaoBase.gordurasConsumidoNoDiaProgresso',
      context: context);

  @override
  num get gordurasConsumidoNoDiaProgresso {
    _$gordurasConsumidoNoDiaProgressoAtom.reportRead();
    return super.gordurasConsumidoNoDiaProgresso;
  }

  @override
  set gordurasConsumidoNoDiaProgresso(num value) {
    _$gordurasConsumidoNoDiaProgressoAtom
        .reportWrite(value, super.gordurasConsumidoNoDiaProgresso, () {
      super.gordurasConsumidoNoDiaProgresso = value;
    });
  }

  late final _$fibrasConsumidoNoDiaAtom = Atom(
      name: '_ControladorNutricaoBase.fibrasConsumidoNoDia', context: context);

  @override
  num get fibrasConsumidoNoDia {
    _$fibrasConsumidoNoDiaAtom.reportRead();
    return super.fibrasConsumidoNoDia;
  }

  @override
  set fibrasConsumidoNoDia(num value) {
    _$fibrasConsumidoNoDiaAtom.reportWrite(value, super.fibrasConsumidoNoDia,
        () {
      super.fibrasConsumidoNoDia = value;
    });
  }

  late final _$fibrasQuantidadeTotalNoDiaAtom = Atom(
      name: '_ControladorNutricaoBase.fibrasQuantidadeTotalNoDia',
      context: context);

  @override
  num get fibrasQuantidadeTotalNoDia {
    _$fibrasQuantidadeTotalNoDiaAtom.reportRead();
    return super.fibrasQuantidadeTotalNoDia;
  }

  @override
  set fibrasQuantidadeTotalNoDia(num value) {
    _$fibrasQuantidadeTotalNoDiaAtom
        .reportWrite(value, super.fibrasQuantidadeTotalNoDia, () {
      super.fibrasQuantidadeTotalNoDia = value;
    });
  }

  late final _$fibrasConsumidoNoDiaProgressoAtom = Atom(
      name: '_ControladorNutricaoBase.fibrasConsumidoNoDiaProgresso',
      context: context);

  @override
  num get fibrasConsumidoNoDiaProgresso {
    _$fibrasConsumidoNoDiaProgressoAtom.reportRead();
    return super.fibrasConsumidoNoDiaProgresso;
  }

  @override
  set fibrasConsumidoNoDiaProgresso(num value) {
    _$fibrasConsumidoNoDiaProgressoAtom
        .reportWrite(value, super.fibrasConsumidoNoDiaProgresso, () {
      super.fibrasConsumidoNoDiaProgresso = value;
    });
  }

  late final _$valorTotalConclusaoPlanoAtom = Atom(
      name: '_ControladorNutricaoBase.valorTotalConclusaoPlano',
      context: context);

  @override
  num? get valorTotalConclusaoPlano {
    _$valorTotalConclusaoPlanoAtom.reportRead();
    return super.valorTotalConclusaoPlano;
  }

  @override
  set valorTotalConclusaoPlano(num? value) {
    _$valorTotalConclusaoPlanoAtom
        .reportWrite(value, super.valorTotalConclusaoPlano, () {
      super.valorTotalConclusaoPlano = value;
    });
  }

  late final _$pesoMaximoAtom =
      Atom(name: '_ControladorNutricaoBase.pesoMaximo', context: context);

  @override
  num? get pesoMaximo {
    _$pesoMaximoAtom.reportRead();
    return super.pesoMaximo;
  }

  @override
  set pesoMaximo(num? value) {
    _$pesoMaximoAtom.reportWrite(value, super.pesoMaximo, () {
      super.pesoMaximo = value;
    });
  }

  late final _$dataPesoMaximoAtom =
      Atom(name: '_ControladorNutricaoBase.dataPesoMaximo', context: context);

  @override
  DateTime? get dataPesoMaximo {
    _$dataPesoMaximoAtom.reportRead();
    return super.dataPesoMaximo;
  }

  @override
  set dataPesoMaximo(DateTime? value) {
    _$dataPesoMaximoAtom.reportWrite(value, super.dataPesoMaximo, () {
      super.dataPesoMaximo = value;
    });
  }

  late final _$pesoMinimoAtom =
      Atom(name: '_ControladorNutricaoBase.pesoMinimo', context: context);

  @override
  num? get pesoMinimo {
    _$pesoMinimoAtom.reportRead();
    return super.pesoMinimo;
  }

  @override
  set pesoMinimo(num? value) {
    _$pesoMinimoAtom.reportWrite(value, super.pesoMinimo, () {
      super.pesoMinimo = value;
    });
  }

  late final _$dataPesoMinimoAtom =
      Atom(name: '_ControladorNutricaoBase.dataPesoMinimo', context: context);

  @override
  DateTime? get dataPesoMinimo {
    _$dataPesoMinimoAtom.reportRead();
    return super.dataPesoMinimo;
  }

  @override
  set dataPesoMinimo(DateTime? value) {
    _$dataPesoMinimoAtom.reportWrite(value, super.dataPesoMinimo, () {
      super.dataPesoMinimo = value;
    });
  }

  late final _$quantiadeRefeicoesConsumidasDiaProgressoAtom = Atom(
      name: '_ControladorNutricaoBase.quantiadeRefeicoesConsumidasDiaProgresso',
      context: context);

  @override
  String get quantiadeRefeicoesConsumidasDiaProgresso {
    _$quantiadeRefeicoesConsumidasDiaProgressoAtom.reportRead();
    return super.quantiadeRefeicoesConsumidasDiaProgresso;
  }

  @override
  set quantiadeRefeicoesConsumidasDiaProgresso(String value) {
    _$quantiadeRefeicoesConsumidasDiaProgressoAtom
        .reportWrite(value, super.quantiadeRefeicoesConsumidasDiaProgresso, () {
      super.quantiadeRefeicoesConsumidasDiaProgresso = value;
    });
  }

  late final _$listaReceitasFavoritasAtom = Atom(
      name: '_ControladorNutricaoBase.listaReceitasFavoritas',
      context: context);

  @override
  ObservableList<ReceitaFavorita> get listaReceitasFavoritas {
    _$listaReceitasFavoritasAtom.reportRead();
    return super.listaReceitasFavoritas;
  }

  @override
  set listaReceitasFavoritas(ObservableList<ReceitaFavorita> value) {
    _$listaReceitasFavoritasAtom
        .reportWrite(value, super.listaReceitasFavoritas, () {
      super.listaReceitasFavoritas = value;
    });
  }

  late final _$listaPlanoNutricionalFitstreamAtom = Atom(
      name: '_ControladorNutricaoBase.listaPlanoNutricionalFitstream',
      context: context);

  @override
  ObservableList<PlanosNutricionaisFitStream>
      get listaPlanoNutricionalFitstream {
    _$listaPlanoNutricionalFitstreamAtom.reportRead();
    return super.listaPlanoNutricionalFitstream;
  }

  @override
  set listaPlanoNutricionalFitstream(
      ObservableList<PlanosNutricionaisFitStream> value) {
    _$listaPlanoNutricionalFitstreamAtom
        .reportWrite(value, super.listaPlanoNutricionalFitstream, () {
      super.listaPlanoNutricionalFitstream = value;
    });
  }

  late final _$planoNutricionalSelecionadoFitstreamAtom = Atom(
      name: '_ControladorNutricaoBase.planoNutricionalSelecionadoFitstream',
      context: context);

  @override
  PlanosNutricionaisFitStream get planoNutricionalSelecionadoFitstream {
    _$planoNutricionalSelecionadoFitstreamAtom.reportRead();
    return super.planoNutricionalSelecionadoFitstream;
  }

  @override
  set planoNutricionalSelecionadoFitstream(PlanosNutricionaisFitStream value) {
    _$planoNutricionalSelecionadoFitstreamAtom
        .reportWrite(value, super.planoNutricionalSelecionadoFitstream, () {
      super.planoNutricionalSelecionadoFitstream = value;
    });
  }

  late final _$statusConsultarPlanoNutricionaisFitStreamAtom = Atom(
      name:
          '_ControladorNutricaoBase.statusConsultarPlanoNutricionaisFitStream',
      context: context);

  @override
  ServiceStatus get statusConsultarPlanoNutricionaisFitStream {
    _$statusConsultarPlanoNutricionaisFitStreamAtom.reportRead();
    return super.statusConsultarPlanoNutricionaisFitStream;
  }

  @override
  set statusConsultarPlanoNutricionaisFitStream(ServiceStatus value) {
    _$statusConsultarPlanoNutricionaisFitStreamAtom.reportWrite(
        value, super.statusConsultarPlanoNutricionaisFitStream, () {
      super.statusConsultarPlanoNutricionaisFitStream = value;
    });
  }

  late final _$_ControladorNutricaoBaseActionController =
      ActionController(name: '_ControladorNutricaoBase', context: context);

  @override
  dynamic popularDadosFicticios() {
    final _$actionInfo = _$_ControladorNutricaoBaseActionController.startAction(
        name: '_ControladorNutricaoBase.popularDadosFicticios');
    try {
      return super.popularDadosFicticios();
    } finally {
      _$_ControladorNutricaoBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
indexTelaInicial: ${indexTelaInicial},
dataSelecionadaCalendario: ${dataSelecionadaCalendario},
progressoRefeicoesConsumidasCard: ${progressoRefeicoesConsumidasCard},
gridSelecionado: ${gridSelecionado},
pesoAtualUsuario: ${pesoAtualUsuario},
paginaAtualAnamnese: ${paginaAtualAnamnese},
quantidadeMlConsumidoNoDia: ${quantidadeMlConsumidoNoDia},
botaoContinuarAnamneseLiberado: ${botaoContinuarAnamneseLiberado},
planoNutricionalAluno: ${planoNutricionalAluno},
listaHistoricoHidratacao: ${listaHistoricoHidratacao},
listaPesoUsuario: ${listaPesoUsuario},
listaNovidades: ${listaNovidades},
listaPlanoNutricao: ${listaPlanoNutricao},
listaPlanoNutricaoBackup: ${listaPlanoNutricaoBackup},
listaAlimentosCriados: ${listaAlimentosCriados},
statusConsultarListaRefeicoes: ${statusConsultarListaRefeicoes},
statusConsultarRefeicoes: ${statusConsultarRefeicoes},
statusConsultarListaIngredientes: ${statusConsultarListaIngredientes},
statusListaCompras: ${statusListaCompras},
statusConsultarPlanosNutricionais: ${statusConsultarPlanosNutricionais},
statusConsultarNovidades: ${statusConsultarNovidades},
statusCarregandoDicas: ${statusCarregandoDicas},
statusTelaPrincipal: ${statusTelaPrincipal},
statusGerarRefeicoesPlano: ${statusGerarRefeicoesPlano},
statusConsultarReceitaSubstituicao: ${statusConsultarReceitaSubstituicao},
listaRefeicoes: ${listaRefeicoes},
listaRefeicoesBackup: ${listaRefeicoesBackup},
listaIngredientes: ${listaIngredientes},
listaIngredientesBackup: ${listaIngredientesBackup},
listaIngredientesAdicionados: ${listaIngredientesAdicionados},
listaRefeicoesAdicionados: ${listaRefeicoesAdicionados},
listaRefeicoesParaConfirmacao: ${listaRefeicoesParaConfirmacao},
refeicoesListaCompras: ${refeicoesListaCompras},
listaDicas: ${listaDicas},
listaCompras: ${listaCompras},
retornoCalendario: ${retornoCalendario},
listaRefeicoesParaSubstituicao: ${listaRefeicoesParaSubstituicao},
listaRefeicoesFavoritasBackup: ${listaRefeicoesFavoritasBackup},
listaRefeicoesFavoritas: ${listaRefeicoesFavoritas},
listaHistoricoRespiracao: ${listaHistoricoRespiracao},
dataInicialListaCompras: ${dataInicialListaCompras},
dataFinalListaCompras: ${dataFinalListaCompras},
carboidratosConsumidoNoDia: ${carboidratosConsumidoNoDia},
carboidratosQuantidadeTotalNoDia: ${carboidratosQuantidadeTotalNoDia},
carboidratosConsumidoNoDiaProgresso: ${carboidratosConsumidoNoDiaProgresso},
proteinasConsumidoNoDia: ${proteinasConsumidoNoDia},
proteinasQuantidadeTotalNoDia: ${proteinasQuantidadeTotalNoDia},
proteinasConsumidoNoDiaProgresso: ${proteinasConsumidoNoDiaProgresso},
gordurasConsumidoNoDia: ${gordurasConsumidoNoDia},
gordurasQuantidadeTotalNoDia: ${gordurasQuantidadeTotalNoDia},
gordurasConsumidoNoDiaProgresso: ${gordurasConsumidoNoDiaProgresso},
fibrasConsumidoNoDia: ${fibrasConsumidoNoDia},
fibrasQuantidadeTotalNoDia: ${fibrasQuantidadeTotalNoDia},
fibrasConsumidoNoDiaProgresso: ${fibrasConsumidoNoDiaProgresso},
valorTotalConclusaoPlano: ${valorTotalConclusaoPlano},
pesoMaximo: ${pesoMaximo},
dataPesoMaximo: ${dataPesoMaximo},
pesoMinimo: ${pesoMinimo},
dataPesoMinimo: ${dataPesoMinimo},
quantiadeRefeicoesConsumidasDiaProgresso: ${quantiadeRefeicoesConsumidasDiaProgresso},
listaReceitasFavoritas: ${listaReceitasFavoritas},
listaPlanoNutricionalFitstream: ${listaPlanoNutricionalFitstream},
planoNutricionalSelecionadoFitstream: ${planoNutricionalSelecionadoFitstream},
statusConsultarPlanoNutricionaisFitStream: ${statusConsultarPlanoNutricionaisFitStream}
    ''';
  }
}
