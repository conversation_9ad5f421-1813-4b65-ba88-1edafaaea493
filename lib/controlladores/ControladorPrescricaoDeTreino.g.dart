// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorPrescricaoDeTreino.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorPrescricaoDeTreino
    on _ControladorPrescricaoDeTreinoBase, Store {
  late final _$alunosAtom =
      Atom(name: '_ControladorPrescricaoDeTreinoBase.alunos', context: context);

  @override
  ObservableList<Aluno> get alunos {
    _$alunosAtom.reportRead();
    return super.alunos;
  }

  @override
  set alunos(ObservableList<Aluno> value) {
    _$alunosAtom.reportWrite(value, super.alunos, () {
      super.alunos = value;
    });
  }

  late final _$alunosCarteiraAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.alunosCarteira',
      context: context);

  @override
  ObservableList<Aluno> get alunosCarteira {
    _$alunosCarteiraAtom.reportRead();
    return super.alunosCarteira;
  }

  @override
  set alunosCarteira(ObservableList<Aluno> value) {
    _$alunosCarteiraAtom.reportWrite(value, super.alunosCarteira, () {
      super.alunosCarteira = value;
    });
  }

  late final _$alunosAuxAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.alunosAux', context: context);

  @override
  ObservableList<Aluno> get alunosAux {
    _$alunosAuxAtom.reportRead();
    return super.alunosAux;
  }

  @override
  set alunosAux(ObservableList<Aluno> value) {
    _$alunosAuxAtom.reportWrite(value, super.alunosAux, () {
      super.alunosAux = value;
    });
  }

  late final _$mAlunosAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.mAlunos', context: context);

  @override
  ObservableList<AlunoDeProfessor> get mAlunos {
    _$mAlunosAtom.reportRead();
    return super.mAlunos;
  }

  @override
  set mAlunos(ObservableList<AlunoDeProfessor> value) {
    _$mAlunosAtom.reportWrite(value, super.mAlunos, () {
      super.mAlunos = value;
    });
  }

  late final _$alunosExibirAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.alunosExibir',
      context: context);

  @override
  ObservableList<AlunoDeProfessor> get alunosExibir {
    _$alunosExibirAtom.reportRead();
    return super.alunosExibir;
  }

  @override
  set alunosExibir(ObservableList<AlunoDeProfessor> value) {
    _$alunosExibirAtom.reportWrite(value, super.alunosExibir, () {
      super.alunosExibir = value;
    });
  }

  late final _$mAlunosAddAulaAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.mAlunosAddAula',
      context: context);

  @override
  ObservableList<AlunoDeProfessor> get mAlunosAddAula {
    _$mAlunosAddAulaAtom.reportRead();
    return super.mAlunosAddAula;
  }

  @override
  set mAlunosAddAula(ObservableList<AlunoDeProfessor> value) {
    _$mAlunosAddAulaAtom.reportWrite(value, super.mAlunosAddAula, () {
      super.mAlunosAddAula = value;
    });
  }

  late final _$mAlunosAddAulaOriginalAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.mAlunosAddAulaOriginal',
      context: context);

  @override
  ObservableList<AlunoDeProfessor> get mAlunosAddAulaOriginal {
    _$mAlunosAddAulaOriginalAtom.reportRead();
    return super.mAlunosAddAulaOriginal;
  }

  @override
  set mAlunosAddAulaOriginal(ObservableList<AlunoDeProfessor> value) {
    _$mAlunosAddAulaOriginalAtom
        .reportWrite(value, super.mAlunosAddAulaOriginal, () {
      super.mAlunosAddAulaOriginal = value;
    });
  }

  late final _$alunoVisualizarAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.alunoVisualizar',
      context: context);

  @override
  AlunoDeProfessor? get alunoVisualizar {
    _$alunoVisualizarAtom.reportRead();
    return super.alunoVisualizar;
  }

  @override
  set alunoVisualizar(AlunoDeProfessor? value) {
    _$alunoVisualizarAtom.reportWrite(value, super.alunoVisualizar, () {
      super.alunoVisualizar = value;
    });
  }

  late final _$mAlunosAcompanharAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.mAlunosAcompanhar',
      context: context);

  @override
  ObservableList<Aluno> get mAlunosAcompanhar {
    _$mAlunosAcompanharAtom.reportRead();
    return super.mAlunosAcompanhar;
  }

  @override
  set mAlunosAcompanhar(ObservableList<Aluno> value) {
    _$mAlunosAcompanharAtom.reportWrite(value, super.mAlunosAcompanhar, () {
      super.mAlunosAcompanhar = value;
    });
  }

  late final _$codigoAulaAcompanharAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.codigoAulaAcompanhar',
      context: context);

  @override
  num get codigoAulaAcompanhar {
    _$codigoAulaAcompanharAtom.reportRead();
    return super.codigoAulaAcompanhar;
  }

  @override
  set codigoAulaAcompanhar(num value) {
    _$codigoAulaAcompanharAtom.reportWrite(value, super.codigoAulaAcompanhar,
        () {
      super.codigoAulaAcompanhar = value;
    });
  }

  late final _$mAlunosEmExecucaoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.mAlunosEmExecucao',
      context: context);

  @override
  List<AlunoFichaEmExecucao> get mAlunosEmExecucao {
    _$mAlunosEmExecucaoAtom.reportRead();
    return super.mAlunosEmExecucao;
  }

  @override
  set mAlunosEmExecucao(List<AlunoFichaEmExecucao> value) {
    _$mAlunosEmExecucaoAtom.reportWrite(value, super.mAlunosEmExecucao, () {
      super.mAlunosEmExecucao = value;
    });
  }

  late final _$mProgramaAlunoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.mProgramaAluno',
      context: context);

  @override
  ProgramaDeTreino? get mProgramaAluno {
    _$mProgramaAlunoAtom.reportRead();
    return super.mProgramaAluno;
  }

  @override
  set mProgramaAluno(ProgramaDeTreino? value) {
    _$mProgramaAlunoAtom.reportWrite(value, super.mProgramaAluno, () {
      super.mProgramaAluno = value;
    });
  }

  late final _$eCriarNovoPlanoDeTreinoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.eCriarNovoPlanoDeTreino',
      context: context);

  @override
  bool get eCriarNovoPlanoDeTreino {
    _$eCriarNovoPlanoDeTreinoAtom.reportRead();
    return super.eCriarNovoPlanoDeTreino;
  }

  @override
  set eCriarNovoPlanoDeTreino(bool value) {
    _$eCriarNovoPlanoDeTreinoAtom
        .reportWrite(value, super.eCriarNovoPlanoDeTreino, () {
      super.eCriarNovoPlanoDeTreino = value;
    });
  }

  late final _$pesquisaPorNomeAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.pesquisaPorNome',
      context: context);

  @override
  bool get pesquisaPorNome {
    _$pesquisaPorNomeAtom.reportRead();
    return super.pesquisaPorNome;
  }

  @override
  set pesquisaPorNome(bool value) {
    _$pesquisaPorNomeAtom.reportWrite(value, super.pesquisaPorNome, () {
      super.pesquisaPorNome = value;
    });
  }

  late final _$mHistoricoExecsAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.mHistoricoExecs',
      context: context);

  @override
  ObservableList<HistoricoExecucaoAluno> get mHistoricoExecs {
    _$mHistoricoExecsAtom.reportRead();
    return super.mHistoricoExecs;
  }

  @override
  set mHistoricoExecs(ObservableList<HistoricoExecucaoAluno> value) {
    _$mHistoricoExecsAtom.reportWrite(value, super.mHistoricoExecs, () {
      super.mHistoricoExecs = value;
    });
  }

  late final _$filtroAtualAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.filtroAtual', context: context);

  @override
  int get filtroAtual {
    _$filtroAtualAtom.reportRead();
    return super.filtroAtual;
  }

  @override
  set filtroAtual(int value) {
    _$filtroAtualAtom.reportWrite(value, super.filtroAtual, () {
      super.filtroAtual = value;
    });
  }

  late final _$statusConsultarAlunosAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.statusConsultarAlunos',
      context: context);

  @override
  ServiceStatus get statusConsultarAlunos {
    _$statusConsultarAlunosAtom.reportRead();
    return super.statusConsultarAlunos;
  }

  @override
  set statusConsultarAlunos(ServiceStatus value) {
    _$statusConsultarAlunosAtom.reportWrite(value, super.statusConsultarAlunos,
        () {
      super.statusConsultarAlunos = value;
    });
  }

  late final _$statusConsultaProgramaAlunoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.statusConsultaProgramaAluno',
      context: context);

  @override
  ServiceStatus get statusConsultaProgramaAluno {
    _$statusConsultaProgramaAlunoAtom.reportRead();
    return super.statusConsultaProgramaAluno;
  }

  @override
  set statusConsultaProgramaAluno(ServiceStatus value) {
    _$statusConsultaProgramaAlunoAtom
        .reportWrite(value, super.statusConsultaProgramaAluno, () {
      super.statusConsultaProgramaAluno = value;
    });
  }

  late final _$statusConsultaAcessosAlunoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.statusConsultaAcessosAluno',
      context: context);

  @override
  ServiceStatus get statusConsultaAcessosAluno {
    _$statusConsultaAcessosAlunoAtom.reportRead();
    return super.statusConsultaAcessosAluno;
  }

  @override
  set statusConsultaAcessosAluno(ServiceStatus value) {
    _$statusConsultaAcessosAlunoAtom
        .reportWrite(value, super.statusConsultaAcessosAluno, () {
      super.statusConsultaAcessosAluno = value;
    });
  }

  late final _$eRetryProgramaAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.eRetryPrograma',
      context: context);

  @override
  bool get eRetryPrograma {
    _$eRetryProgramaAtom.reportRead();
    return super.eRetryPrograma;
  }

  @override
  set eRetryPrograma(bool value) {
    _$eRetryProgramaAtom.reportWrite(value, super.eRetryPrograma, () {
      super.eRetryPrograma = value;
    });
  }

  late final _$statusConsultaHistoricoAlunoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.statusConsultaHistoricoAluno',
      context: context);

  @override
  ServiceStatus get statusConsultaHistoricoAluno {
    _$statusConsultaHistoricoAlunoAtom.reportRead();
    return super.statusConsultaHistoricoAluno;
  }

  @override
  set statusConsultaHistoricoAluno(ServiceStatus value) {
    _$statusConsultaHistoricoAlunoAtom
        .reportWrite(value, super.statusConsultaHistoricoAluno, () {
      super.statusConsultaHistoricoAluno = value;
    });
  }

  late final _$statusConsultaTimeLineAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.statusConsultaTimeLine',
      context: context);

  @override
  ServiceStatus get statusConsultaTimeLine {
    _$statusConsultaTimeLineAtom.reportRead();
    return super.statusConsultaTimeLine;
  }

  @override
  set statusConsultaTimeLine(ServiceStatus value) {
    _$statusConsultaTimeLineAtom
        .reportWrite(value, super.statusConsultaTimeLine, () {
      super.statusConsultaTimeLine = value;
    });
  }

  late final _$listEventosTimeLineAlunoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.listEventosTimeLineAluno',
      context: context);

  @override
  List<EventoTimeLineAluno> get listEventosTimeLineAluno {
    _$listEventosTimeLineAlunoAtom.reportRead();
    return super.listEventosTimeLineAluno;
  }

  @override
  set listEventosTimeLineAluno(List<EventoTimeLineAluno> value) {
    _$listEventosTimeLineAlunoAtom
        .reportWrite(value, super.listEventosTimeLineAluno, () {
      super.listEventosTimeLineAluno = value;
    });
  }

  late final _$startDateAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.startDate', context: context);

  @override
  DateTime? get startDate {
    _$startDateAtom.reportRead();
    return super.startDate;
  }

  @override
  set startDate(DateTime? value) {
    _$startDateAtom.reportWrite(value, super.startDate, () {
      super.startDate = value;
    });
  }

  late final _$endDateAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.endDate', context: context);

  @override
  DateTime? get endDate {
    _$endDateAtom.reportRead();
    return super.endDate;
  }

  @override
  set endDate(DateTime? value) {
    _$endDateAtom.reportWrite(value, super.endDate, () {
      super.endDate = value;
    });
  }

  late final _$filtroperidoSelecionadoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.filtroperidoSelecionado',
      context: context);

  @override
  FilterPeriodo? get filtroperidoSelecionado {
    _$filtroperidoSelecionadoAtom.reportRead();
    return super.filtroperidoSelecionado;
  }

  @override
  set filtroperidoSelecionado(FilterPeriodo? value) {
    _$filtroperidoSelecionadoAtom
        .reportWrite(value, super.filtroperidoSelecionado, () {
      super.filtroperidoSelecionado = value;
    });
  }

  late final _$diasExecutadosAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.diasExecutados',
      context: context);

  @override
  ObservableList<String> get diasExecutados {
    _$diasExecutadosAtom.reportRead();
    return super.diasExecutados;
  }

  @override
  set diasExecutados(ObservableList<String> value) {
    _$diasExecutadosAtom.reportWrite(value, super.diasExecutados, () {
      super.diasExecutados = value;
    });
  }

  late final _$diasExecutadosStatusAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.diasExecutadosStatus',
      context: context);

  @override
  ServiceStatus get diasExecutadosStatus {
    _$diasExecutadosStatusAtom.reportRead();
    return super.diasExecutadosStatus;
  }

  @override
  set diasExecutadosStatus(ServiceStatus value) {
    _$diasExecutadosStatusAtom.reportWrite(value, super.diasExecutadosStatus,
        () {
      super.diasExecutadosStatus = value;
    });
  }

  late final _$mlistaEventosAulasAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.mlistaEventosAulas',
      context: context);

  @override
  List<EventoTimeLineAluno> get mlistaEventosAulas {
    _$mlistaEventosAulasAtom.reportRead();
    return super.mlistaEventosAulas;
  }

  @override
  set mlistaEventosAulas(List<EventoTimeLineAluno> value) {
    _$mlistaEventosAulasAtom.reportWrite(value, super.mlistaEventosAulas, () {
      super.mlistaEventosAulas = value;
    });
  }

  late final _$mListaAvaliacaoProgressoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.mListaAvaliacaoProgresso',
      context: context);

  @override
  ObservableList<AvaliacaoProgresso> get mListaAvaliacaoProgresso {
    _$mListaAvaliacaoProgressoAtom.reportRead();
    return super.mListaAvaliacaoProgresso;
  }

  @override
  set mListaAvaliacaoProgresso(ObservableList<AvaliacaoProgresso> value) {
    _$mListaAvaliacaoProgressoAtom
        .reportWrite(value, super.mListaAvaliacaoProgresso, () {
      super.mListaAvaliacaoProgresso = value;
    });
  }

  late final _$isLastPageAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.isLastPage', context: context);

  @override
  bool get isLastPage {
    _$isLastPageAtom.reportRead();
    return super.isLastPage;
  }

  @override
  set isLastPage(bool value) {
    _$isLastPageAtom.reportWrite(value, super.isLastPage, () {
      super.isLastPage = value;
    });
  }

  late final _$alunosNaAcademiaAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.alunosNaAcademia',
      context: context);

  @override
  ObservableList<Aluno> get alunosNaAcademia {
    _$alunosNaAcademiaAtom.reportRead();
    return super.alunosNaAcademia;
  }

  @override
  set alunosNaAcademia(ObservableList<Aluno> value) {
    _$alunosNaAcademiaAtom.reportWrite(value, super.alunosNaAcademia, () {
      super.alunosNaAcademia = value;
    });
  }

  late final _$listaAlunosNaAcademiaAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.listaAlunosNaAcademia',
      context: context);

  @override
  List<ContentAlunoAcademia> get listaAlunosNaAcademia {
    _$listaAlunosNaAcademiaAtom.reportRead();
    return super.listaAlunosNaAcademia;
  }

  @override
  set listaAlunosNaAcademia(List<ContentAlunoAcademia> value) {
    _$listaAlunosNaAcademiaAtom.reportWrite(value, super.listaAlunosNaAcademia,
        () {
      super.listaAlunosNaAcademia = value;
    });
  }

  late final _$codigoDeAcessoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.codigoDeAcesso',
      context: context);

  @override
  String get codigoDeAcesso {
    _$codigoDeAcessoAtom.reportRead();
    return super.codigoDeAcesso;
  }

  @override
  set codigoDeAcesso(String value) {
    _$codigoDeAcessoAtom.reportWrite(value, super.codigoDeAcesso, () {
      super.codigoDeAcesso = value;
    });
  }

  late final _$statusCardContratoAlunoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.statusCardContratoAluno',
      context: context);

  @override
  ServiceStatus get statusCardContratoAluno {
    _$statusCardContratoAlunoAtom.reportRead();
    return super.statusCardContratoAluno;
  }

  @override
  set statusCardContratoAluno(ServiceStatus value) {
    _$statusCardContratoAlunoAtom
        .reportWrite(value, super.statusCardContratoAluno, () {
      super.statusCardContratoAluno = value;
    });
  }

  late final _$statusCardPerfilAlunoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.statusCardPerfilAluno',
      context: context);

  @override
  ServiceStatus get statusCardPerfilAluno {
    _$statusCardPerfilAlunoAtom.reportRead();
    return super.statusCardPerfilAluno;
  }

  @override
  set statusCardPerfilAluno(ServiceStatus value) {
    _$statusCardPerfilAlunoAtom.reportWrite(value, super.statusCardPerfilAluno,
        () {
      super.statusCardPerfilAluno = value;
    });
  }

  late final _$statusCardAvaliacaoFisicaAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.statusCardAvaliacaoFisica',
      context: context);

  @override
  ServiceStatus get statusCardAvaliacaoFisica {
    _$statusCardAvaliacaoFisicaAtom.reportRead();
    return super.statusCardAvaliacaoFisica;
  }

  @override
  set statusCardAvaliacaoFisica(ServiceStatus value) {
    _$statusCardAvaliacaoFisicaAtom
        .reportWrite(value, super.statusCardAvaliacaoFisica, () {
      super.statusCardAvaliacaoFisica = value;
    });
  }

  late final _$statusCardObservacoesAlunoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.statusCardObservacoesAluno',
      context: context);

  @override
  ServiceStatus get statusCardObservacoesAluno {
    _$statusCardObservacoesAlunoAtom.reportRead();
    return super.statusCardObservacoesAluno;
  }

  @override
  set statusCardObservacoesAluno(ServiceStatus value) {
    _$statusCardObservacoesAlunoAtom
        .reportWrite(value, super.statusCardObservacoesAluno, () {
      super.statusCardObservacoesAluno = value;
    });
  }

  late final _$statusCardNiveisAlunoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.statusCardNiveisAluno',
      context: context);

  @override
  ServiceStatus get statusCardNiveisAluno {
    _$statusCardNiveisAlunoAtom.reportRead();
    return super.statusCardNiveisAluno;
  }

  @override
  set statusCardNiveisAluno(ServiceStatus value) {
    _$statusCardNiveisAlunoAtom.reportWrite(value, super.statusCardNiveisAluno,
        () {
      super.statusCardNiveisAluno = value;
    });
  }

  late final _$statusConsultaDetalhesDoAlunoAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.statusConsultaDetalhesDoAluno',
      context: context);

  @override
  ServiceStatus get statusConsultaDetalhesDoAluno {
    _$statusConsultaDetalhesDoAlunoAtom.reportRead();
    return super.statusConsultaDetalhesDoAluno;
  }

  @override
  set statusConsultaDetalhesDoAluno(ServiceStatus value) {
    _$statusConsultaDetalhesDoAlunoAtom
        .reportWrite(value, super.statusConsultaDetalhesDoAluno, () {
      super.statusConsultaDetalhesDoAluno = value;
    });
  }

  late final _$alunosFichaAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.alunosFicha', context: context);

  @override
  AlunoFichaEmExecucao get alunosFicha {
    _$alunosFichaAtom.reportRead();
    return super.alunosFicha;
  }

  @override
  set alunosFicha(AlunoFichaEmExecucao value) {
    _$alunosFichaAtom.reportWrite(value, super.alunosFicha, () {
      super.alunosFicha = value;
    });
  }

  late final _$mStatusProfessorWebAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.mStatusProfessorWeb',
      context: context);

  @override
  ServiceStatus get mStatusProfessorWeb {
    _$mStatusProfessorWebAtom.reportRead();
    return super.mStatusProfessorWeb;
  }

  @override
  set mStatusProfessorWeb(ServiceStatus value) {
    _$mStatusProfessorWebAtom.reportWrite(value, super.mStatusProfessorWeb, () {
      super.mStatusProfessorWeb = value;
    });
  }

  late final _$professorTreinoWebAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.professorTreinoWeb',
      context: context);

  @override
  List<ProfTreinoWeb> get professorTreinoWeb {
    _$professorTreinoWebAtom.reportRead();
    return super.professorTreinoWeb;
  }

  @override
  set professorTreinoWeb(List<ProfTreinoWeb> value) {
    _$professorTreinoWebAtom.reportWrite(value, super.professorTreinoWeb, () {
      super.professorTreinoWeb = value;
    });
  }

  late final _$mStatusCadastroProfessorWebAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.mStatusCadastroProfessorWeb',
      context: context);

  @override
  ServiceStatus get mStatusCadastroProfessorWeb {
    _$mStatusCadastroProfessorWebAtom.reportRead();
    return super.mStatusCadastroProfessorWeb;
  }

  @override
  set mStatusCadastroProfessorWeb(ServiceStatus value) {
    _$mStatusCadastroProfessorWebAtom
        .reportWrite(value, super.mStatusCadastroProfessorWeb, () {
      super.mStatusCadastroProfessorWeb = value;
    });
  }

  late final _$cadastroProfessorTreinoWebAtom = Atom(
      name: '_ControladorPrescricaoDeTreinoBase.cadastroProfessorTreinoWeb',
      context: context);

  @override
  List<CadastroProfessorTreinoWeb> get cadastroProfessorTreinoWeb {
    _$cadastroProfessorTreinoWebAtom.reportRead();
    return super.cadastroProfessorTreinoWeb;
  }

  @override
  set cadastroProfessorTreinoWeb(List<CadastroProfessorTreinoWeb> value) {
    _$cadastroProfessorTreinoWebAtom
        .reportWrite(value, super.cadastroProfessorTreinoWeb, () {
      super.cadastroProfessorTreinoWeb = value;
    });
  }

  @override
  String toString() {
    return '''
alunos: ${alunos},
alunosCarteira: ${alunosCarteira},
alunosAux: ${alunosAux},
mAlunos: ${mAlunos},
alunosExibir: ${alunosExibir},
mAlunosAddAula: ${mAlunosAddAula},
mAlunosAddAulaOriginal: ${mAlunosAddAulaOriginal},
alunoVisualizar: ${alunoVisualizar},
mAlunosAcompanhar: ${mAlunosAcompanhar},
codigoAulaAcompanhar: ${codigoAulaAcompanhar},
mAlunosEmExecucao: ${mAlunosEmExecucao},
mProgramaAluno: ${mProgramaAluno},
eCriarNovoPlanoDeTreino: ${eCriarNovoPlanoDeTreino},
pesquisaPorNome: ${pesquisaPorNome},
mHistoricoExecs: ${mHistoricoExecs},
filtroAtual: ${filtroAtual},
statusConsultarAlunos: ${statusConsultarAlunos},
statusConsultaProgramaAluno: ${statusConsultaProgramaAluno},
statusConsultaAcessosAluno: ${statusConsultaAcessosAluno},
eRetryPrograma: ${eRetryPrograma},
statusConsultaHistoricoAluno: ${statusConsultaHistoricoAluno},
statusConsultaTimeLine: ${statusConsultaTimeLine},
listEventosTimeLineAluno: ${listEventosTimeLineAluno},
startDate: ${startDate},
endDate: ${endDate},
filtroperidoSelecionado: ${filtroperidoSelecionado},
diasExecutados: ${diasExecutados},
diasExecutadosStatus: ${diasExecutadosStatus},
mlistaEventosAulas: ${mlistaEventosAulas},
mListaAvaliacaoProgresso: ${mListaAvaliacaoProgresso},
isLastPage: ${isLastPage},
alunosNaAcademia: ${alunosNaAcademia},
listaAlunosNaAcademia: ${listaAlunosNaAcademia},
codigoDeAcesso: ${codigoDeAcesso},
statusCardContratoAluno: ${statusCardContratoAluno},
statusCardPerfilAluno: ${statusCardPerfilAluno},
statusCardAvaliacaoFisica: ${statusCardAvaliacaoFisica},
statusCardObservacoesAluno: ${statusCardObservacoesAluno},
statusCardNiveisAluno: ${statusCardNiveisAluno},
statusConsultaDetalhesDoAluno: ${statusConsultaDetalhesDoAluno},
alunosFicha: ${alunosFicha},
mStatusProfessorWeb: ${mStatusProfessorWeb},
professorTreinoWeb: ${professorTreinoWeb},
mStatusCadastroProfessorWeb: ${mStatusCadastroProfessorWeb},
cadastroProfessorTreinoWeb: ${cadastroProfessorTreinoWeb}
    ''';
  }
}
