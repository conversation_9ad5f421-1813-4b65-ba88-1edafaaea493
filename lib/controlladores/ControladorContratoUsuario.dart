// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:typed_data';
import 'package:app_treino/ServiceProvider/ContratoUsuarioService.dart';
import 'package:app_treino/ServiceProvider/authServices/ServiceAuth.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorAulaTurma.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/bodyscrypto.dart';
import 'package:app_treino/model/contrato/ContratoOperacao.dart';
import 'package:app_treino/model/contrato/ContratoUsuario.dart';
import 'package:app_treino/model/contrato/ExtratoCreditoTurma.dart';
import 'package:app_treino/model/contrato/FiltroParcelas.dart';
import 'package:app_treino/model/contrato/JustificativaContrato.dart';
import 'package:app_treino/model/contrato/OperacaoValidacao.dart';
import 'package:app_treino/model/contrato/ParcelaContrato.dart';
import 'package:app_treino/model/contrato/CarenciaOuTrancamento.dart';
import 'package:app_treino/model/contrato/ProdutoContrato.dart';
import 'package:app_treino/model/contrato/TransferenciaCredito.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/doClienteApp/DadosDoUsuario.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/SituacaoCliente.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:app_treino/controlladores/ControladorDBExtend.dart';
import 'package:app_treino/model/TermosDoContrato.dart';
import 'package:app_treino/screens/editarColaborador/TelaCreditosContrato.dart';
import 'package:collection/collection.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:signature/signature.dart';
part 'ControladorContratoUsuario.g.dart';

class ControladorContratoUsuario = _ControladorContratoUsuarioBase with _$ControladorContratoUsuario;

abstract class _ControladorContratoUsuarioBase extends UtilDataBase with Store {
  @observable
  ServiceStatus mStatusConsultaContratos = ServiceStatus.Waiting;

  @observable
  ServiceStatus mStatusSimulacaoRenovacao = ServiceStatus.Waiting;

  @observable
  ServiceStatus mStatusConsultaTrancamento = ServiceStatus.Waiting;

  @observable
  ServiceStatus mStatusValidacaoContrato = ServiceStatus.Waiting;

  @observable
  ServiceStatus mStatusSimulacaoCarencia = ServiceStatus.Waiting;

  @observable
  ServiceStatus mStatusConsultaHistoricoCredito = ServiceStatus.Waiting;

  @observable
  bool isPlanoSelecionado = false;
  @observable
  bool isMeuscontratoSelecionado = true;

  String frequenciaUsuarioLogado = '';

  List<ExtratoCreditoTurma> mCreditosHistorico = [];
  String _consultarContratoAluno = '';

  final _mServiceContrato = GetIt.I.get<ContratoUsuarioService>();
  final _mCliente = GetIt.I.get<ControladorCliente>();
  final _mApp = GetIt.I.get<ControladorApp>();

  List<ContratoUsuario?> mListaContratos = [];
  List<ParcelaContrato> mTodasParcelas = [];
  List<ContratoUsuario?> listaContratosDropDown = [];

  EnumFiltrarParcelas filtroSelecionadoParcelas = EnumFiltrarParcelas.TODOS;

  @observable
  ContratoUsuario? mContratoSelecionado;

  @observable
  ObservableList<ContratoAssinatura> contratoAssinatura = ObservableList<ContratoAssinatura>();

  void resetParaContratoAtual() {
    mContratoSelecionado = null;
  }

  void setContratoVisualizar(ContratoUsuario contrato) {
    mStatusConsultaContratos = ServiceStatus.Waiting;
    carregarValidacaoDeTrancamentoDeContrato(
      mContrato: contrato,
      sucesso: (a, b, c) {
        mContratoSelecionado = contrato;
        mStatusConsultaContratos = ServiceStatus.Done;
      },
      falha: (falha) {
        mStatusConsultaContratos = ServiceStatus.Error;
      },
    );
    Future.delayed(const Duration(milliseconds: 200)).then((value) {});
  }

  Future<ContratoAssinatura> consultarTermosDeContratoPorContrato(codigoEmpresa) async {
    try {
      String contratoString = await GetIt.I
          .get<ContratoUsuarioService>()
          .consultarContratoAlunoPorContrato(BodyConsultarContratoPorCodigoCrypto(contrato: contratoVisualizar!.codigo!, empresa: _mCliente.mUsuarioLogado!.codEmpresa!));
      ContratoAssinatura contratoAtualizado = ContratoAssinatura.fromJson(jsonDecode(contratoString)[0]);
      TermosContratoAssinadoFirebase termosContratoArmazenar = TermosContratoAssinadoFirebase(codigoContrato: contratoVisualizar!.codigo!, contratoAssinado: contratoAtualizado);
      await inserirNoBanco('termosContratoAssinado', termosContratoArmazenar.toJson());
      return contratoAtualizado;
    } catch (e) {
      var termosContratoEmBanco = await buscarNoBanco('termosContratoAssinado');
      if (termosContratoEmBanco != null && termosContratoEmBanco['codigoContrato'] == contratoVisualizar!.codigo!) {
        return TermosContratoAssinadoFirebase.fromJson(termosContratoEmBanco).contratoAssinado;
      }
      throw e;
    }
  }

  Future<void> limparCacheContrato(num contratoID) async {
    try {
      await deletarDoBanco('termosContratoAssinado');
    } catch (e) {
      print('Erro ao limpar cache do contrato: $e');
    }
  }

  Future<void> removerContratoAssinadoDoBanco({Function()? carregando, Function()? sucesso, Function(String? falha)? falha}) async {
    return deletarDoBanco('termosContratoAssinado').then((value) => sucesso?.call()).catchError((e) => falha?.call(e.toString()));
  }

  bool get contratoVisualizarEstaDeFerias {
    var validaOperacao = false;
    if (operacaoesContrato.isNotEmpty)
      validaOperacao = UtilDataHora.parseStringToDate(operacaoesContrato.first.dataInicio!)!.isBefore(DateTime.now()) &&
          UtilDataHora.parseStringToDate(operacaoesContrato.first.dataFim!)!.isAfter(DateTime.now()) &&
          operacaoesContrato.first.tipoOperacaoApresentar!.contains('Férias');
    return validaOperacao || (contratoVisualizar?.situacaoSubordinada ?? '').toLowerCase().contains('cr');
  }

  bool get contratoVisualizarEstaTrancado {
    var validaOperacao = false;
    if (operacaoesContrato.isNotEmpty)
      validaOperacao = UtilDataHora.parseStringToDate(operacaoesContrato.first.dataFim!)!.isAfter(DateTime.now()) && operacaoesContrato.first.tipoOperacao!.contains('TR');
    return (contratoVisualizar!.situacao ?? '').toLowerCase().contains('trancado') || validaOperacao;
  }

  bool get contratoTrancadoPodeVoltar {
    return !(contratoVisualizar!.situacaoSubordinada ?? '').toLowerCase().contains('tv');
  }

  @observable
  List<ContratoOperacao> operacaoesContrato = [];

  String get contratoVisualizarModalidades {
    var retorno = '';
    contratoVisualizar?.modalidades?.forEach((modalidade) {
      var virgula = contratoVisualizar!.modalidades!.last.codigo != modalidade.codigo;
      if (_mApp.getConfigWebTreino('NAO_EXIBIR_NUMERO_DE_VEZES_NO_APP')) {
        if (modalidade.nrVezesSemana != null) {
          retorno += "${modalidade.modalidade}${virgula ? ', ' : ''}";
        } else {
          retorno += modalidade.modalidade! + "${modalidade.codigo}${virgula ? ', ' : ''}";
        }
      } else if (modalidade.nrVezesSemana != null) {
        retorno += "${modalidade.modalidade} ${modalidade.nrVezesSemana}X por semana${virgula ? ', ' : ''}";
      } else {
        retorno += modalidade.modalidade! + "${modalidade.codigo}${virgula ? ', ' : ''}";
      }
    });

    return UtilitarioApp.sentenseCase(retorno);
  }

  ContratoUsuario? get contratoVisualizar {
    if (mContratoSelecionado == null) {
      return mListaContratos.isNotEmpty
          ? isContratoConcomitantes
              ? mListaContratos.firstWhere(
                  (element) => element!.situacao!.contains('Ativo') && (element.vendaCreditoTreino ?? false),
                  orElse: () {
                    return mListaContratos.firstWhere((element) => element!.situacao!.contains('Ativo'), orElse: () => mListaContratos.first);
                  },
                )
              : mListaContratos.first
          : null;
    } else {
      return mContratoSelecionado;
    }
  }

  bool mostrarBotaoAcaoRapida(ModuloApp modulo) {
    List<ModuloApp> modulosPermitidos = [];
    contratoVisualizar!.vendaCreditoTreino = true;
    if (!_mApp.getConfiguracaoApp(modulo).habilitado!) {
      return false;
    } else if (contratoVisualizar!.situacao!.contains('Ativo')) {
      if (contratoVisualizar!.permiteRenovar! && !contratoVisualizar!.bolsa! && contratoVisualizar!.vendaCreditoTreino!) {
        modulosPermitidos.addAll([ModuloApp.MODULO_CONTRATO_FERIAS, ModuloApp.MODULO_TRANCAMENTO_DE_CONTRATO, ModuloApp.MODULO_RENOVACAO_DE_CONTRATO, ModuloApp.MODULO_CREDITO_DE_CONTRATO]);
      }
      if (contratoVisualizar!.permiteRenovar! && !contratoVisualizar!.bolsa! && !contratoVisualizar!.vendaCreditoTreino!) {
        modulosPermitidos.addAll([ModuloApp.MODULO_CONTRATO_FERIAS, ModuloApp.MODULO_TRANCAMENTO_DE_CONTRATO, ModuloApp.MODULO_RENOVACAO_DE_CONTRATO]);
      } else {
        modulosPermitidos.addAll([ModuloApp.MODULO_CONTRATO_FERIAS, ModuloApp.MODULO_TRANCAMENTO_DE_CONTRATO]);
      }
    } else if (contratoVisualizar!.situacaoSubordinada!.contains('VE') && contratoVisualizar!.permiteRenovar! && !contratoVisualizar!.bolsa!) {
      modulosPermitidos.add(ModuloApp.MODULO_RENOVACAO_DE_CONTRATO);
    }
    if (contratoVisualizarEstaTrancado || contratoVisualizarEstaDeFerias) return false;
    return _mApp.getConfiguracaoApp(modulo).habilitado! && modulosPermitidos.any((m) => m == modulo);
  }

  num duracaoContratoVisualizarSimulacao(ContratoUsuario contratoVisualizar) {
    var dataInicio = UtilDataHora.parseStringToDate(contratoVisualizar.dataLancamento!)!;
    var dataFim = UtilDataHora.parseStringToDate(contratoVisualizar.vigenciaAteAjustada!)!;
    var diasTotal = dataFim.difference(dataInicio).inDays;
    return diasTotal;
  }

  num get duracaoContratoVisualizar {
    var dataInicio = UtilDataHora.parseStringToDate(contratoVisualizar!.dataLancamento!)!;
    var dataFim = UtilDataHora.parseStringToDate(contratoVisualizar!.vigenciaAteAjustada!)!;
    var diasTotal = dataFim.difference(dataInicio).inDays;
    return diasTotal;
  }

  num duracaoMesesContrato(ContratoUsuario contrato) {
    var dataInicio = UtilDataHora.parseStringToDate(contrato.dataLancamento!)!;
    var dataFim = UtilDataHora.parseStringToDate(contrato.vigenciaAteAjustada!)!;
    var diasTotal = dataFim.difference(dataInicio).inDays ~/ 30;
    return diasTotal;
  }

  List<ParcelaContrato> get parcelasContratoVisualizar {
    return mTodasParcelas.where((parcela) => parcela.contrato == contratoVisualizar!.codigo).toList();
  }

  num get numeroTotalParcelaDoContrato {
    List<ParcelaContrato> parcelas = [];
    parcelas.addAll(parcelasContratoVisualizar);
    parcelas.removeWhere((element) =>
        (element.descricao ?? '').toLowerCase().contains('produtos') ||
        (element.descricao ?? '').toLowerCase().contains('anuidade') ||
        (element.descricao ?? '').toLowerCase().contains('adesão') ||
        (element.descricao ?? '').toLowerCase().contains('rematrícula') ||
        (element.descricao ?? '').toLowerCase().contains('matricula') ||
        (element.descricao ?? '').toLowerCase().contains('renovação') ||
        (element.descricao ?? '').isEmpty);
    return parcelas.length;
  }

  ParcelaContrato? get parcelaAtualContrato {
    var mParcelaAtual;
    for (var i = 0; i < parcelasContratoVisualizar.length; i++) {
      if (parcelasContratoVisualizar[i].situacao == 'Em Aberto') {
        mParcelaAtual = parcelasContratoVisualizar[i];
        break;
      }
    }
    /* for (final parcela in mTodasParcelas) {
      if(UtilDataHora.getMesAno(dateString: parcela.dataVencimento) == UtilDataHora.getMesAno(dateTime: DateTime.now())){
        mParcelaAtual = parcela;
      }
    } */
    return mParcelaAtual;
  }

  num get numeroParcelaAtual {
    var index;
    for (var i = 0; i < parcelasContratoVisualizar.length; i++) {
      if (UtilDataHora.getMesAno(dateString: parcelasContratoVisualizar[i].dataVencimento) == UtilDataHora.getMesAno(dateTime: DateTime.now())) {
        index = i + 1;
        break;
      }
    }
    return index;
  }

  List<ParcelaContrato> get parcelasAtrasadas {
    return mTodasParcelas.where((parcela) => UtilDataHora.dataMenorQueDeAgora(dataString: parcela.dataVencimento) && parcela.situacao == 'Em Aberto').toList();
  }

  Color? corDaSituacaoParcela(ParcelaContrato parcela) {
    switch (parcela.situacao) {
      case 'Pago':
        return Colors.green[400];
      case 'Em Aberto':
        return Colors.yellow[500];
      case 'Em Remessa':
        return Colors.blueGrey[500];
      case 'Cancelado':
        return Colors.yellow[500];
      default:
        return Colors.grey;
    }
  }

  double get porcentagemVigenciaCumpridaDoContrato {
    var dataInicio = UtilDataHora.parseStringToDate(contratoVisualizar!.vigenciaDe!)!;
    var dataFim = UtilDataHora.parseStringToDate(contratoVisualizar!.vigenciaAteAjustada!)!;
    var diasTotal = dataFim.difference(dataInicio).inDays;
    var diasPassouDoInicioAteHoje = DateTime.now().difference(dataInicio).inDays;
    diasPassouDoInicioAteHoje = diasPassouDoInicioAteHoje > 0 ? diasPassouDoInicioAteHoje : 0;
    return UtilitarioApp.percentEntre0e100(valorA: diasPassouDoInicioAteHoje, valorB: diasTotal) as double;
  }

  List<ContratoAssinatura> get obterAditivosDoContratoVisualizar {
    final controladorContratoUsuario = GetIt.I.get<ControladorContratoUsuario>();
    try {
      return controladorContratoUsuario.contratoAssinatura.where((element) => element.contrato == contratoVisualizar!.codigo && element.ehAditivo!).toList();
    } catch (e) {
      return [];
    }
  }

  void podeAddDropDown(ContratoUsuario? contrato) {
    try {
      ContratoAssinatura? c = contratoAssinatura.firstWhereOrNull((element) => element.contrato == contrato!.codigo);
      if (c != null && !(c.ehAditivo ?? false)) {
        listaContratosDropDown.add(contrato);
      }
    } catch (e) {
      listaContratosDropDown.add(contrato);
    }
  }

  void setarContratosDropDown() {
    listaContratosDropDown.clear();
    if (mListaContratos.length == 1) {
      podeAddDropDown(contratoVisualizar);
    } else {
      for (final element in mListaContratos) {
        if ((element?.situacao ?? '').contains('Ativo') ||
            ((element?.situacaoSubordinada ?? '') == 'VE' && (element?.situacao ?? '') == 'Inativo') ||
            (element?.situacao ?? '').contains('Trancado') ||
            (element?.situacaoSubordinada ?? '').contains('AV')) {
          podeAddDropDown(element);
        }
      }

      // if(contratoVisualizar!.situacaoSubordinada == 'VE' && contratoVisualizar!.situacao == 'Inativo'){
      //   listaContratosDropDown.add(contratoVisualizar);
      // }
      // if(contratoVisualizar!.situacao == 'Ativo') {
      //   listaContratosDropDown = mListaContratos.where((contrato) => contrato!.situacao == 'Ativo').toList();
      // }
      // if(contratoVisualizar!.situacao == 'Trancado'){
      //   listaContratosDropDown.add(contratoVisualizar);
      // }
      // if(contratoVisualizar!.situacaoSubordinada == 'CR'){
      //   listaContratosDropDown.add(contratoVisualizar);
      // }
      // if(contratoVisualizar!.situacaoSubordinada == 'DE'){
      //  listaContratosDropDown.add(contratoVisualizar);
      // }
      // if(contratoVisualizar!.situacaoSubordinada == 'CA'){
      //  listaContratosDropDown.add(contratoVisualizar);
      // }
    }
  }

  void setContratoSelecionado(ContratoUsuario contrato) {
    mContratoSelecionado = contrato;
    _mServiceContrato.obterContratoOperacao(contratoVisualizar!.codigo!).then((operacaoesContratoRetorno) {
      this.operacaoesContrato = operacaoesContratoRetorno;
      operacaoesContrato.sort((b, a) => a.codigo!.compareTo(b.codigo!));
    }).catchError((onError) {
      print('Erro ao obter operações do contrato: $onError');
      // Considere definir um estado de erro ou notificar o usuário
    });
  }

  void consultarOsContratosDoUsuario({Function()? carregando, Function()? sucesso, Function(String? falha)? falha, bool ignorar = false}) {
    if (_mCliente.isUsuarioColaborador) {
      carregando?.call();
      falha?.call('Colaborador não precisa de consulta de contrato');
      return;
    }
    if (_mCliente.mUsuarioLogado?.codigoCliente == null) {
      carregando?.call();
      mStatusConsultaContratos = ServiceStatus.Done;
      falha?.call('O cliente não tem código para consulta');
      return;
    }
    carregando?.call();
    mStatusConsultaContratos = ServiceStatus.Waiting;
    _mServiceContrato.consultarContratos(_mCliente.mUsuarioLogado!.codigoCliente, 6).then((value) async {
      mListaContratos.clear();
      mListaContratos.addAll(value);
      mListaContratos.sort((b, a) => UtilDataHora.parseStringToDate(a!.vigenciaAteAjustada!)!.compareTo(UtilDataHora.parseStringToDate(b!.vigenciaAteAjustada!)!));
      if (mListaContratos.isNotEmpty) {
        consultarContratosAluno(
            retornarTodos: true,
            carregando: () {},
            falha: (_) {},
            sucessoLista: (listaCompleta) {
              var contratosNaoAssinados = listaCompleta.where((c) => (c.ehAditivo ?? false) == false && (c.assinado ?? true) == false && (c.situacaocontrato ?? 'AT') == 'AT').toList();
              if (contratosNaoAssinados.isEmpty) {
                var aditivosNaoAssinados = listaCompleta.where((c) => (c.ehAditivo ?? false) == true && (c.assinado ?? true) == false && (c.situacaocontrato ?? 'AT') == 'AT').toList();
                if (aditivosNaoAssinados.isNotEmpty) {
                  print('Foram encontrados ${aditivosNaoAssinados.length} aditivos não assinados');
                }
              }
            });

        return await _mServiceContrato.consultarParcelas(_mCliente.mUsuarioLogado!.codigoCliente, 200, false);
      } else {
        mStatusConsultaContratos = ServiceStatus.Empty;
        await consultarContratosAluno();
      }
    }).then((parcelas) async {
      mTodasParcelas.clear();
      if (parcelas != null) {
        mTodasParcelas.addAll(parcelas);
      }
      if (mListaContratos.isNotEmpty) return await _mServiceContrato.obterContratoOperacao(contratoVisualizar!.codigo!);
    }).then((operacaoesContratoRetorno) {
      if (mListaContratos.isNotEmpty) {
        this.operacaoesContrato = operacaoesContratoRetorno!;
        operacaoesContrato.sort((b, a) => a.codigo!.compareTo(b.codigo!));
        if (!ignorar) {
          _mCliente.validarSituacaoAluno(
            sucesso: () {
              mStatusConsultaContratos = mListaContratos.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
              sucesso?.call();
            },
          );
        } else {
          sucesso?.call();
        }
      } else {
        if (!ignorar) {
          _mCliente.validarSituacaoAluno(
            sucesso: () {
              mStatusConsultaContratos = mListaContratos.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
              sucesso?.call();
            },
          );
        } else {
          sucesso?.call();
        }
      }
    }).catchError((onError) {
      mStatusConsultaContratos = ServiceStatus.Error;
      falha?.call(onError.message);
    });
  }

  void consultarDadosDeCarenciaDoContrato({Function()? carregando, Function(CarenciaOuTrancamento carencia)? sucesso, Function(String? falha)? falha, bool simulacao = true}) {
    carregando?.call();
    mStatusSimulacaoCarencia = ServiceStatus.Waiting;
    _mServiceContrato.consultarCarencia(contratoVisualizar!.codigo!).then((value) {
      sucesso?.call(value);
      mStatusSimulacaoCarencia = ServiceStatus.Done;
    }).catchError((onError) {
      mStatusSimulacaoCarencia = ServiceStatus.Error;
      falha?.call(onError.message);
    });
  }

  void confirmarTrancamentoDeContrato(BuildContext context, CarenciaOuTrancamento trancamento, ProdutoContrato produtoContrato, JustificativaContrato justificativaContrato,
      {Function()? carregando, Function()? sucesso, Function(String? falha)? falha, required OperacaoValidacao simulacaoTrancamento}) {
    carregando?.call();
    if (_mApp.getConfiguracaoApp(ModuloApp.ALTERAR_VENCIMENTO_PARCELAS).habilitado ?? false) {
      var dataInicioTrancamento = UtilDataHora.getDiaMesAno(dateTime: DateTime.now().add(const Duration(days: -1)));
      var dadosAlteracaoVencimentoParcela = DadosAlteracaoVencimentoParcela(
        codigoContrato: contratoVisualizar!.codigo!,
        numeroDias: simulacaoTrancamento.diasCongelados ?? 0,
        observacao: 'Trancamento de contrato ${trancamento.tipoOperacao!} - ${justificativaContrato.descricao!} Via App ${F.nomeApp} com alteração de vencimeto de parcelas',
        alterarVencimentoParcelas: true,
        dataTrancamentoRetroativo: dataInicioTrancamento,
      );
      _mServiceContrato
          .gravarAlteracaoVencimentoParcela(dadosAlteracaoVencimentoParcela: dadosAlteracaoVencimentoParcela, empresa: _mCliente.mUsuarioLogado!.codEmpresa!, chave: _mApp.chave!)
          .then((value) {
        DSalerta().exibirToast(
          context,
          localizedString('alteracao_vencimento_parcela_aguarde'),
        );
        consultarOsContratosDoUsuario(sucesso: sucesso, falha: falha);
      }).catchError((onError) {
        falha?.call(onError.message);
        mStatusValidacaoContrato = ServiceStatus.Error;
      });
    } else {
      var dataInicioTrancamento = UtilDataHora.getDaTaMesDiaAno(dateTime: DateTime.now().add(const Duration(days: 1)));
      _mServiceContrato
          .gravarDadosOperacaoContrato(
              contratoVisualizar!.codigo!, trancamento.tipoOperacao!, dataInicioTrancamento, dataInicioTrancamento, produtoContrato.codigo!, justificativaContrato.codigo!, '', 4)
          .then((value) {
        consultarOsContratosDoUsuario(sucesso: sucesso, falha: falha);
      }).catchError((onError) {
        falha?.call(onError.message);
      });
    }
  }

  void validarTrancamentoDeContrato(CarenciaOuTrancamento trancamento, ProdutoContrato produtoContrato, JustificativaContrato justificativaContrato,
      {Function()? carregando, Function(OperacaoValidacao contratoSimulado)? sucesso, Function(String? falha)? falha}) {
    carregando?.call();
    mStatusValidacaoContrato = ServiceStatus.Waiting;
    var dataInicioTrancamento = UtilDataHora.getDaTaMesDiaAno(dateTime: DateTime.now().add(const Duration(days: 1)));
    _mServiceContrato
        .validarDadosOperacaoContrato(
            contratoVisualizar!.codigo!, trancamento.tipoOperacao!, dataInicioTrancamento, dataInicioTrancamento, produtoContrato.codigo!, justificativaContrato.codigo!)
        .then((value) {
      sucesso?.call(value);
      mStatusValidacaoContrato = ServiceStatus.Done;
    }).catchError((onError) {
      falha?.call(onError.message);
      mStatusValidacaoContrato = ServiceStatus.Error;
    });
  }

  void renovarContrato({Function()? carregando, Function(ContratoUsuario? contratoSimulado)? sucesso, Function(String? falha)? falha, bool simulacao = true}) {
    carregando?.call();
    if (contratoVisualizar == null) {
      mListaContratos.add(_mCliente.contratoUsuarioVencido == null ? ContratoUsuario(codigo: _mCliente.mLogadoSituacao!.codigoContrato) : _mCliente.contratoUsuarioVencido);
    }
    mStatusSimulacaoRenovacao = ServiceStatus.Waiting;
    _mServiceContrato.renovarContrato(contratoVisualizar!.codigo!, simulacao, _mCliente.mUsuarioLogado!.codigoCliente, 4).then((value) {
      if (simulacao) {
        sucesso?.call(value);
        mStatusSimulacaoRenovacao = ServiceStatus.Done;
      } else {
        consultarOsContratosDoUsuario(sucesso: () => sucesso?.call(null), falha: falha);
      }
    }).catchError((onError) {
      falha?.call(onError.message);
      if (simulacao) mStatusSimulacaoRenovacao = ServiceStatus.Error;
    });
  }

  void carregarValidacaoDeTrancamentoDeContrato(
      {Function()? carregando,
      Function(CarenciaOuTrancamento? trancamento, List<ContratoOperacao>? operacaoContrato, String diasBonus)? sucesso,
      Function(String? falha)? falha,
      ContratoUsuario? mContrato}) {
    carregando?.call();
    CarenciaOuTrancamento? trancamento;
    List<ContratoOperacao>? operacoesContrato;
    mStatusConsultaTrancamento = ServiceStatus.Waiting;
    _mServiceContrato.consultarTrancamento((mContrato ?? contratoVisualizar)!.codigo!).then((tc) {
      trancamento = tc;
      return _mServiceContrato.obterContratoOperacao(contratoVisualizar!.codigo!);
    }).then((ops) {
      operacoesContrato = ops;
      return _mServiceContrato.obterDiasBonusContrato(contratoVisualizar!.codigo!);
    }).then((diasBonus) {
      sucesso?.call(trancamento, operacoesContrato, diasBonus);
      mStatusConsultaTrancamento = ServiceStatus.Done;
    }).catchError((onError) {
      mStatusConsultaTrancamento = ServiceStatus.Error;
      falha?.call(onError.message);
    });
  }

  void solicitarFeriasContrato(CarenciaOuTrancamento carencia,
      {Function()? carregando, Function()? sucesso, Function(String? falha)? falha, DateTime? dataInicio, DateTime? dataFim, JustificativaContrato? justificativaContrato}) {
    carregando?.call();
    // Valida se periodo é valido
    if (carencia.qtdRestanteContrato == 0) {
      falha?.call('Você não possui dias restantes para continuar');
    } else if (carencia.qtdCarenciaPermitida == 0) {
      falha?.call('Não será possível realizar as férias para este contrato, pois ele não permite dias para férias.');
    } else if (justificativaContrato == null) {
      falha?.call('Selecione uma justificativa para continuar');
    } else if (dataInicio == null || dataFim == null) {
      falha?.call('A data início e data fim devem ser informadas');
    } else if (dataInicio.isAfter(dataFim)) {
      falha?.call(localizedString('a_data_inicio_nao_pode_ser_depois'));
    } else if (dataFim.isBefore(dataInicio)) {
      falha?.call('A data de fim não pode ser antes da data de início');
    } else if (dataFim.difference(dataInicio).inDays > carencia.qtdRestanteContrato!) {
      falha?.call('O periodo informado é maior que o dias restantes');
    } else if ((dataFim.difference(dataInicio).inDays + 1) < carencia.qtdMinimaCarencia!) {
      falha?.call('O período informado não corresponde ao mínimo de ${carencia.qtdMinimaCarencia} dias');
    } else if (!(GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_LANCAR_FERIAS_RETRO).habilitado!) &&
        DateTime(dataInicio.year, dataInicio.month, dataInicio.day, 23, 59).isBefore(DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, 00, 00))) {
      falha?.call(localizedString('sua_academia_nao_permite_ferias_retroativas'));
    } else {
      _mServiceContrato
          .gravarDadosOperacaoContrato(contratoVisualizar!.codigo!, carencia.tipoOperacao!, UtilDataHora.getDiaMesAno(dateTime: dataInicio), UtilDataHora.getDiaMesAno(dateTime: dataFim), 0,
              justificativaContrato.codigo!, '', 4)
          .then((value) {
        consultarOsContratosDoUsuario(
          sucesso: sucesso,
          falha: (x) {
            consultarOsContratosDoUsuario(sucesso: sucesso, falha: falha);
          },
        );
      }).catchError((onError) {
        falha?.call(onError.message);
      });
    }
  }

  List<ExtratoCreditoTurma> obterExtratosPorFiltro(FiltrosCredito? filtro) {
    return mCreditosHistorico.where((h) {
      switch (filtro!.id) {
        case 0:
          return true;
        case 1:
          return DateTime.now().difference(h.dataEmDateTime!).inDays <= 30;
        case 2:
          return DateTime.now().difference(h.dataEmDateTime!).inDays >= 30;
        case 3:
          return true;
        default:
          return true;
      }
    }).toList();
  }

  void retornarContratoDeTrancamento({Function()? carregando, Function()? sucesso, Function(String falha)? falha}) {
    carregando?.call();
    _mServiceContrato
        .retornoTrancamentoFerias(contratoVisualizar!.codigo!, 4)
        .then((value) => {sucesso?.call()})
        .catchError((onError) => falha?.call(onError.message.toString().replaceAll('compareãa', 'compareça')));
  }

  void consultarHistoricoCreditos({Function()? carregando, Function(num saldoAtual)? sucesso, Function(String? falha)? falha}) {
    mStatusConsultaHistoricoCredito = ServiceStatus.Waiting;
    carregando?.call();
    _mServiceContrato
        .consultarHistoricoDeUtilizacaoDeCredito(_mCliente.mUsuarioLogado!.matricula!, UtilDataHora.getDiaMesAnoHorasMinutos(dateString: contratoVisualizar!.dataLancamento))
        .then((value) {
      mCreditosHistorico.clear();
      mCreditosHistorico.addAll(value);
      mCreditosHistorico.sort((b, a) => a.dataEmDateTime!.compareTo(b.dataEmDateTime!));
      GetIt.I.get<ControladorAulaTurma>().consultarSaldoAlunoOnline(
          sucesso: (saldo) {
            mStatusConsultaHistoricoCredito = ServiceStatus.Done;
            sucesso?.call(saldo);
          },
          falha: falha);
    }).catchError((onError) {
      falha?.call(onError.message);
      mStatusConsultaHistoricoCredito = ServiceStatus.Error;
    });
  }

  SituacaoCliente? situacaoUsuarioLogado;

  consultarSituacaoAluno({Function()? onSuccess, Function()? onError, Function()? onLoading}) {
    onLoading?.call();
    GetIt.I.get<ServiceAuth>().consultarSituacaoAluno(_mCliente.mUsuarioLogado!.matricula!).then((SituacaoCliente situacao) {
      situacaoUsuarioLogado = situacao;
      onSuccess?.call();
    }).catchError((e) {
      onError?.call();
    });
  }

  bool isUsuarioFrequenteParaRenovacaoAntecipada({required num qtdeAcessosSemanaPassada, required num qtdeAcessosSemana2, required num qtdeAcessosSemana3, required num qtdeAcessosSemana4}) {
    try {
      if (qtdeAcessosSemanaPassada + qtdeAcessosSemana2 + qtdeAcessosSemana3 + qtdeAcessosSemana4 >= 8) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  bool get isContratoConcomitantes {
    bool result = false;
    if (mListaContratos.isNotEmpty && mListaContratos.length >= 2) {
      for (var i = 0; i <= 1; i++) {
        if (mListaContratos[i]?.situacaoSubordinada == 'AT' ||
            mListaContratos[i]?.situacaoSubordinada == 'AV' ||
            mListaContratos[i]?.situacaoSubordinada == 'NO' ||
            mListaContratos[i]?.situacaoSubordinada == 'TR' ||
            mListaContratos[i]?.situacaoSubordinada == 'FR') {
          result = true;
        } else {
          result = false;
        }
      }
    }
    return result;
  }

  bool get isVisitante => contratoVisualizar?.situacaoSubordinada?.contains('VI') ?? false;
  bool get isDesistente => contratoVisualizar?.situacaoSubordinada?.contains('DE') ?? false;
  bool get isVencido => contratoVisualizar?.situacaoSubordinada?.contains('VE') ?? false;
  bool get isCancelado => contratoVisualizar?.situacaoSubordinada?.contains('CA') ?? false;

  Future<void> limparTudo() async {
    // Resetar todos os status de serviços
    mStatusConsultaContratos = ServiceStatus.Waiting;
    mStatusSimulacaoRenovacao = ServiceStatus.Waiting;
    mStatusConsultaTrancamento = ServiceStatus.Waiting;
    mStatusValidacaoContrato = ServiceStatus.Waiting;
    mStatusSimulacaoCarencia = ServiceStatus.Waiting;
    mStatusConsultaHistoricoCredito = ServiceStatus.Waiting;
    mStatusConsultaCreditosPermitidos = ServiceStatus.Waiting;
    mConsultaAlunoDestinatarioCredito = ServiceStatus.Waiting;

    // Resetar variáveis booleanas observáveis
    isPlanoSelecionado = false;
    isMeuscontratoSelecionado = true;

    // Limpar strings
    frequenciaUsuarioLogado = '';
    _consultarContratoAluno = '';

    // Limpar todas as listas
    mListaContratos = [];
    mTodasParcelas = [];
    listaContratosDropDown = [];
    mCreditosHistorico = [];
    contratoAssinatura.clear();
    operacaoesContrato.clear();

    // Resetar objetos observáveis
    mContratoSelecionado = null;
    mCreditosPermitidosTransferir = null;
    mAlunoDestinatarioCredito = null;

    // Resetar enum para valor padrão
    filtroSelecionadoParcelas = EnumFiltrarParcelas.TODOS;

    // Limpar SharedPreferences
    var prefs = await SharedPreferences.getInstance();
    await prefs.remove('contrato_assinado');
    await prefs.remove('data_consulta_contrato');
    await prefs.remove('aditivos_assinados');

    // Limpar dados do banco local
    await deletarDoBanco('_consultarContratoAluno');
    await deletarDoBanco('_consultarContratoAlunoDate');
    await deletarDoBanco('termosContratoAssinado');
  }

  void gerarBoletoParcelaEspecifica({required num movParcela, required Function() carregando, required Function(BoletoParcela? boleto)? sucesso, required Function(String? falha)? falha}) {
    _mServiceContrato.gerarBoletoParcelaEspecifica(_mCliente.mUsuarioLogado!.codEmpresa!, num.parse(_mCliente.mUsuarioLogado!.matricula!), movParcela).then((value) {
      var valueMap = jsonDecode(value);
      BoletoParcela dadosBoleto = BoletoParcela();
      dadosBoleto = valueMap.map((dynamic i) => BoletoParcela.fromJson(i as Map<String, dynamic>)).toList()[0];
      sucesso!(dadosBoleto);
    }).catchError((e) {
      falha!('');
    });
  }

  void consultarLinkConviteAmigo({required Function() carregando, required Function(String boleto)? sucesso, required Function(String falha) falha}) {
    carregando();
    _mServiceContrato.consultarLinkConviteAmigo(_mCliente.mUsuarioLogado!.codEmpresa!, num.parse(_mCliente.mUsuarioLogado!.matricula!)).then((value) {
      sucesso!(value);
    }).catchError((e) {
      falha(e);
    });
  }

  Future<void> consultarContratosAluno(
      {Function()? carregando,
      Function(ContratoAssinatura contrato)? sucesso,
      Function(List<ContratoAssinatura> listaContratos)? sucessoLista,
      Function(String falha)? falha,
      int? idContrato,
      int? idAditivo,
      bool retornarTodos = false,
      bool somenteNaoAssinados = true,
      bool forceconsulta = false}) async {
    carregando?.call();
    forceconsulta = true;
    try {
      if (_consultarContratoAluno.isEmpty || forceconsulta) {
        var dbInfo = await buscarListaNoBanco('_consultarContratoAluno');
        _consultarContratoAluno = dbInfo != null ? const JsonCodec().encode(dbInfo) : '';
        var ultimaConsulta = await buscarIntNoBanco('_consultarContratoAlunoDate');
        if (ultimaConsulta == null || DateTime.now().difference(DateTime.fromMillisecondsSinceEpoch(ultimaConsulta)).inDays >= 3 || forceconsulta) {
          _consultarContratoAluno = await _mServiceContrato.consultarContratoAluno(BodyContratoAlunoCrypto(
            validar: false,
            matricula: _mCliente.mUsuarioLogado!.matricula!,
            empresa: _mCliente.mUsuarioLogado!.codEmpresa!,
          ));
          try {
            if (const JsonCodec().decode(_consultarContratoAluno)['erro'] != null) {
              falha?.call(const JsonCodec().decode(_consultarContratoAluno)['erro']);
              return;
            }
          } catch (e) {}
          inserirIntNoBanco('_consultarContratoAlunoDate', DateTime.now().millisecondsSinceEpoch);
          await inserirListaNoBanco('_consultarContratoAluno', jsonDecode(_consultarContratoAluno));
        }
      }

      List<dynamic> valueMap = jsonDecode(_consultarContratoAluno);
      contratoAssinatura
        ..clear()
        ..addAll(valueMap.map((e) => ContratoAssinatura.fromJson(e)).toList());

      if (idContrato != null || idAditivo != null) {
        ContratoAssinatura? contratoEncontrado;

        if (idContrato != null && idAditivo != null) {
          contratoEncontrado = contratoAssinatura.firstWhereOrNull((c) => c.contrato == idContrato && c.aditivo == idAditivo);
          contratoEncontrado ??= contratoAssinatura.firstWhereOrNull((c) => c.contrato == idContrato);
        } else if (idContrato != null) {
          contratoEncontrado = contratoAssinatura.firstWhereOrNull((c) => c.contrato == idContrato);
        } else if (idAditivo != null) {
          contratoEncontrado = contratoAssinatura.firstWhereOrNull((c) => c.aditivo == idAditivo);
        }
        contratoEncontrado ??= contratoAssinatura.isNotEmpty ? contratoAssinatura.first : null;
        if (contratoEncontrado != null && sucesso != null && (!somenteNaoAssinados || contratoEncontrado.assinado != true)) {
          sucesso(contratoEncontrado);
          setarContratosDropDown();
          return;
        } else if (contratoEncontrado != null && contratoEncontrado.assinado == true && somenteNaoAssinados) {
          falha?.call('Contrato já está assinado');
          setarContratosDropDown();
          return;
        }
      }
      if (retornarTodos && sucessoLista != null) {
        sucessoLista(contratoAssinatura.toList());
        setarContratosDropDown();
        return;
      }
      List<ContratoAssinatura> contratosAtivos = contratoAssinatura.where((c) => (c.situacaocontrato ?? 'AT') == 'AT').toList();
      if (contratosAtivos.isEmpty) {
        falha?.call('Nenhum contrato ou aditivo encontrado');
        setarContratosDropDown();
        return;
      }
      if (somenteNaoAssinados) {
        List<ContratoAssinatura> contratosNaoAssinados = contratosAtivos.where((c) => c.assinado != true).toList();
        if (contratosNaoAssinados.isEmpty) {
          falha?.call('Todos os contratos já estão assinados');
          setarContratosDropDown();
          return;
        }
        List<ContratoAssinatura> aditivosNaoAssinados = contratosNaoAssinados.where((c) => (c.ehAditivo ?? false) == true).toList();

        if (aditivosNaoAssinados.isNotEmpty) {
          var aditivoOrdenado = aditivosNaoAssinados.reduce((a, b) => (a.aditivo ?? 0) > (b.aditivo ?? 0) ? a : b);
          sucesso?.call(aditivoOrdenado);
          setarContratosDropDown();
          return;
        }
        List<ContratoAssinatura> contratosPrincipaisNaoAssinados = contratosNaoAssinados.where((c) => (c.ehAditivo ?? false) == false).toList();
        if (contratosPrincipaisNaoAssinados.isNotEmpty) {
          sucesso?.call(contratosPrincipaisNaoAssinados.first);
          setarContratosDropDown();
          return;
        }
      } else {
        sucesso?.call(contratosAtivos.first);
        return;
      }
      falha?.call('Nenhum contrato ou aditivo pendente encontrado');
    } catch (e) {
      falha?.call(e.toString());
    } finally {
      setarContratosDropDown();
    }
  }

  Future<void> assinouAditivo(num contratoID, num aditivoID) async {
    var aditivoAssinado = contratoAssinatura.firstWhereOrNull((item) => item.contrato == contratoID && item.aditivo == aditivoID);
    if (aditivoAssinado != null) {
      aditivoAssinado.assinado = true;
    }
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> aditivosAssinados = prefs.getStringList('aditivos_assinados') ?? [];
    String aditivoChave = '$contratoID-$aditivoID';
    if (!aditivosAssinados.contains(aditivoChave)) {
      aditivosAssinados.add(aditivoChave);
      await prefs.setStringList('aditivos_assinados', aditivosAssinados);
    }
  }

  Future<void> assinouContrato(String data) async {
    try {
      SharedPreferences dataConsultarContrato = await SharedPreferences.getInstance();
      await dataConsultarContrato.remove('contrato_assinado');
      await dataConsultarContrato.setString('contrato_assinado', data);
      await deletarDoBanco('termosContratoAssinado');
    } catch (e) {}
  }

  final Map<String, List<Point>> _mapAssinaturasImagemBackup = {};
  final Map<String, String> _mapAssinaturasSelfieImagemBackup = {};
  final Map<String, bool> _mapAssinaturasTermosImagemBackup = {};

  void setarAssinaturaTermosBackupList({required num codigoContrato, num? codigoAditivo, required bool assinou}) {
    _mapAssinaturasTermosImagemBackup['$codigoContrato-$codigoAditivo'] = assinou;
  }

  void setarAssinaturaSelfieBackupList({required num codigoContrato, num? codigoAditivo, required String selfieBase64}) {
    _mapAssinaturasSelfieImagemBackup['$codigoContrato-$codigoAditivo'] = selfieBase64;
  }

  bool hasAssinaturaTermosNoContrato(num codigoContrato, num? codigoAditivo) {
    return _mapAssinaturasTermosImagemBackup['$codigoContrato-$codigoAditivo'] ?? false;
  }

  bool hasAssinaturaSelfieNoContrato(num codigoContrato, num? codigoAditivo) {
    return _mapAssinaturasSelfieImagemBackup.containsKey('$codigoContrato-$codigoAditivo');
  }

  bool hasAssinaturaNoContrato(num codigoContrato, num? codigoAditivo) {
    return _mapAssinaturasImagemBackup.containsKey('$codigoContrato-$codigoAditivo');
  }
  void setarAssinaturaPointsBackupList({required num codigoContrato, num? codigoAditivo, required List<Point> pointsList}) {
    _mapAssinaturasImagemBackup['$codigoContrato-$codigoAditivo'] = pointsList;
  }
  String assinaturaSelfieBackupList({required num codigoContrato, num? codigoAditivo}) {
    return _mapAssinaturasSelfieImagemBackup['$codigoContrato-$codigoAditivo'] ?? '';
  }
  List<Point> get primeiraAssinaturaValida {
    return _mapAssinaturasImagemBackup.values.firstWhere((element) => element.isNotEmpty, orElse: () => []);
  }
  List<Point> assinaturaPointsBackupList({required num codigoContrato, num? codigoAditivo}) {
    return _mapAssinaturasImagemBackup['$codigoContrato-$codigoAditivo'] ?? [];
  }

  Future<String> listPointToBase64(List<Point> pointsList) async {
    final _controllerExport = SignatureController(penStrokeWidth: 2, exportBackgroundColor: Colors.white, exportPenColor: Colors.black, points: pointsList);
    Uint8List? pngSignature = await _controllerExport.toPngBytes();
    _controllerExport.dispose();
    if (pngSignature != null) {
      String signatureString64 = base64.encode(pngSignature);
      return signatureString64;
    }
    return '';
  }

  void assinarContratoBase64({
    required num codigoContrato,
    num? codigoAditivo,
    required String assinaturaBase64,
    String? documento,
    String? ip,
    String? tipoAutenticacao,
    String? dadosAutenticacao,
    required Function() carregando,
    required Function(String) sucesso,
    required Function(String) falha,
    List<Point>? pointsList,
  }) {
    carregando();
    setarAssinaturaPointsBackupList(codigoContrato: codigoContrato, codigoAditivo: codigoAditivo, pointsList: pointsList ?? []);
    try {
      limparCacheContrato(codigoContrato).then((_) {
        // Nova verificação usando configuração ao invés de chave hardcoded
        final usarNovoFormatoAutenticador = _mApp.getConfiguracaoApp(ModuloApp.MODULO_AUTH_PIC_CONTRATO).habilitado == true;

        if (usarNovoFormatoAutenticador) {
          // Novo formato: sempre usa assinaturaRequest
          final assinaturaRequest = AssinaturaDigitalRequest(
            assinatura: assinaturaBase64,
            documentos: documento ?? '',
            ip: ip ?? '',
            tipoAutenticacao: tipoAutenticacao ?? '',
            dadosAutenticacao: dadosAutenticacao ?? '',
          );

          if (codigoAditivo != null) {
            _mServiceContrato
                .inserirAssinaturaBase64AditivoAutenticador(
              empresa: _mCliente.mUsuarioLogado!.codEmpresa!,
              contrato: codigoContrato,
              aditivo: codigoAditivo,
              assinaturaDigitalRequest: assinaturaRequest,
            )
                .then((value) async {
              try {
                await assinouAditivo(codigoContrato, codigoAditivo);
                consultarContratosAluno(
                  retornarTodos: true,
                  carregando: () {},
                  falha: (_) {},
                  sucessoLista: (_) {},
                );
              } catch (e) {
                print('Erro ao marcar aditivo como assinado: $e');
              }
              sucesso(value);
            }).catchError((e) {
              print('Erro ao assinar aditivo: $e');
              falha(e.toString());
            });
          } else {
            _mServiceContrato
                .inserirAssinaturaBase64Autenticador(
              empresa: _mCliente.mUsuarioLogado!.codEmpresa!,
              contrato: codigoContrato,
              assinaturaDigitalRequest: assinaturaRequest,
            )
                .then((value) {
              GetIt.I.get<ControladorCliente>().contratoAssinatura = true;
              consultarContratosAluno(
                retornarTodos: true,
                carregando: () {},
                falha: (_) {},
                sucessoLista: (_) {},
              );
              sucesso(value);
            }).catchError((e) {
              falha(e.toString());
            });
          }
        } else {
          // Fluxo antigo (padrão)
          if (codigoAditivo != null) {
            _mServiceContrato
                .inserirAssinaturaBase64Aditivo(
              _mCliente.mUsuarioLogado!.codEmpresa!,
              codigoContrato,
              codigoAditivo,
              assinaturaBase64,
            )
                .then((value) async {
              try {
                await assinouAditivo(codigoContrato, codigoAditivo);
                consultarContratosAluno(
                  retornarTodos: true,
                  carregando: () {},
                  falha: (_) {},
                  sucessoLista: (_) {},
                );
              } catch (e) {
                print('Erro ao marcar aditivo como assinado: $e');
              }
              sucesso(value);
            }).catchError((e) {
              print('Erro ao assinar aditivo: $e');
              falha(e.toString());
            });
          } else {
            _mServiceContrato
                .inserirAssinaturaBase64(
              _mCliente.mUsuarioLogado!.codEmpresa!,
              codigoContrato,
              assinaturaBase64,
            )
                .then((value) {
              GetIt.I.get<ControladorCliente>().contratoAssinatura = true;
              consultarContratosAluno(
                retornarTodos: true,
                carregando: () {},
                falha: (_) {},
                sucessoLista: (_) {},
              );
              sucesso(value);
            }).catchError((e) {
              falha(e.toString());
            });
          }
        }
      });
    } catch (e) {
      falha(e.toString());
    }
  }

  Future<void> analisarImagemComDocIA(
      {required Uint8List imagem, required String usuarioApp, required Function() carregando, required Function(RetornoValidacaoIA) sucesso, required Function(String) falha}) async {
    carregando();
    try {
      // // Comprimir a imagem
      var imagemComprimida = await FlutterImageCompress.compressWithList(
        imagem,
        minWidth: 800,
        minHeight: 800,
        quality: 50,
      );
      // Converter de volta para base64 com o prefixo correto
      final String formattedBase64 = 'data:image/jpeg;base64,${base64Encode(imagemComprimida)}';
      // Criar o objeto de acordo com a API
      final Map<String, dynamic> requestBody = {'imagemBase64': formattedBase64, 'usuarioApp': usuarioApp};
      // Usar o serviço através do GetIt e passar o retorno tipado diretamente
      _mServiceContrato.analisarImagemComDocumentoIA(requestBody).then((value) {
        // Agora value já é do tipo RetornoValidacaoIA
        sucesso(value);
      }).catchError((e) {
        falha(e.toString());
      });
    } catch (e) {
      falha('Erro ao processar imagem: $e');
    }
  }

  @observable
  CreditoPermitidos? mCreditosPermitidosTransferir;

  @observable
  ServiceStatus mStatusConsultaCreditosPermitidos = ServiceStatus.Waiting;

  Future<void> consultarCreditosPermitidos({
    Function()? carregando,
    Function()? sucesso,
    Function(String mensagem)? falha,
    Function()? vazio,
  }) {
    carregando?.call();
    mStatusConsultaCreditosPermitidos = ServiceStatus.Waiting;
    if (mStatusConsultaContratos == ServiceStatus.Empty || contratoVisualizar == null) {
      // Não faz sentido consultar créditos se não há contratos
      mCreditosPermitidosTransferir = null;
      mStatusConsultaCreditosPermitidos = ServiceStatus.Empty;
      vazio?.call();
      return Future.value();
    }
    return _mServiceContrato.consultarQuantosCreditosPodeTransferir(contratoId: contratoVisualizar!.codigo!).then((resultado) {
      mCreditosPermitidosTransferir = resultado;
      mStatusConsultaCreditosPermitidos = (mCreditosPermitidosTransferir?.quantidadeDeCreditosPermitidos != null && mCreditosPermitidosTransferir!.quantidadeDeCreditosPermitidos! > 0)
          ? ServiceStatus.Done
          : ServiceStatus.Empty;
      sucesso?.call();
    }).catchError((e) {
      mStatusConsultaCreditosPermitidos = ServiceStatus.Error;
      falha?.call('Erro ao consultar créditos disponíveis para transferência');
      print('Erro ao consultar créditos permitidos: $e');
    });
  }

  Future<void> atualizarContratosAlunoAposAssinatura() async {
    await consultarContratosAluno(forceconsulta: true, retornarTodos: true);
  }

  @observable
  ServiceStatus mConsultaAlunoDestinatarioCredito = ServiceStatus.Waiting;

  @observable
  AlunoDestinatario? mAlunoDestinatarioCredito;

  Future<void> buscarAlunoDestinatario({String? cpf, String? email, Function()? carregando, Function()? sucesso, Function()? vazio, Function(String mensagem)? erro}) {
    carregando?.call();
    mConsultaAlunoDestinatarioCredito = ServiceStatus.Waiting;

    return _mServiceContrato
        .buscarAlunoParaTransferirCreditos(
      cpf: cpf,
      email: email,
      empresa: _mCliente.mUsuarioLogado!.codEmpresa!,
    )
        .then((resultado) {
      mAlunoDestinatarioCredito = resultado;
      if (mAlunoDestinatarioCredito?.codigoAluno != null) {
        mConsultaAlunoDestinatarioCredito = ServiceStatus.Done;
        sucesso?.call();
      } else {
        mConsultaAlunoDestinatarioCredito = ServiceStatus.Empty;
        vazio?.call();
        erro?.call(mAlunoDestinatarioCredito?.mensagem ?? 'Aluno não encontrado');
      }
    }).catchError((e) {
      mConsultaAlunoDestinatarioCredito = ServiceStatus.Error;
      erro?.call('Erro ao buscar aluno. Verifique os dados informados.');
      print('Erro ao buscar aluno destinatário: $e');
    });
  }

  Future<bool> transferirCreditosTreino(
      {required num quantidade, required num destinatarioId, required Function() carregando, required Function(String mensagem) sucesso, required Function(String mensagem) falha}) {
    carregando();

    return _mServiceContrato
        .transferirCreditos(TransferenciaCredito(
            alunoId: num.parse(_mCliente.mUsuarioLogado!.codigoCliente), contratoId: contratoVisualizar!.codigo!, quantidadeCreditos: quantidade, destinatarioId: destinatarioId))
        .then((resposta) {
      if (resposta.sucesso) {
        sucesso(resposta.mensagem);
        return true;
      } else {
        falha(resposta.mensagem);
        return false;
      }
    }).catchError((e) {
      falha('Erro ao transferir créditos. Tente novamente mais tarde.');
      print('Erro ao transferir créditos: $e');
      return false;
    });
  }
}

num? diasAteVencer(String? dataString) {
  try {
    DateTime? data = UtilDataHora.parseStringToDate(dataString);
    return (data!.millisecondsSinceEpoch - DateTime.now().millisecondsSinceEpoch) ~/ 86400000;
  } catch (e) {
    return null;
  }
}

bool isContratoVencendo(num? diasAteVencer) {
  if (diasAteVencer == null || diasAteVencer < 0) {
    return false;
  }
  return diasAteVencer <= 15;
}

bool isContratoRecorrente(String? nomePlano) {
  if (nomePlano == null) {
    return false;
  }
  return nomePlano.contains('RECORRENTE');
}
