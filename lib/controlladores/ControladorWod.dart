import 'dart:async';
import 'package:app_treino/ServiceProvider/authServices/PersonalRecordService.dart';
import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/PersonalRecord.dart';
import 'package:app_treino/screens/novoCross/modalRankingEPostarResultado/PostarResultadoWodView.dart';
import 'package:app_treino/screens/novoCross/modalRankingEPostarResultado/RankingView.dart';
import 'package:collection/collection.dart';
import 'package:expandable/expandable.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:sembast/sembast.dart';
import 'dart:collection';
import 'package:app_treino/controlladores/ControladorDBExtend.dart';
import 'package:app_treino/ServiceProvider/WodService.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/model/wod/FiltrarAtividade.dart';
import 'package:app_treino/model/wod/WorkoutOfDay.dart';
import 'package:app_treino/model/wod/unidadeMedida.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:diacritic/diacritic.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorWod.g.dart';

class ControladorWod = _ControladorWodBase with _$ControladorWod;

abstract class _ControladorWodBase extends UtilDataBase with Store {
  final _wodService = GetIt.I.get<WodService>();
  final _mControladorUsuario = GetIt.I.get<ControladorCliente>();
  final _prService = GetIt.I.get<PersonalRecordService>();
  
  ObjetoPostagemResultadoWod objetoPostagemResultadoWod = ObjetoPostagemResultadoWod();

  @observable
  TipoFiltroPR filtroSelecionadoAtividadesCross = TipoFiltroPR.TODOS;

  /// se o fluxo é da tela do cross na home para a tela de historico, então os botões são de Ranking. Se é da tela do cronômetro, os botões são de postar resultado
  bool veioDoCronometro = false;

  @observable
  bool estaPesquisando = false;
  @observable
  bool estaNaTelaListaWods = true;
  @observable
  ServiceStatus statusWodsPorDia = ServiceStatus.Waiting;
  @observable
  ServiceStatus statusConsultaPR = ServiceStatus.Waiting;
  @observable
  ServiceStatus statusCarregamentoWods = ServiceStatus.Waiting;
  @observable
  ServiceStatus statusDetalhesDaAtividadeCross = ServiceStatus.Done;
  @observable
  ServiceStatus statusCarregamentoAtividadeCross = ServiceStatus.Waiting;
  @observable
  ServiceStatus statusDetalheAtividadeCross = ServiceStatus.Done;
  @observable
  ServiceStatus statusCarregamentoPr = ServiceStatus.Waiting;
  @observable
  ServiceStatus statusCarregamentoRanking = ServiceStatus.Waiting;
  @observable
  int mTempoIniciado = 0;
  @observable
  String mTempoDecorrido = '00:00';
  @observable
  bool isLibra = false;
  @observable
  List<PersonalRecords> listaPersonalRecords = [];
  @observable
  String? prAtualUsuario = '';
  @observable
  bool isMigrar = false;
  @observable
  int wodSelecionado = 0;

  // bool didUpdatePRs = false;

  WorkoutOfDay? wodPostar;

  bool mStart = true;
  bool mStop = true;
  bool mCronometroLigado = false;
  Timer? timer;
  int _tempoGasto = 0;
  var swatch = Stopwatch();

  List<SpotRankingWod>? rankingFiltrar = [];


  List<AtividadeWod> listaAtividadesWod = [];
  List<AtividadeWod> listaAtividadesRecordsOriginal = [];

  bool fNivelIN = false;
  bool fNivelAM = false;
  bool fNivelSC = false;
  bool fNivelRX = false;
  bool fSexoM = false;
  bool fSexoF = false;

  WorkoutOfDay? workoutOfDaySelecionado;

  @observable
  WorkoutOfDay? wodDetalhado;

  DateTime? mDataWodVisualizar;

  List<WorkoutOfDay> mListaWods = [];

  List<WorkoutOfDay> wodsDataSelecionada = [];

  List<WorkoutOfDay> listaWodsConsultados = [];

  PersonalRecords? personalRecord;

  ObservableList<PersonalRecords> listaPr = ObservableList<PersonalRecords>();

  String textopesquisa = '';
  TipoFiltroPR filtroSelecionadoAtividade = TipoFiltroPR.TODOS;
  EnumUnidadeMedida mUnidadeMedida = EnumUnidadeMedida.QUILOS;

  WorkoutOfDay? wodExpandido;
  ExpandableController controllerExpandable = new ExpandableController();

  bool filtrarAtividades(AtividadeWod element) {
    if (textopesquisa.isEmpty) {
      return true;
    }
    return removeDiacritics(element.nomeAtividade!.toLowerCase()).contains(removeDiacritics(textopesquisa.toLowerCase())) ||
        removeDiacritics(element.categoriaAtividadeWod!.toLowerCase()).contains(removeDiacritics(textopesquisa.toLowerCase()));
  }

  var mMapaRankingWods = LinkedHashMap<num?, List<SpotRankingWod>>();

  List<WorkoutOfDay> get wodsDeHoje {
    return mListaWods.where((element) => element.dia == UtilDataHora.getDiaMesAno(dateTime: mDataWodVisualizar ?? DateTime.now())).toList();
  }

  String get diaVisualizarString {
    if (mDataWodVisualizar == null) return '';

    return UtilDataHora.getDiaMes(dateTime: mDataWodVisualizar);
  }

  isWodFuturoHabilitado(DateTime? dateTime) {
    if (dateTime == null) {
      return true;
    }
    if (GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.BLOQUEAR_WODS_FUTUROS_PARA_ALUNOS).habilitado!) {
      return dateTime.isBefore(DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, 23, 59, 59));
    } else {
      return true;
    }
  }

  isEdicaoWodHabilitada(DateTime? dateTime) {
    if (dateTime == null) {
      return true;
    }
    return dateTime.isBefore(DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, 23, 59, 59));
  }

  getFotos3PrimeirosColocados(List<SpotRankingWod>? rankingWod){
    if (rankingWod != null) {
      List<String> urlFotos = ['', '', ''];
      for (int i = 0; i < rankingWod.length; i++) {
        if(i < 3) {
          urlFotos[i] = rankingWod[i].foto ?? '';
        }
      }
      return urlFotos;
    }
  }

  getNumeroParticipantesAlemDoPodio(List<SpotRankingWod>? rankingWod) {
    if (rankingWod != null) {
      if (rankingWod.length > 3) {
        return '+ ${(rankingWod.length - 3).toString()}';
      }
      else {
        return '+ 0';
      }
    }
  }

  getNumeroParticipantes(List<SpotRankingWod>? rankingWod) {
    if (rankingWod != null) {
      return rankingWod.length;
    }
  }

  getPosicaoUsuarioLogado(List<SpotRankingWod>? rankingWod) {
    if (rankingWod != null) {
      for (int i = 0; i < rankingWod.length; i++) {
        if (rankingWod[i].codUsuario == GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codUsuario) {
          return i + 1;
        }
      }
    }
    return null;

  }

  bool usuarioLogadoRegistrouResultado(List<SpotRankingWod>? rankingWod) {
    if (getPosicaoUsuarioLogado(rankingWod) != null) {
      return true;
    }
    else {
      return false;
    }
  }

  bool isUsuarioLogadoNoPodio(List<SpotRankingWod>? rankingWod) {
    return getPosicaoUsuarioLogado(rankingWod) <= 3 ? true : false;
  }

  Color? getCorNoRanking(List<SpotRankingWod>? rankingWod) {
    int posicaoUsuarioLogado = getPosicaoUsuarioLogado(rankingWod);

    switch (posicaoUsuarioLogado) {
      case 1:
        return const Color(0xffF0B924);
      case 2:
        return const Color(0xff80858C);
      case 3:
        return const Color(0xff946F0A);
      default:
        return null;
    }
  }

  void removerRegistroDoPr(MeuResultadoPr pr, AtividadeWod atividadeWod, {Function(AtividadeWod atv)? callback}) {
    atividadeWod.meusPr!.removeWhere((element) => element!.index == pr.index);
    inserirNoBanco('atividadeWo_${atividadeWod.codAtividade}', atividadeWod.toJson()).then((valor) {
      callback?.call(atividadeWod);
    });
  }

  List<DadoSimplesGrafico> obterDadosDoGrafico(AtividadeWod atividadeWod, num quantidade) {
    List<DadoSimplesGrafico> dadosRetorno = [];
    atividadeWod.meusPr ??= [];
    atividadeWod.meusPr!.sublist(0, atividadeWod.meusPr!.length > quantidade ? quantidade as int? : atividadeWod.meusPr!.length).forEach((element) {
      dadosRetorno.add(DadoSimplesGrafico(valor: element!.valorExibirGrafico(atividadeWod), label: UtilDataHora.getDaTaMesDiaAno(dataMilis: element.dataRegistro as int?)));
    });
    return dadosRetorno;
  }

  void salvarResultadoPr(AtividadeWod atividadeWod, Function(AtividadeWod atv) callback, num? dataInclusao, num? reps, num? time, num? weight, num? valorGenerico, {Function()? carregando, Function()? sucesso, Function()? falha}) {
    inserirNoBanco('atividadeWo_${atividadeWod.codAtividade}', atividadeWod.toJson()).then((valor) async {
      // ignore: unnecessary_cast
      return await Future.wait(listaAtividadesWod.map((e) => (buscarNoBanco('atividadeWo_${e.codAtividade}'))) as Iterable<Future>);
    }).then((amarzenadasNoBanco) {
      List<dynamic> tempBanco = [];
      tempBanco.addAll(amarzenadasNoBanco);
      tempBanco.removeWhere((element) => element == null);
      for (final element in tempBanco) {
        var atividade = AtividadeWod.fromJson((element as Map) as Map<String, dynamic>);
        var index = listaAtividadesWod.indexWhere((el) => el.codAtividade == atividade.codAtividade);
        listaAtividadesWod[index].meusPr = atividade.meusPr;
      }
      listaAtividadesRecordsOriginal.clear();
      listaAtividadesRecordsOriginal.addAll(listaAtividadesWod);
      callback.call(atividadeWod);
      atualizarPr(
        atividadeWod, {
        'dataInclusao': dataInclusao,
        'codAtividade': atividadeWod.codAtividade.toString(),
        'reps': reps.toString(),
        'time': time.toString(),
        'weight': weight.toString(),
        'valorGenerico': valorGenerico.toString(),
        'tipo': atividadeWod.unidadeMedida
      },
      carregando: () {
        carregando?.call();
        statusCarregamentoPr = ServiceStatus.Waiting;
      },
      sucesso: (){
        obterPrDaAtividade(atividadeWod.codAtividade);
        sucesso?.call();
        statusCarregamentoPr = ServiceStatus.Done;
      },
      falha: (){
        falha?.call();
        statusCarregamentoPr = ServiceStatus.Error;
      }
      );
    }).catchError((onError) {
      
      statusCarregamentoAtividadeCross = ServiceStatus.Error;
      falha?.call();
    });
  }

  void obterRecordsDaAtividade(AtividadeWod atividadeWod, Function(AtividadeWod atv) callback, {Function()? carregando, Function()? sucesso, Function()? falha})async {
    List<AtividadeWod> listaItens = [];
    for (final atividadeWod in listaAtividadesRecordsOriginal) {
      return listaItens.add(atividadeWod);
    }
    List<Map<String,dynamic>> jsonEnviar = [];
    for (final atividade in listaItens) {
      var mapaPrs = atividade.meusPr?.map((e) => {
        'dataInclusao': UtilDataHora.parseNunToDate(e!.dataRegistro),
        'codAtividade': atividade.codAtividade.toString(),
        'reps': e.repeticoes.toString(),
        'time': e.tempo.toString(),
        'weight': e.peso.toString(),
        'valorGenerico': e.valorGenerico.toString(),
        'tipo': atividade.unidadeMedida,
      });
      jsonEnviar.addAll(mapaPrs!);
      for (final element in jsonEnviar) {
        atualizarPr(atividadeWod, element, sucesso: sucesso, falha: falha);
          consultarPR();
      }
    }
  }

  inserirCampoNivelEmUltimo(List<TipoCampoWod> listCampos) {
    if (listCampos.contains(TipoCampoWod.NIVEL)) {
      listCampos.removeWhere((field) => field == TipoCampoWod.NIVEL);
    } 
    listCampos.add(TipoCampoWod.NIVEL);
  }

  List<TipoCampoWod> obterCamposDoWod(WorkoutOfDay wod) {
    List<TipoCampoWod> wodFields = [];
    wod.tipoWodTabela!.camposResultado!.split(',').forEach((field) {
      wodFields.add(TipoCampoWodExtension.getTipoCampoByRetorno(field));
    });
    // faz o campo de nivel ser o ultimo da lista para fins de layout
    inserirCampoNivelEmUltimo(wodFields);
    return wodFields;
  }

  bool isWodFuturo(DateTime? dataPesquisaWod) {
    if (dataPesquisaWod == null) {
      return false;
    }
    if (dataPesquisaWod.compareTo(DateTime.utc(DateTime.now().year, DateTime.now().month, DateTime.now().day, 23, 59, 59)) > 0) {
      return true;
    }
    return false;
  }

  bool isWodPassado(DateTime? dataPesquisaWod) {
    if (dataPesquisaWod == null) {
      return false;
    }
    // now.compareTo(future); // -1
    // now.compareTo(past); // 1
    // now.compareTo(newDate); // 0
    // se for 1 o resultado então o objeto é depois do parâmetro
    if (dataPesquisaWod.compareTo(DateTime.utc(DateTime.now().year, DateTime.now().month, DateTime.now().day - 1, 00, 00, 00)) > 0) {
      return false;
    }
    return true;
  }

  consultarWodPelaData({ required DateTime data, Function()? onLoading, Function()? onSuccess, Function(String? msg)? onError}) {
    statusWodsPorDia = ServiceStatus.Waiting;
      getServicoWod(_mControladorUsuario.mUsuarioLogado!.codUsuario!, UtilDataHora.getDiaMesAno(dateTime: data), UtilDataHora.getDiaMesAno(dateTime: data),GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa ?? 0).then((List<WorkoutOfDay> responseWods) {
      wodsDataSelecionada.clear();
      wodsDataSelecionada.addAll(responseWods);
      return Future.wait(wodsDataSelecionada.map((e) => _wodService.obterRanking(e.codigo!)).toList());
    }).then((List<List<SpotRankingWod>> listRankings) {
      // seta cada ranking consultado para o respectivo wod correspondente
      for (final ranking in listRankings) {
        wodsDataSelecionada.firstWhereOrNull((wod) => wod.codigo == ranking.firstOrNull?.codigoWOD)?.rankingWod = ranking;
      }
      onSuccess?.call();
      statusWodsPorDia = wodsDataSelecionada.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    }).catchError((onError) {
      onError?.call(onError.message);
      statusWodsPorDia = ServiceStatus.Error;
    });
  }

  void consultarWods({Function()? carregando, Function()? sucesso, Function(String? mensagem)? falha,}) {
    statusCarregamentoWods = ServiceStatus.Waiting;
    carregando?.call();
       getServicoWod(_mControladorUsuario.mUsuarioLogado!.codUsuario!, UtilDataHora.getDiaMesAno(dateTime: DateTime.now()),
        UtilDataHora.getDiaMesAno(dateTime: DateTime.now()),GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa?? 0).then((List<WorkoutOfDay> responseWods) {
      mListaWods.clear();
      mListaWods.addAll(responseWods);
      return Future.wait(mListaWods.map((e) => _wodService.obterRanking(e.codigo!)).toList());
    }).then((List<List<SpotRankingWod>> listRankings) {
      // seta cada ranking consultado para o respectivo wod correspondente
      for (final ranking in listRankings) {
        mListaWods.firstWhereOrNull((wod) => wod.codigo == ranking.firstOrNull?.codigoWOD)?.rankingWod = ranking;
      }
      statusCarregamentoWods = mListaWods.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
      statusCarregamentoWods = ServiceStatus.Error;
    });
  }

  Future<List<WorkoutOfDay>> getServicoWod(num usuario, String dataInicio, String dataFim, num empresaCodigoZw) async {
    FirebaseRemoteConfig.instance.setDefaults({'usar_novo_servico_consulta_wod': false});
    await FirebaseRemoteConfig.instance.fetchAndActivate();
    bool usarUrlNova = FirebaseRemoteConfig.instance.getBool('usar_novo_servico_consulta_wod');
    String? urlTreino = await ConfigURL().url('{TREINO}');
    if (urlTreino?.toLowerCase().contains('zw5') ?? false) {
      return _wodService.consultarWods(usuario, dataInicio, dataFim, empresaCodigoZw);
    } else {
      if (usarUrlNova) {
        return _wodService.consultarWods(usuario, dataInicio, dataFim, empresaCodigoZw);
      } else {
        return _wodService.consultarWodsUrlAntigo(usuario, dataInicio, dataFim);
      }
    }
  }

  void consultarWodPeloDia({Function()? carregando, bool forcarOnline = false, Function()? sucesso, Function(String? mensagem)? falha}) {
    // statusCarregamentoWods = ServiceStatus.Waiting;
    // if (wodsDeHoje.isNotEmpty && !forcarOnline) {
    //   if (wodsDeHoje.length == 1) {
    //     workoutOfDaySelecionado = wodsDeHoje.first;
    //   }
    //   statusCarregamentoWods = ServiceStatus.Done;
    //   sucesso?.call();
    //   return;
    // }
    // _wodService.consultarWods(_mControladorUsuario.mUsuarioLogado!.codUsuario!, UtilDataHora.getDiaMesAno(dateTime: mDataWodVisualizar), UtilDataHora.getDiaMesAno(dateTime: mDataWodVisualizar)).then((value) {
    //   mListaWods.clear();
    //   mListaWods.addAll(value);
    //   if (mListaWods.isNotEmpty) {
    //     wodExpandido = mListaWods.first;
    //     if (controllerExpandable.expanded) {
    //       controllerExpandable.toggle();
    //     }
    //   }
    //   for (final wod in value) {
    //     if (!mListaWods.any((element) => element.codigo == wod.codigo)) mListaWods.add(wod);
    //     mMapaRankingWods[wod.codigo] = [];
    //   }
    //   return Future.wait(value.map((e) => _wodService.obterRanking(e.codigo!)).toList());
    // }).then((value) {
    //   mMapaRankingWods.clear();
    //   for (final listaRanking in value) {
    //     for (final raking in listaRanking) {
    //       if (mMapaRankingWods[raking.codigoWOD] == null) mMapaRankingWods[raking.codigoWOD] = [];
    //       mMapaRankingWods[raking.codigoWOD]!.add(raking);
    //     }
    //   }
    //   if (wodsDeHoje.length == 1) {
    //     workoutOfDaySelecionado = wodsDeHoje.first;
    //   }
    //   sucesso?.call();
    //   statusCarregamentoWods = wodsDeHoje.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    // }).catchError((onError) {
    //   falha?.call(onError.message);
    //   statusCarregamentoWods = ServiceStatus.Error;
    // });
  }

  // void consultarWodsMatheus({Function()? carregando, DateTime? dataConsultaWods, Function()? sucesso, Function(String? mensagem)? falha, DateTime? diaConsultar, bool ignorarFiltroHoje = false}) {
  //       _wodService.consultarWods(_mControladorUsuario.mUsuarioLogado!.codUsuario!, UtilDataHora.getDiaMesAno(dateTime: dataConsultaWods), UtilDataHora.getDiaMesAno(dateTime: dataConsultaWods)
  //       ).then((List<WorkoutOfDay> responseListWods) {
  //         listaWodsConsultados.clear();
  //         listaWodsConsultados.addAll(responseListWods);
  //         return Future.wait(responseListWods.map((e) => _wodService.obterRanking(e.codigo!)).toList());
  //       }).then((List<List<RankingWorkoutOfDay>> listRankings) {
  //         mMapaRankingWods.clear();
  //         for (final ranking in listRankings) {
  //           for (final spotDoRanking in ranking) {
              
  //             listaWodsConsultados.firstWhereOrNull((wod) => wod.codigo == spotDoRanking.codigoWOD)?.listSpotRankingsWod?.add(spotDoRanking);
  //             if (mMapaRankingWods[spotDoRanking.codigoWOD] == null) {
  //               mMapaRankingWods[spotDoRanking.codigoWOD] = [];
  //             }
  //             mMapaRankingWods[spotDoRanking.codigoWOD]!.add(spotDoRanking);
  //           }
  //         }

  //         for (final listaRanking in listRankings) {
  //           for (final raking in listaRanking) {
  //             if (mMapaRankingWods[raking.codigoWOD] == null) mMapaRankingWods[raking.codigoWOD] = [];
  //             mMapaRankingWods[raking.codigoWOD]!.add(raking);
  //           }
  //         }
  //       }).catchError((error) {

  //       });

  // }

  List<SpotRankingWod>? obterRankingPorWod(WorkoutOfDay workoutOfDay) {
    if (mMapaRankingWods[workoutOfDay.codigo] != null) {
      mMapaRankingWods[workoutOfDay.codigo]!.sort((a, b) => a.posicao!.compareTo(b.posicao!));
      statusCarregamentoRanking = ServiceStatus.Done;
      return mMapaRankingWods[workoutOfDay.codigo];
    }
    return [];
  }

  @observable
  FiltroGenero filtroGeneroRankingWod = FiltroGenero.TODOS;

  bool get alunoPossuiModalidadeCross {
    if (mListaWods.isEmpty || wodsDeHoje.isEmpty) return true;
    return mListaWods.where((element) => element.contratoCrossfit == true).isNotEmpty;
  }

  SpotRankingWod meuResultadoOuPrimeiroLugar(WorkoutOfDay workoutOfDaySelecionado) {
    if (rankingFiltrar == null || rankingFiltrar!.isEmpty) return SpotRankingWod(nome: '-', nivelSigla: '-', nivelDescricao: '', posicao: 0, descricao: '');
    return rankingFiltrar!.firstWhere((element) => element.posicao == 1, orElse: () => rankingFiltrar!.first);
  }

  List<SpotRankingWod>? resultadoDoWod(WorkoutOfDay workoutOfDaySelecionado) {
    if (mMapaRankingWods[workoutOfDaySelecionado.codigo] == null) mMapaRankingWods[workoutOfDaySelecionado.codigo] = [];
    return mMapaRankingWods[workoutOfDaySelecionado.codigo];
  }

  void resultadoDoWodFiltrado(List<SpotRankingWod>? listaRankingWorkoutOfDay) {
    statusCarregamentoRanking = ServiceStatus.Waiting;

    if (fNivelIN == true && fSexoM == false && fSexoF == false) {
      rankingFiltrar = listaRankingWorkoutOfDay!.where((e) => e.nivelSigla == NivelAlunoWod.INICIANTE.getAbreviacao).toList();
    } else if (fNivelIN == true && fSexoM == true) {
      rankingFiltrar = listaRankingWorkoutOfDay!.where((e) => e.nivelSigla == NivelAlunoWod.INICIANTE.getAbreviacao && e.sexo == 'M').toList();
    } else if (fNivelIN == true && fSexoF == true) {
      rankingFiltrar = listaRankingWorkoutOfDay!.where((e) => e.nivelSigla == NivelAlunoWod.INICIANTE.getAbreviacao && e.sexo == 'F').toList();
    } else if (fNivelAM == true && fSexoM == false && fSexoF == false) {
      rankingFiltrar = listaRankingWorkoutOfDay!.where((e) => e.nivelSigla == NivelAlunoWod.AMADOR.getAbreviacao).toList();
    } else if (fNivelAM == true && fSexoM == true) {
      rankingFiltrar = listaRankingWorkoutOfDay!.where((e) => e.nivelSigla == NivelAlunoWod.AMADOR.getAbreviacao && e.sexo == 'M').toList();
    } else if (fNivelAM == true && fSexoF == true) {
      rankingFiltrar = listaRankingWorkoutOfDay!.where((e) => e.nivelSigla == NivelAlunoWod.AMADOR.getAbreviacao && e.sexo == 'F').toList();
    } else if (fNivelSC == true && fSexoM == false && fSexoF == false) {
      rankingFiltrar = listaRankingWorkoutOfDay!.where((e) => e.nivelSigla == NivelAlunoWod.SCALED.getAbreviacao).toList();
    } else if (fNivelSC == true && fSexoM == true) {
      rankingFiltrar = listaRankingWorkoutOfDay!.where((e) => e.nivelSigla == NivelAlunoWod.SCALED.getAbreviacao && e.sexo == 'M').toList();
    } else if (fNivelSC == true && fSexoF == true) {
      rankingFiltrar = listaRankingWorkoutOfDay!.where((e) => e.nivelSigla == NivelAlunoWod.SCALED.getAbreviacao && e.sexo == 'F').toList();
    } else if (fNivelRX == true && fSexoM == false && fSexoF == false) {
      rankingFiltrar = listaRankingWorkoutOfDay!.where((e) => e.nivelSigla == NivelAlunoWod.AVANCADO.getAbreviacao).toList();
    } else if (fNivelRX == true && fSexoM == true) {
      rankingFiltrar = listaRankingWorkoutOfDay!.where((e) => e.nivelSigla == NivelAlunoWod.AVANCADO.getAbreviacao && e.sexo == 'M').toList();
    } else if (fNivelRX == true && fSexoF == true) {
      rankingFiltrar = listaRankingWorkoutOfDay!.where((e) => e.nivelSigla == NivelAlunoWod.AVANCADO.getAbreviacao && e.sexo == 'F').toList();
    } else if (fSexoM == true) {
      rankingFiltrar = listaRankingWorkoutOfDay!.where((e) => e.sexo == 'M').toList();
    } else if (fSexoF == true) {
      rankingFiltrar = listaRankingWorkoutOfDay!.where((e) => e.sexo == 'F').toList();
    } else {
      rankingFiltrar = listaRankingWorkoutOfDay;
    }

    statusCarregamentoRanking = rankingFiltrar!.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
  }

  List<WorkoutOfDay> get obterWodsDeHoje {
    return mListaWods.where((wod) => UtilDataHora.dataEigualDaDeHoje(dataString: wod.diaApresentar)).toList();
  }

  List<WorkoutOfDay> get obterWodsAnterioresAHoje {
    var wodsAnterioroes = mListaWods.where((wod) => UtilDataHora.dataMenorQueDeAgora(dataString: wod.diaApresentar)).toList();
    wodsAnterioroes.sort((a, b) => UtilDataHora.parseStringToDate(b.diaApresentar!)!.compareTo(UtilDataHora.parseStringToDate(a.diaApresentar!)!));
    return wodsAnterioroes;
  }

  bool get apresentarEmptyStateSemRankin {
    return obterWodsDeHoje.isEmpty && obterWodsAnterioresAHoje.isEmpty;
  }

  bool get mostrarBotaoRegistrarWod {
    return UtilDataHora.dataEigualDaDeHoje(
            dataString: wodDetalhado!.diaApresentar!) &&
        wodDetalhado!.tipoWodTabela!.camposResultado!.isNotEmpty &&
        wodDetalhado!.meuRanking == null;
    //return workoutOfDaySelecionado.tipoWodTabela.camposResultado.isNotEmpty && workoutOfDaySelecionado.ranking == null;
  }

  bool usuarioTemResultadoWod(WorkoutOfDay wod){
    bool resultado = false;
    if(wod.rankingWod?.isNotEmpty ?? false){
      for (final element in wod.rankingWod!) {
        if(element.nome?.toLowerCase() == GetIt.I.get<ControladorCliente>().mUsuarioLogado!.nome?.toLowerCase()){
          resultado = true;
        }
      }
    }else {
      resultado = false;
    }
    return resultado;
  }

  void pesquisarAtividadeRecords(String termo) {
    listaAtividadesWod.clear();
    if (termo.isNotEmpty) {
      var termoLimpo = removeDiacritics(termo.toLowerCase());
      listaAtividadesWod.addAll(listaAtividadesRecordsOriginal.where((element) => removeDiacritics(element.nomeAtividade!.toLowerCase()).contains(termoLimpo)));
      statusCarregamentoAtividadeCross = listaAtividadesWod.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    } else {
      listaAtividadesWod.addAll(listaAtividadesRecordsOriginal);
      statusCarregamentoAtividadeCross = listaAtividadesWod.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    }
  }

  List<AtividadeWod> filtrarListaAtividadesPorNome(String termo, { required List<AtividadeWod> list }) {
    List<AtividadeWod> listOriginal = [];
    listOriginal.addAll(list);
    list.clear();
    if (termo.isNotEmpty) {
      var termoLimpo = removeDiacritics(termo.toLowerCase());
      list.addAll(listOriginal.where((element) => removeDiacritics(element.nomeAtividade!.toLowerCase()).contains(termoLimpo)));
    }
    return list;
  }

  void consultarAtividadesWod({Function()? carregando, Function()? sucesso, Function(String? mensagem)? falha}) {
    statusCarregamentoAtividadeCross = ServiceStatus.Waiting;
    carregando?.call();
    _wodService.obterAtividadesCross().then((value) async {
      listaAtividadesWod.clear();
      listaAtividadesWod.addAll(value);
      statusCarregamentoAtividadeCross = listaAtividadesWod.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      sucesso?.call();
    }).catchError((onError) {
      statusCarregamentoAtividadeCross = ServiceStatus.Error;
      falha?.call(onError.message);
    });
  }

  void consultarRankingDoWod({Function()? carregando, Function()? sucesso, Function(String? mensagem)? falha}) {
    _wodService.obterRanking(workoutOfDaySelecionado!.codigo!).then((value) {
      mMapaRankingWods[workoutOfDaySelecionado!.codigo] = value;
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.messagem);
    });
  }

  void registrarResultadoWod({ required WorkoutOfDay wod, Function()? carregando, Function()? sucesso, Function(String? mensagem)? falha, num? peso, num? tempo, num? repeticoes, String? comentario, num? rounds, WodNiveis? nivel}) {
    statusCarregamentoWods = ServiceStatus.Waiting;
    carregando?.call();
    _wodService.gravarResultado(wod.codigo!, _mControladorUsuario.mUsuarioLogado!.codUsuario!,
            rounds: rounds, tempo: tempo, repeticoes: repeticoes, peso: peso, comentario: comentario, nivel: (nivel != null ? nivel.id : null))
        .then((value) {
      wod.meuRanking = value;
      return _wodService.obterRanking(wod.codigo!);
    }).then((value) {
      wod.rankingWod = value;
      // mMapaRankingWods[workoutOfDaySelecionado!.codigo] = v alue;
      // var index = mListaWods.indexWhere((w) => w.codigo == workoutOfDaySelecionado!.codigo);
      // mListaWods[index].meuRanking = value.firstWhere((r) => r.codUsuario == _mControladorUsuario.mUsuarioLogado!.codUsuario);
      statusCarregamentoWods = ServiceStatus.Done;
      sucesso?.call();
    }).catchError((onError) {
      statusCarregamentoWods = ServiceStatus.Error;
      falha?.call(onError.message);
    });
  }

  AtividadeWod obterMaiorPrDaAtividade(AtividadeWod atividadeWod, {Function()? carregando, Function()? sucesso, Function()? falha}) {
    carregando?.call();
    if (atividadeWod.meusPr == null || atividadeWod.meusPr!.isEmpty) return atividadeWod;
    List<MeuResultadoPr?> listaAuxiliar = [];
    listaAuxiliar.addAll(atividadeWod.meusPr!);
    if(atividadeWod.unidadeMedida!.toLowerCase().contains('time')){
      listaAuxiliar.sort((a, b) => a!.valorExibirGrafico(atividadeWod).compareTo(b!.valorExibirGrafico(atividadeWod)));
    } else{
      listaAuxiliar.sort((a, b) => b!.valorExibirGrafico(atividadeWod).compareTo(a!.valorExibirGrafico(atividadeWod)));
    }
    for (int i = 0; i < atividadeWod.meusPr!.length; i++) {
      if (atividadeWod.meusPr![i] == listaAuxiliar.first) {
        atividadeWod.meusPr![i]!.maiorPr = true;
        //consultarPR(atividadeWod);
        break;
      } else {
        atividadeWod.meusPr![i]!.maiorPr = false;
        //consultarPR(atividadeWod);
      }
    }
    return atividadeWod;
  }

  startTimeProgressivo() {
    mCronometroLigado = true;
    timer = Timer.periodic(const Duration(seconds: 1), (t) {
      if (mCronometroLigado) {
        // ignore: unnecessary_null_comparison, prefer_conditional_assignment
        if (mTempoIniciado == null) mTempoIniciado = DateTime.now().millisecondsSinceEpoch;
        _tempoGasto = (DateTime.now().millisecondsSinceEpoch - mTempoIniciado) ~/ 1000;
        // _tempoGasto =  _tempoGasto + 1;
        int minutos = (_tempoGasto ~/ 60);
        int segundos = (_tempoGasto % 60).toInt();
        String minS = minutos < 10 ? '0$minutos' : minutos.toString();
        String segS = segundos < 10 ? '0$segundos' : segundos.toString();
        mTempoDecorrido = '$minS:$segS';
      } else {
        t.cancel();
      }
    });
  }

  void limparTudo() {
    statusCarregamentoAtividadeCross = ServiceStatus.Waiting;
    textopesquisa = '';
    for (final element in listaAtividadesWod) {
      store.record('atividadeWo_${element.codAtividade}').delete(getDb);
    }
    listaAtividadesWod = [];
    listaAtividadesRecordsOriginal = [];
    wodExpandido = null;
  }

  void consultarUnidadeMedida() {
    SharedPreferences.getInstance().then((value) {
      isLibra = value.getBool('usuario_selecionou_libras') ?? false;
    });
  }

  double converterQuiloEmLibra(double valor) {
    if (isLibra) {
      return valor * 2.205;
    } else {
      return valor;
    }
  }

  @observable
  AtividadeWod? atividadeCrossDetalhada;

  void consultarPR({Function()? carregando, Function()? sucesso, Function()? falha}) async{
    carregando?.call();
    statusConsultaPR = ServiceStatus.Waiting;
    listaPersonalRecords.clear();
    _prService.consultarPR(_mControladorUsuario.mUsuarioAuth!.uid!).then((value) {
    statusConsultaPR = ServiceStatus.Done;
      listaPersonalRecords.clear();
      listaPersonalRecords.addAll(value);
      sucesso?.call();
    }).catchError((onError){
      statusConsultaPR = ServiceStatus.Error;
      falha?.call();
    });
  }

   void obterPrDaAtividade(int? codigoAtividade, {Function()? carregando, Function()? sucesso, Function()? onError}) async{
    carregando?.call();
    statusCarregamentoPr = ServiceStatus.Waiting;
    List<PersonalRecords> listAux = [];
     await _prService.consultarPR(_mControladorUsuario.mUsuarioAuth!.uid!).then((value) {
      listaPersonalRecords.clear();
      listaPr.clear();
      listaPersonalRecords.addAll(value);
      for (final element in listaPersonalRecords) {
        if (element.codAtividade!.contains(codigoAtividade.toString())) {
          listAux.add(element);
        }
      }
      if (listAux.isNotEmpty) {
        ordenarRegistrosPorTipo(listAux);
      }
    listaPr.addAll(listAux);
    statusCarregamentoPr = listaPr.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    sucesso?.call();
    }).catchError((e){
      onError?.call();
      statusCarregamentoPr = ServiceStatus.Error;
    });
  }
List<PersonalRecords> buscarPrsDaAtividade(
  String codigoAtividade, {
  bool filtroAtividadeCross = false,
  bool popularListaPR = false,
  Function()? carregando,
  Function(List<PersonalRecords> registros)? sucesso,
  Function()? onError
}) {
  carregando?.call();
  statusCarregamentoPr = ServiceStatus.Waiting;
  try {
    final records = List<PersonalRecords>.from(listaPersonalRecords);
    List<PersonalRecords> resultado = records
        .where((element) => element.codAtividade == codigoAtividade)
        .toList();
      resultado = resultado
          .where((element) => element.atividadeCross == filtroAtividadeCross)
          .toList();

    if (resultado.isNotEmpty) {
      ordenarRegistrosPorTipo(resultado);
    }
    if(popularListaPR){
      listaPr.clear();
      listaPr.addAll(resultado);
    }
    statusCarregamentoPr = ServiceStatus.Done;
    sucesso?.call(resultado);
    
    return resultado;
  } catch (error) {
    statusCarregamentoPr = ServiceStatus.Error;
    onError?.call();
    return [];
  }
}
// Método auxiliar para ordenar registros por tipo
void ordenarRegistrosPorTipo(List<PersonalRecords> records) {
  if (records.isEmpty) return;
  
  final tipo = records.first.tipo?.toLowerCase() ?? '';
  
  switch (tipo) {
    case 'time':
      // Para tempo, o menor é melhor (ordem crescente)
      records.sort((a, b) => num.parse(a.time ?? '0').compareTo(num.parse(b.time ?? '0')));
      break;
      
    case 'reps':
      // Para repetições, o maior é melhor (ordem decrescente)
      records.sort((a, b) => num.parse(b.reps ?? '0').compareTo(num.parse(a.reps ?? '0')));
      break;

    case 'cals':
      // Para calorias, o maior é melhor
      records.sort((a, b) => num.parse(b.valorGenerico ?? '0').compareTo(num.parse(a.valorGenerico ?? '0')));
      break;

    case 'weight':
      // Para peso, o maior é melhor
      records.sort((a, b) => num.parse(b.weight ?? '0').compareTo(num.parse(a.weight ?? '0')));
      break;

    case 'rounds and reps':
      // Ordenação inicial por rounds
      records.sort((a, b) => num.parse(b.valorGenerico ?? '0').compareTo(num.parse(a.valorGenerico ?? '0')));
      // Aplicar critério de desempate
      records = desempatarRoundsAndReps(records);
      break;

    case 'reps for time':
      // Ordenação inicial por repetições
      records.sort((a, b) => num.parse(b.reps ?? '0').compareTo(num.parse(a.reps ?? '0')));
      // Aplicar critério de desempate
      records = desempatarRepsForTime(records);
      break;
      
    default:
      // Para outros tipos, ordenar por valor genérico
      records.sort((a, b) => num.parse(b.valorGenerico ?? '0').compareTo(num.parse(a.valorGenerico ?? '0')));
  }
}
List<PersonalRecords> getPersonalRecordsPorCodigoAtividade(String codigoAtividade) {
  return personalRecordsNaoCross
    .where((pr) => pr.codAtividade == codigoAtividade)
    .toList();
}

PersonalRecords? getMelhorPersonalRecordPorCodigoAtividade(String codigoAtividade) {
  final records = getPersonalRecordsPorCodigoAtividade(codigoAtividade);
  
  if (records.isEmpty) return null;
  
  // Clone para não afetar a lista original
  final recordsOrdenados = List<PersonalRecords>.from(records);
  final tipo = recordsOrdenados.first.tipo?.toLowerCase() ?? '';
  
  // Ordena a lista com base no tipo de PR
  switch (tipo) {
    case 'time':
      // Para tempo, o menor é melhor
      recordsOrdenados.sort((a, b) => num.parse(a.time ?? '0').compareTo(num.parse(b.time ?? '0')));
      break;
    case 'weight':
      // Para peso, o maior é melhor
      recordsOrdenados.sort((a, b) => num.parse(b.weight ?? '0').compareTo(num.parse(a.weight ?? '0')));
      break;
    case 'reps':
      // Para repetições, o maior é melhor
      recordsOrdenados.sort((a, b) => num.parse(b.reps ?? '0').compareTo(num.parse(a.reps ?? '0')));
      break;
    default:
      // Para outros tipos, ordenar por valor genérico
      recordsOrdenados.sort((a, b) => num.parse(b.valorGenerico ?? '0').compareTo(num.parse(a.valorGenerico ?? '0')));
  }
  
  return recordsOrdenados.isNotEmpty ? recordsOrdenados.first : null;
}

  desempatarRepsForTime (List<PersonalRecords> list) {
    for (int i = 0; i < list.length; i++) {
      for (int j = i + 1; j < list.length; j++) {
        if (list[i].reps == list[j].reps) {
          if (num.parse(list[i].time!) > num.parse(list[j].time!)) {
            PersonalRecords? aux;
            aux = list[i];
            list[i] = list[j];
            list[j] = aux;
          }
        }
      }      
    }
    return list;
  }

  desempatarRoundsAndReps (List<PersonalRecords> list) {
    for (int i = 0; i < list.length; i++) {
      for (int j = i + 1; j < list.length; j++) {
        if (list[i].valorGenerico == list[j].valorGenerico) {
          if (num.parse(list[i].reps!) < num.parse(list[j].reps!)) {
            PersonalRecords? aux;
            aux = list[i];
            list[i] = list[j];
            list[j] = aux;
          }
        }
      }      
    }
    return list;
  }

 void atualizarPr(AtividadeWod? atividadeWod, Map<String, dynamic> pr, {Function()? carregando, Function()? sucesso, Function()? falha}) {
    statusCarregamentoPr = ServiceStatus.Waiting;
    carregando?.call();
    _prService.atualizarPr(_mControladorUsuario.mUsuarioAuth!.uid.toString(), pr).then((value) {
      statusCarregamentoPr = value.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      sucesso?.call();
    }).catchError((onError) {
      falha?.call();
      statusCarregamentoPr = ServiceStatus.Error;
    });
  }
  
 void manterPr({  required Map<String, dynamic> pr, Function()? carregando, Function()? sucesso, Function()? falha }) {
    carregando?.call();
    _prService.atualizarPr(_mControladorUsuario.mUsuarioAuth!.uid.toString(), pr).then((value) {
      sucesso?.call();
    }).catchError((onError) {
      falha?.call();
    });
  }

  void deletaPr(PersonalRecords resultado, { Function()? carregando, Function()? sucesso, Function()? falha}) {
    statusCarregamentoPr = ServiceStatus.Waiting;
    carregando?.call();
    _prService.removerPr(_mControladorUsuario.mUsuarioAuth!.uid.toString(), resultado.id).then((value) {
      listaPersonalRecords.removeWhere((element) => element.id == resultado.id);
      if (listaPersonalRecords.isEmpty) {
        _prService.consultarPR(_mControladorUsuario.mUsuarioAuth!.uid.toString()).then((value) {
          List<PersonalRecords> temp = [];
          for (final element in value) {
            element.reps ??= '';
            element.valorGenerico ??= '';
            element.time ??= '';
            element.weight ??= '';
            temp.add(element);
          }
        });
      }
      sucesso!();
      statusCarregamentoPr = ServiceStatus.Done;
    }).catchError((onError) {
      falha!();
      statusCarregamentoPr = ServiceStatus.Error;
    });
  }

  deletaPrNovo(PersonalRecords pr, { Function()? carregando, Function()? sucesso, Function()? falha}) async {
    carregando?.call();
    await _prService.removerPr(_mControladorUsuario.mUsuarioAuth!.uid.toString(), pr.id).then((value) {
      sucesso?.call();
    }).catchError((onError) {
      falha?.call();
    });
  }

  void dadosMigradoFirebase(bool migrado) async {
    SharedPreferences mDadosMigrado = await SharedPreferences.getInstance();
    await mDadosMigrado.setBool('dados_migrado_firebase', migrado);
    isMigrar = migrado;
  }

  void migracaoPr() {
    SharedPreferences.getInstance().then((value) {
      isMigrar = value.getBool('dados_migrado_firebase') ?? false;
    });
  }

  String mostrarResultadoWod({required WorkoutOfDay wod}) {
   final meuRanking = (wod.rankingWod ?? []).firstWhere(
    (w) => w.codUsuario == _mControladorUsuario.mUsuarioLogado?.codUsuario,
    orElse: () => SpotRankingWod(), 
  );
    String tipoWod = wod.tipoWodTabela?.nome ?? '';
    String res = '';
 
    if(tipoWod.toLowerCase().contains('rounds')) {
      var rounds = meuRanking.rounds ?? 0;
      tipoWod.replaceFirst('rounds', '');
      if(res != '') res += '\n';
      res += '$rounds rounds';
    }
    if(tipoWod.toLowerCase().contains('reps')) {
      var reps = meuRanking.repeticoes ?? 0;
      tipoWod.replaceFirst('reps', '');
      if(res != '') res += '\n';
      res += '$reps reps';
    }
    if(tipoWod.toLowerCase().contains('time')) {
      var tempoWod = meuRanking.tempo ?? 0;
      tipoWod.replaceFirst('time', '');
      if(res != '') res += '\n';
      res += '${(tempoWod/3600).round().toString().padLeft(2,'0')}:${((tempoWod%3600)/60).round().toString().padLeft(2,'0')}:${(tempoWod%60).toString().padLeft(2,'0')}';
    }
    if(tipoWod.toLowerCase().contains('weight')) {
      tipoWod.replaceFirst('weight', '');
      if(res != '') res += '\n';
      consultarUnidadeMedida();
      num peso = converterQuiloEmLibra(meuRanking.peso ?? 0.0);
      res += '$peso ${isLibra ? 'lb':'kg'}';
    }

    return res;
  }

  String getImagemTag(AtividadeWod atividade) {
    String? urlRetorno;
    listaImagensShowCross.forEach((key, value) {
      key.split(',').forEach((element) {
        if (atividade.categoriaAtividadeWod!.toLowerCase().contains(element)) {
          urlRetorno = value;
        }
      });
    });
    return urlRetorno ?? 'assets/images/imagemWod/cross1.webp';
  }

  LinkedHashMap<String, String> imagens = LinkedHashMap();
  LinkedHashMap<String, String> get listaImagensShowCross {
    imagens['barbell'] = 'assets/images/imagemWod/barbell.webp';
    imagens['gymnastic'] = 'assets/images/imagemWod/endurance.webp';
    imagens['endurance'] = 'assets/images/imagemWod/cross4.webp';
    imagens['notable'] = 'assets/images/imagemWod/notble.webp';
    imagens['girls'] = 'assets/images/imagemWod/cross5.webp';
    imagens['open'] = 'assets/images/imagemWod/open.webp';
    imagens['heroes'] = 'assets/images/imagemWod/cross2.webp';
    imagens['padrao'] = 'assets/images/imagemWod/cross1.webp';
    return imagens;
  }

  LinkedHashMap<String, String> get listaImagensShowBeachCross {
    imagens['alongamento'] = 'assets/images/imageBeach/geral.jpeg';
    imagens['defesa'] = 'assets/images/imageBeach/defesa.jpeg';
    imagens['curta'] = 'assets/images/imageBeach/dropshot.jpeg';
    imagens['dropshot'] = 'assets/images/imageBeach/dropshot.jpeg';
    imagens['forehand'] = 'assets/images/imageBeach/forehand.jpeg';
    imagens['smash'] = 'assets/images/imageBeach/smash.jpeg';
    imagens['saque'] = 'assets/images/imageBeach/saque.jpeg';
    imagens['gancho'] = 'assets/images/imageBeach/gancho.jpeg';
    imagens['beach tennis,beach'] = 'assets/images/imageBeach/geral.jpeg';
    return imagens;
  }

  bool get habilitarAcoesCross{
    if ((GetIt.I.get<ControladorCliente>().mLogadoSituacao?.tipoAcesso?.toLowerCase().contains('gympass') ?? false) && (GetIt.I.get<ControladorCliente>().mLogadoSituacao?.checkInAtivo ?? false)) {
     return true;
    } else if ((GetIt.I.get<ControladorCliente>().mLogadoSituacao?.tipoAcesso?.toLowerCase().contains('totalpass') ?? false) && (GetIt.I.get<ControladorCliente>().mLogadoSituacao?.checkInAtivo ?? false)) {
      return true;
    } else if (alunoPossuiModalidadeCross && (!(GetIt.I.get<ControladorCliente>().isVisitante || GetIt.I.get<ControladorCliente>().isDesistente || GetIt.I.get<ControladorCliente>().isVencido || GetIt.I.get<ControladorCliente>().isCancelado))) {
      return true;
    } else if(GetIt.I.get<ControladorCliente>().isUsuarioColaborador) {
      return true;
    } else {
     return false;
    }
  }

  bool get habilitarPostarResultadoWod {
    bool retorno = true;
    var condicao1 = !((wodDetalhado?.tipoWodTabela?.camposResultado?.isNotEmpty ?? false) && (usuarioTemResultadoWod(wodDetalhado!)));
    var condicao2 = (alunoPossuiModalidadeCross && ((GetIt.I.get<ControladorCliente>().isAtivo || GetIt.I.get<ControladorCliente>().isAVencer) || GetIt.I.get<ControladorCliente>().isUsuarioColaborador || (GetIt.I.get<ControladorCliente>().mLogadoSituacao?.checkInAtivo ?? false)));
    var condicao3 = UtilDataHora.dataEigualDaDeHoje(dataString: wodDetalhado!.diaApresentar!) || UtilDataHora.dataMenorSomenteUmDia(dateTime: UtilDataHora.parseStringToDate(wodDetalhado!.diaApresentar!));

    if(condicao1 && condicao2 && condicao3) {
      retorno = true;
    } else {
      retorno = false;
    }
    return retorno;
  }

  void avaliarWod({required Function() carregando, required Function() sucesso, required Function(String erro) falha, required num? wod, String? comentario, num? nota, num? percepcaoEsforco}) {
    carregando.call();
    AvaliarWod avalicao = AvaliarWod(_mControladorUsuario.mUsuarioLogado?.codUsuario ?? 0, comentario, _mControladorUsuario.mUsuarioLogado?.codEmpresa ?? 1, nota, percepcaoEsforco, wod);
    _wodService.avaliarWod(avalicao.toJsonBody()).then((value) {
      if(value.toString().contains('erro')) {
        falha.call(value);
      } else {
        sucesso.call();
      }
    }).catchError((onError) {
      falha.call(onError);
    });
  }

  @observable
  bool usuarioJaAvaliouWod = false;

  void consultarAvaliacaoWod({required num wod, required num codUsuario, required Function() atualizar}) {
    List<AvaliarWod> avaliacoesWod = [];
    _wodService.consultarAvaliacoesWod().then((value) {
      try {
        if(value.isNotEmpty) {
          avaliacoesWod.addAll(value);
          for (final element in avaliacoesWod) {
            if(element.wod == wod && element.usuario == codUsuario) {
              usuarioJaAvaliouWod = true;
              atualizar.call();
              break;
            }
          }
        } else {
          usuarioJaAvaliouWod = false;
          atualizar.call();
        }
      } catch (e) {
        usuarioJaAvaliouWod = false;
        atualizar.call();
      }
    }).catchError((onError) {
      atualizar.call();
    });
  }

  String iconeEsforco(int valor) {
    switch (valor) {
      case 1:
        return 'assets/images/wodIntensidade/muitoleve.png';
      case 2:
        return 'assets/images/wodIntensidade/umpoucoleve.png';
      case 3:
        return 'assets/images/wodIntensidade/leve.png';
      case 4:
        return 'assets/images/wodIntensidade/delevemoderado.png';
      case 5:
        return 'assets/images/wodIntensidade/moderado.png';
      case 6:
        return 'assets/images/wodIntensidade/moderadointenso.png';
      case 7:
        return 'assets/images/wodIntensidade/poucointenso.png';
      case 8:
        return 'assets/images/wodIntensidade/intenso.png';
      case 9:
        return 'assets/images/wodIntensidade/muitointenso.png';
      case 10:
        return 'assets/images/wodIntensidade/extremo.png';
      default:
        return 'assets/images/wodIntensidade/moderado.png';
    }
  }

  @observable
  List<WodNiveis> listaNivelWod = [];

  void consultarNivelWod({required Function() carregando, required Function() sucesso, required Function(String erro) falha}) {
    carregando.call();
    _wodService.consultarNiveisWod().then((value) {
      listaNivelWod.clear();
      listaNivelWod.addAll(value);
      sucesso.call();
    }).catchError((onError) {
      falha.call(onError.message);
    });
  }

  List<PersonalRecords> get personalRecordsNaoCross {
  return listaPersonalRecords
    .where((pr) => pr.atividadeCross == false)
    .toList();
}
}

class DadoSimplesGrafico {
  final String? label;
  final num? valor;

  DadoSimplesGrafico({this.label, this.valor});
}
