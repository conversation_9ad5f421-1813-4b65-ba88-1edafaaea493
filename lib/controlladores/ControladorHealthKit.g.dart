// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorHealthKit.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorHealthKit on _ControladorHealthKitBase, Store {
  late final _$quantidadePassosHojeAtom = Atom(
      name: '_ControladorHealthKitBase.quantidadePassosHoje', context: context);

  @override
  int? get quantidadePassosHoje {
    _$quantidadePassosHojeAtom.reportRead();
    return super.quantidadePassosHoje;
  }

  @override
  set quantidadePassosHoje(int? value) {
    _$quantidadePassosHojeAtom.reportWrite(value, super.quantidadePassosHoje,
        () {
      super.quantidadePassosHoje = value;
    });
  }

  late final _$quantidadeCaloriasHojeAtom = Atom(
      name: '_ControladorHealthKitBase.quantidadeCaloriasHoje',
      context: context);

  @override
  double get quantidadeCaloriasHoje {
    _$quantidadeCaloriasHojeAtom.reportRead();
    return super.quantidadeCaloriasHoje;
  }

  @override
  set quantidadeCaloriasHoje(double value) {
    _$quantidadeCaloriasHojeAtom
        .reportWrite(value, super.quantidadeCaloriasHoje, () {
      super.quantidadeCaloriasHoje = value;
    });
  }

  late final _$_stateAtom =
      Atom(name: '_ControladorHealthKitBase._state', context: context);

  @override
  AppState get _state {
    _$_stateAtom.reportRead();
    return super._state;
  }

  @override
  set _state(AppState value) {
    _$_stateAtom.reportWrite(value, super._state, () {
      super._state = value;
    });
  }

  @override
  String toString() {
    return '''
quantidadePassosHoje: ${quantidadePassosHoje},
quantidadeCaloriasHoje: ${quantidadeCaloriasHoje}
    ''';
  }
}
