import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/ServiceProvider/authServices/ClienteAppService.dart';
import 'package:app_treino/controlladores/ControladorDBExtend.dart';
import 'package:app_treino/controlladores/ControladorTreinosPreDefinidos.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/manterExercicio/AtividadeManter.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';

part 'ControladorManterExercicio.g.dart';

class ControladorManterExercicio = _ControladorManterExercicioBase with _$ControladorManterExercicio;

abstract class _ControladorManterExercicioBase extends UtilDataBase with Store {
  bool carregouAgora = true;

  @observable
  ObservableList<Uint8List> mImagensPick = ObservableList<Uint8List>();

  @observable
  ObservableList<AtividadeBase> exericicios = ObservableList<AtividadeBase>();

  @observable
  ObservableList<AtividadeBase> exericiciosFiltrados = ObservableList<AtividadeBase>();

  @observable
  String filtro = 'TODAS';

  @observable
  ObservableList<ImagemBase> imagensBase = ObservableList<ImagemBase>();

  @observable
  ObservableList<ImagemBase> imagensBaseFiltradas = ObservableList<ImagemBase>();

  @observable
  ObservableList<ImagemBase> imagensAddBiblioteca = ObservableList<ImagemBase>();

  @observable
  ObservableList<GruposMuscular> gruposMusculares = ObservableList<GruposMuscular>();

  @observable
  ServiceStatus satusCarregamento = ServiceStatus.Waiting;

  var mService = GetIt.I.get<PersonalService>();
  void carregarImagensApartirDeFiltro({String termo = ''}) {
    imagensBaseFiltradas.clear();
    List<ImagemBase>? temp = [];
    if (termo.isNotEmpty) {
      temp.addAll(imagensBase.where((element) => element.nome!.toLowerCase().contains(termo.toLowerCase())).toList());
    } else {
      temp = imagensBase;
    }
    imagensBaseFiltradas.addAll(temp);
    temp = null;
  }

  Timer? _debounce;

  void carregarApartirDeFiltro({String termo = ''}) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 100), () {
      exericiciosFiltrados.clear();
      List<AtividadeBase>? temp = [];
      satusCarregamento = ServiceStatus.Waiting;
      if (termo.isNotEmpty) {
        temp.addAll(exericicios.where((element) => element.nome!.toLowerCase().contains(termo.toLowerCase())).toList());
      } else {
        temp = exericicios;
      }
      switch (filtro) {
        case 'TODAS':
          exericiciosFiltrados.addAll(temp);
          break;
        case 'SQ':
          exericiciosFiltrados.addAll(temp.where((element) => element.treinoLivre!));
          break;
        case 'CQ':
          exericiciosFiltrados.addAll(temp.where((element) => !element.treinoLivre!));
          break;
      }
      satusCarregamento = ServiceStatus.Done;
      temp = null;
    });
  }

  void adicionarImagemBiblioteca(ImagemBase image) {
    if (imagensAddBiblioteca.where((element) => element.id == image.id).isNotEmpty) {
      imagensAddBiblioteca.remove(image);
    } else {
      imagensAddBiblioteca.add(image);
    }
  }

  bool imagemEstaNaListaDeAdicionar(ImagemBase image) => imagensAddBiblioteca.where((element) => element.id == image.id).isNotEmpty;

  void adicionarImagem(Uint8List imagem) {
    mImagensPick.add(imagem);
  }

  void removerImagem(Uint8List imagem) {
    mImagensPick.remove(imagem);
  }

  final _mService = GetIt.I.get<ClienteAppService>();
  final _mServicePesonal = GetIt.I.get<PersonalService>();

  void consultarDadosBaseExercicios({Function()? sucesso, Function(String? erro)? falha}) {
    Future.wait([_mServicePesonal.consultarAtividadeBase(), _mServicePesonal.consultarCatalogoDeImagens(), _mServicePesonal.consultarGruposMusculares()]).then((value) {
      for (final element in value) {
        if (element is List<AtividadeBase>) {
          exericicios.clear();
          exericicios.addAll(element);
          carregarApartirDeFiltro();
        } else if (element is List<ImagemBase>) {
          imagensBase.clear();
          imagensBase.addAll(element);
          carregarImagensApartirDeFiltro();
        } else if (element is List<GruposMuscular>) {
          gruposMusculares.clear();
          gruposMusculares.addAll(element);
        }
      }

      sucesso?.call();
    }).catchError((onError) {
      satusCarregamento = ServiceStatus.Error;
      
      falha?.call(onError.message);
    });
  }

  void salvarExercicio(AtividadeBase atividadeManter, {Function(AtividadeBase atividadeBase)? sucesso, Function(String? erro)? erro}) {
    atividadeManter.imageUploads = [];
    for (final element in mImagensPick) {
      atividadeManter.imageUploads!.add(ImageUpload(data: base64Encode(element), nome: DateTime.now().toIso8601String()));
    }
    _mServicePesonal.manterAtividade(atividadeManter.toJson()).then((value) async {
      mImagensPick.clear();
      sucesso?.call(value);
      if (value.ativa!) {
        if (exericicios.where((element) => element.id == value.id).isEmpty) {
          exericicios.insert(0, value);
        } else {
          exericicios[exericicios.indexWhere((element) => element.id == value.id)] = value;
        }
        var controller = GetIt.I.get<ControladorTreinosPreDefinidos>();
        for (var i = 0; i < controller.todasAtividades.length; i++) {
          var atv = controller.todasAtividades[i];
          if (atv.cod == value.id) {
            atv.nome = value.nome;
            atv.treinoLivre = value.treinoLivre;
            atv.tipo = value.tipo == 'CARDIOVASCULAR'
                ? 0
                : value.tipo == 'NEUROMUSCULAR'
                    ? 1
                    : null;
            atv.tipoAtividade = atv.tipo;
            atv.imgMedium = value.images != null
                ? value.images!.isNotEmpty
                    ? value.images!.first.uri ?? null
                    : null
                : null;
            atv.urlVideo = value.videoUri;
            atv.imgMediumUrls ??= [];
            if (value.images != null) {
              atv.imgMediumUrls!.addAll(value.images!.map((e) => e.uri).toList());
            }
            break;
          }
        }
        await inserirNoBanco('todasAtividade', const JsonCodec().decode(const JsonCodec().encode(controller.todasAtividades)));
      } else {
        exericicios.removeWhere((element) => element.id == value.id);
      }
      carregarApartirDeFiltro();
    }).catchError((onError) {
      erro?.call(onError.message);
    });
  }

  void enviarImagensEPegarUrls({Function(List<String> imagens)? sucesso, Function(String? erro)? erro}) {
    if (mImagensPick.isNotEmpty) {
      Future.wait(mImagensPick.map((element) => _mService.uploadImagem(base64Encode(element))).toList()).then((imagens) {
        sucesso?.call(imagens);
      }).catchError((onError) {
        erro!(onError.message);
      });
    } else {
      sucesso?.call([]);
    }
  }

  limparTudo() {
    mImagensPick.clear();
  }
}
