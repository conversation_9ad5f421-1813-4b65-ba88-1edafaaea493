// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorTime.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorTime on _ControladorTimeBase, Store {
  late final _$tempoExibirAtom =
      Atom(name: '_ControladorTimeBase.tempoExibir', context: context);

  @override
  String get tempoExibir {
    _$tempoExibirAtom.reportRead();
    return super.tempoExibir;
  }

  @override
  set tempoExibir(String value) {
    _$tempoExibirAtom.reportWrite(value, super.tempoExibir, () {
      super.tempoExibir = value;
    });
  }

  late final _$roudsExibirAtom =
      Atom(name: '_ControladorTimeBase.roudsExibir', context: context);

  @override
  String get roudsExibir {
    _$roudsExibirAtom.reportRead();
    return super.roudsExibir;
  }

  @override
  set roudsExibir(String value) {
    _$roudsExibirAtom.reportWrite(value, super.roudsExibir, () {
      super.roudsExibir = value;
    });
  }

  late final _$pausadoAtom =
      Atom(name: '_ControladorTimeBase.pausado', context: context);

  @override
  bool get pausado {
    _$pausadoAtom.reportRead();
    return super.pausado;
  }

  @override
  set pausado(bool value) {
    _$pausadoAtom.reportWrite(value, super.pausado, () {
      super.pausado = value;
    });
  }

  late final _$descansoAtom =
      Atom(name: '_ControladorTimeBase.descanso', context: context);

  @override
  bool get descanso {
    _$descansoAtom.reportRead();
    return super.descanso;
  }

  @override
  set descanso(bool value) {
    _$descansoAtom.reportWrite(value, super.descanso, () {
      super.descanso = value;
    });
  }

  late final _$iniciouTimerAtom =
      Atom(name: '_ControladorTimeBase.iniciouTimer', context: context);

  @override
  bool get iniciouTimer {
    _$iniciouTimerAtom.reportRead();
    return super.iniciouTimer;
  }

  @override
  set iniciouTimer(bool value) {
    _$iniciouTimerAtom.reportWrite(value, super.iniciouTimer, () {
      super.iniciouTimer = value;
    });
  }

  late final _$porcentagemCompletadaAtom = Atom(
      name: '_ControladorTimeBase.porcentagemCompletada', context: context);

  @override
  num get porcentagemCompletada {
    _$porcentagemCompletadaAtom.reportRead();
    return super.porcentagemCompletada;
  }

  @override
  set porcentagemCompletada(num value) {
    _$porcentagemCompletadaAtom.reportWrite(value, super.porcentagemCompletada,
        () {
      super.porcentagemCompletada = value;
    });
  }

  late final _$iniciouPreTimerAtom =
      Atom(name: '_ControladorTimeBase.iniciouPreTimer', context: context);

  @override
  bool get iniciouPreTimer {
    _$iniciouPreTimerAtom.reportRead();
    return super.iniciouPreTimer;
  }

  @override
  set iniciouPreTimer(bool value) {
    _$iniciouPreTimerAtom.reportWrite(value, super.iniciouPreTimer, () {
      super.iniciouPreTimer = value;
    });
  }

  late final _$contadorInicioAtom =
      Atom(name: '_ControladorTimeBase.contadorInicio', context: context);

  @override
  num get contadorInicio {
    _$contadorInicioAtom.reportRead();
    return super.contadorInicio;
  }

  @override
  set contadorInicio(num value) {
    _$contadorInicioAtom.reportWrite(value, super.contadorInicio, () {
      super.contadorInicio = value;
    });
  }

  @override
  String toString() {
    return '''
tempoExibir: ${tempoExibir},
roudsExibir: ${roudsExibir},
pausado: ${pausado},
descanso: ${descanso},
iniciouTimer: ${iniciouTimer},
porcentagemCompletada: ${porcentagemCompletada},
iniciouPreTimer: ${iniciouPreTimer},
contadorInicio: ${contadorInicio}
    ''';
  }
}
