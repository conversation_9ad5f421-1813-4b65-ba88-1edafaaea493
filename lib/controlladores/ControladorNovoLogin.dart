// ignore_for_file: avoid_print

import 'dart:convert' as ct;
import 'dart:math';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorLoginPersonalizado.dart';
import 'package:app_treino/model/bodyscrypto.dart';
import 'package:crypto/crypto.dart';
import 'package:app_treino/ServiceProvider/authServices/ServiceAuth.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorDBExtend.dart';
import 'package:app_treino/fabricaGetIt.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/model/doClienteApp/UsuarioAppTelefone.dart';
import 'package:app_treino/model/doClienteApp/UsuarioKeep.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/BodyAuthSocial.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/BodyLinkLogin.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/LogSolicitacaoToken.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/ResultCentralDeAjuda.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/Usuario.dart';
import 'package:app_treino/model/util/UtilColor.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:app_treino/screens/novologin/ErroDeLogin.dart';
import 'package:app_treino/screens/novologin/TestesDeFluxo.enum.dart';
import 'package:app_treino/screens/novologin/TipoDeLoginSocial.enum.dart';
import 'package:app_treino/screens/novologin/widgets/InputSwitchUsuarioTelefone.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:sembast/sembast.dart';
part 'ControladorNovoLogin.g.dart';

class ControladorNovoLogin = _ControladorNovoLoginBase with _$ControladorNovoLogin;

abstract class _ControladorNovoLoginBase extends UtilDataBase with Store {
  FluxoDeLogin fluxoDeLogin = FluxoDeLogin.redesenhadoFluxoB;
  bool pularSenha = false;
  bool isSocialLoginLoading = false;

  // tokenObserver :)
  late Function() callLoginCodigoDeepLink;
  // countDownTimers :)
  int timerAteNovoSMS = InputTypeUsuarioTelefone.telefone.tempoAteNovo;
  int timerAteNovoEmail = InputTypeUsuarioTelefone.usuario.tempoAteNovo;
  @observable
  String deepLinkCodigo = '';
  @observable
  bool carregouTimer = false;
  String email = '', emailSocialVincular = '';
  String get emailEnviado {
    if (currentLoginType == InputTypeUsuarioTelefone.usuario) {
      return email;
    } else {
      return mUsuario.email ?? '';
    }
  }

  String telefone = '';
  String ddd = '+55';
  String ddi = '';
  List<String> tokensEnviados = [];
  Usuario mUsuario = Usuario();
  Usuario mUsuarioSocial = Usuario();
  List<Usuario> mOpcoesDeUsuario = [];
  List<Usuario> get mOpcoesDeUsuarioOrdenada {
    List<Usuario> mUsuariosColaboradores = mOpcoesDeUsuario.where((element) => element.mUserTel!.codigocolaborador != null && element.mUserTel!.codigocolaborador != 0).toList()
      ..sort((a, b) => (a.nome ?? '').compareTo(b.nome ?? ''));

    List<Usuario> mUsuariosAlunos = mOpcoesDeUsuario.where((element) => element.mUserTel?.matricula != null).toList()..sort((a, b) => (a.nome ?? '').compareTo(b.nome ?? ''));
    return [...mUsuariosColaboradores, ...mUsuariosAlunos];
  }

  bool isEsperandoCodigoEmailApple = false;
  BodyAuthSocial mVinculacaoAppleSocial = BodyAuthSocial();
  ResultCentralDeAjuda? mDadosCentralDeAjuda;
  // Controlador App
  ControladorApp get _controladorApp => GetIt.I.get<ControladorApp>();
  // Controlador Cliente
  ControladorCliente get _controlladorCliente => GetIt.I.get<ControladorCliente>();
  // Serviços de lgn
  ServiceAuth get _mServiceAuth => GetIt.I.get<ServiceAuth>();
  late InputTypeUsuarioTelefone currentLoginType;
  BodyLinkLogin bodyLinkLogin = BodyLinkLogin();
  List<UsuarioKeep> mUsuariosKeepPrevius = [];
  bool _deveExibirSMS = true;
  bool get exibirViewSolicitarSMS {
    if (currentLoginType == InputTypeUsuarioTelefone.telefone && !_deveExibirSMS) {
      return !UtilitarioApp.validarEnderecoEmail(mUsuario.email ?? mUsuario.username);
    }
    return _deveExibirSMS;
  }

  void loadRemoteConfig() {
    FirebaseRemoteConfig.instance.setDefaults({'habilitarBTNSMS': true});
    FirebaseRemoteConfig.instance.fetchAndActivate().then((value) {
      _deveExibirSMS = FirebaseRemoteConfig.instance.getBool('habilitarBTNSMS');
    });
  }

  void consultarInstalacoesPrevias(Function(bool hasPrevius) done) {
    if (_controlladorCliente.mUsuarioLogado != null) {
      done(true);
      return;
    }
    StoreRef<int, Map<String, dynamic>> store = intMapStoreFactory.store('usuariosKeep');
    store.find(getDb, finder: Finder()).then((records) {
      if (records.isEmpty) {
        _mServiceAuth.veificarUsuariosPorDeviceID({'deviceid': thedeviceID}).then((value) {
          _controladorApp.mClienteAppSelecionado = value.clienteApp;
          _controladorApp.defaultColor = HexColor.fromHex(value.clienteApp.corPadrao);
          if (_controladorApp.mClienteAppSelecionado?.empresaApps?.length == 1) {
            _controladorApp.chave = _controladorApp.mClienteAppSelecionado!.empresaApps!.first.chave;
          }
          mUsuariosKeepPrevius
            ..clear()
            ..addAll(value.usuarios);
          _controlladorCliente.mUsuariosSalvos
            ..clear()
            ..addAll(value.usuarios);
          done(true);
        }).catchError((onError) {
          done(false);
        });
      } else {
        done(false);
      }
    });
  }

  void validarCodigoEmail({Function()? carregando, Function()? sucesso, Function(ErroDeLoginEnum)? falha}) {
    carregando?.call();
    _mServiceAuth.validarCodigoLinkLogin(bodyLinkLogin).then((value) {
      sucesso?.call();
    }).catchError((x) {
      falha?.call(ErroDeLoginEnum.codigoIcorreto);
    });
  }

  void solicitarLinkECodigoEmail({bool validouTimer = false, Function()? carregando, Function()? sucesso, Function(ErroDeLoginEnum)? falha}) {
    if (!validouTimer) {
      _podeSolicitarToken(InputTypeUsuarioTelefone.usuario, podeSolicitar: () {
        solicitarLinkECodigoEmail(validouTimer: true, carregando: carregando, sucesso: sucesso, falha: falha);
      }, naoSolicitar: () {
        carregando?.call();
        Future.value(const Duration(seconds: 1)).then((value) => sucesso?.call());
      });
      return;
    }
    carregando?.call();
    bodyLinkLogin.chave = _controladorApp.chave;
    if (currentLoginType == InputTypeUsuarioTelefone.usuario) {
      bodyLinkLogin.email = email;
    } else {
      bodyLinkLogin.email = mUsuario.email;
    }
    bodyLinkLogin.userName = mUsuario.username;
    bodyLinkLogin.nomeDoApp = _controladorApp.mClienteAppSelecionado?.nomeDoApp;
    bodyLinkLogin.clienteAppID = _controladorApp.mClienteAppSelecionado?.id;
    bodyLinkLogin.nomePessoa = mUsuario.nome;
    _mServiceAuth.solicitarLinkDeLogin(bodyLinkLogin).then((value) {
      bodyLinkLogin = value;
      sucesso?.call();
      _putLogSolicitarToken(LogSolicitacaoToken(tipo: InputTypeUsuarioTelefone.usuario));
    }).catchError((x) {
      falha?.call(ErroDeLoginEnum.erroAoEnviarCodigoEmail);
    });
  }

  void consultarUsuario(
    InputTypeUsuarioTelefone tipoLogin, {
    bool isSocial = false,
    Function()? carregando,
    Function()? sucesso,
    Function(ErroDeLoginEnum)? falha,
    bool isRecursive = false,
    bool tentarNormal = false,
    bool validouLoginTeste = false,
  }) {
    if (!validouLoginTeste) {
      ControladorLoginPersonalizado().validarLoginPersonalizado(
          userName: isSocial ? (mUsuario.email ?? '') : email,
          pass: '',
          continueLogin: () {
            consultarUsuario(tipoLogin,
                isSocial: isSocial, carregando: carregando, sucesso: sucesso, falha: falha, isRecursive: isRecursive, tentarNormal: tentarNormal, validouLoginTeste: true);
          });
      return;
    }
    pularSenha = isSocial;
    currentLoginType = tipoLogin;
    Future<dynamic> mCall;
    mOpcoesDeUsuario = [];
    bool negarFluxo = false;
    switch (tipoLogin) {
      case InputTypeUsuarioTelefone.usuario:
        negarFluxo = email.isEmpty;
        if (tentarNormal) {
          mCall = _mServiceAuth.loginRedeSocial(BodyLoginCrypto(email: isSocial ? (mUsuario.email ?? '') : email, aluno: false));
        } else {
          mCall = _mServiceAuth.consultarPeloEmailOuUsername(BodyLoginCrypto(email: isSocial ? (mUsuario.email ?? '') : email));
        }
      case InputTypeUsuarioTelefone.telefone:
        negarFluxo = telefone.isEmpty || telefone.length < 8;
        mCall =
            _mServiceAuth.consultarPeloNumeroTelefone(BodyDescrobrirUsuariosCrypto(celular: telefone.replaceFirst(telefone.substring(0, 2), ''), ddi: ddi, ddd: telefone.substring(0, 2)));
    }
    if (negarFluxo && !isSocial) {
      carregando?.call();
      return;
    }

    carregando?.call();
    // consulta a url do treino e faz consuta de usuario
    _controladorApp.consultarUrlTreino(suceso: () {
      mCall.then((result) {
        extractUserOuUsers(result);
        sucesso?.call();
      }).catchError((onError) {
        if (onError?.toString().toLowerCase().contains('Email não encontrado'.toLowerCase()) ?? false) {
          falha?.call(ErroDeLoginEnum.usuarioNaoEcontrado);
        } else {
          falha?.call(ErroDeLoginEnum.usuarioNaoEcontrado);
        }
      });
    }, erro: (v) {
      falha?.call(ErroDeLoginEnum.usuarioNaoEcontrado);
    });
  }

  void extractUserOuUsers(result) {
    mOpcoesDeUsuario.clear();
    if (result is Usuario) {
      mUsuario = result;
    } else if (result is List<UsuarioAppTelefone>) {
      for (final mUser in result) {
        mOpcoesDeUsuario.add(Usuario(
            email: mUser.email,
            matricula: mUser.matricula,
            nome: mUser.nome,
            nomeEmpresa: mUser.nomeEmpresa,
            urlFoto: mUser.urlfoto,
            srcImgprev: mUser.urlfoto,
            perfilUsuario: (mUser.codigocolaborador ?? 0) > 0 ? 'COLABORADOR' : '',
            username: mUser.usernameusuario ?? mUser.usernameLoginRedeSocial,
            codUsuario: mUser.codigousuariotreino,
            mUserTel: mUser));
      }
    }
    if (mOpcoesDeUsuario.length == 1) {
      mUsuario = mOpcoesDeUsuario.first;
    }
  }

  void obterNovoTokenSMS({bool validouTimer = false, Function()? carregando, Function()? sucesso, Function(ErroDeLoginEnum)? falha}) {
    if ((modoHomologacao) || kDebugMode) {
      carregando?.call();
      if (tokensEnviados.any((c) => c.contains('debug'))) tokensEnviados.add('debug');
      Future.delayed(const Duration(seconds: 1)).then((value) => sucesso?.call());
      return;
    }
    if (!validouTimer) {
      _podeSolicitarToken(
        InputTypeUsuarioTelefone.telefone,
        podeSolicitar: () {
          analytic(EventosKey.lgn_novoSMS);
          obterNovoTokenSMS(validouTimer: true, carregando: carregando, sucesso: sucesso, falha: falha);
        },
        naoSolicitar: () {},
      );
      return;
    }
    carregando?.call();
    if (ddd.contains('+55')) {
      _mServiceAuth.gerarTokenSMSNovo('Utilize o código verificação', F.title, '$ddd$telefone'.replaceAll('+', '')).then((value) {
        try {
          tokensEnviados.add(value.split(' ')[2]);
          _putLogSolicitarToken(LogSolicitacaoToken(tipo: InputTypeUsuarioTelefone.telefone));
          sucesso?.call();
        } catch (e) {
          falha?.call(ErroDeLoginEnum.erroAoEnviarCodigoSMS);
        }
      }).catchError((onError) {
        falha?.call(ErroDeLoginEnum.erroAoEnviarCodigoSMS);
      });
    } else {
      FirebaseAuth.instance.verifyPhoneNumber(
        phoneNumber: '$ddd$telefone'.replaceAll('+', ''),
        timeout: const Duration(seconds: 30),
        verificationCompleted: (AuthCredential credential) {},
        verificationFailed: (FirebaseAuthException authException) {
          falha?.call(ErroDeLoginEnum.erroAoEnviarCodigoSMS);
        },
        codeSent: (String verificationId, [int? forceResendingToken]) {
          sucesso?.call();
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          falha?.call(ErroDeLoginEnum.codigoExpirado);
        },
      );
    }
  }

  Future<void> logarUsuario({String? pass, Function()? carregando, Function()? sucesso, Function(ErroDeLoginEnum)? falha}) async {
    switch (currentLoginType) {
      case InputTypeUsuarioTelefone.usuario:
        analytic(EventosKey.lgn_userPass);
        if (ControladorLoginPersonalizado().isTestePersonalizado(mUsuario.email?.toLowerCase() ?? '', pass ?? 'x')) {
          pass = '';
          await ControladorLoginPersonalizado().loadClienteApp();
          print(_controladorApp.mClienteAppSelecionado?.nomeDoApp);
          _controladorApp.prepararTelasBaseadoNasConfiguracoes(eColaborador: false, tipoApp: F.appFlavor!);
        }
        _controlladorCliente.logarUsuarioPorSenha(
            user: (mUsuario.mUserTel?.usernameLoginRedeSocial ?? '').isNotEmpty
                ? (mUsuario.mUserTel?.usernameLoginRedeSocial ?? mUsuario.mUserTel?.usernameusuario)
                : mUsuario.mUserTel?.nomeusuariomovel,
            pass: pass,
            carregando: carregando,
            sucesso: sucesso,
            codigoUsuarioTreino: mUsuario.mUserTel?.codigousuariotreino,
            isColaborador: (mUsuario.mUserTel?.codigocolaborador ?? 0) > 0,
            falha: (x) {
              if (x == 'user-disabled') {
                falha?.call(ErroDeLoginEnum.contaInativa);
                return;
              }
              if ((mUsuario.username?.isNotEmpty ?? false) || (pass?.isNotEmpty ?? false)) {
                falha?.call(ErroDeLoginEnum.senhaIncorreta);
              } else {
                falha?.call(ErroDeLoginEnum.usuarioNaoEcontrado);
              }
            });
        break;
      case InputTypeUsuarioTelefone.telefone:
        analytic(EventosKey.lgn_telefone);
        _controlladorCliente.logarPorTelefone(mUsuario.mUserTel!, loading: carregando!, sucesso: sucesso, onErro: (x) {
          if (x == 'user-disabled') {
            falha?.call(ErroDeLoginEnum.contaInativa);
            return;
          } else {
            falha?.call(ErroDeLoginEnum.usuarioNaoEcontrado);
          }
        });
        break;
    }
  }

  void realizarLoginRedeSocial(TipoLoginSocial tipoBtn, {Function()? carregando, Function()? sucesso, Function(ErroDeLoginEnum)? falha, required Function() vincularEmailApple}) {
    currentLoginType = InputTypeUsuarioTelefone.usuario;
    isEsperandoCodigoEmailApple = false;
    pularSenha = true;
    carregando?.call();
    switch (tipoBtn) {
      case TipoLoginSocial.google:
        _doGoogle(sucesso: sucesso, falha: falha);
      case TipoLoginSocial.facebook:
        _doFacebook(sucesso: sucesso, falha: falha);
      case TipoLoginSocial.apple:
        _doApple(sucesso: sucesso, falha: falha, vincularEmailApple: vincularEmailApple);
    }
  }

  String generateNonce([int length = 32]) {
    const charset = '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = Random.secure();
    return List.generate(length, (_) => charset[random.nextInt(charset.length)]).join();
  }

  String sha256ofString(String input) {
    final bytes = ct.utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  void _doApple({Function()? carregando, Function()? sucesso, Function(ErroDeLoginEnum)? falha, Function()? vincularEmailApple}) {
    final appleProvider = AppleAuthProvider()..addScope('email');
    FirebaseAuth.instance.signInWithProvider(appleProvider).then((providerResult) {
      return providerResult;
      // return FirebaseAuth.instance.signInWithCredential(providerResult.credential!);
    }).then((value) {
      mUsuario.email = value.user?.email;
      return _mServiceAuth.usuarioVinculadoApple(BodyAuthSocial(userIdentifierApple: value.user!.uid));
    }).then((responseSocial) {
      mVinculacaoAppleSocial = responseSocial;
      if (responseSocial.emailAdress?.isNotEmpty ?? false) {
        mUsuario.email = responseSocial.emailAdress!;
        consultarUsuario(InputTypeUsuarioTelefone.usuario, carregando: carregando, sucesso: sucesso, falha: falha, isSocial: true);
      } else if (mUsuario.email?.isNotEmpty ?? false) {
        consultarUsuario(InputTypeUsuarioTelefone.usuario, isSocial: true, carregando: carregando, sucesso: sucesso, falha: (v) {
          if (mUsuario.email?.contains('appleid.com') ?? false) {
            vincularEmailApple?.call();
          } else {
            falha?.call(ErroDeLoginEnum.usuarioNaoEcontrado);
          }
        });
      } else {
        vincularEmailApple?.call();
      }
    }).catchError((onError) {
      falha?.call(ErroDeLoginEnum.cancelou);
    });
  }

  void confirmCheckEmail({required Function() vinculado, required Function() naoVinculado}) {
    _mServiceAuth.vincularUsuariosSocial(mVinculacaoAppleSocial).then((value) {
      mVinculacaoAppleSocial = value;
      if (mVinculacaoAppleSocial.validouEmail) {
        vinculado();
      } else {
        naoVinculado();
      }
    });
  }

  void checkUserEmail(String email, {Function()? encontrouUser, Function()? carregando, Function()? semUsuario, Function(ErroDeLoginEnum)? falha}) {
    carregando?.call();
    late Future<List<UsuarioAppTelefone>> call;
    if (mUsuarioSocial.email?.toLowerCase().contains(email.toLowerCase()) ?? false) {
      call = Future.value(mOpcoesDeUsuario.map((e) => e.mUserTel!).toList());
    } else {
      call = _mServiceAuth.consultarPeloEmailOuUsername(BodyLoginCrypto(email: email));
      //   call = _mServiceAuth.loginRedeSocial(email, GetIt.I.get<ControladorCliente>().usuarioLogar?.matricula != null);
    }
    call.then((value) {
      extractUserOuUsers(value);
      if (value.isEmpty) {
        semUsuario?.call();
        return Future.value(BodyAuthSocial());
      } else {
        encontrouUser?.call();
        mVinculacaoAppleSocial.validouEmail = false;
        mVinculacaoAppleSocial.emailAdress = email;
        bodyLinkLogin.nomeDoApp = _controladorApp.mClienteAppSelecionado?.nomeDoApp;
        bodyLinkLogin.clienteAppID = _controladorApp.mClienteAppSelecionado?.id;
        bodyLinkLogin.nomePessoa = camelCase(value.first.nome);
        return _mServiceAuth.vincularUsuariosSocial(mVinculacaoAppleSocial);
      }
    }).then((value) {
      if (value.userIdentifierApple?.isEmpty ?? true) {
        return;
      }
      if (value.mensagem?.isEmpty ?? false) {
        _putLogSolicitarToken(LogSolicitacaoToken(tipo: InputTypeUsuarioTelefone.usuario));
      }
    }).catchError((onError) {
      semUsuario?.call();
    });
  }

  void _doFacebook({Function()? carregando, Function()? sucesso, Function(ErroDeLoginEnum)? falha}) {
    FacebookAuth.instance.login().then((resultOfLogin) async {
      if (resultOfLogin.status == LoginStatus.success) {
        return await FacebookAuth.instance.getUserData();
      } else {
        falha?.call(ErroDeLoginEnum.usuarioNaoEcontrado);
      }
    }).then((metaSignInAccount) {
      if (metaSignInAccount != null) {
        mUsuario.email = metaSignInAccount['email'];
        consultarUsuario(InputTypeUsuarioTelefone.usuario, isSocial: true, carregando: carregando, sucesso: sucesso, falha: falha);
      } else {
        falha?.call(ErroDeLoginEnum.cancelou);
      }
    }).catchError((onError) {
      falha?.call(ErroDeLoginEnum.cancelou);
    });
  }

  void _doGoogle({Function()? carregando, Function()? sucesso, Function(ErroDeLoginEnum)? falha}) {
    final googleProvider = GoogleAuthProvider()..addScope('email');
    FirebaseAuth.instance.signInWithProvider(googleProvider).then((providerResult) {
      return providerResult;
      // return FirebaseAuth.instance.signInWithCredential(providerResult.credential!);
    }).then((googleSignInAccount) {
      if (googleSignInAccount.user != null) {
        mUsuario.email = googleSignInAccount.user?.email;
        consultarUsuario(InputTypeUsuarioTelefone.usuario, isSocial: true, carregando: carregando, sucesso: sucesso, falha: falha);
      } else {
        falha?.call(ErroDeLoginEnum.cancelou);
      }
    }).catchError((onError) {
      falha?.call(ErroDeLoginEnum.cancelou);
    });
  }

  Future<void> _putLogSolicitarToken(LogSolicitacaoToken log) async {
    await inserirNoBanco(log.tipo.dbKey, log.toJson());
  }

  // Valida se pode solicitar novo Token SMS/ Email
  void _podeSolicitarToken(InputTypeUsuarioTelefone tipo, {required Function() podeSolicitar, required Function() naoSolicitar}) {
    // primeiro valida para E-mail
    carregouTimer = false;
    buscarNoBanco(tipo.dbKey).then((dbValue) {
      if (dbValue == null) {
        podeSolicitar();
      } else {
        LogSolicitacaoToken log = LogSolicitacaoToken.fromJson(dbValue);
        if (tipo == InputTypeUsuarioTelefone.usuario) {
          timerAteNovoEmail = log.timerRestante();
          if (log.timerRestante() == 0) {
            timerAteNovoEmail = InputTypeUsuarioTelefone.usuario.tempoAteNovo;
          }
        } else {
          timerAteNovoSMS = log.timerRestante();
          if (log.timerRestante() == 0) {
            timerAteNovoSMS = InputTypeUsuarioTelefone.telefone.tempoAteNovo;
          }
        }
        (log.timerRestante() == 0 ? podeSolicitar : naoSolicitar).call();
      }
      carregouTimer = true;
    });
  }

  void consultarCentralDeAjuda({Function()? carregando, Function(bool pularpop)? sucesso, Function()? falha}) {
    if (mDadosCentralDeAjuda != null) {
      return sucesso?.call(true);
    }
    carregando?.call();
    _mServiceAuth.getCentralDeAjuda().then((value) {
      mDadosCentralDeAjuda = value;
      sucesso?.call(false);
    }).catchError((onError) {
      falha?.call();
    });
  }
}
