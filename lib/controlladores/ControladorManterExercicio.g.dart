// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorManterExercicio.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorManterExercicio on _ControladorManterExercicioBase, Store {
  late final _$mImagensPickAtom = Atom(
      name: '_ControladorManterExercicioBase.mImagensPick', context: context);

  @override
  ObservableList<Uint8List> get mImagensPick {
    _$mImagensPickAtom.reportRead();
    return super.mImagensPick;
  }

  @override
  set mImagensPick(ObservableList<Uint8List> value) {
    _$mImagensPickAtom.reportWrite(value, super.mImagensPick, () {
      super.mImagensPick = value;
    });
  }

  late final _$exericiciosAtom = Atom(
      name: '_ControladorManterExercicioBase.exericicios', context: context);

  @override
  ObservableList<AtividadeBase> get exericicios {
    _$exericiciosAtom.reportRead();
    return super.exericicios;
  }

  @override
  set exericicios(ObservableList<AtividadeBase> value) {
    _$exericiciosAtom.reportWrite(value, super.exericicios, () {
      super.exericicios = value;
    });
  }

  late final _$exericiciosFiltradosAtom = Atom(
      name: '_ControladorManterExercicioBase.exericiciosFiltrados',
      context: context);

  @override
  ObservableList<AtividadeBase> get exericiciosFiltrados {
    _$exericiciosFiltradosAtom.reportRead();
    return super.exericiciosFiltrados;
  }

  @override
  set exericiciosFiltrados(ObservableList<AtividadeBase> value) {
    _$exericiciosFiltradosAtom.reportWrite(value, super.exericiciosFiltrados,
        () {
      super.exericiciosFiltrados = value;
    });
  }

  late final _$filtroAtom =
      Atom(name: '_ControladorManterExercicioBase.filtro', context: context);

  @override
  String get filtro {
    _$filtroAtom.reportRead();
    return super.filtro;
  }

  @override
  set filtro(String value) {
    _$filtroAtom.reportWrite(value, super.filtro, () {
      super.filtro = value;
    });
  }

  late final _$imagensBaseAtom = Atom(
      name: '_ControladorManterExercicioBase.imagensBase', context: context);

  @override
  ObservableList<ImagemBase> get imagensBase {
    _$imagensBaseAtom.reportRead();
    return super.imagensBase;
  }

  @override
  set imagensBase(ObservableList<ImagemBase> value) {
    _$imagensBaseAtom.reportWrite(value, super.imagensBase, () {
      super.imagensBase = value;
    });
  }

  late final _$imagensBaseFiltradasAtom = Atom(
      name: '_ControladorManterExercicioBase.imagensBaseFiltradas',
      context: context);

  @override
  ObservableList<ImagemBase> get imagensBaseFiltradas {
    _$imagensBaseFiltradasAtom.reportRead();
    return super.imagensBaseFiltradas;
  }

  @override
  set imagensBaseFiltradas(ObservableList<ImagemBase> value) {
    _$imagensBaseFiltradasAtom.reportWrite(value, super.imagensBaseFiltradas,
        () {
      super.imagensBaseFiltradas = value;
    });
  }

  late final _$imagensAddBibliotecaAtom = Atom(
      name: '_ControladorManterExercicioBase.imagensAddBiblioteca',
      context: context);

  @override
  ObservableList<ImagemBase> get imagensAddBiblioteca {
    _$imagensAddBibliotecaAtom.reportRead();
    return super.imagensAddBiblioteca;
  }

  @override
  set imagensAddBiblioteca(ObservableList<ImagemBase> value) {
    _$imagensAddBibliotecaAtom.reportWrite(value, super.imagensAddBiblioteca,
        () {
      super.imagensAddBiblioteca = value;
    });
  }

  late final _$gruposMuscularesAtom = Atom(
      name: '_ControladorManterExercicioBase.gruposMusculares',
      context: context);

  @override
  ObservableList<GruposMuscular> get gruposMusculares {
    _$gruposMuscularesAtom.reportRead();
    return super.gruposMusculares;
  }

  @override
  set gruposMusculares(ObservableList<GruposMuscular> value) {
    _$gruposMuscularesAtom.reportWrite(value, super.gruposMusculares, () {
      super.gruposMusculares = value;
    });
  }

  late final _$satusCarregamentoAtom = Atom(
      name: '_ControladorManterExercicioBase.satusCarregamento',
      context: context);

  @override
  ServiceStatus get satusCarregamento {
    _$satusCarregamentoAtom.reportRead();
    return super.satusCarregamento;
  }

  @override
  set satusCarregamento(ServiceStatus value) {
    _$satusCarregamentoAtom.reportWrite(value, super.satusCarregamento, () {
      super.satusCarregamento = value;
    });
  }

  @override
  String toString() {
    return '''
mImagensPick: ${mImagensPick},
exericicios: ${exericicios},
exericiciosFiltrados: ${exericiciosFiltrados},
filtro: ${filtro},
imagensBase: ${imagensBase},
imagensBaseFiltradas: ${imagensBaseFiltradas},
imagensAddBiblioteca: ${imagensAddBiblioteca},
gruposMusculares: ${gruposMusculares},
satusCarregamento: ${satusCarregamento}
    ''';
  }
}
