import 'package:app_treino/ServiceProvider/ContratoUsuarioService.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/contrato/BodyCancelarContrato.dart';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';

class ControladorContrato {
  final ContratoUsuarioService _service = GetIt.I.get<ContratoUsuarioService>();

  ServiceStatus statusCancelarContrato = ServiceStatus.Waiting;

  Future<void> cancelarContrato({
    required String matricula,
    required int codigoContrato,
    String? observacao,
    required Function() carregando,
    required Function() sucesso,
    required Function(String) falha,
  }) async {
    try {
      statusCancelarContrato = ServiceStatus.Waiting;
      carregando();

      final body = BodyCancelarContrato(
        matricula: matricula,
        codigoContrato: codigoContrato,
        observacao: observacao,
      );

      // Chamada ao serviço
      final usuario = GetIt.I.get<ControladorCliente>().mUsuarioLogado;
      if (usuario?.codEmpresa == null) {
        statusCancelarContrato = ServiceStatus.Error;
        falha('Sessão expirada. Faça login novamente.');
        return;
      }
      final String response = await _service.cancelarContrato(
        body,
        usuario!.codEmpresa!.toInt(),
      );

      
      // Verifica se a resposta indica sucesso
      if (response == 'success') {
        statusCancelarContrato = ServiceStatus.Done;
        sucesso();
      } else {
        statusCancelarContrato = ServiceStatus.Error;
        falha('Falha ao cancelar contrato');
      }
    } catch (e) {
      statusCancelarContrato = ServiceStatus.Error;
      
      String mensagemErro = 'Erro ao cancelar contrato';
      if (e is DioException) {
        if (e.response?.statusCode == 401) {
          mensagemErro = 'Sessão expirada. Faça login novamente.';
        } else if (e.response?.data != null) {
          // Tenta extrair a mensagem de erro da resposta
          if (e.response?.data is Map && e.response?.data['mensagem'] != null) {
            mensagemErro = e.response?.data['mensagem'];
          } else if (e.response?.data is String) {
            mensagemErro = e.response?.data;
          }
        } else if (e.message != null) {
          mensagemErro = e.message!;
        }
      }
      
      falha(mensagemErro);
    }
  }
}
