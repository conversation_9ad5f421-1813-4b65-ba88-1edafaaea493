// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorPlanoDeTreino.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorPlanoDeTreino on _ControladorPlanoDeTreinoBase, Store {
  late final _$programaManterAtom = Atom(
      name: '_ControladorPlanoDeTreinoBase.programaManter', context: context);

  @override
  ProgramaDeTreino? get programaManter {
    _$programaManterAtom.reportRead();
    return super.programaManter;
  }

  @override
  set programaManter(ProgramaDeTreino? value) {
    _$programaManterAtom.reportWrite(value, super.programaManter, () {
      super.programaManter = value;
    });
  }

  late final _$testeDataFinalAtom = Atom(
      name: '_ControladorPlanoDeTreinoBase.testeDataFinal', context: context);

  @override
  int get testeDataFinal {
    _$testeDataFinalAtom.reportRead();
    return super.testeDataFinal;
  }

  @override
  set testeDataFinal(int value) {
    _$testeDataFinalAtom.reportWrite(value, super.testeDataFinal, () {
      super.testeDataFinal = value;
    });
  }

  late final _$mudouAlgoAtom =
      Atom(name: '_ControladorPlanoDeTreinoBase.mudouAlgo', context: context);

  @override
  bool get mudouAlgo {
    _$mudouAlgoAtom.reportRead();
    return super.mudouAlgo;
  }

  @override
  set mudouAlgo(bool value) {
    _$mudouAlgoAtom.reportWrite(value, super.mudouAlgo, () {
      super.mudouAlgo = value;
    });
  }

  @override
  String toString() {
    return '''
programaManter: ${programaManter},
testeDataFinal: ${testeDataFinal},
mudouAlgo: ${mudouAlgo}
    ''';
  }
}
