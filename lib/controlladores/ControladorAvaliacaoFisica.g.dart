// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorAvaliacaoFisica.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorAvaliacaoFisica on _ControladorAvaliacaoFisicaBase, Store {
  late final _$mConsultaAvaliacoesAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.mConsultaAvaliacoes',
      context: context);

  @override
  ServiceStatus get mConsultaAvaliacoes {
    _$mConsultaAvaliacoesAtom.reportRead();
    return super.mConsultaAvaliacoes;
  }

  @override
  set mConsultaAvaliacoes(ServiceStatus value) {
    _$mConsultaAvaliacoesAtom.reportWrite(value, super.mConsultaAvaliacoes, () {
      super.mConsultaAvaliacoes = value;
    });
  }

  late final _$mConsultaAgendaAvaliacaoAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.mConsultaAgendaAvaliacao',
      context: context);

  @override
  ServiceStatus get mConsultaAgendaAvaliacao {
    _$mConsultaAgendaAvaliacaoAtom.reportRead();
    return super.mConsultaAgendaAvaliacao;
  }

  @override
  set mConsultaAgendaAvaliacao(ServiceStatus value) {
    _$mConsultaAgendaAvaliacaoAtom
        .reportWrite(value, super.mConsultaAgendaAvaliacao, () {
      super.mConsultaAgendaAvaliacao = value;
    });
  }

  late final _$listaDobrasAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.listaDobras', context: context);

  @override
  ObservableList<Dobra> get listaDobras {
    _$listaDobrasAtom.reportRead();
    return super.listaDobras;
  }

  @override
  set listaDobras(ObservableList<Dobra> value) {
    _$listaDobrasAtom.reportWrite(value, super.listaDobras, () {
      super.listaDobras = value;
    });
  }

  late final _$mListaCorporalAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.mListaCorporal', context: context);

  @override
  List<NewComposicaoCorporal> get mListaCorporal {
    _$mListaCorporalAtom.reportRead();
    return super.mListaCorporal;
  }

  @override
  set mListaCorporal(List<NewComposicaoCorporal> value) {
    _$mListaCorporalAtom.reportWrite(value, super.mListaCorporal, () {
      super.mListaCorporal = value;
    });
  }

  late final _$mListaDobrasAgrupadasAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.mListaDobrasAgrupadas',
      context: context);

  @override
  ObservableList<DobrasAgrupada> get mListaDobrasAgrupadas {
    _$mListaDobrasAgrupadasAtom.reportRead();
    return super.mListaDobrasAgrupadas;
  }

  @override
  set mListaDobrasAgrupadas(ObservableList<DobrasAgrupada> value) {
    _$mListaDobrasAgrupadasAtom.reportWrite(value, super.mListaDobrasAgrupadas,
        () {
      super.mListaDobrasAgrupadas = value;
    });
  }

  late final _$isDireitaAtom =
      Atom(name: '_ControladorAvaliacaoFisicaBase.isDireita', context: context);

  @override
  bool get isDireita {
    _$isDireitaAtom.reportRead();
    return super.isDireita;
  }

  @override
  set isDireita(bool value) {
    _$isDireitaAtom.reportWrite(value, super.isDireita, () {
      super.isDireita = value;
    });
  }

  late final _$indexFotoPosturalAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.indexFotoPostural',
      context: context);

  @override
  int get indexFotoPostural {
    _$indexFotoPosturalAtom.reportRead();
    return super.indexFotoPostural;
  }

  @override
  set indexFotoPostural(int value) {
    _$indexFotoPosturalAtom.reportWrite(value, super.indexFotoPostural, () {
      super.indexFotoPostural = value;
    });
  }

  late final _$fotoPosturalSelecionadaAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.fotoPosturalSelecionada',
      context: context);

  @override
  FotoUsuarioAvaliacao get fotoPosturalSelecionada {
    _$fotoPosturalSelecionadaAtom.reportRead();
    return super.fotoPosturalSelecionada;
  }

  @override
  set fotoPosturalSelecionada(FotoUsuarioAvaliacao value) {
    _$fotoPosturalSelecionadaAtom
        .reportWrite(value, super.fotoPosturalSelecionada, () {
      super.fotoPosturalSelecionada = value;
    });
  }

  late final _$mAvaliacoesFisicasAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.mAvaliacoesFisicas',
      context: context);

  @override
  List<AvaliacaoFisica> get mAvaliacoesFisicas {
    _$mAvaliacoesFisicasAtom.reportRead();
    return super.mAvaliacoesFisicas;
  }

  @override
  set mAvaliacoesFisicas(List<AvaliacaoFisica> value) {
    _$mAvaliacoesFisicasAtom.reportWrite(value, super.mAvaliacoesFisicas, () {
      super.mAvaliacoesFisicas = value;
    });
  }

  late final _$mBioimpedanciaAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.mBioimpedancia', context: context);

  @override
  Bioimpedancia get mBioimpedancia {
    _$mBioimpedanciaAtom.reportRead();
    return super.mBioimpedancia;
  }

  @override
  set mBioimpedancia(Bioimpedancia value) {
    _$mBioimpedanciaAtom.reportWrite(value, super.mBioimpedancia, () {
      super.mBioimpedancia = value;
    });
  }

  late final _$tipoFiltroTempoAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.tipoFiltroTempo',
      context: context);

  @override
  FiltroFrequencia get tipoFiltroTempo {
    _$tipoFiltroTempoAtom.reportRead();
    return super.tipoFiltroTempo;
  }

  @override
  set tipoFiltroTempo(FiltroFrequencia value) {
    _$tipoFiltroTempoAtom.reportWrite(value, super.tipoFiltroTempo, () {
      super.tipoFiltroTempo = value;
    });
  }

  late final _$paginaFotoComparacaoAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.paginaFotoComparacao',
      context: context);

  @override
  int get paginaFotoComparacao {
    _$paginaFotoComparacaoAtom.reportRead();
    return super.paginaFotoComparacao;
  }

  @override
  set paginaFotoComparacao(int value) {
    _$paginaFotoComparacaoAtom.reportWrite(value, super.paginaFotoComparacao,
        () {
      super.paginaFotoComparacao = value;
    });
  }

  late final _$mostrarMaskAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.mostrarMask', context: context);

  @override
  bool get mostrarMask {
    _$mostrarMaskAtom.reportRead();
    return super.mostrarMask;
  }

  @override
  set mostrarMask(bool value) {
    _$mostrarMaskAtom.reportWrite(value, super.mostrarMask, () {
      super.mostrarMask = value;
    });
  }

  late final _$dadosBasicosAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.dadosBasicos', context: context);

  @override
  DadosBasicoAlunoAvaliacao get dadosBasicos {
    _$dadosBasicosAtom.reportRead();
    return super.dadosBasicos;
  }

  @override
  set dadosBasicos(DadosBasicoAlunoAvaliacao value) {
    _$dadosBasicosAtom.reportWrite(value, super.dadosBasicos, () {
      super.dadosBasicos = value;
    });
  }

  late final _$fotobaseAtom =
      Atom(name: '_ControladorAvaliacaoFisicaBase.fotobase', context: context);

  @override
  String? get fotobase {
    _$fotobaseAtom.reportRead();
    return super.fotobase;
  }

  @override
  set fotobase(String? value) {
    _$fotobaseAtom.reportWrite(value, super.fotobase, () {
      super.fotobase = value;
    });
  }

  late final _$respondeuParqAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.respondeuParq', context: context);

  @override
  String get respondeuParq {
    _$respondeuParqAtom.reportRead();
    return super.respondeuParq;
  }

  @override
  set respondeuParq(String value) {
    _$respondeuParqAtom.reportWrite(value, super.respondeuParq, () {
      super.respondeuParq = value;
    });
  }

  late final _$mRespostaParQAlunoAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.mRespostaParQAluno',
      context: context);

  @override
  List<ItemRespostaParQ> get mRespostaParQAluno {
    _$mRespostaParQAlunoAtom.reportRead();
    return super.mRespostaParQAluno;
  }

  @override
  set mRespostaParQAluno(List<ItemRespostaParQ> value) {
    _$mRespostaParQAlunoAtom.reportWrite(value, super.mRespostaParQAluno, () {
      super.mRespostaParQAluno = value;
    });
  }

  late final _$assinaturaDoAlunoParqAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.assinaturaDoAlunoParq',
      context: context);

  @override
  AssinaturaParQ? get assinaturaDoAlunoParq {
    _$assinaturaDoAlunoParqAtom.reportRead();
    return super.assinaturaDoAlunoParq;
  }

  @override
  set assinaturaDoAlunoParq(AssinaturaParQ? value) {
    _$assinaturaDoAlunoParqAtom.reportWrite(value, super.assinaturaDoAlunoParq,
        () {
      super.assinaturaDoAlunoParq = value;
    });
  }

  late final _$devicesAtom =
      Atom(name: '_ControladorAvaliacaoFisicaBase.devices', context: context);

  @override
  List<IHealthDevice> get devices {
    _$devicesAtom.reportRead();
    return super.devices;
  }

  @override
  set devices(List<IHealthDevice> value) {
    _$devicesAtom.reportWrite(value, super.devices, () {
      super.devices = value;
    });
  }

  late final _$resultadoBioimpedanciaAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.resultadoBioimpedancia',
      context: context);

  @override
  BeurerBiaData? get resultadoBioimpedancia {
    _$resultadoBioimpedanciaAtom.reportRead();
    return super.resultadoBioimpedancia;
  }

  @override
  set resultadoBioimpedancia(BeurerBiaData? value) {
    _$resultadoBioimpedanciaAtom
        .reportWrite(value, super.resultadoBioimpedancia, () {
      super.resultadoBioimpedancia = value;
    });
  }

  late final _$manterAvaliacaoBioimpedanciaAlunoAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.manterAvaliacaoBioimpedanciaAluno',
      context: context);

  @override
  ManterAvaliacaoFisica get manterAvaliacaoBioimpedanciaAluno {
    _$manterAvaliacaoBioimpedanciaAlunoAtom.reportRead();
    return super.manterAvaliacaoBioimpedanciaAluno;
  }

  @override
  set manterAvaliacaoBioimpedanciaAluno(ManterAvaliacaoFisica value) {
    _$manterAvaliacaoBioimpedanciaAlunoAtom
        .reportWrite(value, super.manterAvaliacaoBioimpedanciaAluno, () {
      super.manterAvaliacaoBioimpedanciaAluno = value;
    });
  }

  late final _$listaDiferencaDobrasAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.listaDiferencaDobras',
      context: context);

  @override
  List<ResumoDobras> get listaDiferencaDobras {
    _$listaDiferencaDobrasAtom.reportRead();
    return super.listaDiferencaDobras;
  }

  @override
  set listaDiferencaDobras(List<ResumoDobras> value) {
    _$listaDiferencaDobrasAtom.reportWrite(value, super.listaDiferencaDobras,
        () {
      super.listaDiferencaDobras = value;
    });
  }

  late final _$mFotosAvaliacaoFisicaAtualAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.mFotosAvaliacaoFisicaAtual',
      context: context);

  @override
  ObservableList<FotoUsuarioAvaliacao> get mFotosAvaliacaoFisicaAtual {
    _$mFotosAvaliacaoFisicaAtualAtom.reportRead();
    return super.mFotosAvaliacaoFisicaAtual;
  }

  @override
  set mFotosAvaliacaoFisicaAtual(ObservableList<FotoUsuarioAvaliacao> value) {
    _$mFotosAvaliacaoFisicaAtualAtom
        .reportWrite(value, super.mFotosAvaliacaoFisicaAtual, () {
      super.mFotosAvaliacaoFisicaAtual = value;
    });
  }

  late final _$fotosAgrupadasAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.fotosAgrupadas', context: context);

  @override
  Map<String, List<FotoUsuarioAvaliacao>> get fotosAgrupadas {
    _$fotosAgrupadasAtom.reportRead();
    return super.fotosAgrupadas;
  }

  @override
  set fotosAgrupadas(Map<String, List<FotoUsuarioAvaliacao>> value) {
    _$fotosAgrupadasAtom.reportWrite(value, super.fotosAgrupadas, () {
      super.fotosAgrupadas = value;
    });
  }

  late final _$fotoSelecionadoAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.fotoSelecionado',
      context: context);

  @override
  String get fotoSelecionado {
    _$fotoSelecionadoAtom.reportRead();
    return super.fotoSelecionado;
  }

  @override
  set fotoSelecionado(String value) {
    _$fotoSelecionadoAtom.reportWrite(value, super.fotoSelecionado, () {
      super.fotoSelecionado = value;
    });
  }

  late final _$mGruposMuscularesAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.mGruposMusculares',
      context: context);

  @override
  GrupoMuscular? get mGruposMusculares {
    _$mGruposMuscularesAtom.reportRead();
    return super.mGruposMusculares;
  }

  @override
  set mGruposMusculares(GrupoMuscular? value) {
    _$mGruposMuscularesAtom.reportWrite(value, super.mGruposMusculares, () {
      super.mGruposMusculares = value;
    });
  }

  late final _$mGraficoGruposTrabalhadosAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.mGraficoGruposTrabalhados',
      context: context);

  @override
  ServiceStatus get mGraficoGruposTrabalhados {
    _$mGraficoGruposTrabalhadosAtom.reportRead();
    return super.mGraficoGruposTrabalhados;
  }

  @override
  set mGraficoGruposTrabalhados(ServiceStatus value) {
    _$mGraficoGruposTrabalhadosAtom
        .reportWrite(value, super.mGraficoGruposTrabalhados, () {
      super.mGraficoGruposTrabalhados = value;
    });
  }

  late final _$topSizeAtom =
      Atom(name: '_ControladorAvaliacaoFisicaBase.topSize', context: context);

  @override
  String get topSize {
    _$topSizeAtom.reportRead();
    return super.topSize;
  }

  @override
  set topSize(String value) {
    _$topSizeAtom.reportWrite(value, super.topSize, () {
      super.topSize = value;
    });
  }

  late final _$bottomSizeAtom = Atom(
      name: '_ControladorAvaliacaoFisicaBase.bottomSize', context: context);

  @override
  String get bottomSize {
    _$bottomSizeAtom.reportRead();
    return super.bottomSize;
  }

  @override
  set bottomSize(String value) {
    _$bottomSizeAtom.reportWrite(value, super.bottomSize, () {
      super.bottomSize = value;
    });
  }

  late final _$_ControladorAvaliacaoFisicaBaseActionController =
      ActionController(
          name: '_ControladorAvaliacaoFisicaBase', context: context);

  @override
  void setTopSize(String size) {
    final _$actionInfo = _$_ControladorAvaliacaoFisicaBaseActionController
        .startAction(name: '_ControladorAvaliacaoFisicaBase.setTopSize');
    try {
      return super.setTopSize(size);
    } finally {
      _$_ControladorAvaliacaoFisicaBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setBottomSize(String size) {
    final _$actionInfo = _$_ControladorAvaliacaoFisicaBaseActionController
        .startAction(name: '_ControladorAvaliacaoFisicaBase.setBottomSize');
    try {
      return super.setBottomSize(size);
    } finally {
      _$_ControladorAvaliacaoFisicaBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
mConsultaAvaliacoes: ${mConsultaAvaliacoes},
mConsultaAgendaAvaliacao: ${mConsultaAgendaAvaliacao},
listaDobras: ${listaDobras},
mListaCorporal: ${mListaCorporal},
mListaDobrasAgrupadas: ${mListaDobrasAgrupadas},
isDireita: ${isDireita},
indexFotoPostural: ${indexFotoPostural},
fotoPosturalSelecionada: ${fotoPosturalSelecionada},
mAvaliacoesFisicas: ${mAvaliacoesFisicas},
mBioimpedancia: ${mBioimpedancia},
tipoFiltroTempo: ${tipoFiltroTempo},
paginaFotoComparacao: ${paginaFotoComparacao},
mostrarMask: ${mostrarMask},
dadosBasicos: ${dadosBasicos},
fotobase: ${fotobase},
respondeuParq: ${respondeuParq},
mRespostaParQAluno: ${mRespostaParQAluno},
assinaturaDoAlunoParq: ${assinaturaDoAlunoParq},
devices: ${devices},
resultadoBioimpedancia: ${resultadoBioimpedancia},
manterAvaliacaoBioimpedanciaAluno: ${manterAvaliacaoBioimpedanciaAluno},
listaDiferencaDobras: ${listaDiferencaDobras},
mFotosAvaliacaoFisicaAtual: ${mFotosAvaliacaoFisicaAtual},
fotosAgrupadas: ${fotosAgrupadas},
fotoSelecionado: ${fotoSelecionado},
mGruposMusculares: ${mGruposMusculares},
mGraficoGruposTrabalhados: ${mGraficoGruposTrabalhados},
topSize: ${topSize},
bottomSize: ${bottomSize}
    ''';
  }
}
