// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorEdicaoiExercicio.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorEdicaoiExercicio on _ControladorEdicaoiExercicioBase, Store {
  late final _$seriesManterAtom = Atom(
      name: '_ControladorEdicaoiExercicioBase.seriesManter', context: context);

  @override
  ObservableList<Series?> get seriesManter {
    _$seriesManterAtom.reportRead();
    return super.seriesManter;
  }

  @override
  set seriesManter(ObservableList<Series?> value) {
    _$seriesManterAtom.reportWrite(value, super.seriesManter, () {
      super.seriesManter = value;
    });
  }

  late final _$serieAtom =
      Atom(name: '_ControladorEdicaoiExercicioBase.serie', context: context);

  @override
  Observable<Series> get serie {
    _$serieAtom.reportRead();
    return super.serie;
  }

  @override
  set serie(Observable<Series> value) {
    _$serieAtom.reportWrite(value, super.serie, () {
      super.serie = value;
    });
  }

  late final _$mudouMetodoAtom = Atom(
      name: '_ControladorEdicaoiExercicioBase.mudouMetodo', context: context);

  @override
  bool get mudouMetodo {
    _$mudouMetodoAtom.reportRead();
    return super.mudouMetodo;
  }

  @override
  set mudouMetodo(bool value) {
    _$mudouMetodoAtom.reportWrite(value, super.mudouMetodo, () {
      super.mudouMetodo = value;
    });
  }

  late final _$seriesEstaoEmptyAtom = Atom(
      name: '_ControladorEdicaoiExercicioBase.seriesEstaoEmpty',
      context: context);

  @override
  bool get seriesEstaoEmpty {
    _$seriesEstaoEmptyAtom.reportRead();
    return super.seriesEstaoEmpty;
  }

  @override
  set seriesEstaoEmpty(bool value) {
    _$seriesEstaoEmptyAtom.reportWrite(value, super.seriesEstaoEmpty, () {
      super.seriesEstaoEmpty = value;
    });
  }

  @override
  String toString() {
    return '''
seriesManter: ${seriesManter},
serie: ${serie},
mudouMetodo: ${mudouMetodo},
seriesEstaoEmpty: ${seriesEstaoEmpty}
    ''';
  }
}
