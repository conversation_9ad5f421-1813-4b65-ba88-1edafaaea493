// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorNotificacoesCrm.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorNotificacoesCrm on _ControladorNotificacoesCrmBase, Store {
  late final _$statusConsultaCrmAtom = Atom(
      name: '_ControladorNotificacoesCrmBase.statusConsultaCrm',
      context: context);

  @override
  ServiceStatus get statusConsultaCrm {
    _$statusConsultaCrmAtom.reportRead();
    return super.statusConsultaCrm;
  }

  @override
  set statusConsultaCrm(ServiceStatus value) {
    _$statusConsultaCrmAtom.reportWrite(value, super.statusConsultaCrm, () {
      super.statusConsultaCrm = value;
    });
  }

  late final _$mNotificacoesCrmAtom = Atom(
      name: '_ControladorNotificacoesCrmBase.mNotificacoesCrm',
      context: context);

  @override
  ObservableList<NotificacaoCRMAcademia> get mNotificacoesCrm {
    _$mNotificacoesCrmAtom.reportRead();
    return super.mNotificacoesCrm;
  }

  @override
  set mNotificacoesCrm(ObservableList<NotificacaoCRMAcademia> value) {
    _$mNotificacoesCrmAtom.reportWrite(value, super.mNotificacoesCrm, () {
      super.mNotificacoesCrm = value;
    });
  }

  late final _$mPushUsuarioAtom = Atom(
      name: '_ControladorNotificacoesCrmBase.mPushUsuario', context: context);

  @override
  List<PushUsuario> get mPushUsuario {
    _$mPushUsuarioAtom.reportRead();
    return super.mPushUsuario;
  }

  @override
  set mPushUsuario(List<PushUsuario> value) {
    _$mPushUsuarioAtom.reportWrite(value, super.mPushUsuario, () {
      super.mPushUsuario = value;
    });
  }

  late final _$listPushAtom =
      Atom(name: '_ControladorNotificacoesCrmBase.listPush', context: context);

  @override
  List<String> get listPush {
    _$listPushAtom.reportRead();
    return super.listPush;
  }

  @override
  set listPush(List<String> value) {
    _$listPushAtom.reportWrite(value, super.listPush, () {
      super.listPush = value;
    });
  }

  @override
  String toString() {
    return '''
statusConsultaCrm: ${statusConsultaCrm},
mNotificacoesCrm: ${mNotificacoesCrm},
mPushUsuario: ${mPushUsuario},
listPush: ${listPush}
    ''';
  }
}
