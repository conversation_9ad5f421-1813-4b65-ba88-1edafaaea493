import 'dart:async';

import 'package:app_treino/model/util/PickerTipoDeTimer.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:mobx/mobx.dart';
part 'ControladorTime.g.dart';

class ControladorTime = _ControladorTimeBase with _$ControladorTime;

abstract class _ControladorTimeBase with Store {
  @observable
  String tempoExibir = '';
  @observable
  String roudsExibir = '';
  @observable
  bool pausado = false;
  @observable
  bool descanso = false;
  @observable
  bool iniciouTimer = false;
  @observable
  num porcentagemCompletada = 0;
  @observable
  bool iniciouPreTimer = false;
  @observable
  num contadorInicio = 3;
  num? segundosTotais = 0, segundosPassou = 0, totalDerounds = 1;
  num? segundosPostar = 0, roundsPostar = 0;
  late Timer _timer;
  Timer? timer;
  DateTime? horaInicio;
  List<ClassTimeLine> timeLine = [];
  int posicaoTimeLine = 0;

  void criarTimeLine(ArgsSubTelaTimer dados, Function() acabou) {
    timeLine.clear();
    posicaoTimeLine = 0;
    segundosTotais = 0;
    segundosPassou = 0;
    segundosPostar = 0;
    tempoExibir = '';
    roudsExibir = '';
    totalDerounds = dados.rounds;
    // iniciouTimer = false;
    for (int i = 0; i < dados.rounds!; i++) {
      timeLine.add(ClassTimeLine(tipoTimer: dados.direcao ?? TipoTimer.Execucao, duracao: dados.duration, roundAtual: i + 1));
      segundosTotais = segundosTotais! + dados.duration!.inSeconds;
      if (dados.descanso != null) {
        timeLine.add(ClassTimeLine(tipoTimer: TipoTimer.Descanso, duracao: dados.descanso, roundAtual: i + 1));
        segundosTotais = segundosTotais! + dados.descanso!.inSeconds;
      }
    }
    comecarTempo(dados, acabou);
  }

  void stopTimer(Function()? sucesso) {
    // tempoExibir = "";
    // roudsExibir = "";
    pausado = false;
    iniciouTimer = false;
    // posicaoTimeLine = 0;
    // segundosTotais = 0;
    segundosPassou = segundosPassou! - 1;
    segundosPostar = segundosPassou! - 1;
    // contadorInicio = 3;
    // porcentagemCompletada = 0.0;
    timer?.cancel();
    sucesso?.call();
  }

  void limpar() {
    posicaoTimeLine = 0;
    segundosTotais = 0;

    contadorInicio = 3;
    porcentagemCompletada = 0.0;
    tempoExibir = '';
    roudsExibir = '';
  }

  num regresisoOuNao(num tempo, ClassTimeLine atual) {
    if (atual.tipoTimer == TipoTimer.Progressivo) {
      return atual.duracao!.inSeconds - tempo;
    }
    return tempo;
  }

  void startPreTimer(
    ArgsSubTelaTimer? dados,
    Function() callback,
  ) {
    const oneSec = const Duration(seconds: 1);
    if (!iniciouTimer) {
      iniciouPreTimer = true;
      _timer = new Timer.periodic(
        oneSec,
        (Timer timer) {
          if (contadorInicio < 1) {
            timer.cancel();
            _timer.cancel();
          } else {
            contadorInicio -= 1;
            if (contadorInicio == 1) {
              criarTimeLine(dados!, callback);
            }
          }
        },
      );
    } else {
      contadorInicio = 0;
    }
  }

  void comecarTempo(ArgsSubTelaTimer dados, Function() acabou) {
    var segundosTimeLineAtual = timeLine[posicaoTimeLine].duracao!.inSeconds;
    timer = Timer.periodic(const Duration(seconds: 1), (tempoTimer) {
      if (!pausado) {
        var atual = timeLine[posicaoTimeLine];
        descanso = atual.tipoTimer == TipoTimer.Descanso;
        int tempoTotal = (segundosTimeLineAtual).toInt();
        --segundosTimeLineAtual;
        tempoExibir = UtilDataHora.format(Duration(seconds: regresisoOuNao(tempoTotal, atual) as int));
        roudsExibir = atual.roundAtual.toString();
        roundsPostar = atual.roundAtual;
        segundosPassou = segundosPassou! + 1;
        segundosPostar = segundosPostar! + 1;
        iniciouPreTimer = false;
        iniciouTimer = true;
        porcentagemCompletada = segundosPassou! / segundosTotais!;
        if (tempoTotal == 0) {
          if (posicaoTimeLine < (timeLine.length - 1)) {
            posicaoTimeLine += 1;
            segundosTimeLineAtual = timeLine[posicaoTimeLine].duracao!.inSeconds;
            segundosPostar = segundosPostar! - 1;
          } else {
            segundosPostar = segundosPostar! - 1;
            timer!.cancel();
            contadorInicio = 3;
            iniciouPreTimer = false;
            iniciouTimer = false;
            acabou.call();
          }
        }
      }
    });
  }

  void limparDados() {
    timer?.cancel();
    horaInicio = null;
    contadorInicio = 3;
    iniciouTimer = false;
    iniciouPreTimer = false;
  }
}

class ArgsSubTelaTimer {
  Duration? duration, descanso;
  TipoTimer? direcao;
  num? rounds;

  ArgsSubTelaTimer({this.descanso, this.duration, this.rounds, this.direcao});
}