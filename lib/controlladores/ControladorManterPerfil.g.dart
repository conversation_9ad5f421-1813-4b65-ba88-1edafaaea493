// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorManterPerfil.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorManterPerfil on _ControladorManterPerfilBase, Store {
  late final _$mUsuarioManterAtom = Atom(
      name: '_ControladorManterPerfilBase.mUsuarioManter', context: context);

  @override
  DadosUsuario? get mUsuarioManter {
    _$mUsuarioManterAtom.reportRead();
    return super.mUsuarioManter;
  }

  @override
  set mUsuarioManter(DadosUsuario? value) {
    _$mUsuarioManterAtom.reportWrite(value, super.mUsuarioManter, () {
      super.mUsuarioManter = value;
    });
  }

  late final _$unitImageFotoAtom = Atom(
      name: '_ControladorManterPerfilBase.unitImageFoto', context: context);

  @override
  Uint8List? get unitImageFoto {
    _$unitImageFotoAtom.reportRead();
    return super.unitImageFoto;
  }

  @override
  set unitImageFoto(Uint8List? value) {
    _$unitImageFotoAtom.reportWrite(value, super.unitImageFoto, () {
      super.unitImageFoto = value;
    });
  }

  late final _$urlFotoPersonalAtom = Atom(
      name: '_ControladorManterPerfilBase.urlFotoPersonal', context: context);

  @override
  String? get urlFotoPersonal {
    _$urlFotoPersonalAtom.reportRead();
    return super.urlFotoPersonal;
  }

  @override
  set urlFotoPersonal(String? value) {
    _$urlFotoPersonalAtom.reportWrite(value, super.urlFotoPersonal, () {
      super.urlFotoPersonal = value;
    });
  }

  late final _$nomeAtom =
      Atom(name: '_ControladorManterPerfilBase.nome', context: context);

  @override
  String? get nome {
    _$nomeAtom.reportRead();
    return super.nome;
  }

  @override
  set nome(String? value) {
    _$nomeAtom.reportWrite(value, super.nome, () {
      super.nome = value;
    });
  }

  late final _$dataNascimentoAtom = Atom(
      name: '_ControladorManterPerfilBase.dataNascimento', context: context);

  @override
  String get dataNascimento {
    _$dataNascimentoAtom.reportRead();
    return super.dataNascimento;
  }

  @override
  set dataNascimento(String value) {
    _$dataNascimentoAtom.reportWrite(value, super.dataNascimento, () {
      super.dataNascimento = value;
    });
  }

  late final _$sexoAtom =
      Atom(name: '_ControladorManterPerfilBase.sexo', context: context);

  @override
  String get sexo {
    _$sexoAtom.reportRead();
    return super.sexo;
  }

  @override
  set sexo(String value) {
    _$sexoAtom.reportWrite(value, super.sexo, () {
      super.sexo = value;
    });
  }

  late final _$celularAtom =
      Atom(name: '_ControladorManterPerfilBase.celular', context: context);

  @override
  String? get celular {
    _$celularAtom.reportRead();
    return super.celular;
  }

  @override
  set celular(String? value) {
    _$celularAtom.reportWrite(value, super.celular, () {
      super.celular = value;
    });
  }

  late final _$emailAtom =
      Atom(name: '_ControladorManterPerfilBase.email', context: context);

  @override
  String? get email {
    _$emailAtom.reportRead();
    return super.email;
  }

  @override
  set email(String? value) {
    _$emailAtom.reportWrite(value, super.email, () {
      super.email = value;
    });
  }

  late final _$fotoAtom =
      Atom(name: '_ControladorManterPerfilBase.foto', context: context);

  @override
  String? get foto {
    _$fotoAtom.reportRead();
    return super.foto;
  }

  @override
  set foto(String? value) {
    _$fotoAtom.reportWrite(value, super.foto, () {
      super.foto = value;
    });
  }

  late final _$codigoAtom =
      Atom(name: '_ControladorManterPerfilBase.codigo', context: context);

  @override
  int? get codigo {
    _$codigoAtom.reportRead();
    return super.codigo;
  }

  @override
  set codigo(int? value) {
    _$codigoAtom.reportWrite(value, super.codigo, () {
      super.codigo = value;
    });
  }

  late final _$situacaoAtom =
      Atom(name: '_ControladorManterPerfilBase.situacao', context: context);

  @override
  String? get situacao {
    _$situacaoAtom.reportRead();
    return super.situacao;
  }

  @override
  set situacao(String? value) {
    _$situacaoAtom.reportWrite(value, super.situacao, () {
      super.situacao = value;
    });
  }

  @override
  String toString() {
    return '''
mUsuarioManter: ${mUsuarioManter},
unitImageFoto: ${unitImageFoto},
urlFotoPersonal: ${urlFotoPersonal},
nome: ${nome},
dataNascimento: ${dataNascimento},
sexo: ${sexo},
celular: ${celular},
email: ${email},
foto: ${foto},
codigo: ${codigo},
situacao: ${situacao}
    ''';
  }
}
