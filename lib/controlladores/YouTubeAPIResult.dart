// To parse this JSON data, do
//
//     final youTubeApiResult = youTubeApiResultFromJson(jsonString);

import 'dart:convert';

YouTubeApiResult youTubeApiResultFromJson(String str) => YouTubeApiResult.fromJson(const JsonCodec().decode(str));

String youTubeApiResultToJson(YouTubeApiResult data) => json.encode(data.toJson());

class YouTubeApiResult {
  List<Item>? items;

  YouTubeApiResult({
    this.items,
  });

  factory YouTubeApiResult.fromJson(Map<String, dynamic> json) => YouTubeApiResult(
        items: List<Item>.from(json['items'].map((x) => Item.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        'items': List<dynamic>.from(items!.map((x) => x.toJson())),
      };
}

class Item {
  String? kind;
  String? etag;
  String? id;
  ContentDetails? contentDetails;

  Item({
    this.kind,
    this.etag,
    this.id,
    this.contentDetails,
  });

  factory Item.fromJson(Map<String, dynamic> json) => Item(
        kind: json['kind'],
        etag: json['etag'],
        id: json['id'],
        contentDetails: ContentDetails.fromJson(json['contentDetails']),
      );

  Map<String, dynamic> toJson() => {
        'kind': kind,
        'etag': etag,
        'id': id,
        'contentDetails': contentDetails!.toJson(),
      };
}

class ContentDetails {
  String? duration;
  String? dimension;
  String? definition;
  String? caption;
  bool? licensedContent;
  String? projection;

  ContentDetails({
    this.duration,
    this.dimension,
    this.definition,
    this.caption,
    this.licensedContent,
    this.projection,
  });

  factory ContentDetails.fromJson(Map<String, dynamic> json) => ContentDetails(
        duration: json['duration'],
        dimension: json['dimension'],
        definition: json['definition'],
        caption: json['caption'],
        licensedContent: json['licensedContent'],
        projection: json['projection'],
      );

  Map<String, dynamic> toJson() => {
        'duration': duration,
        'dimension': dimension,
        'definition': definition,
        'caption': caption,
        'licensedContent': licensedContent,
        'projection': projection,
      };
}
