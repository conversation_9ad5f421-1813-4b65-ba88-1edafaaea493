import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoDeTreino.dart';
import 'package:app_treino/model/personal/ObjetivoAluno.dart';
import 'package:app_treino/model/personal/ProgramaFicha.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorPlanoDeTreino.g.dart';

class ControladorPlanoDeTreino = _ControladorPlanoDeTreinoBase with _$ControladorPlanoDeTreino;

abstract class _ControladorPlanoDeTreinoBase with Store {
  @observable
  ProgramaDeTreino? programaManter;

  @observable
  int testeDataFinal = 0;

  final _mService = GetIt.I.get<PersonalService>();
  final _controllerCliente = GetIt.I.get<ControladorCliente>();
  var _aluno = GetIt.I.get<ControladorPrescricaoDeTreino>().alunoVisualizar;
  final _controlPrescricao = GetIt.I.get<ControladorPrescricaoDeTreino>();

  @observable
  var mudouAlgo = false;

  void setPlanoDeTreino(ProgramaDeTreino? plano) {
    programaManter = plano != null ? ProgramaDeTreino.fromJson(plano.toJson()) : ProgramaDeTreino();
  }

  void setObjetivo(ObjetivoAluno add) {
    ObjetivosPrograma objetivo = ObjetivosPrograma(objetivo: add.codigo, nome: add.nome, codigo: add.codigo, programa: programaManter!.codigo ?? 0);
    if (programaManter!.objetivosPrograma == null) programaManter!.objetivosPrograma = [];
    if (programaManter!.objetivosPrograma!.where((element) => element.objetivo == objetivo.objetivo).isEmpty) {
      programaManter!.objetivosPrograma!.add(objetivo);
    } else {
      programaManter!.objetivosPrograma!.removeWhere((element) => element.objetivo == objetivo.objetivo);
    }
    this.mudouAlgo = !mudouAlgo;
  }

  void calculaDatasEAulasPrevistas() {
    // Aulas prvesitos
    var dias = UtilDataHora.daysBetween(inicioMilis: programaManter!.dataTerminoPrevisto, fimMilis: programaManter!.dataInicio);
    if (programaManter == null) {
      programaManter!.totalAulasPrevistas = 0;
    } else {
      programaManter!.totalAulasPrevistas = (dias.toDouble() / 7 * (programaManter?.diasPorSemana ?? 0)).toInt();
    }
    this.mudouAlgo = !mudouAlgo;
  }

  void calcularDataFinal(){
    var dias = (programaManter!.totalAulasPrevistas! / (programaManter?.diasPorSemana ?? 0).toInt() * 7).toInt();
    if(programaManter == null){
      programaManter!.dataTerminoPrevisto = 0;
    }else{
      programaManter!.dataTerminoPrevisto = programaManter!.dataInicio! + (dias * 86400000);
      this.mudouAlgo = !mudouAlgo;
    }
  }

  void manterPlanoDeTreino({Function()? sucesso, Function(String? mensagem)? falha}) {
    _mService.manterPrograma(programaManter!.toJsonPredefindo()).then((value) {
      _controlPrescricao.consultarProgramaDeTreinoDoAluno(sucesso: sucesso, falha: falha);
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  void renovarPlanoDeTreino({Function()? sucesso, Function(String? mensagem)? falha}) {
    _mService.renovarPrograma(_controllerCliente.mUsuarioLogado!.username!, _controllerCliente.mUsuarioLogado!.codEmpresa!, _controlPrescricao.programaMaisRecenteAlunoExibir().id!).then((value) {
      sucesso?.call();
      // _controlPrescricao.consultarProgramaDeTreinoDoAluno(sucesso: sucesso, falha: falha);
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  void consultarPlanoBaseDeTreino({Function()? sucesso, Function(String? mensagem)? falha}) {
    _aluno = GetIt.I.get<ControladorPrescricaoDeTreino>().alunoVisualizar;
    _mService.consultarProgramaBase(_aluno!.codigo!, _controllerCliente.mUsuarioLogado!.username!).then((value) {
      value.nome = 'PLANO A';
      programaManter = value;
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  limparTudo() {
    programaManter = null;
    mudouAlgo = false;
  }
}
