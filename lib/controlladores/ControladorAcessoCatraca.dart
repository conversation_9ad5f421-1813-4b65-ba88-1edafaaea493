import 'package:app_treino/ServiceProvider/authServices/ServiceAuth.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/registrar_uso_app.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';

part 'ControladorAcessoCatraca.g.dart';

class ControladorAcessoCatraca = _ControladorAcessoCatracaBase with _$ControladorAcessoCatraca;

abstract class _ControladorAcessoCatracaBase with Store {
  @observable
  List<LocalDeAcesso> mLocaisAcessoExibir = [];

  @observable
  AcessoRegistrado registroAcesso = AcessoRegistrado();

  final _mServiceAuth = GetIt.I.get<ServiceAuth>();

  void consultarLocalAcesso({required num codEmpresa}){
    mLocaisAcessoExibir.clear();
    _mServiceAuth.listarPontosDeAcesso(codEmpresa).then((value) {
      mLocaisAcessoExibir = [...value.data!.where((x) => x.dadosDoAcesso!.ativa ?? false).toList()];
    }).catchError((onError){
      //print(onError.toString());
    });
  }

  Future<void> registrarAcesso({required String chave, required num empresa, required num codigoCliente, required String dataAcesso})async {
    _mServiceAuth.registrarAcesso(chave, 'registrarAcesso', empresa, codigoCliente, dataAcesso, 'DA_ENTRADA', 'APLICATIVO', 'RV_LIBACESSOAUTORIZADO', 1, 0, true).then((value) {
    registroAcesso = value;
    }).catchError((onError){});
  }

}