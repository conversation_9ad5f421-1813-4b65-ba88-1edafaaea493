import 'dart:collection';
import 'dart:developer';
import 'package:app_treino/ServiceProvider/AulaTurmaService.dart';
import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorContratoUsuario.dart';
import 'package:app_treino/controlladores/ControladorNotificacoes.dart';
import 'package:app_treino/controlladores/ControladorSplash.dart';
import 'package:app_treino/controlladores/ControladorTreinoAluno.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/aulaTurma/AlunoFilaDeEspera.dart';
import 'package:app_treino/model/aulaTurma/AulaTurma.dart';
import 'package:app_treino/model/contrato/ContratoUsuario.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/EmpresaRede.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/Usuario.dart';
import 'package:app_treino/model/doClienteApp/historico_aula.dart';
import 'package:app_treino/model/personal/ProgramaFicha.dart';
import 'package:app_treino/model/planner/RefeicaoPlanner.dart';
import 'package:app_treino/model/treinoAluno/ProgramadeTreino.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:app_treino/util/debug_utils.dart';
import 'package:diacritic/diacritic.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:shared_preferences/shared_preferences.dart';
part 'ControladorAulaTurma.g.dart';

class ControladorAulaTurma = _ControladorAulaTurmaBase with _$ControladorAulaTurma;

abstract class _ControladorAulaTurmaBase with Store {
  GlobalKey keyTopoListaAulasTurmas = GlobalKey();
  final _controladorCliente = GetIt.I.get<ControladorCliente>();
  final _controladorContrato = GetIt.I.get<ControladorContratoUsuario>();
  final _controladorApp = GetIt.I.get<ControladorApp>();
  final _prescrService = GetIt.I.get<PersonalService>();
  final _controladorPerfil = GetIt.I.get<ControladorSplash>();

  bool professorViuComoConfirmarQr = false;

  @observable
  ServiceStatus consultaAulaTurmas = ServiceStatus.Waiting;
  
  @observable
  bool estaPesquisando = false;
  
  @observable
  bool aulaAgendada = false;

  @observable
  bool aulaDesmarcada = false;

  bool saldoDoAlunoEcredito = false;

  @observable
  ServiceStatus pesquisaDeAulasTurmas = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultarHistoricoAulas = ServiceStatus.Waiting;

  @observable
  ServiceStatus consultarAlunosTurmaAula = ServiceStatus.Waiting;

  @observable
  ServiceStatus consultarAlunosNaAulaHistorico = ServiceStatus.Waiting;

  @observable
  ServiceStatus consultarSaldoAluno = ServiceStatus.Waiting;

  ServiceStatus statusConsultaProgramaAluno = ServiceStatus.Waiting;

  @observable
  ProgramaDeTreino? mProgramaAluno;

  @observable
  bool alunoEmFerias = false;
  bool alunoAtestado = false;

  @observable
  bool alunoTrancado = false;

  List<HistoricoAula> historicoAulas = [];
  
  @observable
  ContratoUsuario? contratoMarcacaoAula;

  @observable
  String termoBuscaUnidade = '';
  List<EmpresaRede> empresasRedeFiltradas = [];
  @observable
  EmpresaRede? empresaSelecionada;

  String? textopesquisa;

  @observable
  List<AlunoNaAulaTurma> mListaSomenteAlunosAula = [];
  @observable
  List<AlunoNaAulaTurma> mListaSomenteAlunosAulaBKP = [];

  bool filtrarAulas(AulaTurma element) => (removeDiacritics(element.nome!.toLowerCase()).contains(removeDiacritics(textopesquisa!.toLowerCase())) ||
      removeDiacritics(element.modalidade!.toLowerCase()).contains(removeDiacritics(textopesquisa!.toLowerCase())));

  Empresa? get empresaSelecionadaColaborador {
    if ((_controladorCliente.mUsuarioLogado!.empresas?.isNotEmpty ?? false) && ((_controladorCliente.mUsuarioLogado!.empresas?.length ?? 0) > 1)) {
      return _controladorCliente.mUsuarioLogado!.empresas!.firstWhere((element) => element.codigo == _controladorCliente.mUsuarioLogado!.codEmpresa, orElse: () {
        return Empresa();
      });
    } else {
      return null;
    }
  }

  void filtrarUnidadesPorTexto(String termo) {
    empresasRedeFiltradas.clear();
    if (!termo.isEmpty) {
      empresasRedeFiltradas.addAll(_controladorApp.empresasRede.where((empresa) => empresa.nome!.contains((removeDiacritics(termo)).toUpperCase())).toList());
    }
  }

  DateTime? diaConsultarAulasTurma;

  String get diaConsultar {
    if (diaConsultarAulasTurma != null) {
      return UtilDataHora.getDiaMesAno(dateTime: diaConsultarAulasTurma);
    } else {
      return UtilDataHora.getDiaMesAno(dateTime: DateTime.now());
    }
  }

  num get getCodEmpresa {
    if(empresaSelecionada != null){
      return empresaSelecionada!.codigo!;
    } else if ((_mControladorUsuario.mUsuarioLogado!.codEmpresa ?? 0) > 0) {
      return _mControladorUsuario.mUsuarioLogado!.codEmpresa ?? 1;
    } 
    else{
      return 1;
    }
  }

  bool get getPermissaoExcluirAlunoAula {
    var possuiPermissao = true;
    if(_controladorPerfil.mPerfilUsuario != null){
      for (final element in _controladorPerfil.mPerfilUsuario!.perfilUsuario!.funcionalidades!) {
      if(element.nome! == 'excluir_aluno'){
        if(element.possuiFuncionalidade!){
          possuiPermissao = true;
        } else{
          possuiPermissao = false;
        }
      }
    }
    } else{
      return possuiPermissao = true;
    }
    return possuiPermissao;
  }

  bool get getPermissaoAdicionarAlunoAula {
    var possuiPermissao = true;
    if(_controladorPerfil.mPerfilUsuario != null){
      for (final element in _controladorPerfil.mPerfilUsuario!.perfilUsuario!.funcionalidades!) {
      if(element.nome! == 'inserir_aluno'){
        if(element.possuiFuncionalidade!){
          possuiPermissao = true;
        } else{
          possuiPermissao = false;
        }
      }
    }
    } else{
      return possuiPermissao = true;
    }
    return possuiPermissao;
  }
  

  @observable
  num saldoParaAulasTurmasReposicao = 0;
  @observable
  num saldoParaAulasTurmasCredito = 0;

  ExpandableController controllerExpandable = new ExpandableController();
  AulaTurma? mAulasTurmasExpandido;
  List<AulaTurma> mAulasETurmas = [];
  List<AulaTurma> mAulasETurmasDoDia = [];
  List<AulaTurma> mAulasETurmasAgendadas = [];
  List<AulaTurma> mAulasETurmasAgendadasDoDia = [];
  List<AulaTurma> mTurmasAgendadas = [];
  List<AlunoNaAulaTurma> mListaAlunosNaAula = [];
  List<AlunoNaAulaTurma> mListaAlunosNaAulaSelecionadosParaConfirmaPresenca = [];
  List<AlunoNaAulaTurma> mListaAlunosNaAulaBackup = [];
  LinkedHashMap<DateTime, String> mAulasTurmasFavoritas =
      LinkedHashMap<DateTime, String>();

  final _mServiceAulasTurmas = GetIt.I.get<AulaTurmaService>();
  final _mControladorUsuario = GetIt.I.get<ControladorCliente>();

  List<AulaTurma?> mAulasETurmasAgendadasPorData(DateTime? data) {
    String diaConsultar = UtilDataHora.getDiaMesAno(dateTime: data);
    //   List<AulaTurma> aulasFiltro = [];
    //   var agendadasRemover = mAulasETurmas.where((element) => element.alunoEstaNaAula).toList();
    //   mAulasETurmasAgendadas.addAll(agendadasRemover);

    return mAulasETurmasAgendadas.where((element) => element.alunoEstaNaAula! && element.dia == diaConsultar).toList();
  }

  String obterInfoQr(AulaTurma aulaTurma) {
    return '${(empresaSelecionada?.chave ?? GetIt.I.get<ControladorApp>().chave)};;${aulaTurma.codigo};;${aulaTurma.dia};;${_mControladorUsuario.mUsuarioLogado!.matricula}';
  }

  String get diaConsultarString {
    if (diaConsultarAulasTurma == null) return UtilDataHora.getDiaMes(dateTime: DateTime.now());
    return UtilDataHora.getDiaMes(dateTime: diaConsultarAulasTurma);
  }

  bool aulaTurmaLotada(AulaTurma aulaTurma) {
    return aulaTurma.capacidade == aulaTurma.ocupacao;
  }

  bool isAulaCheia(AulaTurma? aulaTurma) {
    return aulaTurma!.capacidade! <= aulaTurma.ocupacao!;
  }

  bool? exibirBotoesMarcarDesmarcar(AulaTurma? aulaTurma) {
    if (_mControladorUsuario.isUsuarioColaborador) {
      return true;
    }
    if (aulaTurma!.coletiva!) {
      return GetIt.I.get<ControladorApp>().getConfigWebTreino('MODULO_AULAS_ABA_AULAS_COLETIVAS');
    } else {
      return GetIt.I.get<ControladorApp>().getConfigWebTreino('MODULO_AULAS_ABA_TURMAS');
    }
  }

  bool aulaNaoEstaCheiaEFilaDesabilitada(AulaTurma aulaTurma) {
    return !((aulaTurma.capacidade ?? 0) <= (aulaTurma.ocupacao ?? 0) && !GetIt.I.get<ControladorApp>().getConfigWebTreino('HABILITAR_FILA_ESPERA'));
  }

  bool aulaNaoETurmaEEstaCheia(AulaTurma aulaTurma) {
    return !(aulaTurma.coletiva! && (aulaTurma.ocupacao ?? 0) >= (aulaTurma.capacidade ?? 0));
  }


  @observable
  ObservableList<AulaTurma>  todasAsAulas = ObservableList<AulaTurma> ();

  @observable
  ObservableList<AulaTurma>  todasAsAulasBackup = ObservableList<AulaTurma> ();

  @observable
  SaldoAulaColetiva? saldoAulaColetiva;

  aulaFiltradaPorNome(String nome) {
    nome = removeDiacritics(nome).toLowerCase();
    consultaAulaTurmas = ServiceStatus.Waiting;
    try {
      todasAsAulasBackup.clear();
      todasAsAulas.clear();
      todasAsAulasBackup.addAll(aulaTurmaAcontecendo);
      todasAsAulasBackup.addAll(aulaTurmaFuturasDia);
      todasAsAulasBackup.addAll(aulasProfessorJaFinalizadas);
      todasAsAulas.addAll(todasAsAulasBackup.where((element) => removeDiacritics(element.nome ?? '').toLowerCase().contains(nome.toLowerCase())).toList());
    } catch (e) {}
    consultaAulaTurmas = ServiceStatus.Done;
  }

  List<AulaTurma> get aulaTurmaAcontecendo {
    if (diaConsultarString != UtilDataHora.getDiaMes(dateTime: DateTime.now())) return [];
    // Novo jeito e lavamos nóz
    return mAulasETurmas.where((aulaTurma) {
      var rangeDaAula = DurationRange(
          start: Duration(hours: num.parse(aulaTurma.inicio!.split(':').first) as int, minutes: num.parse(aulaTurma.inicio!.split(':').last) as int),
          end: Duration(hours: num.parse(aulaTurma.fim!.split(':').first) as int, minutes: num.parse(aulaTurma.fim!.split(':').last) as int));
      var durationAgora = Duration(hours: DateTime.now().hour, minutes: DateTime.now().minute);
      return rangeDaAula.estaNoRange(durationAgora);
    }).toList();
  }

  List<AulaTurma> get aulaTurmaFuturasDia {
    return mAulasETurmas.where((aula) {
      if ('316_07/05/2021' == aula.codDia) {
        //var debug = UtilDataHora.parseStringToDate("${aula.dia} ${aula.inicio}",minutos: true)
      }
      return getIncioDaAulaEmDate(aula)!.isAfter(DateTime.now()) && aulaTurmaAcontecendo.where((agr) => agr.codigo == aula.codigo).isEmpty;
    }).toList();
  }

  List<AulaTurma> get aulasProfessorJaFinalizadas {
   if (diaConsultarString != UtilDataHora.getDiaMes(dateTime: DateTime.now())) return [];
    return mAulasETurmas.where((aula) {
      return DateTime.now().isAfter(getFimDaAulaEmDate(aula)!);
    }).toList();
  }

  DateTime? getFimDaAulaEmDate(AulaTurma aula) => UtilDataHora.parseStringToDate('${UtilDataHora.getDiaMesAno(dataMilis: aula.diaDate as int?)} ${aula.fim}', minutos: true);
  DateTime? getIncioDaAulaEmDate(AulaTurma aula) => UtilDataHora.parseStringToDate('${aula.dia} ${aula.inicio}', minutos: true);

  void consultarSaldoAlunoOnline({Function()? carregando, Function(num saldo)? sucesso, Function(String)? falha}) {
    carregando?.call();
    _mServiceAulasTurmas.consultarSaldoAulasColetivas(matricula: num.tryParse(_mControladorUsuario.mUsuarioLogado!.matricula!)!).then((value) {
      saldoAulaColetiva = value;
    },).catchError((onError) {
    });
    _mServiceAulasTurmas.consultarSaldoDoUsuario(matricula: num.tryParse(_mControladorUsuario.mUsuarioLogado!.matricula!)!, idContrato: codigoContrato()).then((value) {
      log('Consultei o saldo do aluno');
      saldoParaAulasTurmasReposicao = value.isEmpty ? 0 :int.parse(value['saldoTurma(reposiçoes)']!);
      saldoParaAulasTurmasCredito = value.isEmpty ? 0 :int.parse(value['creditos']! );
      alunoEmFerias = false;
      alunoTrancado = false;
      alunoAtestado = false;
      consultarSaldoAluno = ServiceStatus.Done;
      sucesso?.call(saldoParaAulasTurmasReposicao);
    }).catchError((onError) {
      consultarSaldoAluno = ServiceStatus.Error;
      if (onError?.message?.toString().toLowerCase().contains('aluno em férias') ?? false) {
        alunoEmFerias = true;
        consultaAulaTurmas = ServiceStatus.Done;
      } else if (onError?.message?.toString().toLowerCase().contains('atestado') ?? false) {
        alunoAtestado = true;
        consultaAulaTurmas = ServiceStatus.Done;
      } else if (onError?.message?.toString().toLowerCase().contains('trancado') ?? false) {
        alunoTrancado = true;
        consultaAulaTurmas = ServiceStatus.Done;
      } else {
         consultaAulaTurmas = ServiceStatus.Done;
      }
    });
  }
   void consultarAlunosDaAulaHistorico(HistoricoAula historicoAula, {Function()? carregando, Function(AlunoNaAulaTurma? dadosAluno)? sucesso, Function(String)? falha}) {
    consultarAlunosNaAulaHistorico = ServiceStatus.Waiting;
    var consulta;
    if (empresaSelecionada != null && empresaSelecionada!.urlTreino != null/* empresaSelecionada!.codigo != getCodEmpresa */) {
      consulta = [ _mServiceAulasTurmas.consultarAlunosColetivaURLpersonalizada(urlTreino: empresaSelecionada!.urlTreino!, chaveEmpresa: empresaSelecionada!.chave!, codigoAula: historicoAula.codigoHorarioTurma!, dia: UtilDataHora.converterFormatoDatasInvertidas(historicoAula.dia ?? ''))];
    } else {
      consulta = [ _mServiceAulasTurmas.consultarAlunosColetiva(historicoAula.codigoHorarioTurma!, UtilDataHora.converterFormatoDatasInvertidas(historicoAula.dia ?? '')), ];
    }
    Future.wait(consulta).then((value) {
      value  = value[0];
      AlunoNaAulaTurma? aluno = value.where((element) =>  int.tryParse(_mControladorUsuario.mUsuarioLogado!.matricula!) == int.tryParse(element.matricula)).firstOrNull;
      consultarAlunosNaAulaHistorico = value.isEmpty ? ServiceStatus.Empty : ServiceStatus.Done;
      sucesso?.call(aluno);
    }).catchError((onError) {
      falha?.call('Houve um erro ao consultar quem irá participar.');
      consultarAlunosNaAulaHistorico = ServiceStatus.Error;
    });
  }

  void consultarAlunosDaAula(AulaTurma aulaTurma, {Function()? carregando, Function()? sucesso, Function(String)? falha}) {
    DebugUtils.debugLog('🔍 [AULA] Iniciando consultarAlunosDaAula - codigo: ${aulaTurma.codigo}, dia: ${aulaTurma.dia}');
    consultarAlunosTurmaAula = ServiceStatus.Waiting;
    var consulta;
    if (empresaSelecionada != null && empresaSelecionada!.urlTreino != null/* empresaSelecionada!.codigo != getCodEmpresa */) {
      DebugUtils.debugLog('🌐 [AULA] Usando URL personalizada: ${empresaSelecionada!.urlTreino}');
      consulta = [ _mServiceAulasTurmas.consultarAlunosColetivaURLpersonalizada(urlTreino: empresaSelecionada!.urlTreino!, chaveEmpresa: empresaSelecionada!.chave!, codigoAula: aulaTurma.codigo!, dia: (aulaTurma.dia ?? ''))];
    } else {
      final codEmpresa = getCodEmpresa;
      DebugUtils.debugLog('🏢 [AULA] Usando URLs padrão - getCodEmpresa: $codEmpresa');
      DebugUtils.debugLog('🏢 [AULA] Parâmetros: codigoAula=${aulaTurma.codigo}, dia=${aulaTurma.dia}, empresa=$codEmpresa');
      consulta = [ _mServiceAulasTurmas.consultarAlunosColetiva(aulaTurma.codigo!, (aulaTurma.dia ?? '')), _mServiceAulasTurmas.consultarAlunosTurma(aulaTurma.codigo!, (aulaTurma.dia ?? ''), codEmpresa)];
    }
    Future.wait(consulta).then((value) {
      _processarAlunosConsultados(value, aulaTurma, sucesso);
    }).catchError((onError) {
      // Se apenas consultarAlunosTurma falhar, tenta continuar só com consultarAlunosColetiva
      if (empresaSelecionada == null || empresaSelecionada!.urlTreino == null) {
        DebugUtils.debugLog('🔄 [AULA] Tentando fallback apenas com consultarAlunosColetiva...');
        _mServiceAulasTurmas.consultarAlunosColetiva(aulaTurma.codigo!, (aulaTurma.dia ?? '')).then((value) {
          DebugUtils.debugLog('✅ [AULA] Fallback bem-sucedido, processando ${value.length} alunos');
          _processarAlunosConsultados([value], aulaTurma, sucesso);
        }).catchError((secondError) {
          falha?.call('Houve um erro ao consultar quem irá participar.');
          consultarAlunosTurmaAula = ServiceStatus.Error;
        });
      } else {
        falha?.call('Houve um erro ao consultar quem irá participar.');
        consultarAlunosTurmaAula = ServiceStatus.Error;
      }
    });
  }

  void _processarAlunosConsultados(List<dynamic> value, AulaTurma aulaTurma, Function()? sucesso) {
    mListaAlunosNaAula.clear();
    mListaAlunosNaAulaBackup.clear();

    final Set<num> ids = {};
    final Set<String> visitantes = {};
    List<AlunoNaAulaTurma> listAuxiliar = [];

    for (final listaAlunos in value) {
      for (final aluno in listaAlunos) {
        aluno.foto ??= aluno.cliente?.urlFoto;

        final String? matriculaStr = aluno.matricula?.toString().trim();
        final bool temMatricula = matriculaStr != null && matriculaStr.isNotEmpty;

        if (temMatricula) {
          final num? matNum = num.tryParse(matriculaStr);
          if (matNum != null && !ids.contains(matNum)) {
            listAuxiliar.add(aluno);
            mListaAlunosNaAulaBackup.add(aluno);
            ids.add(matNum);
          }
        } else if ((aluno.vinculoComAula ?? '').toUpperCase() == 'AULA_EXPERIMENTAL') {
          // Visitante: gerar uma chave estável usando o máximo de identificadores disponíveis
          final String chaveVisitante = [
            'AULA_EXPERIMENTAL',
            (aluno.cliente?.codUsuario?.toString() ?? ''),
            (aluno.cliente?.codigo?.toString() ?? ''),
            (aluno.cliente?.codigoPessoa?.toString() ?? ''),
            (aluno.nome ?? '').trim().toLowerCase(),
            (aluno.dataNascimento?.toIso8601String() ?? ''),
            (aluno.telefone ?? const <String>[]).join(',')
          ].join('|');

          if (!visitantes.contains(chaveVisitante)) {
            listAuxiliar.add(aluno);
            mListaAlunosNaAulaBackup.add(aluno);
            visitantes.add(chaveVisitante);
          }
        }
      }
    }

    mListaAlunosNaAula.addAll(listAuxiliar);
    var sizeList = mListaAlunosNaAula.length;
    for (var i = 0; i < aulaTurma.capacidade! - sizeList; i++) {
      mListaAlunosNaAula.add(AlunoNaAulaTurma());
      mListaAlunosNaAulaBackup.add(AlunoNaAulaTurma());
    }
    consultarAlunosTurmaAula = mListaAlunosNaAula.isEmpty ? ServiceStatus.Empty : ServiceStatus.Done;
    sucesso?.call();
  }


  void pesquisarAlunoAula({required String termo}) {    
    consultarAlunosTurmaAula = ServiceStatus.Waiting;
    mListaAlunosNaAula.clear();
    if (termo.isNotEmpty) {
      var aux = mListaAlunosNaAulaBackup.where((element) => removeDiacritics((element.nome ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())) || removeDiacritics((element.matricula ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())));
      mListaAlunosNaAula.addAll(aux);
      consultarAlunosTurmaAula = mListaAlunosNaAula.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    } else {
      mListaAlunosNaAula.addAll(mListaAlunosNaAulaBackup);
      consultarAlunosTurmaAula = ServiceStatus.Done;
    }
  }

  @observable
  AulaTurmaSimplificado? mAulaTurmaSimplificado;
  @observable
  Map<String, String>? retornoErro = {};

  void desmarcarAlunoAulaTurma(AulaTurma aulaTurma,{Function()? carregando, Function()? sucesso, Function(String)? falha, AlunoNaAulaTurma? aluno, String? justificativa}){
    consultaAulaTurmas = ServiceStatus.Waiting;
    mAulaTurmaSimplificado = null;
    analytic(EventosKey.aulas_desmarcarAula);
    carregando?.call();
    num? matricula = num.parse(aluno?.matricula ?? '-1');
    if (matricula == '-1') matricula = null;
    _mServiceAulasTurmas.desmarcarPresencaAulaTurma(aulaTurma.codigo.toString(), _mControladorUsuario.mUsuarioLogado!.codEmpresa!,  num.parse(UtilDataHora.getDaTaAnoMesDia(dataMilis: aulaTurma.diaDate!.toInt()).replaceAll('-', '').toString()), 
    matricula ?? num.parse(_mControladorUsuario.mUsuarioLogado!.matricula.toString()), 
    justificativa!).then((value){
      mAulaTurmaSimplificado = value;
      consultaAulaTurmas = ServiceStatus.Waiting;
      mAulasETurmasAgendadas.removeWhere((element) => element.codigo == aulaTurma.codigo);
      mAulasETurmasAgendadasDoDia.removeWhere((element) => element.codigo == aulaTurma.codigo);
      aulaTurma.alunoEstaNaAula = false;
      aulaTurma.ocupacao = aulaTurma.ocupacao! - 1;
      if (aulaTurma.ocupacao! < 0) aulaTurma.ocupacao = 0;
      if (mAulasETurmas.any((a) => aulaTurma.codDia == a.codDia)) {
        mAulasETurmas[mAulasETurmas.indexWhere((a) => aulaTurma.codDia == a.codDia)] = aulaTurma;
      } else {
        mAulasETurmas.add(aulaTurma);
      }
      if (mAulasETurmasDoDia.any((a) => aulaTurma.codDia == a.codDia)) {
        mAulasETurmasDoDia[mAulasETurmasDoDia.indexWhere((a) => aulaTurma.codDia == a.codDia)] = aulaTurma;
      } else {
        mAulasETurmasDoDia.add(aulaTurma);
      }
      mAulasETurmas.sort((aulaA, aulaB) => aulaA.diaDate!.compareTo(aulaB.diaDate!));
      mAulasETurmasDoDia.sort((aulaA, aulaB) => aulaA.diaDate!.compareTo(aulaB.diaDate!));

    if (!_mControladorUsuario.isUsuarioColaborador) {
        GetIt.I.get<ControladorNotificacoes>().cancelarLembretePorId(aulaTurma.codigo as int);
      } else {
        consultaAulaTurmas = ServiceStatus.Done;
        sucesso?.call();
        return;
      }
      consultarSaldoAlunoOnline(
        sucesso: (saldo) {
          consultaAulaTurmas = ServiceStatus.Done;
          sucesso?.call();
        },
        falha: (v) {
          consultaAulaTurmas = ServiceStatus.Done;
          sucesso?.call();
        },
      );
      sucesso?.call();
    }).catchError((onError){
      if (onError.message.toString().contains('Future')) {
        falha?.call(localizedString('unable_to_process_this'));
        consultaAulaTurmas = ServiceStatus.Error;
      } else if(onError.message.toString().contains('soPodeDesmarcarAulaAte') || onError.error.toString().contains('soPodeDesmarcarAulaAte')){
        falha?.call(localizedString('nao_pode_desmarcar_mais', args: ['Só pode desmarcar aula até ${onError.message != null ? onError.message.toString().substring(23) : onError.error.toString().split(';').last}']));
        consultaAulaTurmas = ServiceStatus.Error;
      } else if(onError.message.toString().contains('409')){
        falha?.call(localizedString('nao_pode_desmarcar_mais', args: ['Só pode desmarcar aula até ${onError.response.data['meta']['message'].toString().substring(23)}']));
        consultaAulaTurmas = ServiceStatus.Error;
      } else {
        falha?.call(localizedString('unable_to_check_out_this_class', args: [onError.message.toString()]));
        consultaAulaTurmas = ServiceStatus.Error;
      }
    });
    
  }


  void desmarcarPresencaNaAulaOuTurma(AulaTurma aulaTurma,
      {Function()? carregando,
      Function()? sucesso,
      Function(String)? falha,
      bool aulaExperimental = false,
      AlunoNaAulaTurma? aluno}) {
    Future<String> sericeCall;
    analytic(EventosKey.aulas_desmarcarAula);
    carregando?.call();
    String? matricula = num.parse(aluno?.matricula ?? '-1').toString();
    if (matricula == '-1') matricula = null;
    if (aulaTurma.coletiva!) {
      if (((empresaSelecionada != null) && ((empresaSelecionada?.chave?.toString() ?? '') != GetIt.I.get<ControladorApp>().chave.toString()))) {
        sericeCall = _mServiceAulasTurmas.desmarcarPresencaAulaURLpersonalizada(urlTreino: empresaSelecionada!.urlTreino!, chaveEmpresa: empresaSelecionada!.chave!, codigoHorarioTurma: aulaTurma.codigo!, 
          matricula: matricula ?? _mControladorUsuario.mUsuarioLogado!.matricula!, data: UtilDataHora.getDaTaMesDiaAno(locale: 'pt_BR', dataMilis: aulaTurma.diaDate as int?), chaveOrigem: GetIt.I.get<ControladorApp>().chave!);
      } else {
        sericeCall = _mServiceAulasTurmas.desmarcarPresencaAula(aulaTurma.codigo!, matricula ?? _mControladorUsuario.mUsuarioLogado!.matricula!, UtilDataHora.getDaTaMesDiaAno(locale: 'pt_BR', dataMilis: aulaTurma.diaDate as int?));
      }
    } else {
      sericeCall = _mServiceAulasTurmas.desmarcarPresencaTurma(
          matricula ?? _mControladorUsuario.mUsuarioLogado!.matricula!, UtilDataHora.getDaTaMesDiaAno(locale: 'pt_BR', dataMilis: aulaTurma.diaDate as int?), aulaTurma.codigo!);
    }
    sericeCall.timeout(const Duration(seconds: 10));
    sericeCall.then((value) {
      consultaAulaTurmas = ServiceStatus.Waiting;
      mAulasETurmasAgendadas.removeWhere((element) => element.codigo == aulaTurma.codigo);
      mAulasETurmasAgendadasDoDia.removeWhere((element) => element.codigo == aulaTurma.codigo);
      aulaTurma.alunoEstaNaAula = false;

      aulaTurma.ocupacao = aulaTurma.ocupacao! - 1;
      if (aulaTurma.ocupacao! < 0) aulaTurma.ocupacao = 0;
      if (mAulasETurmas.any((a) => aulaTurma.codDia == a.codDia)) {
        mAulasETurmas[mAulasETurmas.indexWhere((a) => aulaTurma.codDia == a.codDia)] = aulaTurma;
      } else {
        mAulasETurmas.add(aulaTurma);
      }
      if (mAulasETurmasDoDia.any((a) => aulaTurma.codDia == a.codDia)) {
        mAulasETurmasDoDia[mAulasETurmasDoDia.indexWhere((a) => aulaTurma.codDia == a.codDia)] = aulaTurma;
      } else {
        mAulasETurmasDoDia.add(aulaTurma);
      }
      mAulasETurmas.sort((aulaA, aulaB) => aulaA.diaDate!.compareTo(aulaB.diaDate!));
      mAulasETurmasDoDia.sort((aulaA, aulaB) => aulaA.diaDate!.compareTo(aulaB.diaDate!));
      // if(!aulaTurma.coletiva){
      //   consultarSaldoAluno = ServiceStatus.Waiting;
      //   if(saldoParaAulasTurmas == null)
      //   saldoParaAulasTurmas = 0;
      //   saldoParaAulasTurmas ++;
      //   consultarSaldoAluno = ServiceStatus.Done;
      // }

      if (!_mControladorUsuario.isUsuarioColaborador) {
        GetIt.I.get<ControladorNotificacoes>().cancelarLembretePorId(aulaTurma.codigo as int);
      } else {
        consultaAulaTurmas = ServiceStatus.Done;
        sucesso?.call();
        return;
      }

      consultarSaldoAlunoOnline(
        sucesso: (saldo) {
          consultaAulaTurmas = ServiceStatus.Done;
          sucesso?.call();
        },
        falha: (v) {
          consultaAulaTurmas = ServiceStatus.Done;
          sucesso?.call();
        },
      );
    }).catchError((onError) {
      if (onError.message.toString().contains('Future')) {
        falha?.call(localizedString('unable_to_process_this'));
      } else if(onError.message.toString().contains('soPodeDesmarcarAulaAte')){
        falha?.call(localizedString('nao_pode_desmarcar_mais', args: [onError.message]));
      } else {
        falha?.call(localizedString('unable_to_check_out_this_class', args: [onError.message]));
      }
    });
  }

  void entrarNaFilaDaAula({ required AulaTurma aulaTurma, required num codigoAluno, Function()? onSuccess, Function(String? error)? onError, Function()? onLoading }) {
    onLoading?.call();
    _mServiceAulasTurmas.inserirNaFila(codigoAula: aulaTurma.codigo!, dia: aulaTurma.dia!, codigoAluno: codigoAluno /* _mControladorUsuario.mUsuarioLogado!.codigoCliente */).then((dynamic response) {
      if ((response['Fila'] as String).toLowerCase() == 'aluno inserido com sucesso na fila de espera!') {
        obterAlunoNaFilaDeEspera(aulaTurma, carregando: (){}, sucesso: (){}, falha: (e) {});
        onSuccess?.call();
      } else {
        onError?.call(response['Fila']);
      }
    }).catchError((error) {
      onError?.call(error.message.toString());
    });
  }
  
  /// Remove um aluno da fila de espera de uma aula/turma específica.
  ///
  /// Parâmetros:
  /// - [aulaTurma]: AulaTurma a ser removida da fila de espera.
  /// - [codigoAluno]: Código do aluno a ser removido.
  /// - [onSuccess]: Função a ser chamada em caso de sucesso.
  /// - [onError]: Função a ser chamada em caso de erro, com uma mensagem de erro opcional.
  /// - [onLoading]: Função a ser chamada durante o carregamento.
  void removerDaFilaDeEspera({required AulaTurma aulaTurma, required num codAluno, Function()? onSuccess, Function(String? error)? onError, Function()? onLoading}) {
    onLoading?.call();
    _mServiceAulasTurmas.removerNaFila(codigoHorarioTurma: aulaTurma.codigo!, dia: aulaTurma.dia ?? '', codigoAluno: codAluno).then((dynamic response) {
      if ((response['Fila'] as String).toLowerCase() == 'aluno removido da fila de espera com sucesso!') {
        obterAlunoNaFilaDeEspera(aulaTurma, carregando: (){}, sucesso: (){}, falha: (e) {});
        onSuccess?.call();
      } else {
        onError?.call(response['Fila']);
      }
    }).catchError((error) {
      onError?.call(localizedString('falha_remover_fila'));
    });
  }

  /// O Professor remove da fila de espera de uma aula.
  ///
  /// Parâmetros:
  /// - [aulaTurma]: AulaTurma da qual o professor será removido da fila de espera.
  /// - [codAluno]: Código do aluno que será removido da fila de espera.
  /// - [onSuccess]: Função opcional a ser executada em caso de sucesso.
  /// - [onError]: Função opcional a ser executada em caso de erro, recebe uma mensagem de erro como parâmetro.
  /// - [onLoading]: Função opcional a ser executada durante o carregamento.
  void professorRemoverDaFilaDeEspera({required AulaTurma aulaTurma, required num codAluno, Function()? onSuccess, Function(String? error)? onError, Function()? onLoading}) {
    onLoading?.call();
    var codUsuario = _mControladorUsuario.mUsuarioLogado?.codUsuario ?? 0;
    var dia = UtilDataHora.getDaTaAnoMesDia(dateTime: UtilDataHora.parseStringToDate(aulaTurma.dia));
    _mServiceAulasTurmas.professorRemoverDaFila(codigoHorarioTurma: aulaTurma.codigo!, dia: dia.replaceAll('-', ''), codigoUsuario: codUsuario, codigoAluno: codAluno).then((dynamic response) {
      if ((response['Fila'] as String).toLowerCase() == 'ok') {
        obterAlunoNaFilaDeEspera(aulaTurma, carregando: (){}, sucesso: (){}, falha: (e) {});
        onSuccess?.call();
      } else {
        onError?.call(response['Fila']);
      }
    }).catchError((error) {
      onError?.call(localizedString('falha_remover_fila'));
    });
  }

  @observable
  List<AlunoFilaDeEspera> mListaAlunosFilaDeEspera = [];
  @observable
  AulaTurma aulaSelecionadaDetalhes = AulaTurma();
  @observable
  ServiceStatus consultarAlunosNaFila = ServiceStatus.Waiting;

  // Obter todos alunos que estão na fila de espera de uma aula/turma específica.
  //
  void obterAlunoNaFilaDeEspera(AulaTurma aulaTurma, {Function()? carregando, Function()? sucesso, Function(String)? falha}) {
    carregando?.call();
    consultarAlunosNaFila = ServiceStatus.Waiting;
    var data = UtilDataHora.parseStringToDate((aulaTurma.dia ?? ''), minutos: false);
    _mServiceAulasTurmas.consultarAlunosNaFila(aulaTurma.codigo!, (data.toString()).replaceAll('-', '').split(' ').first).then((value) {
      mListaAlunosFilaDeEspera.clear();
      mListaAlunosFilaDeEspera.addAll(value);
      mListaAlunosFilaDeEspera.sort((a, b) => a.ordem!.compareTo(b.ordem!));
      consultarAlunosNaFila = mListaAlunosFilaDeEspera.isEmpty ? ServiceStatus.Empty : ServiceStatus.Done;
      sucesso?.call();
    }).catchError((onError) {
      if(onError.message.toString().contains('FILA_DE_ESPERA_DESABILITADA')){
        falha?.call('Fila de espera desabilitada.');
        consultarAlunosNaFila = ServiceStatus.Empty;
      } else {
        falha?.call('Houve um erro ao consultar quem está na fila de espera.');
        consultarAlunosNaFila = ServiceStatus.Error;
      }
    });
  }

  bool get alunoEstaNaFilaDeEspera {
  try {
    var matricula = (_mControladorUsuario.mUsuarioLogado?.matricula ?? '0').isNotEmpty ? (_mControladorUsuario.mUsuarioLogado?.matricula ?? '0') : '0';
    final matriculaUsuario = num.tryParse(matricula) ?? 0;
    return mListaAlunosFilaDeEspera.any((element) => (element.matricula ?? 0) == matriculaUsuario);
  } catch (e) {
    debugPrint('Erro ao verificar se o aluno está na fila de espera: $e');
    return false;
  }
}

  AlunoFilaDeEspera get dadosAlunoNafila {
    for (int i = 0; i < mListaAlunosFilaDeEspera.length; i++) {
      if (mListaAlunosFilaDeEspera[i].matricula == num.parse(_mControladorUsuario.mUsuarioLogado?.matricula ?? '0')) {
        mListaAlunosFilaDeEspera[i].ordem = i + 1;
        return mListaAlunosFilaDeEspera[i];
      }
    }
    return AlunoFilaDeEspera();
  }

  /// Reserva um equipamento para uma aula/turma específica.
  void reservarEquipamento({required AulaTurma aulaTurma, required String equipamento, Function()? onSuccess, Function(String? error)? onError, Function()? onLoading}) {
    onLoading?.call();
    var data = UtilDataHora.parseStringToDate((aulaTurma.dia ?? ''), minutos: false);
    _mServiceAulasTurmas.reservarEquipamento(horarioTurmaId: aulaTurma.codigo.toString(), dia: (data.toString()).replaceAll('-', '').split(' ').first, 
    empresaId: _mControladorUsuario.mUsuarioLogado?.codEmpresa ?? 1, equipamento: equipamento, usuarioId: _mControladorUsuario.mUsuarioLogado?.codUsuario ?? 1).then((dynamic response) {
      if (response == 'Equipamento incluído com sucesso') {
        itensAtualizadosParaReserva = true;
        onSuccess?.call();
      } else {
        onError?.call(response);
      }
    }).catchError((error) {
      itensAtualizadosParaReserva = false;
      onError?.call(localizedString('falha_reservar_equipamento'));
    });
  }

  /// Cancela uma reserva de equipamento.
  void removerReservaEquipamento({required AulaTurma aulaTurma, required String equipamento, Function()? onSuccess, Function(String? error)? onError, Function()? onLoading}) {
    onLoading?.call();
    var data = UtilDataHora.parseStringToDate((aulaTurma.dia ?? ''), minutos: false);
    _mServiceAulasTurmas.removerReservaEquipamento(horarioTurmaId: aulaTurma.codigo.toString(), dia: (data.toString()).replaceAll('-', '').split(' ').first, empresaId: _mControladorUsuario.mUsuarioLogado?.codEmpresa ?? 1, equipamento: equipamento, usuarioId: _mControladorUsuario.mUsuarioLogado?.codUsuario ?? 1).then((dynamic response) {
      if (response == 'Equipamento removido com sucesso') {
        itensAtualizadosParaReserva = true;
        onSuccess?.call();
      } else {
        onError?.call(response);
      }
    }).catchError((error) {
      itensAtualizadosParaReserva = false;
      onError?.call(localizedString('falha_reservar_equipamento'));
    });
  }

  @observable
  List<String> mListaEquipamentosReservados = [];
  @observable
  List<String> mListaEquipamentosOcupados = [];
  @observable
  bool itensAtualizadosParaReserva = false;

  void obterPosicaoEquipamentos({required AulaTurma aulaTurma}) {
    mListaEquipamentosReservados.clear();
    mListaEquipamentosOcupados.clear();
    if((aulaTurma.mapaEquipamentos ?? '').isNotEmpty) {
      var equipamentos = (aulaTurma.mapaEquipamentos ?? '').split(';');
      mListaEquipamentosReservados.addAll(equipamentos);
      mListaEquipamentosReservados.removeWhere((element) => element.isEmpty);
    }
    if((aulaTurma.equipamentosOcupados ?? '').isNotEmpty) {
      var ocupados = (aulaTurma.equipamentosOcupados ?? '').split(';');
      mListaEquipamentosOcupados.addAll(ocupados);
      mListaEquipamentosOcupados.removeWhere((element) => element.isEmpty);
    }
  }

  @action
  bool aparelhoEstaReservado(String equipamento) {
    return mListaEquipamentosOcupados.any((element) => element.split('&')[0] == equipamento);
  }

  @action
  bool alunoTemEquipamentoReservado(String nome) {
    return mListaEquipamentosOcupados.any((element) => element.contains(nome));
  }

  @action
  String getEquipamentoReservado(String nome) {
    var reserva = mListaEquipamentosOcupados.firstWhere((element) => element.contains(nome), orElse: () => '');
    return reserva.split('&').first;
  }

  String getIconeEquipamento({required AulaTurma aulaTurma, required String posicao}) {
    var equipamento = aulaTurma.listaMapaEquipamentoAparelho?.firstWhere((element) => element.posicaoMapa == posicao, orElse: () => MapaEquipamentosAparelho());
    return (equipamento?.iconeAparelho ?? '').split(' ').last;
  }

  String getSiglaEquipamento({required AulaTurma aulaTurma, required String posicao}) {
    var equipamento = aulaTurma.listaMapaEquipamentoAparelho?.firstWhere((element) => element.posicaoMapa == posicao, orElse: () => MapaEquipamentosAparelho());
    return equipamento?.siglaAparelho ?? '';
  }

  String getNumeradorEquipamento({required AulaTurma aulaTurma, required String posicao}) {
    var equipamento = aulaTurma.listaMapaEquipamentoAparelho?.firstWhere((element) => element.posicaoMapa == posicao, orElse: () => MapaEquipamentosAparelho());
    return (equipamento?.posicaoNumeroSequencial ?? 0).toString();
  }

  // @action
  // bool getEquipamentoTemIcone(AulaTurma aulaTurma) {
  //   return (aulaTurma.listaMapaEquipamentoAparelho ?? []).any((element) => (element.iconeAparelho?.isNotEmpty ?? false));
  // }

  // @action
  // bool getEquipamentoTemSiglas(AulaTurma aulaTurma) {
  //   return (aulaTurma.listaMapaEquipamentoAparelho ?? []).any((element) => (element.siglaAparelho?.isNotEmpty ?? false));
  // }


  void marcarPresencaNaAulaTurma(AulaTurma? aulaTurma,
      {String? matriculaAluno, Function()? onAulaCheia, Function(String? mensagem)? usarAulaExperimental, Function()? carregando, Function()? sucesso, Function(String)? falha, bool aulaExperimental = false, }) {
    Future<MarcacaoAulaTurma> sericeCall;
    analytic(EventosKey.aulas_marcarAula);
    if (_mControladorUsuario.isUsuarioColaborador && aulaTurma!.coletiva!) {
      sericeCall = _mServiceAulasTurmas.professorMarcarPresenca(aulaTurma.codigo!, aulaExperimental, (matriculaAluno ?? _mControladorUsuario.mUsuarioLogado!.matricula!),
          UtilDataHora.getDaTaMesDiaAno(locale: 'pt_BR', dataMilis: aulaTurma.diaDate as int?), _mControladorUsuario.mUsuarioLogado!.codUsuario!);
    } else if (aulaTurma!.coletiva!) {
      if (((empresaSelecionada != null) && ((empresaSelecionada?.chave?.toString() ?? '') != GetIt.I.get<ControladorApp>().chave.toString()))) {
        sericeCall = _mServiceAulasTurmas.marcarPresencaAulaURLpersonalizada(urlTreino: empresaSelecionada!.urlTreino!, codigoContratoMarcacao: codigoContrato(), chaveAluno: GetIt.I.get<ControladorApp>().chave!,  chaveEmpresa: empresaSelecionada!.chave!, codigoAula: aulaTurma.codigo!, aulaExperimental: aulaExperimental,
          matricula: (matriculaAluno ?? _mControladorUsuario.mUsuarioLogado!.matricula!), dia: UtilDataHora.getDaTaMesDiaAno(locale: 'pt_BR', dataMilis: aulaTurma.diaDate as int?), );
      } else {
        sericeCall = _mServiceAulasTurmas.marcarPresencaAula(codigoAula: aulaTurma.codigo!, aulaExperimental: aulaExperimental, matricula: (matriculaAluno ?? _mControladorUsuario.mUsuarioLogado!.matricula!),
          dia: UtilDataHora.getDaTaMesDiaAno(locale: 'pt_BR', dataMilis: aulaTurma.diaDate as int?), codigoContratoMarcacao: codigoContrato());
      }
    } else {
      sericeCall = _mServiceAulasTurmas.marcarPresencaTurma(
          (matriculaAluno ?? _mControladorUsuario.mUsuarioLogado!.matricula!), UtilDataHora.getDaTaMesDiaAno(locale: 'pt_BR', dataMilis: aulaTurma.diaDate as int?), aulaTurma.codigo!, codigoContrato());
    }
    carregando?.call();
    sericeCall.timeout(const Duration(seconds: 30)).then((value) {
      if (value.aulaExperimental != null) {
        if (value.aulaExperimental?.trim().contains('aluno não possui a modalidade, deseja usar uma') ?? false) {
          usarAulaExperimental?.call(localizedString('you_dont_have_access_to_this_class', args: [value.aulaExperimental!.substring(2)]));
        }
        if (value.aulaExperimental?.trim().contains('Você não tem essa modalidade, deseja usar uma de') ?? false) {
          usarAulaExperimental?.call(localizedString('you_dont_have_access_to_this_class', args: [value.aulaExperimental!.substring(2)]));
        }
        if (value.aulaExperimental?.trim().contains('A aula não está dentro do horário do seu plano') ?? false) {
          usarAulaExperimental?.call(localizedString('your_plan_doesnt_allow_classes_this_period', args: [value.aulaExperimental!]));
        }
        if (value.aulaExperimental!.trim().contains('Esta aula não pertence a esta empresa')) {
          usarAulaExperimental?.call(localizedString('this_class_is_not_from_this_company', args: [value.aulaExperimental!.replaceAll('  Esta aula não pertence a esta empresa', "A ${aulaTurma.coletiva! ? 'aula' : 'turma'} não percente a esta empresa")]));
        }
      } else {
        var index = mAulasETurmas.indexWhere((element) => element.codigo == aulaTurma.codigo);
        //var indexDia = mAulasETurmasDoDia.indexWhere((element) => element.codigo == aulaTurma.codigo);
        if (!_mControladorUsuario.isUsuarioColaborador) {
          mAulasETurmas.removeAt(index);
          //mAulasETurmasDoDia.removeAt(indexDia);
          value.aula ??= aulaTurma;
          value.aula!.alunoEstaNaAula = true;
          value.aula!.fotoProfessor = aulaTurma.fotoProfessor;
          if (value.aula!.alunoEstaNaAula! && !aulaTurma.coletiva!) value.aula!.ocupacao = value.aula!.ocupacao! + 1;
          mAulasETurmasAgendadas.add(value.aula!);
          if (value.aula?.dia == UtilDataHora.getDiaMesAno(dateTime: DateTime.now())) {
            mAulasETurmasAgendadasDoDia.add(value.aula!);
          }
        } else {
          mAulasETurmas[index].capacidade = value.aula!.capacidade;
          mAulasETurmas[index].ocupacao = value.aula!.ocupacao;
          //mAulasETurmasDoDia[indexDia].capacidade = value.aula!.capacidade;
          //mAulasETurmasDoDia[indexDia].ocupacao = value.aula!.ocupacao;
        }
        aulaTurma.ocupacao = value.aula?.ocupacao;
        consultaAulaTurmas = ServiceStatus.Waiting;
        if (_mControladorUsuario.isUsuarioColaborador) {
          consultaAulaTurmas = ServiceStatus.Done;
          sucesso?.call();
        } else {
          consultarSaldoAlunoOnline(
            sucesso: (saldo) {
              consultaAulaTurmas = ServiceStatus.Done;
              sucesso?.call();
              UtilitarioApp.showAppReview();
            },
            falha: (m) {
              consultaAulaTurmas = ServiceStatus.Done;
              sucesso?.call();
            },
          );
        }
      }
    }).catchError((onError) {
      if (onError.message.toString().contains('A aula já está cheia')) {
        onAulaCheia?.call();
        return;
      }
      if (onError.message.toString().contains('Você já possui compromissos no horário')) {
        falha?.call(localizedString('você_ja_possui_compromissos_no_horario', args: [onError.message.toString().replaceAll('Você já possui compromissos no horário: ', '')]));
        return;
      }
      if (onError.message.toString().contains('Future')) {
        falha?.call(localizedString('unable_to_process_this_request'));
      } 
      else if (onError.message.toString().contains('Você não possui a modalidade')) {
        _controladorCliente.isUsuarioColaborador ? falha?.call(localizedString('o_aluno_nao_tem_modalidade')): falha?.call(localizedString('erro_nao_tem_modalidade', args: [onError.message.toString()]));
      }
      else if(onError.message.toString().contains('Você não tem permissão para adicionar aluno em aula iniciada/realizada')){
        falha?.call(localizedString('sem_permissao_para_add_em_aula_iniciada', args: [onError.message]));
      }
      else if(onError.message.toString().contains(' Operação não permitida. Período permitido')){
        String mensagemIngles = onError.message.toString().substring(69);
        mensagemIngles = mensagemIngles.replaceAll('é de', 'is from');
        mensagemIngles = mensagemIngles.replaceAll('até', 'to');
        falha?.call(localizedString('check_in_nao_permitido_nesse_periodo', namedArgs: {'mensagemPortugues': onError.message, 'mensagemIngles': mensagemIngles}));
      }
      else {
        String mensagemTratada = onError.message.toString().replaceAll('ERRO: ', '');
        if(onError.message.toString().contains('Você só pode marcar a')){
          falha?.call(localizedString('erro_marcar_aula_com_antecedencia', args: [mensagemTratada]));
        }
        else if(onError.message.toString().contains('Aluno não possui autorização de acesso')){
          falha?.call(localizedString('erro_aluno_nao_possui_autorizacao', args: [mensagemTratada]));
        }
        else if(onError.message.toString().contains('Já foi marcada uma aula nesse dia')){
          falha?.call(localizedString('erro_ja_foi_marcada_uma_aula', args: [mensagemTratada]));
        }
        else if(onError.message.toString().contains('O número de aulas marcadas na semana')){
          falha?.call(localizedString('erro_atingiu_numero_de_aulas_semanais', args: [mensagemTratada]));
        }
        else{
          falha?.call(localizedString('erro_checkin_aula', args: [mensagemTratada]));
        }
      }
    });
  }

  bool get todosAlunosComPresenca {
    bool presenca = false;
    List<AlunoNaAulaTurma> mListaAux = [];
    mListaSomenteAlunosAula.clear();
    for (final element in mListaAlunosNaAulaBackup) {
      if (element.matricula != null || (element.matricula?.isNotEmpty ?? false)) {
        mListaAux.add(element);
      } else {
        mListaSomenteAlunosAula = [];
      }
    }
    mListaSomenteAlunosAula.addAll(mListaAux);
    presenca = mListaSomenteAlunosAula.isEmpty || !mListaSomenteAlunosAula.any((element) => element.confirmado == false);
    return presenca;
  }

  void marcarPresencaTodosAlunosNaAula({required AulaTurma aulaTurma, required Function()? carregando, required Function()? sucesso, required Function(String)? falha}) {
    carregando?.call();
    mAulaTurmaSimplificado = null;
    _mServiceAulasTurmas.marcarPresencaTodosAlunosNaAula(aulaTurma.codigo.toString(), _mControladorUsuario.mUsuarioLogado?.codEmpresa ?? 1,  num.parse(UtilDataHora.getDaTaAnoMesDia(dataMilis: aulaTurma.diaDate?.toInt()).replaceAll('-', '').toString())).then((value) {
      mAulaTurmaSimplificado = value;
      consultarAlunosDaAula(aulaTurma);
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message.toString());
    });
  }

  void consultarAulasTurmasProfessor({required Function()? carregando, required Function()? sucesso, required Function(String?)? falha}) {
    var codColaborador = GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codigoColaborador;
    carregando!.call();
    _mServiceAulasTurmas.consultarAulasTurmasProfessor(getCodEmpresa, diaConsultar, codColaborador ?? 0).then((value) {
      mAulasETurmas.clear();
      mAulasETurmas.addAll(value);
      if(mAulasETurmas.isNotEmpty) {
        if (mAulasETurmas.first.dia == UtilDataHora.getDiaMesAno(dateTime: DateTime.now())) {
          mAulasETurmasDoDia.clear();
          mAulasETurmasDoDia.addAll(mAulasETurmas);
        }
      }
      if(mAulasETurmas.isNotEmpty){
        mAulasTurmasExpandido = mAulasETurmas.first;
        if (controllerExpandable.expanded) {
          controllerExpandable.toggle();
        }
      }
      consultaAulaTurmas = mAulasETurmas.isEmpty ? ServiceStatus.Empty : ServiceStatus.Done;
      sucesso!.call();
    }).catchError((onError) {
      consultaAulaTurmas = ServiceStatus.Error;
      falha!.call(onError.message);
    });
  }

  void consultarAulasTurmas({Function()? carregando, Function()? sucesso, Function(String?)? falha}) {
    var usuario = _mControladorUsuario.mUsuarioLogado;
    if (_mControladorUsuario.isUsuarioColaborador) return;
    consultaAulaTurmas = ServiceStatus.Waiting;
    carregando?.call();
    mAulasETurmas.clear();
    mAulasETurmasAgendadas.clear();
    mTurmasAgendadas.clear();
    mListaAlunosNaAula.clear();
    mAulasTurmasFavoritas.clear();
    // Valida se a empresa selecionada é diferente da empresa do usuário logado
    // Se a chave for a mesma, não é necessário consultar a URL de treino
    if ((empresaSelecionada != null) && ((empresaSelecionada?.chave?.toString() ?? '') != GetIt.I.get<ControladorApp>().chave.toString())) {
      _mServiceAulasTurmas.descobrirURLTreino(chaveUnidade: empresaSelecionada?.chave ?? '').then((dadosEmpresa) {
        empresaSelecionada?.urlTreino = dadosEmpresa.serviceUrls?.treinoUrl ?? '';
        var consultas = [_mServiceAulasTurmas.consultarAulasColetivasURLpersonalizada(urlTreino: empresaSelecionada!.urlTreino!, chaveEmpresa: empresaSelecionada?.chave ?? '', dia: diaConsultar, codigoEmpresa: getCodEmpresa, matricula: num.parse(usuario!.matricula!), aulasFuturas: true, chaveOrigem: GetIt.I.get<ControladorApp>().chave!)];
        consultarSaldoAlunoOnline(
          sucesso: (saldo) {
            saldoParaAulasTurmasReposicao = saldo;
            Future.wait(consultas).then((value) {
              mAulasETurmas.clear();
              mAulasETurmasAgendadas.clear();
              for (final element in value) {
                mAulasETurmas.addAll(element.where((aulaTurma) => !aulaTurma.alunoEstaNaAula!).toList());
                mAulasETurmasAgendadas.addAll(element.where((aulaTurma) => aulaTurma.alunoEstaNaAula!).toList());
              }
              if(mAulasETurmas.isNotEmpty) {
                if (mAulasETurmas.first.dia == UtilDataHora.getDiaMesAno(dateTime: DateTime.now())) {
                  mAulasETurmasDoDia.clear();
                  mAulasETurmasDoDia.addAll(mAulasETurmas);
                }
              }
              if (saldoParaAulasTurmasReposicao == 0) {
                mAulasETurmas.removeWhere((element) => !element.coletiva! && !element.alunoEstaNaAula!);
                mAulasETurmasDoDia.removeWhere((element) => !element.coletiva! && !element.alunoEstaNaAula!);
              }
              if (getCodEmpresa == usuario.codEmpresa) {
                return _mServiceAulasTurmas.consultarTurmasAgendada(usuario.matricula!, true, (codigoContrato()));
              } else {
                return <AulaTurma>[];
              }
            }).then((turmasAgendadas) {
              if((turmasAgendadas as List<dynamic>).length >= 1){
                mTurmasAgendadas = turmasAgendadas as List<AulaTurma>;
              }else {
                mTurmasAgendadas = [];
              }
              for (var i = 0; i < mTurmasAgendadas.length; i++) {
                mTurmasAgendadas[i].alunoEstaNaAula = true;
              }
              for (var i = 0; i < mAulasETurmas.length; i++) {
                // ignore: unnecessary_type_check
                if (turmasAgendadas is List<AulaTurma> && turmasAgendadas.any((agendada) => agendada.codigo == mAulasETurmas[i].codigo && agendada.codDia == mAulasETurmas[i].codDia)) {
                  mAulasETurmas[i].eAulaAgendada = true;
                  mAulasETurmas[i].alunoEstaNaAula = true;
                }
              }
              for (var i = 0; i < mAulasETurmasDoDia.length; i++) {
                // ignore: unnecessary_type_check
                if (turmasAgendadas is List<AulaTurma> && turmasAgendadas.any((agendada) => agendada.codigo == mAulasETurmas[i].codigo && agendada.codDia == mAulasETurmas[i].codDia)) {
                  mAulasETurmasDoDia[i].eAulaAgendada = true;
                  mAulasETurmasDoDia[i].alunoEstaNaAula = true;
                }
              }
              List<AulaTurma>? agendadasRemover = mAulasETurmas.where((element) => element.alunoEstaNaAula! || (element.eAulaAgendada ?? false)).toList();
              mAulasETurmasAgendadas.addAll(agendadasRemover);
              mAulasETurmasAgendadas.addAll(mTurmasAgendadas.where((element) => element.alunoEstaNaAula! && element.dia == diaConsultar && !mAulasETurmasAgendadas.any((x) => x.codigo == element.codigo)).toList());
              mAulasETurmas.addAll(mTurmasAgendadas.where((element) => !element.alunoEstaNaAula! && element.dia == diaConsultar && !mAulasETurmasAgendadas.any((x) => x.codigo == element.codigo)).toList());
              agendadasRemover = null;
              mAulasETurmas.removeWhere((element) => element.alunoEstaNaAula!);
              if(mAulasETurmasAgendadas.isNotEmpty) {
                if (mAulasETurmasAgendadas.first.dia == UtilDataHora.getDiaMesAno(dateTime: DateTime.now())) {
                  mAulasETurmasAgendadasDoDia.clear();
                  mAulasETurmasAgendadasDoDia.addAll(mAulasETurmasAgendadas);
                }
              }
              consultaAulaTurmas = mAulasETurmas.isEmpty ? ServiceStatus.Empty : ServiceStatus.Done;
              sucesso?.call();
            }).catchError((onError) {
              consultaAulaTurmas = ServiceStatus.Error;
              falha?.call(onError.message);
            });
          },
          falha: falha);      
      });
    } else {
      var consultas = [_mServiceAulasTurmas.consultarAulasColetivas(dia: diaConsultar, codigoEmpresa: getCodEmpresa, matricula: num.parse(usuario!.matricula!), aulasFuturas: true)];
      if (getCodEmpresa == usuario.codEmpresa) {
        consultas.add(_mServiceAulasTurmas.consultarTurmasDoAluno(num.parse(usuario.matricula!), diaConsultar, diaConsultar, (codigoContrato())));
      }
      consultarSaldoAlunoOnline(
          sucesso: (saldo) {
            saldoParaAulasTurmasReposicao = saldo;
            Future.wait(consultas).then((value) {
              mAulasETurmas.clear();
              mAulasETurmasAgendadas.clear();
              for (final element in value) {
                mAulasETurmas.addAll(element.where((aulaTurma) => !aulaTurma.alunoEstaNaAula!).toList());
                mAulasETurmasAgendadas.addAll(element.where((aulaTurma) => aulaTurma.alunoEstaNaAula!).toList());
              }
              if (saldoParaAulasTurmasCredito == 0 && saldoParaAulasTurmasReposicao == 0) {
                mAulasETurmas.removeWhere((element) => !element.coletiva! && !element.alunoEstaNaAula!);
              }
              if (getCodEmpresa == usuario.codEmpresa) {
                return _mServiceAulasTurmas.consultarTurmasAgendada(usuario.matricula!, true, (codigoContrato()));
              } else {
                return <AulaTurma>[];
              }
            }).then((turmasAgendadas) {
              if((turmasAgendadas as List<dynamic>).length >= 1){
                mTurmasAgendadas = turmasAgendadas as List<AulaTurma>;
              }else {
                mTurmasAgendadas = [];
              }
              for (var i = 0; i < mTurmasAgendadas.length; i++) {
                mTurmasAgendadas[i].alunoEstaNaAula = true;
              }
              for (var i = 0; i < mAulasETurmas.length; i++) {
                // ignore: unnecessary_type_check
                if (turmasAgendadas is List<AulaTurma> && turmasAgendadas.any((agendada) => agendada.codigo == mAulasETurmas[i].codigo && agendada.codDia == mAulasETurmas[i].codDia)) {
                  mAulasETurmas[i].eAulaAgendada = true;
                  mAulasETurmas[i].alunoEstaNaAula = true;
                }
              }
              List<AulaTurma>? agendadasRemover = mAulasETurmas.where((element) => element.alunoEstaNaAula! || (element.eAulaAgendada ?? false)).toList();
              mAulasETurmasAgendadas.addAll(agendadasRemover);
              mAulasETurmasAgendadas.addAll(mTurmasAgendadas.where((element) => element.alunoEstaNaAula! && element.dia == diaConsultar && !mAulasETurmasAgendadas.any((x) => x.codigo == element.codigo)).toList());
              mAulasETurmas.addAll(mTurmasAgendadas.where((element) => !element.alunoEstaNaAula! && element.dia == diaConsultar && !mAulasETurmasAgendadas.any((x) => x.codigo == element.codigo)).toList());
              agendadasRemover = null;
              mAulasETurmas.removeWhere((element) => element.alunoEstaNaAula!);
              if(mAulasETurmasAgendadas.isNotEmpty) {
                if (mAulasETurmasAgendadas.first.dia == UtilDataHora.getDiaMesAno(dateTime: DateTime.now())) {
                  mAulasETurmasAgendadasDoDia.clear();
                  mAulasETurmasAgendadasDoDia.addAll(mAulasETurmasAgendadas);
                }
              }
              consultaAulaTurmas = (mAulasETurmas.isEmpty && mAulasETurmasAgendadas.isEmpty) ? ServiceStatus.Empty : ServiceStatus.Done;
              sucesso?.call();
            }).catchError((onError) {
              if(onError.message.contains('is not a subtype of type')){
                consultaAulaTurmas = ServiceStatus.Empty;
              }else {
                consultaAulaTurmas = ServiceStatus.Error;
                falha?.call(onError.message);
              }
            });
          },
          falha: falha);
    
    }
  }

  void confirmarPresencaAluno(AlunoNaAulaTurma aluno, AulaTurma aulaTurma, {Function()? carregando, Function()? sucesso, Function(String?)? falha}) {
    carregando?.call();
    analytic(EventosKey.marcarAula_confirmar);
    _mServiceAulasTurmas.confirmarAlunoAula(aulaTurma.coletiva! ? 'confirmarAlunoAulaColetiva' : 'confirmarAlunoAula', aulaTurma.codigo!, num.parse(aluno.matricula!), aulaTurma.dia!,
      _mControladorUsuario.mUsuarioLogado!.empresas?.first.codigoProfessor ?? _mControladorUsuario.mUsuarioLogado!.codigoProfessor ?? _mControladorUsuario.mUsuarioLogado!.codUsuario!, 
      num.parse(aluno.matricula!)).then((value) {
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(localizedString('erro_confirmando_presenca_aluno', args: [onError.message]));
    });
  }

  void desmarcarAluno(AlunoNaAulaTurma aluno, AulaTurma aulaTurma, {Function()? carregando, Function()? sucesso, Function(String?)? falha}) {
    analytic(EventosKey.aulas_desmarcarAula);
    desmarcarPresencaNaAulaOuTurma(aulaTurma, sucesso: sucesso, carregando: carregando, falha: falha, aluno: aluno);
  }

  void desmarcarTodosAluno(List<AlunoNaAulaTurma> aluno, AulaTurma aulaTurma, {Function()? carregando, Function()? sucesso, Function(String?)? falha}) {
    analytic(EventosKey.aulas_desmarcarAula);
    for (final element in aluno) {
      if (element.matricula != null) {
         desmarcarPresencaNaAulaOuTurma(aulaTurma, sucesso: sucesso, carregando: carregando, falha: falha, aluno: element);
      }
    }
  }

  num? codigoContrato() {
    if (contratoMarcacaoAula == null) {
      if (_controladorContrato.contratoVisualizar == null) {
        return null;
      } else {
        return (_controladorContrato.contratoVisualizar?.codigo ?? 0);
      }
    } else {
      return (contratoMarcacaoAula?.codigo ?? 0);
    }
  }

  void limparTudo() {
    professorViuComoConfirmarQr = false;
    consultaAulaTurmas = ServiceStatus.Waiting;
    consultarAlunosTurmaAula = ServiceStatus.Waiting;
    consultarSaldoAluno = ServiceStatus.Waiting;
    diaConsultarAulasTurma = null;
    saldoParaAulasTurmasReposicao = 0;
    contratoMarcacaoAula = null;
    mAulasETurmas.clear();
    mAulasETurmasDoDia.clear();
    mAulasETurmasAgendadas.clear();
    mAulasETurmasAgendadasDoDia.clear();
    mTurmasAgendadas.clear();
    mListaAlunosNaAula.clear();
    mAulasTurmasFavoritas.clear();
    mListaSomenteAlunosAula.clear();
    mAulaTurmaSimplificado = null;
    empresaSelecionada = null;
  }

  void listaAlunosNaAula({Function()? sucesso}){
    List<AlunoNaAulaTurma> mListaAux = [];
    mListaSomenteAlunosAula.clear();
    mListaSomenteAlunosAulaBKP.clear();
    for (final element in mListaAlunosNaAulaBackup) {
      if (element.matricula != null || (element.matricula?.isNotEmpty ?? false)) {
        mListaAux.add(element);
      } else {
        mListaSomenteAlunosAula = [];
        mListaSomenteAlunosAulaBKP.addAll(mListaSomenteAlunosAula);
      }
    }
    mListaSomenteAlunosAula.addAll(mListaAux);
    mListaSomenteAlunosAulaBKP.addAll(mListaSomenteAlunosAula);
    sucesso?.call();
  }
  
    void consultarHistoricoAulas({Function()? carregando, Function()? sucesso, Function(dynamic)? falha, DateTime? dataInicial, DateTime? dataFinal, int? page, bool? addNovaPagina}) {
    addNovaPagina ??= false;
    carregando?.call();
    statusConsultarHistoricoAulas = ServiceStatus.Waiting;
    _mServiceAulasTurmas.consultarHistoricoAulas(num.parse(_mControladorUsuario.mUsuarioLogado!.matricula!), UtilDataHora.getDiaMesAno(dateTime: dataInicial ?? DateTime.now()),
            UtilDataHora.getDiaMesAno(dateTime: dataFinal ?? DateTime.now()), page ?? 0, 99, codigoContrato() ?? 0, _controladorCliente.mUsuarioLogado?.codEmpresa ?? 0)
        .then((value) {
      for (final element in value) {
        element.dia = element.dia?.substring(0, 10);
      }
      historicoAulas.clear();
      if (addNovaPagina!) {
        historicoAulas.addAll(value);
        historicoAulas.removeWhere((aula) {
          final horarioFinal = aula.horario?.split(' - ').last;
          final dataAula = aula.dia != null ? UtilDataHora.parseStringToDate(aula.dia!) : null;
          if (horarioFinal != null && dataAula != null) {
            final horaAtual = DateTime.now();
            final horaFinal = DateTime(dataAula.year, dataAula.month, dataAula.day,
          int.parse(horarioFinal.split(':').first), int.parse(horarioFinal.split(':').last));
            return horaFinal.isAfter(horaAtual) && dataAula.isAfter(DateTime.now());
          }
          return false;
        });
        statusConsultarHistoricoAulas = ServiceStatus.Done;
      } else {
        historicoAulas = value;
        historicoAulas.removeWhere((aula) {
          final horarioFinal = aula.horario?.split(' - ').last;
          final dataAula = aula.dia != null ? UtilDataHora.parseStringToDate(aula.dia!) : null;
          if (horarioFinal != null && dataAula != null) {
            final horaAtual = DateTime.now();
            final horaFinal = DateTime(dataAula.year, dataAula.month, dataAula.day,
          int.parse(horarioFinal.split(':').first), int.parse(horarioFinal.split(':').last));
            return horaFinal.isAfter(horaAtual) && dataAula.isAfter(DateTime.now());
          }
          return false;
        });
        if (historicoAulas.isEmpty) {
          statusConsultarHistoricoAulas = ServiceStatus.Empty;
        } else {
          statusConsultarHistoricoAulas = ServiceStatus.Done;
        }
      }

      sucesso?.call();
    }).catchError((erro) {
      falha?.call(erro);
      statusConsultarHistoricoAulas = ServiceStatus.Error;
    });
  }

  void pesquisarSomenteAlunoAula({required String termo}) {    
    consultarAlunosTurmaAula = ServiceStatus.Waiting;
    mListaSomenteAlunosAula.clear();
    if (termo.isNotEmpty) {
      var aux = mListaSomenteAlunosAulaBKP.where((element) => removeDiacritics((element.nome ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())) || removeDiacritics((element.matricula ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())));
      mListaSomenteAlunosAula.addAll(aux);
      consultarAlunosTurmaAula = mListaSomenteAlunosAula.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    } else {
      mListaSomenteAlunosAula.addAll(mListaSomenteAlunosAulaBKP);
      consultarAlunosTurmaAula = ServiceStatus.Done;
    }
  }

  void consultarProgramaTreinoProfessor(AlunoNaAulaTurma? alunos, int index, {Function()? carregando, Function()? sucesso, Function(String? message)? falha}) {
    carregando?.call();
    statusConsultaProgramaAluno = ServiceStatus.Waiting;
    _prescrService.consultarUltimoProgramaDoAluno(_controladorCliente.mUsuarioLogado!.username!, mListaSomenteAlunosAula[index].cliente!.codigoCliente).then((value) {
      mProgramaAluno = value;
      statusConsultaProgramaAluno = ServiceStatus.Done;
      sucesso!();
    }).catchError((onError) {
      statusConsultaProgramaAluno = ServiceStatus.Error;
      falha?.call(onError.message);
    });
  }

  Future<bool> confirmarAlunoEmAula({required num codigoAula, required num matricula, required String dia}) async {
    try {
      dynamic body = await _mServiceAulasTurmas.isAlunoNaAula(codigoAula: codigoAula, urlTreino: empresaSelecionada?.urlTreino ?? ConfigURL.TREINO, chaveUnidade: empresaSelecionada?.chave ?? GetIt.I.get<ControladorApp>().chave!, matricula: matricula, dia: dia, chaveOrigem: GetIt.I.get<ControladorApp>().chave!);
      return body['alunoNaAula'];
    } catch (e) {
      return false;
    }
  }

  @observable
  bool agruparAulasPorHorario = true;

  @action
  Future<void> gravarVisualizacaoAgrupamento(bool habilitado) async {
    SharedPreferences db = await SharedPreferences.getInstance();
    await db.setBool('AULAS_AGRUPADAS_POR_HORARIO', habilitado);
  }

  @action
   consultarVisualizacaoAgrupamentoHorario() async {
    SharedPreferences.getInstance().then((value) {
      agruparAulasPorHorario = value.getBool('AULAS_AGRUPADAS_POR_HORARIO') ?? true;
    });
  }

  String imagemBeachAulaTurma({String? nome, Ficha? ficha, required bool treino}) {
    final Map<String, String> imagens = {
      'defesa': 'assets/images/imageBeach/defesa.jpeg',
      'curta': 'assets/images/imageBeach/dropshot.jpeg',
      'dropshot': 'assets/images/imageBeach/dropshot.jpeg',
      'forehand': 'assets/images/imageBeach/forehand.jpeg',
      'smash': 'assets/images/imageBeach/smash.jpeg',
      'saque': 'assets/images/imageBeach/saque.jpeg',
      'gancho': 'assets/images/imageBeach/gancho.jpeg',
    };

    final imagemPadrao = 'assets/images/imageBeach/geral.jpeg';

    if(treino) {
      try {
        if (ficha == null) {
          return imagemPadrao;
        }
        if (ficha.atividades == null) {
          if (ficha.atividades?.isEmpty ?? true) {
            return imagemPadrao;
          }
          return imagemPadrao;
        }
        var primeiraAtividade = ficha.atividades?.first;
        var atividade = GetIt.I.get<ControladorTreinoAluno>().mProgramaCarregado!.programa!.atividades!.firstWhere(
          (element) => element.codigoAtividade.toString() == primeiraAtividade?.codigoAtividade,
          orElse: () {
            return ProgramaAtividade(nome: 'alongamento');
          },
        );
        String? urlRetorno;
        imagens.forEach((key, value) {
          key.split(',').forEach((element) {
            if (removeDiacritics(atividade.nome!.toLowerCase()).contains(element)) {
              urlRetorno = value;
            }
          });
        });
        return urlRetorno ?? imagemPadrao;
      } catch (e) {
        return imagemPadrao;
      }
    }else {
      for (final key in imagens.keys) {
        if (removeDiacritics(nome!.toLowerCase()).contains(key)) {
          return imagens[key]!;
        }
      }
    }

    return imagemPadrao;
  }

  bool isVideoParaProfessor(List<LinkVideos> links) {
    if(links.isNotEmpty){
      return links.any((element) => element.professor ?? false);
    } else {
      return false;
    }
  }

  List<AulaTurma> get aulaTurmaAcontecendoDoDia {
    //if (diaConsultarString != UtilDataHora.getDiaMes(dateTime: DateTime.now())) return [];
    // Novo jeito e lavamos nóz
    return mAulasETurmasDoDia.where((aulaTurma) {
      var rangeDaAula = DurationRange(
          start: Duration(hours: num.parse(aulaTurma.inicio!.split(':').first) as int, minutes: num.parse(aulaTurma.inicio!.split(':').last) as int),
          end: Duration(hours: num.parse(aulaTurma.fim!.split(':').first) as int, minutes: num.parse(aulaTurma.fim!.split(':').last) as int));
      var durationAgora = Duration(hours: DateTime.now().hour, minutes: DateTime.now().minute);
      return rangeDaAula.estaNoRange(durationAgora);
    }).toList();
  }

  List<AulaTurma> get aulaTurmaFuturasDiaDoDia {
    return mAulasETurmasDoDia.where((aula) {
      if ('316_07/05/2021' == aula.codDia) {
        //var debug = UtilDataHora.parseStringToDate("${aula.dia} ${aula.inicio}",minutos: true)
      }
      return getIncioDaAulaEmDate(aula)!.isAfter(DateTime.now()) && aulaTurmaAcontecendo.where((agr) => agr.codigo == aula.codigo).isEmpty;
    }).toList();
  }
}
