import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/ServiceProvider/TreinoService.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/avaliarProfessor/ProfessorAvaliar.dart';
import 'package:app_treino/model/avalicaoFisica/AvaliacaoFisicaRecente.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorAvaliarProfessor.g.dart';

class ControladorAvaliarProfessor = _ControladorAvaliarProfessorBase with _$ControladorAvaliarProfessor;

abstract class _ControladorAvaliarProfessorBase with Store {
  @observable
  ObservableList<AvaliacaoDeProfessor> listaProfessoresAvaliados = ObservableList<AvaliacaoDeProfessor>();

  @observable
  ServiceStatus statusConsultaProfessores = ServiceStatus.Waiting;

  List<ProfessorAvaliar> mTodosProfessores = [];

  var mService = GetIt.I.get<PersonalService>();
  var mCliente = GetIt.I.get<ControladorCliente>();
  var treinoService = GetIt.I.get<TreinoService>();


  void consultarAvaliacoesPorCliente({Function()? carregando, Function()? sucesso, Function(String? falha)? falha}) {
    carregando?.call();
    listaProfessoresAvaliados.clear();
    statusConsultaProfessores = ServiceStatus.Waiting;
    treinoService.listaAvaliacoesPorCliente((mCliente.mUsuarioLogado!.codUsuario!).toInt()).then((value) {
      value.sort((a, b) => b.dataRegistro!.compareTo(a.dataRegistro!));
      if (value.isEmpty) {
        if (mCliente.mUsuarioLogado!.professor!.toLowerCase().contains('sem professor')) {
          statusConsultaProfessores = ServiceStatus.Error;
          return;
        }
         listaProfessoresAvaliados.add(AvaliacaoDeProfessor(
            empresa: (mCliente.mUsuarioLogado?.codEmpresa ?? 1).toInt(),
            clienteUsername: mCliente.mUsuarioLogado!.username,
            professorNome: mCliente.mUsuarioLogado!.professor,
            notaAvaliada: null,
         ));
      } else {
        if (validarSeTemAvaliacaoFeitaAmaisDeTrintaDias(value)) {
          if (!mCliente.mUsuarioLogado!.professor!.toLowerCase().contains('sem professor')) {
            final fotoProfessor = value.firstWhere((element) => element.professorNome?.toLowerCase() == mCliente.mUsuarioLogado!.professor?.toLowerCase(), orElse: () {
              return AvaliacaoDeProfessor();
            },).fotoProfessor;
            listaProfessoresAvaliados.add(AvaliacaoDeProfessor(
            empresa: (mCliente.mUsuarioLogado?.codEmpresa ?? 1).toInt(),
            clienteUsername: mCliente.mUsuarioLogado!.username,
            professorNome: mCliente.mUsuarioLogado!.professor,
            fotoProfessor: fotoProfessor, 
            notaAvaliada: null,
          ));
          }
        }
        listaProfessoresAvaliados.addAll(value);
      }
      statusConsultaProfessores = ServiceStatus.Done;
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
      statusConsultaProfessores = ServiceStatus.Error;
    });
  }

  bool validarSeTemAvaliacaoFeitaAmaisDeTrintaDias(List<AvaliacaoDeProfessor> listaProfessoresAvaliados) {
    try {
      var listaProfessores = listaProfessoresAvaliados.where((element) => element.professorNome?.toLowerCase() == mCliente.mUsuarioLogado!.professor?.toLowerCase()).toList();
      listaProfessores.sort((a, b) => b.dataRegistro!.compareTo(a.dataRegistro!));
      if (listaProfessores.isNotEmpty) {
        var dataAtual = DateTime.now();
        var dataAvaliacao = DateTime.fromMillisecondsSinceEpoch(listaProfessores.first.dataRegistro!);
        var diferenca = UtilDataHora.daysBetween(inicioMilis: dataAtual.millisecondsSinceEpoch, fimMilis: dataAvaliacao.millisecondsSinceEpoch);
        return diferenca > 30 || diferenca < -30;
      } else {
        return true;
      }
    } catch (e) {
      return true;
    }
  }

  void avaliarProfessor({String? comentario, num? nota, Function()? carregando, Function()? sucesso, Function(String? falha)? falha}) {
    carregando?.call();
    statusConsultaProfessores = ServiceStatus.Waiting;
    var json = {
      'codUsuario': mCliente.mUsuarioLogado!.codUsuario!,
      'empresa': (mCliente.mUsuarioLogado?.codEmpresa ?? 1).toInt(),
      'clienteUsername': mCliente.mUsuarioLogado!.username,
      'professorCodigo': mCliente.mUsuarioLogado!.codigoProfessor,
      'professorNome': mCliente.mUsuarioLogado!.professor,
      'notaAvaliada': (nota ?? 0).toInt(),
      'comentario': comentario
    };
    treinoService.avaliarProfessor(json).then((value) {
      consultarAvaliacoesPorCliente(sucesso: () {
        sucesso?.call();
         statusConsultaProfessores = ServiceStatus.Done;
      }, falha: (error) {
        statusConsultaProfessores = ServiceStatus.Done;
        falha?.call(error.toString());
      }); 
    }).catchError((onError) {
      falha?.call(onError.message);
      statusConsultaProfessores = ServiceStatus.Done;
    });
  }
}
