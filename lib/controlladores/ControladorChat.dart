import 'dart:async';

import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/chat/ConversaChat.dart';
import 'package:app_treino/model/chat/MensagemChat.dart';
import 'package:app_treino/model/chat/UsuarioChat.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:diacritic/diacritic.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorChat.g.dart';

class ControladorChat = _ControladorChatBase with _$ControladorChat;

abstract class _ControladorChatBase with Store {
  @observable
  ObservableList<UsuarioChat> mUsuariosConversa = ObservableList<UsuarioChat>();
  Stream<QuerySnapshot>? listenerConversa;

  @observable
  bool carregandoConatos = true;

  @observable
  bool carregandoConversas = true;

  @observable
  ObservableList<UsuarioChat> mContatos = ObservableList<UsuarioChat>();
  @observable
  ObservableList<UsuarioChat> mContatosExibir = ObservableList<UsuarioChat>();

  @observable
  ObservableList<ConversaChat> mConversas = ObservableList<ConversaChat>();
  @observable
  ObservableList<MensagemChat> mMensagensConversa = ObservableList<MensagemChat>();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  final ControladorCliente _cliente = GetIt.I.get<ControladorCliente>();

  final ControladorApp _mApp = GetIt.I.get<ControladorApp>();

  void pegarDadosUsuario(
      String? refUsuario,
      Function(
    UsuarioChat usuarioChat,
  )
          callback) {
    if (mUsuariosConversa.any((element) => element.refUsuario == refUsuario)) {
      callback(mUsuariosConversa.firstWhere((element) => element.refUsuario == refUsuario));
      for (var i = 0; i < mConversas.length; i++) {
        if (mConversas[i].usuarios!.contains(refUsuario)) mConversas[i].usuarioMostrar = mUsuariosConversa.firstWhere((element) => element.refUsuario == refUsuario);
      }
    } else {
      _firestore.doc('UsuariosAPP/$refUsuario').get().then((value) {
        var usuario = UsuarioChat.fromJson(value.data()!);
        usuario.nome = capitalize(usuario.nome);
        usuario.refUsuario = value.reference.id;
        mUsuariosConversa.add(usuario);
        pegarDadosUsuario(refUsuario, callback);
      });
    }
  }

  void addMensagem(ConversaChat conversaChat, String conteudo, Function() callback) {
    var mensagem = MensagemChat(
      conteudo: conteudo,
      dataEnviada: DateTime.now(),
      usuario: _cliente.mUsuarioAuth!.uid,
    );
    int indexConversa = mConversas.indexWhere((element) => element.conversaID == conversaChat.conversaID);
    if (indexConversa == -1) {
      mConversas.add(conversaChat);
      indexConversa = mConversas.indexWhere((element) => element.conversaID == conversaChat.conversaID);
    }
    mConversas[indexConversa].ultimaMensagem = mensagem;
    mConversas[indexConversa].ultimaInteracao = mensagem.dataEnviada;
    mConversas.sort((b, a) => a.ultimaInteracao!.compareTo(b.ultimaInteracao!));
    _firestore.collection('chat/${conversaChat.conversaID}/mensagens').add(mensagem.toJson()).then((value) {
      callback();
    });
  }

  void carregarTrocaDeMensagens(ConversaChat conversaChat, {Function()? carregando, Function()? sucesso, Function(String falha)? falha}) {
    carregando?.call();
    mMensagensConversa.clear();
    listenerConversa = _firestore.collection('chat/${conversaChat.conversaID}/mensagens').orderBy('dataEnviada', descending: true).snapshots();

    listenerConversa!.listen((event) {
      mMensagensConversa.clear();
      for (final e in event.docs) {
        var mensagem = MensagemChat.fromJson(e.data() as Map<String, dynamic>);
        mensagem.refMensagem = e.id;
        if (!mMensagensConversa.any((element) => element.refMensagem == e.id)) {
          if (mensagem.dataEnviada != null) mMensagensConversa.add(mensagem);
          if (!mensagem.emissor && mensagem.dataLida == null) {
            e.reference.update({'dataLida': DateTime.now().microsecondsSinceEpoch});
          }
        }
      }
    }).onDone(() {
      sucesso?.call();
    });
  }

  void matarListenerConversa() {
    listenerConversa = null;
  }

  void carregarConversas({Function()? sucesso, Function(String falha)? falha, Function()? carregando}) {
    carregando?.call();
    carregandoConversas = true;
    _firestore.collection('chat').where('usuarios', arrayContains: _cliente.mUsuarioAuth!.uid).snapshots().listen((event) {
      mConversas.clear();
      try {
        for (final conversaSnap in event.docs) {
          try {
            ConversaChat mConversaParse = ConversaChat.fromJson(conversaSnap.data());
            if (mConversaParse.ultimaMensagem == null) continue;
            mConversaParse.usuarios?.removeWhere((element) => element == _cliente.mUsuarioAuth!.uid);
            if (mConversas.any((c) => c.conversaID == mConversaParse.conversaID)) {
              int index = mConversas.indexWhere((c) => c.conversaID == mConversaParse.conversaID);
              mConversas.removeAt(index);
              mConversas.add(mConversaParse);
            } else {
              mConversas.add(mConversaParse);
            }
            mConversas.sort((b, a) {
              try {
                return a.ultimaInteracao!.compareTo(b.ultimaInteracao!);
              } catch (e) {
                return 0;
              }
            });
          } catch (e) {
          }
        }
      } catch (e) {
      }
      carregandoConversas = false;
    }).onError((handleError) {
    });
  }

  obterConversa(UsuarioChat usuarioChat, {Function(ConversaChat conversaChat)? callback, Function(String falha)? falha, Function()? carregando}) {
    carregando?.call();
    if (mConversas.any((element) => element.usuarios!.contains(usuarioChat.refUsuario))) {
      callback!(mConversas.firstWhere((element) => element.usuarios!.contains(usuarioChat.refUsuario)));
    } else {
      var idConversa = _firestore.collection('rx').doc().id;
      var conversaCriar = ConversaChat(
        conversaID: idConversa,
        usuarios: [_cliente.mUsuarioAuth!.uid, usuarioChat.refUsuario],
        eConversaComNutri: false,
        ultimaMensagemLida: false,
        usuarioMostrar: usuarioChat,
        mensagens: [],
      );
      _firestore.doc('/chat/$idConversa').set(conversaCriar.toJson(), SetOptions(merge: true)).then((value) => callback!(conversaCriar)).catchError((onError) {
        falha?.call('Não foi possível iniciar uma conversa no momento');
      });
    }
  }

  void pesquisarContato(String nome) {
    mContatosExibir.clear();
    if (nome.isNotEmpty) {
      var termo = removeDiacritics(nome.toLowerCase());
      mContatosExibir.addAll(mContatos.where((element) => removeDiacritics(element.nome!.toLowerCase()).contains(termo)).toList());
    } else {
      mContatosExibir.addAll(mContatos);
    }
  }

  void carregarContatos({Function()? sucesso, Function(String falha)? falha, Function()? carregando}) {
    mContatos.clear();
    _firestore
        .collection('UsuariosAPP')
        .where('idClienteApp', isEqualTo: _mApp.mClienteAppSelecionado!.documentkey)
        .where('tipoPerfil', isEqualTo: _cliente.isUsuarioColaborador ? 'ALUNO' : 'COLABORADOR')
        .get()
        .then((value) {
      mContatos.addAll(value.docs.map((e) {
        var u = UsuarioChat.fromJson(e.data());
        u.nome = capitalize(u.nome);
        u.refUsuario = e.id;
        return u;
      }));
      pesquisarContato('');
      carregandoConatos = false;
    }).catchError((onError) {
      carregandoConatos = false;
    });
  }
}
