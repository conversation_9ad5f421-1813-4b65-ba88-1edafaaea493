import 'package:app_treino/ServiceProvider/authServices/ClienteAppService.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/notificacao/ConfiguracaoPushIA.dart';
import 'package:app_treino/model/notificacao/GrupoDeEnvioPushIA.dart';
import 'package:app_treino/screens/configuracoes/notificacoes/widgets/DSRangeDiasDaSemana.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_fonts.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorPushIa.g.dart';

class ControladorPushIA = _ControladorPushIABase with _$ControladorPushIA;

abstract class _ControladorPushIABase with Store {
  ConfiguracaoPushIA mConfigPushIa = ConfiguracaoPushIA();
  Map<int, Function()> mEventBus = {};
  ClienteAppService mService = GetIt.I.get<ClienteAppService>();
  @observable
  ServiceStatus statusConsultaPreferencias = ServiceStatus.Empty;

  BuildContext get contextApp => GetIt.I.get<NavigationService>().context;

  ControladorApp get mControladorApp => GetIt.I.get<ControladorApp>();

  ControladorCliente get mControladorCliente => GetIt.I.get<ControladorCliente>();

  /// Verifica se o dia da semana já está selecionado em outro grupo
  ///
  /// **index:**  Índice do grupo atual
  /// **weekday:** Dia da semana a ser verificado
  bool diaBloqueadoParaOGrupo(int index, DsWeekDays weekday) {
    return mConfigPushIa.gruposDeEnvio!.entries.any((e) => e.key != index && e.value.diasDaSemana.any((x) => x == weekday));
  }

  /// Lista de dias da semana já selecionados em todos os grupos
  List<DsWeekDays> get mDiasJaSelecionados {
    return mConfigPushIa.gruposDeEnvio?.values.map((e) => e.diasDaSemana).expand((l) => l).toList().toSet().toList() ?? [];
  }

  /// Limpa todos os tópicos de notificação PushIA do Firebase
  void limparTopicosPushIA() {
    mConfigPushIa.topicos().entries.forEach((entry) {
      for (final topic in entry.value) {
        FirebaseMessaging.instance.unsubscribeFromTopic(topic);
      }
    });
    mConfigPushIa = ConfiguracaoPushIA();
  }

  /// Inscreve e desinscreve os tópicos de notificação PushIA no Firebase
  void inscreverTopicosPushIA() {
    mConfigPushIa.topicos().entries.forEach((entry) {
      if (entry.key == 'escutar') {
        for (final topic in entry.value) {
          FirebaseMessaging.instance.subscribeToTopic(topic);
        }
      } else {
        for (final topic in entry.value) {
          FirebaseMessaging.instance.unsubscribeFromTopic(topic);
        }
      }
    });
  }

  /// Remove um grupo de notificação PushIA
  ///
  /// **grupo:** Grupo de notificação a ser removido
  void removerGrupo(GrupoDeEnvioPushIA grupo) {
    DSalerta().exibirAlerta(
        context: contextApp,
        titulo: localizedString('confpushiaremov'),
        subtitulo: localizedString('confpushiaremovn1'),
        tituloBotao: localizedString('delete'),
        tituloBotaoSecundario: localizedString('cancel'),
        onTap: () {
          Navigator.pop(contextApp);
          UtilitarioApp().showDialogCarregando(contextApp);
          statusConsultaPreferencias = ServiceStatus.Waiting;
          Future.delayed(const Duration(milliseconds: 300)).then((value) {
            mConfigPushIa.gruposDeEnvio!.removeWhere((k, v) => k == grupo.index);
            Map<int, GrupoDeEnvioPushIA> novoMap = {};
            int count = 0;
            for (final e in mConfigPushIa.gruposDeEnvio!.entries) {
              e.value.index = count;
              novoMap[count] = e.value;
              count++;
            }
            mConfigPushIa.gruposDeEnvio = novoMap;
            statusConsultaPreferencias = ServiceStatus.Done;
            Navigator.pop(contextApp);
            eventBus();
          });
        });
  }

  /// Consulta e salva as preferências de notificação PushIA
  ///
  /// **isSalvar:** Indica se as preferências devem ser salvas (true) ou apenas consultadas (false)
  void consultarSalvarPrefrerencias({Function()? carregando, Function()? sucesso, Function(String?)? falha, bool isSalvar = false}) {
    carregando?.call();

    statusConsultaPreferencias = ServiceStatus.Waiting;
    mConfigPushIa.chave = mControladorApp.chave;
    mConfigPushIa.codEmpresa = mControladorCliente.mUsuarioLogado!.codEmpresa;
    mConfigPushIa.codUsuario = mControladorCliente.mUsuarioLogado!.codUsuario;
    mConfigPushIa.refUsuario = mControladorCliente.mUsuarioAuth!.uid;
    late Future<ConfiguracaoPushIA> call;
    if (isSalvar) {
      call = mService.prefPushIaSalvar(mConfigPushIa);
    } else {
      call = mService.consultarPrefPushIa(mConfigPushIa);
    }
    call.then(
      (response) {
        mConfigPushIa = response;
        if (!mConfigPushIa.configSalva || mConfigPushIa.gruposDeEnvio == null) {
          mConfigPushIa = ConfiguracaoPushIA.sample();
        }
        statusConsultaPreferencias = ServiceStatus.Done;
        inscreverTopicosPushIA();
        sucesso?.call();
      },
    ).catchError((x) {
      statusConsultaPreferencias = ServiceStatus.Error;
      falha?.call(x.toString());
    });
  }

  /// Chama as funções do EventBus
  void eventBus() {
    mEventBus.forEach((key, value) {
      value();
    });
  }

  /// Atualiza as preferências de notificação PushIA
  ///
  /// **grupo:** Grupo de notificação a ser atualizado
  void updatePreferencia(GrupoDeEnvioPushIA grupo) {
    mConfigPushIa.gruposDeEnvio![grupo.index!] = grupo;
    eventBus();
  }

  /// Verifica se é possível adicionar um novo cartão de notificação PushIA
  ///
  /// **callback:** Função a ser chamada caso seja possível adicionar um novo grupo
  void podeAddNovoCard(Function() callback) {
    if (mDiasJaSelecionados.length == DsWeekDays.values.length) {
      DSalerta().exibirAlertaSimplificado(
        context: contextApp,
        titulo: 'confpushiarsemdias',
        subtitulo: 'subtitulo',
        tituloBotao: 'tituloBotao',
      );
      return;
    }
    if (mDiasJaSelecionados.isEmpty || (mConfigPushIa.gruposDeEnvio?.values.last.diasDaSemana.isEmpty ?? true)) {
      DSalerta().exibirAlertaSimplificado(
        context: contextApp,
        titulo: 'confpushiargrupoatual',
        subtitulo: 'confpushiaraddvazio',
        tituloBotao: 'entendido',
      );
      return;
    }
    if (mDiasJaSelecionados.length < DsWeekDays.values.length) {
      int index = mConfigPushIa.gruposDeEnvio!.entries.length;
      mConfigPushIa.gruposDeEnvio![index] = GrupoDeEnvioPushIA(index: index, diasDaSemana: []);
      callback();
    }
  }

  /// Divide um intervalo de data em partes iguais
  ///
  /// **startDate:** Data de início do intervalo
  /// **endDate:** Data de fim do intervalo
  /// **divisions:** Número de divisões do intervalo
  List<DateTimeRange> divideDateRange(DateTime startDate, DateTime endDate, int divisions) {
    List<DateTimeRange> ranges = [];
    List<DateTime> dates = [startDate];

    do {
      dates.add(dates.last.add(Duration(hours: divisions)));
    } while (!dates.any((d) => d.hour == (endDate.hour == 0 ? 23 : endDate.hour)));

    for (final date in dates) {
      ranges.add(DateTimeRange(start: date, end: date.add(const Duration(minutes: 59))));
    }

    return ranges;
  }
}
