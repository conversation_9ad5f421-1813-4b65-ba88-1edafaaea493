import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:app_treino/ServiceProvider/PrescricaoTreinoService.dart';
import 'package:app_treino/ServiceProvider/TreinoService.dart';
import 'package:app_treino/ServiceProvider/authServices/ServiceAuth.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorConversaAI.dart';
import 'package:app_treino/controlladores/ControladorDBExtend.dart';
import 'package:app_treino/controlladores/ControladorExecucaoTreino.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoTreino.dart';
import 'package:app_treino/controlladores/ControladorUsuarioApp.dart';
import 'package:app_treino/controlladores/controladorPlanner.dart';
import 'package:app_treino/fabricaGetIt.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/aulaTurma/AulaTurma.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/doClienteApp/TryLaterSubMitTreino.dart';
import 'package:app_treino/model/treinoAluno/BodyCriarTreinoIA.dart';
import 'package:app_treino/model/treinoAluno/ProgramadeTreino.dart';
import 'package:app_treino/model/treinoAluno/ResponseTrocaAtividade.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/screens/_treino6/anamnese_ia/TelaAnamnese.dart';
import 'package:app_treino/screens/prescricaoDeTreino/mockia/mock.ia.dart';
import 'package:app_treino/screens/treino/TelaConcluirTreino.dart';
import 'package:app_treino/screens/treino/TelaDescanso.dart';
import 'package:app_treino/util/debug_utils.dart';
import 'package:diacritic/diacritic.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sembast/sembast.dart';
import 'package:shared_preferences/shared_preferences.dart';
part 'ControladorTreinoAluno.g.dart';


class ControladorTreinoAluno = _ControladorTreinoAlunoBase with _$ControladorTreinoAluno;

abstract class _ControladorTreinoAlunoBase extends UtilDataBase with Store {
  final ControladorApp _controladorApp = GetIt.I.get<ControladorApp>();

  ValueNotifier<ServiceStatus> mTreinoEmExecucaoNotifier = ValueNotifier(ServiceStatus.Waiting);
  ValueNotifier<SituacaoValidacao> mTreinoIANotifier = ValueNotifier(SituacaoValidacao.NEGADO);
  @observable
  bool mTreinoEmExecucao = false;
  Function()? callBackPegarValores;

  var deslogou;

  bool mCronometroLigado = false;
  bool iniciando = false;

  @observable
  int mTempoIniciado = 0;

  @observable
  String mTempoDecorrido = '00:00';

  DateTime? horaInicioDoTreino;

  @observable
  ServiceStatus mStatusConsultaPrograma = ServiceStatus.Waiting;
  @observable
  ServiceStatus mStatusConsultaAtividadesRelacionadas = ServiceStatus.Waiting;

  int _tempoGasto = 0;
  int mAtividadesConcluidas = 0;
  bool? mControlaDescanso;

  @observable
  bool mProgramaAtualizou = false;

  @observable
  ProgramadeTreino? mProgramaCarregado;

  bool atividadeTemTrocaIa(bool emExecucao) {
    return emExecucao && (mProgramaCarregado?.programa?.geradoPorIA ?? false) && (GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.TDAIA).habilitado ?? false);
  }

  @observable
  SituacaoValidacao statusTreinoIASituacao = SituacaoValidacao.NEGADO;
  TreinoIAstatus statusTreinoIA = TreinoIAstatus();

  /// CORREÇÃO: Método centralizado para atualizar status IA e evitar inconsistências
  void updateStatusTreinoIA(SituacaoValidacao newStatus) {
    statusTreinoIASituacao = newStatus;
    mTreinoIANotifier.value = newStatus;
  }

  @observable
  Ficha? mFichaExibir;
  @observable
  ProgramaAtividade? mAtividadeProgramaExibir;
  @observable
  FichaAtividade? mAtividadeFichaExibir;
  @observable
  ObservableList<ExecucaoTreinoFirebase> mHistoricoExecucao = ObservableList<ExecucaoTreinoFirebase>();

  @observable
  int? mCodigoFichaExecucao;

  bool programaVenceuENaoPodeExibir = false;

  Timer? timer;
  resetAluno({Function()? carregando, Function()? sucesso}) async {
    carregando?.call();
    String? urlTreino = await ConfigURL().url(ConfigURL.TREINO);
    _mServiceTreino.resetarUsuario({
      'codEmpresa': 1,
      'chave': _controladorApp.chave,
      'ref': _controladorApp.mClienteAppSelecionado?.documentkey ?? '',
      'idPrograma': mProgramaCarregado?.programa?.cod,
      'idAluno': GetIt.I.get<ControladorCliente>().mUsuarioLogado?.cod,
      'urlTreino': urlTreino
    }).then((value) {
      sucesso?.call();
      statusTreinoIA = TreinoIAstatus(situacao: SituacaoValidacao.NEGADO);
    }).catchError((onError) {
      sucesso?.call();
    });
  }

  startTime() {
    mCronometroLigado = true;
    timer = Timer.periodic(const Duration(seconds: 1), (t) {
      if (mCronometroLigado) {
        // ignore: unnecessary_null_comparison, prefer_conditional_assignment
        if (mTempoIniciado == null) mTempoIniciado = DateTime.now().millisecondsSinceEpoch;
        _tempoGasto = (DateTime.now().millisecondsSinceEpoch - mTempoIniciado) ~/ 1000;
        // _tempoGasto =  _tempoGasto + 1;
        int minutos = (_tempoGasto ~/ 60);
        int segundos = (_tempoGasto % 60).toInt();
        String minS = minutos < 10 ? '0$minutos' : minutos.toString();
        String segS = segundos < 10 ? '0$segundos' : segundos.toString();
        mTempoDecorrido = '$minS:$segS';
      } else {
        t.cancel();
      }
    });
  }

  pauseTime() {
    mCronometroLigado = false;
    timer!.cancel();
  }

  void setFichaExibir(Ficha value) {
    mFichaExibir = value;
    // if(mProgramaCarregado.programa.getFichaDoDia().cod != mCodigoFichaExecucao)
    // mFichaExibir?.atividades?.forEach((element) {
    //     element.concluida = false;
    // });
  }

  void setAtividadeVerDetalhes(FichaAtividade valueA, ProgramaAtividade valueB) {
    mAtividadeProgramaExibir = valueB;
    mAtividadeFichaExibir = valueA;
  }

  final _mServiceTreino = GetIt.I.get<TreinoService>();
  PrescricaoTreinoService get _mServicePrescricao => GetIt.I.get<PrescricaoTreinoService>();
  final _controladorCliente = GetIt.I.get<ControladorCliente>();

  Future<void> salvarFotoAtividade(Uint8List foto, FichaAtividade atividade, {Function()? carregando, Function()? sucesso, Function(String x)? falha}) async {
    ListExerciciosOffline todosOsDonwloads = ListExerciciosOffline(todosExerciciosOff: []);
    carregando?.call();
    await buscarNoBanco('exerciciosOffline').then((value) {
      if (value != null) todosOsDonwloads = ListExerciciosOffline.fromJson(value);
      return getApplicationDocumentsDirectory();
    }).then((directory) {
      var index = '${todosOsDonwloads.todosExerciciosOff!.where((x) => x.codAtividade == atividade.atividade).length}';
      var path = '${directory.path}/${DateTime.now().millisecondsSinceEpoch}${atividade.atividade}-$index.jpg';
      todosOsDonwloads.todosExerciciosOff = todosOsDonwloads.todosExerciciosOff!.where((x) => !x.path!.contains('${atividade.atividade}-$index')).toList();
      todosOsDonwloads.todosExerciciosOff!.add(FotoUsuarioAtividade(codAtividade: atividade.atividade, path: path));
      File(path).writeAsBytesSync(foto);
      return inserirNoBanco('exerciciosOffline', todosOsDonwloads.toJson());
    }).then((value) {
      sucesso?.call();
    }).catchError((onError) {
      falha?.call('Não foi possível enviar a foto');
    });
  }

  Future<void> obterImagemLocalAtividade(
      {Function()? carregando, Function(List<FotoUsuarioAtividade> fotos)? sucesso, Function(String)? falha, bool force = true, FichaAtividade? mAtividade}) async {
    carregando?.call();
    ListExerciciosOffline todosOsDonwloads = ListExerciciosOffline(todosExerciciosOff: []);
    await buscarNoBanco('exerciciosOffline').then((value) async {
      if (value != null) todosOsDonwloads = ListExerciciosOffline.fromJson(value);
      if (Platform.isIOS) {
        return await getApplicationDocumentsDirectory();
      }
      return null;
    }).then((pathIOS) {
      if (pathIOS != null) {
        for (var i = 0; i < todosOsDonwloads.todosExerciciosOff!.length; i++) {
          var path = todosOsDonwloads.todosExerciciosOff![i].path;
          path = "${pathIOS.path}/${path!.split("/").last}";
          todosOsDonwloads.todosExerciciosOff![i].path = path;
        }
      }
      sucesso?.call(todosOsDonwloads.todosExerciciosOff?.where((element) => element.codAtividade == (mAtividade ?? mAtividadeFichaExibir)!.atividade).toList() ?? []);
    }).catchError((onError) {
      falha?.call('');
    });
  }

  void excluirFotoAtividade(String path, {Function()? carregando, Function()? sucesso, Function(String x)? falha}) {
    carregando?.call();
    Future.delayed(const Duration(seconds: 1)).then((_) async {
      ListExerciciosOffline todosOsDonwloads = ListExerciciosOffline(todosExerciciosOff: []);
      await buscarNoBanco('exerciciosOffline').then((value) {
        if (value != null) todosOsDonwloads = ListExerciciosOffline.fromJson(value);
        return getApplicationDocumentsDirectory();
      }).then((directory) {
        todosOsDonwloads.todosExerciciosOff = todosOsDonwloads.todosExerciciosOff?.where((x) => x.path != path).toList();
        File(path).deleteSync();
        return inserirNoBanco('exerciciosOffline', todosOsDonwloads.toJson());
      }).then((value) {
        sucesso?.call();
      }).catchError((onError) {
        falha?.call('Não foi possível excluir a foto');
      });
    });
  }

  carregarFichaDoDia(bool forceUpdate, AlunoNaAulaTurma alunos, {Function()? carregando, Function()? sucesso, Function()? falha, bool veioTelaListaFicha = false}) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    carregando?.call();
    mStatusConsultaPrograma = ServiceStatus.Waiting;
    //offlineSubmitTreino();
    if (prefs.getInt('codigoFichaExecucao') != null && veioTelaListaFicha) {
      forceUpdate = false;
    }
    if (forceUpdate || mProgramaCarregado == null || deslogou != null) {
      await _mServiceTreino.consultarProgramaAtual(alunos.matricula.toString()).then((value) {
        //value.programa!.fichas!.removeWhere((element) => (element!.atividades?.isEmpty ?? true));
        mProgramaCarregado = value;
        // Apenas consultar fila se houver configuracao de IA ativda e sem programa de treino
        this.statusTreinoIA = TreinoIAstatus(situacao: SituacaoValidacao.NEGADO);
        ajustaConclusoes();
        sucesso?.call();
        mProgramaCarregado != null ? mStatusConsultaPrograma = ServiceStatus.Done : mStatusConsultaPrograma = ServiceStatus.Empty;
      }).catchError((onError) async {
        falha?.call();
        mStatusConsultaPrograma = ServiceStatus.Error;
      });
    }
  }

  Future<void> proximoPassoPrescriaoIA(BuildContext context, EnumAnamnesePrescriaoIA atual) async {
    switch (atual) {
      // 1º Qual o objetivo
      case EnumAnamnesePrescriaoIA.objetivo:
        Navigator.pushNamed(context, '/telaAnamneseOnboardAtividade');
        break;
      // 2º Qual genero
      case EnumAnamnesePrescriaoIA.genero:
        Navigator.pushNamed(context, '/telaAnamneseOnboardIdade');
        break;
      // 3º Qual idade
      case EnumAnamnesePrescriaoIA.idade:
        Navigator.pushNamed(context, '/telaAnamneseOnboardAltura');
        break;
      // 4º Qual Altura
      case EnumAnamnesePrescriaoIA.altura:
        Navigator.pushNamed(context, '/telaAnamneseOnboardPeso');
        break;
      // 5º Qual Peso
      case EnumAnamnesePrescriaoIA.peso:
        // '/telaAnamneseOnboardNivel'
        // ** Navigator.pushNamed(context, '/telaAnamneseOnboardExperienciaTreino');
        Navigator.pushNamed(context, '/telaAnamneseOnboardNivel');
        break;
      // 6º Classificação de XP de treino
      case EnumAnamnesePrescriaoIA.experiencia:
        // '/telaAnamneseInicio'
        Navigator.pushNamed(context, '/telaAnamneseOnboardAtividade');
        break;
      // 7º Qual seu nível atual de treino
      case EnumAnamnesePrescriaoIA.nivel:
        // ** Navigator.pushNamed(context, '/telaAnamneseOnboardAtividade');
        Navigator.pushNamed(context, '/telaAnamneseOnboardExperienciaTreino');
        break;
      // 8º Quantos dias por semana Treina
      case EnumAnamnesePrescriaoIA.diasPorSemana:
        Navigator.pushNamed(context, '/telaAnamneseOnboardTempoPorDia');
        break;
      case EnumAnamnesePrescriaoIA.tempoPorDia:
        await DSalertaSucesso().exibirAlerta(context: context);
        solicitarTreinosIA(GetIt.I.get<ControladorUsuarioApp>().mCriarTreinoAluno);
        Navigator.of(context).pushNamed('/telaAnamneseInicio');
        break;
      case EnumAnamnesePrescriaoIA.restricao:
        break;
    }
    // Colocar no ultimo passo
//
  }

  Timer? _debounceConsultaIAPrograma;
  void consultarProgramaAoTerminarTempo() {
    if ((_controladorApp.getConfigWebTreino('PERMITIR_ALUNO_CRIAR_TREINO_IA_APP')) && statusTreinoIA.situacao != SituacaoValidacao.LIBERADO) {
      if (_debounceConsultaIAPrograma?.isActive ?? false) {
        _debounceConsultaIAPrograma?.cancel();
      }
      if (statusTreinoIA.obrigatorioProfValidar ?? false) {
        return;
      }
      _debounceConsultaIAPrograma = Timer(Duration(seconds: ((statusTreinoIA.segundosParaAprovacaoAutomatica))), () {
        // if (mProgramaCarregado != null) {
        //   statusTreinoIA = TreinoIAstatus(situacao: SituacaoValidacao.NEGADO);
        //   statusTreinoIASituacao = SituacaoValidacao.NEGADO;
        //   _debounceConsultaIAPrograma = null;
        //   mStatusConsultaPrograma = ServiceStatus.Waiting;
        //   Future.delayed(const Duration(milliseconds: 500), () {
        //     mStatusConsultaPrograma = ServiceStatus.Done;
        //   });
        //   return;
        // }
        if (programaVenceuENaoPodeExibir) {
          return;
        }
        GetIt.I.get<ControladorPlanner>().statusCarregandoFichaDoDia = ServiceStatus.Waiting;
        this.carregarProgramaDeTreino(true, carregando: () {}, sucesso: () {
          statusTreinoIA.situacao = SituacaoValidacao.NEGADO;
          // CORREÇÃO: Usar método centralizado
          updateStatusTreinoIA(SituacaoValidacao.NEGADO);
          GetIt.I.get<ControladorPlanner>().statusCarregandoFichaDoDia = ServiceStatus.Done;
        }, falha: (e) {
          statusTreinoIA.segundosParaAprovacaoAutomatica = 30;
          consultarProgramaAoTerminarTempo();
        }, consultarStatusIA: false);
        _debounceConsultaIAPrograma = null;
      });
    }
  }

  DateTime? horaSoliciacaoTreiniIA;
  Future<void> solicitarTreinosIA(BodyCriarTreinoIa body, {Function()? carregando, Function()? sucesso, Function(String?)? falha, bool force = true}) async {
    horaSoliciacaoTreiniIA ??= DateTime.now();
    updateStatusTreinoIA(SituacaoValidacao.processando);
    if (mProgramaCarregado?.programa?.cod != null) {
      mStatusConsultaPrograma = ServiceStatus.Done;
      GetIt.I.get<PrescricaoTreinoService>().deletarProgramaTreino(mProgramaCarregado!.programa!.cod!).then((onValue) {
        mProgramaCarregado = null;
        solicitarTreinosIA(
          body,
          sucesso: sucesso,
          falha: falha,
        );
      }).catchError((onError) {
        horaSoliciacaoTreiniIA = null;
        falha?.call('');
      });
      return;
    }
    body.currentCondition = CurrentConditionIA.Ativo;
    body.weight = num.tryParse(body.weight!.toStringAsFixed(2));
    body.clientId = GetIt.I.get<ControladorCliente>().mUsuarioLogado!.cod.toString();

    _mServiceTreino.solicitarTreinoParaIA(body).then((value) async {
      horaSoliciacaoTreiniIA = null;
      mProgramaCarregado = null;
      mStatusConsultaPrograma = ServiceStatus.Done;
      this.carregarProgramaDeTreino(true, carregando: () {
        statusTreinoIA.segundosParaAprovacaoAutomatica = 10;
        updateStatusTreinoIA(SituacaoValidacao.processando);
      }, sucesso: () {
        // consultarProgramaAoTerminarTempo();
      }, falha: (e) {
        statusTreinoIA.segundosParaAprovacaoAutomatica = 30;
        // consultarProgramaAoTerminarTempo();
      }, consultarStatusIA: false);
      if (GetIt.I.get<ControladorApp>().chave == 'd337c1cc81b670547f476d247186fa8f') {
        try {
          await GetIt.I.get<ControladorConversaAI>().iniciarFaseAI(
            fase: 'PRIMEIRO_PROGRAMA_TREINO',
            carregando: () {},
            sucesso: () {},
            erro: (erro) {},
          );
        } catch (e) {
          log(e.toString());
        }
      }
      sucesso?.call();
    }).catchError((onError) {
      horaSoliciacaoTreiniIA = null;
      // CORREÇÃO: Usar método centralizado
      updateStatusTreinoIA(SituacaoValidacao.LIBERADO);
      falha?.call('');
    });
  }

  carregarProgramaDeTreino(bool forceUpdate, {Function()? carregando, Function()? sucesso, Function(String e)? falha, bool veioTelaListaFicha = false, bool consultarStatusIA = true}) async {
    var temp = await buscarNoBanco('programaAtualAluno');
    SharedPreferences prefs = await SharedPreferences.getInstance();
    mControlaDescanso = prefs.getBool('controlarDescanso') ?? null;
    carregando?.call();
    mStatusConsultaPrograma = ServiceStatus.Waiting;
    mTreinoEmExecucaoNotifier.value = mStatusConsultaPrograma;
    //offlineSubmitTreino();
    if (prefs.getInt('codigoFichaExecucao') != null && veioTelaListaFicha) {
      forceUpdate = false;
    }
    if (temp != null && (!forceUpdate || mTreinoEmExecucao)) {
      mProgramaCarregado = ProgramadeTreino.fromJson(temp);
      if (!mTreinoEmExecucao) ajustaConclusoes();
      sucesso?.call();
      mStatusConsultaPrograma = ServiceStatus.Done;
      mTreinoEmExecucaoNotifier.value = mStatusConsultaPrograma;
    } else if (forceUpdate || mProgramaCarregado == null || deslogou != null) {
      _mServiceTreino.consultarProgramaAtual(_controladorCliente.mUsuarioLogado!.matricula).then((value) {
        programaVenceuENaoPodeExibir = false;
        deslogou = null;
        // value.programa!.fichas!.removeWhere((element) => element!.atividades == null || element.atividades!.isEmpty);
        if (temp == null) {
          inserirNoBanco('programaAtualAluno', value.toJson()).then((valor) {
            mProgramaCarregado = value;
            ajustaConclusoes();
            sucesso?.call();
            mStatusConsultaPrograma = ServiceStatus.Done;
            mTreinoEmExecucaoNotifier.value = mStatusConsultaPrograma;
          });
        } else {
          inserirNoBanco('programaAtualAluno', value.toJson(), true).then((valor) {
            mProgramaCarregado = value;
            ajustaConclusoes();
            sucesso?.call();
            mStatusConsultaPrograma = ServiceStatus.Done;
            mTreinoEmExecucaoNotifier.value = mStatusConsultaPrograma;
          });
        }
      }).catchError((onError) async {
        if (consultarStatusIA) {
          try {
            if (_controladorApp.getConfigWebTreino('PERMITIR_ALUNO_CRIAR_TREINO_IA_APP')) {
              statusTreinoIA = TreinoIAstatus.fromMensagem(mensagem: onError?.message.toString(), data: onError.response?.toString());
              statusTreinoIASituacao = statusTreinoIA.getsituacao;
              mTreinoIANotifier.value = statusTreinoIASituacao;
            }
          } catch (e) {}
          consultarProgramaAoTerminarTempo();
          GetIt.I.get<ControladorPlanner>().statusCarregandoFichaDoDia = ServiceStatus.Error;
          if (onError.message?.toString().toLowerCase().contains('aluno não possui autorização de acesso.') ?? false) {
            mProgramaCarregado = null;
            programaVenceuENaoPodeExibir = true;
            statusTreinoIASituacao = SituacaoValidacao.NEGADO;
            mTreinoIANotifier.value = statusTreinoIASituacao;
            mStatusConsultaPrograma = ServiceStatus.Done;
            mTreinoEmExecucaoNotifier.value = mStatusConsultaPrograma;
          } else if (onError.message?.toString().toLowerCase().contains('Treino vencido. Contate seu Professor!'.toLowerCase()) ?? false) {
            programaVenceuENaoPodeExibir = true;

            sucesso?.call();
          } else if (onError.message?.toString().toLowerCase().contains('você já executou') ?? false) {
            programaVenceuENaoPodeExibir = true;

            sucesso?.call();
          } else if (onError.message?.toString().toLowerCase().contains('professor está realizando a validação do seu treino') ?? false) {
            sucesso?.call();
          } else if (onError.message?.toString().toLowerCase().contains('nenhum treino. contate seu professor!') ?? false) {
            mProgramaCarregado = null;
            if (statusTreinoIA.situacao == SituacaoValidacao.LIBERADO) {
              GetIt.I.get<ControladorPlanner>().statusCarregandoFichaDoDia = ServiceStatus.Done;
            }
            programaVenceuENaoPodeExibir = true;

            sucesso?.call();
          } else if (onError?.message?.toString().contains('Sem conexão com a internet') ?? false) {
            if (temp != null || mTreinoEmExecucao) {
              mProgramaCarregado = ProgramadeTreino.fromJson(temp!);
              if (!mTreinoEmExecucao) ajustaConclusoes();
              sucesso?.call();
              mStatusConsultaPrograma = ServiceStatus.Done;
              mTreinoEmExecucaoNotifier.value = mStatusConsultaPrograma;
              return;
            }
          }
          GetIt.I.get<ControladorPlanner>().statusCarregandoFichaDoDia = ServiceStatus.Error;

          if (statusTreinoIASituacao.isErro()) {
            GetIt.I.get<ControladorPlanner>().statusCarregandoFichaDoDia = ServiceStatus.Done;
          } else if (statusTreinoIASituacao == SituacaoValidacao.aguardando) {
            GetIt.I.get<ControladorPlanner>().statusCarregandoFichaDoDia = ServiceStatus.Done;
          }
          falha?.call(onError.message ?? '');
        }
      });
    }
  }

  Future<void> carregarProgramaDeTreinoColaborador(
    bool forceUpdate, {
    Function()? carregando,
    Function()? sucesso,
    Function(String e)? falha,
    bool veioTelaListaFicha = false,
    bool consultarStatusIA = true,
  }) async {
    var temp = await buscarNoBanco('programaAtualAluno');
    SharedPreferences prefs = await SharedPreferences.getInstance();
    mControlaDescanso = prefs.getBool('controlarDescanso') ?? null;
    carregando?.call();
    mStatusConsultaPrograma = ServiceStatus.Waiting;

    if (prefs.getInt('codigoFichaExecucao') != null && veioTelaListaFicha) {
      forceUpdate = false;
    }
    if (temp != null && (!forceUpdate || mTreinoEmExecucao)) {
      mProgramaCarregado = ProgramadeTreino.fromJson(temp);
      if (!mTreinoEmExecucao) ajustaConclusoes();
      sucesso?.call();
      mStatusConsultaPrograma = ServiceStatus.Done;
    } else if (forceUpdate || mProgramaCarregado == null || deslogou != null) {
      _mServiceTreino.consultarProgramaAtualColaborador('C0LAB_${_controladorCliente.mUsuarioLogado?.codigoColaborador}').then((value) {
        programaVenceuENaoPodeExibir = false;
        deslogou = null;
        if (temp == null) {
          inserirNoBanco('programaAtualAluno', value.toJson()).then((valor) {
            mProgramaCarregado = value;
            ajustaConclusoes();
            sucesso?.call();
            mStatusConsultaPrograma = ServiceStatus.Done;
          });
        } else {
          inserirNoBanco('programaAtualAluno', value.toJson(), true).then((valor) {
            mProgramaCarregado = value;
            ajustaConclusoes();
            sucesso?.call();
            mStatusConsultaPrograma = ServiceStatus.Done;
          });
        }
      }).catchError((onError) async {
        GetIt.I.get<ControladorPlanner>().statusCarregandoFichaDoDia = ServiceStatus.Error;
        if (onError.message?.toString().toLowerCase().contains('colaborador não possui autorização de acesso.') ?? false) {
          mProgramaCarregado = null;
          programaVenceuENaoPodeExibir = true;
          mStatusConsultaPrograma = ServiceStatus.Done;
        } else if (onError.message?.toString().toLowerCase().contains('Treino vencido. Contate seu Professor!'.toLowerCase()) ?? false) {
          programaVenceuENaoPodeExibir = true;
          sucesso?.call();
        } else if (onError.message?.toString().toLowerCase().contains('você já executou') ?? false) {
          programaVenceuENaoPodeExibir = true;
          sucesso?.call();
        } else if (onError.message?.toString().toLowerCase().contains('professor está realizando a validação do seu treino') ?? false) {
          sucesso?.call();
        } else if (onError.message?.toString().toLowerCase().contains('nenhum treino. contate seu professor!') ?? false) {
          mProgramaCarregado = null;
          programaVenceuENaoPodeExibir = true;
          sucesso?.call();
        } else if (onError?.message?.toString().contains('Sem conexão com a internet') ?? false) {
          if (temp != null || mTreinoEmExecucao) {
            mProgramaCarregado = ProgramadeTreino.fromJson(temp!);
            if (!mTreinoEmExecucao) ajustaConclusoes();
            sucesso?.call();
            mStatusConsultaPrograma = ServiceStatus.Done;
          } else {
            mProgramaCarregado = null;
            programaVenceuENaoPodeExibir = true;
            sucesso?.call();
          }
        }
        GetIt.I.get<ControladorPlanner>().statusCarregandoFichaDoDia = ServiceStatus.Error;
        falha?.call(onError.message);
      });
    }
  }

  concluirTreino(int nota, String comentario, {Function()? carregando, Function()? sucesso, Function()? falha}) async {
    analytic(EventosKey.treinoExecucao_concluir);
    carregando?.call();
    resetarTreino();
    await enviarTreinoFirebase();

    _mServiceTreino
        .concluirTreino(_controladorCliente.mUsuarioLogado!.username!, mProgramaCarregado!.programa!.cod!.toInt(), mFichaExibir!.cod!.toInt(),
            UtilDataHora.getDiaMesAno(dateTime: DateTime.now()), nota, _tempoGasto, comentario, _controladorCliente.mUsuarioLogado!.matricula!)
        .then((value) {
      // ignore: unnecessary_null_comparison
      if (value != null) {
        var index = mProgramaCarregado!.programa!.fichas!.indexWhere((element) => mFichaExibir!.cod == element!.cod);
        mProgramaCarregado!.programa!.fichas![index]!.ultimaExecucaoLong = DateTime.now().millisecondsSinceEpoch;
        mProgramaAtualizou = true;
        mProgramaCarregado!.acompanhamento = value;
        inserirNoBanco('programaAtualAluno', mProgramaCarregado!.toJson(), true);
        ajustaConclusoes();
        sucesso!();
        analytic(EventosKey.TREINO_CONCLUIDO_APP_TREINO);
        if (nota == 5) {
          analytic(EventosKey.avaliou_treino);
        }

        UtilitarioApp.showAppReview();
      } else {
        UtilitarioApp.showAppReview();
        falha!();
      }
    }).catchError((onError) async {
      falha?.call();
      var offstore = intMapStoreFactory.store('trylater');
      await getDb.transaction((txn) async {
        await offstore.add(
            txn,
            TryLaterSubMitTreino(
                    username: _controladorCliente.mUsuarioLogado!.username,
                    idPrograma: mProgramaCarregado!.programa!.cod,
                    idFicha: mFichaExibir!.cod!.toInt(),
                    dia: UtilDataHora.getDiaMesAno(dateTime: DateTime.now()),
                    comentario: comentario,
                    nota: nota,
                    tempo: _tempoGasto)
                .toJson());
      });
    });
  }

  concluirTreinoPrimeiraVezes(int nota, String comentario, {Function()? carregando, Function()? sucesso, Function()? falha}) async {
    //analytic(EventosKey.treinoExecucao_concluir_automaticamente);
    carregando?.call();
    await enviarTreinoFirebase();
    _mServiceTreino
        .concluirTreino(_controladorCliente.mUsuarioLogado!.username!, mProgramaCarregado!.programa!.cod!.toInt(), mFichaExibir!.cod!.toInt(),
            UtilDataHora.getDiaMesAno(dateTime: DateTime.now()), nota, _tempoGasto, comentario, _controladorCliente.mUsuarioLogado!.matricula!)
        .then((value) {
      sucesso!();
    }).catchError((onError) async {
      falha?.call();
      var offstore = intMapStoreFactory.store('trylater');
      await getDb.transaction((txn) async {
        await offstore.add(
            txn,
            TryLaterSubMitTreino(
                    username: _controladorCliente.mUsuarioLogado!.username,
                    idPrograma: mProgramaCarregado!.programa!.cod,
                    idFicha: mFichaExibir!.cod!.toInt(),
                    dia: UtilDataHora.getDiaMesAno(dateTime: DateTime.now()),
                    comentario: comentario,
                    nota: nota,
                    tempo: _tempoGasto)
                .toJson());
      });
    });
  }

  void offlineSubmitTreino() {
    if (!_controladorCliente.isUsuarioColaborador) {
      var offstore = intMapStoreFactory.store('trylater');
      List<TryLaterSubMitTreino> mList = [];
      offstore.find(getDb).then((value) async {
        for (final element in value) {
          mList.add(TryLaterSubMitTreino.fromJson(element.value));
        }
        bool algumDeuCerto = false;
        for (final tryThat in mList) {
          dynamic result;
          try {
            result = await _mServiceTreino.concluirTreino(tryThat.username!, tryThat.idPrograma!.toInt(), tryThat.idFicha!.toInt(), tryThat.dia!, tryThat.nota!.toInt(),
                tryThat.tempo!.toInt(), tryThat.comentario!, _controladorCliente.mUsuarioLogado!.matricula);
          } catch (e) {}
          if (result != null) {
            algumDeuCerto = true;
            offstore.delete(getDb, finder: Finder(filter: Filter.equals('idFicha', tryThat.idFicha)));
          }
        }
        if (algumDeuCerto) carregarProgramaDeTreino(true);
      });
    }
  }

  enviarTreinoFirebase() {
    var treinoExecutado = ExecucaoTreinoFirebase(
        categoria: mFichaExibir!.categoria!,
        dataConclusao: DateTime.now().millisecondsSinceEpoch,
        nome: mFichaExibir!.nome,
        tempoTotalEmexecusao: _tempoGasto,
        quantidadeAtividadesConcluidas: (mFichaExibir?.atividades?.where((a) => (a.concluida ?? false)).length ?? 0));
    Map<String, dynamic> treinoExecutadoJSON = {
      'imagemKey': getTagImagem(mFichaExibir!),
      'categoria': mFichaExibir!.categoria,
      'dataConclusao': DateTime.now().millisecondsSinceEpoch,
      'nome': mFichaExibir!.nome,
      'tempoTotalEmexecusao': _tempoGasto,
      'quantidadeAtividadesConcluidas ': (mFichaExibir?.atividades?.where((a) => (a.concluida ?? false)).length ?? 0),
      'refClienteApp': _controladorApp.mClienteAppSelecionado?.documentkey ?? '-',
      'chaveZw': _controladorApp.chave,
      'codigoEmpresa': _controladorCliente.mUsuarioLogado?.codEmpresa,
    };
    _mServiceTreino.enviarTreinoFirebase(treinoExecutadoJSON, GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid!).then((value) {
      treinoExecutado.key = DateTime.now().millisecondsSinceEpoch.toString();
      mHistoricoExecucao.insert(0, treinoExecutado);
      callBackPegarValores?.call();
    }).catchError((onError) {});
  }

  List<ExecucaoTreinoFirebase> mFichasConcluidas = [];

  consultarHistoricoTreinoFirebase({Function()? carregando, Function()? sucesso, Function()? falha}) async {
    _mServiceTreino.consultarHistoricoExecucao(_controladorCliente.mUsuarioAuth!.uid!).then((value) async {
      mHistoricoExecucao.clear();
      mFichasConcluidas.clear();
      for (final ExecucaoTreinoFirebase exec in value) {
        if (exec.dataConclusao != null) mHistoricoExecucao.add(exec);
      }
      mHistoricoExecucao.sort((a, b) => b.dataConclusao!.compareTo(a.dataConclusao!));
      if (mHistoricoExecucao.length > 1) {
        for (var i = 0; i < mHistoricoExecucao.length; i++) {
          if (mHistoricoExecucao[i].nome == mHistoricoExecucao[i + 1].nome &&
              UtilDataHora.getMesAno(dataMilis: mHistoricoExecucao[i].dataConclusao) == UtilDataHora.getMesAno(dataMilis: mHistoricoExecucao[i + 1].dataConclusao)) {
            if ((mHistoricoExecucao[i].quantidadeAtividadesConcluidas == 0) || (mHistoricoExecucao[i].tempoTotalEmexecusao == 0)) {
              mHistoricoExecucao.removeAt(i);
            } else if ((mHistoricoExecucao[i + 1].quantidadeAtividadesConcluidas == 0) || (mHistoricoExecucao[i + 1].tempoTotalEmexecusao == 0)) {
              mHistoricoExecucao.removeAt(i + 1);
            }
          }
        }
      }
      mFichasConcluidas.addAll(mHistoricoExecucao);
      sucesso!();
    }).catchError((onError) {
      falha!();
    });
  }

  setControleDescanso(bool controlar) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool('controlarDescanso', controlar);
    mControlaDescanso = controlar;
  }

  ajustaConclusoes() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    for (final Ficha? ficha in mProgramaCarregado!.programa!.fichas!) {
      if (ficha!.ultimaExecucao == null) {
        ficha.ultimaExecucao = '1970-01-01 00:00:00';
        ficha.ultimaExecucaoLong = 10800000;
        ficha.ultimoTreino = '01/01/1970';
        ficha.ultimoTreinoLong = 10800000;
      }
      for (final FichaAtividade atividade in ficha.atividades!) {
        if (atividade.series!.isEmpty) {
          atividade.concluida = true;
        }
      }
      DateTime ultimaExecucaoDia = DateTime.fromMillisecondsSinceEpoch(ficha.ultimaExecucaoLong!.toInt());
      if (ficha.ultimoTreinoLong != null) {
        if (ficha.ultimoTreinoLong != 0) {
          if (ficha.ultimaExecucaoLong! > ficha.ultimoTreinoLong!.toInt()) {
            ficha.ultimoTreino = UtilDataHora.getDiaMesAno(dataMilis: ficha.ultimaExecucaoLong);
          }
        } else if (UtilDataHora.getDiaMesAno(dataMilis: ultimaExecucaoDia.millisecondsSinceEpoch) == UtilDataHora.getDiaMesAno(dataMilis: DateTime.now().millisecondsSinceEpoch)) {
          ficha.ultimoTreino = UtilDataHora.getDiaMesAno(dataMilis: ultimaExecucaoDia.millisecondsSinceEpoch);
        }
      } else if (UtilDataHora.getDiaMesAno(dataMilis: ultimaExecucaoDia.millisecondsSinceEpoch) == UtilDataHora.getDiaMesAno(dataMilis: DateTime.now().millisecondsSinceEpoch)) {
        ficha.ultimoTreino = UtilDataHora.getDiaMesAno(dataMilis: ultimaExecucaoDia.millisecondsSinceEpoch);
      }
      if (ficha.ultimoTreino == UtilDataHora.getDiaMesAno(dataMilis: DateTime.now().millisecondsSinceEpoch)) {
        if (prefs.getBool('fichaIniciada') ?? false) {
          if (ficha.cod == (prefs.getInt('codigoFichaExecucao') ?? 0)) {
            ficha.concluida = false;
            continuarTreino();
            if ((ficha.atividades != null && ficha.atividades!.isNotEmpty) && prefs.getStringList('atividadesConcluidas') != null) {
              concluirAtividadesDefaultPreparacao(ficha.atividades!, ficha);
              for (final FichaAtividade atv in ficha.atividades!) {
                if ((atv.series != null && atv.series!.isNotEmpty) && prefs.getStringList('seriesConcluidas') != null) {
                  concluirSeriesDefaultPreparacao(atv.series!);
                }
              }
            }
          } else {
            ficha.concluida = false;
            resetarAtividades(ficha);
            await resetarSeries(ficha);
          }
        } else if (UtilDataHora.getDiaMesAno(dataMilis: ficha.ultimaExecucaoLong) == UtilDataHora.getDiaMesAno(dataMilis: DateTime.now().millisecondsSinceEpoch)) {
          for (final FichaAtividade atividade in ficha.atividades!) {
            atividade.concluida = true;
            concluirAtividade(atividade.concluida!, atividade.atividade!, ficha);
          }
          ficha.concluida = true;
        } else {
          ficha.concluida = false;
          resetarAtividades(ficha);
          await resetarSeries(ficha);
        }
      } else {
        if (UtilDataHora.getDiaMesAno(dataMilis: ficha.ultimaExecucaoLong) == UtilDataHora.getDiaMesAno(dataMilis: DateTime.now().millisecondsSinceEpoch)) {
          for (final FichaAtividade atividade in ficha.atividades!) {
            atividade.concluida = true;
            concluirAtividade(atividade.concluida!, atividade.atividade!, ficha);
          }
          ficha.concluida = true;
        } else {
          ficha.concluida = false;
          resetarAtividades(ficha);
          await resetarSeries(ficha);
        }
      }
    }
  }

  bool get programaDeTreinoVencido {
    try {
      if (mProgramaCarregado?.programa?.dataTerminoPrevisto == null) {
        return false;
      }
      mProgramaCarregado!.programa!.dataTerminoPrevisto =
          UtilDataHora.parseStringToDate('${UtilDataHora.getDiaMesAno(dateTime: mProgramaCarregado!.programa!.dataTerminoPrevisto)} 23:59:00', minutos: true);
      return DateTime.now().isAfter(mProgramaCarregado!.programa!.dataTerminoPrevisto!);
    } catch (e) {
      return false;
    }
  }

  bool estaFichaEmExecucao() {
    return mFichaExibir!.cod == mCodigoFichaExecucao;
  }

  fichaEmExecucao() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (prefs.getInt('dataFichaIniciada') != null) {
      if (UtilDataHora.dataPassouUmDia(dataMilis: prefs.getInt('dataFichaIniciada')) &&
          !(GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.TREINO_NAO_FIANLIZAR_MEIA_NOITE).habilitado ?? false)) {
        //ajustaConclusoes();
        resetarTreino();
        carregarProgramaDeTreino(true);
        analytic(EventosKey.treinoExecucao_concluir_automaticamente);
      }
    }
  }

  iniciarTreino() async {
    analytic(EventosKey.treino_iniciar);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool('fichaIniciada', true);
    prefs.setInt('codigoFichaExecucao', mFichaExibir!.cod!.toInt());
    if (mFichaExibir!.cod!.toInt() == mProgramaCarregado!.programa!.getFichaDoDia?.cod?.toInt()) {
      prefs.setBool('isTreinoDoDiaEmExecucao', true);
    }
    prefs.setInt('dataFichaIniciada', DateTime.now().millisecondsSinceEpoch);
    mTreinoEmExecucao = true;
    mCodigoFichaExecucao = mFichaExibir!.cod!.toInt();
    mTempoIniciado = DateTime.now().millisecondsSinceEpoch;
    atualizaFicha(mTempoIniciado);
    _tempoGasto = 0;
    startTime();
  }

  resetarTreino() async {
    try {
      mTreinoEmExecucao = false;
      mProgramaCarregado?.programa?.fichaDoDiaTemp = null;
      analytic(EventosKey.treinoExecucao_sair);
      SharedPreferences prefs = await SharedPreferences.getInstance();
      prefs.remove('fichaIniciada');
      prefs.remove('dataFichaIniciada');
      prefs.remove('atividadesExecutadas');
      prefs.remove('isTreinoDoDiaEmExecucao');
      prefs.remove('listAtividadesConcluidasDaFichaEmExecucao');
      mAtividadesConcluidas = 0;
      mCodigoFichaExecucao = null;
      for (final Ficha? ficha in mProgramaCarregado!.programa!.fichas!) {
        if (ficha!.cod == prefs.getInt('codigoFichaExecucao')) {
          resetarAtividades(ficha);
          await resetarSeries(ficha);
          prefs.remove('codigoFichaExecucao');
          break;
        }
      }
      mTempoIniciado = 0;
      _tempoGasto = 0;
      mTempoDecorrido = '00:00';
      timer?.cancel();
    } catch (e) {
      createAlbum(FlutterErrorDetails(exception: e));
    }
  }

  continuarTreino() async {
    analytic(EventosKey.treino_iniciar);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    mTreinoEmExecucao = true;
    mCodigoFichaExecucao = prefs.getInt('codigoFichaExecucao');
    mAtividadesConcluidas = prefs.getInt('atividadesExecutadas') ?? 0;
    mTempoIniciado = prefs.getInt('dataFichaIniciada')!;
    _tempoGasto = ((DateTime.now().millisecondsSinceEpoch - mTempoIniciado) ~/ 1000);
    startTime();
  }

  concluirAtividadesDefaultPreparacao(List<FichaAtividade> listaAtividades, Ficha ficha) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> atvs = prefs.getStringList('atividadesConcluidas') ?? [];
    if (atvs.isNotEmpty && listaAtividades.isNotEmpty) {
      for (final FichaAtividade at in listaAtividades) {
        if (atvs.contains(at.atividade)) {
          at.concluida = true;
          concluirAtividade(true, at.atividade!, ficha);
        }
      }
    }
  }

  concluirSeriesDefaultPreparacao(List<SeriesAtividade> listaSeries) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> srs = prefs.getStringList('seriesConcluidas') ?? [];
    if (srs.isNotEmpty && listaSeries.isNotEmpty) {
      for (final SeriesAtividade sr in listaSeries) {
        if (srs.contains('${sr.codSerie}')) {
          sr.concluida = true;
          await concluirSerie(sr.codSerie!.toInt(), () {});
        }
      }
    }
  }

  concluirAtividadesDefault(bool conclusao, String codAtividade) async {
    analytic(EventosKey.treinoExecucao_concluirAtividade);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (prefs.getStringList('atividadesConcluidas') != null) {
      List<String> atvs = prefs.getStringList('atividadesConcluidas')!;
      if (conclusao) {
        atvs.add(codAtividade);
      } else {
        atvs.removeWhere((element) => element == codAtividade);
      }
      prefs.setStringList('atividadesConcluidas', atvs);
    } else {
      prefs.setStringList('atividadesConcluidas', []);
      concluirAtividadesDefault(conclusao, codAtividade);
    }
  }

  concluirSeriesDefault(bool conclusao, String codSerie) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (prefs.getStringList('seriesConcluidas') != null) {
      List<String> series = prefs.getStringList('seriesConcluidas')!;
      if (conclusao) {
        series.add(codSerie);
      } else {
        series.removeWhere((element) => element == codSerie);
      }
      prefs.setStringList('seriesConcluidas', series);
    } else {
      prefs.setStringList('seriesConcluidas', []);
      concluirSeriesDefault(conclusao, codSerie);
    }
  }

  resetarAtividades(Ficha ficha) {
    for (final FichaAtividade atividade in ficha.atividades!) {
      atividade.concluida = false;
      inserirNoBanco('programaAtualAluno', mProgramaCarregado!.toJson(), true);
    }
  }

  Future<void> resetarSeries(Ficha ficha) async {
    final List<Future<void>> futures = [];

    for (final FichaAtividade atividade in ficha.atividades!) {
      for (final SeriesAtividade serie in atividade.series!) {
        serie.concluida = false;
        futures.add(inserirNoBanco('programaAtualAluno', mProgramaCarregado!.toJson(), true));
      }
    }

    await Future.wait(futures);
  }

  Future<void> concluirSerie(int codSerie, Function()? callback) async {
    outer:
    for (final Ficha? ficha in mProgramaCarregado!.programa!.fichas!) {
      for (final FichaAtividade atividade in ficha!.atividades!) {
        for (final SeriesAtividade serie in atividade.series!) {
          if (serie.codSerie == codSerie) {
            serie.concluida = true;
            break outer;
          }
        }
      }
    }
    inserirNoBanco('programaAtualAluno', mProgramaCarregado!.toJson());
    callback!();
  }

  concluirAtividade(bool conclusao, String? codAtividade, Ficha ficha, {Function()? callback}) async {
    var prefs = await SharedPreferences.getInstance();
    var listAtividadesConcluidasDaFichaEmExecucao = prefs.getStringList('listAtividadesConcluidasDaFichaEmExecucao') ?? [];
    listAtividadesConcluidasDaFichaEmExecucao.add(codAtividade ?? '');
    prefs.setStringList('listAtividadesConcluidasDaFichaEmExecucao', listAtividadesConcluidasDaFichaEmExecucao);
    analytic(EventosKey.treinoExecucao_concluirAtividade);
    if (!conclusao) {
      if (callback != null) callback();
    } else {
      if ((mProgramaCarregado?.programa?.fichas?.length ?? 0) > 0) {
        outer:
        for (int index = 0; index <= mProgramaCarregado!.programa!.fichas!.length; index++) {
          if (mProgramaCarregado?.programa?.fichas?.length != null &&
              index < mProgramaCarregado!.programa!.fichas!.length &&
              mProgramaCarregado?.programa?.fichas![index]?.cod == ficha.cod) {
            for (final FichaAtividade atividade in ficha.atividades!) {
              if (atividade.atividade != null) {
                if (atividade.atividade?.contains(codAtividade ?? '0') ?? false) {
                  if (mFichaExibir?.atividades != null) {
                    try {
                      mAtividadesConcluidas = mFichaExibir!.atividades!.where((element) => (element.concluida ?? false)).length;
                    } catch (error) {
                      mAtividadesConcluidas = 0;
                    }
                    atividade.concluida = true;
                    mAtividadesConcluidas++;
                    mProgramaCarregado!.programa!.fichas![index] = ficha;
                    break outer;
                  }
                }
              }
            }
          }
        }
      }
      inserirNoBanco('programaAtualAluno', mProgramaCarregado!.toJson());
      if (callback != null) callback();
    }
  }

  atualizaFicha(int ultimoTreinoLong) {
    for (final Ficha? ficha in mProgramaCarregado!.programa!.fichas!) {
      if (ficha!.cod == mFichaExibir!.cod) {
        ficha.ultimoTreinoLong = ultimoTreinoLong;
        ficha.ultimoTreino = UtilDataHora.getDiaMesAno(dataMilis: ultimoTreinoLong);
        break;
      }
    }
    inserirNoBanco('programaAtualAluno', mProgramaCarregado!.toJson());
  }

  Future<void> alterarCarga(SeriesAtividade? serie, FichaAtividade atv, String carga, {Function()? callback, Function(String erro)? falha, Function()? carregando}) async {
    if (serie != null) {
      serie.cargaApp = '0';
      serie.cargaAtt = carga;
    } else {
      for (final SeriesAtividade sr in atv.series!) {
        sr.cargaApp = '0';
        sr.cargaAtt = carga;
      }
    }
    for (Ficha? ficha in mProgramaCarregado!.programa!.fichas!) {
      if (ficha!.cod == mFichaExibir!.cod) {
        ficha = mFichaExibir;
        break;
      }
    }
    // Vlaida x
    if (_controladorApp.getConfigWebTreino('MOBILE_SEMPRE_ATUALIZAR_CARGA_FICHA')) {
      carregando?.call();
      Future.wait(serie != null
              ? [
                  _mServiceTreino.editarSerie({
                    'codSerie': serie.codSerie!.toInt(),
                    'carga': carga.replaceAll(',', '.'),
                    'cargaApp': carga.replaceAll(',', '.'),
                    'repeticao': serie.repeticao,
                    'repeticaoApp': serie.repeticaoApp
                  })
                ]
              : atv.series!
                  .map((e) => _mServiceTreino.editarSerie({
                        'codSerie': e.codSerie!.toInt(),
                        'carga': carga.replaceAll(',', '.'),
                        'cargaApp': carga.replaceAll(',', '.'),
                        'repeticao': e.repeticao,
                        'repeticaoApp': e.repeticaoApp
                      }))
                  .toList())
          .then((value) {
        inserirNoBanco('programaAtualAluno', (mProgramaCarregado ?? ProgramadeTreino()).toJson());
        callback?.call();
      }).catchError((onError) {
        falha?.call('Não foi possível alterar a carga');
      });
    } else {
      inserirNoBanco('programaAtualAluno', (mProgramaCarregado ?? ProgramadeTreino()).toJson());
      callback?.call();
    }
  }

  Future<void> executaSerie(BuildContext context, SeriesAtividade serie, FichaAtividade atividade, Function()? callback) async {
    if (mControlaDescanso ?? false) {
      await concluirSerie(serie.codSerie!.toInt(), () {
        concluirSeriesDefaultPreparacao(atividade.series!);
        if (atividade.series != null) {
          if (atividade.series!.isNotEmpty) {
            for (final SeriesAtividade serieAtividade in atividade.series!) {
              if (serieAtividade.codSerie == serie.codSerie) {
                serieAtividade.concluida = true;
                break;
              }
            }
            if (todasSeriesConcluidas(atividade.series!)) {
              concluirAtividade(true, atividade.atividade, mFichaExibir!, callback: () {
                callback!();
                if (todasAtividadesConcluidas()) {
                  finalizarTreino(context);
                } else {
                  abrirDescanso(context, serie, atividade);
                }
              });
            } else {
              atualizaFicha(DateTime.now().millisecondsSinceEpoch);
              abrirDescanso(context, serie, atividade);
            }
          } else {
            concluirAtividade(true, atividade.atividade, mFichaExibir!, callback: () {
              callback!();
              if (todasAtividadesConcluidas()) {
                finalizarTreino(context);
              } else {
                abrirDescanso(context, serie, atividade);
              }
            });
          }
          callback!();
        } else {
          concluirAtividade(true, atividade.atividade, mFichaExibir!, callback: () {
            callback!();
            if (todasAtividadesConcluidas()) {
              finalizarTreino(context);
            } else {
              abrirDescanso(context, serie, atividade);
            }
          });
        }
      });
    } else {
      concluirAtividade(true, atividade.atividade, mFichaExibir!, callback: () {
        callback!();
        if (todasAtividadesConcluidas()) {
          finalizarTreino(context);
        }
      });
    }
  }

  finalizarTreino(BuildContext context) {
    analytic(EventosKey.treinoExecucao_concluir);
    atualizaFrequencia(() {
      atualizaUltimaExecucao(() {
        abrirTelaConclusao(context);
      });
    });
  }

  abrirDescanso(BuildContext context, SeriesAtividade serie, FichaAtividade atividade) {
    Navigator.of(context).push(PageRouteBuilder(opaque: false, pageBuilder: (BuildContext context, _, __) => TelaDescanso(descansoArgument: DescansoArgument(serie, atividade))));
  }

  abrirTelaConclusao(BuildContext context) {
    pauseTime();
    Navigator.of(context).push(PageRouteBuilder(opaque: false, pageBuilder: (BuildContext context, _, __) => TelaConcluirTreino()));
  }

  bool todasSeriesConcluidas(List<SeriesAtividade> series) {
    for (final SeriesAtividade serie in series) {
      if (!(serie.concluida ?? false)) {
        return false;
      }
    }
    return true;
  }

  bool todasAtividadesConcluidas() {
    for (final FichaAtividade atividade in mFichaExibir!.atividades!) {
      if (!atividade.concluida!) {
        return false;
      }
    }
    return true;
  }

  atualizaFrequencia(Function()? callback) {
    var comecoFrequencia = mProgramaCarregado!.acompanhamento!.frequencia!.split('/')[0];
    var fimFrequencia = mProgramaCarregado!.acompanhamento!.frequencia!.split('/')[1];
    mProgramaCarregado!.acompanhamento!.frequencia = '${(int.parse(comecoFrequencia) + 1)}/$fimFrequencia';
    inserirNoBanco('programaAtualAluno', mProgramaCarregado!.toJson());

    callback!();
  }

  atualizaUltimaExecucao(Function()? callback) {
    for (final Ficha? ficha in mProgramaCarregado!.programa!.fichas!) {
      if (ficha!.cod == mFichaExibir!.cod) {
        ficha.ultimaExecucaoLong = DateTime.now().millisecondsSinceEpoch;
        ficha.ultimaExecucao = UtilDataHora.getDiaMesAnoHorasMinutos(dataMilis: ficha.ultimaExecucaoLong);
        ficha.concluida = true;
        break;
      }
    }
    inserirNoBanco('programaAtualAluno', mProgramaCarregado!.toJson());
    callback!();
  }

  String getImagemPadraoFicha(Ficha? ficha) {
    try {
      if (ficha == null) {
        return listaImagensShow['alongamento']!;
      }
      if (ficha.atividades == null) {
        if (ficha.atividades?.isEmpty ?? true) {
          return listaImagensShow['alongamento']!;
        }
        return listaImagensShow['alongamento']!;
      }
      var primeiraAtividade = ficha.atividades?.first;
      var atividade = mProgramaCarregado!.programa!.atividades!.firstWhere(
        (element) => element.codigoAtividade.toString() == primeiraAtividade?.codigoAtividade,
        orElse: () {
          return ProgramaAtividade(nome: 'alongamento');
        },
      );
      String? urlRetorno;
      (listaImagensShow).forEach((key, value) {
        key.split(',').forEach((element) {
          if (removeDiacritics(atividade.nome!.toLowerCase()).contains(element)) {
            urlRetorno = value;
          }
        });
      });
      return urlRetorno ?? listaImagensShow['alongamento']!;
    } catch (e) {
      return listaImagensShow['alongamento']!;
    }
  }

  String getImagemPadraoFichaPelaAtividade(String atividade) {
    String? urlRetorno;
    (listaImagensShow).forEach((key, value) {
      key.split(',').forEach((element) {
        if (removeDiacritics(atividade.toLowerCase()).contains(element)) {
          urlRetorno = value;
        }
      });
    });
    return urlRetorno ?? listaImagensShow['alongamento']!;
  }

  String getTagImagem(Ficha? ficha) {
    if (ficha == null) return '';
    if (ficha.atividades == null || ficha.atividades!.isEmpty) {
      return 'alongamento';
    }
    var primeiraAtividade = ficha.atividades!.first;
    var atividade = mProgramaCarregado!.programa!.atividades!.firstWhere((element) => element.codigoAtividade.toString() == primeiraAtividade.codigoAtividade);
    String? urlRetorno;
    (listaImagensShow).forEach((key, value) {
      key.split(',').forEach((element) {
        if (removeDiacritics(atividade.nome!.toLowerCase()).contains(element)) {
          urlRetorno = key;
        }
      });
    });
    return urlRetorno ?? 'alongamento';
  }

  @observable
  List<String> mListaGrupoMuscular = [];

  void consultarGruposMuscularesDaFicha({required Ficha ficha}) {
    mListaGrupoMuscular.clear();
    _mServiceTreino.consultarGruposMuscularesDaFicha(codigoFicha: ficha.cod.toString(), empresaId: _controladorCliente.mUsuarioLogado?.codEmpresa ?? 1).then((value) {
      mListaGrupoMuscular.addAll(value);
    }).catchError((onError) {});
  }

  LinkedHashMap<String, String> imagens = LinkedHashMap();
  LinkedHashMap<String, String> get listaImagensShow {
    imagens['alongamento'] = 'assets/images/treinoAluno/alongamento.webp';
    imagens['legpess,leg'] = 'assets/images/treinoAluno/leg.webp';
    imagens['prancha'] = 'assets/images/treinoAluno/prancha.webp';
    imagens['remada'] = 'assets/images/treinoAluno/remada.webp';
    imagens['supino'] = 'assets/images/treinoAluno/supino.webp';
    imagens['abs,abdominal'] = 'assets/images/treinoAluno/abs.webp';
    imagens['biceps,bicipes'] = 'assets/images/treinoAluno/biceps.webp';
    imagens['cardio'] = 'assets/images/treinoAluno/cardio.webp';
    imagens['crucifixo'] = 'assets/images/treinoAluno/crucifixo.webp';
    imagens['elevação'] = 'assets/images/treinoAluno/elevacaolateral.webp';
    imagens['evolução'] = 'assets/images/treinoAluno/evolucao.webp';
    imagens['scott'] = 'assets/images/treinoAluno/scott.webp';
    imagens['triceps'] = 'assets/images/treinoAluno/triceps.webp';
    imagens['rosca scott'] = 'assets/images/treinoAluno/rosca.webp';
    imagens['beach tennis,beach'] = 'assets/images/treinoAluno/beach.webp';
    return imagens;
  }

  LinkedHashMap<String, String> get listaImagensShowBeach {
    imagens['alongamento'] = 'assets/images/imageBeach/geral.jpeg';
    imagens['defesa'] = 'assets/images/imageBeach/defesa.jpeg';
    imagens['curta'] = 'assets/images/imageBeach/dropshot.jpeg';
    imagens['dropshot'] = 'assets/images/imageBeach/dropshot.jpeg';
    imagens['forehand'] = 'assets/images/imageBeach/forehand.jpeg';
    imagens['smash'] = 'assets/images/imageBeach/smash.jpeg';
    imagens['saque'] = 'assets/images/imageBeach/saque.jpeg';
    imagens['gancho'] = 'assets/images/imageBeach/gancho.jpeg';
    imagens['beach tennis,beach'] = 'assets/images/imageBeach/geral.jpeg';
    return imagens;
  }

  String _tokenTrocaAtv = '';
  DateTime? _tokenTrocaAtvExpires;
  void _gerarTokenTrocaAtividade({Function()? onToken, Function()? onError}) async {
    if (_tokenTrocaAtv.isEmpty || _tokenTrocaAtvExpires == null || DateTime.now().isAfter(_tokenTrocaAtvExpires!)) {
      try {
        GetIt.I<ServiceAuth>().gerarTokenAuthColaboradorV3(
          {
            'chave': GetIt.I<ControladorApp>().chave,
            'codUsuario': GetIt.I<ControladorCliente>().mUsuarioLogado!.codUsuario ?? '',
          },
        ).then((token) {
          _tokenTrocaAtv = token;
          _tokenTrocaAtvExpires = DateTime.now().add(const Duration(minutes: 20));
          onToken?.call();
        }).catchError((onError) {
          onError?.call();
        });
      } catch (error) {
        onError?.call();
      }
    } else {
      onToken?.call();
    }
  }

  Future<ResponseTrocaDeAtividade?> substituirAtividadeNaFicha(
      {required ProgramaAtividade orignal,
      required ProgramaAtividadeReplace replace,
      Function()? carregando,
      Function(ResponseTrocaDeAtividade atv)? sucesso,
      Function()? falha,
      int? indexNaFicha}) {
    carregando?.call();
    ControladorExecucaoTreino controladorExecucaoTreino = GetIt.I<ControladorExecucaoTreino>();

    return _mServicePrescricao
        .trocarAtividadePorOutra(codigoFicha: (mFichaExibir ?? controladorExecucaoTreino.treinoEmExecucao)!.cod!, codAtividadeOriginal: orignal.cod!, codAtividadeSubstituir: replace.id!)
        .then((trocaAtv) {
      try {
        int indexPrograma = mProgramaCarregado?.programa?.atividades?.indexWhere((element) => element.codigoAtividade == orignal.codigoAtividade) ?? 0;
        int indexFicha = (mFichaExibir ?? controladorExecucaoTreino.treinoEmExecucao)?.atividades?.indexWhere((element) => element.atividade == orignal.cod.toString()) ?? 0;
        trocaAtv.atividadeFicha.nomeAtividade = trocaAtv.atividadeBase.nome;

        //da ficha em exebição
        // trocaAtv.atividadeFicha.atividade = trocaAtv.atividadeBase.cod.toString();

        trocaAtv.atividadeBase.cod = num.tryParse(trocaAtv.atividadeFicha.atividade.toString());
        controladorExecucaoTreino.treinoEmExecucao?.atividades?.removeAt(indexFicha);
        controladorExecucaoTreino.treinoEmExecucao?.atividades?.insert(indexFicha, trocaAtv.atividadeFicha);
        mFichaExibir?.atividades?.removeAt(indexFicha);
        mFichaExibir?.atividades?.insert(indexFicha, trocaAtv.atividadeFicha);
        //do programa
        mProgramaCarregado?.programa?.atividades?.removeAt(indexPrograma);
        mProgramaCarregado?.programa?.atividades?.insert(indexPrograma, trocaAtv.atividadeBase);
        // mFichaExibir?.atividades?.removeAt(indexFicha);
      } catch (e) {
        log(e.toString());
      }
      sucesso?.call(trocaAtv);
      return trocaAtv;
    }).catchError((onError) {
      falha?.call();
      throw onError;
    });
  }

  void consultarAtividadeReplace(ProgramaAtividade atividade, {Function()? carregando, Function(List<ProgramaAtividadeReplace> atividades)? sucesso, Function()? falha, bool force = true}) {
    carregando?.call();
    mStatusConsultaAtividadesRelacionadas = ServiceStatus.Waiting;
    _gerarTokenTrocaAtividade(onToken: () {
      _mServicePrescricao
          .consultarAtividadesAlternativas(token: _tokenTrocaAtv, codAtividade: atividade.codigoAtividade!, codEmpresa: _controladorCliente.mUsuarioLogado!.codEmpresa!)
          .then((value) {
        sucesso?.call(value.atividadesRelacionadas!);
        mStatusConsultaAtividadesRelacionadas = value.atividadesRelacionadas!.isEmpty ? ServiceStatus.Empty : ServiceStatus.Done;
      }).catchError((onError) {
        mStatusConsultaAtividadesRelacionadas = ServiceStatus.Error;
        falha?.call();
      });
    });
  }

  limparTudo() async {
    try {
      // Resetar variáveis booleanas
      mTreinoEmExecucao = false;
      iniciando = false;
      mCronometroLigado = false;
      mProgramaAtualizou = false;
      programaVenceuENaoPodeExibir = false;
      // Resetar controladores específicos
      mControlaDescanso = null;
      deslogou = 'xxxxxx';

      // Resetar variáveis numéricas
      mTempoIniciado = 0;
      _tempoGasto = 0;
      mAtividadesConcluidas = 0;
      mCodigoFichaExecucao = null;

      // Resetar strings
      mTempoDecorrido = '00:00';
      _tokenTrocaAtv = '';

      // Resetar datas
      horaInicioDoTreino = null;
      _tokenTrocaAtvExpires = null;
      horaSoliciacaoTreiniIA = null;

      // Resetar observables de estado
      statusTreinoIASituacao = SituacaoValidacao.NEGADO;
      statusTreinoIA = TreinoIAstatus();
      mStatusConsultaPrograma = ServiceStatus.Waiting;
      mStatusConsultaAtividadesRelacionadas = ServiceStatus.Waiting;

      // Resetar objetos complexos
      mProgramaCarregado = null;
      mFichaExibir = null;
      mAtividadeProgramaExibir = null;
      mAtividadeFichaExibir = null;

      // Limpar listas
      mHistoricoExecucao.clear();
      mFichasConcluidas.clear();
      mListaGrupoMuscular.clear();
      imagens.clear();

      // Cancelar timer se existir
      timer?.cancel();
      _debounceConsultaIAPrograma?.cancel();

      // Limpar SharedPreferences
      SharedPreferences prefs = await SharedPreferences.getInstance();
      prefs.remove('fichaIniciada');
      prefs.remove('dataFichaIniciada');
      prefs.remove('codigoFichaExecucao');
      prefs.remove('isTreinoDoDiaEmExecucao');
      prefs.setStringList('atividadesConcluidas', []);
      prefs.setStringList('seriesConcluidas', []);
      prefs.setStringList('listAtividadesConcluidasDaFichaEmExecucao', []);

      // Limpar banco de dados local
      await store.record('programaAtualAluno').delete(getDb);
      await store.record('exerciciosOffline').delete(getDb);
      imagens.clear();
      // Não resetamos variáveis avaliadas com GetIt
    } catch (e) {
      createAlbum(FlutterErrorDetails(exception: e));
    }
  }

  bool isProfessorVerVideo(List<UrlLinkVideos> link) {
    if (link.isNotEmpty) {
      return link.any((element) => element.professor ?? false);
    } else {
      return false;
    }
  }

  bool isProfessorVerImagem(List<Images> imagens) {
    if (imagens.isNotEmpty) {
      return imagens.any((element) => element.professor ?? false);
    } else {
      return false;
    }
  }

  //
  // String getUrlFotoFicha(){

  // }
}

class DescansoArgument {
  final SeriesAtividade serie;
  final FichaAtividade atividade;

  DescansoArgument(this.serie, this.atividade);
}
