import 'dart:convert';
import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/personal/Notificacao.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorNotificacoes.g.dart';

class ControladorNotificacoes = _ControladorNotificacoesBase with _$ControladorNotificacoes;

abstract class _ControladorNotificacoesBase with Store {
  final _service = GetIt.I.get<PersonalService>();
  FlutterLocalNotificationsPlugin? flutterNotificationLocalPlugin;
  bool carregouApp = false;
  bool abriuAppApartirDePush = false;
  String payloadPushAbriu = '';

  @observable
  ObservableList<NotificacaoPersonal> notificacoes = ObservableList<NotificacaoPersonal>();

  @observable
  ServiceStatus statusConsultaObservacoes = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultaObservacoesMore = ServiceStatus.Done;

  num get quantidadeNotificacoesNaoLida => notificacoes.where((element) => !element.lida!).length;

  @observable
  num quantidadeRecebidasSessao = 0;

  void marcarNotificacoesComoLidas() {
    _service.marcarNotificacoesComoLidaPersonal().then((value) {
      for (var i = 0; i < notificacoes.length; i++) {
        this.notificacoes[i].lida = true;
      }
    }).catchError((onError) {});
  }

  limparTudo() {
    notificacoes.clear();
  }

  Future<void> onDidReceiveLocalNotification(int? id, String? title, String? body, String? payload) async {
    if (payload != null && payload.contains('beberAgua') || payload!.contains('refeicao')) {
      // GetIt.I.get<ControladorTelaPrincipal>().selecionarRefeicoes();
    }
  }

  Future<void> onSelectNotification(dynamic pushPayload) async {
    if ((pushPayload != null && pushPayload.contains('beberAgua') || pushPayload.contains('refeicao'))) {
      // GetIt.I.get<ControladorTelaPrincipal>().selecionarRefeicoes();
    }
  }

  void validarPushEmBackground(dynamic messagem) {
    try {
      if (messagem is String) {
        messagem = const JsonCodec().decode(messagem);
      }
    } catch (e) {}
  }

  void validarSeVeioDePush() {
    if (abriuAppApartirDePush) {
      onSelectNotification(payloadPushAbriu);
    }
  }

  void verificaSeAbriuAppApartirDePush() {
    // NOTE: if you want to find out if the app was launched via notification then you could use the following call and then do something like
    // change the default route of the app
    flutterNotificationLocalPlugin?.getNotificationAppLaunchDetails().then((value) {
      abriuAppApartirDePush = value?.didNotificationLaunchApp ?? false;
      payloadPushAbriu = value?.notificationResponse?.payload ?? '';
    });
  }

  Future<void> cancelarLembretePorId(int id) async {
    return flutterNotificationLocalPlugin!.cancel(id).catchError((onError) {});
  }

  Future<void> cancelarTodosLembretesBeberAgua() async {
    return flutterNotificationLocalPlugin!.cancelAll().catchError((onError) {});
  }

  Function() onTapPermissaoNotificacoes = () {};
}
