// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorManterAluno.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorManterAluno on _ControladorManterAlunoBase, Store {
  Computed<bool>? _$isFotoValidaComputed;

  @override
  bool get isFotoValida =>
      (_$isFotoValidaComputed ??= Computed<bool>(() => super.isFotoValida,
              name: '_ControladorManterAlunoBase.isFotoValida'))
          .value;
  Computed<bool>? _$liberadoOCadastroComputed;

  @override
  bool get liberadoOCadastro => (_$liberadoOCadastroComputed ??= Computed<bool>(
          () => super.liberadoOCadastro,
          name: '_ControladorManterAlunoBase.liberadoOCadastro'))
      .value;

  late final _$mServiceAtom =
      Atom(name: '_ControladorManterAlunoBase.mService', context: context);

  @override
  PersonalService get mService {
    _$mServiceAtom.reportRead();
    return super.mService;
  }

  @override
  set mService(PersonalService value) {
    _$mServiceAtom.reportWrite(value, super.mService, () {
      super.mService = value;
    });
  }

  late final _$cadastroAlunoManterAtom = Atom(
      name: '_ControladorManterAlunoBase.cadastroAlunoManter',
      context: context);

  @override
  CadastroAluno get cadastroAlunoManter {
    _$cadastroAlunoManterAtom.reportRead();
    return super.cadastroAlunoManter;
  }

  @override
  set cadastroAlunoManter(CadastroAluno value) {
    _$cadastroAlunoManterAtom.reportWrite(value, super.cadastroAlunoManter, () {
      super.cadastroAlunoManter = value;
    });
  }

  late final _$mListaObjetivosAtom = Atom(
      name: '_ControladorManterAlunoBase.mListaObjetivos', context: context);

  @override
  ObservableList<ObjetivoAluno> get mListaObjetivos {
    _$mListaObjetivosAtom.reportRead();
    return super.mListaObjetivos;
  }

  @override
  set mListaObjetivos(ObservableList<ObjetivoAluno> value) {
    _$mListaObjetivosAtom.reportWrite(value, super.mListaObjetivos, () {
      super.mListaObjetivos = value;
    });
  }

  late final _$objetivoSalvarNoAlunoAtom = Atom(
      name: '_ControladorManterAlunoBase.objetivoSalvarNoAluno',
      context: context);

  @override
  ObjetivoAluno? get objetivoSalvarNoAluno {
    _$objetivoSalvarNoAlunoAtom.reportRead();
    return super.objetivoSalvarNoAluno;
  }

  @override
  set objetivoSalvarNoAluno(ObjetivoAluno? value) {
    _$objetivoSalvarNoAlunoAtom.reportWrite(value, super.objetivoSalvarNoAluno,
        () {
      super.objetivoSalvarNoAluno = value;
    });
  }

  late final _$consultouObjetivosAtom = Atom(
      name: '_ControladorManterAlunoBase.consultouObjetivos', context: context);

  @override
  bool get consultouObjetivos {
    _$consultouObjetivosAtom.reportRead();
    return super.consultouObjetivos;
  }

  @override
  set consultouObjetivos(bool value) {
    _$consultouObjetivosAtom.reportWrite(value, super.consultouObjetivos, () {
      super.consultouObjetivos = value;
    });
  }

  late final _$falhaConsultaObjetivosAtom = Atom(
      name: '_ControladorManterAlunoBase.falhaConsultaObjetivos',
      context: context);

  @override
  bool get falhaConsultaObjetivos {
    _$falhaConsultaObjetivosAtom.reportRead();
    return super.falhaConsultaObjetivos;
  }

  @override
  set falhaConsultaObjetivos(bool value) {
    _$falhaConsultaObjetivosAtom
        .reportWrite(value, super.falhaConsultaObjetivos, () {
      super.falhaConsultaObjetivos = value;
    });
  }

  late final _$imagemMemoryAtom =
      Atom(name: '_ControladorManterAlunoBase.imagemMemory', context: context);

  @override
  Uint8List? get imagemMemory {
    _$imagemMemoryAtom.reportRead();
    return super.imagemMemory;
  }

  @override
  set imagemMemory(Uint8List? value) {
    _$imagemMemoryAtom.reportWrite(value, super.imagemMemory, () {
      super.imagemMemory = value;
    });
  }

  @override
  String toString() {
    return '''
mService: ${mService},
cadastroAlunoManter: ${cadastroAlunoManter},
mListaObjetivos: ${mListaObjetivos},
objetivoSalvarNoAluno: ${objetivoSalvarNoAluno},
consultouObjetivos: ${consultouObjetivos},
falhaConsultaObjetivos: ${falhaConsultaObjetivos},
imagemMemory: ${imagemMemory},
isFotoValida: ${isFotoValida},
liberadoOCadastro: ${liberadoOCadastro}
    ''';
  }
}
