import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:developer' as log;
import 'package:app_treino/ServiceProvider/TreinoService.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/controlladores/ControladorAcessoCatraca.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorAulaTurma.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorConfiguracao.dart';
import 'package:app_treino/controlladores/ControladorEventBus.dart';
import 'package:app_treino/controlladores/ControladorNotificacoes.dart';
import 'package:app_treino/controlladores/ControladorTreinoAluno.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/util/UtilColor.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:assets_audio_player/assets_audio_player.dart';
import 'package:audio_session/audio_session.dart';
import 'package:diacritic/diacritic.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:get_it/get_it.dart';
import 'package:just_audio/just_audio.dart';
import 'package:mobx/mobx.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:app_treino/model/treinoAluno/ProgramadeTreino.dart';
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
part 'ControladorExecucaoTreino.g.dart';

class ControladorExecucaoTreino = _ControladorExecucaoTreinoBase with _$ControladorExecucaoTreino;

abstract class _ControladorExecucaoTreinoBase with Store {
  final serviceTreino = GetIt.I.get<TreinoService>();

  static const channel = MethodChannel('myPlatformChannel');

  @observable
  bool treinoIniciado = false;

  @observable
  bool descanso = false;

  bool telaTreinoAcabouDeFechar = false;

  @observable
  bool proximoExercicio = false;

  @observable
  int duracaoTotalDescansoExercicioAtual = 0;

  @observable
  int contadorDescansoExercicioAtual = 0;

  @observable
  int duracaoTreino = 0;

  @observable
  String duracaoTreinoString = '00:00:00';

  @observable
  int horarioQueDispositivoInativou = 0;

  Timer? _timerDescanso;

  Timer? _timerTreino;

  @observable
  Ficha? treinoEmExecucao;

  @observable
  int indexExercicioEmExecucao = 0;

  @observable
  int indexSelecionado = 0;

  @observable
  int indexSerieExercicioEmExecucao = 0;

  @observable
  var temSuporteAppleWatch = false;

  @observable
  var estaConectadoAppleWatch = false;

  @observable
  var estaNoProximoAppleWach = false;

  /**
   * Essa variável serve para identificar quando eu concluo um exercicio pelo 
   * apple watch se estou na tela de treino (detalhes) ou no widget (treino resumido).
   * Se eu abro a tela de detalhes, eu seto a variavel como true, quando eu saio da tela, seto ela como false. 
   * Desse modo, evito que o EVENT_BUS chame os eventos das duas telas e o metodo ao mesmo momento, fazendo com que quebre.
   * Para o Lailan do futuro, fica a dica para pensar numa forma melhor.
   */
  @observable
  var concluiuExercicioAppleWatchTelaDetalhes = false;

  @observable
  ServiceStatus filtrandoNomeExercicio = ServiceStatus.Done;

  Future<void> agendarNotificacaoDescanso({required int descanso}) async {
    tz.initializeTimeZones();
    var _timezone = await FlutterTimezone.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(_timezone));
    try {
      await GetIt.I.get<ControladorNotificacoes>().flutterNotificationLocalPlugin!.zonedSchedule(
          1999,
          '⏰ Descanso acabou!',
          'Hora de voltar a treinar.',
          tz.TZDateTime.now(tz.local).add(Duration(seconds: descanso)),
          const NotificationDetails(
              android: AndroidNotificationDetails('descanso_treino_id', 'descanso_treino_channel', channelDescription: 'descanso_treino_description', icon: '@mipmap/ic_launcher')),
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime);
    } catch (e) {}
  }

  Future<void> cancelarNotificacaoDescanso() async {
    try {
      await GetIt.I.get<ControladorNotificacoes>().flutterNotificationLocalPlugin!.cancel(1999);
    } catch (e) {}
  }

  @action
  concluirTodasSeries({required Function() concluiu, required Function() concluiuTreino}) {
    treinoEmExecucao?.atividades?[indexExercicioEmExecucao].concluida = true;
    for (final element in treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series ?? []) {
      element.concluida = true;
    }
    var quantidadeAtividadesConcluidas = 0;
    for (int i = 0; i < (treinoEmExecucao?.atividades?.length ?? 0); i++) {
      if (!(treinoEmExecucao?.atividades?[i].concluida ?? false)) {
        indexExercicioEmExecucao = i;
        indexSelecionado = indexExercicioEmExecucao;
        for (int s = 0; s < (treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series?.length ?? 0); s++) {
          if (!(treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series![s].concluida ?? false)) {
            indexSerieExercicioEmExecucao = s;
            break;
          }
        }
        break;
      } else {
        quantidadeAtividadesConcluidas += 1;
      }
    }
    salvarTreinoEmExecucao(fichaEmExecucao: treinoEmExecucao!);
    quantidadeAtividadesConcluidas == treinoEmExecucao?.atividades?.length ? concluiuTreino() : concluiu();
  }

  @action
  concluirExercicio(
      {required bool veioDoDescanso, required Function() iniciarDescanso, required Function() concluiuExercicio, required Function() concluiuTreino, required Function() concluiuDescanso}) {
    if (descanso) {
      desmarcarSerie(indexSerieExercicioEmExecucao);
      pularDescanso();
    } else {
      if (veioDoDescanso) {
        var quantidadeSeriesConcluidas = treinoEmExecucao!.atividades![indexExercicioEmExecucao].series!.where((element) => element.concluida ?? false).length;
        if (quantidadeSeriesConcluidas >= treinoEmExecucao!.atividades![indexExercicioEmExecucao].series!.length) {
          treinoEmExecucao!.atividades![indexExercicioEmExecucao].concluida = true;
          if (treinoEmExecucao!.atividades!.where((element) => !(element.concluida ?? false)).isNotEmpty) {
            getIndexProximoExercicioQueNaoFoiConcluido();
            proximoExercicio = true;
            concluiuExercicio();
          } else {
            concluiuTreino();
          }
        } else {
          indexSelecionado = indexExercicioEmExecucao;
          getIndexProximaSerieQueNaoFoiConcluida();
          concluiuDescanso();
        }
      } else {
        treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series![indexSerieExercicioEmExecucao].concluida = true;
        final descansoInt = int.parse(treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series?[indexSerieExercicioEmExecucao].descanso ?? '0');
        if (descansoInt > 0 && GetIt.I.get<ControladorConfiguracao>().descansoHabilitado) {
          agendarNotificacaoDescanso(descanso: descansoInt);
          iniciarDescanso();
        } else {
          cancelarNotificacaoDescanso();
          var quantidadeSeriesConcluidas = treinoEmExecucao!.atividades![indexExercicioEmExecucao].series!.where((element) => element.concluida ?? false).length;
          if (quantidadeSeriesConcluidas >= treinoEmExecucao!.atividades![indexExercicioEmExecucao].series!.length) {
            treinoEmExecucao!.atividades![indexExercicioEmExecucao].concluida = true;
            if (treinoEmExecucao!.atividades!.where((element) => !(element.concluida ?? false)).isNotEmpty) {
              getIndexProximoExercicioQueNaoFoiConcluido();
              proximoExercicio = true;
              concluiuExercicio();
            } else {
              concluiuTreino();
            }
          } else {
            indexSelecionado = indexExercicioEmExecucao;
            getIndexProximaSerieQueNaoFoiConcluida();
            concluiuDescanso();
          }
        }
      }
    }
    salvarTreinoEmExecucao(fichaEmExecucao: treinoEmExecucao!);
  }
  
  void desmarcarExercicio(int indexAtividade) {
    if (treinoEmExecucao != null && treinoEmExecucao!.atividades != null && indexAtividade < treinoEmExecucao!.atividades!.length) {
      // Desmarca o exercício
      treinoEmExecucao!.atividades![indexAtividade].concluida = false;

      // Desmarca todas as séries do exercício
      for (int i = 0; i < (treinoEmExecucao!.atividades![indexAtividade].series?.length ?? 0); i++) {
        treinoEmExecucao!.atividades![indexAtividade].series![i].concluida = false;
      }

      // Atualiza os índices para o exercício desmarcado se for o caso
      if (treinoEmExecucao!.atividades!.indexWhere((element) => !(element.concluida ?? false)) < indexExercicioEmExecucao) {
        indexExercicioEmExecucao = indexAtividade;
        indexSelecionado = indexAtividade;
        indexSerieExercicioEmExecucao = 0;
      }

      // Salva o treino atualizado
      salvarTreinoEmExecucao(fichaEmExecucao: treinoEmExecucao!);

      // Garante que o próximo exercício está desativado
      proximoExercicio = false;
    }
  }
  
  pularDescansoEorganizarSeries({required Function() concluiuExercicio, required Function() concluiuTreino}) {
    pularDescanso();
    treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series![indexSerieExercicioEmExecucao].concluida = true;
    var quantidadeSeriesConcluidas = 0;
    for (int i = 0; i < (treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series?.length ?? 0); i++) {
      if (!(treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series![i].concluida ?? false)) {
        indexSerieExercicioEmExecucao = i;
        break;
      } else {
        quantidadeSeriesConcluidas = quantidadeSeriesConcluidas + 1;
      }
    }
    if (quantidadeSeriesConcluidas == treinoEmExecucao!.atividades![indexExercicioEmExecucao].series!.length) {
      treinoEmExecucao!.atividades![indexExercicioEmExecucao].concluida = true;
      if (treinoEmExecucao!.atividades!.where((element) => !(element.concluida ?? false)).isNotEmpty) {
        getIndexProximoExercicioQueNaoFoiConcluido();
        proximoExercicio = true;
        concluiuExercicio();
      } else {
        concluiuTreino();
      }
    }
    salvarTreinoEmExecucao(fichaEmExecucao: treinoEmExecucao!);
  }

  /// Desmarca a série no índice fornecido.
  ///
  /// Este método é usado para desmarcar uma série específica em uma lista de séries.
  ///
  /// Parâmetros:
  /// - `index`: O índice da série a ser desmarcada.
  void desmarcarSerie(int index) {
    treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series![index].concluida = false;
    treinoEmExecucao?.atividades?[indexExercicioEmExecucao].concluida = false;
    indexSerieExercicioEmExecucao = getIndexProximaSerieQueNaoFoiConcluida();
    proximoExercicio = false;
    salvarTreinoEmExecucao(fichaEmExecucao: treinoEmExecucao!);
  }

  bool get botaoConcluirTodasHabilitado {
    if (treinoEmExecucao!.atividades![indexExercicioEmExecucao].series?.isNotEmpty ?? false) {
      return treinoEmExecucao!.atividades![indexExercicioEmExecucao].series!.every((element) => element.concluida == true);
    } else {
      return false;
    }
  }

  /// Retorna o índice do próximo exercício que não foi concluído.
  ///
  /// Este método percorre a lista de exercícios e retorna o índice do
  /// primeiro exercício que ainda não foi concluído. Se todos os exercícios
  /// foram concluídos, o método pode retornar um valor específico para
  /// indicar essa condição (por exemplo, -1).
  ///
  /// Retorna:
  /// - O índice do próximo exercício não concluído.
  /// - Um valor específico (por exemplo, -1) se todos os exercícios foram concluídos.
  int getIndexProximoExercicioQueNaoFoiConcluido() {
    for (int i = 0; i < treinoEmExecucao!.atividades!.length; i++) {
      if (!(treinoEmExecucao!.atividades![i].concluida ?? false)) {
        indexExercicioEmExecucao = i;
        indexSelecionado = indexExercicioEmExecucao;
        getIndexProximaSerieQueNaoFoiConcluida();
        return indexExercicioEmExecucao;
      }
    }
    return indexExercicioEmExecucao;
  }

  int getIndexProximaSerieQueNaoFoiConcluida() {
    bool existeIndexNaoConcluido = false;
    for (int i = 0; i < (treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series?.length ?? 0); i++) {
      if (!(treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series![i].concluida ?? false)) {
        existeIndexNaoConcluido = true;
        return indexSerieExercicioEmExecucao = i;
      }
    }
    return existeIndexNaoConcluido ? indexSerieExercicioEmExecucao : (treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series?.length ?? 0) - 1;
  }

  bool todasAtividadesConcluidas() {
    for (int i = 0; i < (treinoEmExecucao?.atividades?.length ?? 0); i++) {
      if (treinoEmExecucao?.atividades?[i].series != null) {
        if (treinoEmExecucao?.atividades?[i].series?.isNotEmpty ?? false) {
          if (treinoEmExecucao?.atividades?[i].series?.where((element) => !(element.concluida ?? false)).isEmpty ?? false) {
            treinoEmExecucao?.atividades?[i].concluida = true;
          }
        }
      }
    }
    return treinoEmExecucao!.atividades!.where((element) => !(element.concluida ?? false)).isEmpty;
  }

  @action
  double quantidadeDeAtividadesConcluidas() {
    var atividadesConcluidas = 0;
    try {
      for (int i = 0; i < (treinoEmExecucao?.atividades?.length ?? 0); i++) {
        if (treinoEmExecucao?.atividades?[i].concluida ?? false) {
          atividadesConcluidas += 1;
        }
      }
      return (atividadesConcluidas / (treinoEmExecucao?.atividades?.length ?? 0)) > 1
          ? 1
          : (atividadesConcluidas / (treinoEmExecucao?.atividades?.length ?? 0)) < 0
              ? 0
              : (atividadesConcluidas / (treinoEmExecucao?.atividades?.length ?? 0));
    } catch (e) {
      return atividadesConcluidas.toDouble();
    }
  }

  @action
  int quantidadeDeSeriesConcluidas() {
    try {
      var seriesConcluidas = 0;
      if (treinoEmExecucao?.atividades != null) {
        for (int i = 0; i < (treinoEmExecucao?.atividades?.length ?? 0); i++) {
          if (treinoEmExecucao?.atividades?[i] != null) {
            for (int s = 0; s < (treinoEmExecucao?.atividades?[i].series?.length ?? 0); s++) {
              if (treinoEmExecucao?.atividades?[i].series?[s].concluida ?? false) {
                seriesConcluidas += 1;
              }
            }
          }
        }
      }
      return seriesConcluidas;
    } catch (e) {
      return 0;
    }
  }

  mudarExercicioEmExecucao({required Function() concluiu, required bool avancar}) {
    if (avancar) {
      indexExercicioEmExecucao =
          (indexExercicioEmExecucao + 1 > (treinoEmExecucao?.atividades?.length ?? 0) ? ((treinoEmExecucao?.atividades?.length ?? 0) - 1) : indexExercicioEmExecucao + 1);
      indexSelecionado = indexExercicioEmExecucao;
      indexSerieExercicioEmExecucao = getIndexProximaSerieQueNaoFoiConcluida();
    } else {
      indexExercicioEmExecucao = (indexExercicioEmExecucao - 1 <= 0 ? 0 : indexExercicioEmExecucao - 1);
      indexSelecionado = indexExercicioEmExecucao;
      indexSerieExercicioEmExecucao = getIndexProximaSerieQueNaoFoiConcluida();
    }
    concluiu();
  }

  Ficha limparFicha() {
    finalizarTreinoEmExecucao();
    GetIt.I.get<ControladorTreinoAluno>().mFichaExibir?.atividades?.forEach((element) {
      element.concluida = false;
      element.series?.forEach((element) {
        element.concluida = false;
      });
    });
    return GetIt.I.get<ControladorTreinoAluno>().mFichaExibir!;
  }

  @action
  salvarTreinoEmExecucao({required Ficha fichaEmExecucao}) {
    try {
      fichaEmExecucao.dataInicioTreino = DateTime.now().millisecondsSinceEpoch;
    fichaEmExecucao.indexExercicioEmExecucao = indexExercicioEmExecucao;
    fichaEmExecucao.indexSelecionado = indexSelecionado;
    fichaEmExecucao.indexSerieExercicioEmExecucao = indexSerieExercicioEmExecucao;
    for (int i = 0; i < fichaEmExecucao.atividades!.length; i++) {
      var atividade;
      try {
        atividade = GetIt.I
            .get<ControladorTreinoAluno>()
            .mProgramaCarregado!
            .programa!
            .atividades!
            .firstWhere((element) => element.cod.toString() == fichaEmExecucao.atividades?[i].atividade && element.nome!.contains(fichaEmExecucao.atividades?[i].nomeMetodoExecucao ?? ''));
      } catch (e) {
        try {
          atividade =
              GetIt.I.get<ControladorTreinoAluno>().mProgramaCarregado!.programa!.atividades!.firstWhere((element) => element.cod.toString() == fichaEmExecucao.atividades?[i].atividade);
        } catch (e) {}
      }
      fichaEmExecucao.atividades![i].nomeAtividade = atividade.nome ?? '';
      fichaEmExecucao.atividades![i].urlFotoAtividade = atividade?.imgMedium!.isEmpty ?? true
          ? 'https://prgbrasil.com/wp-content/themes/consultix/images/no-image-found-360x250.png'
          : atividade?.imgMedium ?? 'https://prgbrasil.com/wp-content/themes/consultix/images/no-image-found-360x250.png';
      fichaEmExecucao.atividades![i].tipoAtividade = atividade.tipo ?? 0;
      }
    } catch (e) {
      
    }
    var ficha = jsonEncode(fichaEmExecucao);
    SharedPreferences.getInstance().then((db) async {
      db.setString('TREINO_EM_EXECUCAO', ficha);
    });

    treinoEmExecucao = null;
    treinoEmExecucao = fichaEmExecucao;
    enviarTreinoParaAppleWatch(treinoEmExecucao);
  }

  @action
  Future<Ficha?> getTreinoEmExecucao() async {
    await SharedPreferences.getInstance().then((db) async {
      var ficha = db.getString('TREINO_EM_EXECUCAO');
      if (ficha != null) {
        try {
          treinoEmExecucao = Ficha();
          treinoEmExecucao = Ficha.fromJson(jsonDecode(ficha));
        } catch (e) {
          return treinoEmExecucao;
        }
        final dataInicioTreino = UtilDataHora.getDataComHorasPersonalizada(
            dateTime: DateTime.fromMillisecondsSinceEpoch(int.parse('${treinoEmExecucao?.dataInicioTreino ?? 0}')), horas: 0, minutos: 0, segundos: 0);
        if (UtilDataHora.dataJaPassouDeHoje(dateTime: dataInicioTreino, ateUltimoMinutoDoDia: true) &&
            !(GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.TREINO_NAO_FIANLIZAR_MEIA_NOITE).habilitado ?? false)) {
          return limparFicha();
        } else {
          return treinoEmExecucao;
        }
      }
    });
    return Ficha();
  }

  /**
   * Envia a conclusão do treino para o PACTO ADM AKA ZW/TREINO
   */
  finalizarTreinoeEnviarNota(int nota, String comentario, {Function()? carregando, Function()? sucesso, Function()? falha}) {
    carregando!();
    if(GetIt.I.get<ControladorCliente>().isUsuarioColaborador) {
      enviarTreinoFirebase();
      finalizarTreinoEmExecucao();
      sucesso!();
    } else {
      serviceTreino
        .concluirTreino(
            GetIt.I.get<ControladorCliente>().mUsuarioLogado!.username!,
            GetIt.I.get<ControladorTreinoAluno>().mProgramaCarregado!.programa!.cod!.toInt(),
            treinoEmExecucao?.cod?.toInt() ?? 0,
            UtilDataHora.getDiaMesAno(dateTime: DateTime.now()),
            nota,
            duracaoTreino,
            comentario,
            GetIt.I.get<ControladorCliente>().mUsuarioLogado!.matricula!)
        .then((value) {
      sucesso!();
      enviarTreinoFirebase();
      finalizarTreinoEmExecucao();
      //registrarAcessoNoTreino();
    }).catchError((onError) async {
      enviarTreinoFirebase();
      finalizarTreinoEmExecucao();
      falha?.call();
    });
    }
  }

  // * Registrar acesso manualmente
  void registrarAcessoNoTreino() {
    if (GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.CONTABILIZAR_PRESENCA_AO_EXECUTAR_TREINO).habilitado!) {
      GetIt.I.get<ControladorAcessoCatraca>().registrarAcesso(
          chave: GetIt.I.get<ControladorApp>().chave!,
          empresa: GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa ?? 1,
          codigoCliente: GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codigoCliente ?? 0,
          dataAcesso: UtilDataHora.getDiaMesAnoHorasMinutosSegundos(dateTime: DateTime.now()));
    }
  }

  DateTime getStartOfWeek(DateTime date) {
    // Ajuste o dia para que o início seja segunda-feira (weekday == 1)
    int offset = date.weekday - DateTime.monday;
    return date.subtract(Duration(days: offset));
  }

  DateTime getEndOfWeek(DateTime date) {
    // Ajuste o dia para que o final seja domingo (weekday == 7)
    int offset = DateTime.sunday - date.weekday;
    return date.add(Duration(days: offset));
  }

  /**
   * Envia a conclusão do treino para o Firebase
   */
  enviarTreinoFirebase() {
    Map<String, dynamic> treinoExecutadoJSON = {
      'imagemKey': GetIt.I.get<ControladorTreinoAluno>().getTagImagem(treinoEmExecucao!),
      'categoria': treinoEmExecucao?.categoria,
      'dataConclusao': DateTime.now().millisecondsSinceEpoch,
      'nome': treinoEmExecucao?.nome,
      'tempoTotalEmexecusao': duracaoTreino,
      'quantidadeAtividadesConcluidas ': (treinoEmExecucao?.atividades?.where((a) => (a.concluida ?? false)).length ?? 0),
      'refClienteApp': GetIt.I.get<ControladorApp>().mClienteAppSelecionado?.documentkey ?? '-',
      'chaveZw': GetIt.I.get<ControladorApp>().chave,
      'nomePrograma': GetIt.I.get<ControladorTreinoAluno>().mProgramaCarregado?.programa?.nome ?? 'Programa sem nome',
      'codigoEmpresa': GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa,
    };
    serviceTreino.enviarTreinoFirebase(treinoExecutadoJSON, GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid!).then((value) {}).catchError((onError) {});
  }

  @action
  void iniciarCronometroTreino() {
    SharedPreferences.getInstance().then((db) async {
      if (db.getInt('TREINO_EM_EXECUCAO_HORARIO_INATIVOU') != null) {
        indexExercicioEmExecucao = await db.getInt('TREINO_EM_EXECUCAO_EXERCICIO_INDEX') ?? 0;
        indexSerieExercicioEmExecucao = await db.getInt('TREINO_EM_EXECUCAO_SERIE_INDEX') ?? 0;
        horarioQueDispositivoInativou = await db.getInt('TREINO_EM_EXECUCAO_HORARIO_INATIVOU') ?? 0;
        duracaoTreino = await db.getInt('DURACAO_TREINO_EM_EXECUCAO') ?? 0;
        final segundosParaAcabarDescanso = await db.getInt('DURACAO_DESCANSO_RESTANTE') ?? 0;
        final data1 = new DateTime.fromMillisecondsSinceEpoch(horarioQueDispositivoInativou);
        final diferencaEmSegundos = (DateTime.now().difference(data1).inSeconds);
        db.remove('TREINO_EM_EXECUCAO_HORARIO_INATIVOU');
        db.remove('DURACAO_TREINO_EM_EXECUCAO');
        duracaoTreino = duracaoTreino + diferencaEmSegundos;
        if (diferencaEmSegundos > (db.getInt('HORAS_ENCERRAR_TREINO_AUTOMATICAMENTE') ?? 3) * 60 * 60) {
          finalizarTreinoEmExecucao();
        } else {
          if (segundosParaAcabarDescanso == 1) {
            descanso = false;
            contadorDescansoExercicioAtual = 0;
            await db.setInt('DURACAO_DESCANSO_RESTANTE', 0);
            try {
              if (_timerDescanso?.isActive ?? false) {
                _timerDescanso?.cancel();
              }
            } catch (e) {}
            ajustarContadorDescansoQuandoVierDoBackground();
          } else if (diferencaEmSegundos >= segundosParaAcabarDescanso) {
            descanso = false;
            contadorDescansoExercicioAtual = 0;
            await db.setInt('DURACAO_DESCANSO_RESTANTE', 0);
            try {
              if (_timerDescanso?.isActive ?? false) {
                _timerDescanso?.cancel();
              }
            } catch (e) {}
            ajustarContadorDescansoQuandoVierDoBackground();
          } else {
            duracaoTotalDescansoExercicioAtual = int.parse(treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series?[indexSerieExercicioEmExecucao].descanso ?? '0');
            contadorDescansoExercicioAtual = (segundosParaAcabarDescanso - diferencaEmSegundos);
            descanso = true;
            iniciarTimer(
                tipo: TipoContador.BACKGROUND,
                finalizouDescanso: () {
                  ajustarContadorDescansoQuandoVierDoBackground();
                });
          }
        }
      } else {
        db.setBool('TREINO_PAUSADO_PELO_USUARIO', false);
        duracaoTreino = duracaoTreino == 0 ? (db.getInt('DURACAO_TREINO_EM_EXECUCAO') ?? 0) : duracaoTreino;
      }
    });
    try {
      if (!(_timerTreino?.isActive ?? false)) {
        cronometroTreino();
      }
    } catch (e) {
      cronometroTreino();
    }
  }

  ajustarContadorDescansoQuandoVierDoBackground() {
    var quantidadeSeriesConcluidas = treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series?.where((element) => element.concluida ?? false).length;
    if ((quantidadeSeriesConcluidas ?? 0) >= (treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series?.length ?? 0)) {
      treinoEmExecucao?.atividades?[indexExercicioEmExecucao].concluida = true;
      if (treinoEmExecucao != null) {
        if (treinoEmExecucao!.atividades != null && treinoEmExecucao!.atividades!.where((element) => !(element.concluida ?? false)).isNotEmpty) {
          getIndexProximoExercicioQueNaoFoiConcluido();
          proximoExercicio = true;
          GetIt.I.get<ControladorEventBus>().eventBus.fire(ConcluiuExercicioEvent(true));
        } else {
          GetIt.I.get<ControladorEventBus>().eventBus.fire(ConcluiuTreinoEvent(true));
        }
      }
    } else {
      indexSelecionado = indexExercicioEmExecucao;
      getIndexProximaSerieQueNaoFoiConcluida();
      GetIt.I.get<ControladorEventBus>().eventBus.fire(ConcluiuSerieEvent(true));
    }
  }

  cronometroTreino() {
    _timerTreino = Timer.periodic(const Duration(seconds: 1), (t) {
      duracaoTreino += 1;
      int horas = duracaoTreino ~/ 3600;
      int minutos = ((duracaoTreino ~/ 60) % 60).toInt();
      int segundos = (duracaoTreino % 60).toInt();
      String minS = minutos < 10 ? '0$minutos' : minutos.toString();
      String segS = segundos < 10 ? '0$segundos' : segundos.toString();
      String horS = horas < 10 ? '0$horas' : horas.toString();
      duracaoTreinoString = '$horS:$minS:$segS';
    });
  }

  pausarCronometroTreino({required bool inativou}) {
    if (inativou) {
      horarioQueDispositivoInativou = DateTime.now().millisecondsSinceEpoch;
      SharedPreferences.getInstance().then((db) async {
        if (db.getInt('TREINO_EM_EXECUCAO_HORARIO_INATIVOU') == null) {
          db.setInt('TREINO_EM_EXECUCAO_EXERCICIO_INDEX', indexExercicioEmExecucao);
          db.setInt('TREINO_EM_EXECUCAO_SERIE_INDEX', indexSerieExercicioEmExecucao);
          db.setInt('TREINO_EM_EXECUCAO_HORARIO_INATIVOU', horarioQueDispositivoInativou);
          db.setInt('DURACAO_TREINO_EM_EXECUCAO', duracaoTreino);
          if (descanso) {
            await db.setInt('DURACAO_DESCANSO_RESTANTE', contadorDescansoExercicioAtual);
          } else {
            await db.remove('DURACAO_DESCANSO_RESTANTE');
          }
        }
      });
    }
    _timerTreino?.cancel();
  }

  final _player = AudioPlayer(
    handleInterruptions: true,
    androidApplyAudioAttributes: false,
    handleAudioSessionActivation: false,
  );

  @action
  iniciarTimer({required TipoContador tipo, required Function() finalizouDescanso}) async {
    switch (tipo) {
      case TipoContador.DESCANSO:
        proximoExercicio = false;
        if (GetIt.I.get<ControladorConfiguracao>().descansoHabilitado) {
          contadorDescansoExercicioAtual = int.parse(treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series?[indexSerieExercicioEmExecucao].descanso ?? '0');
          duracaoTotalDescansoExercicioAtual = int.parse(treinoEmExecucao?.atividades?[indexExercicioEmExecucao].series?[indexSerieExercicioEmExecucao].descanso ?? '0');
          break;
        } else {
          contadorDescansoExercicioAtual = 0;
          duracaoTotalDescansoExercicioAtual = 0;
          break;
        }
      case TipoContador.INICIOU:
        contadorDescansoExercicioAtual = (GetIt.I.get<ControladorConfiguracao>().countDownExercicio ?? 3) == 0 ? 1 : (GetIt.I.get<ControladorConfiguracao>().countDownExercicio ?? 3);
        duracaoTotalDescansoExercicioAtual = (GetIt.I.get<ControladorConfiguracao>().countDownExercicio ?? 3) == 0 ? 1 : (GetIt.I.get<ControladorConfiguracao>().countDownExercicio ?? 3);
        break;
      case TipoContador.PROXIMO_EXERCICIO:
        if (GetIt.I.get<ControladorConfiguracao>().avisoSonoroHabilitado && GetIt.I.get<ControladorConfiguracao>().volumeHabilitado) {
          if (Platform.isAndroid) {
            _assetsAudioPlayer.open(Audio('assets/gifs/exercicio_concluido.mp3'),
                autoStart: true, playInBackground: PlayInBackground.disabledPause, audioFocusStrategy: AudioFocusStrategy.none());
          } else {
            AudioSession.instance.then((audioSession) async {
              await audioSession.configure(const AudioSessionConfiguration(
                avAudioSessionCategory: AVAudioSessionCategory.playback,
                avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.duckOthers,
                avAudioSessionMode: AVAudioSessionMode.defaultMode,
                avAudioSessionRouteSharingPolicy: AVAudioSessionRouteSharingPolicy.defaultPolicy,
                avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
                androidAudioAttributes: AndroidAudioAttributes(
                  contentType: AndroidAudioContentType.speech,
                  flags: AndroidAudioFlags.none,
                  usage: AndroidAudioUsage.voiceCommunication,
                ),
                androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
                androidWillPauseWhenDucked: true,
              ));
              _handleInterruptions(audioSession);
              await _player.setAsset('assets/gifs/exercicio_concluido.mp3', preload: true).then((value) {
                _player.play().then((value) async {
                  await Future.delayed(const Duration(milliseconds: 500));
                  _player.stop();
                });
              });
            });
          }
        }
        contadorDescansoExercicioAtual = (GetIt.I.get<ControladorConfiguracao>().countDownExercicio ?? 3) == 0 ? 1 : (GetIt.I.get<ControladorConfiguracao>().countDownExercicio ?? 3);
        duracaoTotalDescansoExercicioAtual = (GetIt.I.get<ControladorConfiguracao>().countDownExercicio ?? 3) == 0 ? 1 : (GetIt.I.get<ControladorConfiguracao>().countDownExercicio ?? 3);
        break;
      case TipoContador.BACKGROUND:
        break;
    }
    if (contadorDescansoExercicioAtual > 0) {
      try {
        _timerDescanso?.cancel();
      } catch (e) {
        _timerDescanso = Timer(const Duration(milliseconds: 1), () {});
        _timerDescanso?.cancel();
      }
      descanso = (tipo == TipoContador.DESCANSO || tipo == TipoContador.BACKGROUND);
      const oneSec = const Duration(seconds: 1);
      _timerDescanso = new Timer.periodic(
        oneSec,
        (Timer timer) async {
          if (contadorDescansoExercicioAtual <= 1) {
            _timerDescanso?.cancel();
            descanso = false;
            treinoIniciado = true;
            proximoExercicio = false;
            finalizouDescanso();
          } else if (contadorDescansoExercicioAtual == 4 && (tipo == TipoContador.DESCANSO || tipo == TipoContador.BACKGROUND)) {
            if (GetIt.I.get<ControladorConfiguracao>().avisoSonoroHabilitado && GetIt.I.get<ControladorConfiguracao>().volumeHabilitado) {
              iniciarAudioCountdown();
            }
            contadorDescansoExercicioAtual--;
          } else if (contadorDescansoExercicioAtual == 3 && tipo == TipoContador.PROXIMO_EXERCICIO) {
            proximoExercicio = true;
            contadorDescansoExercicioAtual--;
          } else {
            contadorDescansoExercicioAtual--;
          }
        },
      );
    }
  }

  final AssetsAudioPlayer _assetsAudioPlayer = AssetsAudioPlayer.newPlayer();

  iniciarAudioCountdown() {
    if (Platform.isAndroid) {
      _assetsAudioPlayer.open(Audio('assets/gifs/countdown.mp3'), autoStart: true, playInBackground: PlayInBackground.disabledPause, audioFocusStrategy: AudioFocusStrategy.none());
    } else {
      AudioSession.instance.then((audioSession) async {
        await audioSession.configure(const AudioSessionConfiguration(
          avAudioSessionCategory: AVAudioSessionCategory.playback,
          avAudioSessionCategoryOptions: AVAudioSessionCategoryOptions.duckOthers,
          avAudioSessionMode: AVAudioSessionMode.defaultMode,
          avAudioSessionRouteSharingPolicy: AVAudioSessionRouteSharingPolicy.defaultPolicy,
          avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
          androidAudioAttributes: AndroidAudioAttributes(
            contentType: AndroidAudioContentType.speech,
            flags: AndroidAudioFlags.none,
            usage: AndroidAudioUsage.voiceCommunication,
          ),
          androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
          androidWillPauseWhenDucked: true,
        ));
        _handleInterruptions(audioSession);
        await _player.setAsset('assets/gifs/countdown.mp3', preload: true).then((value) {
          _player.play().then((value) async {
            await Future.delayed(const Duration(milliseconds: 500));
            _player.stop();
          });
        });
      });
    }
  }

  void _handleInterruptions(AudioSession audioSession) {
    // just_audio can handle interruptions for us, but we have disabled that in
    // order to demonstrate manual configuration.
    bool playInterrupted = false;
    audioSession.becomingNoisyEventStream.listen((_) {
      log.log('PAUSE');
      _player.pause();
    });
    _player.playingStream.listen((playing) {
      playInterrupted = false;
      if (playing) {
        audioSession.setActive(true);
      } else {
        audioSession.setActive(false);
      }
    });
    audioSession.interruptionEventStream.listen((event) {
      log.log('interruption begin: ${event.begin}');
      log.log('interruption type: ${event.type}');
      if (event.begin) {
        switch (event.type) {
          case AudioInterruptionType.duck:
            if (audioSession.androidAudioAttributes!.usage == AndroidAudioUsage.game) {
              _player.setVolume(_player.volume / 2);
            }
            playInterrupted = false;
            break;
          case AudioInterruptionType.pause:
          case AudioInterruptionType.unknown:
            if (_player.playing) {
              _player.pause();
              playInterrupted = true;
            }
            break;
        }
      } else {
        switch (event.type) {
          case AudioInterruptionType.duck:
            _player.setVolume(min(1.0, _player.volume * 2));
            playInterrupted = false;
            break;
          case AudioInterruptionType.pause:
            if (playInterrupted) _player.play();
            playInterrupted = false;
            break;
          case AudioInterruptionType.unknown:
            playInterrupted = false;
            break;
        }
      }
    });
    audioSession.devicesChangedEventStream.listen((event) {
      log.log('Devices added: ${event.devicesAdded}');
      log.log('Devices removed: ${event.devicesRemoved}');
    });
  }

  @action
  void editarCargaOnline({
    required List<SeriesAtividade> series,
    required Function() carregando,
    required Function() sucesso,
    required Function() error,
  }) {
    carregando();
    Future.wait(series.map((e) => serviceTreino.editarSerie({'codSerie': e.codSerie!.toInt(), 'cargaApp': e.cargaAtt!.replaceAll(',', '.'), 'repeticaoApp': e.repeticaoApp})).toList())
        .then((value) {
      sucesso();
    }).catchError((falha) {
      error.call();
    });
  }

  @action
  void pularDescanso() {
    cancelarNotificacaoDescanso();
    SharedPreferences.getInstance().then((db) async {
      await db.setInt('DURACAO_DESCANSO_RESTANTE', 0);
    });
    descanso = false;
    treinoIniciado = true;
    try {
      _timerDescanso?.cancel();
    } catch (e) {
      _timerDescanso = Timer(const Duration(milliseconds: 1), () {});
      _timerDescanso?.cancel();
    }
    _timerDescanso?.cancel();
    duracaoTotalDescansoExercicioAtual = 0;
    contadorDescansoExercicioAtual = 0;
  }

  @action
  finalizarTreinoEmExecucao() async {
    duracaoTreino = 0;
    treinoIniciado = false;
    treinoEmExecucao = null;
    descanso = false;
    duracaoTotalDescansoExercicioAtual = 0;
    contadorDescansoExercicioAtual = 0;
    indexExercicioEmExecucao = 0;
    indexSelecionado = indexExercicioEmExecucao;
    indexSerieExercicioEmExecucao = 0;
    try {
      _timerDescanso?.cancel();
    } catch (e) {
      _timerDescanso = Timer(const Duration(milliseconds: 1), () {});
      _timerDescanso?.cancel();
    }
    try {
      _timerTreino?.cancel();
    } catch (e) {
      _timerTreino = Timer(const Duration(milliseconds: 1), () {});
      _timerTreino?.cancel();
    }
    SharedPreferences.getInstance().then((db) async {
      db.remove('TREINO_EM_EXECUCAO');
      db.remove('TREINO_EM_EXECUCAO_HORARIO_INATIVOU');
      db.remove('TREINO_EM_EXECUCAO_EXERCICIO_INDEX');
      db.remove('TREINO_EM_EXECUCAO_SERIE_INDEX');
      db.remove('DURACAO_TREINO_EM_EXECUCAO');
      db.remove('DURACAO_DESCANSO_RESTANTE');
    });
  }

  finalizarTreinoAppleWatch() async {
    await channel.invokeMethod('flutterToWatch', {'method': 'encerrouTreinoPeloIphone', 'data': ''});
  }

  pausarOTreinoAppleWatch() async {
    await channel.invokeMethod('flutterToWatch', {'method': 'pausouTreinoPeloIphone', 'data': ''});
  }

  retomarOTreinoAppleWatch() async {
    await channel.invokeMethod('flutterToWatch', {'method': 'retomouTreinoPeloIphone', 'data': ''});
  }

  /**
   * Envia o treino para o Apple Watch que está em execução
   */
  Future<void> enviarTreinoParaAppleWatch(treinoEmExecucao) async {
    var treino = treinoEmExecucao == null ? await getTreinoEmExecucao() : treinoEmExecucao;
    var treinoEncodado = jsonEncode(treino);
    if (treino != null) {
      await channel.invokeMethod('flutterToWatch', {'method': 'receberFichaEmExecucao', 'data': treinoEncodado});
    }
  }

  /**
   * Envia dados do usuário e as URLS para o Apple Watch 
   * utilizar nas requisições POST / GET de aulas e WOD
   */
  Future<void> enviarDadosAlunoEURLParaAppleWatch(context) async {
    var urlTreino = '';
    await SharedPreferences.getInstance().then((db) async {
      urlTreino = await db.getString(ConfigURL.TREINO.toString()) ?? '';
    });
    var dadosJSON = {
      'codigoUsuario': GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codUsuario,
      'urlTreino': urlTreino,
      'codigoEmpresa': GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa,
      'matricula': GetIt.I.get<ControladorCliente>().mUsuarioLogado!.matricula,
      'chave': GetIt.I.get<ControladorApp>().chave,
      'corPrimaria': Theme.of(context).primaryColor.toHex(leadingHashSign: false).toString(),
      'nome': UtilitarioApp.sentenseCaseFirst(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.nome!.split(' ')[0]),
      'urlFoto': GetIt.I.get<ControladorCliente>().mUsuarioLogado!.srcImg ?? '',
      'codigoContrato': GetIt.I.get<ControladorAulaTurma>().codigoContrato(),
      'nomeAcademia': UtilitarioApp.sentenseCase((GetIt.I.get<ControladorCliente>().mUsuarioLogado?.nomeEmpresa ?? '')),
    };
    var dadosEncodado = jsonEncode(dadosJSON);
    await channel.invokeMethod('flutterToWatch', {'method': 'receberDadosUsuario', 'data': dadosEncodado});
  }

  /**
   * Cria um canal de comunicação entre o Flutter e o Apple Watch e 
   * verifica se existe um apple watch, se ele está conectado e se ele está próximo.
   * O nome do canal é: receberFichaEmExecucaoWatch
   * Recebe uma ficha no formato String do Apple Watch e atualiza a execução no aplicativo.
   */
  Future<void> iniciarComunicacaoEntreAppleWatchEApp(context) async {
    channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'receberFichaEmExecucaoWatch':
          try {
            var treinoEmExecucaoB = Ficha();
            treinoEmExecucaoB = Ficha.fromJson(jsonDecode(call.arguments['data']));
            indexExercicioEmExecucao = (treinoEmExecucaoB.indexExercicioEmExecucao ?? 0).toInt();
            indexSelecionado = (treinoEmExecucaoB.indexExercicioEmExecucao ?? 0).toInt();
            indexSerieExercicioEmExecucao = (treinoEmExecucaoB.indexSerieExercicioEmExecucao ?? 0).toInt();
            treinoEmExecucao = treinoEmExecucaoB;
            GetIt.I.get<ControladorEventBus>().eventBus.fire(ConcluiuSeriePeloWatchEvent(true));
          } catch (e) {}
          break;
        case 'fazerDownloadFicha':
          try {
            enviarTreinoParaAppleWatch(treinoEmExecucao);
          } catch (e) {}
          break;
        case 'fazerDownloadDadosUsuario':
          try {
            enviarDadosAlunoEURLParaAppleWatch(context);
          } catch (e) {}
          break;
        case 'encerrouOTreinoPeloWatch':
          try {
            var treinoEmExecucaoB = Ficha();
            treinoEmExecucaoB = Ficha.fromJson(jsonDecode(call.arguments['data']));
            treinoEmExecucaoB.atividades?.forEach((atividade) {
              atividade.concluida = true;
              atividade.series?.forEach((serie) {
                serie.concluida = true;
              });
            });
            indexExercicioEmExecucao = (treinoEmExecucaoB.indexExercicioEmExecucao ?? 0).toInt();
            indexSelecionado = (treinoEmExecucaoB.indexExercicioEmExecucao ?? 0).toInt();
            indexSerieExercicioEmExecucao = (treinoEmExecucaoB.indexSerieExercicioEmExecucao ?? 0).toInt();
            treinoEmExecucao = treinoEmExecucaoB;
            GetIt.I.get<ControladorEventBus>().eventBus.fire(ConcluiuSeriePeloWatchEvent(false));
          } catch (e) {}
          break;
        case 'pausouOTreinoPeloWatch':
          GetIt.I.get<ControladorEventBus>().eventBus.fire(PausouOTreinoPeloWatchEvent(false));
          break;
        default:
          break;
      }
    });
  }

  @action
  void consultarExercicioPorNome({required String nome}) {
    filtrandoNomeExercicio = ServiceStatus.Waiting;
    listaExerciciosTreinoExtra.clear();
    if (nome.isEmpty) {
      listaExerciciosTreinoExtra.addAll(listaExerciciosTreinoExtraBackup);
      filtrandoNomeExercicio = ServiceStatus.Done;
    } else {
      listaExerciciosTreinoExtra.addAll(listaExerciciosTreinoExtraBackup.where((element) => removeDiacritics(localizedString(element.nome).toLowerCase()).contains(removeDiacritics(nome.toLowerCase()))).toList());
      filtrandoNomeExercicio = ServiceStatus.Done;
    }
  }

  @observable
  ObservableList<ExerciciosTreinoExtra> listaExerciciosTreinoExtra = ObservableList<ExerciciosTreinoExtra>();

  @observable
  Ficha? fichaSelecionadaTreinoExtra;

  @observable
  ObservableList<ExerciciosTreinoExtra> listaExerciciosTreinoExtraBackup = ObservableList<ExerciciosTreinoExtra>();

  popularTreinoExtra() {
    listaExerciciosTreinoExtra.clear();
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'Registrar treino do programa', tipo: TipoTreinoExtra.FICHA, icone: TreinoIcon.Mindfull, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'alongamento', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.body_alongamento, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'arremesso_de_disco', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.fire, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'artes_marciais', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.medal, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'badminton', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.table_tennis, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'basquete', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.basketball_hoop, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'beach_tennis', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.table_tennis, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'caminhada_ao_ar_livre', tipo: TipoTreinoExtra.TEMPO_CALORIAS_DISTANCIA, icone: TreinoIcon.Step, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'caminhada_com_carrinho_de_bebe', tipo: TipoTreinoExtra.TEMPO_CALORIAS_DISTANCIA, icone: TreinoIcon.Step, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'caminhada_com_peso', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.Step, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'caminhada_em_esteira', tipo: TipoTreinoExtra.TEMPO_CALORIAS_DISTANCIA, icone: TreinoIcon.Step, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'caminhada_em_montanhas', tipo: TipoTreinoExtra.TEMPO_CALORIAS_DISTANCIA, icone: TreinoIcon.Step, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'ciclismo_ao_ar_livre', tipo: TipoTreinoExtra.TEMPO_CALORIAS_DISTANCIA, icone: TreinoIcon.fire, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'ciclismo_indoor', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.fire, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'corrida_ao_ar_livre', tipo: TipoTreinoExtra.TEMPO_CALORIAS_DISTANCIA, icone: TreinoIcon.Step, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'corrida_na_esteira', tipo: TipoTreinoExtra.TEMPO_CALORIAS_DISTANCIA, icone: TreinoIcon.Step, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'danca', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.users_alt, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'eliptico', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.record_audio, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'escalada', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.fire, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'escalador', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.fire, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'esportes_de_raquete', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.table_tennis, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'futebol', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.football, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'golfe', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.golf_ball, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'hiit', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.fire, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'hoquei_no_gelo', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.fire, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'ioga', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.heart, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'meditacao', tipo: TipoTreinoExtra.TEMPO, icone: TreinoIcon.yin_yang, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'mergulho', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.water, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'natacao_em_aguas_abertas', tipo: TipoTreinoExtra.TEMPO_CALORIAS_DISTANCIA, icone: TreinoIcon.swimmer, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'natacao_em_piscina', tipo: TipoTreinoExtra.TEMPO_CALORIAS_VOLTAS, icone: TreinoIcon.swimmer, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'padel', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.table_tennis, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'patinacao', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.fire, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'pilates', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.fire, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'pular_corda', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.fire, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'remo_indoor', tipo: TipoTreinoExtra.TEMPO_CALORIAS_DISTANCIA, icone: TreinoIcon.kayak, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'squash', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.table_tennis, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'tenis', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.table_tennis, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'tenis_de_mesa', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.table_tennis, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'treinamento_de_forca', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.dumbbell, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'treinamento_funcional', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.kg, selecionado: false));
    listaExerciciosTreinoExtra.add(ExerciciosTreinoExtra(nome: 'Outro', tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, icone: TreinoIcon.Mindfull, selecionado: false));
    if (listaExerciciosTreinoExtraBackup.isEmpty) {
      listaExerciciosTreinoExtraBackup.addAll(listaExerciciosTreinoExtra);
    }
  }
}

class ExerciciosTreinoExtra {
  String nome;
  TipoTreinoExtra tipo;
  IconData icone;
  bool selecionado;

  ExerciciosTreinoExtra({required this.nome, required this.tipo, required this.icone, required this.selecionado});
}

enum TipoTreinoExtra {
  TEMPO_E_CALORIAS,
  TEMPO_CALORIAS_DISTANCIA,
  TEMPO_CALORIAS_VOLTAS,
  TEMPO,
  FICHA
}

enum TipoContador { DESCANSO, INICIOU, PROXIMO_EXERCICIO, BACKGROUND }
