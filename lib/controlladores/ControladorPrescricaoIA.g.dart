// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorPrescricaoIA.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorPrescricaoIA on _ControladorPrescricaoIABase, Store {
  late final _$statusConsultarTreinosRevisaoAtom = Atom(
      name: '_ControladorPrescricaoIABase.statusConsultarTreinosRevisao',
      context: context);

  @override
  ServiceStatus get statusConsultarTreinosRevisao {
    _$statusConsultarTreinosRevisaoAtom.reportRead();
    return super.statusConsultarTreinosRevisao;
  }

  @override
  set statusConsultarTreinosRevisao(ServiceStatus value) {
    _$statusConsultarTreinosRevisaoAtom
        .reportWrite(value, super.statusConsultarTreinosRevisao, () {
      super.statusConsultarTreinosRevisao = value;
    });
  }

  late final _$listaProgramasTreinoAguardandoRevisaoAtom = Atom(
      name:
          '_ControladorPrescricaoIABase.listaProgramasTreinoAguardandoRevisao',
      context: context);

  @override
  ObservableList<DadosUsuarioProgramaIA>
      get listaProgramasTreinoAguardandoRevisao {
    _$listaProgramasTreinoAguardandoRevisaoAtom.reportRead();
    return super.listaProgramasTreinoAguardandoRevisao;
  }

  @override
  set listaProgramasTreinoAguardandoRevisao(
      ObservableList<DadosUsuarioProgramaIA> value) {
    _$listaProgramasTreinoAguardandoRevisaoAtom
        .reportWrite(value, super.listaProgramasTreinoAguardandoRevisao, () {
      super.listaProgramasTreinoAguardandoRevisao = value;
    });
  }

  late final _$programaRevisaoIAAtom = Atom(
      name: '_ControladorPrescricaoIABase.programaRevisaoIA', context: context);

  @override
  DadosUsuarioProgramaIA? get programaRevisaoIA {
    _$programaRevisaoIAAtom.reportRead();
    return super.programaRevisaoIA;
  }

  @override
  set programaRevisaoIA(DadosUsuarioProgramaIA? value) {
    _$programaRevisaoIAAtom.reportWrite(value, super.programaRevisaoIA, () {
      super.programaRevisaoIA = value;
    });
  }

  late final _$fichaAtom =
      Atom(name: '_ControladorPrescricaoIABase.ficha', context: context);

  @override
  Fichas? get ficha {
    _$fichaAtom.reportRead();
    return super.ficha;
  }

  @override
  set ficha(Fichas? value) {
    _$fichaAtom.reportWrite(value, super.ficha, () {
      super.ficha = value;
    });
  }

  late final _$listaProgramasAprovadosAtom = Atom(
      name: '_ControladorPrescricaoIABase.listaProgramasAprovados',
      context: context);

  @override
  ObservableList<ProgramaTreino> get listaProgramasAprovados {
    _$listaProgramasAprovadosAtom.reportRead();
    return super.listaProgramasAprovados;
  }

  @override
  set listaProgramasAprovados(ObservableList<ProgramaTreino> value) {
    _$listaProgramasAprovadosAtom
        .reportWrite(value, super.listaProgramasAprovados, () {
      super.listaProgramasAprovados = value;
    });
  }

  late final _$tokenIAAtom =
      Atom(name: '_ControladorPrescricaoIABase.tokenIA', context: context);

  @override
  String get tokenIA {
    _$tokenIAAtom.reportRead();
    return super.tokenIA;
  }

  @override
  set tokenIA(String value) {
    _$tokenIAAtom.reportWrite(value, super.tokenIA, () {
      super.tokenIA = value;
    });
  }

  late final _$consultarTreinosPorSituacaoAsyncAction = AsyncAction(
      '_ControladorPrescricaoIABase.consultarTreinosPorSituacao',
      context: context);

  @override
  Future consultarTreinosPorSituacao() {
    return _$consultarTreinosPorSituacaoAsyncAction
        .run(() => super.consultarTreinosPorSituacao());
  }

  late final _$_ControladorPrescricaoIABaseActionController =
      ActionController(name: '_ControladorPrescricaoIABase', context: context);

  @override
  dynamic iniciarRevisaoTreinoIA(
      {required DadosUsuarioProgramaIA treinoIA,
      required dynamic Function() sucesso,
      required dynamic Function() carregando,
      required dynamic Function(String) erro}) {
    final _$actionInfo =
        _$_ControladorPrescricaoIABaseActionController.startAction(
            name: '_ControladorPrescricaoIABase.iniciarRevisaoTreinoIA');
    try {
      return super.iniciarRevisaoTreinoIA(
          treinoIA: treinoIA,
          sucesso: sucesso,
          carregando: carregando,
          erro: erro);
    } finally {
      _$_ControladorPrescricaoIABaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic consultarTreinosPredefinidosIA(
      {required num codigoFicha,
      required dynamic Function() sucesso,
      required dynamic Function() carregando,
      required dynamic Function(String) erro}) {
    final _$actionInfo =
        _$_ControladorPrescricaoIABaseActionController.startAction(
            name:
                '_ControladorPrescricaoIABase.consultarTreinosPredefinidosIA');
    try {
      return super.consultarTreinosPredefinidosIA(
          codigoFicha: codigoFicha,
          sucesso: sucesso,
          carregando: carregando,
          erro: erro);
    } finally {
      _$_ControladorPrescricaoIABaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
statusConsultarTreinosRevisao: ${statusConsultarTreinosRevisao},
listaProgramasTreinoAguardandoRevisao: ${listaProgramasTreinoAguardandoRevisao},
programaRevisaoIA: ${programaRevisaoIA},
ficha: ${ficha},
listaProgramasAprovados: ${listaProgramasAprovados},
tokenIA: ${tokenIA}
    ''';
  }
}
