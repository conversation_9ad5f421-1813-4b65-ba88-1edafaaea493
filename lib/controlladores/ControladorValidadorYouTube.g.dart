// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorValidadorYouTube.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorValidadorYouTube on _ControladorValidadorYouTubeBase, Store {
  late final _$thumbAtom =
      Atom(name: '_ControladorValidadorYouTubeBase.thumb', context: context);

  @override
  String? get thumb {
    _$thumbAtom.reportRead();
    return super.thumb;
  }

  @override
  set thumb(String? value) {
    _$thumbAtom.reportWrite(value, super.thumb, () {
      super.thumb = value;
    });
  }

  late final _$videoNameAtom = Atom(
      name: '_ControladorValidadorYouTubeBase.videoName', context: context);

  @override
  String? get videoName {
    _$videoNameAtom.reportRead();
    return super.videoName;
  }

  @override
  set videoName(String? value) {
    _$videoNameAtom.reportWrite(value, super.videoName, () {
      super.videoName = value;
    });
  }

  late final _$videoDurationAtom = Atom(
      name: '_ControladorValidadorYouTubeBase.videoDuration', context: context);

  @override
  String? get videoDuration {
    _$videoDurationAtom.reportRead();
    return super.videoDuration;
  }

  @override
  set videoDuration(String? value) {
    _$videoDurationAtom.reportWrite(value, super.videoDuration, () {
      super.videoDuration = value;
    });
  }

  late final _$ytVideoIDAtom = Atom(
      name: '_ControladorValidadorYouTubeBase.ytVideoID', context: context);

  @override
  String? get ytVideoID {
    _$ytVideoIDAtom.reportRead();
    return super.ytVideoID;
  }

  @override
  set ytVideoID(String? value) {
    _$ytVideoIDAtom.reportWrite(value, super.ytVideoID, () {
      super.ytVideoID = value;
    });
  }

  late final _$falhaAtom =
      Atom(name: '_ControladorValidadorYouTubeBase.falha', context: context);

  @override
  bool get falha {
    _$falhaAtom.reportRead();
    return super.falha;
  }

  @override
  set falha(bool value) {
    _$falhaAtom.reportWrite(value, super.falha, () {
      super.falha = value;
    });
  }

  @override
  String toString() {
    return '''
thumb: ${thumb},
videoName: ${videoName},
videoDuration: ${videoDuration},
ytVideoID: ${ytVideoID},
falha: ${falha}
    ''';
  }
}
