// To parse this JSON data, do
//
//     final videoYoutbeInfo = videoYoutbeInfoFromJson(jsonString);

import 'dart:convert';

VideoYoutbeInfo videoYoutbeInfoFromJson(String str) => VideoYoutbeInfo.fromJson(const JsonCodec().decode(str));

String videoYoutbeInfoToJson(VideoYoutbeInfo data) => json.encode(data.toJson());

class VideoYoutbeInfo {
  String? authorName;
  String? providerUrl;
  int? thumbnailHeight;
  int? height;
  String? providerName;
  String? type;
  String? thumbnailUrl;
  String? title;
  String? version;
  String? html;
  int? thumbnailWidth;
  String? authorUrl;
  int? width;

  VideoYoutbeInfo({
    this.authorName,
    this.providerUrl,
    this.thumbnailHeight,
    this.height,
    this.providerName,
    this.type,
    this.thumbnailUrl,
    this.title,
    this.version,
    this.html,
    this.thumbnailWidth,
    this.authorUrl,
    this.width,
  });

  factory VideoYoutbeInfo.fromJson(Map<String, dynamic> json) => VideoYoutbeInfo(
        authorName: json['author_name'],
        providerUrl: json['provider_url'],
        thumbnailHeight: json['thumbnail_height'],
        height: json['height'],
        providerName: json['provider_name'],
        type: json['type'],
        thumbnailUrl: json['thumbnail_url'],
        title: json['title'],
        version: json['version'],
        html: json['html'],
        thumbnailWidth: json['thumbnail_width'],
        authorUrl: json['author_url'],
        width: json['width'],
      );

  Map<String, dynamic> toJson() => {
        'author_name': authorName,
        'provider_url': providerUrl,
        'thumbnail_height': thumbnailHeight,
        'height': height,
        'provider_name': providerName,
        'type': type,
        'thumbnail_url': thumbnailUrl,
        'title': title,
        'version': version,
        'html': html,
        'thumbnail_width': thumbnailWidth,
        'author_url': authorUrl,
        'width': width,
      };
}
