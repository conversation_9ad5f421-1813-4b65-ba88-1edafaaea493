// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorAulaTurma.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorAulaTurma on _ControladorAulaTurmaBase, Store {
  late final _$consultaAulaTurmasAtom = Atom(
      name: '_ControladorAulaTurmaBase.consultaAulaTurmas', context: context);

  @override
  ServiceStatus get consultaAulaTurmas {
    _$consultaAulaTurmasAtom.reportRead();
    return super.consultaAulaTurmas;
  }

  @override
  set consultaAulaTurmas(ServiceStatus value) {
    _$consultaAulaTurmasAtom.reportWrite(value, super.consultaAulaTurmas, () {
      super.consultaAulaTurmas = value;
    });
  }

  late final _$estaPesquisandoAtom =
      Atom(name: '_ControladorAulaTurmaBase.estaPesquisando', context: context);

  @override
  bool get estaPesquisando {
    _$estaPesquisandoAtom.reportRead();
    return super.estaPesquisando;
  }

  @override
  set estaPesquisando(bool value) {
    _$estaPesquisandoAtom.reportWrite(value, super.estaPesquisando, () {
      super.estaPesquisando = value;
    });
  }

  late final _$aulaAgendadaAtom =
      Atom(name: '_ControladorAulaTurmaBase.aulaAgendada', context: context);

  @override
  bool get aulaAgendada {
    _$aulaAgendadaAtom.reportRead();
    return super.aulaAgendada;
  }

  @override
  set aulaAgendada(bool value) {
    _$aulaAgendadaAtom.reportWrite(value, super.aulaAgendada, () {
      super.aulaAgendada = value;
    });
  }

  late final _$aulaDesmarcadaAtom =
      Atom(name: '_ControladorAulaTurmaBase.aulaDesmarcada', context: context);

  @override
  bool get aulaDesmarcada {
    _$aulaDesmarcadaAtom.reportRead();
    return super.aulaDesmarcada;
  }

  @override
  set aulaDesmarcada(bool value) {
    _$aulaDesmarcadaAtom.reportWrite(value, super.aulaDesmarcada, () {
      super.aulaDesmarcada = value;
    });
  }

  late final _$pesquisaDeAulasTurmasAtom = Atom(
      name: '_ControladorAulaTurmaBase.pesquisaDeAulasTurmas',
      context: context);

  @override
  ServiceStatus get pesquisaDeAulasTurmas {
    _$pesquisaDeAulasTurmasAtom.reportRead();
    return super.pesquisaDeAulasTurmas;
  }

  @override
  set pesquisaDeAulasTurmas(ServiceStatus value) {
    _$pesquisaDeAulasTurmasAtom.reportWrite(value, super.pesquisaDeAulasTurmas,
        () {
      super.pesquisaDeAulasTurmas = value;
    });
  }

  late final _$statusConsultarHistoricoAulasAtom = Atom(
      name: '_ControladorAulaTurmaBase.statusConsultarHistoricoAulas',
      context: context);

  @override
  ServiceStatus get statusConsultarHistoricoAulas {
    _$statusConsultarHistoricoAulasAtom.reportRead();
    return super.statusConsultarHistoricoAulas;
  }

  @override
  set statusConsultarHistoricoAulas(ServiceStatus value) {
    _$statusConsultarHistoricoAulasAtom
        .reportWrite(value, super.statusConsultarHistoricoAulas, () {
      super.statusConsultarHistoricoAulas = value;
    });
  }

  late final _$consultarAlunosTurmaAulaAtom = Atom(
      name: '_ControladorAulaTurmaBase.consultarAlunosTurmaAula',
      context: context);

  @override
  ServiceStatus get consultarAlunosTurmaAula {
    _$consultarAlunosTurmaAulaAtom.reportRead();
    return super.consultarAlunosTurmaAula;
  }

  @override
  set consultarAlunosTurmaAula(ServiceStatus value) {
    _$consultarAlunosTurmaAulaAtom
        .reportWrite(value, super.consultarAlunosTurmaAula, () {
      super.consultarAlunosTurmaAula = value;
    });
  }

  late final _$consultarAlunosNaAulaHistoricoAtom = Atom(
      name: '_ControladorAulaTurmaBase.consultarAlunosNaAulaHistorico',
      context: context);

  @override
  ServiceStatus get consultarAlunosNaAulaHistorico {
    _$consultarAlunosNaAulaHistoricoAtom.reportRead();
    return super.consultarAlunosNaAulaHistorico;
  }

  @override
  set consultarAlunosNaAulaHistorico(ServiceStatus value) {
    _$consultarAlunosNaAulaHistoricoAtom
        .reportWrite(value, super.consultarAlunosNaAulaHistorico, () {
      super.consultarAlunosNaAulaHistorico = value;
    });
  }

  late final _$consultarSaldoAlunoAtom = Atom(
      name: '_ControladorAulaTurmaBase.consultarSaldoAluno', context: context);

  @override
  ServiceStatus get consultarSaldoAluno {
    _$consultarSaldoAlunoAtom.reportRead();
    return super.consultarSaldoAluno;
  }

  @override
  set consultarSaldoAluno(ServiceStatus value) {
    _$consultarSaldoAlunoAtom.reportWrite(value, super.consultarSaldoAluno, () {
      super.consultarSaldoAluno = value;
    });
  }

  late final _$mProgramaAlunoAtom =
      Atom(name: '_ControladorAulaTurmaBase.mProgramaAluno', context: context);

  @override
  ProgramaDeTreino? get mProgramaAluno {
    _$mProgramaAlunoAtom.reportRead();
    return super.mProgramaAluno;
  }

  @override
  set mProgramaAluno(ProgramaDeTreino? value) {
    _$mProgramaAlunoAtom.reportWrite(value, super.mProgramaAluno, () {
      super.mProgramaAluno = value;
    });
  }

  late final _$alunoEmFeriasAtom =
      Atom(name: '_ControladorAulaTurmaBase.alunoEmFerias', context: context);

  @override
  bool get alunoEmFerias {
    _$alunoEmFeriasAtom.reportRead();
    return super.alunoEmFerias;
  }

  @override
  set alunoEmFerias(bool value) {
    _$alunoEmFeriasAtom.reportWrite(value, super.alunoEmFerias, () {
      super.alunoEmFerias = value;
    });
  }

  late final _$alunoTrancadoAtom =
      Atom(name: '_ControladorAulaTurmaBase.alunoTrancado', context: context);

  @override
  bool get alunoTrancado {
    _$alunoTrancadoAtom.reportRead();
    return super.alunoTrancado;
  }

  @override
  set alunoTrancado(bool value) {
    _$alunoTrancadoAtom.reportWrite(value, super.alunoTrancado, () {
      super.alunoTrancado = value;
    });
  }

  late final _$contratoMarcacaoAulaAtom = Atom(
      name: '_ControladorAulaTurmaBase.contratoMarcacaoAula', context: context);

  @override
  ContratoUsuario? get contratoMarcacaoAula {
    _$contratoMarcacaoAulaAtom.reportRead();
    return super.contratoMarcacaoAula;
  }

  @override
  set contratoMarcacaoAula(ContratoUsuario? value) {
    _$contratoMarcacaoAulaAtom.reportWrite(value, super.contratoMarcacaoAula,
        () {
      super.contratoMarcacaoAula = value;
    });
  }

  late final _$termoBuscaUnidadeAtom = Atom(
      name: '_ControladorAulaTurmaBase.termoBuscaUnidade', context: context);

  @override
  String get termoBuscaUnidade {
    _$termoBuscaUnidadeAtom.reportRead();
    return super.termoBuscaUnidade;
  }

  @override
  set termoBuscaUnidade(String value) {
    _$termoBuscaUnidadeAtom.reportWrite(value, super.termoBuscaUnidade, () {
      super.termoBuscaUnidade = value;
    });
  }

  late final _$empresaSelecionadaAtom = Atom(
      name: '_ControladorAulaTurmaBase.empresaSelecionada', context: context);

  @override
  EmpresaRede? get empresaSelecionada {
    _$empresaSelecionadaAtom.reportRead();
    return super.empresaSelecionada;
  }

  @override
  set empresaSelecionada(EmpresaRede? value) {
    _$empresaSelecionadaAtom.reportWrite(value, super.empresaSelecionada, () {
      super.empresaSelecionada = value;
    });
  }

  late final _$mListaSomenteAlunosAulaAtom = Atom(
      name: '_ControladorAulaTurmaBase.mListaSomenteAlunosAula',
      context: context);

  @override
  List<AlunoNaAulaTurma> get mListaSomenteAlunosAula {
    _$mListaSomenteAlunosAulaAtom.reportRead();
    return super.mListaSomenteAlunosAula;
  }

  @override
  set mListaSomenteAlunosAula(List<AlunoNaAulaTurma> value) {
    _$mListaSomenteAlunosAulaAtom
        .reportWrite(value, super.mListaSomenteAlunosAula, () {
      super.mListaSomenteAlunosAula = value;
    });
  }

  late final _$mListaSomenteAlunosAulaBKPAtom = Atom(
      name: '_ControladorAulaTurmaBase.mListaSomenteAlunosAulaBKP',
      context: context);

  @override
  List<AlunoNaAulaTurma> get mListaSomenteAlunosAulaBKP {
    _$mListaSomenteAlunosAulaBKPAtom.reportRead();
    return super.mListaSomenteAlunosAulaBKP;
  }

  @override
  set mListaSomenteAlunosAulaBKP(List<AlunoNaAulaTurma> value) {
    _$mListaSomenteAlunosAulaBKPAtom
        .reportWrite(value, super.mListaSomenteAlunosAulaBKP, () {
      super.mListaSomenteAlunosAulaBKP = value;
    });
  }

  late final _$saldoParaAulasTurmasReposicaoAtom = Atom(
      name: '_ControladorAulaTurmaBase.saldoParaAulasTurmasReposicao',
      context: context);

  @override
  num get saldoParaAulasTurmasReposicao {
    _$saldoParaAulasTurmasReposicaoAtom.reportRead();
    return super.saldoParaAulasTurmasReposicao;
  }

  @override
  set saldoParaAulasTurmasReposicao(num value) {
    _$saldoParaAulasTurmasReposicaoAtom
        .reportWrite(value, super.saldoParaAulasTurmasReposicao, () {
      super.saldoParaAulasTurmasReposicao = value;
    });
  }

  late final _$saldoParaAulasTurmasCreditoAtom = Atom(
      name: '_ControladorAulaTurmaBase.saldoParaAulasTurmasCredito',
      context: context);

  @override
  num get saldoParaAulasTurmasCredito {
    _$saldoParaAulasTurmasCreditoAtom.reportRead();
    return super.saldoParaAulasTurmasCredito;
  }

  @override
  set saldoParaAulasTurmasCredito(num value) {
    _$saldoParaAulasTurmasCreditoAtom
        .reportWrite(value, super.saldoParaAulasTurmasCredito, () {
      super.saldoParaAulasTurmasCredito = value;
    });
  }

  late final _$todasAsAulasAtom =
      Atom(name: '_ControladorAulaTurmaBase.todasAsAulas', context: context);

  @override
  ObservableList<AulaTurma> get todasAsAulas {
    _$todasAsAulasAtom.reportRead();
    return super.todasAsAulas;
  }

  @override
  set todasAsAulas(ObservableList<AulaTurma> value) {
    _$todasAsAulasAtom.reportWrite(value, super.todasAsAulas, () {
      super.todasAsAulas = value;
    });
  }

  late final _$todasAsAulasBackupAtom = Atom(
      name: '_ControladorAulaTurmaBase.todasAsAulasBackup', context: context);

  @override
  ObservableList<AulaTurma> get todasAsAulasBackup {
    _$todasAsAulasBackupAtom.reportRead();
    return super.todasAsAulasBackup;
  }

  @override
  set todasAsAulasBackup(ObservableList<AulaTurma> value) {
    _$todasAsAulasBackupAtom.reportWrite(value, super.todasAsAulasBackup, () {
      super.todasAsAulasBackup = value;
    });
  }

  late final _$saldoAulaColetivaAtom = Atom(
      name: '_ControladorAulaTurmaBase.saldoAulaColetiva', context: context);

  @override
  SaldoAulaColetiva? get saldoAulaColetiva {
    _$saldoAulaColetivaAtom.reportRead();
    return super.saldoAulaColetiva;
  }

  @override
  set saldoAulaColetiva(SaldoAulaColetiva? value) {
    _$saldoAulaColetivaAtom.reportWrite(value, super.saldoAulaColetiva, () {
      super.saldoAulaColetiva = value;
    });
  }

  late final _$mAulaTurmaSimplificadoAtom = Atom(
      name: '_ControladorAulaTurmaBase.mAulaTurmaSimplificado',
      context: context);

  @override
  AulaTurmaSimplificado? get mAulaTurmaSimplificado {
    _$mAulaTurmaSimplificadoAtom.reportRead();
    return super.mAulaTurmaSimplificado;
  }

  @override
  set mAulaTurmaSimplificado(AulaTurmaSimplificado? value) {
    _$mAulaTurmaSimplificadoAtom
        .reportWrite(value, super.mAulaTurmaSimplificado, () {
      super.mAulaTurmaSimplificado = value;
    });
  }

  late final _$retornoErroAtom =
      Atom(name: '_ControladorAulaTurmaBase.retornoErro', context: context);

  @override
  Map<String, String>? get retornoErro {
    _$retornoErroAtom.reportRead();
    return super.retornoErro;
  }

  @override
  set retornoErro(Map<String, String>? value) {
    _$retornoErroAtom.reportWrite(value, super.retornoErro, () {
      super.retornoErro = value;
    });
  }

  late final _$mListaAlunosFilaDeEsperaAtom = Atom(
      name: '_ControladorAulaTurmaBase.mListaAlunosFilaDeEspera',
      context: context);

  @override
  List<AlunoFilaDeEspera> get mListaAlunosFilaDeEspera {
    _$mListaAlunosFilaDeEsperaAtom.reportRead();
    return super.mListaAlunosFilaDeEspera;
  }

  @override
  set mListaAlunosFilaDeEspera(List<AlunoFilaDeEspera> value) {
    _$mListaAlunosFilaDeEsperaAtom
        .reportWrite(value, super.mListaAlunosFilaDeEspera, () {
      super.mListaAlunosFilaDeEspera = value;
    });
  }

  late final _$aulaSelecionadaDetalhesAtom = Atom(
      name: '_ControladorAulaTurmaBase.aulaSelecionadaDetalhes',
      context: context);

  @override
  AulaTurma get aulaSelecionadaDetalhes {
    _$aulaSelecionadaDetalhesAtom.reportRead();
    return super.aulaSelecionadaDetalhes;
  }

  @override
  set aulaSelecionadaDetalhes(AulaTurma value) {
    _$aulaSelecionadaDetalhesAtom
        .reportWrite(value, super.aulaSelecionadaDetalhes, () {
      super.aulaSelecionadaDetalhes = value;
    });
  }

  late final _$consultarAlunosNaFilaAtom = Atom(
      name: '_ControladorAulaTurmaBase.consultarAlunosNaFila',
      context: context);

  @override
  ServiceStatus get consultarAlunosNaFila {
    _$consultarAlunosNaFilaAtom.reportRead();
    return super.consultarAlunosNaFila;
  }

  @override
  set consultarAlunosNaFila(ServiceStatus value) {
    _$consultarAlunosNaFilaAtom.reportWrite(value, super.consultarAlunosNaFila,
        () {
      super.consultarAlunosNaFila = value;
    });
  }

  late final _$mListaEquipamentosReservadosAtom = Atom(
      name: '_ControladorAulaTurmaBase.mListaEquipamentosReservados',
      context: context);

  @override
  List<String> get mListaEquipamentosReservados {
    _$mListaEquipamentosReservadosAtom.reportRead();
    return super.mListaEquipamentosReservados;
  }

  @override
  set mListaEquipamentosReservados(List<String> value) {
    _$mListaEquipamentosReservadosAtom
        .reportWrite(value, super.mListaEquipamentosReservados, () {
      super.mListaEquipamentosReservados = value;
    });
  }

  late final _$mListaEquipamentosOcupadosAtom = Atom(
      name: '_ControladorAulaTurmaBase.mListaEquipamentosOcupados',
      context: context);

  @override
  List<String> get mListaEquipamentosOcupados {
    _$mListaEquipamentosOcupadosAtom.reportRead();
    return super.mListaEquipamentosOcupados;
  }

  @override
  set mListaEquipamentosOcupados(List<String> value) {
    _$mListaEquipamentosOcupadosAtom
        .reportWrite(value, super.mListaEquipamentosOcupados, () {
      super.mListaEquipamentosOcupados = value;
    });
  }

  late final _$itensAtualizadosParaReservaAtom = Atom(
      name: '_ControladorAulaTurmaBase.itensAtualizadosParaReserva',
      context: context);

  @override
  bool get itensAtualizadosParaReserva {
    _$itensAtualizadosParaReservaAtom.reportRead();
    return super.itensAtualizadosParaReserva;
  }

  @override
  set itensAtualizadosParaReserva(bool value) {
    _$itensAtualizadosParaReservaAtom
        .reportWrite(value, super.itensAtualizadosParaReserva, () {
      super.itensAtualizadosParaReserva = value;
    });
  }

  late final _$agruparAulasPorHorarioAtom = Atom(
      name: '_ControladorAulaTurmaBase.agruparAulasPorHorario',
      context: context);

  @override
  bool get agruparAulasPorHorario {
    _$agruparAulasPorHorarioAtom.reportRead();
    return super.agruparAulasPorHorario;
  }

  @override
  set agruparAulasPorHorario(bool value) {
    _$agruparAulasPorHorarioAtom
        .reportWrite(value, super.agruparAulasPorHorario, () {
      super.agruparAulasPorHorario = value;
    });
  }

  late final _$gravarVisualizacaoAgrupamentoAsyncAction = AsyncAction(
      '_ControladorAulaTurmaBase.gravarVisualizacaoAgrupamento',
      context: context);

  @override
  Future<void> gravarVisualizacaoAgrupamento(bool habilitado) {
    return _$gravarVisualizacaoAgrupamentoAsyncAction
        .run(() => super.gravarVisualizacaoAgrupamento(habilitado));
  }

  late final _$consultarVisualizacaoAgrupamentoHorarioAsyncAction = AsyncAction(
      '_ControladorAulaTurmaBase.consultarVisualizacaoAgrupamentoHorario',
      context: context);

  @override
  Future consultarVisualizacaoAgrupamentoHorario() {
    return _$consultarVisualizacaoAgrupamentoHorarioAsyncAction
        .run(() => super.consultarVisualizacaoAgrupamentoHorario());
  }

  late final _$_ControladorAulaTurmaBaseActionController =
      ActionController(name: '_ControladorAulaTurmaBase', context: context);

  @override
  bool aparelhoEstaReservado(String equipamento) {
    final _$actionInfo = _$_ControladorAulaTurmaBaseActionController
        .startAction(name: '_ControladorAulaTurmaBase.aparelhoEstaReservado');
    try {
      return super.aparelhoEstaReservado(equipamento);
    } finally {
      _$_ControladorAulaTurmaBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  bool alunoTemEquipamentoReservado(String nome) {
    final _$actionInfo =
        _$_ControladorAulaTurmaBaseActionController.startAction(
            name: '_ControladorAulaTurmaBase.alunoTemEquipamentoReservado');
    try {
      return super.alunoTemEquipamentoReservado(nome);
    } finally {
      _$_ControladorAulaTurmaBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  String getEquipamentoReservado(String nome) {
    final _$actionInfo = _$_ControladorAulaTurmaBaseActionController
        .startAction(name: '_ControladorAulaTurmaBase.getEquipamentoReservado');
    try {
      return super.getEquipamentoReservado(nome);
    } finally {
      _$_ControladorAulaTurmaBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
consultaAulaTurmas: ${consultaAulaTurmas},
estaPesquisando: ${estaPesquisando},
aulaAgendada: ${aulaAgendada},
aulaDesmarcada: ${aulaDesmarcada},
pesquisaDeAulasTurmas: ${pesquisaDeAulasTurmas},
statusConsultarHistoricoAulas: ${statusConsultarHistoricoAulas},
consultarAlunosTurmaAula: ${consultarAlunosTurmaAula},
consultarAlunosNaAulaHistorico: ${consultarAlunosNaAulaHistorico},
consultarSaldoAluno: ${consultarSaldoAluno},
mProgramaAluno: ${mProgramaAluno},
alunoEmFerias: ${alunoEmFerias},
alunoTrancado: ${alunoTrancado},
contratoMarcacaoAula: ${contratoMarcacaoAula},
termoBuscaUnidade: ${termoBuscaUnidade},
empresaSelecionada: ${empresaSelecionada},
mListaSomenteAlunosAula: ${mListaSomenteAlunosAula},
mListaSomenteAlunosAulaBKP: ${mListaSomenteAlunosAulaBKP},
saldoParaAulasTurmasReposicao: ${saldoParaAulasTurmasReposicao},
saldoParaAulasTurmasCredito: ${saldoParaAulasTurmasCredito},
todasAsAulas: ${todasAsAulas},
todasAsAulasBackup: ${todasAsAulasBackup},
saldoAulaColetiva: ${saldoAulaColetiva},
mAulaTurmaSimplificado: ${mAulaTurmaSimplificado},
retornoErro: ${retornoErro},
mListaAlunosFilaDeEspera: ${mListaAlunosFilaDeEspera},
aulaSelecionadaDetalhes: ${aulaSelecionadaDetalhes},
consultarAlunosNaFila: ${consultarAlunosNaFila},
mListaEquipamentosReservados: ${mListaEquipamentosReservados},
mListaEquipamentosOcupados: ${mListaEquipamentosOcupados},
itensAtualizadosParaReserva: ${itensAtualizadosParaReserva},
agruparAulasPorHorario: ${agruparAulasPorHorario}
    ''';
  }
}
