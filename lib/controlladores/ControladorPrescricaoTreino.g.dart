// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorPrescricaoTreino.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RetornoFichas _$RetornoFichasFromJson(Map json) => RetornoFichas(
      content: (json['content'] as List<dynamic>?)
          ?.map((e) => Fichas.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      totalElements: (json['totalElements'] as num?)?.toInt(),
      totalPages: (json['totalPages'] as num?)?.toInt(),
      first: json['first'] as bool?,
      last: json['last'] as bool?,
      size: (json['size'] as num?)?.toInt(),
      number: (json['number'] as num?)?.toInt(),
    );

Map<String, dynamic> _$RetornoFichasToJson(RetornoFichas instance) =>
    <String, dynamic>{
      'content': instance.content?.map((e) => e.toJson()).toList(),
      'totalElements': instance.totalElements,
      'totalPages': instance.totalPages,
      'first': instance.first,
      'last': instance.last,
      'size': instance.size,
      'number': instance.number,
    };

Fichas _$FichasFromJson(Map json) => Fichas(
      id: (json['id'] as num?)?.toInt(),
      idPrograma: (json['idPrograma'] as num?)?.toInt(),
      versao: (json['versao'] as num?)?.toInt(),
      nome: json['nome'] as String?,
      categoria: json['categoria'] == null
          ? null
          : CategoriaFicha.fromJson(
              Map<String, dynamic>.from(json['categoria'] as Map)),
      tipo_execucao: json['tipo_execucao'] as String?,
      mensagem: json['mensagem'] as String?,
      atividades: (json['atividades'] as List<dynamic>?)
          ?.map((e) => Atividades.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      ativo: json['ativo'] as bool?,
      predefinida: json['predefinida'] as bool?,
      estaNoPrograma: json['estaNoPrograma'] as bool?,
      dias_semana: (json['dias_semana'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      aparelhos: (json['aparelhos'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      dataInicio: json['dataInicio'] as String?,
      dataTermino: json['dataTermino'] as String?,
    );

Map<String, dynamic> _$FichasToJson(Fichas instance) => <String, dynamic>{
      'id': instance.id,
      'idPrograma': instance.idPrograma,
      'versao': instance.versao,
      'nome': instance.nome,
      'categoria': instance.categoria?.toJson(),
      'tipo_execucao': instance.tipo_execucao,
      'mensagem': instance.mensagem,
      'atividades': instance.atividades?.map((e) => e.toJson()).toList(),
      'ativo': instance.ativo,
      'predefinida': instance.predefinida,
      'estaNoPrograma': instance.estaNoPrograma,
      'dias_semana': instance.dias_semana,
      'aparelhos': instance.aparelhos,
      'dataInicio': instance.dataInicio,
      'dataTermino': instance.dataTermino,
    };

CategoriaFicha _$CategoriaFichaFromJson(Map json) => CategoriaFicha(
      id: json['id'] as num?,
      nome: json['nome'] as String?,
    );

Map<String, dynamic> _$CategoriaFichaToJson(CategoriaFicha instance) =>
    <String, dynamic>{
      'id': instance.id,
      'nome': instance.nome,
    };

Atividades _$AtividadesFromJson(Map json) => Atividades(
      id: (json['id'] as num?)?.toInt(),
      sequencia: (json['sequencia'] as num?)?.toInt(),
      atividade: json['atividade'] == null
          ? null
          : Atividade.fromJson(
              Map<String, dynamic>.from(json['atividade'] as Map)),
      metodoExecucao: json['metodoExecucao'] as String?,
      series: (json['series'] as List<dynamic>?)
          ?.map((e) => Series.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      fichaId: (json['fichaId'] as num?)?.toInt(),
      setAtividades: (json['setAtividades'] as List<dynamic>?)
          ?.map((e) =>
              SetAtividades.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      nomeNaFicha: json['nomeNaFicha'] as String?,
      complementoNomeAtividade: json['complementoNomeAtividade'] as String?,
      esforco: (json['esforco'] as num?)?.toInt(),
      atividadeId: (json['atividadeId'] as num?)?.toInt(),
      selecionada: json['selecionada'] as bool?,
      atividadesSequenciaSet: (json['atividadesSequenciaSet'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      nomesAtividadesAgrupadas:
          (json['nomesAtividadesAgrupadas'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      aparelhos: (json['aparelhos'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$AtividadesToJson(Atividades instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sequencia': instance.sequencia,
      'atividade': instance.atividade?.toJson(),
      'metodoExecucao': instance.metodoExecucao,
      'series': instance.series?.map((e) => e.toJson()).toList(),
      'fichaId': instance.fichaId,
      'setAtividades': instance.setAtividades?.map((e) => e.toJson()).toList(),
      'nomeNaFicha': instance.nomeNaFicha,
      'complementoNomeAtividade': instance.complementoNomeAtividade,
      'esforco': instance.esforco,
      'atividadeId': instance.atividadeId,
      'selecionada': instance.selecionada,
      'atividadesSequenciaSet': instance.atividadesSequenciaSet,
      'nomesAtividadesAgrupadas': instance.nomesAtividadesAgrupadas,
      'aparelhos': instance.aparelhos,
    };

Atividade _$AtividadeFromJson(Map json) => Atividade(
      id: (json['id'] as num?)?.toInt(),
      nome: json['nome'] as String?,
      ativa: json['ativa'] as bool?,
      tipo: json['tipo'] as String?,
      descricao: json['descricao'] as String?,
      videoUri: json['videoUri'] as String?,
      images: (json['images'] as List<dynamic>?)
          ?.map((e) => Images.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      empresas: (json['empresas'] as List<dynamic>?)
          ?.map((e) => Empresas.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      serieApenasDuracao: json['serieApenasDuracao'] as bool?,
      urlLinkVideos: (json['urlLinkVideos'] as List<dynamic>?)
          ?.map((e) =>
              UrlLinkVideos.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$AtividadeToJson(Atividade instance) => <String, dynamic>{
      'id': instance.id,
      'nome': instance.nome,
      'ativa': instance.ativa,
      'tipo': instance.tipo,
      'descricao': instance.descricao,
      'videoUri': instance.videoUri,
      'images': instance.images?.map((e) => e.toJson()).toList(),
      'empresas': instance.empresas?.map((e) => e.toJson()).toList(),
      'serieApenasDuracao': instance.serieApenasDuracao,
      'urlLinkVideos': instance.urlLinkVideos?.map((e) => e.toJson()).toList(),
    };

Images _$ImagesFromJson(Map json) => Images(
      type: json['type'] as String?,
      id: (json['id'] as num?)?.toInt(),
      uri: json['uri'] as String?,
      nome: json['nome'] as String?,
      fotoKeyPequena: json['fotoKeyPequena'] as String?,
      fotoKeyMiniatura: json['fotoKeyMiniatura'] as String?,
      professor: json['professor'] as bool?,
    );

Map<String, dynamic> _$ImagesToJson(Images instance) => <String, dynamic>{
      'type': instance.type,
      'id': instance.id,
      'uri': instance.uri,
      'nome': instance.nome,
      'fotoKeyPequena': instance.fotoKeyPequena,
      'fotoKeyMiniatura': instance.fotoKeyMiniatura,
      'professor': instance.professor,
    };

UrlLinkVideos _$UrlLinkVideosFromJson(Map json) => UrlLinkVideos(
      id: (json['id'] as num?)?.toInt(),
      linkVideo: json['linkVideo'] as String?,
      professor: json['professor'] as bool?,
    );

Map<String, dynamic> _$UrlLinkVideosToJson(UrlLinkVideos instance) =>
    <String, dynamic>{
      'id': instance.id,
      'linkVideo': instance.linkVideo,
      'professor': instance.professor,
    };

Empresas _$EmpresasFromJson(Map json) => Empresas(
      id: (json['id'] as num?)?.toInt(),
      identificador: json['identificador'] as String?,
      empresa: json['empresa'] == null
          ? null
          : CategoriaFicha.fromJson(
              Map<String, dynamic>.from(json['empresa'] as Map)),
    );

Map<String, dynamic> _$EmpresasToJson(Empresas instance) => <String, dynamic>{
      'id': instance.id,
      'identificador': instance.identificador,
      'empresa': instance.empresa?.toJson(),
    };

Series _$SeriesFromJson(Map json) => Series(
      id: (json['id'] as num?)?.toInt(),
      atividadeFichaId: (json['atividadeFichaId'] as num?)?.toInt(),
      sequencia: (json['sequencia'] as num?)?.toInt(),
      repeticoes: json['repeticoes'] as String?,
      carga: json['carga'] as String?,
      cadencia: json['cadencia'] as String?,
      descanso: (json['descanso'] as num?)?.toInt(),
      complemento: json['complemento'] as String?,
      velocidade: json['velocidade'] as num?,
      duracao: (json['duracao'] as num?)?.toInt(),
      distancia: (json['distancia'] as num?)?.toInt(),
      cargaApp: json['cargaApp'] as String?,
      repeticaoApp: json['repeticaoApp'] as String?,
    );

Map<String, dynamic> _$SeriesToJson(Series instance) => <String, dynamic>{
      'id': instance.id,
      'atividadeFichaId': instance.atividadeFichaId,
      'sequencia': instance.sequencia,
      'repeticoes': instance.repeticoes,
      'carga': instance.carga,
      'cadencia': instance.cadencia,
      'descanso': instance.descanso,
      'complemento': instance.complemento,
      'velocidade': instance.velocidade,
      'duracao': instance.duracao,
      'distancia': instance.distancia,
      'cargaApp': instance.cargaApp,
      'repeticaoApp': instance.repeticaoApp,
    };

SetAtividades _$SetAtividadesFromJson(Map json) => SetAtividades(
      id: (json['id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SetAtividadesToJson(SetAtividades instance) =>
    <String, dynamic>{
      'id': instance.id,
    };

ProgramaTreino _$ProgramaTreinoFromJson(Map json) => ProgramaTreino(
      id: (json['id'] as num?)?.toInt(),
      nome: json['nome'] as String?,
      dataLancamento: (json['dataLancamento'] as num?)?.toInt(),
      totalTreinos: (json['totalTreinos'] as num?)?.toInt(),
      qtdDiasSemana: (json['qtdDiasSemana'] as num?)?.toInt(),
      fichas: (json['fichas'] as List<dynamic>?)
          ?.map((e) => Fichas.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      professorMontou: json['professorMontou'] == null
          ? null
          : ProfessorMontou.fromJson(
              Map<String, dynamic>.from(json['professorMontou'] as Map)),
      genero: json['genero'] as String?,
      situacao: json['situacao'] as String?,
      predefinido: json['predefinido'] as bool?,
      professorId: (json['professorId'] as num?)?.toInt(),
      estaNoPrograma: json['estaNoPrograma'] as bool?,
      inicio: json['inicio'] as num?,
      termino: json['termino'] as num?,
      alunoId: json['alunoId'] as num?,
    );

Map<String, dynamic> _$ProgramaTreinoToJson(ProgramaTreino instance) =>
    <String, dynamic>{
      'id': instance.id,
      'nome': instance.nome,
      'dataLancamento': instance.dataLancamento,
      'totalTreinos': instance.totalTreinos,
      'qtdDiasSemana': instance.qtdDiasSemana,
      'fichas': instance.fichas?.map((e) => e.toJson()).toList(),
      'professorMontou': instance.professorMontou?.toJson(),
      'genero': instance.genero,
      'situacao': instance.situacao,
      'predefinido': instance.predefinido,
      'professorId': instance.professorId,
      'estaNoPrograma': instance.estaNoPrograma,
      'inicio': instance.inicio,
      'termino': instance.termino,
      'alunoId': instance.alunoId,
    };

ProfessorMontou _$ProfessorMontouFromJson(Map json) => ProfessorMontou(
      id: (json['id'] as num?)?.toInt(),
      codigoColaborador: (json['codigoColaborador'] as num?)?.toInt(),
      nome: json['nome'] as String?,
      imageUri: json['imageUri'] as String?,
    );

Map<String, dynamic> _$ProfessorMontouToJson(ProfessorMontou instance) =>
    <String, dynamic>{
      'id': instance.id,
      'codigoColaborador': instance.codigoColaborador,
      'nome': instance.nome,
      'imageUri': instance.imageUri,
    };

AtividadeFicha _$AtividadeFichaFromJson(Map json) => AtividadeFicha(
      id: (json['id'] as num?)?.toInt(),
      nome: json['nome'] as String?,
      estaNoTreino: json['estaNoTreino'] as bool?,
      image: json['image'] as String?,
      tipo: json['tipo'] as String?,
    );

Map<String, dynamic> _$AtividadeFichaToJson(AtividadeFicha instance) =>
    <String, dynamic>{
      'id': instance.id,
      'nome': instance.nome,
      'estaNoTreino': instance.estaNoTreino,
      'image': instance.image,
      'tipo': instance.tipo,
    };

FiltrosAtividade _$FiltrosAtividadeFromJson(Map json) => FiltrosAtividade(
      situacaoAtividade: (json['situacaoAtividade'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      quicksearchValue: json['quicksearchValue'] as String?,
      quicksearchFields: (json['quicksearchFields'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      grupoMuscularesIds: (json['grupoMuscularesIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$FiltrosAtividadeToJson(FiltrosAtividade instance) =>
    <String, dynamic>{
      'situacaoAtividade': instance.situacaoAtividade,
      'quicksearchValue': instance.quicksearchValue,
      'quicksearchFields': instance.quicksearchFields,
      'grupoMuscularesIds': instance.grupoMuscularesIds,
    };

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorPrescricaoTreino on _ControladorPrescricaoTreinoBase, Store {
  late final _$listaProgramasTreinoAtom = Atom(
      name: '_ControladorPrescricaoTreinoBase.listaProgramasTreino',
      context: context);

  @override
  ObservableList<ProgramaTreino> get listaProgramasTreino {
    _$listaProgramasTreinoAtom.reportRead();
    return super.listaProgramasTreino;
  }

  @override
  set listaProgramasTreino(ObservableList<ProgramaTreino> value) {
    _$listaProgramasTreinoAtom.reportWrite(value, super.listaProgramasTreino,
        () {
      super.listaProgramasTreino = value;
    });
  }

  late final _$listaFichasAtom = Atom(
      name: '_ControladorPrescricaoTreinoBase.listaFichas', context: context);

  @override
  ObservableList<Fichas> get listaFichas {
    _$listaFichasAtom.reportRead();
    return super.listaFichas;
  }

  @override
  set listaFichas(ObservableList<Fichas> value) {
    _$listaFichasAtom.reportWrite(value, super.listaFichas, () {
      super.listaFichas = value;
    });
  }

  late final _$listaAtividadesAtom = Atom(
      name: '_ControladorPrescricaoTreinoBase.listaAtividades',
      context: context);

  @override
  ObservableList<AtividadeFicha> get listaAtividades {
    _$listaAtividadesAtom.reportRead();
    return super.listaAtividades;
  }

  @override
  set listaAtividades(ObservableList<AtividadeFicha> value) {
    _$listaAtividadesAtom.reportWrite(value, super.listaAtividades, () {
      super.listaAtividades = value;
    });
  }

  late final _$listaAtividadesAdicionadasAtom = Atom(
      name: '_ControladorPrescricaoTreinoBase.listaAtividadesAdicionadas',
      context: context);

  @override
  ObservableList<AtividadeFicha> get listaAtividadesAdicionadas {
    _$listaAtividadesAdicionadasAtom.reportRead();
    return super.listaAtividadesAdicionadas;
  }

  @override
  set listaAtividadesAdicionadas(ObservableList<AtividadeFicha> value) {
    _$listaAtividadesAdicionadasAtom
        .reportWrite(value, super.listaAtividadesAdicionadas, () {
      super.listaAtividadesAdicionadas = value;
    });
  }

  late final _$listaFichasAdicionadasNoProgramaAtom = Atom(
      name: '_ControladorPrescricaoTreinoBase.listaFichasAdicionadasNoPrograma',
      context: context);

  @override
  ObservableList<Fichas> get listaFichasAdicionadasNoPrograma {
    _$listaFichasAdicionadasNoProgramaAtom.reportRead();
    return super.listaFichasAdicionadasNoPrograma;
  }

  @override
  set listaFichasAdicionadasNoPrograma(ObservableList<Fichas> value) {
    _$listaFichasAdicionadasNoProgramaAtom
        .reportWrite(value, super.listaFichasAdicionadasNoPrograma, () {
      super.listaFichasAdicionadasNoPrograma = value;
    });
  }

  late final _$statusConsultarAtividadesAtom = Atom(
      name: '_ControladorPrescricaoTreinoBase.statusConsultarAtividades',
      context: context);

  @override
  ServiceStatus get statusConsultarAtividades {
    _$statusConsultarAtividadesAtom.reportRead();
    return super.statusConsultarAtividades;
  }

  @override
  set statusConsultarAtividades(ServiceStatus value) {
    _$statusConsultarAtividadesAtom
        .reportWrite(value, super.statusConsultarAtividades, () {
      super.statusConsultarAtividades = value;
    });
  }

  late final _$statusConsultarProgramasPredefinidosAtom = Atom(
      name:
          '_ControladorPrescricaoTreinoBase.statusConsultarProgramasPredefinidos',
      context: context);

  @override
  ServiceStatus get statusConsultarProgramasPredefinidos {
    _$statusConsultarProgramasPredefinidosAtom.reportRead();
    return super.statusConsultarProgramasPredefinidos;
  }

  @override
  set statusConsultarProgramasPredefinidos(ServiceStatus value) {
    _$statusConsultarProgramasPredefinidosAtom
        .reportWrite(value, super.statusConsultarProgramasPredefinidos, () {
      super.statusConsultarProgramasPredefinidos = value;
    });
  }

  late final _$statusConsultarFichasPredefinidasAtom = Atom(
      name:
          '_ControladorPrescricaoTreinoBase.statusConsultarFichasPredefinidas',
      context: context);

  @override
  ServiceStatus get statusConsultarFichasPredefinidas {
    _$statusConsultarFichasPredefinidasAtom.reportRead();
    return super.statusConsultarFichasPredefinidas;
  }

  @override
  set statusConsultarFichasPredefinidas(ServiceStatus value) {
    _$statusConsultarFichasPredefinidasAtom
        .reportWrite(value, super.statusConsultarFichasPredefinidas, () {
      super.statusConsultarFichasPredefinidas = value;
    });
  }

  late final _$seriesAtom =
      Atom(name: '_ControladorPrescricaoTreinoBase.series', context: context);

  @override
  num get series {
    _$seriesAtom.reportRead();
    return super.series;
  }

  @override
  set series(num value) {
    _$seriesAtom.reportWrite(value, super.series, () {
      super.series = value;
    });
  }

  late final _$repeticoesAtom = Atom(
      name: '_ControladorPrescricaoTreinoBase.repeticoes', context: context);

  @override
  String get repeticoes {
    _$repeticoesAtom.reportRead();
    return super.repeticoes;
  }

  @override
  set repeticoes(String value) {
    _$repeticoesAtom.reportWrite(value, super.repeticoes, () {
      super.repeticoes = value;
    });
  }

  late final _$pesoAtom =
      Atom(name: '_ControladorPrescricaoTreinoBase.peso', context: context);

  @override
  String get peso {
    _$pesoAtom.reportRead();
    return super.peso;
  }

  @override
  set peso(String value) {
    _$pesoAtom.reportWrite(value, super.peso, () {
      super.peso = value;
    });
  }

  late final _$cadenciaAtom =
      Atom(name: '_ControladorPrescricaoTreinoBase.cadencia', context: context);

  @override
  String get cadencia {
    _$cadenciaAtom.reportRead();
    return super.cadencia;
  }

  @override
  set cadencia(String value) {
    _$cadenciaAtom.reportWrite(value, super.cadencia, () {
      super.cadencia = value;
    });
  }

  late final _$velocidadeAtom = Atom(
      name: '_ControladorPrescricaoTreinoBase.velocidade', context: context);

  @override
  num? get velocidade {
    _$velocidadeAtom.reportRead();
    return super.velocidade;
  }

  @override
  set velocidade(num? value) {
    _$velocidadeAtom.reportWrite(value, super.velocidade, () {
      super.velocidade = value;
    });
  }

  late final _$duracaoAtom =
      Atom(name: '_ControladorPrescricaoTreinoBase.duracao', context: context);

  @override
  int? get duracao {
    _$duracaoAtom.reportRead();
    return super.duracao;
  }

  @override
  set duracao(int? value) {
    _$duracaoAtom.reportWrite(value, super.duracao, () {
      super.duracao = value;
    });
  }

  late final _$distanciaAtom = Atom(
      name: '_ControladorPrescricaoTreinoBase.distancia', context: context);

  @override
  int? get distancia {
    _$distanciaAtom.reportRead();
    return super.distancia;
  }

  @override
  set distancia(int? value) {
    _$distanciaAtom.reportWrite(value, super.distancia, () {
      super.distancia = value;
    });
  }

  late final _$descansoAtom =
      Atom(name: '_ControladorPrescricaoTreinoBase.descanso', context: context);

  @override
  int get descanso {
    _$descansoAtom.reportRead();
    return super.descanso;
  }

  @override
  set descanso(int value) {
    _$descansoAtom.reportWrite(value, super.descanso, () {
      super.descanso = value;
    });
  }

  late final _$houveModificacaoAtom = Atom(
      name: '_ControladorPrescricaoTreinoBase.houveModificacao',
      context: context);

  @override
  bool get houveModificacao {
    _$houveModificacaoAtom.reportRead();
    return super.houveModificacao;
  }

  @override
  set houveModificacao(bool value) {
    _$houveModificacaoAtom.reportWrite(value, super.houveModificacao, () {
      super.houveModificacao = value;
    });
  }

  late final _$estaCriandoProgramaTreinoAtom = Atom(
      name: '_ControladorPrescricaoTreinoBase.estaCriandoProgramaTreino',
      context: context);

  @override
  bool get estaCriandoProgramaTreino {
    _$estaCriandoProgramaTreinoAtom.reportRead();
    return super.estaCriandoProgramaTreino;
  }

  @override
  set estaCriandoProgramaTreino(bool value) {
    _$estaCriandoProgramaTreinoAtom
        .reportWrite(value, super.estaCriandoProgramaTreino, () {
      super.estaCriandoProgramaTreino = value;
    });
  }

  late final _$fluxoPredefinidoAtom = Atom(
      name: '_ControladorPrescricaoTreinoBase.fluxoPredefinido',
      context: context);

  @override
  bool get fluxoPredefinido {
    _$fluxoPredefinidoAtom.reportRead();
    return super.fluxoPredefinido;
  }

  @override
  set fluxoPredefinido(bool value) {
    _$fluxoPredefinidoAtom.reportWrite(value, super.fluxoPredefinido, () {
      super.fluxoPredefinido = value;
    });
  }

  late final _$consultarProgramaTreinoPredefinidasAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.consultarProgramaTreinoPredefinidas',
      context: context);

  @override
  Future consultarProgramaTreinoPredefinidas() {
    return _$consultarProgramaTreinoPredefinidasAsyncAction
        .run(() => super.consultarProgramaTreinoPredefinidas());
  }

  late final _$consultarProgramaTreinoPredefinidasPorNomeAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.consultarProgramaTreinoPredefinidasPorNome',
      context: context);

  @override
  Future consultarProgramaTreinoPredefinidasPorNome({String? nome}) {
    return _$consultarProgramaTreinoPredefinidasPorNomeAsyncAction.run(
        () => super.consultarProgramaTreinoPredefinidasPorNome(nome: nome));
  }

  late final _$consultarFichasPredefinidasAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.consultarFichasPredefinidas',
      context: context);

  @override
  Future consultarFichasPredefinidas() {
    return _$consultarFichasPredefinidasAsyncAction
        .run(() => super.consultarFichasPredefinidas());
  }

  late final _$consultarFichasPredefinidasPorNomeAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.consultarFichasPredefinidasPorNome',
      context: context);

  @override
  Future consultarFichasPredefinidasPorNome({String? nome}) {
    return _$consultarFichasPredefinidasPorNomeAsyncAction
        .run(() => super.consultarFichasPredefinidasPorNome(nome: nome));
  }

  late final _$consultarAtividadesFichaAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.consultarAtividadesFicha',
      context: context);

  @override
  Future consultarAtividadesFicha({String? nome}) {
    return _$consultarAtividadesFichaAsyncAction
        .run(() => super.consultarAtividadesFicha(nome: nome));
  }

  late final _$gravarFichaAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.gravarFicha',
      context: context);

  @override
  Future gravarFicha(
      {required dynamic Function(Fichas) sucesso,
      required dynamic Function() falha,
      required dynamic Function() carregando,
      required Fichas ficha,
      ProgramaTreino? dadosPrograma}) {
    return _$gravarFichaAsyncAction.run(() => super.gravarFicha(
        sucesso: sucesso,
        falha: falha,
        carregando: carregando,
        ficha: ficha,
        dadosPrograma: dadosPrograma));
  }

  late final _$gravarProgramaPredefinidoAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.gravarProgramaPredefinido',
      context: context);

  @override
  Future gravarProgramaPredefinido(
      {required ProgramaTreino programa,
      int? alunoId,
      required dynamic Function(ProgramaTreino) sucesso,
      required dynamic Function() falha,
      required dynamic Function() carregando}) {
    return _$gravarProgramaPredefinidoAsyncAction.run(() => super
        .gravarProgramaPredefinido(
            programa: programa,
            alunoId: alunoId,
            sucesso: sucesso,
            falha: falha,
            carregando: carregando));
  }

  late final _$tornarProgramaEmPredefinidoAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.tornarProgramaEmPredefinido',
      context: context);

  @override
  Future tornarProgramaEmPredefinido(
      {required dynamic Function(ProgramaTreino) sucesso,
      required dynamic Function() falha,
      required dynamic Function() carregando,
      required ProgramaTreino programa}) {
    return _$tornarProgramaEmPredefinidoAsyncAction.run(() => super
        .tornarProgramaEmPredefinido(
            sucesso: sucesso,
            falha: falha,
            carregando: carregando,
            programa: programa));
  }

  late final _$tornarFichaPredefinidaAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.tornarFichaPredefinida',
      context: context);

  @override
  Future tornarFichaPredefinida(
      {required dynamic Function() sucesso,
      required dynamic Function() falha,
      required dynamic Function() carregando,
      required dynamic idFicha}) {
    return _$tornarFichaPredefinidaAsyncAction.run(() => super
        .tornarFichaPredefinida(
            sucesso: sucesso,
            falha: falha,
            carregando: carregando,
            idFicha: idFicha));
  }

  late final _$incluirFichaEmBrancoNoProgramaTreinoAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.incluirFichaEmBrancoNoProgramaTreino',
      context: context);

  @override
  Future incluirFichaEmBrancoNoProgramaTreino(
      {required dynamic Function(Fichas) sucesso,
      required dynamic Function() falha,
      required dynamic Function() carregando,
      required ProgramaTreino dadosPrograma}) {
    return _$incluirFichaEmBrancoNoProgramaTreinoAsyncAction.run(() => super
        .incluirFichaEmBrancoNoProgramaTreino(
            sucesso: sucesso,
            falha: falha,
            carregando: carregando,
            dadosPrograma: dadosPrograma));
  }

  late final _$incluirFichaNoProgramaTreinoAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.incluirFichaNoProgramaTreino',
      context: context);

  @override
  Future incluirFichaNoProgramaTreino(
      {required dynamic Function(Fichas) sucesso,
      required dynamic Function() falha,
      required dynamic Function() carregando,
      required Fichas ficha,
      required ProgramaTreino dadosPrograma}) {
    return _$incluirFichaNoProgramaTreinoAsyncAction.run(() => super
        .incluirFichaNoProgramaTreino(
            sucesso: sucesso,
            falha: falha,
            carregando: carregando,
            ficha: ficha,
            dadosPrograma: dadosPrograma));
  }

  late final _$incluirProgramaTreinoNoAlunoAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.incluirProgramaTreinoNoAluno',
      context: context);

  @override
  Future incluirProgramaTreinoNoAluno(
      {required dynamic Function(ProgramaTreino) sucesso,
      required dynamic Function() falha,
      required dynamic Function() carregando,
      required int idPrograma,
      required int idAluno,
      required num empresaId}) {
    return _$incluirProgramaTreinoNoAlunoAsyncAction.run(() => super
        .incluirProgramaTreinoNoAluno(
            sucesso: sucesso,
            falha: falha,
            carregando: carregando,
            idPrograma: idPrograma,
            idAluno: idAluno,
            empresaId: empresaId));
  }

  late final _$incluirFichaNoAlunoAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.incluirFichaNoAluno',
      context: context);

  @override
  Future incluirFichaNoAluno(
      {required dynamic Function() sucesso,
      required dynamic Function() falha,
      required dynamic Function() semPrograma,
      required dynamic Function() carregando,
      required int idAluno,
      required Fichas ficha}) {
    return _$incluirFichaNoAlunoAsyncAction.run(() => super.incluirFichaNoAluno(
        sucesso: sucesso,
        falha: falha,
        semPrograma: semPrograma,
        carregando: carregando,
        idAluno: idAluno,
        ficha: ficha));
  }

  late final _$deletarProgramTreinoAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.deletarProgramTreino',
      context: context);

  @override
  Future deletarProgramTreino(
      {required dynamic Function() sucesso,
      required dynamic Function() falha,
      required dynamic Function() carregando,
      required int idPrograma}) {
    return _$deletarProgramTreinoAsyncAction.run(() => super
        .deletarProgramTreino(
            sucesso: sucesso,
            falha: falha,
            carregando: carregando,
            idPrograma: idPrograma));
  }

  late final _$deletarFichaAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.deletarFicha',
      context: context);

  @override
  Future deletarFicha(
      {required dynamic Function() sucesso,
      required dynamic Function() falha,
      required dynamic Function() carregando,
      required int idFicha}) {
    return _$deletarFichaAsyncAction.run(() => super.deletarFicha(
        sucesso: sucesso,
        falha: falha,
        carregando: carregando,
        idFicha: idFicha));
  }

  late final _$consultarAlunoDetalhadoAsyncAction = AsyncAction(
      '_ControladorPrescricaoTreinoBase.consultarAlunoDetalhado',
      context: context);

  @override
  Future consultarAlunoDetalhado(
      {required dynamic Function(ProgramaTreino) sucesso,
      required dynamic Function() falha,
      required dynamic Function() semPrograma,
      required dynamic Function() carregando,
      required int idAluno}) {
    return _$consultarAlunoDetalhadoAsyncAction.run(() => super
        .consultarAlunoDetalhado(
            sucesso: sucesso,
            falha: falha,
            semPrograma: semPrograma,
            carregando: carregando,
            idAluno: idAluno));
  }

  @override
  String toString() {
    return '''
listaProgramasTreino: ${listaProgramasTreino},
listaFichas: ${listaFichas},
listaAtividades: ${listaAtividades},
listaAtividadesAdicionadas: ${listaAtividadesAdicionadas},
listaFichasAdicionadasNoPrograma: ${listaFichasAdicionadasNoPrograma},
statusConsultarAtividades: ${statusConsultarAtividades},
statusConsultarProgramasPredefinidos: ${statusConsultarProgramasPredefinidos},
statusConsultarFichasPredefinidas: ${statusConsultarFichasPredefinidas},
series: ${series},
repeticoes: ${repeticoes},
peso: ${peso},
cadencia: ${cadencia},
velocidade: ${velocidade},
duracao: ${duracao},
distancia: ${distancia},
descanso: ${descanso},
houveModificacao: ${houveModificacao},
estaCriandoProgramaTreino: ${estaCriandoProgramaTreino},
fluxoPredefinido: ${fluxoPredefinido}
    ''';
  }
}
