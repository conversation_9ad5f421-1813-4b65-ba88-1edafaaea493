import 'dart:convert';
import 'dart:typed_data';

import 'package:app_treino/ServiceProvider/WodService.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorWod.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:app_treino/model/wod/WorkoutOfDay.dart';
import 'package:diacritic/diacritic.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorManterWod.g.dart';

class ControladorManterWod = _ControladorManterWodBase with _$ControladorManterWod;

abstract class _ControladorManterWodBase with Store {
  final _wodService = GetIt.I.get<WodService>();
  final _mControladorUsuaro = GetIt.I.get<ControladorCliente>();

  Uint8List? mImagemUpload;

  @observable
  ServiceStatus mStatusConsulta = ServiceStatus.Waiting;
  @observable
  ServiceStatus mStatusConsultaAparelhos = ServiceStatus.Waiting;
  @observable
  ServiceStatus mStatusConsultaAtividades = ServiceStatus.Waiting;

  @observable
  ObservableList<AparelhoWod> mListaAparelhosWod = ObservableList<AparelhoWod>();

  @observable
  ObservableList<AtividadeWod> mListaAtividadesWod = ObservableList<AtividadeWod>();

  @observable
  ObservableList<TipoWodTabela> mListaTiposWod = ObservableList<TipoWodTabela>();

  @observable
  ObservableList<WorkoutOfDay> mListaWods = ObservableList<WorkoutOfDay>();

  List<WorkoutOfDay> get wodsDeHoje {
    return mListaWods.where((element) => element.dia == UtilDataHora.getDiaMesAno(dateTime: DateTime.now())).toList();
  }

  List<WorkoutOfDay> mListaDeWodsDeHoje = [];

  List<AparelhoWod> mListaAparelhosWodOriginal = [];
  List<AtividadeWod> mListaAtividadesWodOriginal = [];
  List<WorkoutOfDay> mListaWodsOriginal = [];

  void excluirWoD(WorkoutOfDay wod, {Function()? carregando, Function()? sucesso, Function(String? mensagem)? falha}) {
    carregando?.call();
    _wodService.excluirWod({'codigo': wod.codigo, 'operacao': 'EXCLUIR'}.toString()).then((value) {
      mListaWodsOriginal.removeWhere((element) => element.codigo == wod.codigo);
      pesquisarWodPorNome('', (pesquisaAtiva) => null);
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  void salvarWoDonline(WorkoutOfDay wod, {Function()? carregando, Function()? sucesso, Function(String? mensagem)? falha}) {
    carregando?.call();
    if(wod.descricaoExercicios?.isEmpty ?? true){
      falha?.call(localizedString('observacao_wod_obrigatoria'));
    }else{
        WoDmanter? manter =  WoDmanter(wodParse: wod,empresa: _mControladorUsuaro.mUsuarioLogado!.codEmpresa!.toInt() ,imagem: mImagemUpload);
        String? json = const JsonCodec().encode(manter.toJson());
        _wodService.manterWodOnline(json).then((value) {
        
        if(manter!.operacao == 'ADICIONAR'){
            mListaWodsOriginal.insert(0, value);
            pesquisarWodPorNome('', (pesquisaAtiva) => null);
            manter = null;
            json = null;
            mImagemUpload = null;
        }
        sucesso?.call();
      }).catchError((onError) {
        falha?.call(onError.message);
      });
    }
  }

  void pesquisarAparelhoPorNome(String termo, Function(bool pesquisaAtiva) pesquisaAtiva) {
    mListaAparelhosWod.clear();
    mListaAparelhosWod.clear();
    if (termo.isNotEmpty) {
      pesquisaAtiva.call(true);
      var termoLimpo = removeDiacritics(termo);
      mListaAparelhosWod.addAll(mListaAparelhosWodOriginal.where((element) => removeDiacritics(element.nomeAparelho!.toLowerCase()).contains(termoLimpo.toLowerCase())));
    } else {
      pesquisaAtiva.call(false);
      mListaAparelhosWod.addAll(mListaAparelhosWodOriginal);
    }
  }

  void pesquisarAtividadePorNome(String termo, Function(bool pesquisaAtiva) pesquisaAtiva) {
    mListaAtividadesWod.clear();
    if (termo.isNotEmpty) {
      pesquisaAtiva.call(true);
      var termoLimpo = removeDiacritics(termo);
      mListaAtividadesWod.addAll(mListaAtividadesWodOriginal.where((element) => removeDiacritics(element.nomeAtividade!.toLowerCase()).contains(termoLimpo.toLowerCase())));
    } else {
      pesquisaAtiva.call(false);
      mListaAtividadesWod.addAll(mListaAtividadesWodOriginal);
    }
  }

  void pesquisarWodPorNome(String termo, Function(bool pesquisaAtiva) pesquisaAtiva) {
  mListaWods.clear();
  if (termo.isNotEmpty) {
    var termoLimpo = removeDiacritics(termo.trim().toLowerCase());
    mListaWods.addAll(mListaWodsOriginal.where((element) => 
      removeDiacritics((element.nome ?? '').trim().toLowerCase()).contains(termoLimpo)));
    pesquisaAtiva.call(true);
  } else {
    pesquisaAtiva.call(false);
    mListaWods.addAll(mListaWodsOriginal);
  }
}

  void consultaApenasAparelhosCrossFit({Function()? carregando, Function()? sucesso, Function(String? mensagem)? falha, required bool force}) {
    carregando?.call();
    mStatusConsultaAparelhos = ServiceStatus.Waiting;
    if (!force) {
      sucesso?.call();
      mStatusConsultaAparelhos = ServiceStatus.Done;
    } else {
      _wodService.obterAparelhosCrossfit().then((value) {
        mListaAparelhosWod.clear();
        mListaAparelhosWod.addAll(value);
        sucesso?.call();
        mStatusConsultaAparelhos = ServiceStatus.Done;
      }).catchError((onError) {
        falha?.call(onError.message);
        mStatusConsultaAparelhos = ServiceStatus.Error;
      });
    }
  }

  void consultaApenasAtividadesCrossFit({Function()? carregando, Function()? sucesso, Function(String? mensagem)? falha, bool force = false}) {
    carregando?.call();
    mStatusConsultaAtividades = ServiceStatus.Waiting;
    if (!force) {
      sucesso?.call();
      mStatusConsultaAtividades = ServiceStatus.Done;
    } else {
      _wodService.obterAtividadesCross().then((value) {
        mListaAtividadesWod.clear();
        mListaAtividadesWod.addAll(value);
        sucesso?.call();
        mStatusConsultaAtividades = ServiceStatus.Done;
      }).catchError((onError) {
        falha?.call(onError.message);
        mStatusConsultaAtividades = ServiceStatus.Error;
      });
    }
  }

  void carregarDadosNecessariosManterWods({Function()? carregando, Function()? sucesso, Function(String? mensagem)? falha}) {
    // Para manter wods é necessário carregar três informações
    // Os Wods, A lista de atividades e a Lista de aparelhos
    mStatusConsulta = ServiceStatus.Waiting;
    carregando?.call();

    Future.wait([
       GetIt.I.get<ControladorWod>().getServicoWod(_mControladorUsuaro.mUsuarioLogado!.codUsuario!, UtilDataHora.getDiaMesAno(dateTime: DateTime.now().subtract(const Duration(days: 60))),
          UtilDataHora.getDiaMesAno(dateTime: DateTime.now().add(const Duration(days: 60))),GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa ?? 0),
      _wodService.obterAtividadesCross(),
      _wodService.obterAparelhosCrossfit(),
      _wodService.obterTiposDeWod()
    ]).then((result) {
      mListaAparelhosWod.clear();
      mListaAtividadesWod.clear();
      mListaWods.clear();
      mListaTiposWod.clear();
      for (final element in result) {
        if (element is List<AparelhoWod>) {
          mListaAparelhosWod.addAll(element);
          mListaAparelhosWodOriginal = element;
        } else if (element is List<AtividadeWod>) {
          mListaAtividadesWod.addAll(element);
          mListaAtividadesWodOriginal = element;
        } else if (element is List<WorkoutOfDay>) {
          element.sort((b, a) => UtilDataHora.parseStringToDate(a.dia!)!.compareTo(UtilDataHora.parseStringToDate(b.dia!)!));
          mListaWodsOriginal = element;
          mListaWods.addAll(element);
        } else if (element is List<TipoWodTabela>) {
          mListaTiposWod.addAll(element);
        }
      }
      mStatusConsulta = ServiceStatus.Done;
      mStatusConsultaAparelhos = ServiceStatus.Done;
      mStatusConsultaAtividades = ServiceStatus.Done;
      sucesso?.call();
    }).catchError((onError) {
      mStatusConsulta = ServiceStatus.Error;
      falha?.call(onError.message);
    });
  }
}
