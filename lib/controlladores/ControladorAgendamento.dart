import 'package:app_treino/ServiceProvider/AgendamentoService.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/agenda/Agendamento.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_calendar_carousel/classes/event.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorAgendamento.g.dart';

class ControladorAgendamento = _ControladorAgendamentoBase with _$ControladorAgendamento;

abstract class _ControladorAgendamentoBase with Store {
  @observable
  ServiceStatus statusConsultaAgendamentos = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultaHorarios = ServiceStatus.Waiting;

  @observable
  ServiceStatus status = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusTipoAgendamento = ServiceStatus.Waiting;

  // Agendamentos
  @observable
  List<ProfessorNovoAgendamento> professoresAgendamento = [];
  List<NovoAgendamento> agendamentosDisponiveis = [];
  List<Agendamento> agendamentosDoAluno = [];
  List<Agendamento> agendamentosDoAlunoTodos = [];
  // Horarios para reagendar
  List<HorarioReagendamento> horariosDisponiveisReagendar = [];

  ControladorCliente mCliente = GetIt.I.get<ControladorCliente>();

  AgendamentoService mServiceAgendamento = GetIt.I.get<AgendamentoService>();

  String obterHorarioExibir({NovoAgendamento? novoAgendamento, Agendamento? agendamentoAluno}) {
    if (novoAgendamento != null) {
      return novoAgendamento.horarioInicial! + localizedString('to') + novoAgendamento.horarioFinal!;
    } else {
      return agendamentoAluno!.hora! + ' até ' + agendamentoAluno.horaFinal!;
    }
  }

  DateTime? get dataProximaAvaliacaoFisica {
    try {
      var order = agendamentosDoAlunoTodos.where((element) => element.nome!.toLowerCase().contains('avaliação física') || element.nome!.toLowerCase().contains('avaliação 2ª')).toList();
      order.sort((b, a) => a.dataLong!.compareTo(b.dataLong!));
      var dataProxim = UtilDataHora.parseNunToDate(order.first.dataLong)!;
      return DateTime.now().isBefore(dataProxim) ? dataProxim : null;
    } catch (e) {
      return null;
    }
  }

  void consultarAgendamentos({bool disponibilidades = true, Function()? sucesso, Function()? carregando, Function(String? erro)? falha, DateTime? dia, String? periodo}) {
    if (mCliente.mUsuarioLogado?.matricula == null) return;
    carregando?.call();
    statusConsultaAgendamentos = ServiceStatus.Waiting;
    var diaSearch = dia ?? DateTime.now();
    agendamentosDisponiveis.clear();
    agendamentosDoAluno.clear();
    agendamentosDoAlunoTodos.clear();
    agendamentosFuturos.clear();
    bool consultouDisponiveis = false;
    // Null safety: validações para mUsuarioLogado e propriedades
    final usuario = mCliente.mUsuarioLogado;
    if (usuario == null || usuario.codEmpresa == null || usuario.matricula == null || usuario.username == null) {
      statusConsultaAgendamentos = ServiceStatus.Error;
      falha?.call('Usuário não está logado ou dados incompletos para consultar agendamentos.');
      return;
    }
    var _resultTratamentoDisponveis = (value) {
      consultouDisponiveis = true;
      value.forEach((key, disponiveis) {
        agendamentosDisponiveis.addAll(disponiveis);
        obterTiposDeAgendamento();
      });
      statusConsultaAgendamentos = agendamentosFuturos.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      sucesso?.call();
    };
    var consultarDisponiveis = disponibilidades
        ? mServiceAgendamento.consultarAgendamentosPeloDia(
            usuario.codEmpresa!, UtilDataHora.getDiaMesAno(dateTime: diaSearch), periodo ?? 'DIA', true, usuario.matricula)
        : Future.value({});
    mServiceAgendamento
        .consultarAgendamentos(usuario.username!, usuario.matricula)
        .then((value) {
          try {
            agendamentosDoAluno.addAll(value.where((element) => UtilDataHora.getDiaMesAno(dateString: element.data) == UtilDataHora.getDiaMesAno(dateTime: diaSearch)).toList());
          } catch (e) {
            if (kDebugMode) {
              print(e);
            }
          }
          for (var i = 0; i < agendamentosDoAluno.length; i++) {
            agendamentosDoAluno[i].status = localizedString('confirmed');
          }
          agendamentosDoAlunoTodos = value;
          for (final element in agendamentosDoAlunoTodos) {
            try {
              if ((UtilDataHora.dataEigualDaDeHoje(dateTime: UtilDataHora.parseStringToDate(element.data!, minutos: true)!) ||
                      !UtilDataHora.dataJaPassouDeHoje(dateTime: UtilDataHora.parseStringToDate(element.data!, minutos: true)!, ateUltimoMinutoDoDia: true)) ||
                  DateTime.now() == UtilDataHora.parseNunToDate(element.dataLong!)) {
                agendamentosFuturos.add(element);
              }
            } catch (e) {
              if (kDebugMode) {
                print(e);
              }
            }
          }
          return consultarDisponiveis;
        })
        .then(_resultTratamentoDisponveis)
        .catchError((onError) async {
          if (!consultouDisponiveis) {
            _resultTratamentoDisponveis(await consultarDisponiveis);
            return;
          }
          statusConsultaAgendamentos = ServiceStatus.Error;

          falha?.call(onError.message);
        });
  }

final ValueNotifier<List<Agendamento>> listeneragendamentosFuturos = ValueNotifier<List<Agendamento>>([]);
  List<Agendamento> get agendamentosFuturos => listeneragendamentosFuturos.value;
//   List<Agendamento> agendamentosFuturos = [];
  @observable
  List<String> listaAgendamentosPorTipo = [];

  void agruparAgendamentosPorTipo(String comportamento) {
    listaAgendamentosPorTipo.clear();
    Set<String> agendamentos = {};

    for (final element in agendamentosDisponiveis) {
      if ((element.tipoAgendamento?.comportamento ?? '').contains(comportamento)) {
        agendamentos.add(element.tipoAgendamento?.nome ?? '');
      }
    }

    listaAgendamentosPorTipo.addAll(agendamentos);
  }

  /* List<Agendamento> get agendamentosFuturos {
    List<Agendamento> agendamentos = [];
    for (final element in agendamentosDoAlunoTodos) { 
      if((UtilDataHora.dataEigualDaDeHoje(dateTime: UtilDataHora.parseStringToDate(element.data!, minutos: true)!) || !UtilDataHora.dataJaPassouDeHoje(dateTime: UtilDataHora.parseStringToDate(element.data!, minutos: true)!, ateUltimoMinutoDoDia: true)) 
      || DateTime.now() == UtilDataHora.parseNunToDate(element.dataLong!)){
        agendamentos.add(element);
      }
    }
    return agendamentos;
  } */

  bool get temAgendamentoHoje {
    return agendamentosFuturos.any((element) => UtilDataHora.horaDataEstaOcorrendoAgora(dateTime: UtilDataHora.parseStringToDate(element.data!, minutos: true)));
  }

  List<Agendamento> get agendamentosDeHoje {
    List<Agendamento> agendamentos = [];
    for (final element in agendamentosFuturos) {
      if (UtilDataHora.dataEigualDaDeHoje(dataString: element.data)) {
        agendamentos.add(element);
      }
    }
    return agendamentos;
  }

  List<Agendamento> get agendamentoAvaliacao {
    var ordem = agendamentosDoAlunoTodos.where((element) => element.nome!.toLowerCase().contains('avaliação física') || element.nome!.toLowerCase().contains('avaliação 2ª')).toList();
    ordem.sort((b, a) => a.dataLong!.compareTo(b.dataLong!));
    return ordem;
  }

  void cancelarAgendamento(Agendamento agendamento, {Function()? sucesso, Function()? carregando, Function(String? erro)? falha}) {
  carregando?.call();
  final username = mCliente.mUsuarioLogado?.username ?? '';
  final matricula = mCliente.mUsuarioLogado?.matricula ?? '';
  mServiceAgendamento.cancelarAgendamento(username, agendamento.id!, matricula).then((value) {
      agendamentosFuturos.removeWhere((element) => element.id == agendamento.id);
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  void criarAgendamentoAluno(NovoAgendamento agendamento, {Function()? sucesso, Function()? carregando, Function(String? erro)? falha}) {
    carregando?.call();
    mServiceAgendamento.persistirUmAgendamentoAoAluno(PersitenciaDeAgendamento.apartirDeNovoAgendamento(agendamento, 'CONFIRMADO').toJson()).then((value) {
      sucesso?.call();
    }).catchError((onError) {
      if (onError.message.contains('Http status error [409]')) {
        falha?.call('erro_agendamento');
      } else {
        falha?.call(onError.message);
      }
    });
  }

  HorarioReagendamento filtrarHorariosPeloDia(DateTime dateTime) {
    HorarioReagendamento? horario;
    if (horariosDisponiveisReagendar.where((horario) => UtilDataHora.getDiaMesAno(dateTime: dateTime) == horario.dia).isEmpty) {
      return HorarioReagendamento(horarios: [], dia: UtilDataHora.getDiaMesAno(dateTime: dateTime));
    }
    horario = horariosDisponiveisReagendar
        .firstWhere((horario) => UtilDataHora.getDiaMesAno(dateTime: dateTime) == horario.dia && UtilDataHora.dataJaPassouDeHojeHora0(dateTime: UtilDataHora.parseStringToDate(horario.dia)));
    Set<String> conjuntoSemDuplicatas = Set<String>.from(horario.horarios!);
    horario.horarios = conjuntoSemDuplicatas.toList();
    return horario;
  }

  void reagendarEvento(Agendamento agendamento, DateTime dia, String horario, {Function()? sucesso, Function()? carregando, Function(String? erro)? falha}) {
    carregando?.call();
    final username = mCliente.mUsuarioLogado?.username ?? '';
    final matricula = mCliente.mUsuarioLogado?.matricula ?? '';
    mServiceAgendamento
        .reagendarEvento(username, agendamento.id!, UtilDataHora.getDiaMesAno(dateTime: dia), horario, matricula)
        .then((value) {
      consultarAgendamentos(sucesso: sucesso, falha: falha);
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  List<Event> obterListaEventosAgenda({required day}) {
    var dataTratada = UtilDataHora.getDataComHorasPersonalizada(dateTime: day, horas: 0, minutos: 0, segundos: 0);
    List<Event> retorno = [];
    for (final element in horariosDisponiveisReagendar) {
      if (element.horarios!.isNotEmpty) {
        var data = UtilDataHora.parseStringToDate(element.dia!)!;
        if (data == dataTratada) {
          retorno.add(Event(
              date: UtilDataHora.parseStringToDate(element.dia!)!,
              dot: const CircleAvatar(
                backgroundColor: Colors.amber,
              )));
        }
      }
    }
    return retorno;
  }

  void consultarHorariosDisponiveisAgendamentos(Agendamento? agendamento, {Function()? sucesso, Function()? carregando, Function(String? erro)? falha, DateTime? dataPesquisar}) {
    carregando?.call();
    statusConsultaHorarios = ServiceStatus.Waiting;
    if (filtrarHorariosPeloDia(dataPesquisar ?? DateTime.now()).horarios!.isNotEmpty) {
      statusConsultaHorarios = ServiceStatus.Done;
    } else {
      horariosDisponiveisReagendar.clear();
      final username = mCliente.mUsuarioLogado?.username ?? '';
      final matricula = mCliente.mUsuarioLogado?.matricula ?? '';
      mServiceAgendamento
          .consultarReagendamentos(
              username, agendamento!.id!, UtilDataHora.getDiaMesAno(dateTime: dataPesquisar ?? DateTime.now()), matricula)
          .then((value) {
        horariosDisponiveisReagendar.addAll(value);
        statusConsultaHorarios = ServiceStatus.Done;
        sucesso?.call();
      }).catchError((onError) {
        statusConsultaHorarios = ServiceStatus.Error;
        falha?.call(onError.message);
      });
    }
  }

  @observable
  List<TipoAgendamento> tipoDeAgendamento = [];
  @observable
  List<NovoAgendamento> mAgendamentosPorProfessor = [];

  void obterTiposDeAgendamento() {
    tipoDeAgendamento.clear();
    if (agendamentosDisponiveis.isNotEmpty) {
      statusTipoAgendamento = ServiceStatus.Waiting;
      for (final element in agendamentosDisponiveis) {
        tipoDeAgendamento.add(element.tipoAgendamento!);
        statusTipoAgendamento = ServiceStatus.Done;
      }
    } else {
      statusTipoAgendamento = ServiceStatus.Empty;
      tipoDeAgendamento = [];
    }
  }

  void consultarProfessoresAgenda(String value, {Function()? sucesso}) {
    professoresAgendamento.clear();
    if (agendamentosDisponiveis.isNotEmpty) {
      for (final element in agendamentosDisponiveis) {
        if (element.tipoAgendamento!.nome!.contains(value) && (element.tipoAgendamento!.permitir_app!)) {
          professoresAgendamento.add(element.professor!);
        }
      }
    } else {
      professoresAgendamento = [];
    }
    sucesso?.call();
  }

  @observable
  Map<String, List<ProfessorNovoAgendamento>> professoresAgrupado = {};

  void agruparProfessores() {
    professoresAgrupado.clear();
    for (final professor in professoresAgendamento) {
      if (professoresAgrupado.containsKey(professor.nome)) {
        professoresAgrupado[professor.nome]!.add(professor);
      } else {
        professoresAgrupado[professor.nome!] = [professor];
      }
    }
    //print(professoresAgrupado);
  }

  void agendamentoPorProfessor(ProfessorNovoAgendamento professor, String value, DateTime diaSelecionado) {
    List<NovoAgendamento> agendamentos = [];
    mAgendamentosPorProfessor.clear();
    if (agendamentosDisponiveis.isNotEmpty) {
      for (final element in agendamentosDisponiveis) {
        if (element.professor!.nome == professor.nome && element.tipoAgendamento!.nome!.contains(value) && element.dia == UtilDataHora.getDiaMesAno(dateTime: diaSelecionado)) {
          agendamentos.add(element);
        }
      }
      mAgendamentosPorProfessor.addAll(agendamentos);
    } else {
      mAgendamentosPorProfessor = [];
    }
  }

  List<DateTime> todosAgendamentosProfessor(ProfessorNovoAgendamento professor, String value) {
    List<DateTime> datas = [];
    for (final element in agendamentosDisponiveis) {
      if (element.tipoAgendamento?.nome == value && element.professor?.id == professor.id) {
        datas.add(UtilDataHora.parseStringToDate(element.dia) as DateTime);
      }
    }
    return datas;
  }

  void limparTudo() {
    statusConsultaAgendamentos = ServiceStatus.Waiting;
    agendamentosDoAlunoTodos = [];
    agendamentosDoAluno = [];
    agendamentosDisponiveis = [];
    professoresAgendamento = [];
  }
}
