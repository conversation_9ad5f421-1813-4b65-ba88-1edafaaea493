// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorNovoLogin.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorNovoLogin on _ControladorNovoLoginBase, Store {
  late final _$deepLinkCodigoAtom =
      Atom(name: '_ControladorNovoLoginBase.deepLinkCodigo', context: context);

  @override
  String get deepLinkCodigo {
    _$deepLinkCodigoAtom.reportRead();
    return super.deepLinkCodigo;
  }

  @override
  set deepLinkCodigo(String value) {
    _$deepLinkCodigoAtom.reportWrite(value, super.deepLinkCodigo, () {
      super.deepLinkCodigo = value;
    });
  }

  late final _$carregouTimerAtom =
      Atom(name: '_ControladorNovoLoginBase.carregouTimer', context: context);

  @override
  bool get carregouTimer {
    _$carregouTimerAtom.reportRead();
    return super.carregouTimer;
  }

  @override
  set carregouTimer(bool value) {
    _$carregouTimerAtom.reportWrite(value, super.carregouTimer, () {
      super.carregouTimer = value;
    });
  }

  @override
  String toString() {
    return '''
deepLinkCodigo: ${deepLinkCodigo},
carregouTimer: ${carregouTimer}
    ''';
  }
}
