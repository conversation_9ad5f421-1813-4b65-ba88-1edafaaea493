import 'dart:collection';
// ignore: unused_import
import 'dart:convert';
import 'dart:developer';
import 'dart:typed_data';
import 'package:app_treino/ServiceProvider/ContratoUsuarioService.dart';
import 'package:app_treino/ServiceProvider/ServiceDioProvider.dart';
import 'package:app_treino/ServiceProvider/TreinoService.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/controlladores/ControladorAcessoCatraca.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorExecucaoTreino.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoTreino.dart';
import 'package:app_treino/controlladores/ControladorSplash.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/model/avalicaoFisica/AvaliacaoFisicaRecente.dart';
import 'package:app_treino/model/contrato/ContratoUsuario.dart';
import 'package:app_treino/model/doClienteApp/AlunoNaAcademia.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/Usuario.dart';
import 'package:app_treino/model/graduacao/NivelAlunoGraduacao.dart';
import 'package:app_treino/model/personal/EventoTimeLineAluno.dart';
import 'package:app_treino/model/personal/Aluno.dart';
import 'package:app_treino/model/personal/ObjectConsultaAluno.dart';
import 'package:app_treino/model/personal/ProfTreinoWeb.dart';
import 'package:app_treino/model/personal/ProgramaTreinoPerfilAluno.dart';
import 'package:app_treino/model/personal/cadastroProfessorTreinoWeb.dart';
import 'package:app_treino/model/treinoAluno/ProgramadeTreino.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaFiltrosAluno.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaHistoricoAluno.dart';
import 'package:app_treino/screens/prescricaoDeTreino/widgets/GraficoFrequenciaAluno.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaFiltrosTimeLine.dart';
import 'package:collection/collection.dart' show IterableExtension;
import 'package:diacritic/diacritic.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/personal/AlunoDeProfessor.dart';
import 'package:app_treino/model/personal/CadastroAluno.dart';
import 'package:app_treino/model/personal/ProgramaFicha.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:shared_preferences/shared_preferences.dart';
part 'ControladorPrescricaoDeTreino.g.dart';

class ControladorPrescricaoDeTreino = _ControladorPrescricaoDeTreinoBase with _$ControladorPrescricaoDeTreino;

abstract class _ControladorPrescricaoDeTreinoBase with Store {
  final _prescrService = GetIt.I.get<PersonalService>();
  final _controladorCliente = GetIt.I.get<ControladorCliente>();
  final _controladorProfessorWeb = GetIt.I.get<PersonalService>();
  bool veioDiretoDoPerfil = false;
  bool salvouUmTreinoEDeveAbrirOPlano = false;
  Empresa? get empresaSelecionada {
    if ((_controladorCliente.mUsuarioLogado!.empresas?.isNotEmpty ?? false) && ((_controladorCliente.mUsuarioLogado!.empresas?.length ?? 0) > 1)) {
      return _controladorCliente.mUsuarioLogado!.empresas!.firstWhere((element) => element.codigo == _controladorCliente.mUsuarioLogado!.codEmpresa, orElse: () {
        return Empresa();
      });
    } else {
      return null;
    }
  }

  @observable
  ObservableList<Aluno> alunos = ObservableList<Aluno>();
  @observable
  ObservableList<Aluno> alunosCarteira = ObservableList<Aluno>();
  @observable
  ObservableList<Aluno> alunosAux = ObservableList<Aluno>();
  @observable
  ObservableList<AlunoDeProfessor> mAlunos = ObservableList<AlunoDeProfessor>();
  @observable
  ObservableList<AlunoDeProfessor> alunosExibir = ObservableList<AlunoDeProfessor>();
  @observable
  ObservableList<AlunoDeProfessor> mAlunosAddAula = ObservableList<AlunoDeProfessor>();
  @observable
  ObservableList<AlunoDeProfessor> mAlunosAddAulaOriginal = ObservableList<AlunoDeProfessor>();
  @observable
  AlunoDeProfessor? alunoVisualizar;
  @observable
  ObservableList<Aluno> mAlunosAcompanhar = ObservableList<Aluno>();
  @observable
  num codigoAulaAcompanhar = 0;
  @observable
  List<AlunoFichaEmExecucao> mAlunosEmExecucao = [];

  Aluno? alunoExibir;

  @observable
  ProgramaDeTreino? mProgramaAluno;
  @observable
  bool eCriarNovoPlanoDeTreino = false;
  @observable
  bool pesquisaPorNome = false;
  @observable
  ObservableList<HistoricoExecucaoAluno> mHistoricoExecs = ObservableList<HistoricoExecucaoAluno>();

  bool carregandoAluno = false;

  FilterAluno? filterAluno;
  FilterAluno? filter;

  num porcentagemConclusaoDoPrograma(ProgramaDeTreino plano) {
    if (plano.dataTerminoPrevisto == null) {
      return 0.0;
    } else if (DateTime.now().isAfter(UtilDataHora.parseNunToDate(plano.dataTerminoPrevisto)!)) {
      return 1.0;
    } else {
      return (DateTime.now().millisecondsSinceEpoch / (plano.dataTerminoPrevisto ?? 1)) / 100;
    }
  }

  void setAlunoVer(AlunoDeProfessor value) {
    alunoVisualizar = value;
    mProgramaAluno = null;
    mHistoricoExecs.clear();
    eRetryPrograma = false;
    statusConsultaProgramaAluno = ServiceStatus.Waiting;
  }

  void setAlunoExibir(Aluno aluno) {
    alunoExibir = aluno;

  }

  @observable
  int filtroAtual = 0;

  @observable
  ServiceStatus statusConsultarAlunos = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultaProgramaAluno = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultaAcessosAluno = ServiceStatus.Waiting;

  @observable
  bool eRetryPrograma = false;

  @observable
  ServiceStatus statusConsultaHistoricoAluno = ServiceStatus.Waiting;

  Future<void> setFiltro(int value) async {
    filtroAtual = value;
  }

  var mAlunosFiltro = LinkedHashMap<int, List<AlunoDeProfessor>>();

  var alunosFiltrados = LinkedHashMap<int, List<Aluno>>();

  Future<void> consultarAlunosDoProfessor({bool force = false, required Function() sucesso, required Function(String? mensagem) erro, isTelaAddAluno = false}) async {
    statusConsultarAlunos = ServiceStatus.Waiting;
    if (!force && mAlunosFiltro[filtroAtual] != null) {
      statusConsultarAlunos = ServiceStatus.Waiting;
      await Future.delayed(const Duration(seconds: 1));
      mAlunos.clear();
      mAlunos.addAll(mAlunosFiltro[filtroAtual]!);
      statusConsultarAlunos = ServiceStatus.Done;
      if (mAlunos.isEmpty) statusConsultarAlunos = ServiceStatus.Empty;
      try {
        sucesso.call();
      } catch (e) {}
    } else {
      _prescrService
          .consultarAlunos(isTelaAddAluno ? 0 : (filtroAtual != -1 ? filtroAtual : 0), _controladorCliente.mUsuarioLogado!.codUsuario!, _controladorCliente.mUsuarioLogado!.codEmpresa!, 100)
          .then((value) {
        mAlunos.clear();
        if (!isTelaAddAluno) {
          if (filtroAtual == -1) {
            mAlunosFiltro[filtroAtual] = value.where((element) => element.situacao != null && element.situacao == 'IN').toList();
          } else {
            mAlunosFiltro[filtroAtual] = value;
          }
          mAlunos.addAll(mAlunosFiltro[filtroAtual]!);
        } else {
          mAlunosAddAulaOriginal.clear();
          mAlunosAddAulaOriginal.addAll(value);
          mAlunosAddAula.clear();
          mAlunosAddAula.addAll(value);
        }

        if ((mAlunos.isEmpty && !isTelaAddAluno) || (mAlunosAddAula.isEmpty && isTelaAddAluno)) {
          statusConsultarAlunos = ServiceStatus.Empty;
          sucesso.call();
        } else {
          statusConsultarAlunos = ServiceStatus.Done;
          sucesso.call();
        }
      }).catchError((onError) {
        erro.call(onError.message);
        statusConsultarAlunos = ServiceStatus.Error;
        return onError.message;
      });
    }
  }

  bool usuarioTemPrograma() {
    return mProgramaAluno != null && mProgramaAluno!.dataInicio != null && statusConsultaProgramaAluno == ServiceStatus.Done && !programaVenceu();
  }

  bool programaVenceu() {
    if (mProgramaAluno!.dataTerminoPrevisto == null) return false;
    return DateTime.fromMillisecondsSinceEpoch(mProgramaAluno!.dataTerminoPrevisto as int).isBefore(DateTime.now());
  }

  void consultarProgramaDeTreinoDoAluno({Function()? carregando, Function()? sucesso, Function(String? message)? falha, num? alunoId}) {
    statusConsultaProgramaAluno = ServiceStatus.Waiting;
    _prescrService.consultarUltimoProgramaDoAluno(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.username ?? '',
     (alunoId ?? alunoExibir!.id ?? 1)
     ).then((value) {
      mProgramaAluno = value;
      statusConsultaProgramaAluno = ServiceStatus.Done;
      sucesso?.call();
    }).catchError((onError) {
      statusConsultaProgramaAluno = ServiceStatus.Error;
      eRetryPrograma = true;
      falha?.call(onError.message);
    });
  }

  /* void consultarProgramaTreinoProfessor(AlunoNaAulaTurma? alunos, int index, {Function()? carregando, Function()? sucesso, Function(String? message)? falha}) {
    carregando?.call();
    statusConsultaProgramaAluno = ServiceStatus.Waiting;
    _prescrService.consultarUltimoProgramaDoAluno(_controladorCliente.mUsuarioLogado!.username!, alunoVisualizar!.codigo!).then((value) {
      mProgramaAluno = value;
      statusConsultaProgramaAluno = ServiceStatus.Done;
      sucesso!();
    }).catchError((onError) {
      statusConsultaProgramaAluno = ServiceStatus.Error;
      eRetryPrograma = true;
      falha?.call(onError.message);
    });
  } */

  List<HistoricoExecucaoAluno> historicoExecucoesAluno = [];

  void consultarHistoricoDeExecucoesAluno({required num idAluno}) {
    historicoExecucoesAluno = [];
    statusConsultaHistoricoAluno = ServiceStatus.Waiting;
    _prescrService.consultarHistoricoExecucoes(idAluno, 0, 365).then((value) {
      if (value.isNotEmpty) {
        historicoExecucoesAluno.addAll(value);
        historicoExecucoesAluno.sort((a, b) => b.inicio!.compareTo(a.inicio!));
        statusConsultaHistoricoAluno = ServiceStatus.Done;
      } else {
        statusConsultaHistoricoAluno = ServiceStatus.Empty;
      }
    }).catchError((onError) {
      statusConsultaHistoricoAluno = ServiceStatus.Error;
    });
  }

  @observable
  ServiceStatus statusConsultaTimeLine = ServiceStatus.Waiting;

  List<FilterTimeLine> filterTimeLine = [];

  @observable
  List<EventoTimeLineAluno> listEventosTimeLineAluno = [];

  int startRangeTimeLine = DateTime.now().subtract(const Duration(days: 15)).millisecondsSinceEpoch;
  int endRangeTimeLine = DateTime.now().millisecondsSinceEpoch;
  @observable
  DateTime? startDate;
  @observable
  DateTime? endDate;
  @observable
  FilterPeriodo? filtroperidoSelecionado;

  void consultarTimeLineAluno({required num idAluno, Function()? onSuccess, Function()? onError, Function()? carregando}) {
    carregando?.call();
    statusConsultaTimeLine = ServiceStatus.Waiting;
    int dataInicial = startDate != null ? startDate!.millisecondsSinceEpoch : startRangeTimeLine;
    int dataFinal = endDate != null ? endDate!.millisecondsSinceEpoch : endRangeTimeLine;
    String stringFilter = StringFilterTimeLine.parseFilterToString(filterTimeLine);
    var consultas = [_prescrService.consultarTimeLineAluno(dataInicio: dataInicial, dataFim: dataFinal, idAluno: idAluno, tiposEvento: stringFilter),
    _prescrService.listarAtividadeExtras(GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid ?? '')
    ];
     Future.wait(consultas).then((value) {
      ajustarExerciciosVindosDosDoisSistemas(value);
      onSuccess?.call();
    }).catchError((onError) {
      statusConsultaTimeLine = ServiceStatus.Error;
      onError?.call();
    });
  }

  Future<void> consultarLinhaDoTempoComFiltro({required num idAluno, Function()? onSuccess, Function(String? erro)? onError, Function()? carregando, required num idEmpresa}) async {
    carregando?.call();
    statusConsultaTimeLine = ServiceStatus.Waiting;
    int dataInicial = startDate != null ? startDate!.millisecondsSinceEpoch : startRangeTimeLine;
    int dataFinal = endDate != null ? endDate!.millisecondsSinceEpoch : endRangeTimeLine;
    var consultas = [_prescrService.consultarTimeLineAlunoComFiltro(dataInicio: dataInicial, dataFim: dataFinal, idAluno: idAluno, tiposEvento: ['TREINOU'], empresaId: idEmpresa),
    _prescrService.listarAtividadeExtras(GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid ?? '')
    ];
    Future.wait(consultas).then((value) {
      ajustarExerciciosVindosDosDoisSistemas(value);
      onSuccess?.call();
    }).catchError((onError) {
      statusConsultaTimeLine = ServiceStatus.Error;
      onError?.call(onError.toString());
    });
  }

  inserirAtividadeExtras({required ExercicioTreinoExtraFirebase dadosExercicios, required Function() carregando, required Function() sucesso, required Function(String? erro) erro}) {
    carregando();
    _prescrService.inserirAtividadeExtras(GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid ?? '', dadosExercicios.toJson()).then((value) {
      sucesso();
    }).catchError((onError) {
      erro(onError.toString());
    });
  }



  @observable
  ObservableList<String> diasExecutados = ObservableList<String>();

  @observable
  ServiceStatus diasExecutadosStatus = ServiceStatus.Waiting;

  

  void ajustarExerciciosVindosDosDoisSistemas(List<List<Object>> value) {
       listEventosTimeLineAluno.clear();
   diasExecutadosStatus = ServiceStatus.Waiting;
   diasExecutados.clear();
    for (final item in value) {
      if (item is List<EventoTimeLineAluno>) {
        listEventosTimeLineAluno.addAll(item);
      } else if (item is List<ExercicioTreinoExtraFirebase>) {
        for (final exercicio in item) {
          final exercicioFirebase  = GetIt.I.get<ControladorExecucaoTreino>().listaExerciciosTreinoExtra.firstWhere((e) {
            return e.nome.toLowerCase() == exercicio.nomeExercicio!.toLowerCase();
          }, orElse: () {
            return ExerciciosTreinoExtra(nome: exercicio.nomeExercicio!, icone: TreinoIcon.fire, tipo: TipoTreinoExtra.TEMPO_E_CALORIAS, selecionado: false); 
          });
          listEventosTimeLineAluno.add(
            EventoTimeLineAluno(
              data: exercicio.dataExecucao,
              evento: 'TREINO_EXTRA',
              icone: Icon(exercicioFirebase.icone, color: Theme.of(GetIt.I.get<NavigationService>().context).iconTheme.color,),
              descricao: localizedString(exercicio.nomeExercicio ?? ''),
            ));
        }
      }
    }   
    listEventosTimeLineAluno.sort((a, b) => b.data!.compareTo(a.data!));
    if (listEventosTimeLineAluno.isNotEmpty) {
        var listaBackup = List<EventoTimeLineAluno>.from(listEventosTimeLineAluno);
        listaBackup.removeWhere((element) => UtilDataHora.getDataComHorasPersonalizada(dateTime: element.data!, horas: 10)!.isBefore(getPrimeiroDiaDaSemana(UtilDataHora.getDataComHorasPersonalizada(dateTime:DateTime.now(), horas: 9)!)));
        diasExecutados.addAll(
          listaBackup.map((element) => UtilDataHora.getDiaAbreviacao(dateTime: element.data).toUpperCase().replaceAll('.', '')).toList()
        );
      }
    diasExecutadosStatus = ServiceStatus.Done;
    listEventosTimeLineAluno.isEmpty ? statusConsultaTimeLine = ServiceStatus.Empty : statusConsultaTimeLine = ServiceStatus.Done;
  }

  DateTime getPrimeiroDiaDaSemana(DateTime data) {
    int diaDaSemana = data.weekday;
    return data.subtract(Duration(days: diaDaSemana - 1));
  }

  @observable
  List<EventoTimeLineAluno> mlistaEventosAulas = [];

  void filtrarAulasEventos() {
    mlistaEventosAulas.clear();
    listEventosTimeLineAluno.removeWhere((element) => element.evento != 'FEZ_AULA');
    mlistaEventosAulas.addAll(listEventosTimeLineAluno);
  }

  @observable
  ObservableList<AvaliacaoProgresso> mListaAvaliacaoProgresso = ObservableList<AvaliacaoProgresso>();

  void consultarAvaliacaoProgresso({required num codigoClienteAluno, required num idFicha, Function()? onLoading, Function()? onSuccess, Function(String? error)? onError}) {
    onLoading?.call();
    mListaAvaliacaoProgresso.clear();
    _prescrService.consultarAvaliacaoProgresso(alunoId: codigoClienteAluno, fichaId: idFicha).then((value) {
      mListaAvaliacaoProgresso.addAll(value);
      onSuccess?.call();
    }).catchError((onError) {
      onError?.call(onError.toString());
    });
  }

  bool getNivelConcluido(FichaGraduacao niveis, int index) {
    bool resultado = false;
    for (final avaliacao in mListaAvaliacaoProgresso) {
      if (niveis.niveis![index].id == avaliacao.nivelId) {
        resultado = true;
        break;
      } else {
        resultado = false;
      }
    }
    return resultado;
  }

  void renovarProgramaDoAluno({required Function()? carregando, Function()? sucesso, Function(String? erro)? erro}) {
    carregando?.call();
    _prescrService.renovarPrograma(_controladorCliente.mUsuarioLogado!.username!, alunoVisualizar!.empresa!, mProgramaAluno!.codigo!).then((value) {
      consultarProgramaDeTreinoDoAluno(sucesso: () {}, falha: (v) {}, carregando: () {});
      sucesso!();
    }).catchError((onError) {
      erro!(onError.message);
    });
  }

  void updateOrInsertAluno(CadastroAluno alunosave) {
    var alunoInserir = AlunoDeProfessor(
        nome: alunosave.nome,
        email: alunosave.email,
        userName: alunosave.email,
        urlFoto: alunosave.imagemData,
        matricula: alunosave.id,
        codigo: alunosave.id,
        telefones: alunosave.celular,
        dataNascimento: UtilDataHora.getDataAnoMesDia(dataMilis: alunosave.dataNascimento as int?),
        sexo: alunosave.sexo,
        situacao: alunosave.situacao,
        idade: UtilDataHora.getAnosAteHoje(dataMilis: alunosave.dataNascimento as int?),
        objetivos: alunosave.objetivo ?? '');
    if (alunosave.idAluno != null) {
      if (alunoVisualizar != null && alunoVisualizar!.codigo == alunosave.idAluno) {
        alunoVisualizar!.nome = alunosave.nome;
        alunoVisualizar!.email = alunosave.email;
        alunoVisualizar!.urlFoto = alunosave.imagemData;
        alunoVisualizar!.telefones = alunosave.celular;
        alunoVisualizar!.dataNascimento = alunoInserir.dataNascimento;
        alunoVisualizar!.idade = alunoInserir.idade;
        if (alunosave.objetivo != null && alunosave.objetivo!.isNotEmpty) alunoVisualizar!.objetivos = alunosave.objetivo;
      }
      mAlunosFiltro.forEach((key, value) {
        if (value.isNotEmpty) {
          var alunoReplace = value.firstWhereOrNull((element) => element.codigo == alunosave.id);
          if (alunoReplace != null) {
            var index = value.indexOf(alunoReplace);
            mAlunosFiltro[key]![index] = alunoInserir;
            //statusConsultarAlunos = ServiceStatus.Done;
          }
        }
      });
      int indexUpdate = mAlunos.indexWhere((element) => element.codigo == alunosave.id);
      mAlunos[indexUpdate] = alunoInserir;
    } else {
      mAlunosFiltro[0]!.insert(0, alunoInserir);
      mAlunosFiltro[0]!.sort((a, b) => b.nome!.compareTo(a.nome!));
      if (alunosave.situacao == 'IN' && mAlunosFiltro[-1] != null) {
        mAlunosFiltro[-1]!.insert(0, alunoInserir);
      }
      mAlunos.insert(0, alunoInserir);
      mAlunos.sort((a, b) => b.nome!.compareTo(a.nome!));
      //statusConsultarAlunos = ServiceStatus.Done;
    }
  }

  void marcarUpdateAlunos() {
    statusConsultarAlunos = mAlunos.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
  }

  void consultarAlunoPorNome(String termo, {bool telaAddAluno = false}) {
    if (termo.isNotEmpty) {
      var temoSemAcentos = removeDiacritics(termo);
      (telaAddAluno ? mAlunosAddAula : mAlunos).clear();
      (telaAddAluno ? mAlunosAddAula : mAlunos).addAll(
          (telaAddAluno ? mAlunosAddAulaOriginal : mAlunosFiltro[filtroAtual])!.where((element) => removeDiacritics(element.nome!.toLowerCase()).contains(temoSemAcentos.toLowerCase())));
      pesquisaPorNome = true;
      if ((telaAddAluno ? mAlunosAddAula : mAlunos).isEmpty || (telaAddAluno ? mAlunosAddAula : mAlunos).length <= 3) {
        _prescrService
            .consultarAlunos(filtroAtual != -1 ? filtroAtual : 0, _controladorCliente.mUsuarioLogado!.codUsuario!, _controladorCliente.mUsuarioLogado!.codEmpresa!, 100,
                nomePesquisa: temoSemAcentos)
            .then((value) {
          (telaAddAluno ? mAlunosAddAula : mAlunos).clear();
          (telaAddAluno ? mAlunosAddAula : mAlunos).addAll(value);
          statusConsultarAlunos = (telaAddAluno ? mAlunosAddAula : mAlunos).isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
        }).catchError((onError) {
          statusConsultarAlunos = ServiceStatus.Empty;
        });
      } else {
        statusConsultarAlunos = ServiceStatus.Done;
      }
    } else {
      statusConsultarAlunos = ServiceStatus.Done;
      pesquisaPorNome = false;
      (telaAddAluno ? mAlunosAddAula : mAlunos).clear();
      (telaAddAluno ? mAlunosAddAula : mAlunos).addAll((telaAddAluno ? mAlunosAddAulaOriginal : mAlunosFiltro[filtroAtual]!));
      if ((telaAddAluno ? mAlunosAddAula : mAlunos).isEmpty) {
        statusConsultarAlunos = ServiceStatus.Empty;
      }
    }
  }

  @observable
  bool isLastPage = false;
  int pageConsultaAlunos = 0;

  @observable
  ObservableList<Aluno> alunosNaAcademia = ObservableList<Aluno>();

  Future<void> consultarAlunos({String? termoPesquisa, Function(Aluno? aluno)? sucesso, Function(String? mensagem)? erro}) async {
    if (pageConsultaAlunos == 0) {
      statusConsultarAlunos = ServiceStatus.Waiting;
    }
     ObjectConsultaAluno obj = new ObjectConsultaAluno(filterAluno != null ? [filterAluno!] : null, ['nome'], (termoPesquisa?.isNotEmpty ?? false) ? termoPesquisa : null);
     String jsonEncoded = json.encode(obj);
    _prescrService.consultarAlunosTreino(jsonEncoded, json.toString(), pageConsultaAlunos, 10, _controladorCliente.mUsuarioLogado!.codEmpresa!)
      .then((ResponseAlunosPaginado response) {
      try {
        if (response.totalPages == pageConsultaAlunos) {
          isLastPage = true;
        } else {
          isLastPage = false;
        }
        alunosAux.clear();
        if (pageConsultaAlunos != 0) {
          alunosAux.addAll(alunos);
        }
        alunosAux.addAll(response.content!);
        alunos.clear();
        alunos.addAll(alunosAux);
        if (alunos.isEmpty) {
          statusConsultarAlunos = ServiceStatus.Empty;
        } else {
          statusConsultarAlunos = ServiceStatus.Done;
        }
        sucesso?.call(alunos.first);
      } catch (e) {
        erro?.call('Não foi possível carregar o aluno');
      }
    }).catchError((onError) {
      erro?.call(onError.message);
      statusConsultarAlunos = ServiceStatus.Error;
    });
  }

  @observable
  List<ContentAlunoAcademia> listaAlunosNaAcademia = [];

  Future<void> consultarAlunosColaboradorNaAcademia({String? termoPesquisa, Function(Aluno? aluno)? sucesso, Function(String? mensagem)? erro, required bool carteira}) async {
    var obj = {'treino':'NA_ACADEMIA'};
    String jsonEncoded = json.encode(obj);
    _prescrService.consultarAlunosColaborador(carteira, false, jsonEncoded, json.toString(), 0, 30, _controladorCliente.mUsuarioLogado!.codEmpresa!)
      .then((response) {
        listaAlunosNaAcademia.clear();
        listaAlunosNaAcademia.addAll(response);
        listaAlunosNaAcademia.sort((a, b) => (b.dataUltimoAcesso ?? 0).compareTo(a.dataUltimoAcesso ?? 0));
      sucesso?.call(alunos.firstOrNull);
    }).catchError((onError) {
      erro?.call(onError.message);
    });
  }

  void consultarFichaCompleta(ProgramaFicha treinoModelo, {Function(ProgramaFicha treino)? sucesso, Function(String? mensagem)? falha}) {
    _prescrService.consultarDadosDaFicha(_controladorCliente.mUsuarioLogado!.username!, (treinoModelo.ficha ?? treinoModelo.codigo!)).then((value) {
      sucesso!.call(value);
    }).catchError((onError) {
      falha!(onError.message);
    });
  }

  Future<void> removerFichaDeTreino(ProgramaFicha treinoModelo, {Function()? sucesso, Function(String? mensagem)? falha}) async {
    ConfigURL().url(ConfigURL.TREINO).then((urlTreino) async {
      var dx = ServiceDioProvider();
      await dx.getToken();

      http.delete(
          Uri.parse(
              '$urlTreino/prest/ficha/${GetIt.I.get<ControladorApp>().chave}/app/deletarFicha?username=${_controladorCliente.mUsuarioLogado!.username}&codigoDaFicha=${treinoModelo.ficha ?? treinoModelo.codigo}'),
          headers: {'Authorization': '${GetIt.I.get<ControladorSplash>().currentToken}'}).then((value) {
        if (value.body.contains('sucesso')) {
          mProgramaAluno!.programaFichas!.remove(treinoModelo);
          sucesso!();
        } else {
          falha!(localizedString('could_not_delete_workout'));
        }
      }).catchError((onError) {
        falha!.call(onError.message);
      });
    });

    //  _prescrService.deletarFichaDeUmPrograma(_controladorCliente.mUsuarioLogado.username, treinoModelo.ficha ?? treinoModelo.codigo).then((value){
    //     mProgramaAluno.programaFichas.remove(treinoModelo);
    //     sucesso?.call();
    //  })
    //  .catchError((onError){
    //    falha?.call(onError.message);
    //  });
  }

  void manterFichaOnline(ProgramaFicha treinoModelo, {Function()? sucesso, Function(String mensagem)? falha}) {
    treinoModelo.programasFicha = [];
    if (treinoModelo.programasFicha == null || treinoModelo.programasFicha!.isEmpty) {
      treinoModelo.programasFicha = [];
      treinoModelo.programasFicha!.add(ProgramaSimplesAluno(
        programa: mProgramaAluno!.codigo,
        diaSemana: treinoModelo.diaSemana,
      ));
    } else {
      treinoModelo.programasFicha![0].diaSemana = treinoModelo.diaSemana;
    }
    if (treinoModelo.nivel?.nome == null) treinoModelo.nivel = null;
    treinoModelo.username = _controladorCliente.mUsuarioLogado!.username;
    if (treinoModelo.codigo != null) {
      treinoModelo.atividadesFicha?.forEach((element) {
        element.ficha = treinoModelo.codigo;
        element.series?.forEach((serie) {
          serie!.atividadeFicha = element.codigo;
          serie.ordem = element.series!.indexOf(serie);
          try {
            serie.carga = num.tryParse((serie.cargaApp?.isEmpty ?? false) ? '0' : serie.cargaApp ?? '0') ?? 0;
          } catch (e) {}
          serie.repeticaoComp = serie.repeticaoApp;
        });
      });
    }
    treinoModelo.usarComoPredefinida ??= false;
    log('JSON FICHA', error: const JsonCodec().encode(treinoModelo.toJsonPredefindo(permitirCodAtividade: false)));
    _prescrService.persistirFicha(treinoModelo.toJsonPredefindo(permitirCodAtividade: false), _controladorCliente.mUsuarioLogado!.username!).then((value) {
      var achou = false;
      value.diaSemana = treinoModelo.diaSemana;
      for (var i = 0; i < mProgramaAluno!.programaFichas!.length; i++) {
        var element = mProgramaAluno!.programaFichas![i];
        if (treinoModelo.codigo != null && (element.ficha == treinoModelo.codigo || element.codigo != null && element.codigo == treinoModelo.codigo)) {
          mProgramaAluno!.programaFichas![i] = value;
          achou = true;
        }
      }
      value.tipoExecucao = treinoModelo.tipoExecucao;
      value.programasFicha = treinoModelo.programasFicha;
      value.diaSemana = treinoModelo.diaSemana;
      if (!achou) {
        mProgramaAluno!.programaFichas!.insert(0, value);
      }
      sucesso!();
    }).catchError((onError) {
      falha!(localizedString('could_not_add_workout'));
    });
  }

  int getDiasSubtrair(FiltroFrequencia filtro) {
    switch (filtro) {
      case FiltroFrequencia.MES:
        return 30;
      case FiltroFrequencia.SEMANA:
        return 7;
      case FiltroFrequencia.SEIS_MESES:
        return 180;
      default:
        return 30;
    }
  }

  // void consultarAcessosAluno(
  //   FiltroFrequencia filtro, {
  //   Function()? carregando,
  //   Function(AcessosSemanalAluno acessos)? sucesso,
  //   Function(String erro)? falha,
  // }) {
  //   carregando?.call();
  //   statusConsultaAcessosAluno = ServiceStatus.Waiting;
  //   int diasSubtrair = filtro.numeroDias ?? 30;
  //   _prescrService
  //       .consultarAcessos(_controladorCliente.mUsuarioLogado!.username!, alunoExibir!.matriculaZW!, (DateTime.now().subtract(Duration(days: diasSubtrair)).millisecondsSinceEpoch), DateTime.now().millisecondsSinceEpoch)
  //       .then((value) {
  //     sucesso?.call(value);
  //     statusConsultaAcessosAluno = ServiceStatus.Done;
  //   }).catchError((onError) {
  //     statusConsultaAcessosAluno = ServiceStatus.Error;
  //   });
  // }

  void registrarFaceAluno(Uint8List fotoA, Uint8List fotoB, String codigoAcesso, {Function()? sucesso, Function()? carregando, Function(String mensagem)? falha}) {
    carregando?.call();
    _prescrService.registrarFace({
      'codigo': codigoAcesso,
      'chave': GetIt.I.get<ControladorApp>().chave,
      'imagem1': base64Encode(fotoA),
      'imagem2': base64Encode(fotoB),
    }).then((value) {
      sucesso?.call();
    }).catchError((onError) {
      falha?.call('Não foi possível registrar a face');
    });
  }

  @observable
  String codigoDeAcesso = '';

  void consultarCodAcesso(num matricula, {Function()? sucesso, Function()? carregando, Function(String mensagem)? falha}) {
    carregando?.call();
    _prescrService.consultarCodAcesso(matricula: matricula).then((value) {
      codigoDeAcesso = value;
    }).catchError((onError) {
      falha?.call(onError.toString());
    });
  }

  @observable
  ServiceStatus statusCardContratoAluno = ServiceStatus.Waiting;
  @observable
  ServiceStatus statusCardPerfilAluno = ServiceStatus.Waiting;

  ServiceStatus statusConsultaContrato = ServiceStatus.Waiting;
  final _serviceContrato = GetIt.I.get<ContratoUsuarioService>();
  ContratoUsuario? contratoAlunoExibir;

  void consultarContrato({required num codigoClienteAluno, Function()? onSuccess, Function()? onLoading, Function(String mensagem)? onError}) {
    onLoading?.call();
    contratoAlunoExibir = null;
    statusConsultaContrato = ServiceStatus.Waiting;
    _serviceContrato.consultarContratos(codigoClienteAluno, 1).then((List<ContratoUsuario> listContratos) {
      if (listContratos.isNotEmpty) {
        contratoAlunoExibir = listContratos.first;
        statusConsultaContrato = ServiceStatus.Done;
      } else {
        statusConsultaContrato = ServiceStatus.Empty;
      }
      onSuccess?.call();
    }).catchError((error) {
      statusConsultaContrato = ServiceStatus.Error;
      onError?.call(error.toString());
    });
  }

  ServiceStatus statusConsultaAvaliacaoRecente = ServiceStatus.Waiting;
  @observable
  ServiceStatus statusCardAvaliacaoFisica = ServiceStatus.Waiting;
  AvaliacaoFisicaRecente? avaliacaoRecenteAlunoExibir;

  void consultarAvaliacaoFisicaRecente({required num idAluno, Function()? onLoading, Function()? onSuccess, Function(String? error)? onError}) {
    onLoading?.call();
    statusConsultaAvaliacaoRecente = ServiceStatus.Waiting;
    avaliacaoRecenteAlunoExibir = null;
    _prescrService.consultarAvaliacaoFisicaRecente(_controladorCliente.mUsuarioLogado!.codEmpresa!, idAluno).then((AvaliacaoFisicaRecente avaliacao) {
      avaliacaoRecenteAlunoExibir = avaliacao;
      if (avaliacao.id == null) {
        statusConsultaAvaliacaoRecente = ServiceStatus.Empty;
      } else {
        statusConsultaAvaliacaoRecente = ServiceStatus.Done;
      }
      onSuccess?.call();
    }).catchError((onError) {
      onError?.call(onError.toString());
      statusConsultaAvaliacaoRecente = ServiceStatus.Error;
    });
  }

  List<ObservacaoAluno> listObservacoes = [];
  ServiceStatus statusConsultaObservacoes = ServiceStatus.Waiting;
  @observable
  ServiceStatus statusCardObservacoesAluno = ServiceStatus.Waiting;

  void consultarObservacoesAluno({required num codigoMatricula, Function()? onLoading, Function()? onSuccess, Function(String? error)? onError}) {
    onLoading?.call();
    listObservacoes.clear();
    statusConsultaObservacoes = ServiceStatus.Waiting;
   _prescrService.consultarObservacoesAluno(codigoMatricula,page: 0,size: 10).then((List<ObservacaoAluno> listObservacoes) {
      if (listObservacoes.isNotEmpty) {
      listObservacoes.sort((a, b) => num.parse(b.dataCadastro.toString()).compareTo(num.parse(a.dataCadastro.toString())));
        this.listObservacoes.clear();
        this.listObservacoes = listObservacoes;
        statusConsultaObservacoes = ServiceStatus.Done;
        onSuccess?.call();
      } else {
        statusConsultaObservacoes = ServiceStatus.Empty;
        onSuccess?.call();
      }
    }).catchError((error) {
      statusConsultaObservacoes = ServiceStatus.Error;
      onError?.call(error.toString());
    });
  }

  List<NivelAlunoGraduacao>? listNiveisGraduacao;
  @observable
  ServiceStatus statusCardNiveisAluno = ServiceStatus.Waiting;
  ServiceStatus statusConsultaGraduacao = ServiceStatus.Waiting;

  void consultarGraduacaoAluno({required num codigoClienteAluno, Function()? onLoading, Function()? onSuccess, Function(String? error)? onError}) {
    onLoading?.call();
    this.listNiveisGraduacao?.clear();
    statusConsultaGraduacao = ServiceStatus.Waiting;
    _prescrService.consultarGraduacaoAluno(codigoCliente: codigoClienteAluno, chaveZW: GetIt.I.get<ControladorApp>().chave!).then((List<NivelAlunoGraduacao> value) {
      listNiveisGraduacao = value;
      if (listNiveisGraduacao?.isEmpty ?? false) {
        statusConsultaGraduacao = ServiceStatus.Empty;
      } else {
        statusConsultaGraduacao = ServiceStatus.Done;
      }
      onSuccess?.call();
    }).catchError((error) {
      onError?.call(error.toString());
      statusConsultaGraduacao = ServiceStatus.Error;
    });
  }

  String tituloNivelAtual(NivelAlunoGraduacao ficha, num matricula) {
    String nome = localizedString('beginner');
    if (ficha.ficha?.niveis?.isNotEmpty ?? false) {
      ficha.ficha?.niveis?.forEach((element) {
        if(element.alunoIds?.isNotEmpty ?? false) {
          if (element.alunoIds?.any((id) => id == matricula) ?? false) {
            nome = element.nome ?? '';
          }
        }
      });
    }
    return nome;
  }

  ServiceStatus statusConsultaQtdeAulasNoNivel = ServiceStatus.Waiting;

  Future<num> consultarQtdeAulasFeitasNoNivel(
      {required num codigoCliente, required num idNivel, required num matriculaZW, required bool isLastConsulta, Function()? onSuccess, Function()? onError}) async {
    statusConsultaQtdeAulasNoNivel = ServiceStatus.Waiting;
    return await _prescrService.consultarQtdeAulasFeitasNoNivel(codigoCliente: codigoCliente, idNivel: idNivel, matriculaZW: matriculaZW).then((qtde) {
      if (isLastConsulta) {
        statusConsultaQtdeAulasNoNivel = ServiceStatus.Done;
      }
      return qtde;
    }).catchError((error) {
      statusConsultaQtdeAulasNoNivel = ServiceStatus.Error;
      return 0;
    });
  }

  void consultarProgramaTreinoPerfilAluno({required num idAluno, required num idEmpresa, Function()? onLoading, Function(ProgramaTreinoPerfilAluno programa)? onSuccess, Function(String? error)? onError}) {
    onLoading?.call();
    statusConsultaAcessosAluno = ServiceStatus.Waiting;
    _prescrService.consultarProgramaTreinoPerfilAluno(idAluno: idAluno, idEmpresa: idEmpresa).then((ProgramaTreinoPerfilAluno programa) {
      onSuccess?.call(programa);
      statusConsultaAcessosAluno = ServiceStatus.Done;
    }).catchError((onError) {
      statusConsultaAcessosAluno = ServiceStatus.Error;
      onError?.call(onError.toString());
    });
  }

  bool veioPelosDetalhesDoAluno = false;

  @observable
  ServiceStatus statusConsultaDetalhesDoAluno = ServiceStatus.Waiting;

  List<ProgramaTreino> programasDeTreinoAlunoExibir = [];

  ProgramaTreino programaMaisRecenteAlunoExibir() {
    programasDeTreinoAlunoExibir.sort((a, b) => a.termino!.compareTo(b.termino!));
    return programasDeTreinoAlunoExibir.last;
  }

  ProgramaTreino? programaAtualAlunoExibir() {
    ProgramaTreino? programaTreinoFiltrado =
        programasDeTreinoAlunoExibir.firstWhereOrNull((programa) => programa.inicio! < DateTime.now().millisecondsSinceEpoch && programa.termino! > DateTime.now().millisecondsSinceEpoch);
    return programaTreinoFiltrado == null ? programaMaisRecenteAlunoExibir() : programaTreinoFiltrado;
  }

  double porcentagemConclusaoPrograma(ProgramaTreino programaDeTreino) {
    double duracaoEmMilliseconds = (programaDeTreino.termino! - programaDeTreino.inicio!).toDouble();
    double millisecondsSinceComecoPrograma = (DateTime.now().millisecondsSinceEpoch - programaDeTreino.inicio!).toDouble();
    var porcentagem = millisecondsSinceComecoPrograma / duracaoEmMilliseconds;
    if (porcentagem > 1) {
      return 1;
    }
    if (porcentagem < 0) {
      return 0;
    }
    return porcentagem;
  }

  bool isProgramaMaisRecenteVencido() {
    return DateTime.now().isAfter(DateTime.fromMillisecondsSinceEpoch(programaMaisRecenteAlunoExibir().termino!.toInt()));
  }

  void consultarDetalhesDoAluno({bool fluxoAlunos = true, num? matricula, required num idAluno, Function()? onLoading, Function()? onSuccess, Function()? onError}) {
    Future<Aluno> service;
    statusConsultaDetalhesDoAluno = ServiceStatus.Waiting;
    onLoading?.call();
    if (matricula != null) {
      service = _prescrService.consultarAlunoDetalhadoMatricula(matricula: matricula, empresaId: GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0);
    } else {
      service = _prescrService.consultarAlunoDetalhado(codigoAluno: idAluno, empresaId: GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0);
    }
    service.then((alunoDetalhado) { 
      alunoExibir?.id = alunoDetalhado.id;
      programasDeTreinoAlunoExibir = alunoDetalhado.programas ?? [];
      alunoExibir?.professor = alunoDetalhado.professor;
      // isProgramaMaisRecenteVencido(programaMaisRecenteAlunoExibir(programasDeTreinoAlunoExibir))
      statusConsultaDetalhesDoAluno = programasDeTreinoAlunoExibir.isEmpty ? ServiceStatus.Empty : ServiceStatus.Done;
      onSuccess?.call();
    }).catchError((error) {
      statusConsultaDetalhesDoAluno = ServiceStatus.Error;
      onError?.call();
    });
  }

    void consultarDetalhesDoAlunoSimplificado({num? matricula, required num idAluno, Function()? onLoading, Function(Aluno aluno)? onSuccess, Function()? onError}) {
    Future<Aluno> service;
    statusConsultaDetalhesDoAluno = ServiceStatus.Waiting;
    onLoading?.call();
    if (matricula != null) {
      service = _prescrService.consultarAlunoDetalhadoMatricula(matricula: matricula, empresaId: GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0);
    } else {
      service = _prescrService.consultarAlunoDetalhado(codigoAluno: idAluno, empresaId: GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0);
    }
    service.then((alunoDetalhado) {
      statusConsultaDetalhesDoAluno = ServiceStatus.Done;
      onSuccess?.call(alunoDetalhado);
    }).catchError((error) {
      statusConsultaDetalhesDoAluno = ServiceStatus.Error;
      onError?.call();
    });
  }

  void renovarProgramaDeTreinoAtual({required num idAluno, Function()? onLoading, Function()? onSuccess, Function()? onError}) {
    onLoading?.call();
    _prescrService.renovarProgramaDeTreinoAtual(idEmpresa: _controladorCliente.mUsuarioLogado!.codEmpresa!, objetoComIdAluno: {'alunoId': idAluno}).then((value) {
      onSuccess?.call();
    }).catchError((error) {
      onError?.call();
    });
  }

  void checkIfCanBuildCardNiveis() {
    if (statusConsultaGraduacao == ServiceStatus.Done) {
      statusCardNiveisAluno = ServiceStatus.Done;
    }
    if (statusConsultaGraduacao == ServiceStatus.Empty && statusConsultaQtdeAulasNoNivel == ServiceStatus.Empty) {
      statusCardNiveisAluno = ServiceStatus.Empty;
    }
    if (statusConsultaGraduacao == ServiceStatus.Error) {
      statusCardNiveisAluno = ServiceStatus.Error;
    }
  }

  void checkIfCanBuildCardObservacoes() {
    if (statusConsultaObservacoes == ServiceStatus.Done) {
      statusCardObservacoesAluno = ServiceStatus.Done;
    }
    if (statusConsultaObservacoes == ServiceStatus.Waiting) {
      statusCardObservacoesAluno = ServiceStatus.Waiting;
    }
    if (statusConsultaObservacoes == ServiceStatus.Empty) {
      statusCardObservacoesAluno = ServiceStatus.Empty;
    }
      if (statusConsultaObservacoes == ServiceStatus.Error) {
      statusCardObservacoesAluno = ServiceStatus.Error;
    }
  }

  void checkIfCanBuildCardContrato() {
    if ((statusConsultaAvaliacaoRecente == ServiceStatus.Done || statusConsultaAvaliacaoRecente == ServiceStatus.Empty) && statusConsultaContrato == ServiceStatus.Done) {
      statusCardContratoAluno = ServiceStatus.Done;
    }
    if (statusConsultaContrato == ServiceStatus.Empty || statusConsultaContrato == ServiceStatus.Error) {
      statusCardContratoAluno = ServiceStatus.Empty;
    }
  }

  void checkIfCanBuildCardPerfilAluno() {
    if (statusConsultaAvaliacaoRecente == ServiceStatus.Done || statusConsultaAvaliacaoRecente == ServiceStatus.Empty) {
      statusCardPerfilAluno = ServiceStatus.Done;
    }
  }

  void checkIfCanBuildCardAvaliacaoFisica() {
    if (statusConsultaAvaliacaoRecente == ServiceStatus.Done) {
      statusCardAvaliacaoFisica = ServiceStatus.Done;
    } else if (statusConsultaAvaliacaoRecente == ServiceStatus.Empty) {
      statusCardAvaliacaoFisica = ServiceStatus.Empty;
    }
  }

  double? porcentagemCumpridaDoContrato(ContratoUsuario contrato) {
    try {
      var dataInicio = UtilDataHora.parseStringToDate(contrato.dataLancamento!)!;
      var dataFim = UtilDataHora.parseStringToDate(contrato.vigenciaAteAjustada!)!;
      var diasTotal = dataFim.difference(dataInicio).inDays;
      var diasPassouDoInicioAteHoje = DateTime.now().difference(dataInicio).inDays;
      diasPassouDoInicioAteHoje = diasPassouDoInicioAteHoje > 0 ? diasPassouDoInicioAteHoje : 0;
      return UtilitarioApp.percentEntre0e100(valorA: diasPassouDoInicioAteHoje, valorB: diasTotal) as double;
    } catch (error) {
      return null;
    }
  }

  void adicionarAlunoAcompanhar({required Function()? carregando, required Function()? sucesso, required List<Aluno> alunosAcompanhar}) {
    statusConsultarAlunos = ServiceStatus.Waiting;
    carregando?.call();
    mAlunosAcompanhar.addAll(alunosAcompanhar);
    sucesso?.call();
    statusConsultarAlunos = ServiceStatus.Done;
  }

  void limparListaAcompanhar({required Function()? carregando, required Function()? sucesso}) {
    carregando!.call();
    mAlunosAcompanhar.clear();
    mAlunosEmExecucao.clear();
    sucesso!.call();
  }

  void excluirAlunoAcompanhar({required Function()? carregando, required Function()? sucesso, required Aluno alunosAcompanhar}) {
    statusConsultarAlunos = ServiceStatus.Waiting;
    carregando?.call();
    for (final element in mAlunosAcompanhar) {
      if (alunosAcompanhar.matriculaZW == element.matriculaZW) {
        mAlunosAcompanhar.remove(alunosAcompanhar);
        sucesso?.call();
        statusConsultarAlunos = ServiceStatus.Done;
      }
    }
  }

  @observable
  AlunoFichaEmExecucao alunosFicha = AlunoFichaEmExecucao();

  void adicionarAlunoExecucao({Aluno? maluno, Fichas? fichaSelecionada, ProgramaFicha? programaFicha, required Function()? sucesso, required Function()? falha}) async {
    try {
      if (maluno != null) {
        alunosFicha.aluno = maluno;
        alunosFicha.fichaExecutada = fichaSelecionada;
        alunosFicha.programaFicha = programaFicha;
      } else {
        throw Exception('maluno é nulo');
      }
    } catch (e) {
      falha?.call();
      statusConsultarAlunos = ServiceStatus.Error;
      return;
    }
    SharedPreferences alunoEmExecucao = await SharedPreferences.getInstance();
    alunoEmExecucao.setString(maluno.codigoCliente.toString(), jsonEncode(alunosFicha.toJson())).then((value) {
      sucesso?.call();
      statusConsultarAlunos = ServiceStatus.Done;
    }).catchError((onError) {
      falha?.call();
      statusConsultarAlunos = ServiceStatus.Error;
    });
  }

  Future<void> consultarAlunosEmExecucao(Aluno? maluno) async {
    List<AlunoFichaEmExecucao> mAlunosEmExecucaoBackup = [];
    return await SharedPreferences.getInstance().then((db) {
      var rawJson = db.getString(maluno!.codigoCliente.toString());
      Map<String, dynamic> map = jsonDecode(rawJson!);
      AlunoFichaEmExecucao person = AlunoFichaEmExecucao.fromJson(map);
      mAlunosEmExecucaoBackup.add(person);
      mAlunosEmExecucao.addAll(mAlunosEmExecucaoBackup);
    });
  }

  void contabilizarAcompanharWeb({required num id, required Function()? sucesso, required Function()? falha}) {
    _prescrService.contabilizarAcompanharV2(GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa ?? 1, id, GetIt.I.get<ControladorCliente>().getCodigoProfessor).then((value) {
      sucesso?.call();
    }).catchError((onError) {
      falha?.call();
    });
  }

  Future<void> finalizarAcompanharWeb({required num id, required num idPrograma, required num idFicha, required Function() sucesso, required Function() falha}) async {
    await _prescrService.finalizarAcompanharV2(GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa ?? 1, id, GetIt.I.get<ControladorCliente>().getCodigoProfessor, idPrograma, idFicha).then((value) {
      sucesso.call();
    }).catchError((onError) {
      falha.call();
    });
  }

  final _mServiceTreino = GetIt.I.get<TreinoService>();

  Future<void> concluirAtividades(int indexAlunno, int nota, String comentario, Function() concluiuTodasAtividades) async {
    var concluidas = 0;
    for (var i = 0; i < mAlunosEmExecucao[indexAlunno].fichaExecutada!.atividades!.length; i++) {
      if (mAlunosEmExecucao[indexAlunno].fichaExecutada!.atividades![i].concluida) {
        concluidas = concluidas + 1;
      }
    }
    if (concluidas == mAlunosEmExecucao[indexAlunno].fichaExecutada!.atividades!.length) {
      await concluirTreinoProfessor(mAlunosEmExecucao[indexAlunno], nota, comentario, carregando: () {}, sucesso: () {
        concluiuTodasAtividades();
      }, falha: (error) {});
    }
  }

  /// Preserva o status concluído das atividades da ficha original na ficha retornada do servidor
  /// Este método é útil para evitar perder o estado local das atividades após salvamento
  void preservarStatusConcluido(Fichas fichaOriginal, Fichas fichaRetorno, Function(Fichas) sucesso ) {
    if (fichaOriginal.atividades != null && fichaRetorno.atividades != null) {
      fichaRetorno.idPrograma = fichaOriginal.idPrograma;
      // Para cada atividade da ficha retornada, procura a correspondente na ficha original
      for (int i = 0; i < fichaRetorno.atividades!.length; i++) {
        var atividadeRetorno = fichaRetorno.atividades![i];

        // Procura a atividade correspondente na ficha original pelo ID
        for (int j = 0; j < fichaOriginal.atividades!.length; j++) {
          var atividadeOriginal = fichaOriginal.atividades![j];

          // Verifica se os IDs são iguais antes de preservar o status
          if (atividadeOriginal.id != null &&
              atividadeRetorno.id != null &&
              atividadeOriginal.id == atividadeRetorno.id) {
            // IDs são iguais, preserva o status concluído
            atividadeRetorno.concluida = atividadeOriginal.concluida;
            break; // Encontrou a correspondência, pode parar de procurar
          }
        }
      }
    }
    sucesso(fichaRetorno);
  }

  salvarFirebase(AlunoFichaEmExecucao alunoFicha) {
    var treinoExecutado = ExecucaoTreinoFirebase(
        categoria: alunoFicha.fichaExecutada!.categoria!.nome,
        dataConclusao: DateTime.now().millisecondsSinceEpoch,
        nome: alunoFicha.fichaExecutada!.nome,
        tempoTotalEmexecusao: 0,
        quantidadeAtividadesConcluidas: (alunoFicha.fichaExecutada?.atividades?.where((a) => (a.concluida)).length ?? 0));
    Map<String, dynamic> treinoExecutadoJSON = {
      'imagemKey': alunoFicha.fichaExecutada!.atividades!.first.atividade?.images?.first.uri ?? getImagemTag(alunoFicha.fichaExecutada, alunoFicha),
      'categoria': alunoFicha.fichaExecutada?.categoria?.nome ?? 'Sem categoria',
      'dataConclusao': DateTime.now().millisecondsSinceEpoch,
      'nome': alunoFicha.fichaExecutada!.nome,
      'tempoTotalEmexecusao': 0,
      'quantidadeAtividadesConcluidas ': (alunoFicha.fichaExecutada?.atividades?.where((a) => (a.concluida)).length ?? 0)
    };
    _mServiceTreino.enviarTreinoFirebase(treinoExecutadoJSON, GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid!).then((value) {
      treinoExecutado.key = DateTime.now().millisecondsSinceEpoch.toString();
    }).catchError((onError) {});
  }

  Future<void> concluirTreinoProfessor(AlunoFichaEmExecucao alunoFicha, int nota, String comentario, {Function()? carregando, Function()? sucesso, Function(String? erro)? falha}) async {
    final dataInicioExecucao = (UtilDataHora.parseStringToDate(alunoFicha.fichaExecutada?.dataInicio ?? DateTime.now().toString())?.millisecondsSinceEpoch ?? 0);
    final duracaoTotalExecucao =
        ((DateTime.now().millisecondsSinceEpoch - (dataInicioExecucao == 0 ? DateTime.now().subtract(const Duration(minutes: 1)).millisecondsSinceEpoch : dataInicioExecucao)) ~/ 1000);
    String? email = alunoFicha.aluno!.emails!.isNotEmpty ? alunoFicha.aluno?.emails?.first : '';
    carregando?.call();
    await _mServiceTreino.concluirTreino(email ?? '', alunoFicha.fichaExecutada?.idPrograma ?? 0, alunoFicha.fichaExecutada?.id ?? 0, UtilDataHora.getDiaMesAno(dateTime: DateTime.now()), nota, duracaoTotalExecucao,
      comentario, alunoFicha.aluno!.matriculaZW.toString()).then((value) {
      sucesso!.call();
    }).catchError((onError) {
      falha!.call(onError.toString());
    });
  }

  String getImagemTag(Fichas? ficha, AlunoFichaEmExecucao alunoFicha) {
    if (ficha == null) return '';
    if (ficha.atividades == null || ficha.atividades!.isEmpty) {
      return 'alongamento';
    }
    String? urlRetorno;
    listaImagensShow.forEach((key, value) {
      key.split(',').forEach((element) {
        if (removeDiacritics(alunoFicha.fichaExecutada!.atividades!.first.atividade!.nome!.toLowerCase()).contains(element)) {
          urlRetorno = key;
        }
      });
    });
    return urlRetorno ?? 'alongamento';
  }

  LinkedHashMap<String, String> imagens = LinkedHashMap();
  LinkedHashMap<String, String> get listaImagensShow {
    imagens['alongamento'] = 'assets/images/treinoAluno/alongamento.webp';
    imagens['legpess,leg'] = 'assets/images/treinoAluno/leg.webp';
    imagens['prancha'] = 'assets/images/treinoAluno/prancha.webp';
    imagens['remada'] = 'assets/images/treinoAluno/remada.webp';
    imagens['supino'] = 'assets/images/treinoAluno/supino.webp';
    imagens['abs,abdominal'] = 'assets/images/treinoAluno/abs.webp';
    imagens['biceps,bicipes'] = 'assets/images/treinoAluno/biceps.webp';
    imagens['cardio'] = 'assets/images/treinoAluno/cardio.webp';
    imagens['crucifixo'] = 'assets/images/treinoAluno/crucifixo.webp';
    imagens['elevação'] = 'assets/images/treinoAluno/elevacaolateral.webp';
    imagens['evolução'] = 'assets/images/treinoAluno/evolucao.webp';
    imagens['scott'] = 'assets/images/treinoAluno/scott.webp';
    imagens['triceps'] = 'assets/images/treinoAluno/triceps.webp';
    imagens['rosca scott'] = 'assets/images/treinoAluno/rosca.webp';
    return imagens;
  }

  Future<void> alunoConcluiuTreino(Aluno? maluno, {Function()? carregando, Function(Aluno aluno)? sucesso}) async {
    carregando!.call();
    for (final element in mAlunosEmExecucao) {
      if (element.aluno!.id == maluno!.id) {
        mAlunosEmExecucao.remove(element);
        sucesso!.call(maluno);
        break;
      }
    }
  }

  Future<void> registrarAcessoAluno({required Aluno maluno, Function()? carregando, Function(Aluno aluno)? sucesso}) async {
    carregando!.call();
    await GetIt.I.get<ControladorAcessoCatraca>().registrarAcesso(
      chave: GetIt.I.get<ControladorApp>().chave!,
      empresa: GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa ?? 1,
      codigoCliente: maluno.codigoCliente ?? 0,
      dataAcesso: UtilDataHora.getDiaMesAnoHorasMinutosSegundos(dateTime: DateTime.now())
    );
    sucesso!.call(maluno);
  }

  bool habilitarExecucao(Aluno? maluno) {
    var habilitado = true;
    for (final element in mAlunosEmExecucao) {
      if (element.aluno!.matriculaZW == maluno!.matriculaZW) {
        habilitado = false;
      } else {
        habilitado = true;
      }
    }
    return habilitado;
  }

  bool habilitarExecucaoFicha(Fichas? fichas) {
    var habilitado = true;
    if (fichas!.atividades!.isNotEmpty) {
      habilitado = true;
    } else {
      habilitado = false;
    }
    return habilitado;
  }

  String? categoria(int? categoria) {
    switch (categoria) {
      case 0:
        return 'SEM CATEGORIA';
      case 1:
        return 'AEROBICO';
      case 2:
        return 'COND. FÍSICO';
      case 3:
        return 'DEFINIÇÃO';
      case 4:
        return 'DIMINUIR % GORDURA';
      case 5:
        return 'FORÇA';
      case 6:
        return 'HIPERTROFIA';
      case 7:
        return 'SAÚDE';
      case 8:
        return 'SIMPLES';
    }
    return 'SEM CATEGORIA';
  }

  limparTudo() {
    mAlunosAcompanhar.clear();
    mAlunosEmExecucao.clear();
    alunoVisualizar = null;
    mAlunos.clear();
    mAlunosFiltro.clear();
    mHistoricoExecs.clear();
    mProgramaAluno = null;
    eRetryPrograma = false;
    eCriarNovoPlanoDeTreino = false;
    pesquisaPorNome = false;
    codigoDeAcesso = '';
    listEventosTimeLineAluno.clear();
  }

  //---------------------------------------- Serviço listar Professor Web ----------------------------------

  @observable
  ServiceStatus mStatusProfessorWeb = ServiceStatus.Waiting;

  @observable
  List<ProfTreinoWeb> professorTreinoWeb = [];

  void consultarProfessorTreinoWeb({String? nome, Function()? carregando, Function()? sucesso, Function(dynamic)? erro}) {
    var quicksearchValue = nome != null ? nome : 'null';
    var filtros = {
      'tipoColaborador': 'TW',
      'situacoes': ['ATIVO'],
      'quicksearchValue': quicksearchValue,
      'quicksearchFields': ['nome']
    };
    int page = 0;
    int size = 200;
    String jsonFiltros = jsonEncode(filtros);
    mStatusProfessorWeb = ServiceStatus.Waiting;

    _controladorProfessorWeb
        .consultarProfessorTreinoWeb(
            page: page, size: size, filters: jsonFiltros, empresaId: _controladorCliente.mUsuarioLogado!.codEmpresa ?? 1, cod: _controladorCliente.mUsuarioLogado!.codEmpresa ?? 1)
        .then((value) {
      professorTreinoWeb.clear();
      for (final professor in value) {
        if (professor.nome == 'Pacto IA') {
          // Verifica se o nome do professor é "Pacto IA" , Se for, modifica o nome para "Sem professor"
          professor.nome = localizedString('no_trainer');
        }
        professorTreinoWeb.add(professor);
      }
      mStatusProfessorWeb = professorTreinoWeb.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      sucesso?.call();
    }).catchError((onError) {
      mStatusProfessorWeb = ServiceStatus.Error;
      erro?.call(onError);
    });
  }

//---------------------------------------- Serviço cadastro Professor Web ----------------------------------

  @observable
  ServiceStatus mStatusCadastroProfessorWeb = ServiceStatus.Waiting;

  @observable
  List<CadastroProfessorTreinoWeb> cadastroProfessorTreinoWeb = [];

  void cadastrarProfessorTreinoWeb({Function()? carregando, Function()? sucesso, Function(dynamic)? erro, required num codColaborador}) {
    statusCardPerfilAluno = ServiceStatus.Waiting;
    statusConsultarAlunos = ServiceStatus.Waiting;
    Map<String, dynamic> body = {
      'professorId': codColaborador,
    };
    mStatusCadastroProfessorWeb = ServiceStatus.Waiting;
    _controladorProfessorWeb
        .cadastrarProfessorTreinoWeb(
      idAluno: alunoExibir!.id!,
      empresaId: _controladorCliente.mUsuarioLogado!.codEmpresa ?? 1,
      body: body,
    )
        .then((value) {
      alunoExibir?.professor ??= Professor();

      alunoExibir?.professor?.nome = professorTreinoWeb.firstWhere((element) => element.codigoColaborador == codColaborador).nome!;
      alunoExibir?.professor?.id = professorTreinoWeb.firstWhere((element) => element.codigoColaborador == codColaborador).id!;

      statusCardPerfilAluno = ServiceStatus.Done;
      statusConsultarAlunos = ServiceStatus.Done;
      cadastroProfessorTreinoWeb.clear();
      cadastroProfessorTreinoWeb.add(value);
      mStatusCadastroProfessorWeb = cadastroProfessorTreinoWeb.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      sucesso?.call();
    }).catchError((onError) {
      statusCardPerfilAluno = ServiceStatus.Done;
      statusConsultarAlunos = ServiceStatus.Done;
      mStatusCadastroProfessorWeb = ServiceStatus.Error;
      erro?.call(onError);
    });
  }
}
