// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorWod.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorWod on _ControladorWodBase, Store {
  late final _$filtroSelecionadoAtividadesCrossAtom = Atom(
      name: '_ControladorWodBase.filtroSelecionadoAtividadesCross',
      context: context);

  @override
  TipoFiltroPR get filtroSelecionadoAtividadesCross {
    _$filtroSelecionadoAtividadesCrossAtom.reportRead();
    return super.filtroSelecionadoAtividadesCross;
  }

  @override
  set filtroSelecionadoAtividadesCross(TipoFiltroPR value) {
    _$filtroSelecionadoAtividadesCrossAtom
        .reportWrite(value, super.filtroSelecionadoAtividadesCross, () {
      super.filtroSelecionadoAtividadesCross = value;
    });
  }

  late final _$estaPesquisandoAtom =
      Atom(name: '_ControladorWodBase.estaPesquisando', context: context);

  @override
  bool get estaPesquisando {
    _$estaPesquisandoAtom.reportRead();
    return super.estaPesquisando;
  }

  @override
  set estaPesquisando(bool value) {
    _$estaPesquisandoAtom.reportWrite(value, super.estaPesquisando, () {
      super.estaPesquisando = value;
    });
  }

  late final _$estaNaTelaListaWodsAtom =
      Atom(name: '_ControladorWodBase.estaNaTelaListaWods', context: context);

  @override
  bool get estaNaTelaListaWods {
    _$estaNaTelaListaWodsAtom.reportRead();
    return super.estaNaTelaListaWods;
  }

  @override
  set estaNaTelaListaWods(bool value) {
    _$estaNaTelaListaWodsAtom.reportWrite(value, super.estaNaTelaListaWods, () {
      super.estaNaTelaListaWods = value;
    });
  }

  late final _$statusWodsPorDiaAtom =
      Atom(name: '_ControladorWodBase.statusWodsPorDia', context: context);

  @override
  ServiceStatus get statusWodsPorDia {
    _$statusWodsPorDiaAtom.reportRead();
    return super.statusWodsPorDia;
  }

  @override
  set statusWodsPorDia(ServiceStatus value) {
    _$statusWodsPorDiaAtom.reportWrite(value, super.statusWodsPorDia, () {
      super.statusWodsPorDia = value;
    });
  }

  late final _$statusConsultaPRAtom =
      Atom(name: '_ControladorWodBase.statusConsultaPR', context: context);

  @override
  ServiceStatus get statusConsultaPR {
    _$statusConsultaPRAtom.reportRead();
    return super.statusConsultaPR;
  }

  @override
  set statusConsultaPR(ServiceStatus value) {
    _$statusConsultaPRAtom.reportWrite(value, super.statusConsultaPR, () {
      super.statusConsultaPR = value;
    });
  }

  late final _$statusCarregamentoWodsAtom = Atom(
      name: '_ControladorWodBase.statusCarregamentoWods', context: context);

  @override
  ServiceStatus get statusCarregamentoWods {
    _$statusCarregamentoWodsAtom.reportRead();
    return super.statusCarregamentoWods;
  }

  @override
  set statusCarregamentoWods(ServiceStatus value) {
    _$statusCarregamentoWodsAtom
        .reportWrite(value, super.statusCarregamentoWods, () {
      super.statusCarregamentoWods = value;
    });
  }

  late final _$statusDetalhesDaAtividadeCrossAtom = Atom(
      name: '_ControladorWodBase.statusDetalhesDaAtividadeCross',
      context: context);

  @override
  ServiceStatus get statusDetalhesDaAtividadeCross {
    _$statusDetalhesDaAtividadeCrossAtom.reportRead();
    return super.statusDetalhesDaAtividadeCross;
  }

  @override
  set statusDetalhesDaAtividadeCross(ServiceStatus value) {
    _$statusDetalhesDaAtividadeCrossAtom
        .reportWrite(value, super.statusDetalhesDaAtividadeCross, () {
      super.statusDetalhesDaAtividadeCross = value;
    });
  }

  late final _$statusCarregamentoAtividadeCrossAtom = Atom(
      name: '_ControladorWodBase.statusCarregamentoAtividadeCross',
      context: context);

  @override
  ServiceStatus get statusCarregamentoAtividadeCross {
    _$statusCarregamentoAtividadeCrossAtom.reportRead();
    return super.statusCarregamentoAtividadeCross;
  }

  @override
  set statusCarregamentoAtividadeCross(ServiceStatus value) {
    _$statusCarregamentoAtividadeCrossAtom
        .reportWrite(value, super.statusCarregamentoAtividadeCross, () {
      super.statusCarregamentoAtividadeCross = value;
    });
  }

  late final _$statusDetalheAtividadeCrossAtom = Atom(
      name: '_ControladorWodBase.statusDetalheAtividadeCross',
      context: context);

  @override
  ServiceStatus get statusDetalheAtividadeCross {
    _$statusDetalheAtividadeCrossAtom.reportRead();
    return super.statusDetalheAtividadeCross;
  }

  @override
  set statusDetalheAtividadeCross(ServiceStatus value) {
    _$statusDetalheAtividadeCrossAtom
        .reportWrite(value, super.statusDetalheAtividadeCross, () {
      super.statusDetalheAtividadeCross = value;
    });
  }

  late final _$statusCarregamentoPrAtom =
      Atom(name: '_ControladorWodBase.statusCarregamentoPr', context: context);

  @override
  ServiceStatus get statusCarregamentoPr {
    _$statusCarregamentoPrAtom.reportRead();
    return super.statusCarregamentoPr;
  }

  @override
  set statusCarregamentoPr(ServiceStatus value) {
    _$statusCarregamentoPrAtom.reportWrite(value, super.statusCarregamentoPr,
        () {
      super.statusCarregamentoPr = value;
    });
  }

  late final _$statusCarregamentoRankingAtom = Atom(
      name: '_ControladorWodBase.statusCarregamentoRanking', context: context);

  @override
  ServiceStatus get statusCarregamentoRanking {
    _$statusCarregamentoRankingAtom.reportRead();
    return super.statusCarregamentoRanking;
  }

  @override
  set statusCarregamentoRanking(ServiceStatus value) {
    _$statusCarregamentoRankingAtom
        .reportWrite(value, super.statusCarregamentoRanking, () {
      super.statusCarregamentoRanking = value;
    });
  }

  late final _$mTempoIniciadoAtom =
      Atom(name: '_ControladorWodBase.mTempoIniciado', context: context);

  @override
  int get mTempoIniciado {
    _$mTempoIniciadoAtom.reportRead();
    return super.mTempoIniciado;
  }

  @override
  set mTempoIniciado(int value) {
    _$mTempoIniciadoAtom.reportWrite(value, super.mTempoIniciado, () {
      super.mTempoIniciado = value;
    });
  }

  late final _$mTempoDecorridoAtom =
      Atom(name: '_ControladorWodBase.mTempoDecorrido', context: context);

  @override
  String get mTempoDecorrido {
    _$mTempoDecorridoAtom.reportRead();
    return super.mTempoDecorrido;
  }

  @override
  set mTempoDecorrido(String value) {
    _$mTempoDecorridoAtom.reportWrite(value, super.mTempoDecorrido, () {
      super.mTempoDecorrido = value;
    });
  }

  late final _$isLibraAtom =
      Atom(name: '_ControladorWodBase.isLibra', context: context);

  @override
  bool get isLibra {
    _$isLibraAtom.reportRead();
    return super.isLibra;
  }

  @override
  set isLibra(bool value) {
    _$isLibraAtom.reportWrite(value, super.isLibra, () {
      super.isLibra = value;
    });
  }

  late final _$listaPersonalRecordsAtom =
      Atom(name: '_ControladorWodBase.listaPersonalRecords', context: context);

  @override
  List<PersonalRecords> get listaPersonalRecords {
    _$listaPersonalRecordsAtom.reportRead();
    return super.listaPersonalRecords;
  }

  @override
  set listaPersonalRecords(List<PersonalRecords> value) {
    _$listaPersonalRecordsAtom.reportWrite(value, super.listaPersonalRecords,
        () {
      super.listaPersonalRecords = value;
    });
  }

  late final _$prAtualUsuarioAtom =
      Atom(name: '_ControladorWodBase.prAtualUsuario', context: context);

  @override
  String? get prAtualUsuario {
    _$prAtualUsuarioAtom.reportRead();
    return super.prAtualUsuario;
  }

  @override
  set prAtualUsuario(String? value) {
    _$prAtualUsuarioAtom.reportWrite(value, super.prAtualUsuario, () {
      super.prAtualUsuario = value;
    });
  }

  late final _$isMigrarAtom =
      Atom(name: '_ControladorWodBase.isMigrar', context: context);

  @override
  bool get isMigrar {
    _$isMigrarAtom.reportRead();
    return super.isMigrar;
  }

  @override
  set isMigrar(bool value) {
    _$isMigrarAtom.reportWrite(value, super.isMigrar, () {
      super.isMigrar = value;
    });
  }

  late final _$wodSelecionadoAtom =
      Atom(name: '_ControladorWodBase.wodSelecionado', context: context);

  @override
  int get wodSelecionado {
    _$wodSelecionadoAtom.reportRead();
    return super.wodSelecionado;
  }

  @override
  set wodSelecionado(int value) {
    _$wodSelecionadoAtom.reportWrite(value, super.wodSelecionado, () {
      super.wodSelecionado = value;
    });
  }

  late final _$wodDetalhadoAtom =
      Atom(name: '_ControladorWodBase.wodDetalhado', context: context);

  @override
  WorkoutOfDay? get wodDetalhado {
    _$wodDetalhadoAtom.reportRead();
    return super.wodDetalhado;
  }

  @override
  set wodDetalhado(WorkoutOfDay? value) {
    _$wodDetalhadoAtom.reportWrite(value, super.wodDetalhado, () {
      super.wodDetalhado = value;
    });
  }

  late final _$filtroGeneroRankingWodAtom = Atom(
      name: '_ControladorWodBase.filtroGeneroRankingWod', context: context);

  @override
  FiltroGenero get filtroGeneroRankingWod {
    _$filtroGeneroRankingWodAtom.reportRead();
    return super.filtroGeneroRankingWod;
  }

  @override
  set filtroGeneroRankingWod(FiltroGenero value) {
    _$filtroGeneroRankingWodAtom
        .reportWrite(value, super.filtroGeneroRankingWod, () {
      super.filtroGeneroRankingWod = value;
    });
  }

  late final _$atividadeCrossDetalhadaAtom = Atom(
      name: '_ControladorWodBase.atividadeCrossDetalhada', context: context);

  @override
  AtividadeWod? get atividadeCrossDetalhada {
    _$atividadeCrossDetalhadaAtom.reportRead();
    return super.atividadeCrossDetalhada;
  }

  @override
  set atividadeCrossDetalhada(AtividadeWod? value) {
    _$atividadeCrossDetalhadaAtom
        .reportWrite(value, super.atividadeCrossDetalhada, () {
      super.atividadeCrossDetalhada = value;
    });
  }

  late final _$usuarioJaAvaliouWodAtom =
      Atom(name: '_ControladorWodBase.usuarioJaAvaliouWod', context: context);

  @override
  bool get usuarioJaAvaliouWod {
    _$usuarioJaAvaliouWodAtom.reportRead();
    return super.usuarioJaAvaliouWod;
  }

  @override
  set usuarioJaAvaliouWod(bool value) {
    _$usuarioJaAvaliouWodAtom.reportWrite(value, super.usuarioJaAvaliouWod, () {
      super.usuarioJaAvaliouWod = value;
    });
  }

  late final _$listaNivelWodAtom =
      Atom(name: '_ControladorWodBase.listaNivelWod', context: context);

  @override
  List<WodNiveis> get listaNivelWod {
    _$listaNivelWodAtom.reportRead();
    return super.listaNivelWod;
  }

  @override
  set listaNivelWod(List<WodNiveis> value) {
    _$listaNivelWodAtom.reportWrite(value, super.listaNivelWod, () {
      super.listaNivelWod = value;
    });
  }

  @override
  String toString() {
    return '''
filtroSelecionadoAtividadesCross: ${filtroSelecionadoAtividadesCross},
estaPesquisando: ${estaPesquisando},
estaNaTelaListaWods: ${estaNaTelaListaWods},
statusWodsPorDia: ${statusWodsPorDia},
statusConsultaPR: ${statusConsultaPR},
statusCarregamentoWods: ${statusCarregamentoWods},
statusDetalhesDaAtividadeCross: ${statusDetalhesDaAtividadeCross},
statusCarregamentoAtividadeCross: ${statusCarregamentoAtividadeCross},
statusDetalheAtividadeCross: ${statusDetalheAtividadeCross},
statusCarregamentoPr: ${statusCarregamentoPr},
statusCarregamentoRanking: ${statusCarregamentoRanking},
mTempoIniciado: ${mTempoIniciado},
mTempoDecorrido: ${mTempoDecorrido},
isLibra: ${isLibra},
listaPersonalRecords: ${listaPersonalRecords},
prAtualUsuario: ${prAtualUsuario},
isMigrar: ${isMigrar},
wodSelecionado: ${wodSelecionado},
wodDetalhado: ${wodDetalhado},
filtroGeneroRankingWod: ${filtroGeneroRankingWod},
atividadeCrossDetalhada: ${atividadeCrossDetalhada},
usuarioJaAvaliouWod: ${usuarioJaAvaliouWod},
listaNivelWod: ${listaNivelWod}
    ''';
  }
}
