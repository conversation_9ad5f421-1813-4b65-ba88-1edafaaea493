import 'package:app_treino/controlladores/ControladorNotificacoes.dart';
import 'package:app_treino/model/beberagua/RegistroBeberAgua.dart';
import 'package:app_treino/model/beberagua/SemanaAgua.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:app_treino/model/beberagua/DadosAguaUsuario.dart';
import 'package:app_treino/controlladores/ControladorDBExtend.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:sembast/sembast.dart';
part 'ControladorBeberAgua.g.dart';

class ControladorBeberAgua = _ControladorBeberAguaBase with _$ControladorBeberAgua;

abstract class _ControladorBeberAguaBase extends UtilDataBase with Store {
  @observable
  ServiceStatus mCarregouDadosUsuario = ServiceStatus.Waiting;

  @observable
  ServiceStatus mInseriuRegistroAgua = ServiceStatus.Waiting;

  @observable
  ServiceStatus mCarregouHistorico = ServiceStatus.Waiting;

  DadosAguaUsuario mDadosDoBeberAgua = DadosAguaUsuario();

  @observable
  ObservableList<SemanaAgua> mListaSemanas = ObservableList<SemanaAgua>();

  List<ObjetivoDoBeberAgua> get getObjetivosDoBeberAgua {
    return [
      ObjetivoDoBeberAgua(
          id: 0,
          icone: 'assets/images/icones/agua/garrafa.png',
          titulo: 'O primeiro Passo',
          subtitulo: 'Atinja a meta pela primeira vez',
          porcentagemConcluida: mListaSemanas.any((semana) => semana.atingiuMetaAlgumDia)
              ? 1.0
              : (mListaSemanas.isNotEmpty && mListaSemanas.any((s) => s.weekOfYear == UtilDataHora.getWeek(DateTime.now())))
                  ? mListaSemanas.firstWhere((s) => s.weekOfYear == UtilDataHora.getWeek(DateTime.now())).porcentagemDoDia()
                  : 0),
      ObjetivoDoBeberAgua(
          id: 1,
          icone: 'assets/images/icones/agua/joia.png',
          titulo: 'A Semana perfeita',
          subtitulo: 'Beba agua durante todos os dias',
          porcentagemConcluida: mListaSemanas.any((semana) => semana.percentThisWeek() >= 1.0)
              ? 1.0
              : mListaSemanas.any((semana) => semana.percentThisWeek() != 0.0)
                  ? mListaSemanas.firstWhere((semana) => semana.percentThisWeek() != 0.0).percentThisWeek()
                  : 0.0),
      ObjetivoDoBeberAgua(
          id: 2,
          icone: 'assets/images/icones/agua/clock.png',
          titulo: 'Desafio dos 30 dias',
          subtitulo: 'Mantenha o ritmo e atinja todas suas metas',
          porcentagemConcluida: temTrintaDiasDeBeberAgua > 30 ? 1.0 : temTrintaDiasDeBeberAgua / 30),
      ObjetivoDoBeberAgua(
          id: 3,
          icone: 'assets/images/icones/agua/premio.png',
          titulo: 'Mas que ótima jornada',
          subtitulo: 'Utilize o beber água por 365 dias',
          porcentagemConcluida: mListaSemanas.where((s) => s.percentThisWeek() >= 1.0).length >= 48 ? 1.0 : (mListaSemanas.where((s) => s.percentThisWeek() >= 1.0).length / 48)),
    ];
  }

  num get temTrintaDiasDeBeberAgua {
    var totalDias = 0;
    for (final semana in mListaSemanas) {
      totalDias += semana.quantidadeDiasAtingiuMeta as int;
    }
    return totalDias;
  }

  Future<void> carregarBeberAgua() async {
    mCarregouDadosUsuario = ServiceStatus.Waiting;
    //var dadosFromDb = await buscarNoBanco('dadosBeberAgua');
    // if (dadosFromDb != null) {
    //   mDadosDoBeberAgua = DadosAguaUsuario.fromJson(dadosFromDb);
    //   GetIt.I.get<ControladorNotificacoes>().setNotificacaoDeBomDia(mDadosDoBeberAgua.acordaAs, _mCliente.mUsuarioLogado!.nome!.split(' ').first);
    // }
    StoreRef<int, Map<String, dynamic>> storeSemanas = intMapStoreFactory.store('dadosBeberAguaWeeks');
    List<RecordSnapshot<int, Map<String, dynamic>>> recordsWeeks = await storeSemanas.find(getDb);
    mListaSemanas.clear();
    for (final bebeu in recordsWeeks) {
      mListaSemanas.add(SemanaAgua.fromJson(bebeu.value));
    }

    mCarregouDadosUsuario = ServiceStatus.Done;
  }

  Future<void> beberAguaNotificacoesLocais({cancelarTodasRestantesDeHoje = false}) async {
    await carregarBeberAgua();

    var mediaBeber = mDadosDoBeberAgua.metaDiaria! / 250;
    var totalMinutosEntreInicioFim = mDadosDoBeberAgua.receberLembreteFim!.difference(mDadosDoBeberAgua.receberLembreteInicio!).inMinutes;
    var mediaIntervalo = (totalMinutosEntreInicioFim > 0 ? totalMinutosEntreInicioFim : 1) / mediaBeber;
    try {
      mediaIntervalo = (mediaIntervalo <= 30 ? mediaIntervalo : mDadosDoBeberAgua.intervalo!.minute).toDouble();
    } catch (e) {
      
    }

    var inicio = mDadosDoBeberAgua.receberLembreteInicio;

    if (cancelarTodasRestantesDeHoje) {
      for (var i = 0; i < mediaBeber; i++) {
        inicio = inicio!.add(Duration(minutes: mediaIntervalo.toInt()));
        if (inicio.isBefore(mDadosDoBeberAgua.receberLembreteFim!)) GetIt.I.get<ControladorNotificacoes>().cancelarLembretePorId(num.parse('1$i') as int);
      }
    } else if (mDadosDoBeberAgua.lembretes ?? false) {
      for (var i = 0; i < mediaBeber; i++) {
        inicio = inicio!.add(Duration(minutes: mediaIntervalo.toInt()));
        //if (inicio.isBefore(mDadosDoBeberAgua.receberLembreteFim!)) 
        //GetIt.I.get<ControladorNotificacoes>().beberAguaLembrete(num.parse('1$i').toInt(), inicio);
      }
      //GetIt.I.get<ControladorNotificacoes>().beberAguaAgendado();
    }
  }

  Map<String, num> dadosGrafico({DateTime? dateTime, SemanaAgua? semanaAgua}) {
    dateTime = dateTime ?? DateTime.now();
    semanaAgua = semanaAgua ?? semanaAtual;
    Map<String, num> graficoDados = {};
    UtilDataHora.getTodosDiasDaSemana(time: dateTime).forEach((e) {
      graficoDados[UtilDataHora.getDiaAbreviacao(dateTime: e)] = semanaAgua!.porcentagemDoDia(dia: e);
    });
    return graficoDados;
  }

  void salvarDadosBeberAgua(DadosAguaUsuario dadosAguaUsuario, {Function()? carregando, Function()? sucesso, Function(String falha)? falha}) {
    carregando?.call();
    dadosAguaUsuario.intervalo = dadosAguaUsuario.intervalo ?? UtilDataHora.parseStringToDate('11/11/2020 00:30', minutos: true);
    dadosAguaUsuario.receberLembreteInicio = dadosAguaUsuario.receberLembreteInicio ?? dadosAguaUsuario.acordaAs;
    dadosAguaUsuario.receberLembreteFim = dadosAguaUsuario.receberLembreteFim ?? dadosAguaUsuario.acordaAs?.add(const Duration(hours: 12)) ?? null;
    if (dadosAguaUsuario.lembretes ?? false) {
      if (dadosAguaUsuario.intervalo == null || dadosAguaUsuario.receberLembreteInicio == null || dadosAguaUsuario.receberLembreteFim == null) {
        falha?.call('Para ativar os lembretes precisamos que informe os dados corretamente');
        return;
      }
    } else if (dadosAguaUsuario.nascimento == null) {
      falha?.call('Por-favor, inforome a data de nascimento');
      return;
    } else if (dadosAguaUsuario.peso == null) {
      falha?.call('Por-favor, inforome seu peso');
      return;
    }
    dadosAguaUsuario.metaDiaria = ((dadosAguaUsuario.peso ?? 0) * 35);
    inserirNoBanco('dadosBeberAgua', dadosAguaUsuario.toJson()).then((value) async {
      await GetIt.I.get<ControladorNotificacoes>().cancelarTodosLembretesBeberAgua();
      mDadosDoBeberAgua = dadosAguaUsuario;
      //if (dadosAguaUsuario.acordaAs != null) GetIt.I.get<ControladorNotificacoes>().setNotificacaoDeBomDia(dadosAguaUsuario.acordaAs, _mCliente.mUsuarioLogado!.nome!.split(' ').first);
      if (!(dadosAguaUsuario.lembretes ?? false)) {
        GetIt.I.get<ControladorNotificacoes>().cancelarTodosLembretesBeberAgua();
      } else {
        beberAguaNotificacoesLocais();
      }
      sucesso?.call();
    }).catchError((onError) {
      sucesso?.call();
    });
  }

  SemanaAgua get semanaAtual {
    var dataAtual = DateTime.now();
    var mesAno = UtilDataHora.getMesAno(dateTime: dataAtual);
    var semanaMes = UtilDataHora.getWeekOfMonth(dataAtual);
    var semanaAno = UtilDataHora.getWeek(dataAtual);
    if (mListaSemanas.isEmpty) {
      mListaSemanas.add(SemanaAgua(mesAno: mesAno, week: semanaMes as int?, weekOfYear: semanaAno));
      return mListaSemanas.first;
    } else if (mListaSemanas.any((semana) => semana.mesAno!.contains(mesAno) && semana.week == semanaMes)) {
      return mListaSemanas.firstWhere((semana) => semana.mesAno!.contains(mesAno) && semana.week == semanaMes);
    } else {
      mListaSemanas.add(SemanaAgua(mesAno: mesAno, week: semanaMes as int?, weekOfYear: semanaAno));
      return this.semanaAtual;
    }
  }

  Future<void> registrarBeberAgua(RegistroBeberAgua registroBeberAgua, Function() callSet) async {
    mInseriuRegistroAgua = ServiceStatus.Waiting;
    mListaSemanas[mListaSemanas.indexOf(semanaAtual)].addBebeu(registroBeberAgua);
    mListaSemanas[mListaSemanas.indexOf(semanaAtual)].dailyTarget = mDadosDoBeberAgua.metaDiaria;
    StoreRef<int, Map<String, dynamic>> storeSemanas = intMapStoreFactory.store('dadosBeberAguaWeeks');
    await getDb.transaction((txn) async {
      await storeSemanas.record(UtilDataHora.getSemanaMesAno(DateTime.now()).toInt()).put(txn, mListaSemanas[mListaSemanas.indexOf(semanaAtual)].toJson());
    }).then((value) {
      double porcentagem = mListaSemanas[mListaSemanas.indexOf(semanaAtual)].porcentagemDoDia(dia: DateTime.now());
      if (porcentagem >= 1.0) {
        beberAguaNotificacoesLocais(cancelarTodasRestantesDeHoje: true);
      }
      mInseriuRegistroAgua = ServiceStatus.Done;
      callSet.call();
    } as Future Function(Null));
  }

  Future<void> limparTudo() async {
    await deletarDoBanco('dadosBeberAgua'); 
    mCarregouDadosUsuario = ServiceStatus.Waiting;
    mInseriuRegistroAgua = ServiceStatus.Waiting;
    mCarregouHistorico = ServiceStatus.Waiting;
    mDadosDoBeberAgua = DadosAguaUsuario();
    GetIt.I.get<ControladorNotificacoes>().cancelarTodosLembretesBeberAgua();
  }
}
