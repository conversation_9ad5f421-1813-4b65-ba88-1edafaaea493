import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/model/personal/InfoIndicacoesPersonal.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:shared_preferences/shared_preferences.dart';
part 'ControladorIndicacao.g.dart';

class ControladorInicacao = _ControladorInicacaoBase with _$ControladorInicacao;

abstract class _ControladorInicacaoBase with Store {
  @observable
  bool? viuOnBoard;
  @observable
  bool carregouDados = false;

  final _mService = GetIt.I.get<PersonalService>();

  static const MethodChannel channel = MethodChannel('personalFit/controller');

  @observable
  InfoIndicacoesPersonal? mResultadoIndicacao;

  void setViuOnBoard() {
    SharedPreferences.getInstance().then((value) {
      value.setBool('onBoardIndicacao', true);
      viuOnBoard = true;
      carregouDados = true;
    });
  }

  Future<void> compartilharLinkDeIndicacao(String plataforma) async {
    return channel.invokeMethod('shareInstagram', <String, dynamic>{'linkDe': plataforma, 'linkEnviar': 'Olá venha fazer parte do ${F.title}, acesse o link: ${mResultadoIndicacao!.linkCompleto}'});
  }

  void verificarSeViuOnBoard() {
    _mService.consultarInfoIndicacoes().then((value) {
      mResultadoIndicacao = value;
      SharedPreferences.getInstance().then((value) {
        if (value.getBool('onBoardIndicacao') != null) {
          viuOnBoard = value.getBool('onBoardIndicacao');
        } else {
          viuOnBoard = false;
        }
        carregouDados = true;
      });
    }).catchError((onError) {
      
    });
  }

  limparTudo() {
    mResultadoIndicacao = null;
    viuOnBoard = null;
    carregouDados = false;
  }
}
