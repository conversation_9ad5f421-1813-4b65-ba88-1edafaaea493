import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:app_treino/ServiceProvider/TreinoService.dart';
import 'package:app_treino/ServiceProvider/authServices/ClienteAppService.dart';
import 'package:app_treino/ServiceProvider/authServices/ClienteAvaliacaoFisica.dart';
import 'package:app_treino/controlladores/ControladorAgendamento.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoDeTreino.dart';
import 'package:app_treino/controlladores/ControladorUsuarioApp.dart';
import 'package:app_treino/model/avalicaoFisica/AvaliacaoFisicaRecente.dart';
import 'package:app_treino/model/avalicaoFisica/FotosAvaliacaoFisica.dart';
import 'package:app_treino/model/avalicaoFisica/GrupoMuscular.dart';
import 'package:app_treino/model/avalicaoFisica/ManterAvaliacaoFisica.dart' as avBio;
import 'package:app_treino/model/avalicaoFisica/ManterAvaliacaoFisica.dart';
import 'package:app_treino/model/avalicaoFisica/ParQ.dart';
import 'package:app_treino/model/personal/Aluno.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/avalicaoFisica/AvaliacaoFisica.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/screens/novaAvaliacaoFisica/LineChartFlChart.dart';
import 'package:app_treino/screens/novaAvaliacaoFisica/TelaNovaAvaliacaoFisica.dart';
import 'package:app_treino/screens/prescricaoDeTreino/widgets/CardAvaliacaoFisica.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get_it/get_it.dart';
import 'package:health_devices/health_devices.dart';
import 'package:mobx/mobx.dart';

import 'package:app_treino/screens/prescricaoDeTreino/widgets/GraficoFrequenciaAluno.dart';

part 'ControladorAvaliacaoFisica.g.dart';

class ControladorAvaliacaoFisica = _ControladorAvaliacaoFisicaBase with _$ControladorAvaliacaoFisica;

abstract class _ControladorAvaliacaoFisicaBase with Store {
  @observable
  ServiceStatus mConsultaAvaliacoes = ServiceStatus.Waiting;
  
  @observable
  ServiceStatus mConsultaAgendaAvaliacao = ServiceStatus.Waiting;

  @observable
  ObservableList<Dobra> listaDobras = ObservableList<Dobra>();

  @observable
  List<NewComposicaoCorporal> mListaCorporal = [];

  @observable
  ObservableList<DobrasAgrupada> mListaDobrasAgrupadas = ObservableList<DobrasAgrupada>();

  @observable
  bool isDireita = false;

  @observable
  int indexFotoPostural = 0;
  @observable
  FotoUsuarioAvaliacao fotoPosturalSelecionada = FotoUsuarioAvaliacao();

  var mService = GetIt.I.get<TreinoService>();

  var mCliente = GetIt.I.get<ControladorCliente>();

  var mAgendamento = GetIt.I.get<ControladorAgendamento>();

  var mPrescricaoDeTreino = GetIt.I.get<ControladorPrescricaoDeTreino>();

  var mClienteAvaliacaoFisicaService = GetIt.I.get<ClienteAvaliacaoFisicaService>();

  var controladorUsuarioApp = GetIt.I.get<ControladorUsuarioApp>();

  @observable
  List<AvaliacaoFisica> mAvaliacoesFisicas = [];

  List<DobrasGrafico> mListaDobras = [];

  EnumLadoPerimetria mDadosPerimetria = EnumLadoPerimetria.DIREITO;

  Map<AvaliadorFisico, List<EventoHorario>> mProfessoresEHorario = {};

  @observable
  Bioimpedancia mBioimpedancia = Bioimpedancia();

  @observable
  FiltroFrequencia tipoFiltroTempo = FiltroFrequencia.SEMANA;

  @observable
  int paginaFotoComparacao = 0;

  @observable
  bool mostrarMask = true;
  @observable
  DadosBasicoAlunoAvaliacao dadosBasicos = DadosBasicoAlunoAvaliacao();

  String get nomeDaTela {
    switch (paginaFotoComparacao) {
      case 0:
        return 'Frente';
      case 1:
        return 'Lado direito';
      case 2:
        return 'Lado esquerdo';
      case 3:
        return 'Costas';
      default:
        return '';
    }
  }

  void mostrarMaskFoto(bool value) {
    if (value) {
      mostrarMask = value;
    } else {
      mostrarMask = value;
    }
  }

  AvaliacaoFisica? get avaliacaoFisicaAtual {
    if (mAvaliacoesFisicas.isNotEmpty) {
      return mAvaliacoesFisicas.last;
    } else {
      return null;
    }
  }

  void selecionouDirecao(bool? selecionado) {
    isDireita = selecionado ?? false;
  }

  @observable
  String? fotobase;

  Future<void> salvarFotoBase({required dynamic image, required Function()? carregando, required Function(String? foto)? sucesso, required Function(String? falha)? falha}) async {
    fotobase = '';
    carregando?.call();
    var imagem = await FlutterImageCompress.compressWithList(
      File(image.path).readAsBytesSync(),
      minWidth: 320,
      minHeight: 654,
      quality: 20,
    );
    GetIt.I.get<ClienteAppService>().uploadImagem(base64Encode(imagem)).then((value) {
      fotobase = value;
      sucesso?.call(value);
    }).catchError((onError) {
      falha?.call(onError);
    });
  }

  List<PerimetriaGrafico> get exibirGraficoPerimetria {
    List<PerimetriaGrafico> retorno = [];
    for (final avaliacao in mAvaliacoesFisicas) {
      for (final dobra in avaliacao.perimetria!) {
        if (dobra.nome!.contains('dir')) {
          retorno.add(PerimetriaGrafico(dataAvaliacao: DateTime.fromMillisecondsSinceEpoch(avaliacao.dataAtualLong!.toInt()), dobras: dobra, enumerador: EnumLadoPerimetria.DIREITO));
        } else if (dobra.nome!.contains('esq')) {
          retorno.add(PerimetriaGrafico(dataAvaliacao: DateTime.fromMillisecondsSinceEpoch(avaliacao.dataAtualLong!.toInt()), dobras: dobra, enumerador: EnumLadoPerimetria.ESQUERDO));
        } else {
          retorno.add(PerimetriaGrafico(dataAvaliacao: DateTime.fromMillisecondsSinceEpoch(avaliacao.dataAtualLong!.toInt()), dobras: dobra, enumerador: EnumLadoPerimetria.OUTROS));
        }
      }
    }
    return retorno;
  }

  Map<String, Map<String?, List<DadoGraficoLinha>>> get dadosExibirGraficoPerimetria {
    Map<String, Map<String?, List<DadoGraficoLinha>>> retorno = {};
    retorno[localizedString('right')] = {};
    retorno[localizedString('left')] = {};
    retorno[localizedString('others')] = {};
    for (final avaliacao in mAvaliacoesFisicas) {
      for (final dobra in avaliacao.perimetria!) {
        if (dobra.nome!.contains('dir')) {
          if (retorno[localizedString('right')]![dobra.nome] == null) {
            retorno[localizedString('right')]![dobra.nome] = [];
          }
          retorno[localizedString('right')]![dobra.nome]!.add(DadoGraficoLinha(legenda: UtilDataHora.parseNunToDate(avaliacao.dataAtualLong), valor: dobra.valor));
        } else if (dobra.nome!.contains('esq')) {
          if (retorno[localizedString('left')]![dobra.nome] == null) {
            retorno[localizedString('left')]![dobra.nome] = [];
          }
          retorno[localizedString('left')]![dobra.nome]!.add(DadoGraficoLinha(legenda: UtilDataHora.parseNunToDate(avaliacao.dataAtualLong), valor: dobra.valor));
        } else {
          if (retorno[localizedString('others')]![dobra.nome] == null) {
            retorno[localizedString('others')]![dobra.nome] = [];
          }
          retorno[localizedString('others')]![dobra.nome]!.add(DadoGraficoLinha(legenda: UtilDataHora.parseNunToDate(avaliacao.dataAtualLong), valor: dobra.valor));
        }
      }
    }
    return retorno;
  }

  List<DobrasGrafico> get dadosExibirGraficoDobrasCutaneas {
    mListaDobras.clear();
    for (final avaliacao in mAvaliacoesFisicas) {
      for (final dobra in avaliacao.dobras!) {
        mListaDobras.add(DobrasGrafico(dataAvaliacao: DateTime.fromMillisecondsSinceEpoch(avaliacao.dataAtualLong!.toInt()), dobras: dobra));
      }
      mListaDobras.sort((a, b) => a.dobras!.nome!.compareTo(b.dobras!.nome.toString()));
    }
    return mListaDobras;
  }

  Map<String?, List<DadoGraficoLinha>> get dadosGraficoDobrasCutaneas {
    Map<String?, List<DadoGraficoLinha>> retorno = {};
    for (final avaliacao in mAvaliacoesFisicas) {
      for (final dobra in avaliacao.dobras!) {
        if (retorno[dobra.nome] == null) retorno[dobra.nome] = [];
        retorno[dobra.nome]!.add(DadoGraficoLinha(legenda: UtilDataHora.parseNunToDate(avaliacao.dataAtualLong), valor: dobra.valor));
      }
    }
    return retorno;
  }

  void agruparDobras() {
    mListaDobrasAgrupadas.clear();
    for (var j = 0; j < mAvaliacoesFisicas.length;) {
      var dobrasAgrupadas = groupBy(mListaDobras, (DobrasGrafico item) => item.dobras!.nome);
      return dobrasAgrupadas.forEach((key, value) {
        mListaDobrasAgrupadas.add(DobrasAgrupada(key: key, dobras: value.first.dobras));
      });
    }
  }

  bool compararResultadoPerimetriaEDobras(num primeiroValor, num valorAtual) {
    if (primeiroValor <= valorAtual) {
      return true;
    } else {
      return false;
    }
  }

  num get obterPesoIdeal {
    var sexo = mCliente.mDadosDoUsuario?.sexo ?? 'M';
    if (sexo == 'M') {
      return (22 * pow((avaliacaoFisicaAtual!.altura ?? 0.0), 2)).ceil();
    } else {
      return (21 * pow((avaliacaoFisicaAtual!.altura ?? 0.0), 2)).ceil();
    }
  }

  String get obterIdadeCorporal {
    return mPrescricaoDeTreino.avaliacaoRecenteAlunoExibir?.dobras?.bioimpedancia?.entries?.last?.value?.toString() ?? '0';
  }

  void bioimpedancia() {
    var dadosBio = mPrescricaoDeTreino.avaliacaoRecenteAlunoExibir?.dobras!.bioimpedancia.entries.toList();
    mBioimpedancia.imc = dadosBio[0].value ?? 0.0;
    mBioimpedancia.massaGorda = dadosBio[1].value ?? 0.0;
    mBioimpedancia.percentMassaMagra = dadosBio[2].value ?? 0.0;
    mBioimpedancia.residuos = dadosBio[3].value ?? 0.0;
    mBioimpedancia.gorduraIdeal = dadosBio[4].value ?? 0.0;
    mBioimpedancia.reatancia = dadosBio[5].value ?? 0.0;
    mBioimpedancia.necFisica = dadosBio[6].value ?? 0.0;
    mBioimpedancia.necCalorica = dadosBio[7].value ?? 0.0;
    mBioimpedancia.gorduraVisceral = dadosBio[8].value ?? 0.0;
    mBioimpedancia.percentMassaGorda = dadosBio[9].value ?? 0.0;
    mBioimpedancia.massaMagra = dadosBio[10].value ?? 0.0;
    mBioimpedancia.ossos = dadosBio[11].value ?? 0.0;
    mBioimpedancia.musculos = dadosBio[12].value ?? 0.0;
    mBioimpedancia.resistencia = dadosBio[13].value ?? 0.0;
    mBioimpedancia.percentAgua = dadosBio[14].value ?? 0.0;
    mBioimpedancia.tmb = dadosBio[15].value ?? 0.0;
    mBioimpedancia.idadeMetabolica = dadosBio[16].value ?? 0.0;
  }

  double get fatoAtividade {
    switch (controladorUsuarioApp.dadosUsuario!.atividadeFisica) {
      case 0:
        return 1.2;
      case 1:
        return 1.375;
      case 2:
        return 1.375;
      case 3:
        return 1.55;
      case 4:
        return 1.55;
      case 5:
        return 1.725;
      case 6:
        return 1.725;
      case 7:
        return 1.9;
      default:
        return 1.9;
    }
  }

  num get obterTMB {
    int idade = mCliente.mUsuarioLogado?.idade ?? 0;
    var sexo = mCliente.mDadosDoUsuario?.sexo ?? 'M';
    double altura = (avaliacaoFisicaAtual!.altura! * 100);

    if (sexo == 'M') {
      return (fatoAtividade * (66 + (13.75 * avaliacaoFisicaAtual!.peso!) + (5 * altura) - (6.8 * idade)));
    } else {
      return (fatoAtividade * (665 + ((9.6 * avaliacaoFisicaAtual!.peso!) + (1.8 * altura) - (4.7 * idade))));
    }
  }
  num  obterTMBPorDadosBasicos() {
    int idade = mCliente.mUsuarioLogado?.idade ?? 0;
    var sexo = mCliente.mDadosDoUsuario?.sexo ?? 'M';
    double altura = ((dadosBasicos.altura ?? 1)).toDouble();
    if (sexo == 'M') {
      return (fatoAtividade * (66 + (13.75 * (dadosBasicos.peso?? 1)) + (5 * altura) - (6.8 * idade)));
    } else {
      return (fatoAtividade * (665 + ((9.6 * (dadosBasicos.peso?? 1)) + (1.8 * altura) - (4.7 * idade))));
    }
  }

  num get obterValorVisceral {
    if (mBioimpedancia.gorduraVisceral != null) {
      return (mBioimpedancia.gorduraVisceral! / 100);
    } else {
      return 0;
    }
  }

  String get nivelGorduraVisceral {
    var sexo = mCliente.mDadosDoUsuario?.sexo ?? 'M';
    if (sexo == 'M') {
      if (obterValorVisceral <= 16) {
        return 'Baixo';
      } else if (obterValorVisceral >= 21) {
        return 'Alto';
      } else {
        return 'Normal';
      }
    } else if (obterValorVisceral <= 20) {
      return 'Baixo';
    } else if (obterValorVisceral >= 25) {
      return 'Alto';
    } else {
      return 'Normal';
    }
  }

  String get textoGorduraVisceral {
    switch (nivelGorduraVisceral) {
      case 'Baixo':
        return 'visceral_baixo';
      case 'Normal':
        return 'visceral_normal';
      case 'Alto':
        return 'visceral_alto';
      default:
        return 'visceral_baixo';
    }
  }

  String get textoPesoIdeal {
    return localizedString('texto_peso_ideal',
        args: [obterPesoIdeal.toString().replaceAll('.', ','), (obterPesoIdeal - 10).toString().replaceAll('.', ','), (obterPesoIdeal + 10).toString().replaceAll('.', ',')]);
  }

  Color get obterCorImc {
    var imc = avaliacaoFisicaAtual!.imc!;
    return imc < 16
        ? Colors.red
        : imc < 17
            ? Colors.red
            : imc < 18.5
                ? Colors.yellow
                : imc < 25
                    ? Colors.green
                    : imc < 30
                        ? Colors.yellow
                        : imc < 35
                            ? Colors.red
                            : imc < 40
                                ? Colors.red
                                : Colors.red;
  }

  String get obterNivelIMC {
    int imc = (avaliacaoFisicaAtual!.imc ?? 0.0).toInt();
    return imc < 16
        ? 'Baixo'
        : imc < 17
            ? 'Baixo'
            : imc < 18.5
                ? 'Baixo'
                : imc < 25
                    ? 'Normal'
                    : imc < 30
                        ? 'Elevado'
                        : imc < 35
                            ? 'Elevado'
                            : imc >= 35
                                ? 'Muito Elevado'
                                : 'Muito Elevado';
  }

  String get textoSeuIMC {
    switch (obterNivelIMC) {
      case 'Baixo':
        return 'imc_baixo';
      case 'Normal':
        return 'imc_normal';
      case 'Elevado':
        return 'imc_elevado';
      case 'Muito Elevado':
        return 'imc_muitoElevado';
      default:
        return 'imc_muitoElevado';
    }
  }

  String get obterNivelMuscular {
    var musculo = (avaliacaoFisicaAtual?.bioimpedancia ?? false) || isBioimpedanciaAvaliacaoRecente(mPrescricaoDeTreino.avaliacaoRecenteAlunoExibir)
        ? mPrescricaoDeTreino.avaliacaoRecenteAlunoExibir?.alunoBI?.composicaoPorcentualMassaMagra ?? 0
        : mPrescricaoDeTreino.avaliacaoRecenteAlunoExibir?.alunoBI?.composicaoPorcentualMusculos ?? 0;
    var idade = mCliente.mUsuarioLogado?.idade ?? 0;
    var sexo = mCliente.mDadosDoUsuario?.sexo ?? 'M';
    String nivel = 'Baixo';

    if (sexo == 'M') {
      if (idade <= 35) {
        return musculo < 40
            ? nivel = 'Baixo'
            : musculo <= 44
                ? nivel = 'Normal'
                : musculo <= 49
                    ? nivel = 'Bom'
                    : musculo >= 50
                        ? 'Muito bom'
                        : nivel = 'Baixo';
      } else if (idade <= 55) {
        return musculo < 36
            ? nivel = 'Baixo'
            : musculo <= 40
                ? nivel = 'Normal'
                : musculo <= 45
                    ? nivel = 'Bom'
                    : musculo >= 46
                        ? 'Muito bom'
                        : nivel = 'Baixo';
      } else if (idade <= 75) {
        return musculo < 25
            ? nivel = 'Baixo'
            : musculo <= 32
                ? nivel = 'Normal'
                : musculo <= 38
                    ? nivel = 'Bom'
                    : musculo >= 39
                        ? 'Muito bom'
                        : nivel = 'Baixo';
      } else if (idade >= 76) {
        return musculo < 20
            ? nivel = 'Baixo'
            : musculo <= 31
                ? nivel = 'Normal'
                : musculo <= 36
                    ? nivel = 'Bom'
                    : musculo >= 37
                        ? 'Muito bom'
                        : nivel = 'Baixo';
      }
    } else {
      if (idade <= 29) {
        return musculo < 31
            ? nivel = 'Baixo'
            : musculo <= 33
                ? nivel = 'Normal'
                : musculo <= 37
                    ? nivel = 'Bom'
                    : musculo >= 38
                        ? 'Muito bom'
                        : nivel = 'Baixo';
      } else if (idade <= 39) {
        return musculo < 29
            ? nivel = 'Baixo'
            : musculo <= 31
                ? nivel = 'Normal'
                : musculo <= 34
                    ? nivel = 'Bom'
                    : musculo >= 35
                        ? 'Muito bom'
                        : nivel = 'Baixo';
      } else if (idade <= 49) {
        return musculo < 27
            ? nivel = 'Baixo'
            : musculo <= 30
                ? nivel = 'Normal'
                : musculo <= 33
                    ? nivel = 'Bom'
                    : musculo >= 34
                        ? 'Muito bom'
                        : nivel = 'Baixo';
      } else if (idade >= 50) {
        return musculo < 26
            ? nivel = 'Baixo'
            : musculo <= 28
                ? nivel = 'Normal'
                : musculo <= 31
                    ? nivel = 'Bom'
                    : musculo >= 32
                        ? 'Muito bom'
                        : nivel = 'Baixo';
      }
    }
    return nivel;
  }

  String get textoNivelMuscular {
    switch (obterNivelMuscular) {
      case 'Baixo':
        return (avaliacaoFisicaAtual?.bioimpedancia ?? false) || isBioimpedanciaAvaliacaoRecente(mPrescricaoDeTreino.avaliacaoRecenteAlunoExibir) ? 'massa_magra_baixo' : 'muscular_baixo';
      case 'Normal':
        return (avaliacaoFisicaAtual?.bioimpedancia ?? false) || isBioimpedanciaAvaliacaoRecente(mPrescricaoDeTreino.avaliacaoRecenteAlunoExibir) ? 'massa_magra_normal' : 'muscular_normal';
      case 'Bom':
        return (avaliacaoFisicaAtual?.bioimpedancia ?? false) || isBioimpedanciaAvaliacaoRecente(mPrescricaoDeTreino.avaliacaoRecenteAlunoExibir) ? 'massa_magra_normal' : 'muscular_normal';
      case 'Muito bom':
        return (avaliacaoFisicaAtual?.bioimpedancia ?? false) || isBioimpedanciaAvaliacaoRecente(mPrescricaoDeTreino.avaliacaoRecenteAlunoExibir) ? 'massa_magra_alto' : 'muscular_bom';
      default:
        return (avaliacaoFisicaAtual?.bioimpedancia ?? false) || isBioimpedanciaAvaliacaoRecente(mPrescricaoDeTreino.avaliacaoRecenteAlunoExibir) ? 'massa_magra_alto' : 'muscular_bom';
    }
  }

  ValoresReferenciaCorporal get niveisReferenteMusculo {
    var idade = mCliente.mUsuarioLogado?.idade ?? 0;
    var sexo = mCliente.mDadosDoUsuario?.sexo ?? 'M';
    ValoresReferenciaCorporal aux = ValoresReferenciaCorporal(ruim: 0, bom: 0, muitoBom: 0);
    if (sexo == 'M') {
      if (idade <= 35) {
        return aux = ValoresReferenciaCorporal(ruim: 40, bom: 49, muitoBom: 50);
      } else if (idade <= 55) {
        return aux = ValoresReferenciaCorporal(ruim: 36, bom: 45, muitoBom: 46);
      } else if (idade <= 75) {
        return aux = ValoresReferenciaCorporal(ruim: 25, bom: 38, muitoBom: 39);
      } else if (idade >= 76) {
        return aux = ValoresReferenciaCorporal(ruim: 20, bom: 36, muitoBom: 37);
      }
    } else {
      if (idade <= 29) {
        return aux = ValoresReferenciaCorporal(ruim: 31, bom: 37, muitoBom: 38);
      } else if (idade <= 39) {
        return aux = ValoresReferenciaCorporal(ruim: 29, bom: 34, muitoBom: 35);
      } else if (idade <= 49) {
        return aux = ValoresReferenciaCorporal(ruim: 27, bom: 33, muitoBom: 34);
      } else if (idade >= 50) {
        return aux = ValoresReferenciaCorporal(ruim: 26, bom: 31, muitoBom: 32);
      }
    }
    return aux;
  }

  String get obterNivelGorduraCorporal {
    var gordura = mPrescricaoDeTreino.avaliacaoRecenteAlunoExibir?.alunoBI?.composicaoPorcentualGordura ?? 0;
    var idade = mCliente.mUsuarioLogado?.idade ?? 0;
    var sexo = mCliente.mDadosDoUsuario?.sexo ?? 'M';
    String nivel = 'Bom';

    if (sexo == 'M') {
      if (idade <= 29) {
        return gordura < 14
            ? nivel = 'Bom'
            : gordura <= 20
                ? nivel = 'Normal'
                : gordura <= 24
                    ? nivel = 'Alto'
                    : gordura >= 25
                        ? 'Muito alto'
                        : nivel = 'Baixo';
      } else if (idade <= 39) {
        return gordura < 15
            ? nivel = 'Bom'
            : gordura <= 21
                ? nivel = 'Normal'
                : gordura <= 25
                    ? nivel = 'Alto'
                    : gordura >= 26
                        ? 'Muito alto'
                        : nivel = 'Baixo';
      } else if (idade <= 49) {
        return gordura < 16
            ? nivel = 'Bom'
            : gordura <= 22
                ? nivel = 'Normal'
                : gordura <= 26
                    ? nivel = 'Alto'
                    : gordura >= 27
                        ? 'Muito alto'
                        : nivel = 'Baixo';
      } else if (idade >= 50) {
        return gordura < 17
            ? nivel = 'Bom'
            : gordura <= 23
                ? nivel = 'Normal'
                : gordura <= 27
                    ? nivel = 'Alto'
                    : gordura >= 28
                        ? 'Muito alto'
                        : nivel = 'Baixo';
      }
    } else {
      if (idade <= 29) {
        return gordura < 20
            ? nivel = 'Bom'
            : gordura <= 28
                ? nivel = 'Normal'
                : gordura <= 31
                    ? nivel = 'Alto'
                    : gordura >= 31
                        ? 'Muito alto'
                        : nivel = 'Baixo';
      } else if (idade <= 39) {
        return gordura < 21
            ? nivel = 'Bom'
            : gordura <= 29
                ? nivel = 'Normal'
                : gordura <= 32
                    ? nivel = 'Alto'
                    : gordura >= 32
                        ? 'Muito alto'
                        : nivel = 'Baixo';
      } else if (idade <= 49) {
        return gordura < 22
            ? nivel = 'Bom'
            : gordura <= 30
                ? nivel = 'Normal'
                : gordura <= 33
                    ? nivel = 'Alto'
                    : gordura >= 33
                        ? 'Muito alto'
                        : nivel = 'Baixo';
      } else if (idade >= 50) {
        return gordura < 23
            ? nivel = 'Bom'
            : gordura <= 31
                ? nivel = 'Normal'
                : gordura <= 34
                    ? nivel = 'Alto'
                    : gordura >= 34
                        ? 'Muito alto'
                        : nivel = 'Baixo';
      }
    }
    return nivel;
  }

  ValoresReferenciaCorporal get niveisReferenteGordura {
    var idade = mCliente.mUsuarioLogado?.idade ?? 0;
    var sexo = mCliente.mDadosDoUsuario?.sexo ?? 'M';
    ValoresReferenciaCorporal aux = ValoresReferenciaCorporal(ruim: 0, bom: 0, muitoBom: 0);
    if (sexo == 'M') {
      if (idade <= 29) {
        return aux = ValoresReferenciaCorporal(ruim: 14, bom: 20, muitoBom: 24);
      } else if (idade <= 39) {
        return aux = ValoresReferenciaCorporal(ruim: 15, bom: 21, muitoBom: 25);
      } else if (idade <= 49) {
        return aux = ValoresReferenciaCorporal(ruim: 16, bom: 22, muitoBom: 26);
      } else if (idade >= 50) {
        return aux = ValoresReferenciaCorporal(ruim: 17, bom: 23, muitoBom: 27);
      }
    } else {
      if (idade <= 29) {
        return aux = ValoresReferenciaCorporal(ruim: 20, bom: 28, muitoBom: 31);
      } else if (idade <= 39) {
        return aux = ValoresReferenciaCorporal(ruim: 21, bom: 29, muitoBom: 32);
      } else if (idade <= 49) {
        return aux = ValoresReferenciaCorporal(ruim: 22, bom: 30, muitoBom: 33);
      } else if (idade >= 50) {
        return aux = ValoresReferenciaCorporal(ruim: 23, bom: 32, muitoBom: 34);
      }
    }
    return aux;
  }

  String get textoNivelGorduraCorporal {
    switch (obterNivelGorduraCorporal) {
      case 'Bom':
        return 'gordura_bom';
      case 'Normal':
        return 'gordura_normal';
      case 'Alto':
        return 'gordura_normal';
      case 'Muito alto':
        return 'gordura_alto';
      default:
        return 'gordura_alto';
    }
  }

  String get nivelDeAgua {
    if ((mBioimpedancia.percentAgua ?? 0) < 50) {
      return 'Insuficiente';
    } else if ((mBioimpedancia.percentAgua ?? 0) <= 65) {
      return 'Normal';
    } else {
      return 'Bom';
    }
  }

  String get textoNivelAgua {
    switch (nivelDeAgua) {
      case 'Insuficiente':
        return 'nivelAgua_baixo';
      case 'Normal':
        return 'nivelAgua_normal';
      case 'Bom':
        return 'nivelAgua_bom';
      default:
        return 'nivelAgua_bom';
    }
  }

  void consultarAvaliacoesFisicas({Function()? sucesso, Function()? carregando, Function(String? falha)? falha, bool force = false}) {
    carregando?.call();
    mConsultaAvaliacoes = ServiceStatus.Waiting;
    if (!force && mAvaliacoesFisicas.isNotEmpty) {
      mConsultaAvaliacoes = mAvaliacoesFisicas.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      sucesso?.call();
    } else {
      mService.consultarHistoricoAvaliacao((GetIt.I.get<ControladorPrescricaoDeTreino>().alunoExibir?.matriculaZW?.toString() ?? mCliente.mUsuarioLogado!.matricula!)).then((value) {
        mAvaliacoesFisicas.clear();
        mAvaliacoesFisicas.addAll(value);
        mAvaliacoesFisicas.sort((a, b) => a.id!.compareTo(b.id!));
        mConsultaAvaliacoes = mAvaliacoesFisicas.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
        sucesso?.call();
      }).catchError((onError) {
        try {
          if (onError.message.toString().contains('Aluno sem avaliação física.')) {
            mConsultaAvaliacoes = ServiceStatus.Empty;
            mAvaliacoesFisicas.clear();
          } else {
            mConsultaAvaliacoes = ServiceStatus.Error;
          }
        } catch (e) {
          mConsultaAvaliacoes = ServiceStatus.Error;
        }
        falha?.call(onError.message);
      });
    }
  }

  void consultarValorDaAvalicao({Function(num valor)? perguntar, Function()? passarDireto, Function()? carregando, Function(String? falha)? falha}) {
    carregando?.call();
    mService.obterValorProdutoAvaliacao(mCliente.mUsuarioLogado!.matricula!).then((value) {
      if (value == 0) {
        passarDireto?.call();
      } else {
        perguntar?.call(value);
      }
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  void agendarHorarioAvaliacao(AvaliadorFisico avaliador, EventoHorario horario, {Function()? sucesso, Function()? carregando, Function(String? falha)? falha, bool force = false}) {
    carregando?.call();
    mService
        .agendarAvaliacaoFisica(avaliador.primeiroDia!, horario.horario!, horario.tipoEvento!, avaliador.codigo!, mCliente.mUsuarioLogado!.codEmpresa!, mCliente.mUsuarioLogado!.matricula!)
        .then((value) {
      consultarAvaliacoesFisicas(force: true, sucesso: sucesso, falha: (v) => sucesso);
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  void consultarAvaliadoresEHorariosAgendamento(DateTime? dataPesquisa, {Function()? sucesso, Function()? carregando, Function(String? falha)? falha, bool force = false}) {
    carregando?.call();
    dataPesquisa = dataPesquisa ?? DateTime.now();
    mConsultaAgendaAvaliacao = ServiceStatus.Waiting;
    mService.consultarAvaliadoresFisicos(UtilDataHora.getDiaMesAno(dateTime: dataPesquisa)).then((listaAvaliadores) {
      mProfessoresEHorario.clear();
      for (final avaliador in listaAvaliadores) {
        mProfessoresEHorario[avaliador] = [];
      }
      return Future.wait(mProfessoresEHorario.keys
          .map((avaliador) => mService.consultarHorariosSugeridos(UtilDataHora.getDiaMesAno(dateTime: dataPesquisa), avaliador.codigo!, mCliente.mUsuarioLogado!.codEmpresa!))
          .toList());
    }).then((listaHorarios) {
      var pos = 0;
      mProfessoresEHorario.forEach((key, value) {
        mProfessoresEHorario[key] = listaHorarios[pos++];
      });
      var remover = [];
      mProfessoresEHorario.forEach((key, value) {
        if (mProfessoresEHorario[key]!.isEmpty) remover.add(key);
      });
      for (final element in remover) {
        mProfessoresEHorario.remove(element);
      }
      mConsultaAgendaAvaliacao = mProfessoresEHorario.keys.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
      mConsultaAgendaAvaliacao = ServiceStatus.Error;
    });
  }

  String obterResumoHorarios(List<EventoHorario> eventos) {
    String tagMe = '';
    for (final element in eventos) {
      tagMe += element.horario! + ', ';
    }
    return tagMe.substring(0, tagMe.length > 2 ? tagMe.length - 2 : 0);
  }

  List<PerguntaParQ> perguntasParQ = [];

  int get lengthPerguntasParQSteper => perguntasParQ.length + 1;

  void consultarPerguntasParQ({required Function() onSuccess, Function(String error)? onError}) {
    mService.consultarPerguntasParQ().then((response) {
      perguntasParQ.clear();
      perguntasParQ.addAll(response.perguntasParQ);
      onSuccess.call();
    }).catchError((error) {
      onError?.call(error.toString());
    });
  }

  void salvarRespostasParQ({required RespostaParQ respostaParQ, required num usuarioZw, Function()? onSuccess, Function()? onError}) {
    mService.salvarRespostasParQ(usuarioZw: usuarioZw, body: respostaParQ.toJson()).then((response) {
      onSuccess?.call();
    }).catchError((error) {
      error.toString();
      onError?.call();
    });
  }

  @observable
  String respondeuParq = localizedString('em_aberto');
  @observable
  List<ItemRespostaParQ> mRespostaParQAluno = [];
  @observable
  AssinaturaParQ? assinaturaDoAlunoParq;

  // consulta as respostas do parq do aluno. Se é lançada uma exceção (na classe ServiceDioProvider) então o aluno não respondeu, do contrário foi respondido
  void consultarSeAlunoRespondeuParQ({num? cod, Function()? onRespondeu, Function()? onNaoRespondeu}) {
    if (cod == null) {
      return;
    }
    mService.listarRespostasParQPorCliente(cod: cod).then((response) {
      mRespostaParQAluno.clear();
      mRespostaParQAluno.addAll(response);
      respondeuParq = localizedString('respondido');
      onRespondeu?.call();
    }).catchError((error) {
      if (error.message == 'PARQ_NAO_RESPONDIDO') {
        respondeuParq = localizedString('em_aberto');
        onNaoRespondeu?.call();
      }
    });
  }

  void consultarAssinaturaAlunoParq({Function()? sucesso}) {
    BodyAssinatura valor = BodyAssinatura(matriculaAluno: int.parse(mCliente.mUsuarioLogado?.matricula ?? ''), operacao: 'visualizarRespostasParQ', token: mCliente.mUsuarioLogado?.token);
    List<String> params = [if (valor.token != null) 'token=${valor.token}', 'operacao=visualizarRespostasParQ', 'matriculaAluno=${valor.matriculaAluno}'];
    mService.consultarAssinaturaParq(params.join('&')).then((value) {
      assinaturaDoAlunoParq = value;
    }).catchError((onError) {});
  }

  @observable
  List<IHealthDevice> devices = [];
  @observable
  BeurerBiaData? resultadoBioimpedancia;

  BeurerActivityLevel get nivelAtividadesSemana {
    switch (controladorUsuarioApp.dadosUsuario?.atividadeFisica ?? 1) {
      case 1:
        return BeurerActivityLevel.SEDENTARY;
      case 2:
        return BeurerActivityLevel.LIGHTLY_ACTIVE;
      case 3:
        return BeurerActivityLevel.MODERATELY_ACTIVE;
      case 4:
        return BeurerActivityLevel.VERY_ACTIVE;
      case 5:
        return BeurerActivityLevel.SUPER_ACTIVE;
      default:
        return BeurerActivityLevel.SEDENTARY;
    }
  }

  BeurerGender get sexoAluno {
    return (dadosBasicos.sexo_biologico ?? '').toUpperCase() == 'M' ? BeurerGender.M : BeurerGender.F;
  }

  Future<void> setUserBalanca(IHealthDevice device, Aluno aluno, {Function()? sucesso, Function(HealthDeviceError? error)? falha, Function()? balancaOn}) async {
    resultadoBioimpedancia = null;
    device.makeMeasurement(
      userInfo: BeurerUserData(
        activityLevel: nivelAtividadesSemana,
        birthDate: UtilDataHora.parseNunToDate(aluno.dataNascimento),
        nickname: aluno.nome,
        targetWeight: (dadosBasicos.peso ?? 0).toInt(),
        heightInCm: (dadosBasicos.altura ?? 0).toInt(),
        gender: sexoAluno,
      ),
      setNotification: (status, body, error) {
        switch (status) {
          case StatusBioBle.buscando:
            break;
          case StatusBioBle.conectando:
            balancaOn?.call();
            break;
          case StatusBioBle.realizandoMedicao:
            break;
          case StatusBioBle.concluido:
            resultadoBioimpedancia = body!;
            sucesso?.call();
            break;
          case StatusBioBle.falha:
            falha?.call(error);
            break;
        }
      },
    );
  }

  @observable
  avBio.ManterAvaliacaoFisica manterAvaliacaoBioimpedanciaAluno = avBio.ManterAvaliacaoFisica();

  double get valorPercentualOssea {
    var valorTotalOsseo = (resultadoBioimpedancia?.skeletalMuscleMassData.leftArmSkeletalMuscleMass ?? 0) +
        (resultadoBioimpedancia?.skeletalMuscleMassData.leftLegSkeletalMuscleMass ?? 0) +
        (resultadoBioimpedancia?.skeletalMuscleMassData.rightArmSkeletalMuscleMass ?? 0) +
        (resultadoBioimpedancia?.skeletalMuscleMassData.rightLegSkeletalMuscleMass ?? 0) +
        (resultadoBioimpedancia?.skeletalMuscleMassData.trunkSkeletalMuscleMass ?? 0);
    return (valorTotalOsseo / (resultadoBioimpedancia?.weightScaleData.weight ?? 0)) * 100;
  }

  double get valorPercentualAgua {
    return ((resultadoBioimpedancia?.bodyCompositionData.bodyWaterMass ?? 0) / (resultadoBioimpedancia?.weightScaleData.weight ?? 0)) * 100;
  }

  double get valorMassaMagra {
    return (resultadoBioimpedancia?.weightScaleData.weight ?? 0) * ((resultadoBioimpedancia?.bodyCompositionData.softLeanMass ?? 0) / 100);
  }

  double get valorMassaGorda {
    return (resultadoBioimpedancia?.weightScaleData.weight ?? 0) * ((resultadoBioimpedancia?.bodyCompositionData.bodyFat ?? 0) / 100);
  }

  void cadastrarAvaliacaoBioimpedancia({Aluno? aluno, Function()? sucesso, Function(String? erro)? falha, Function()? carregando}) {
    avBio.ManterAvaliacaoFisica avaliacao = avBio.ManterAvaliacaoFisica(
      anamneseRespostas: [],
      parQRespostas: [],
      objetivos: [],
      dataAvaliacao: DateTime.now().millisecondsSinceEpoch,
      pesoAltura: avBio.PesoAltura(altura: ((dadosBasicos.altura ?? 0) / 100), peso: (resultadoBioimpedancia?.weightScaleData.weight ?? 0).toInt()),
      dobras: avBio.DobrasAvaliacao(protocolo: 'BIOIMPEDANCIA', bioimpedancia: {
        'imc': (resultadoBioimpedancia?.weightScaleData.imc ?? 0),
        'massaGorda': valorMassaGorda,
        'percentMassaMagra': (resultadoBioimpedancia?.bodyCompositionData.percentMass ?? 0),
        'residuos': null,
        'gorduraIdeal': null,
        'reatancia': null,
        'necFisica': null,
        'necCalorica': null,
        'gorduraVisceral': resultadoBioimpedancia?.bodyFatData.visceralFatLevel  ?? 0,
        'percentMassaGorda': (resultadoBioimpedancia?.bodyCompositionData.bodyFat ?? 0),
        'massaMagra': resultadoBioimpedancia?.bodyCompositionData.softLeanMass ?? 0,
        'ossos': null,
        'musculos': resultadoBioimpedancia?.massaMuscular ?? 0,
        'resistencia': null,
        'percentAgua': valorPercentualAgua.floorToDouble(),
        'tmb': (resultadoBioimpedancia?.bodyCompositionData.basalMetabolicRate ?? 0),
        'idadeMetabolica': null
      }),
      rml: avBio.Rml(),
      vo2: avBio.Vo2(
        protocolo: 'VO_CAMINHADA_CORRIDA_12_MINUTOS',
        vo2Max: 0,
        limiarVentilatorioI: 0,
        limiarVentilatorioII: 0,
      ),
      perimetria: avBio.PerimetriaAvaliacao(),
      postura: avBio.Postura(),
    );
    carregando?.call();
    mService.cadastrarAvaliacaoFisica((aluno?.id ?? 0).toString(), avaliacao.toJsonBody()).then((value) {
      manterAvaliacaoBioimpedanciaAluno = value;
      dadosBasicos = DadosBasicoAlunoAvaliacao();
      sucesso?.call();
      GetIt.I.get<ControladorPrescricaoDeTreino>().consultarAvaliacaoFisicaRecente(idAluno: aluno?.id ?? 0);
    }).catchError((onError) {
      falha?.call(onError.toString());
    });
  }

  List<NewComposicaoCorporal> consultarListaCorporal(
      {required double percentualMusculo, required double massa, required double percentualGordura, required double percentualOsseo, required double percentualResiduo}) {
    mListaCorporal = (avaliacaoFisicaAtual?.bioimpedancia ?? false) || isBioimpedanciaAvaliacaoRecente(mPrescricaoDeTreino.avaliacaoRecenteAlunoExibir)
        ? [
            NewComposicaoCorporal(mTitulo: localizedString('massa_magra'), mPercentual: massa, nivel: niveisReferenteMusculo, textoNivel: textoNivelMuscular),
            NewComposicaoCorporal(mTitulo: localizedString('massa_gorda'), mPercentual: percentualGordura, nivel: niveisReferenteGordura, textoNivel: textoNivelGorduraCorporal),
          ]
        : [
            NewComposicaoCorporal(mTitulo: localizedString('muscles'), mPercentual: percentualMusculo, nivel: niveisReferenteMusculo, textoNivel: textoNivelMuscular),
            NewComposicaoCorporal(mTitulo: localizedString('fat'), mPercentual: percentualGordura, nivel: niveisReferenteGordura, textoNivel: textoNivelGorduraCorporal),
            NewComposicaoCorporal(
                mTitulo: localizedString('residue'), mPercentual: percentualResiduo, nivel: ValoresReferenciaCorporal(ruim: 0, bom: 5, muitoBom: 10), textoNivel: 'texto_residual')
          ];
    return mListaCorporal;
  }

  String get nivelMassaOssea {
    var sexo = mCliente.mDadosDoUsuario?.sexo ?? 'M';
    var massaOssea = mPrescricaoDeTreino.avaliacaoRecenteAlunoExibir?.alunoBI?.composicaoPorcentualOssos ?? 0;
    if (sexo == 'M') {
      return massaOssea < 1.95
          ? localizedString('niveis_corporal.baixo')
          : massaOssea <= 2.3
              ? localizedString('niveis_corporal.normal')
              : localizedString('niveis_corporal.alto');
    } else {
      return massaOssea < 2
          ? localizedString('niveis_corporal.baixo')
          : massaOssea <= 2.67
              ? localizedString('niveis_corporal.normal')
              : localizedString('niveis_corporal.alto');
    }
  }

  ValoresReferenciaCorporal get niveisReferenteOssea {
    var sexo = mCliente.mDadosDoUsuario?.sexo ?? 'M';
    if (sexo == 'M') {
      return ValoresReferenciaCorporal(ruim: 1.95, bom: 2.3, muitoBom: 2.9);
    } else {
      return ValoresReferenciaCorporal(ruim: 2, bom: 2.67, muitoBom: 3);
    }
  }

  String get textoMassaOssea {
    switch (nivelMassaOssea) {
      case 'Baixo':
        return 'ossea_baixo';
      case 'Normal':
        return 'ossea_normal';
      case 'Alto':
        return 'ossea_alto';
      default:
        return 'ossea_baixo';
    }
  }

  @observable
  List<ResumoDobras> listaDiferencaDobras = [];
  @observable
  ObservableList<FotoUsuarioAvaliacao> mFotosAvaliacaoFisicaAtual = ObservableList<FotoUsuarioAvaliacao>();
  List<List<FotoUsuarioAvaliacao>> mListaAvaliacaoFotos = [];
  List<FotoUsuarioAvaliacao> mTodasFotos = [];

  Future<void> consultarHistoricoFotosAvaliacao(String usuarioApp, num idAvaliacao, {Function()? sucesso, Function()? carregando, Function(String? falha)? falha}) async {
    mFotosAvaliacaoFisicaAtual.clear();
    mListaAvaliacaoFotos.clear();
    carregando?.call();
    await mClienteAvaliacaoFisicaService.consultarHistoricoFotosAvaliacao(usuarioApp, idAvaliacao.toString()).then((value) {
      mFotosAvaliacaoFisicaAtual.addAll(value);
      mListaAvaliacaoFotos.add(mFotosAvaliacaoFisicaAtual);
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError);
    });
  }

  Future<void> consultarHistoricoTodasFotosAvaliacao(String usuarioApp, {Function()? sucesso, Function()? carregando, Function(String? falha)? falha}) async {
    mTodasFotos.clear();
    fotosAgrupadas.clear();
    carregando?.call();
    await mClienteAvaliacaoFisicaService.consultarTodasFotosAvaliacao(usuarioApp).then((value) {
      mTodasFotos.addAll(value);
      mTodasFotos.sort((a, b) {
        int numeroA = int.parse(a.tipoFoto!.split('_').last);
        int numeroB = int.parse(b.tipoFoto!.split('_').last);
        return numeroA.compareTo(numeroB);
      });
      agruparFotosPorAvaliacao(mTodasFotos);
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError);
    });
  }

  @observable
  Map<String, List<FotoUsuarioAvaliacao>> fotosAgrupadas = {};

  void agruparFotosPorAvaliacao(List<FotoUsuarioAvaliacao> fotos) {
    for (final foto in fotos) {
      String idAvaliacao = foto.idAvaliacao!;
      if (!fotosAgrupadas.containsKey(idAvaliacao)) {
        fotosAgrupadas[idAvaliacao] = [];
      }
      fotosAgrupadas[idAvaliacao]!.add(foto);
    }
  }

  void atualizarFotosAvaliacao(String usuarioApp, String url, String tipo, String idAvaliacao, {Function()? sucesso, Function()? carregando, Function(String? falha)? falha}) {
    Map<String, dynamic> foto = {
      'urlFoto': url,
      'tipoFoto': tipo,
      'idAvaliacao': idAvaliacao,
    };
    carregando?.call();
    mClienteAvaliacaoFisicaService.atualizarFotosAvaliacao(usuarioApp, foto).then((value) {
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError);
    });
  }

  void removerFotoAvaliacao(String usuarioApp, String id, {Function()? sucesso, Function()? carregando, Function(String? falha)? falha}) {
    carregando?.call();
    mClienteAvaliacaoFisicaService.removerFotoAvaliacao(id, usuarioApp).then((value) {
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.toString());
    });
  }

  void removerTodasAsFotos({Function()? sucesso, Function()? carregando, Function(String? falha)? falha, required List<FotoUsuarioAvaliacao> fotos, required String usuarioApp}) {
    carregando?.call();
    for (final element in fotos) {
      mClienteAvaliacaoFisicaService.removerFotoAvaliacao(element.id.toString(), usuarioApp);
    }
    sucesso?.call();
  }

  bool jaRegistrouEvolucao(AvaliacaoFisica avaliacao) {
    return fotosAgrupadas.entries.toList().any((element) => element.key == avaliacao.id.toString());
  }

  @observable
  String fotoSelecionado = '';

  void diferencaDobras() {
    var dobras = dadosGraficoDobrasCutaneas.entries.toList();
    num result = 0;
    listaDiferencaDobras.clear();
    for (final element in dobras) {
      if (element.value.length >= 2) {
        if (element.value.first.valor! >= element.value.last.valor!) {
          result = (element.value.first.valor! - element.value.last.valor!);
          var resumo = ResumoDobras(diferenca: result, resultado: 'PERDEU');
          listaDiferencaDobras.add(resumo);
        } else {
          result = (element.value.last.valor! - element.value.first.valor!);
          var resumo = ResumoDobras(diferenca: result, resultado: 'GANHOU');
          listaDiferencaDobras.add(resumo);
        }
      }
    }
  }

  @observable
  GrupoMuscular? mGruposMusculares;
  @observable
  ServiceStatus mGraficoGruposTrabalhados = ServiceStatus.Waiting;

  void consultarGruposTrabalhadosPeriodo(
      {required String datainicio, required String dataFim, required num clienteCodigo, Function()? sucesso, Function()? carregando, Function(String? falha)? falha}) {
    mGraficoGruposTrabalhados = ServiceStatus.Waiting;
    carregando?.call();
    mGruposMusculares = null;
    mService.consultarGruposMusculares(dataInicial: datainicio, dataFinal: dataFim, codigoCliente: clienteCodigo).then((value) {
      mGruposMusculares = value;
      sucesso!.call();
      mGraficoGruposTrabalhados = mGruposMusculares!.content!.gruposTrabalhados!.programaAtual!.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    }).catchError((onError) {
      falha?.call(onError);
      mGraficoGruposTrabalhados = ServiceStatus.Error;
    });
  }

  List<String> get dadosDaMetrica {
    List<String> dados = [];
    if (mGruposMusculares != null || mGruposMusculares!.content!.gruposTrabalhados!.programaAtual!.isNotEmpty) {
      for (final element in mGruposMusculares!.content!.gruposTrabalhados!.programaAtual!) {
        dados.add(element.nome!);
      }
    } else {
      return dados;
    }
    return dados;
  }

  List<int> get valorsDaMetrica {
    List<int> dados = [];
    if (mGruposMusculares != null || mGruposMusculares!.content!.gruposTrabalhados!.programaAtual!.isNotEmpty) {
      for (final element in mGruposMusculares!.content!.gruposTrabalhados!.programaAtual!) {
        dados.add(element.exercicios!);
      }
    } else {
      return dados;
    }
    return dados;
  }

  void limparTudo() {
    mConsultaAvaliacoes = ServiceStatus.Waiting;
    mConsultaAgendaAvaliacao = ServiceStatus.Waiting;
    mAvaliacoesFisicas.clear();
    mProfessoresEHorario.clear();
    mFotosAvaliacaoFisicaAtual.clear();
    mListaAvaliacaoFotos.clear();
  }

  @observable
  String topSize = 'M';
  @observable
  String bottomSize = 'M';

  String get imageUrl {
    switch (topSize) {
      case 'G':
        switch (bottomSize) {
          case 'G':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Ffront%2Fback%2C%20Top%20size%3DG%2C%20Botton%20size%3DG.png?alt=media&token=4c1057ae-e8d9-43f5-80f6-2948e7314ea3&_gl=1*103b7uy*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzYyMDQuMC4wLjA';
          case 'M':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Ffront%2Fback%2C%20Top%20size%3DG%2C%20Botton%20size%3DG.png?alt=media&token=4c1057ae-e8d9-43f5-80f6-2948e7314ea3&_gl=1*103b7uy*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzYyMDQuMC4wLjA';
          case 'P':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Ffront%2Fback%2C%20Top%20size%3DG%2C%20Botton%20size%3DP.png?alt=media&token=2fe0e038-6ddc-4598-8779-95aa21847f43&_gl=1*1gcnt9y*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzYyNTQuMC4wLjA';
        }
        break;
      case 'M':
        switch (bottomSize) {
          case 'G':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Ffront%2Fback%2C%20Top%20size%3DM%2C%20Botton%20size%3DG.png?alt=media&token=16c56929-1613-4109-80ec-c14a4a9c095d&_gl=1*ppgtg4*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzYzMjIuMC4wLjA';
          case 'M':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Ffront%2Fback%2C%20Top%20size%3DM%2C%20Botton%20size%3DM.png?alt=media&token=7865cad9-19be-4172-9cb4-95e89650d38a&_gl=1*1b7y8cm*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzYzMzcuMC4wLjA';
          case 'P':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Ffront%2Fback%2C%20Top%20size%3DM%2C%20Botton%20size%3DP.png?alt=media&token=be1fc8d7-b860-44ff-82a9-59a3e91ddf79&_gl=1*1o2eu3h*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzYzNDYuMC4wLjA';
        }
        break;
      case 'P':
        switch (bottomSize) {
          case 'G':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Ffront%2Fback%2C%20Top%20size%3DP%2C%20Botton%20size%3DG.png?alt=media&token=1e641c44-f16c-4540-b1a4-b66a5db59b52&_gl=1*ww2nti*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzYzNTYuMC4wLjA';
          case 'M':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Ffront%2Fback%2C%20Top%20size%3DP%2C%20Botton%20size%3DM.png?alt=media&token=7ea6fcb7-8849-4766-8b81-294fdf3aa587&_gl=1*1nmfy7d*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzYzNjguMC4wLjA';
          case 'P':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Ffront%2Fback%2C%20Top%20size%3DP%2C%20Botton%20size%3DP.png?alt=media&token=7cb11491-65a4-4159-b348-a7d64e10e43e&_gl=1*3xnovd*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzYzNzYuMC4wLjA';
        }
        break;
    }

    return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Ffront%2Fback%2C%20Top%20size%3DM%2C%20Botton%20size%3DM.png?alt=media&token=7865cad9-19be-4172-9cb4-95e89650d38a&_gl=1*1b7y8cm*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzYzMzcuMC4wLjA';
  }

  String get imageUrlLateral {
    switch (topSize) {
      case 'G':
        switch (bottomSize) {
          case 'G':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Fperfil_esquerdo%2Fangulo%3DPerfil%20Esquerda%2C%20Top%20size%3DG%2C%20Botton%20size%3DG.png?alt=media&token=6555c2e6-af10-4c39-8a5f-9f73520ce690&_gl=1*2zpmhy*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzY1NzMuMC4wLjA';
          case 'M':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Fperfil_esquerdo%2Fangulo%3DPerfil%20Esquerda%2C%20Top%20size%3DG%2C%20Botton%20size%3DM.png?alt=media&token=dfd5c740-87ec-4da5-a9ae-a26aeb747734&_gl=1*1pvt8pb*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzY1ODUuMC4wLjA';
          case 'P':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Fperfil_esquerdo%2Fangulo%3DPerfil%20Esquerda%2C%20Top%20size%3DG%2C%20Botton%20size%3DP.png?alt=media&token=aea3e090-eb3e-43b2-bfeb-c33f0f9d7b3f&_gl=1*54m1xc*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzY2MDQuMC4wLjA';
        }
        break;
      case 'M':
        switch (bottomSize) {
          case 'G':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Fperfil_esquerdo%2Fangulo%3DPerfil%20Esquerda%2C%20Top%20size%3DM%2C%20Botton%20size%3DG.png?alt=media&token=1c872f90-25a5-46b0-8ecf-5229f16ad1a0&_gl=1*1gbeop7*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzY2MTQuMC4wLjA';
          case 'M':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Fperfil_esquerdo%2Fangulo%3DPerfil%20Esquerda%2C%20Top%20size%3DM%2C%20Botton%20size%3DM.png?alt=media&token=f3c7e06e-4d84-40c9-8ed0-f74c11710f62&_gl=1*1j3gf4h*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzY2MjQuMC4wLjA';
          case 'P':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Fperfil_esquerdo%2Fangulo%3DPerfil%20Esquerda%2C%20Top%20size%3DM%2C%20Botton%20size%3DP.png?alt=media&token=7b110bb9-c50a-4833-b9e1-b2c47f308ef7&_gl=1*syuije*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzY2MzQuMC4wLjA';
        }
        break;
      case 'P':
        switch (bottomSize) {
          case 'G':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Fperfil_esquerdo%2Fangulo%3DPerfil%20Esquerda%2C%20Top%20size%3DP%2C%20Botton%20size%3DG.png?alt=media&token=0efc2438-817d-44ac-b8e6-3b80873ea7e3&_gl=1*1e0gdxz*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzY2ODEuMC4wLjA';
          case 'M':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Fperfil_esquerdo%2Fangulo%3DPerfil%20Esquerda%2C%20Top%20size%3DP%2C%20Botton%20size%3DM.png?alt=media&token=065b0c68-5f85-4bfb-a03c-8feda781fffa&_gl=1*9kyxe*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzY2OTIuMC4wLjA';
          case 'P':
            return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Fperfil_esquerdo%2Fangulo%3DPerfil%20Esquerda%2C%20Top%20size%3DP%2C%20Botton%20size%3DP.png?alt=media&token=c13302ac-ed9d-4128-bb05-dab07c994deb&_gl=1*1128fbh*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzY3MDEuMC4wLjA';
        }
        break;
    }

    return 'https://firebasestorage.googleapis.com/v0/b/app-do-aluno-unificado.appspot.com/o/_AvPostural%2Fperfil_esquerdo%2Fangulo%3DPerfil%20Esquerda%2C%20Top%20size%3DM%2C%20Botton%20size%3DM.png?alt=media&token=f3c7e06e-4d84-40c9-8ed0-f74c11710f62&_gl=1*1j3gf4h*_ga*OTM5MzAyNDgyLjE2NDQ0MTQ3NzY.*_ga_CW55HF8NVT*MTY4NTczNTgxNy4yNy4xLjE2ODU3MzY2MjQuMC4wLjA';
  }

  @action
  void setTopSize(String size) {
    topSize = size;
  }

  @action
  void setBottomSize(String size) {
    bottomSize = size;
  }
}

class ResumoDobras {
  num? diferenca;
  String? resultado;

  ResumoDobras({this.diferenca, this.resultado});
}

class ValoresReferenciaCorporal {
  double? ruim;
  double? bom;
  double? muitoBom;

  ValoresReferenciaCorporal({this.ruim, this.bom, this.muitoBom});
}

enum TipoFotoAvaliacao { FRENTE_1, LADO_DIREITO_2, LADO_ESQUERDO_3, COSTA_4 }
