import 'package:app_treino/ServiceProvider/ConversaAiServices.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:get_it/get_it.dart';

class ControladorConversaAI {

  final mService = GetIt.I.get<ServicesConversaAI>();
  final _controladorCliente = GetIt.I.get<ControladorCliente>();

  /// Inicia uma fase de conversa com IA
  ///
  /// Este método chama o serviço iniciarFaseAI passando os parâmetros necessários:
  /// - [fase]: String que identifica a fase da conversa
  /// - [carregando]: Função chamada quando a operação está em andamento
  /// - [sucesso]: Função chamada quando a operação é bem-sucedida
  /// - [erro]: Função chamada quando a operação falha, recebendo uma mensagem de erro
  Future<void> iniciarFaseAI({
    required String fase,
    Function()? carregando,
    Function()? sucesso,
    Function(String mensagem)? erro,
  }) async {
    carregando?.call();

    // Obtém os dados necessários do controlador cliente
    final empresa = _controladorCliente.mUsuarioLogado?.codEmpresa ?? 1;
    final cliente = _controladorCliente.mUsuarioLogado?.codigoCliente ?? 0;

    // Chama o serviço
    await mService.iniciarFaseAI(
      empresa: empresa,
      fase: fase,
      cliente: cliente,
    ).then((response) {
      //print('Fase AI iniciada com sucesso: $response');
      sucesso?.call();
    }).catchError((onError) {
      erro?.call(onError.toString());
    });
  }
}