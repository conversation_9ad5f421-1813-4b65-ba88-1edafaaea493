// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorApp.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorApp on _ControladorAppBase, Store {
  late final _$mClientesAppAtom =
      Atom(name: '_ControladorAppBase.mClientesApp', context: context);

  @override
  ObservableList<ClienteApp> get mClientesApp {
    _$mClientesAppAtom.reportRead();
    return super.mClientesApp;
  }

  @override
  set mClientesApp(ObservableList<ClienteApp> value) {
    _$mClientesAppAtom.reportWrite(value, super.mClientesApp, () {
      super.mClientesApp = value;
    });
  }

  late final _$mClientesAppPesquisaAtom =
      Atom(name: '_ControladorAppBase.mClientesAppPesquisa', context: context);

  @override
  ObservableList<ClienteApp> get mClientesAppPesquisa {
    _$mClientesAppPesquisaAtom.reportRead();
    return super.mClientesAppPesquisa;
  }

  @override
  set mClientesAppPesquisa(ObservableList<ClienteApp> value) {
    _$mClientesAppPesquisaAtom.reportWrite(value, super.mClientesAppPesquisa,
        () {
      super.mClientesAppPesquisa = value;
    });
  }

  late final _$mEmpresasAppPequisaAtom =
      Atom(name: '_ControladorAppBase.mEmpresasAppPequisa', context: context);

  @override
  ObservableList<EmpresaApp> get mEmpresasAppPequisa {
    _$mEmpresasAppPequisaAtom.reportRead();
    return super.mEmpresasAppPequisa;
  }

  @override
  set mEmpresasAppPequisa(ObservableList<EmpresaApp> value) {
    _$mEmpresasAppPequisaAtom.reportWrite(value, super.mEmpresasAppPequisa, () {
      super.mEmpresasAppPequisa = value;
    });
  }

  late final _$mConfiguracoesTreinoWebAtom = Atom(
      name: '_ControladorAppBase.mConfiguracoesTreinoWeb', context: context);

  @override
  ObservableList<ConfiguracaoTreino> get mConfiguracoesTreinoWeb {
    _$mConfiguracoesTreinoWebAtom.reportRead();
    return super.mConfiguracoesTreinoWeb;
  }

  @override
  set mConfiguracoesTreinoWeb(ObservableList<ConfiguracaoTreino> value) {
    _$mConfiguracoesTreinoWebAtom
        .reportWrite(value, super.mConfiguracoesTreinoWeb, () {
      super.mConfiguracoesTreinoWeb = value;
    });
  }

  late final _$pularSenhaLoginAtom =
      Atom(name: '_ControladorAppBase.pularSenhaLogin', context: context);

  @override
  bool get pularSenhaLogin {
    _$pularSenhaLoginAtom.reportRead();
    return super.pularSenhaLogin;
  }

  @override
  set pularSenhaLogin(bool value) {
    _$pularSenhaLoginAtom.reportWrite(value, super.pularSenhaLogin, () {
      super.pularSenhaLogin = value;
    });
  }

  late final _$mostrarLogHttpAtom =
      Atom(name: '_ControladorAppBase.mostrarLogHttp', context: context);

  @override
  bool get mostrarLogHttp {
    _$mostrarLogHttpAtom.reportRead();
    return super.mostrarLogHttp;
  }

  @override
  set mostrarLogHttp(bool value) {
    _$mostrarLogHttpAtom.reportWrite(value, super.mostrarLogHttp, () {
      super.mostrarLogHttp = value;
    });
  }

  late final _$utilizaDescoveryAtom =
      Atom(name: '_ControladorAppBase.utilizaDescovery', context: context);

  @override
  bool get utilizaDescovery {
    _$utilizaDescoveryAtom.reportRead();
    return super.utilizaDescovery;
  }

  @override
  set utilizaDescovery(bool value) {
    _$utilizaDescoveryAtom.reportWrite(value, super.utilizaDescovery, () {
      super.utilizaDescovery = value;
    });
  }

  late final _$veioDoSplashAtom =
      Atom(name: '_ControladorAppBase.veioDoSplash', context: context);

  @override
  bool get veioDoSplash {
    _$veioDoSplashAtom.reportRead();
    return super.veioDoSplash;
  }

  @override
  set veioDoSplash(bool value) {
    _$veioDoSplashAtom.reportWrite(value, super.veioDoSplash, () {
      super.veioDoSplash = value;
    });
  }

  late final _$chaveAtom =
      Atom(name: '_ControladorAppBase.chave', context: context);

  @override
  String? get chave {
    _$chaveAtom.reportRead();
    return super.chave;
  }

  @override
  set chave(String? value) {
    _$chaveAtom.reportWrite(value, super.chave, () {
      super.chave = value;
    });
  }

  late final _$defaultColorAtom =
      Atom(name: '_ControladorAppBase.defaultColor', context: context);

  @override
  Color? get defaultColor {
    _$defaultColorAtom.reportRead();
    return super.defaultColor;
  }

  @override
  set defaultColor(Color? value) {
    _$defaultColorAtom.reportWrite(value, super.defaultColor, () {
      super.defaultColor = value;
    });
  }

  late final _$statusCarregamentoListaClienteAppAtom = Atom(
      name: '_ControladorAppBase.statusCarregamentoListaClienteApp',
      context: context);

  @override
  ServiceStatus get statusCarregamentoListaClienteApp {
    _$statusCarregamentoListaClienteAppAtom.reportRead();
    return super.statusCarregamentoListaClienteApp;
  }

  @override
  set statusCarregamentoListaClienteApp(ServiceStatus value) {
    _$statusCarregamentoListaClienteAppAtom
        .reportWrite(value, super.statusCarregamentoListaClienteApp, () {
      super.statusCarregamentoListaClienteApp = value;
    });
  }

  late final _$statusCarregandoAcademiasAtom = Atom(
      name: '_ControladorAppBase.statusCarregandoAcademias', context: context);

  @override
  ServiceStatus get statusCarregandoAcademias {
    _$statusCarregandoAcademiasAtom.reportRead();
    return super.statusCarregandoAcademias;
  }

  @override
  set statusCarregandoAcademias(ServiceStatus value) {
    _$statusCarregandoAcademiasAtom
        .reportWrite(value, super.statusCarregandoAcademias, () {
      super.statusCarregandoAcademias = value;
    });
  }

  late final _$indexCardTreinoAtom =
      Atom(name: '_ControladorAppBase.indexCardTreino', context: context);

  @override
  int get indexCardTreino {
    _$indexCardTreinoAtom.reportRead();
    return super.indexCardTreino;
  }

  @override
  set indexCardTreino(int value) {
    _$indexCardTreinoAtom.reportWrite(value, super.indexCardTreino, () {
      super.indexCardTreino = value;
    });
  }

  late final _$estadoAppAtom =
      Atom(name: '_ControladorAppBase.estadoApp', context: context);

  @override
  AppLifecycleState get estadoApp {
    _$estadoAppAtom.reportRead();
    return super.estadoApp;
  }

  @override
  set estadoApp(AppLifecycleState value) {
    _$estadoAppAtom.reportWrite(value, super.estadoApp, () {
      super.estadoApp = value;
    });
  }

  late final _$themeModeAtom =
      Atom(name: '_ControladorAppBase.themeMode', context: context);

  @override
  ThemeMode? get themeMode {
    _$themeModeAtom.reportRead();
    return super.themeMode;
  }

  @override
  set themeMode(ThemeMode? value) {
    _$themeModeAtom.reportWrite(value, super.themeMode, () {
      super.themeMode = value;
    });
  }

  late final _$appLocaleAtom =
      Atom(name: '_ControladorAppBase.appLocale', context: context);

  @override
  Locale? get appLocale {
    _$appLocaleAtom.reportRead();
    return super.appLocale;
  }

  @override
  set appLocale(Locale? value) {
    _$appLocaleAtom.reportWrite(value, super.appLocale, () {
      super.appLocale = value;
    });
  }

  late final _$mClienteAppSelecionadoAtom = Atom(
      name: '_ControladorAppBase.mClienteAppSelecionado', context: context);

  @override
  ClienteApp? get mClienteAppSelecionado {
    _$mClienteAppSelecionadoAtom.reportRead();
    return super.mClienteAppSelecionado;
  }

  @override
  set mClienteAppSelecionado(ClienteApp? value) {
    _$mClienteAppSelecionadoAtom
        .reportWrite(value, super.mClienteAppSelecionado, () {
      super.mClienteAppSelecionado = value;
    });
  }

  late final _$telaTabbarAtom =
      Atom(name: '_ControladorAppBase.telaTabbar', context: context);

  @override
  ObservableList<Widget> get telaTabbar {
    _$telaTabbarAtom.reportRead();
    return super.telaTabbar;
  }

  @override
  set telaTabbar(ObservableList<Widget> value) {
    _$telaTabbarAtom.reportWrite(value, super.telaTabbar, () {
      super.telaTabbar = value;
    });
  }

  late final _$indexTabbarAtom =
      Atom(name: '_ControladorAppBase.indexTabbar', context: context);

  @override
  int get indexTabbar {
    _$indexTabbarAtom.reportRead();
    return super.indexTabbar;
  }

  @override
  set indexTabbar(int value) {
    _$indexTabbarAtom.reportWrite(value, super.indexTabbar, () {
      super.indexTabbar = value;
    });
  }

  late final _$controllerAtom =
      Atom(name: '_ControladorAppBase.controller', context: context);

  @override
  PageController get controller {
    _$controllerAtom.reportRead();
    return super.controller;
  }

  @override
  set controller(PageController value) {
    _$controllerAtom.reportWrite(value, super.controller, () {
      super.controller = value;
    });
  }

  late final _$itensBottomBarAtom =
      Atom(name: '_ControladorAppBase.itensBottomBar', context: context);

  @override
  ObservableList<BottomNavigationBarItem> get itensBottomBar {
    _$itensBottomBarAtom.reportRead();
    return super.itensBottomBar;
  }

  @override
  set itensBottomBar(ObservableList<BottomNavigationBarItem> value) {
    _$itensBottomBarAtom.reportWrite(value, super.itensBottomBar, () {
      super.itensBottomBar = value;
    });
  }

  late final _$mListaEmpresasZwAtom =
      Atom(name: '_ControladorAppBase.mListaEmpresasZw', context: context);

  @override
  ObservableList<Empresa> get mListaEmpresasZw {
    _$mListaEmpresasZwAtom.reportRead();
    return super.mListaEmpresasZw;
  }

  @override
  set mListaEmpresasZw(ObservableList<Empresa> value) {
    _$mListaEmpresasZwAtom.reportWrite(value, super.mListaEmpresasZw, () {
      super.mListaEmpresasZw = value;
    });
  }

  late final _$empresaFinanceiraAtom =
      Atom(name: '_ControladorAppBase.empresaFinanceira', context: context);

  @override
  EmpresaFinanceiro get empresaFinanceira {
    _$empresaFinanceiraAtom.reportRead();
    return super.empresaFinanceira;
  }

  @override
  set empresaFinanceira(EmpresaFinanceiro value) {
    _$empresaFinanceiraAtom.reportWrite(value, super.empresaFinanceira, () {
      super.empresaFinanceira = value;
    });
  }

  late final _$textopesquisaAtom =
      Atom(name: '_ControladorAppBase.textopesquisa', context: context);

  @override
  String get textopesquisa {
    _$textopesquisaAtom.reportRead();
    return super.textopesquisa;
  }

  @override
  set textopesquisa(String value) {
    _$textopesquisaAtom.reportWrite(value, super.textopesquisa, () {
      super.textopesquisa = value;
    });
  }

  late final _$_ControladorAppBaseActionController =
      ActionController(name: '_ControladorAppBase', context: context);

  @override
  void setAppLocale(Locale locale) {
    final _$actionInfo = _$_ControladorAppBaseActionController.startAction(
        name: '_ControladorAppBase.setAppLocale');
    try {
      return super.setAppLocale(locale);
    } finally {
      _$_ControladorAppBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void updateEstadoApp(AppLifecycleState novoEstado) {
    final _$actionInfo = _$_ControladorAppBaseActionController.startAction(
        name: '_ControladorAppBase.updateEstadoApp');
    try {
      return super.updateEstadoApp(novoEstado);
    } finally {
      _$_ControladorAppBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic irParaMenuAulas() {
    final _$actionInfo = _$_ControladorAppBaseActionController.startAction(
        name: '_ControladorAppBase.irParaMenuAulas');
    try {
      return super.irParaMenuAulas();
    } finally {
      _$_ControladorAppBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic irParaSaude() {
    final _$actionInfo = _$_ControladorAppBaseActionController.startAction(
        name: '_ControladorAppBase.irParaSaude');
    try {
      return super.irParaSaude();
    } finally {
      _$_ControladorAppBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
mClientesApp: ${mClientesApp},
mClientesAppPesquisa: ${mClientesAppPesquisa},
mEmpresasAppPequisa: ${mEmpresasAppPequisa},
mConfiguracoesTreinoWeb: ${mConfiguracoesTreinoWeb},
pularSenhaLogin: ${pularSenhaLogin},
mostrarLogHttp: ${mostrarLogHttp},
utilizaDescovery: ${utilizaDescovery},
veioDoSplash: ${veioDoSplash},
chave: ${chave},
defaultColor: ${defaultColor},
statusCarregamentoListaClienteApp: ${statusCarregamentoListaClienteApp},
statusCarregandoAcademias: ${statusCarregandoAcademias},
indexCardTreino: ${indexCardTreino},
estadoApp: ${estadoApp},
themeMode: ${themeMode},
appLocale: ${appLocale},
mClienteAppSelecionado: ${mClienteAppSelecionado},
telaTabbar: ${telaTabbar},
indexTabbar: ${indexTabbar},
controller: ${controller},
itensBottomBar: ${itensBottomBar},
mListaEmpresasZw: ${mListaEmpresasZw},
empresaFinanceira: ${empresaFinanceira},
textopesquisa: ${textopesquisa}
    ''';
  }
}
