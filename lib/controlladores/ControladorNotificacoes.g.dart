// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorNotificacoes.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorNotificacoes on _ControladorNotificacoesBase, Store {
  late final _$notificacoesAtom =
      Atom(name: '_ControladorNotificacoesBase.notificacoes', context: context);

  @override
  ObservableList<NotificacaoPersonal> get notificacoes {
    _$notificacoesAtom.reportRead();
    return super.notificacoes;
  }

  @override
  set notificacoes(ObservableList<NotificacaoPersonal> value) {
    _$notificacoesAtom.reportWrite(value, super.notificacoes, () {
      super.notificacoes = value;
    });
  }

  late final _$statusConsultaObservacoesAtom = Atom(
      name: '_ControladorNotificacoesBase.statusConsultaObservacoes',
      context: context);

  @override
  ServiceStatus get statusConsultaObservacoes {
    _$statusConsultaObservacoesAtom.reportRead();
    return super.statusConsultaObservacoes;
  }

  @override
  set statusConsultaObservacoes(ServiceStatus value) {
    _$statusConsultaObservacoesAtom
        .reportWrite(value, super.statusConsultaObservacoes, () {
      super.statusConsultaObservacoes = value;
    });
  }

  late final _$statusConsultaObservacoesMoreAtom = Atom(
      name: '_ControladorNotificacoesBase.statusConsultaObservacoesMore',
      context: context);

  @override
  ServiceStatus get statusConsultaObservacoesMore {
    _$statusConsultaObservacoesMoreAtom.reportRead();
    return super.statusConsultaObservacoesMore;
  }

  @override
  set statusConsultaObservacoesMore(ServiceStatus value) {
    _$statusConsultaObservacoesMoreAtom
        .reportWrite(value, super.statusConsultaObservacoesMore, () {
      super.statusConsultaObservacoesMore = value;
    });
  }

  late final _$quantidadeRecebidasSessaoAtom = Atom(
      name: '_ControladorNotificacoesBase.quantidadeRecebidasSessao',
      context: context);

  @override
  num get quantidadeRecebidasSessao {
    _$quantidadeRecebidasSessaoAtom.reportRead();
    return super.quantidadeRecebidasSessao;
  }

  @override
  set quantidadeRecebidasSessao(num value) {
    _$quantidadeRecebidasSessaoAtom
        .reportWrite(value, super.quantidadeRecebidasSessao, () {
      super.quantidadeRecebidasSessao = value;
    });
  }

  @override
  String toString() {
    return '''
notificacoes: ${notificacoes},
statusConsultaObservacoes: ${statusConsultaObservacoes},
statusConsultaObservacoesMore: ${statusConsultaObservacoesMore},
quantidadeRecebidasSessao: ${quantidadeRecebidasSessao}
    ''';
  }
}
