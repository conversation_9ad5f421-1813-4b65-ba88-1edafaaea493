import 'package:app_treino/controlladores/ControladorTreinosPreDefinidos.dart';
import 'package:app_treino/model/personal/ProgramaFicha.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorEdicaoiExercicio.g.dart';

class ControladorEdicaoiExercicio = _ControladorEdicaoiExercicioBase
    with _$ControladorEdicaoiExercicio;

abstract class _ControladorEdicaoiExercicioBase with Store {
  AtividadesFicha? atividadeManter;

  var controladorTreinoModelo = GetIt.I.get<ControladorTreinosPreDefinidos>();

  @observable
  ObservableList<Series?> seriesManter = ObservableList<Series?>();

  @observable
  var serie = Observable(Series());

  num? quantidadeSeries;
  String? cadencia;
  num? carga;
  num? repeticoes;
  String? cargaApp;
  String? cargaComp;
  String? repeticaoApp;
  String? repeticaoComp;
  num? descanso;
  num? duracao;
  num? velocidade;
  num? distancia;
  String? observacao;
  bool originalSemSeries = true;
  @observable
  bool mudouMetodo = false;
  @observable
  bool seriesEstaoEmpty = false;
  void clean() {
    this.quantidadeSeries = null;
    this.cadencia = '';
    this.carga = null;
    this.cargaApp = '';
    this.cargaComp = '';
    this.repeticaoApp = '';
    this.repeticaoComp = '';
    this.repeticoes = null;
    this.descanso = null;
    this.duracao = null;
    this.velocidade = null;
    this.distancia = null;
    this.observacao = null;
    this.cadencia = null;
  }

  void manterOrdemSerie(num before, num after) {
    for (var i = 0; i < seriesManter.length; i++) {
      seriesManter[i]!.ordem = i + 1;
    }
  }

  void setMetodoBiOuTriSet(
      num? codigoMetodoExecucao, List<AtividadesFicha> atividadesSets) {
    atividadeManter!.setid = '';
    controladorTreinoModelo.manterAordemDosExercicios(null, null);
    for (final atv in atividadesSets) {
      atividadeManter!.setid = atividadeManter!.setid! + '${atv.ordem}|';
    }
    atividadeManter!.setid =
        atividadeManter!.setid! + '${atividadeManter!.ordem}|';
    controladorTreinoModelo.treinoModelo!.atividadesFicha!
        .where((element) => atividadesSets.contains(element))
        .forEach((element) {
      element.metodoExecucao = codigoMetodoExecucao;
      element.setid = atividadeManter!.setid;
    });
  }

  void setAtividade(AtividadesFicha atividade) {
    atividadeManter = atividade;
    seriesManter.clear();
    seriesManter.addAll(atividade.series!);
    originalSemSeries = seriesManter.isEmpty;
  }

  void alteraSerie(Series? serieOriginal, Series? serieAlterado) {
    int index = seriesManter.indexOf(serieOriginal);
    seriesManter[index] = serieAlterado;
    atividadeManter!.series = seriesManter.toList();
  }

  void adicionarNovaSerie() {
    var serieAdd = Series.fromJson(seriesManter.last!.toJson());
    serieAdd.codigo = null;
    seriesManter.add(serieAdd);
    for (var i = 0; i < seriesManter.length; i++) {
      seriesManter[i]!.ordem = i + 1;
    }
    atividadeManter!.series = seriesManter.toList();
  }

  void removerSerie(Series serie) {
    try {
      seriesManter.remove(serie);
    } catch (e) {}

    if (seriesManter.isEmpty) originalSemSeries = true;
    atividadeManter!.series = seriesManter.toList();
    seriesEstaoEmpty = originalSemSeries;
  }

  void setBaseSerie(
      {num? quantidadeSeries,
      String? cargaApp,
      String? repeticaoApp,
      num? descanso,
      String? cadencia,
      num? duracao,
      num? velocidade,
      num? distancia,
      String? observacao}) {
    if (quantidadeSeries != null) this.quantidadeSeries = quantidadeSeries;
    if (cargaApp != null) {
      this.cargaApp = cargaApp;
      this.cargaComp = cargaApp;
    }
    if (repeticaoApp != null) {
      this.repeticaoApp = repeticaoApp;
      this.repeticaoComp = repeticaoApp;
    }
    if (descanso != null) this.descanso = descanso;
    if (cadencia != null) this.cadencia = cadencia;
    if (duracao != null) this.duracao = duracao;
    if (velocidade != null) this.velocidade = velocidade;
    if (distancia != null) this.distancia = distancia;
    if (observacao != null) this.observacao = observacao;
    seriesManter.clear();
    if (this.quantidadeSeries != null) {
      for (var i = 0; i < this.quantidadeSeries!; i++) {
        seriesManter.add(Series(
            cargaApp: this.cargaApp,
            cargaComp: this.cargaApp,
            cadencia: this.cadencia,
            repeticaoApp: this.repeticaoApp,
            repeticaoComp: this.repeticaoApp,
            descanso: this.descanso,
            duracao: this.duracao,
            distancia: this.distancia,
            velocidade: this.velocidade,
            complemento: this.observacao));
        atividadeManter!.series = seriesManter.toList();
      }
    }
  }

  limparTudo() {
    serie = Observable(Series());
    seriesManter.clear();
    quantidadeSeries = null;
    cadencia = null;
    carga = null;
    repeticoes = null;
    descanso = null;
    duracao = null;
    velocidade = null;
    distancia = null;
    originalSemSeries = true;
    mudouMetodo = false;
  }
}
