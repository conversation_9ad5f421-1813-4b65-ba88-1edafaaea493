// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorTreinoAluno.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorTreinoAluno on _ControladorTreinoAlunoBase, Store {
  late final _$mTreinoEmExecucaoAtom = Atom(
      name: '_ControladorTreinoAlunoBase.mTreinoEmExecucao', context: context);

  @override
  bool get mTreinoEmExecucao {
    _$mTreinoEmExecucaoAtom.reportRead();
    return super.mTreinoEmExecucao;
  }

  @override
  set mTreinoEmExecucao(bool value) {
    _$mTreinoEmExecucaoAtom.reportWrite(value, super.mTreinoEmExecucao, () {
      super.mTreinoEmExecucao = value;
    });
  }

  late final _$mTempoIniciadoAtom = Atom(
      name: '_ControladorTreinoAlunoBase.mTempoIniciado', context: context);

  @override
  int get mTempoIniciado {
    _$mTempoIniciadoAtom.reportRead();
    return super.mTempoIniciado;
  }

  @override
  set mTempoIniciado(int value) {
    _$mTempoIniciadoAtom.reportWrite(value, super.mTempoIniciado, () {
      super.mTempoIniciado = value;
    });
  }

  late final _$mTempoDecorridoAtom = Atom(
      name: '_ControladorTreinoAlunoBase.mTempoDecorrido', context: context);

  @override
  String get mTempoDecorrido {
    _$mTempoDecorridoAtom.reportRead();
    return super.mTempoDecorrido;
  }

  @override
  set mTempoDecorrido(String value) {
    _$mTempoDecorridoAtom.reportWrite(value, super.mTempoDecorrido, () {
      super.mTempoDecorrido = value;
    });
  }

  late final _$mStatusConsultaProgramaAtom = Atom(
      name: '_ControladorTreinoAlunoBase.mStatusConsultaPrograma',
      context: context);

  @override
  ServiceStatus get mStatusConsultaPrograma {
    _$mStatusConsultaProgramaAtom.reportRead();
    return super.mStatusConsultaPrograma;
  }

  @override
  set mStatusConsultaPrograma(ServiceStatus value) {
    _$mStatusConsultaProgramaAtom
        .reportWrite(value, super.mStatusConsultaPrograma, () {
      super.mStatusConsultaPrograma = value;
    });
  }

  late final _$mStatusConsultaAtividadesRelacionadasAtom = Atom(
      name: '_ControladorTreinoAlunoBase.mStatusConsultaAtividadesRelacionadas',
      context: context);

  @override
  ServiceStatus get mStatusConsultaAtividadesRelacionadas {
    _$mStatusConsultaAtividadesRelacionadasAtom.reportRead();
    return super.mStatusConsultaAtividadesRelacionadas;
  }

  @override
  set mStatusConsultaAtividadesRelacionadas(ServiceStatus value) {
    _$mStatusConsultaAtividadesRelacionadasAtom
        .reportWrite(value, super.mStatusConsultaAtividadesRelacionadas, () {
      super.mStatusConsultaAtividadesRelacionadas = value;
    });
  }

  late final _$mProgramaAtualizouAtom = Atom(
      name: '_ControladorTreinoAlunoBase.mProgramaAtualizou', context: context);

  @override
  bool get mProgramaAtualizou {
    _$mProgramaAtualizouAtom.reportRead();
    return super.mProgramaAtualizou;
  }

  @override
  set mProgramaAtualizou(bool value) {
    _$mProgramaAtualizouAtom.reportWrite(value, super.mProgramaAtualizou, () {
      super.mProgramaAtualizou = value;
    });
  }

  late final _$mProgramaCarregadoAtom = Atom(
      name: '_ControladorTreinoAlunoBase.mProgramaCarregado', context: context);

  @override
  ProgramadeTreino? get mProgramaCarregado {
    _$mProgramaCarregadoAtom.reportRead();
    return super.mProgramaCarregado;
  }

  @override
  set mProgramaCarregado(ProgramadeTreino? value) {
    _$mProgramaCarregadoAtom.reportWrite(value, super.mProgramaCarregado, () {
      super.mProgramaCarregado = value;
    });
  }

  late final _$statusTreinoIASituacaoAtom = Atom(
      name: '_ControladorTreinoAlunoBase.statusTreinoIASituacao',
      context: context);

  @override
  SituacaoValidacao get statusTreinoIASituacao {
    _$statusTreinoIASituacaoAtom.reportRead();
    return super.statusTreinoIASituacao;
  }

  @override
  set statusTreinoIASituacao(SituacaoValidacao value) {
    _$statusTreinoIASituacaoAtom
        .reportWrite(value, super.statusTreinoIASituacao, () {
      super.statusTreinoIASituacao = value;
    });
  }

  late final _$mFichaExibirAtom =
      Atom(name: '_ControladorTreinoAlunoBase.mFichaExibir', context: context);

  @override
  Ficha? get mFichaExibir {
    _$mFichaExibirAtom.reportRead();
    return super.mFichaExibir;
  }

  @override
  set mFichaExibir(Ficha? value) {
    _$mFichaExibirAtom.reportWrite(value, super.mFichaExibir, () {
      super.mFichaExibir = value;
    });
  }

  late final _$mAtividadeProgramaExibirAtom = Atom(
      name: '_ControladorTreinoAlunoBase.mAtividadeProgramaExibir',
      context: context);

  @override
  ProgramaAtividade? get mAtividadeProgramaExibir {
    _$mAtividadeProgramaExibirAtom.reportRead();
    return super.mAtividadeProgramaExibir;
  }

  @override
  set mAtividadeProgramaExibir(ProgramaAtividade? value) {
    _$mAtividadeProgramaExibirAtom
        .reportWrite(value, super.mAtividadeProgramaExibir, () {
      super.mAtividadeProgramaExibir = value;
    });
  }

  late final _$mAtividadeFichaExibirAtom = Atom(
      name: '_ControladorTreinoAlunoBase.mAtividadeFichaExibir',
      context: context);

  @override
  FichaAtividade? get mAtividadeFichaExibir {
    _$mAtividadeFichaExibirAtom.reportRead();
    return super.mAtividadeFichaExibir;
  }

  @override
  set mAtividadeFichaExibir(FichaAtividade? value) {
    _$mAtividadeFichaExibirAtom.reportWrite(value, super.mAtividadeFichaExibir,
        () {
      super.mAtividadeFichaExibir = value;
    });
  }

  late final _$mHistoricoExecucaoAtom = Atom(
      name: '_ControladorTreinoAlunoBase.mHistoricoExecucao', context: context);

  @override
  ObservableList<ExecucaoTreinoFirebase> get mHistoricoExecucao {
    _$mHistoricoExecucaoAtom.reportRead();
    return super.mHistoricoExecucao;
  }

  @override
  set mHistoricoExecucao(ObservableList<ExecucaoTreinoFirebase> value) {
    _$mHistoricoExecucaoAtom.reportWrite(value, super.mHistoricoExecucao, () {
      super.mHistoricoExecucao = value;
    });
  }

  late final _$mCodigoFichaExecucaoAtom = Atom(
      name: '_ControladorTreinoAlunoBase.mCodigoFichaExecucao',
      context: context);

  @override
  int? get mCodigoFichaExecucao {
    _$mCodigoFichaExecucaoAtom.reportRead();
    return super.mCodigoFichaExecucao;
  }

  @override
  set mCodigoFichaExecucao(int? value) {
    _$mCodigoFichaExecucaoAtom.reportWrite(value, super.mCodigoFichaExecucao,
        () {
      super.mCodigoFichaExecucao = value;
    });
  }

  late final _$mListaGrupoMuscularAtom = Atom(
      name: '_ControladorTreinoAlunoBase.mListaGrupoMuscular',
      context: context);

  @override
  List<String> get mListaGrupoMuscular {
    _$mListaGrupoMuscularAtom.reportRead();
    return super.mListaGrupoMuscular;
  }

  @override
  set mListaGrupoMuscular(List<String> value) {
    _$mListaGrupoMuscularAtom.reportWrite(value, super.mListaGrupoMuscular, () {
      super.mListaGrupoMuscular = value;
    });
  }

  @override
  String toString() {
    return '''
mTreinoEmExecucao: ${mTreinoEmExecucao},
mTempoIniciado: ${mTempoIniciado},
mTempoDecorrido: ${mTempoDecorrido},
mStatusConsultaPrograma: ${mStatusConsultaPrograma},
mStatusConsultaAtividadesRelacionadas: ${mStatusConsultaAtividadesRelacionadas},
mProgramaAtualizou: ${mProgramaAtualizou},
mProgramaCarregado: ${mProgramaCarregado},
statusTreinoIASituacao: ${statusTreinoIASituacao},
mFichaExibir: ${mFichaExibir},
mAtividadeProgramaExibir: ${mAtividadeProgramaExibir},
mAtividadeFichaExibir: ${mAtividadeFichaExibir},
mHistoricoExecucao: ${mHistoricoExecucao},
mCodigoFichaExecucao: ${mCodigoFichaExecucao},
mListaGrupoMuscular: ${mListaGrupoMuscular}
    ''';
  }
}
