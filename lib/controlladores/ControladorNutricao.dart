import 'dart:io';
import 'dart:math';

import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:collection/collection.dart';
import 'package:app_treino/ServiceProvider/NutricaoService.dart';
import 'package:app_treino/config/personal_icon_icons.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:get_it/get_it.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:mobx/mobx.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
part 'ControladorNutricao.g.dart';

class ControladorNutricao = _ControladorNutricaoBase with _$ControladorNutricao;

abstract class _ControladorNutricaoBase with Store {
  var mService = GetIt.I.get<NutricaoService>();
  final _controllerCliente = GetIt.I.get<ControladorCliente>();

  @observable
  num indexTelaInicial = 0;

  FlutterLocalNotificationsPlugin? flutterNotificationLocalPlugin;

  @observable
  DateTime dataSelecionadaCalendario = new DateTime.now();

  @observable
  double progressoRefeicoesConsumidasCard = 0;

  @observable
  bool gridSelecionado = true;

  @observable
  num? pesoAtualUsuario = 0;

  @observable
  num paginaAtualAnamnese = 0;

  @observable
  num quantidadeMlConsumidoNoDia = 0;

  @observable
  bool botaoContinuarAnamneseLiberado = false;

  @observable
  PlanoNutricional planoNutricionalAluno = PlanoNutricional();

  @observable
  ObservableList<HistoricoHidratacao> listaHistoricoHidratacao = ObservableList<HistoricoHidratacao>();

  @observable
  ObservableList<PesoUsuario> listaPesoUsuario = ObservableList<PesoUsuario>();

  @observable
  ObservableList<Novidades> listaNovidades = ObservableList<Novidades>();

  @observable
  ObservableList<PlanoNutricao> listaPlanoNutricao = ObservableList<PlanoNutricao>();

  @observable
  ObservableList<PlanoNutricao> listaPlanoNutricaoBackup = ObservableList<PlanoNutricao>();

  @observable
  ObservableList<AlimentoCriado> listaAlimentosCriados = ObservableList<AlimentoCriado>();

  @observable
  ServiceStatus statusConsultarListaRefeicoes = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultarRefeicoes = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultarListaIngredientes = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusListaCompras = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultarPlanosNutricionais = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultarNovidades = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusCarregandoDicas = ServiceStatus.Waiting;

  @observable
  StatusTelaInicial statusTelaPrincipal = StatusTelaInicial.Carregando;

  @observable
  ServiceStatus statusGerarRefeicoesPlano = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultarReceitaSubstituicao = ServiceStatus.Waiting;

  @observable
  ObservableList<Refeicoes> listaRefeicoes = ObservableList<Refeicoes>();

  @observable
  ObservableList<Refeicoes> listaRefeicoesBackup = ObservableList<Refeicoes>();

  @observable
  ObservableList<Ingredientes> listaIngredientes = ObservableList<Ingredientes>();

  @observable
  ObservableList<Ingredientes> listaIngredientesBackup = ObservableList<Ingredientes>();

  @observable
  ObservableList<Ingredientes> listaIngredientesAdicionados = ObservableList<Ingredientes>();

  @observable
  ObservableList<Refeicoes> listaRefeicoesAdicionados = ObservableList<Refeicoes>();

  @observable
  ObservableList<ReceitasAgrupadas> listaRefeicoesParaConfirmacao = ObservableList<ReceitasAgrupadas>();

  @observable
  ObservableList<ListaCompra> refeicoesListaCompras = ObservableList<ListaCompra>();

  @observable
  ObservableList<DicasNutri> listaDicas = ObservableList<DicasNutri>();

  @observable
  ObservableList<ItemListaCompras> listaCompras = ObservableList<ItemListaCompras>();

  @observable
  ObservableList<CalendarioNutricaoClass> retornoCalendario = ObservableList<CalendarioNutricaoClass>();

  @observable
  ObservableList<Refeicoes> listaRefeicoesParaSubstituicao = ObservableList<Refeicoes>();

  @observable
  ObservableList<Refeicoes> listaRefeicoesFavoritasBackup = ObservableList<Refeicoes>();

  @observable
  ObservableList<Refeicoes> listaRefeicoesFavoritas = ObservableList<Refeicoes>();

  @observable
  ObservableList<HistoricoRespiracao> listaHistoricoRespiracao = ObservableList<HistoricoRespiracao>();

  @observable
  DateTime? dataInicialListaCompras = DateTime.now();

  @observable
  DateTime? dataFinalListaCompras = DateTime.now();

  @observable
  num carboidratosConsumidoNoDia = 0;
  @observable
  num carboidratosQuantidadeTotalNoDia = 0;
  @observable
  num carboidratosConsumidoNoDiaProgresso = 0;

  @observable
  num proteinasConsumidoNoDia = 0;
  @observable
  num proteinasQuantidadeTotalNoDia = 0;
  @observable
  num proteinasConsumidoNoDiaProgresso = 0;

  @observable
  num gordurasConsumidoNoDia = 0;
  @observable
  num gordurasQuantidadeTotalNoDia = 0;
  @observable
  num gordurasConsumidoNoDiaProgresso = 0;

  @observable
  num fibrasConsumidoNoDia = 0;
  @observable
  num fibrasQuantidadeTotalNoDia = 0;
  @observable
  num fibrasConsumidoNoDiaProgresso = 0;

  @observable
  num? valorTotalConclusaoPlano = 0;

  @observable
  num? pesoMaximo = 0;
  @observable
  DateTime? dataPesoMaximo = DateTime.now();

  @observable
  num? pesoMinimo = 0;
  @observable
  DateTime? dataPesoMinimo = DateTime.now();

  @observable
  String quantiadeRefeicoesConsumidasDiaProgresso = '0';

  @observable
  ObservableList<ReceitaFavorita> listaReceitasFavoritas = ObservableList<ReceitaFavorita>();

  @observable
  ObservableList<PlanosNutricionaisFitStream> listaPlanoNutricionalFitstream = ObservableList<PlanosNutricionaisFitStream>();

  @observable
  PlanosNutricionaisFitStream planoNutricionalSelecionadoFitstream = PlanosNutricionaisFitStream();

  @observable
  ServiceStatus statusConsultarPlanoNutricionaisFitStream = ServiceStatus.Waiting;

  organizarTelaInicial() {
    if (planoNutricionalAluno.dataConfiguracaoPlano == null) {
      statusTelaPrincipal = StatusTelaInicial.ConfigurarPlano;
    } else if (planoNutricionalAluno.dataConfiguracaoPlano != null && planoNutricionalAluno.dataConfirmacaoReceitas == null) {
      statusTelaPrincipal = StatusTelaInicial.ConfirmarRefeicoes;
    } else if (planoNutricionalAluno.dataConfirmacaoReceitas != null && planoNutricionalAluno.dataInicioPlano == null) {
      statusTelaPrincipal = StatusTelaInicial.ConfigurarInicioTermino;
    } else if (planoNutricionalAluno.dataInicioPlano != null &&
        !UtilDataHora.dataJaPassouDeHoje(dateTime: UtilDataHora.getDataComHorasPersonalizada(dateTime: planoNutricionalAluno.dataInicioPlano, horas: 0, minutos: 0, segundos: 0))) {
      statusTelaPrincipal = StatusTelaInicial.ConfigurarInicioTermino;
    } else if (planoNutricionalAluno.dataInicioPlano != null &&
        UtilDataHora.dataJaPassouDeHoje(dateTime: UtilDataHora.getDataComHorasPersonalizada(dateTime: planoNutricionalAluno.dataInicioPlano, horas: 0, minutos: 0, segundos: 0)) &&
        !UtilDataHora.dataJaPassouDeHoje(dateTime: UtilDataHora.getDataComHorasPersonalizada(dateTime: planoNutricionalAluno.dataFimPlano, horas: 23, minutos: 59, segundos: 59))) {
      statusTelaPrincipal = StatusTelaInicial.Refeicoes;
    } else if (UtilDataHora.dataJaPassouDeHoje(dateTime: UtilDataHora.getDataComHorasPersonalizada(dateTime: planoNutricionalAluno.dataFimPlano, horas: 23, minutos: 59, segundos: 59))) {
      statusTelaPrincipal = StatusTelaInicial.SemPlano;
    }
  }

  @action
  popularDadosFicticios() {
    planoNutricionalAluno = PlanoNutricional();
    planoNutricionalAluno.nomePlano = 'Plano Personalizado';
    planoNutricionalAluno.quantidadeRefeicoes = 5;
    planoNutricionalAluno.variedade = 'alta';
    planoNutricionalAluno.metaPeso = 0;
    planoNutricionalAluno.pesoAtual = 0.0;
    planoNutricionalAluno.pesoInicial = 0;
    planoNutricionalAluno.metaHidratacao = 3000;
    planoNutricionalAluno.altura = 0;
    statusTelaPrincipal = StatusTelaInicial.DadosMocados;
  }

  consultarPesoUsuario({Function()? onSuccess}) async {
    listaPesoUsuario.clear();
    mService.consultarPesoUsuario(_controllerCliente.mUsuarioAuth!.uid!).then((value) {
      pesoAtualUsuario = 0;
      value.sort((a, b) => a.dataInclusao!.compareTo(b.dataInclusao!));
      listaPesoUsuario.addAll(value);
      pesoAtualUsuario = value.last.peso;
      calcularPesoMaximo();
      calcularPesoMinimo();
      onSuccess?.call();
    });
  }

  atualizarPesoUsuario({Function()? atingiuMetaPeso}) async {
    listaPesoUsuario.clear();
    mService.atualizarPesoUsuario(_controllerCliente.mUsuarioAuth!.uid!, double.parse(pesoAtualUsuario!.toStringAsFixed(2))).then((value) {
      pesoAtualUsuario = 0;
      value.sort((a, b) => a.dataInclusao!.compareTo(b.dataInclusao!));
      listaPesoUsuario.addAll(value);
      pesoAtualUsuario = value.last.peso;
      calcularPesoMaximo();
      calcularPesoMinimo();
      if (validarSeUsuarioAtingiuAMetaDePeso(value.last.peso)) {
        atingiuMetaPeso!();
      }
    });
  }

  bool validarSeUsuarioAtingiuAMetaDePeso(double? pesoAtual) {
    switch (planoNutricionalAluno.objetivo) {
      case 'emagrecer':
        return pesoAtual! <= planoNutricionalAluno.metaPeso!;
      case 'ganhar massa':
        return pesoAtual! >= planoNutricionalAluno.metaPeso!;
      default:
        return false;
    }
  }

  void atualizarMlConsumido() {
    quantidadeMlConsumidoNoDia = quantidadeMlConsumidoNoDia + planoNutricionalAluno.tamanhoCopo!;
    mService.atualizarMlConsumido(_controllerCliente.mUsuarioAuth!.uid!, planoNutricionalAluno.tamanhoCopo ?? 250).then((value) {
      quantidadeMlConsumidoNoDia = somarItens(value);
    });
  }

  void removerMlConsumido() {
    if (quantidadeMlConsumidoNoDia > 0) {
      quantidadeMlConsumidoNoDia = quantidadeMlConsumidoNoDia - (planoNutricionalAluno.tamanhoCopo ?? 250);
      mService.removerMlConsumido(_controllerCliente.mUsuarioAuth!.uid!, planoNutricionalAluno.tamanhoCopo ?? 250).then((value) {
        quantidadeMlConsumidoNoDia = somarItens(value);
      });
    }
  }

  void consultarHistoricoHidratacaoPorData({required DateTime dataInicial, DateTime? dataFinal}) {
    mService.consultarMlConsumidoPorData(_controllerCliente.mUsuarioAuth!.uid!, dataInicial.toString(), dataFinal == null ? DateTime.now().toString() : dataFinal.toString()).then((value) {
      quantidadeMlConsumidoNoDia = somarItens(value);
    });
  }

  void consultarHistoricoHidratacao({DateTime? dataInicial, DateTime? dataFinal, Function()? onSuccess}) {
    mService
        .consultarMlConsumidoPorData(
            _controllerCliente.mUsuarioAuth!.uid!, DateTime.now().subtract(const Duration(days: 15)).toString(), DateTime.now().add(const Duration(days: 15)).toString())
        .then((retornoHistorico) {
      listaHistoricoHidratacao.clear();
      var listaTemporaria = ObservableList<HistoricoHidratacao>();
      var listaAgrupadaPorData =
          groupBy(retornoHistorico, (HistoricoHidratacao item) => UtilDataHora.getDataComHorasPersonalizada(dateTime: item.dataInclusao, horas: 0, minutos: 0, segundos: 0));
      listaAgrupadaPorData.forEach((key, value) {
        int sum = 0;
        for (final i in value) {
          if (i.adicao ?? true) {
            sum = sum + (i.ml as int);
          } else {
            sum = sum - (i.ml as int);
          }
        }
        listaTemporaria.add(HistoricoHidratacao(dataInclusao: key, ml: sum));
      });
      listaHistoricoHidratacao.addAll(listaTemporaria);
      onSuccess?.call();
    });
  }

  int somarItens(List<HistoricoHidratacao> numbers) {
    int sum = 0;
    for (final i in numbers) {
      if (i.adicao ?? true) {
        sum = sum + (i.ml as int);
      } else {
        sum = sum - (i.ml as int);
      }
    }
    return sum < 0 ? 0 : sum;
  }

  void atualizarHorariosRefeicoes({Function()? sucesso, Function()? falha, required Function()? carregando}) {
    carregando?.call();
    mService.atualizarHorarioRefeicoes(_controllerCliente.mUsuarioAuth!.uid!, planoNutricionalAluno.horarioRefeicao!.toJson()).then((value) {
      sucesso!();
    }).catchError((error) {
      falha!();
    });
  }

  void enviarDadosMinhaAcademia() {
    var email = _controllerCliente.mUsuarioLogado?.email ?? '';
    if (email.isEmpty) {
      email = _controllerCliente.mUsuarioLogado?.username ?? '';
    }
    mService
        .enviarDadosMinhaAcademia(GetIt.I.get<ControladorApp>().chave ?? '', GetIt.I.get<ControladorCliente>().mUsuarioLogado?.nome ?? '', Platform.isIOS ? 'iOS' : 'Android', email)
        .then((value) {})
        .catchError((error) {});
  }

  void configurarPlanoNutricional(PlanoNutricional dadosPlano) {
    dadosPlano.usuarioApp = _controllerCliente.mUsuarioAuth!.uid;
    dadosPlano.horarioRefeicao = prepararHorarioRefeicoesInicial();
    dadosPlano.lembretesNotificacoes = prepararNotificacoesInicial();
    mService.configurarPlanoNutricional(dadosPlano.toJson()).then((value) {
      consultarPlanoNutricional(dataConsulta: DateTime.now());
    }).catchError((error) {
      organizarTelaInicial();
    });
  }

  String? retornarHorarioRefeicao(String? tipo) {
    switch (tipo) {
      case 'CAFE_MANHA':
        return planoNutricionalAluno.horarioRefeicao!.cafeManha;
      case 'ALMOCO':
        return planoNutricionalAluno.horarioRefeicao!.almoco;
      case 'JANTAR':
        return planoNutricionalAluno.horarioRefeicao!.jantar;
      case 'LANCHE_MANHA':
        return planoNutricionalAluno.horarioRefeicao!.lancheManha;
      case 'LANCHE_TARDE':
        return planoNutricionalAluno.horarioRefeicao!.lancheTarde;
      case 'CEIA':
        return planoNutricionalAluno.horarioRefeicao!.ceia;
      default:
        return tipo;
    }
  }

  prepararHorarioRefeicoesInicial() {
    var horarios = HorarioRefeicao();
    horarios.cafeManha = '07:00';
    horarios.lancheManha = '09:00';
    horarios.almoco = '12:00';
    horarios.lancheTarde = '16:00';
    horarios.jantar = '20:00';
    horarios.ceia = '22:00';
    return horarios;
  }

  prepararNotificacoesInicial() {
    var horarios = LembretesNotificacoes();
    horarios.cafeManha = true;
    horarios.lancheManha = true;
    horarios.almoco = true;
    horarios.lancheTarde = true;
    horarios.jantar = true;
    horarios.ceia = true;
    horarios.considerarTempoPreparo = false;
    return horarios;
  }

  void consultarPlanoNutricional({Function()? sucesso, Function()? falha, required DateTime dataConsulta}) {
    statusTelaPrincipal = StatusTelaInicial.Carregando;
    planoNutricionalAluno = PlanoNutricional();
    consultarHistoricoRespiracao();
    mService.consultarPlanoNutricional(_controllerCliente.mUsuarioAuth!.uid!, dataConsulta.toString()).then((value) {
      if (value.dataConfirmacaoReceitas != null) {
        planoNutricionalAluno = value;
        mService.consultarReceitasFavoritas(_controllerCliente.mUsuarioAuth!.uid!).then((retornoFavotitas) {
          mService.consultarReceitasGeradas(_controllerCliente.mUsuarioAuth!.uid!, dataConsulta: dataConsulta.toString()).then((value) {
            for (final element in value) {
              if (element.receita != null) {
                if (retornoFavotitas.isNotEmpty) {
                  for (final receitaFavorita in retornoFavotitas) {
                    if (receitaFavorita.refReceita == element.receita!.id) {
                      element.receita!.favorita = receitaFavorita.favorita;
                    }
                  }
                }
                element.receita!.selecionado = false;
              }
            }
            value.sort((a, b) => a.ordem!.compareTo(b.ordem!));
            planoNutricionalAluno.refeicoes = [];
            planoNutricionalAluno.refeicoes!.addAll(value);
            agendarNotificacoesBeberAgua();
            prepararRefeicoesParaConfirmacao(value);
            favoritarReceitasDaLista();
            processarQuantidadeRefeicoesConsumidas();
            consultarPesoUsuario();
            consultarHistoricoHidratacaoPorData(dataInicial: DateTime.now(), dataFinal: DateTime.now().add(const Duration(days: 30)));
            agendarNotificacaoRefeicoes();
            consultarCalendario(falha: () {
              organizarTelaInicial();
              sucesso!();
            }, sucesso: () {
              organizarTelaInicial();
              sucesso!();
            });
          }).catchError((error) {
            falha!();
          }).catchError((error) {
            falha!();
          });
        });
      } else {
        planoNutricionalAluno = value;
        consultarPesoUsuario();
        consultarHistoricoHidratacaoPorData(dataInicial: DateTime.now(), dataFinal: DateTime.now().add(const Duration(days: 30)));
        organizarTelaInicial();
        sucesso!();
      }
    }).catchError((error) {
      if (error.toString().contains('O usuário não tem plano configurado')) {
        statusTelaPrincipal = StatusTelaInicial.ResponderAnamnese;
      } else {
        falha!();
      }
    });
  }

  double processarProgressoPeso() {
    var progresso = ((planoNutricionalAluno.pesoInicial! - pesoAtualUsuario!) / (planoNutricionalAluno.pesoInicial! - planoNutricionalAluno.metaPeso!));
    return (progresso >= 1 ? 1 : progresso).toDouble();
  }

  String processarQuantiadeCaloriasConsumidas() {
    double sum = 0;
    if (planoNutricionalAluno.refeicoes == null) {
      return '0';
    }
    for (final i in planoNutricionalAluno.refeicoes!) {
      if (i.dataRefeicao != null) {
        if (i.ingredientes != null) {
          for (final ing in i.ingredientes!.ingredientes!) {
            sum = sum + ing.valorEnergetico!;
          }
          for (final ing in i.ingredientes!.refeicoes!) {
            sum = sum + ing.valorEnergetico!;
          }
        } else {
          sum = sum + i.receita!.valorEnergetico!;
        }
      }
    }
    return '${sum.toStringAsFixed(0)}';
  }

  String processarQuantiadeCaloriasTotais() {
    double sum = 0;
    if (planoNutricionalAluno.refeicoes == null) {
      return '0';
    }
    for (final i in planoNutricionalAluno.refeicoes!) {
      sum = sum + i.receita!.valorEnergetico!;
    }
    return '${sum.toStringAsFixed(0)}';
  }

  void calcularPesoMaximo() {
    var peso = listaPesoUsuario.reduce((a, b) => a.peso! > b.peso! ? a : b);
    pesoMaximo = peso.peso;
    dataPesoMaximo = peso.dataInclusao;
  }

  void calcularPesoMinimo() {
    var peso = listaPesoUsuario.reduce((a, b) => a.peso! < b.peso! ? a : b);
    pesoMinimo = peso.peso;
    dataPesoMinimo = peso.dataInclusao;
  }

  String? prepararTipoReceita(String? tipo) {
    switch (tipo) {
      case 'CAFE_MANHA':
        return localizedString('breakfast');
      case 'ALMOCO':
        return localizedString('lunch');
      case 'JANTAR':
        return localizedString('dinner');
      case 'LANCHE_MANHA':
        return localizedString('brunch');
      case 'LANCHE_TARDE':
        return localizedString('afternoon_snack');
      case 'CEIA':
        return localizedString('supper_meal');
      default:
        return tipo;
    }
  }

  String? prepararTipoReceitaIconeNotificacao(String? tipo) {
    switch (tipo) {
      case 'CAFE_MANHA':
        return '☕';
      case 'ALMOCO':
        return '🥙';
      case 'JANTAR':
        return '🥗';
      case 'LANCHE_MANHA':
        return '🥞';
      case 'LANCHE_TARDE':
        return '🍪';
      case 'CEIA':
        return '🧁';
      default:
        return tipo;
    }
  }

  bool? vaiExibirNotificacao(String? tipo, LembretesNotificacoes? lembretes) {
    if (lembretes == null) {
      return true;
    } else {
      switch (tipo) {
        case 'CAFE_MANHA':
          return lembretes.cafeManha;
        case 'ALMOCO':
          return lembretes.almoco;
        case 'JANTAR':
          return lembretes.jantar;
        case 'LANCHE_MANHA':
          return lembretes.lancheManha;
        case 'LANCHE_TARDE':
          return lembretes.lancheTarde;
        case 'CEIA':
          return lembretes.ceia;
        default:
          return true;
      }
    }
  }

  String? inverterTipoReceita(String? tipo) {
    switch (tipo) {
      case 'LANCHE_MANHA':
        return 'LANCHE';
      case 'LANCHE_TARDE':
        return 'LANCHE';
      default:
        return tipo;
    }
  }

  prepararTipoReceitaIcone(String? tipo) {
    switch (tipo) {
      case 'CAFE_MANHA':
        return PersonalIcon.sunrise;
      case 'ALMOCO':
        return PersonalIcon.sun;
      case 'JANTAR':
        return PersonalIcon.moon;
      case 'LANCHE':
        return PersonalIcon.sun;
      case 'CEIA':
        return PersonalIcon.moon;
      default:
        return PersonalIcon.sun;
    }
  }

  processarQuantidadeRefeicoesConsumidas() {
    try {
      int sum = 0;
      num carboidratos = 0;
      num proteinas = 0;
      num fibras = 0;
      num gorduras = 0;
      num carboidratosTotal = 0;
      num proteinasTotal = 0;
      num fibrasTotal = 0;
      num gordurasTotal = 0;
      for (int i = 0; i < planoNutricionalAluno.refeicoes!.length; i++) {
        carboidratosTotal = carboidratosTotal + planoNutricionalAluno.refeicoes![i].receita!.carboidrato!;
        proteinasTotal = proteinasTotal + planoNutricionalAluno.refeicoes![i].receita!.proteina!;
        fibrasTotal = fibrasTotal + planoNutricionalAluno.refeicoes![i].receita!.fibra!;
        gordurasTotal = gordurasTotal + planoNutricionalAluno.refeicoes![i].receita!.gordura!;
        if (planoNutricionalAluno.refeicoes![i].dataRefeicao != null) {
          sum = sum + 1;
          if (planoNutricionalAluno.refeicoes![i].ingredientes != null) {
            for (final teste in planoNutricionalAluno.refeicoes![i].ingredientes!.ingredientes!) {
              carboidratos = carboidratos + (teste.carboidrato == null ? 0.0 : teste.carboidrato!);
              proteinas = proteinas + (teste.proteina == null ? 0.0 : teste.proteina!);
              fibras = fibras + (teste.fibra == null ? 0.0 : teste.fibra!);
              gorduras = gorduras + (teste.gordura == null ? 0.0 : teste.gordura!);
            }
            for (final teste in planoNutricionalAluno.refeicoes![i].ingredientes!.refeicoes!) {
              carboidratos = carboidratos + teste.carboidrato!;
              proteinas = proteinas + teste.proteina!;
              fibras = fibras + teste.fibra!;
              gorduras = gorduras + teste.gordura!;
            }
          } else {
            carboidratos = carboidratos + planoNutricionalAluno.refeicoes![i].receita!.carboidrato!;
            proteinas = proteinas + planoNutricionalAluno.refeicoes![i].receita!.proteina!;
            fibras = fibras + planoNutricionalAluno.refeicoes![i].receita!.fibra!;
            gorduras = gorduras + planoNutricionalAluno.refeicoes![i].receita!.gordura!;
          }
        }
      }
      //CALCULOS CARBOIDRATOS
      carboidratosConsumidoNoDia = carboidratos;
      carboidratosQuantidadeTotalNoDia = carboidratosTotal;
      carboidratosConsumidoNoDiaProgresso = (carboidratosConsumidoNoDia / carboidratosQuantidadeTotalNoDia) > 1 ? 1 : (carboidratosConsumidoNoDia / carboidratosQuantidadeTotalNoDia);
      //CALCULOS PROTEINAS
      proteinasConsumidoNoDia = proteinas;
      proteinasQuantidadeTotalNoDia = proteinasTotal;
      proteinasConsumidoNoDiaProgresso = (proteinasConsumidoNoDia / proteinasQuantidadeTotalNoDia) > 1 ? 1 : (proteinasConsumidoNoDia / proteinasQuantidadeTotalNoDia);
      //CALCULOS FIBRAS
      fibrasConsumidoNoDia = fibras;
      fibrasQuantidadeTotalNoDia = fibrasTotal;
      fibrasConsumidoNoDiaProgresso = (fibrasConsumidoNoDia / fibrasQuantidadeTotalNoDia) > 1 ? 1 : (fibrasConsumidoNoDia / fibrasQuantidadeTotalNoDia);
      //CALCULOS GORDURAS
      gordurasConsumidoNoDia = gorduras;
      gordurasQuantidadeTotalNoDia = gordurasTotal;
      gordurasConsumidoNoDiaProgresso = (gordurasConsumidoNoDia / gordurasQuantidadeTotalNoDia) > 1 ? 1 : (gordurasConsumidoNoDia / gordurasQuantidadeTotalNoDia);
      var progresso = (sum / planoNutricionalAluno.refeicoes!.length).toDouble();
      progressoRefeicoesConsumidasCard = progresso > 1 ? 1 : progresso;
      quantiadeRefeicoesConsumidasDiaProgresso = '$sum/${planoNutricionalAluno.refeicoes!.length}';
      organizarTelaInicial();
    } catch (error) {}
  }

  void consultarRefeicoes() {
    if (listaRefeicoesBackup.isEmpty) {
      statusConsultarListaRefeicoes = ServiceStatus.Waiting;
      mService.consultarRefeicoes().then((value) {
        mService.consultarReceitasFavoritas(_controllerCliente.mUsuarioAuth!.uid!).then((retornoFavotitas) {
          for (final element in value) {
            for (final receitaFavorita in retornoFavotitas) {
              if (receitaFavorita.refReceita == element.id) {
                element.favorita = receitaFavorita.favorita;
              }
            }
            element.selecionado = false;
          }
          listaRefeicoes.addAll(value);
          listaRefeicoesBackup.addAll(value);
          favoritarReceitasDaLista();
          statusConsultarListaRefeicoes = ServiceStatus.Done;
        });
      }).catchError((error) {
        statusConsultarListaRefeicoes = ServiceStatus.Error;
      });
    } else {
      listaRefeicoes = listaRefeicoesBackup;
      statusConsultarListaRefeicoes = ServiceStatus.Done;
    }
  }

  void gerarRefeicoesDoPlano() {
    planoNutricionalAluno.refeicoes = [];
    listaRefeicoesParaConfirmacao.clear();
    statusGerarRefeicoesPlano = ServiceStatus.Waiting;
    mService.gerarReceitasPlanoNutricional(_controllerCliente.mUsuarioAuth!.uid!).then((value) {
      planoNutricionalAluno.dataConfiguracaoPlano = DateTime.now();
      value.sort((a, b) => a.ordem!.compareTo(b.ordem!));
      planoNutricionalAluno.refeicoes!.addAll(value);
      prepararRefeicoesParaConfirmacao(value);
    }).catchError((error) {
      statusGerarRefeicoesPlano = ServiceStatus.Error;
    });
  }

  prepararRefeicoesParaConfirmacao(List<RefeicoesDoPlano> refeicoesDoServico) {
    listaRefeicoesParaConfirmacao.clear();
    List<ReceitasAgrupadas> listaReceitasAgrupadas = [];
    List<ReceitasAgrupadas> listaVazia = [];
    final refeicoesAgrupadasPorTipo = groupBy(refeicoesDoServico, (RefeicoesDoPlano e) {
      return e.receitaPara;
    });
    refeicoesAgrupadasPorTipo.forEach((keyGrupo, valueGrupo) {
      final groups = groupBy(valueGrupo, (RefeicoesDoPlano e) {
        return e.refReceita;
      });
      groups.forEach((key, value) {
        listaReceitasAgrupadas.add(ReceitasAgrupadas(nome: keyGrupo, receita: [value.first.receita]));
      });
    });
    final finallist = groupBy(listaReceitasAgrupadas, (ReceitasAgrupadas e) {
      return e.nome;
    });
    finallist.forEach((key, value) {
      List<Refeicoes?> receita = [];
      for (final element in value) {
        receita.add(element.receita!.first);
      }
      listaVazia.add(ReceitasAgrupadas(nome: key, receita: receita));
    });
    listaRefeicoesParaConfirmacao.addAll(listaVazia);
    statusGerarRefeicoesPlano = ServiceStatus.Done;
  }

  void atualizarDataInicioTerminoPlano({Function()? sucesso, Function()? falha, required Function()? carregando, required DateTime novaDataInicio}) {
    carregando?.call();
    mService.atualizarDataInicioTerminoPlano(_controllerCliente.mUsuarioAuth!.uid!, novaDataInicio.toString(), duracao: planoNutricionalAluno.duracao).then((value) {
      var backup = planoNutricionalAluno.refeicoes;
      if (backup == null) {
        mService.consultarReceitasGeradas(_controllerCliente.mUsuarioAuth!.uid!, dataConsulta: novaDataInicio.toString()).then((receitas) {
          mService.calendarioNutricao(_controllerCliente.mUsuarioAuth!.uid!, novaDataInicio.toString()).then((calendario) {
            planoNutricionalAluno = value;
            receitas.sort((a, b) => a.ordem!.compareTo(b.ordem!));
            planoNutricionalAluno.refeicoes = [];
            planoNutricionalAluno.refeicoes = receitas;
            retornoCalendario.clear();
            retornoCalendario.addAll(prepararCalendario(calendario));
            processarQuantidadeRefeicoesConsumidas();
            sucesso!();
          }).catchError((error) {
            falha!();
          });
        });
      } else {
        mService.calendarioNutricao(_controllerCliente.mUsuarioAuth!.uid!, novaDataInicio.toString()).then((calendario) {
          planoNutricionalAluno = value;
          backup.sort((a, b) => a.ordem!.compareTo(b.ordem!));
          planoNutricionalAluno.refeicoes = [];
          planoNutricionalAluno.refeicoes = backup;
          retornoCalendario.clear();
          retornoCalendario.addAll(prepararCalendario(calendario));
          processarQuantidadeRefeicoesConsumidas();
          sucesso!();
        }).catchError((error) {
          falha!();
        });
      }
    }).catchError((error) {
      falha!();
    });
  }

  void consumirRefeicao({Function()? sucesso, Function()? falha, required Function()? carregando, required bool consumida, required DateTime data, required String id}) {
    carregando?.call();
    mService.consumirRefeicao(_controllerCliente.mUsuarioAuth!.uid!, data.toString(), consumida, id).then((planoNutricional) {
      mService.consultarReceitasGeradas(_controllerCliente.mUsuarioAuth!.uid!, dataConsulta: data.toString()).then((value) {
        value.sort((a, b) => a.ordem!.compareTo(b.ordem!));
        planoNutricionalAluno.refeicoes = [];
        planoNutricionalAluno.refeicoes!.addAll(value);
        mService.calendarioNutricao(_controllerCliente.mUsuarioAuth!.uid!, planoNutricionalAluno.dataInicioPlano!.toString()).then((calendario) {
          retornoCalendario.clear();
          retornoCalendario.addAll(prepararCalendario(calendario));
          processarQuantidadeRefeicoesConsumidas();
          statusConsultarRefeicoes = ServiceStatus.Done;
          sucesso!();
        }).catchError((error) {
          falha!();
        });
      }).catchError((error) {
        falha!();
      });
    }).catchError((error) {
      falha!();
    });
  }

  void consultarCalendario({Function()? sucesso, Function()? falha, DateTime? dataConsulta}) {
    mService.calendarioNutricao(_controllerCliente.mUsuarioAuth!.uid!, planoNutricionalAluno.dataInicioPlano!.toString()).then((value) {
      retornoCalendario.clear();
      retornoCalendario.addAll(prepararCalendario(value));
    }).catchError((error) {
      falha!();
    });
  }

  prepararCalendario(List<RefeicoesDoPlano> refeicoesDoPlano) {
    List<CalendarioNutricaoClass> calendario = [];
    final listaSeparadaPorTipoRefeicao = groupBy(refeicoesDoPlano, (RefeicoesDoPlano item) => item.receitaParaODia);
    listaSeparadaPorTipoRefeicao.forEach((key, value) {
      var quantidade = 0.0;
      for (final element in value) {
        if (element.dataRefeicao != null) {
          quantidade = quantidade + 1;
        }
      }
      calendario.add(CalendarioNutricaoClass(dia: key, progresso: (quantidade / value.length.toDouble()).toDouble()));
    });
    return calendario;
  }

  void consultarReceitasGeradas() {
    statusGerarRefeicoesPlano = ServiceStatus.Waiting;
    mService.consultarReceitasGeradas(_controllerCliente.mUsuarioAuth!.uid!).then((value) {
      value.sort((a, b) => a.ordem!.compareTo(b.ordem!));
      planoNutricionalAluno.refeicoes = [];
      planoNutricionalAluno.refeicoes!.addAll(value);
      prepararRefeicoesParaConfirmacao(value);
    }).catchError((error) {});
  }

  void consultarRefeicoesPorData(DateTime dataConsulta) {
    statusConsultarRefeicoes = ServiceStatus.Waiting;
    mService.consultarReceitasFavoritas(_controllerCliente.mUsuarioAuth!.uid!).then((retornoFavotitas) {
      mService.consultarReceitasGeradas(_controllerCliente.mUsuarioAuth!.uid!, dataConsulta: dataConsulta.toString()).then((value) {
        for (final element in value) {
          for (final receitaFavorita in retornoFavotitas) {
            if (receitaFavorita.refReceita == element.receita!.id) {
              element.receita!.favorita = receitaFavorita.favorita;
            }
          }
          element.receita!.selecionado = false;
        }
        value.sort((a, b) => a.ordem!.compareTo(b.ordem!));
        planoNutricionalAluno.refeicoes = [];
        planoNutricionalAluno.refeicoes!.addAll(value);
        prepararRefeicoesParaConfirmacao(value);
        processarQuantidadeRefeicoesConsumidas();
        if (UtilDataHora.getDataComHorasPersonalizada(dateTime: DateTime.now(), horas: 0, minutos: 0, segundos: 0) ==
            UtilDataHora.getDataComHorasPersonalizada(dateTime: dataConsulta, horas: 0, minutos: 0, segundos: 0)) {
          agendarNotificacaoRefeicoes();
        }
        consultarCalendario();
        favoritarReceitasDaLista();
        statusConsultarRefeicoes = ServiceStatus.Done;
      });
    }).catchError((error) {
      statusConsultarRefeicoes = ServiceStatus.Error;
    });
  }

  void consultarRefeicoesPorDataComCallback({DateTime? dataConsulta, required Function()? carregando, Function()? sucesso, Function()? erro}) {
    carregando?.call();
    mService.consultarReceitasFavoritas(_controllerCliente.mUsuarioAuth!.uid!).then((retornoFavotitas) {
      mService.consultarReceitasGeradas(_controllerCliente.mUsuarioAuth!.uid!, dataConsulta: dataConsulta!.toString()).then((value) {
        for (final element in value) {
          for (final receitaFavorita in retornoFavotitas) {
            if (receitaFavorita.refReceita == element.receita!.id) {
              element.receita!.favorita = receitaFavorita.favorita;
            }
          }
          element.receita!.selecionado = false;
        }
        value.sort((a, b) => a.ordem!.compareTo(b.ordem!));
        planoNutricionalAluno.refeicoes = [];
        planoNutricionalAluno.refeicoes!.addAll(value);
        prepararRefeicoesParaConfirmacao(value);
        processarQuantidadeRefeicoesConsumidas();
        consultarCalendario();
        favoritarReceitasDaLista();
        if (UtilDataHora.getDataComHorasPersonalizada(dateTime: DateTime.now(), horas: 0, minutos: 0, segundos: 0) ==
            UtilDataHora.getDataComHorasPersonalizada(dateTime: dataConsulta, horas: 0, minutos: 0, segundos: 0)) {
          agendarNotificacaoRefeicoes();
        }
        sucesso!();
      });
    }).catchError((error) {
      erro!();
    });
  }

  void consultarReceitasParaSubstituicao(DadosSubstituicao dadosConsulta, List<String?> receitasEscolhidas) {
    listaRefeicoesParaSubstituicao.clear();
    statusConsultarReceitaSubstituicao = ServiceStatus.Waiting;
    mService.consultarReceitasParaSubstituicao(dadosConsulta.toJson()).then((value) {
      for (final receitasEscolhida in receitasEscolhidas) {
        for (final val in value) {
          if (receitasEscolhida == val.id) {
            val.selecionado = true;
            break;
          }
        }
      }
      value.sort((a, b) {
        if (b.selecionado ?? false) {
          return 1;
        }
        return -1;
      });
      listaRefeicoesParaSubstituicao.addAll(value);
      statusConsultarReceitaSubstituicao = ServiceStatus.Done;
    }).catchError((error) {
      statusConsultarReceitaSubstituicao = ServiceStatus.Error;
    });
  }

  void substituirRefeicao({Function()? sucesso, Function()? falha, required Function()? carregando, required ReceitaSubstituida dadosReceita}) {
    carregando?.call();
    dadosReceita.usuarioApp = _controllerCliente.mUsuarioAuth!.uid;
    mService.substituirReceita(dadosReceita.toJson()).then((value) {
      sucesso!();
    }).catchError((error) {
      falha!();
    });
  }

  void confirmarReceitasGeradas({Function()? sucesso, Function()? falha, required Function()? carregando}) {
    carregando?.call();
    mService.consultarReceitasGeradas(_controllerCliente.mUsuarioAuth!.uid!, dataConsulta: DateTime.now().toString()).then((receitas) {
      mService.confirmarReceitasGeradas(_controllerCliente.mUsuarioAuth!.uid!).then((value) {
        planoNutricionalAluno = value;
        receitas.sort((a, b) => a.ordem!.compareTo(b.ordem!));
        planoNutricionalAluno.refeicoes = [];
        planoNutricionalAluno.refeicoes!.addAll(receitas);
        organizarTelaInicial();
        sucesso!();
      }).catchError((error) {
        organizarTelaInicial();
        falha!();
      });
    }).catchError((error) {
      falha!();
    });
  }

  String calculadoraIMC() {
    var peso = planoNutricionalAluno.pesoAtual!;
    var altura = planoNutricionalAluno.altura!;
    return (peso / (altura * altura)).toStringAsFixed(1);
  }

  Future<void> agendarNotificacaoRefeicoes() async {
    tz.initializeTimeZones();
    var _timezone = await FlutterTimezone.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(_timezone));
    int idT = 1000;
    for (final _ in planoNutricionalAluno.refeicoes!) {
      idT = idT + 1;
      await flutterNotificationLocalPlugin!.cancel(idT);
    }
    int id = 1000;
    for (final refeicao in planoNutricionalAluno.refeicoes!) {
      if (vaiExibirNotificacao(refeicao.receitaPara, planoNutricionalAluno.lembretesNotificacoes)!) {
        id = id + 1;
        var horas = ajustarHorarios(refeicao: refeicao, mostrarHoras: true);
        var minutos = ajustarHorarios(refeicao: refeicao, mostrarHoras: false);
        DateTime? dataNotificacao = UtilDataHora.getDataComHorasPersonalizada(dateTime: refeicao.receitaParaODia, horas: horas, minutos: minutos, segundos: 0);
        if (planoNutricionalAluno.lembretesNotificacoes!.considerarTempoPreparo!) {
          dataNotificacao = dataNotificacao!.subtract(const Duration(minutes: 30));
        }
        DateTime dataAtual = DateTime.now();
        if (dataNotificacao!.microsecondsSinceEpoch > dataAtual.microsecondsSinceEpoch) {
          try {
            await flutterNotificationLocalPlugin!.zonedSchedule(
                id,
                '${prepararTipoReceita(refeicao.receitaPara)} '
                    '${prepararTipoReceitaIconeNotificacao(refeicao.receitaPara)}',
                'Separe os ingredientes, está na hora de começar a preparar: ${refeicao.receita!.nome}',
                tz.TZDateTime.now(tz.local).add(Duration(
                  seconds: diferencaEntreDatasSegundos(dataNotificacao),
                )),
                const NotificationDetails(android: AndroidNotificationDetails('your channel id', 'your channel name')),
                // ignore: deprecated_member_use
                androidAllowWhileIdle: true,
                payload: 'refeicao',
                uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime);
            //print(
            //    'Agendei a notificação id: $id, tipo: ${refeicao.receitaPara} no horário das $horas:$minutos');
          } catch (e) {}
        }
      }
    }
  }

  ajustarHorarios({required RefeicoesDoPlano refeicao, bool? mostrarHoras}) {
    switch (refeicao.receitaPara) {
      case 'CAFE_MANHA':
        return mostrarHoras! ? num.parse(planoNutricionalAluno.horarioRefeicao!.cafeManha!.split(':').first) : num.parse(planoNutricionalAluno.horarioRefeicao!.cafeManha!.split(':').last);
      case 'ALMOCO':
        return mostrarHoras! ? num.parse(planoNutricionalAluno.horarioRefeicao!.almoco!.split(':').first) : num.parse(planoNutricionalAluno.horarioRefeicao!.almoco!.split(':').last);
      case 'JANTAR':
        return mostrarHoras! ? num.parse(planoNutricionalAluno.horarioRefeicao!.jantar!.split(':').first) : num.parse(planoNutricionalAluno.horarioRefeicao!.jantar!.split(':').last);
      case 'LANCHE_MANHA':
        return mostrarHoras!
            ? num.parse(planoNutricionalAluno.horarioRefeicao!.lancheManha!.split(':').first)
            : num.parse(planoNutricionalAluno.horarioRefeicao!.lancheManha!.split(':').last);
      case 'LANCHE_TARDE':
        return mostrarHoras!
            ? num.parse(planoNutricionalAluno.horarioRefeicao!.lancheTarde!.split(':').first)
            : num.parse(planoNutricionalAluno.horarioRefeicao!.lancheTarde!.split(':').last);
      case 'CEIA':
        return mostrarHoras! ? num.parse(planoNutricionalAluno.horarioRefeicao!.ceia!.split(':').first) : num.parse(planoNutricionalAluno.horarioRefeicao!.ceia!.split(':').last);
      default:
        return mostrarHoras! ? num.parse(planoNutricionalAluno.horarioRefeicao!.cafeManha!.split(':').first) : num.parse(planoNutricionalAluno.horarioRefeicao!.cafeManha!.split(':').last);
    }
  }

  int diferencaEntreDatasSegundos(DateTime dataRefeicao) {
    Duration duracao = DateTime.now().difference(dataRefeicao);
    String differenceInYears = (duracao.inSeconds).floor().toString();
    return num.parse(differenceInYears).abs() as int;
  }

  void consultarIngredientes() {
    if (listaIngredientes.isEmpty) {
      statusConsultarListaIngredientes = ServiceStatus.Waiting;
      mService.consultarAlimentosCriados(_controllerCliente.mUsuarioAuth!.uid!).then((retornoAlimentosCriados) {
        listaIngredientes.clear();
        for (final element in retornoAlimentosCriados) {
          listaIngredientes.add(Ingredientes(
              fibra: 0,
              nome: element.nome!,
              valorEnergetico: element.calorias!.toDouble(),
              gordura: element.gordura!.toDouble(),
              carboidrato: element.carboidratos!.toDouble(),
              proteina: element.proteinas!.toDouble()));
        }
        mService.consultarIngredientes().then((value) {
          listaIngredientes.addAll(value);
          for (final element in listaIngredientes) {
            element.selecionado = false;
          }
          listaIngredientes.sort((itemA, itemB) => itemA.nome!.compareTo(itemB.nome!));
          listaIngredientesBackup = listaIngredientes;
          statusConsultarListaIngredientes = ServiceStatus.Done;
        }).catchError((error) {
          statusConsultarListaIngredientes = ServiceStatus.Error;
        });
      }).catchError((error) {
        statusConsultarListaIngredientes = ServiceStatus.Error;
      });
    }
  }

  Future<void> adicionarIngredienteNaLista(
      {required Function() inicio, required Function()? carregando, required Function()? sucesso, Ingredientes? dadosIngrediente, Refeicoes? dadosRefeicoes}) async {
    inicio();
    await Future.delayed(const Duration(milliseconds: 500));
    carregando?.call();
    if (dadosIngrediente != null) {
      listaIngredientesAdicionados.add(dadosIngrediente);
    }
    if (dadosRefeicoes != null) {
      listaRefeicoesAdicionados.add(dadosRefeicoes);
    }
    await Future.delayed(const Duration(milliseconds: 800));
    sucesso?.call();
  }

  Future<void> limparListaIngredientes(String? nome) async {
    if (nome == null) {
      listaIngredientesAdicionados.clear();
      listaRefeicoesAdicionados.clear();
    } else {
      for (var i = 0; i < listaIngredientesAdicionados.length; i++) {
        if (listaIngredientesAdicionados[i].nome == nome) {
          listaIngredientesAdicionados.removeAt(i);
          break;
        }
      }
      for (var i = 0; i < listaRefeicoesAdicionados.length; i++) {
        if (listaRefeicoesAdicionados[i].nome == nome) {
          listaRefeicoesAdicionados.removeAt(i);
          break;
        }
      }
    }
  }

  void consumirRefeicaoAvulsa({Function()? sucesso, Function()? falha, required Function()? carregando, required bool consumida, DateTime? data, required String id}) {
    carregando?.call();
    IngredientesAdicionados ingredientes = IngredientesAdicionados();
    ingredientes.ingredientes = listaIngredientesAdicionados;
    ingredientes.refeicoes = listaRefeicoesAdicionados;
    mService.consumirRefeicaoAvulsa(_controllerCliente.mUsuarioAuth!.uid!, consumida, id, ingredientes.toJson()).then((planoNutricional) {
      mService.consultarReceitasGeradas(_controllerCliente.mUsuarioAuth!.uid!, dataConsulta: data!.toString()).then((value) {
        value.sort((a, b) => a.ordem!.compareTo(b.ordem!));
        planoNutricionalAluno.refeicoes = [];
        planoNutricionalAluno.refeicoes!.addAll(value);
        mService.calendarioNutricao(_controllerCliente.mUsuarioAuth!.uid!, planoNutricionalAluno.dataInicioPlano!.toString()).then((calendario) {
          retornoCalendario.clear();
          retornoCalendario.addAll(prepararCalendario(calendario));
          processarQuantidadeRefeicoesConsumidas();
          sucesso!();
        }).catchError((error) {
          falha!();
        });
      }).catchError((error) {
        falha!();
      });
    }).catchError((error) {
      falha!();
    });
  }

  void adicionarLegendaRefeicaoAvulsa({Function()? sucesso, Function()? falha, required Function()? carregando, required String legenda, required String id}) {
    carregando?.call();
    mService.adicionarLegendaRefeicaoAvulsa(_controllerCliente.mUsuarioAuth!.uid!, id, legenda).then((planoNutricional) {
      sucesso!();
    }).catchError((error) {
      falha!();
    });
  }

  void salvarFotoRefeicao(
      {Function(String url)? sucesso, Function()? falha, required Function()? carregando, required bool consumida, DateTime? data, required String fotoBase64, required String id}) {
    carregando?.call();
    mService.salvarFotoRefeicao(_controllerCliente.mUsuarioAuth!.uid!, consumida, id, fotoBase64).then((planoNutricional) {
      mService.consultarReceitasGeradas(_controllerCliente.mUsuarioAuth!.uid!, dataConsulta: data!.toString()).then((value) {
        value.sort((a, b) => a.ordem!.compareTo(b.ordem!));
        planoNutricionalAluno.refeicoes = [];
        planoNutricionalAluno.refeicoes!.addAll(value);
        mService.calendarioNutricao(_controllerCliente.mUsuarioAuth!.uid!, planoNutricionalAluno.dataInicioPlano!.toString()).then((calendario) {
          retornoCalendario.clear();
          retornoCalendario.addAll(prepararCalendario(calendario));
          processarQuantidadeRefeicoesConsumidas();
          statusConsultarRefeicoes = ServiceStatus.Done;
          sucesso!(planoNutricional);
        }).catchError((error) {
          falha!();
        });
      }).catchError((error) {
        falha!();
      });
    }).catchError((error) {
      falha!();
    });
  }

  void pesquisarAlimentoPorNome({required String texto}) {
    var listaTemporaria = ObservableList<Ingredientes>();
    listaIngredientes = listaIngredientesBackup;
    for (final item in listaIngredientes) {
      if (item.nome!.toLowerCase().contains(texto.toLowerCase())) {
        listaTemporaria.add(item);
      }
    }
    listaIngredientes = texto.isEmpty ? listaIngredientesBackup : listaTemporaria;
  }

  void pesquisarReceitaPorNomeFavorita({required String texto}) {
    var listaTemporaria = ObservableList<Refeicoes>();
    listaRefeicoesFavoritas = listaRefeicoesFavoritasBackup;
    for (final item in listaRefeicoesFavoritas) {
      if (item.nome!.toLowerCase().contains(texto.toLowerCase())) {
        listaTemporaria.add(item);
      }
    }
    listaRefeicoesFavoritas.addAll(texto.isEmpty ? listaRefeicoesFavoritasBackup : listaTemporaria);
  }

  carregarRefeicaoSelecionadaAvulsa({String? id}) {
    listaIngredientesAdicionados.clear();
    listaRefeicoesAdicionados.clear();
    for (final item in planoNutricionalAluno.refeicoes!) {
      if (item.id == id) {
        listaIngredientesAdicionados.addAll(item.ingredientes!.ingredientes!);
        listaRefeicoesAdicionados.addAll(item.ingredientes!.refeicoes!);
      }
    }
    return listaIngredientesAdicionados;
  }

  prepararReceitasFavoritas(List<ReceitaFavorita> listaFavoritas, List<Refeicoes> refeicoes) {
    for (final favorita in listaFavoritas) {
      for (final receita in refeicoes) {
        if (favorita.refReceita == receita.id) {
          receita.favorita = favorita.favorita;
          break;
        }
      }
    }
    return refeicoes;
  }

  favoritarRefeicao({required String refRefeicao, required bool favorita}) {
    mService.favoritarRefeicao(_controllerCliente.mUsuarioAuth!.uid!, refRefeicao, favorita).then((value) {
      listaReceitasFavoritas.clear();
      listaReceitasFavoritas.addAll(value);
      listaRefeicoes = prepararReceitasFavoritas(value, listaRefeicoes);
      listaRefeicoesBackup = prepararReceitasFavoritas(value, listaRefeicoes);
      favoritarReceitasDaLista();
      //   favoritarReceitasDoDia();
    });
  }

  consultarProgressoTotalPlano() {
    mService.consultarProgressoTotalPlano(_controllerCliente.mUsuarioAuth!.uid!).then((value) {
      valorTotalConclusaoPlano = value.valor;
    }).catchError((error) {});
  }

  favoritarReceitasDaLista() {
    listaRefeicoesFavoritas.clear();
    listaRefeicoesFavoritasBackup.clear();
    for (final favorita in listaReceitasFavoritas) {
      for (final receita in listaRefeicoes) {
        if (favorita.refReceita == receita.id) {
          receita.favorita = favorita.favorita;
          break;
        }
      }
    }
    List<Refeicoes> listaFiltrada = listaRefeicoes.where((element) => element.favorita ?? false).toList();
    var distinctIds = listaFiltrada.toSet().toList();
    listaRefeicoesFavoritas.addAll(distinctIds);
    listaRefeicoesFavoritasBackup.addAll(distinctIds);
  }

  favoritarReceitasDoDia() {
    for (final favorita in listaReceitasFavoritas) {
      for (final receita in planoNutricionalAluno.refeicoes!) {
        if (favorita.refReceita == receita.receita!.id) {
          receita.receita!.favorita = favorita.favorita;
          break;
        }
      }
    }
  }

  void gerarListaComprasPorData({Function()? sucesso, Function()? falha, required Function()? carregando}) {
    statusListaCompras = ServiceStatus.Waiting;
    listaCompras.clear();
    carregando?.call();
    mService.consultarListaCompras(_controllerCliente.mUsuarioAuth!.uid!, dataInicialListaCompras!.toString(), dataFinalListaCompras!.toString()).then((value) {
      listaCompras.addAll(value);
      statusListaCompras = ServiceStatus.Done;
      sucesso!();
    }).catchError((error) {
      falha!();
      statusListaCompras = ServiceStatus.Error;
    });
  }

  editarItemListaCompras({required String ref, required bool comprado, required String quantidade, required String nome}) {
    mService.editarItemListaCompras(_controllerCliente.mUsuarioAuth!.uid!, ref, comprado, nome, quantidade).then((value) {
      value.sort((a, b) {
        if (a.comprado!) {
          return 1;
        }
        return -1;
      });
      listaCompras.clear();
      listaCompras.addAll(value);
    });
  }

  consultarListaComprasGeradas({String? ref, bool? comprado, String? quantidade, String? nome}) {
    listaCompras.clear();
    statusListaCompras = ServiceStatus.Waiting;
    mService.consultarListaComprasGerada(_controllerCliente.mUsuarioAuth!.uid!).then((value) {
      value.sort((a, b) {
        if (a.comprado!) {
          return 1;
        }
        return -1;
      });
      listaCompras.addAll(value);
      statusListaCompras = ServiceStatus.Done;
    });
  }

  void consultarDicasRapidas({Function()? carregando, Function()? sucesso, Function(String? falha)? falha}) {
    statusCarregandoDicas = ServiceStatus.Waiting;
    carregando?.call();
    mService.consultarDicas(10, _controllerCliente.mUsuarioLogado!.codUsuario!, 2).then((value) {
      listaDicas.clear();
      listaDicas.addAll(value);
      statusCarregandoDicas = ServiceStatus.Done;
      sucesso?.call();
    }).catchError((onError) {
      statusCarregandoDicas = ServiceStatus.Error;
      falha?.call(onError.message);
    });
  }

  void consultarHistoricoRespiracao() {
    listaHistoricoRespiracao.clear();
    mService.consultarHistoricoRespiracao(_controllerCliente.mUsuarioAuth!.uid!).then((value) {
      listaHistoricoRespiracao.addAll(value);
    });
  }

  void atualizarRespiracao() {
    listaHistoricoRespiracao.clear();
    mService.atualizarRespiracao(_controllerCliente.mUsuarioAuth!.uid!).then((value) {
      listaHistoricoRespiracao.addAll(value);
    });
  }

  void consultarPlanosNutricionais({required bool semCache, Function? sucesso, Function()? falha, required Function()? carregando}) {
    listaPlanoNutricao.clear();
    listaPlanoNutricaoBackup.clear();
    statusConsultarPlanosNutricionais = ServiceStatus.Waiting;
    carregando!();
    if (semCache) {
      mService.consultarPlanosNutricionaisSemCache().then((value) {
        listaPlanoNutricao.addAll(value);
        listaPlanoNutricaoBackup.addAll(value);
        statusConsultarPlanosNutricionais = ServiceStatus.Done;
        sucesso!();
      }).catchError((error) {
        statusConsultarPlanosNutricionais = ServiceStatus.Error;
        falha!();
      });
    } else {
      mService.consultarPlanosNutricionais().then((value) {
        listaPlanoNutricao.addAll(value);
        listaPlanoNutricaoBackup.addAll(value);
        statusConsultarPlanosNutricionais = ServiceStatus.Done;
        sucesso!();
      }).catchError((error) {
        statusConsultarPlanosNutricionais = ServiceStatus.Error;
        falha!();
      });
    }
  }

  void consultarNovidades() {
    listaNovidades.clear();
    statusConsultarNovidades = ServiceStatus.Waiting;
    mService.consultarNovidades().then((value) {
      listaNovidades.addAll(value);
      statusConsultarNovidades = ServiceStatus.Done;
    }).catchError((error) {
      statusConsultarNovidades = ServiceStatus.Error;
    });
  }

  void substituirPlanoNutricional({Function? sucesso, Function()? falha, required Function()? carregando, required String refNovoPlano, num? duracao}) {
    carregando?.call();
    mService.substituirPlanoNutricional(_controllerCliente.mUsuarioAuth!.uid!, refNovoPlano).then((value) {
      planoNutricionalAluno = PlanoNutricional();
      planoNutricionalAluno = value;
      planoNutricionalAluno.duracao = duracao;
      organizarTelaInicial();
      sucesso!();
    }).catchError((error) {
      falha!();
    });
  }

  void pesquisarPlanoNutricionalPorNome({required String texto}) {
    var listaTemporaria = ObservableList<PlanoNutricao>();
    listaPlanoNutricao = listaPlanoNutricaoBackup;
    for (final item in listaPlanoNutricao) {
      if (item.nomePlano!.toLowerCase().contains(texto.toLowerCase())) {
        listaTemporaria.add(item);
      }
    }
    listaPlanoNutricao = texto.isEmpty ? listaPlanoNutricaoBackup : listaTemporaria;
  }

  criarAlimento(
      {required num gordura,
      required num carboidratos,
      required num proteinas,
      required String nome,
      required num calorias,
      Function()? sucesso,
      Function()? falha,
      required Function()? carregando}) {
    carregando?.call();
    mService.criarAlimento(_controllerCliente.mUsuarioAuth!.uid!, gordura, carboidratos, proteinas, nome, calorias).then((value) {
      listaIngredientes
          .add(Ingredientes(fibra: 0, nome: nome, valorEnergetico: calorias.toDouble(), gordura: gordura.toDouble(), carboidrato: carboidratos.toDouble(), proteina: proteinas.toDouble()));
      listaIngredientesBackup
          .add(Ingredientes(fibra: 0, nome: nome, valorEnergetico: calorias.toDouble(), gordura: gordura.toDouble(), carboidrato: carboidratos.toDouble(), proteina: proteinas.toDouble()));
      sucesso!();
    }).catchError((error) {
      falha!();
    });
  }

  consultarAlimentosCriados() {
    mService.consultarAlimentosCriados(_controllerCliente.mUsuarioAuth!.uid!).then((value) {});
  }

  atualizarMetaAguaETamanhoCopo({required num tamanhoCopo, required num metaHidratacao, required bool lembrete, Function()? sucesso, Function()? falha, Function()? carregando}) {
    carregando?.call();
    mService.atualizarMetaAguaETamanhoCopo(_controllerCliente.mUsuarioAuth!.uid!, tamanhoCopo, metaHidratacao, lembrete).then((value) {
      sucesso!();
    }).catchError((error) {
      falha!();
    });
  }

  atualizarObjetivo({required String objetivo, Function()? sucesso, Function()? falha, Function()? carregando}) {
    carregando?.call();
    mService.atualizarObjetivo(_controllerCliente.mUsuarioAuth!.uid!, objetivo).then((value) {
      planoNutricionalAluno = PlanoNutricional();
      planoNutricionalAluno = value;
      organizarTelaInicial();
      sucesso!();
    }).catchError((error) {
      falha!();
    });
  }

  atualizarMetaDePeso({required num novaMetaPeso, Function()? sucesso, Function()? falha, Function()? carregando}) {
    carregando?.call();
    mService.atualizarMetaDePeso(_controllerCliente.mUsuarioAuth!.uid!, novaMetaPeso).then((value) {
      sucesso!();
    }).catchError((error) {
      falha!();
    });
  }

  atualizarLembretes({required LembretesNotificacoes lembretes, Function()? sucesso, Function()? falha, Function()? carregando}) {
    carregando?.call();
    mService.atualizarLembretes(_controllerCliente.mUsuarioAuth!.uid!, lembretes.toJson()).then((value) {
      sucesso!();
      planoNutricionalAluno.lembretesNotificacoes!.cafeManha = lembretes.cafeManha;
      planoNutricionalAluno.lembretesNotificacoes!.lancheManha = lembretes.lancheManha;
      planoNutricionalAluno.lembretesNotificacoes!.almoco = lembretes.almoco;
      planoNutricionalAluno.lembretesNotificacoes!.lancheTarde = lembretes.lancheTarde;
      planoNutricionalAluno.lembretesNotificacoes!.jantar = lembretes.jantar;
      planoNutricionalAluno.lembretesNotificacoes!.ceia = lembretes.ceia;
      planoNutricionalAluno.lembretesNotificacoes!.considerarTempoPreparo = lembretes.considerarTempoPreparo;
    }).catchError((error) {
      falha!();
    });
  }

  Future<void> agendarNotificacoesBeberAgua() async {
    var listaNotificacoes = [];
    var dataAtual = DateTime.now();
    if (planoNutricionalAluno.lembreteHidratacao ?? false) {
      tz.initializeTimeZones();
      var _timezone = await FlutterTimezone.getLocalTimezone();
      tz.setLocalLocation(tz.getLocation(_timezone));
      int idT = 2000;
      int id = 2000;
      for (int index = 0; index < 13; index++) {
        DateTime? dataNotificacao = UtilDataHora.getDataComHorasPersonalizada(dateTime: DateTime.now(), horas: 8 + index, minutos: 0, segundos: 0);
        listaNotificacoes.add(dataNotificacao);
      }
      for (final _ in listaNotificacoes) {
        idT = idT + 1;
        await flutterNotificationLocalPlugin!.cancel(idT);
      }
      for (int i = 0; i < listaNotificacoes.length; i++) {
        if (listaNotificacoes[i].microsecondsSinceEpoch > dataAtual.microsecondsSinceEpoch) {
          id = id + 1;
          Random rnd;
          int min = 1;
          int max = 15;
          rnd = new Random();
          var r = min + rnd.nextInt(max - min);
          try {
            await flutterNotificationLocalPlugin!.zonedSchedule(
                id,
                notificacaoBeberAgua(r, true),
                notificacaoBeberAgua(r, false),
                tz.TZDateTime.now(tz.local).add(Duration(
                  seconds: diferencaEntreDatasSegundos(listaNotificacoes[i]),
                )),
                const NotificationDetails(android: AndroidNotificationDetails('water', 'Notificações do beber água')),
                payload: 'beberAgua',
                uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime);
            //print(
            //    'Index: $id - Agendei a notificação beber agua titulo: ${notificacaoBeberAgua(r, true)} e subtitulo: ${notificacaoBeberAgua(r, false)}');
          } catch (e) {}
        }
      }
    } else {
      int idT = 2000;
      for (int index = 0; index < 13; index++) {
        DateTime? dataNotificacao = UtilDataHora.getDataComHorasPersonalizada(dateTime: DateTime.now(), horas: 8 + index, minutos: 0, segundos: 0);
        listaNotificacoes.add(dataNotificacao);
      }
      for (final _ in listaNotificacoes) {
        idT = idT + 1;
        await flutterNotificationLocalPlugin!.cancel(idT);
      }
    }
  }

  String notificacaoBeberAgua(int r, bool titulo) {
    var tamanhoCopo = planoNutricionalAluno.tamanhoCopo!.toStringAsFixed(0);
    switch (r) {
      case 1:
        return titulo ? '💧 Continue bem hidratado(a) 💧' : 'Confira sua meta de água e beba mais $tamanhoCopo ml.';
      case 2:
        return titulo ? 'Vai uma aguinha aí?! 🌊🔫' : 'Beba $tamanhoCopo ml para manter a hidratação.';
      case 3:
        return titulo ? 'Está na hora de se hidratar 💦' : 'Beba $tamanhoCopo ml de água.';
      case 4:
        return titulo ? 'A hora da água chegou ⏰💧' : 'Beba $tamanhoCopo ml de água.';
      case 5:
        return titulo ? 'Nada de ficar com sede 🥤🥵' : 'Hora de beber $tamanhoCopo ml de água.';
      case 6:
        return titulo ? 'A hidratação também importa💧' : 'Beba $tamanhoCopo ml de água.';
      case 7:
        return titulo ? 'Turbine seu treino com H20 😉💧' : 'Hora de beber $tamanhoCopo ml de água.';
      case 8:
        return titulo ? 'A manutenção da sua hidratação chegou 💧' : 'Beba mais $tamanhoCopo ml de água.';
      case 9:
        return titulo ? '🔔 Alerta de água 🔔' : 'Beba mais $tamanhoCopo ml.';
      case 10:
        return titulo ? 'Não esqueça da água 💦' : 'Hora de beber $tamanhoCopo ml.';
      case 11:
        return titulo ? 'Se viu essa notificação, beba água 💧👀' : 'Hora de beber mais $tamanhoCopo ml.';
      case 12:
        return titulo ? '💧 Você está quase lá 💧' : 'Beba mais $tamanhoCopo ml de água para bater a sua meta.';
      case 13:
        return titulo ? '💦 Interrompemos a programação para te lembrar💦' : 'Hora de beber $tamanhoCopo ml de água!';
      case 14:
        return titulo ? 'Pare tudo o que está fazendo 🤚💧' : 'E beba $tamanhoCopo ml de água.';
      case 15:
        return titulo ? 'Ter 70% de água no corpo não é desculpa 💧🤨 ' : 'Hora de beber $tamanhoCopo ml de água.';
      default:
        return titulo ? 'Ter 70% de água no corpo não é desculpa 💧🤨 ' : 'Hora de beber $tamanhoCopo ml de água.';
    }
  }

  void resetarPlanoNutricional({Function()? sucesso, Function()? falha, Function()? carregando}) {
    carregando?.call();
    mService.resetarPlanoNutricional(_controllerCliente.mUsuarioAuth!.uid!).then((value) {
      sucesso?.call();
    }).catchError((error) {
      falha?.call();
    });
  }

  void consultarPlanoNutricaoFitstream({required Function() sucesso, required Function() falha, required Function() carregando}) {
    statusConsultarPlanoNutricionaisFitStream = ServiceStatus.Waiting;
    carregando();
    listaPlanoNutricionalFitstream.clear();
    mService.consultarProgramasFitStream(GetIt.I.get<ControladorApp>().chave ?? '').then((value) {
      if (value.isEmpty) {
        statusConsultarPlanoNutricionaisFitStream = ServiceStatus.Empty;
      } else {
        listaPlanoNutricionalFitstream.addAll(value);
        statusConsultarPlanoNutricionaisFitStream = ServiceStatus.Done;
      }
      sucesso();
    }).catchError((error) {
      statusConsultarPlanoNutricionaisFitStream = ServiceStatus.Error;
      falha();
    });
  }

  void inserirProgramasFitStream({required PlanosNutricionaisFitStream planoFitStream, required Function() sucesso, required Function() falha, required Function() carregando}) {
    carregando();
    mService.inserirProgramasFitStream(planoFitStream.toJson()).then((value) {
      listaPlanoNutricionalFitstream.add(value);
      sucesso();
    }).catchError((error) {
      falha();
    });
  }

  void consularDetalhesProgramaFitStream({required String refPlano, required String refUser, required Function() sucesso, required Function() falha, required Function() carregando}) {
    carregando();
    mService.consularDetalhesProgramaFitStream(refPlano, refUser).then((value) {
      planoNutricionalSelecionadoFitstream = PlanosNutricionaisFitStream();
      planoNutricionalSelecionadoFitstream = value;
      sucesso();
    }).catchError((error) {
      falha();
    });
  }

  void removerProgramaFitStream({required String refPlanoFitStream, required Function() sucesso, required Function() falha, required Function() carregando}) {
    carregando();
    mService.removerProgramaFitStream(refPlanoFitStream).then((value) {
      sucesso();
    }).catchError((error) {
      falha();
    });
  }

  void marcarProgramaFitStreamComoConcluidoELike({required String refPlanoFitStream, required String refContentFitStream, required bool concluido, required bool like, required Function(Content) sucesso, required Function() falha, required Function() carregando}) {
    carregando();
    var json = {'refPlano': refPlanoFitStream, 'refContent': refContentFitStream, 'refUser':  GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid ?? '', 'concluido': concluido, 'like': like};
    mService.marcarProgramaFitStreamComoConcluidoELike(json).then((value) {
      sucesso(value);
    }).catchError((error) {
      falha();
    });
  }
}

class CalendarioNutricaoClass {
  num? progresso;
  DateTime? dia;

  CalendarioNutricaoClass({this.progresso, this.dia});
}

class ListaCompra {
  num? quantidade;
  String? nome;
  String? unidadeMedida;
  bool? marcado = false;

  ListaCompra({this.quantidade, this.nome, this.unidadeMedida, this.marcado});
}

class ReceitasAgrupadas {
  String? nome;
  List<Refeicoes?>? receita;
  bool confirmada;

  ReceitasAgrupadas({this.nome, this.receita, this.confirmada = false});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class ReceitaSubstituida {
  factory ReceitaSubstituida.fromJson(Map<String, dynamic> json) => _$ReceitaSubstituidaFromJson(json);
  Map<String, dynamic> toJson() => _$ReceitaSubstituidaToJson(this);
  String? usuarioApp;
  String? receitaPara;
  List<ReceitaNovaAntiga>? receitas;

  ReceitaSubstituida({
    this.usuarioApp,
    this.receitaPara,
    this.receitas,
  });
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class AlimentoCriado {
  factory AlimentoCriado.fromJson(Map<String, dynamic> json) => _$AlimentoCriadoFromJson(json);
  Map<String, dynamic> toJson() => _$AlimentoCriadoToJson(this);
  num? gordura;
  num? carboidratos;
  num? proteinas;
  String? nome;
  num? calorias;

  AlimentoCriado({
    this.gordura,
    this.carboidratos,
    this.proteinas,
    this.nome,
    this.calorias,
  });
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class HistoricoRespiracao {
  factory HistoricoRespiracao.fromJson(Map<String, dynamic> json) => _$HistoricoRespiracaoFromJson(json);
  Map<String, dynamic> toJson() => _$HistoricoRespiracaoToJson(this);
  DateTime? dia;
  bool? realizado;
  HistoricoRespiracao({this.dia, this.realizado});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class ItemListaCompras {
  factory ItemListaCompras.fromJson(Map<String, dynamic> json) => _$ItemListaComprasFromJson(json);
  Map<String, dynamic> toJson() => _$ItemListaComprasToJson(this);
  String? quantidade;
  String? nome;
  bool? comprado;
  String? id;

  ItemListaCompras({this.quantidade, this.nome, this.comprado, this.id});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class ReceitaNovaAntiga {
  factory ReceitaNovaAntiga.fromJson(Map<String, dynamic> json) => _$ReceitaNovaAntigaFromJson(json);
  Map<String, dynamic> toJson() => _$ReceitaNovaAntigaToJson(this);
  String? atual;
  String? novo;

  ReceitaNovaAntiga({
    this.atual,
    this.novo,
  });
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class ReceitaFavorita {
  factory ReceitaFavorita.fromJson(Map<String, dynamic> json) => _$ReceitaFavoritaFromJson(json);
  Map<String, dynamic> toJson() => _$ReceitaFavoritaToJson(this);
  bool? favorita;
  String? refReceita;

  ReceitaFavorita({
    this.favorita,
    this.refReceita,
  });
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class DadosSubstituicao {
  factory DadosSubstituicao.fromJson(Map<String, dynamic> json) => _$DadosSubstituicaoFromJson(json);
  Map<String, dynamic> toJson() => _$DadosSubstituicaoToJson(this);
  String? objetivo;
  String? tipoRefeicao;
  num? caloriasMaximas;
  num? caloriasMinimas;
  List<String?>? restricao;

  DadosSubstituicao({
    this.objetivo,
    this.tipoRefeicao,
    this.caloriasMaximas,
    this.caloriasMinimas,
    this.restricao,
  });
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class PesoUsuario {
  factory PesoUsuario.fromJson(Map<String, dynamic> json) => _$PesoUsuarioFromJson(json);
  Map<String, dynamic> toJson() => _$PesoUsuarioToJson(this);
  double? peso;
  DateTime? dataInclusao;

  PesoUsuario({this.peso, this.dataInclusao});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class HistoricoHidratacao {
  factory HistoricoHidratacao.fromJson(Map<String, dynamic> json) => _$HistoricoHidratacaoFromJson(json);
  Map<String, dynamic> toJson() => _$HistoricoHidratacaoToJson(this);
  num? ml;
  DateTime? dataInclusao;
  bool? adicao;

  HistoricoHidratacao({this.ml, this.dataInclusao});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class AulasFavoritas {
  factory AulasFavoritas.fromJson(Map<String, dynamic> json) => _$AulasFavoritasFromJson(json);
  Map<String, dynamic> toJson() => _$AulasFavoritasToJson(this);
  num? codigo;
  DateTime? dataInclusao;
  bool? favorita;

  AulasFavoritas({this.codigo, this.dataInclusao, this.favorita});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class PlanoNutricional {
  factory PlanoNutricional.fromJson(Map<String, dynamic> json) => _$PlanoNutricionalFromJson(json);
  Map<String, dynamic> toJson() => _$PlanoNutricionalToJson(this);
  num? altura;
  String? atividadeFisica;
  DateTime? dataNascimento;
  String? genero;
  double? metaPeso;
  String? objetivo;
  double? pesoAtual;
  double? pesoInicial;
  num? quantidadeRefeicoes;
  String? variedade;
  List<String?>? restricao;
  String? usuarioApp;
  num? metaHidratacao;
  DateTime? dataInicioPlano;
  DateTime? dataFimPlano;
  DateTime? dataConfiguracaoPlano;
  DateTime? dataConfirmacaoReceitas;
  DateTime? dataInicioListaCompras;
  DateTime? dataFimListaCompras;
  String? nomePlano;
  num? duracao;
  List<RefeicoesDoPlano>? refeicoes;
  HorarioRefeicao? horarioRefeicao;
  LembretesNotificacoes? lembretesNotificacoes;
  num? tamanhoCopo;
  bool? lembreteHidratacao;
  PlanoNutricional(
      {this.altura,
      this.atividadeFisica,
      this.dataNascimento,
      this.genero,
      this.metaPeso,
      this.objetivo,
      this.pesoAtual,
      this.quantidadeRefeicoes,
      this.variedade,
      this.restricao,
      this.usuarioApp,
      this.metaHidratacao,
      this.dataInicioPlano,
      this.dataFimPlano,
      this.dataConfiguracaoPlano,
      this.dataConfirmacaoReceitas,
      this.pesoInicial,
      this.refeicoes,
      this.horarioRefeicao,
      this.lembretesNotificacoes,
      this.dataInicioListaCompras,
      this.dataFimListaCompras,
      this.nomePlano,
      this.tamanhoCopo,
      this.lembreteHidratacao,
      this.duracao});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class HorarioRefeicao {
  factory HorarioRefeicao.fromJson(Map<String, dynamic> json) => _$HorarioRefeicaoFromJson(json);
  Map<String, dynamic> toJson() => _$HorarioRefeicaoToJson(this);
  String? cafeManha;
  String? lancheManha;
  String? almoco;
  String? lancheTarde;
  String? jantar;
  String? ceia;

  HorarioRefeicao({this.cafeManha, this.lancheManha, this.almoco, this.lancheTarde, this.jantar, this.ceia});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class LembretesNotificacoes {
  factory LembretesNotificacoes.fromJson(Map<String, dynamic> json) => _$LembretesNotificacoesFromJson(json);
  Map<String, dynamic> toJson() => _$LembretesNotificacoesToJson(this);
  bool? cafeManha;
  bool? lancheManha;
  bool? almoco;
  bool? lancheTarde;
  bool? jantar;
  bool? ceia;
  bool? considerarTempoPreparo;

  LembretesNotificacoes({this.cafeManha, this.lancheManha, this.almoco, this.lancheTarde, this.jantar, this.ceia, this.considerarTempoPreparo});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class RefeicoesDoPlano {
  factory RefeicoesDoPlano.fromJson(Map<String, dynamic> json) => _$RefeicoesDoPlanoFromJson(json);
  Map<String, dynamic> toJson() => _$RefeicoesDoPlanoToJson(this);
  String? receitaPara;
  DateTime? receitaParaODia;
  String? refReceita;
  DateTime? dataRefeicao;
  Refeicoes? receita;
  String? id;
  num? ordem;
  String? fotoRefeicao;
  IngredientesAdicionados? ingredientes;
  String? legenda;

  RefeicoesDoPlano({this.receitaPara, this.receitaParaODia, this.refReceita, this.dataRefeicao, this.receita, this.id, this.ordem, this.fotoRefeicao, this.ingredientes, this.legenda});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Refeicoes {
  factory Refeicoes.fromJson(Map<String, dynamic> json) => _$RefeicoesFromJson(json);
  Map<String, dynamic> toJson() => _$RefeicoesToJson(this);
  List<String>? tipoRefeicao;
  double? fibra;
  List<String>? tags;
  List<Media>? media;
  double? gordura;
  double? carboidrato;
  double? proteina;
  String? id;
  double? peso;
  double? valorEnergetico;
  List<String>? restricoes;
  num? rendimento;
  List<Alimentos>? alimentos;
  String? docKey;
  List<String>? objetivo;
  String? nome;
  String? descricao;
  bool? selecionado = false;
  bool? favorita;

  Refeicoes(
      {this.tipoRefeicao,
      this.fibra,
      this.tags,
      this.media,
      this.gordura,
      this.carboidrato,
      this.proteina,
      this.id,
      this.peso,
      this.valorEnergetico,
      this.restricoes,
      this.rendimento,
      this.alimentos,
      this.docKey,
      this.objetivo,
      this.nome,
      this.descricao,
      this.selecionado = false,
      this.favorita});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Media {
  factory Media.fromJson(Map<String, dynamic> json) => _$MediaFromJson(json);
  Map<String, dynamic> toJson() => _$MediaToJson(this);
  String? tipo;
  int? posicao;
  String? url;
  bool? principal;
  int? dataUpload;

  Media({this.tipo, this.posicao, this.url, this.principal, this.dataUpload});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class IngredientesAdicionados {
  factory IngredientesAdicionados.fromJson(Map<String, dynamic> json) => _$IngredientesAdicionadosFromJson(json);
  Map<String, dynamic> toJson() => _$IngredientesAdicionadosToJson(this);
  List<Ingredientes>? ingredientes;
  List<Refeicoes>? refeicoes;
  IngredientesAdicionados({this.ingredientes, this.refeicoes});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Ingredientes {
  factory Ingredientes.fromJson(Map<String, dynamic> json) => _$IngredientesFromJson(json);
  Map<String, dynamic> toJson() => _$IngredientesToJson(this);
  double? valorEnergetico;
  double? proteina;
  List<String>? restricoes;
  double? gordura;
  bool? isMedidaCaseira;
  String? unidadeDeMedida;
  String? docKey;
  double? fibra;
  double? carboidrato;
  String? medidaCaseiraNome;
  String? medidaPrimitiva;
  String? nome;
  double? quantidadeMedidaCaseira;
  double? quantidadeMedidaPrimitiva;
  String? tipo;
  bool? selecionado = false;

  Ingredientes(
      {this.proteina,
      this.docKey,
      this.fibra,
      this.valorEnergetico,
      this.quantidadeMedidaPrimitiva,
      this.gordura,
      this.unidadeDeMedida,
      this.quantidadeMedidaCaseira,
      this.medidaCaseiraNome,
      this.isMedidaCaseira,
      this.carboidrato,
      this.medidaPrimitiva,
      this.restricoes,
      this.nome,
      this.tipo,
      this.selecionado = false});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Alimentos {
  factory Alimentos.fromJson(Map<String, dynamic> json) => _$AlimentosFromJson(json);
  Map<String, dynamic> toJson() => _$AlimentosToJson(this);
  bool? isMedidaCaseira;
  String? unidadeDeMedida;
  String? nome;
  String? medidaCaseiraNome;
  num? quantidadeMedidaCaseira;
  String? modoPrepaparo;
  List<Ingredientes>? ingredientes;

  Alimentos({this.isMedidaCaseira, this.unidadeDeMedida, this.nome, this.medidaCaseiraNome, this.quantidadeMedidaCaseira, this.modoPrepaparo, this.ingredientes});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class DicasNutri {
  factory DicasNutri.fromJson(Map<String, dynamic> json) => _$DicasNutriFromJson(json);
  Map<String, dynamic> toJson() => _$DicasNutriToJson(this);
  DicasNutri clone() => _$DicasNutriFromJson(this.toJson());
  DicasNutri({
    this.codigo,
    this.titulo,
    this.categoria,
    this.nivel,
    this.tempo,
    this.tipo,
    this.dataCriacao,
    this.tituloSuperior,
    this.descricaoSuperior,
    this.tituloInferior,
    this.descricaoInferior,
    this.notaDicaNutri,
    this.destaque,
    this.dataCriacaoApresentar,
    this.urlImagem,
    this.responsavel,
  });

  num? codigo;
  String? titulo;
  String? categoria;
  String? nivel;
  String? tempo;
  String? tipo;
  num? dataCriacao;
  String? tituloSuperior;
  String? descricaoSuperior;
  String? tituloInferior;
  String? descricaoInferior;
  num? notaDicaNutri;
  bool? destaque;
  String? dataCriacaoApresentar;
  String? urlImagem;
  Responsavel? responsavel;
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Responsavel {
  factory Responsavel.fromJson(Map<String, dynamic> json) => _$ResponsavelFromJson(json);
  Map<String, dynamic> toJson() => _$ResponsavelToJson(this);
  Responsavel clone() => _$ResponsavelFromJson(this.toJson());
  Responsavel({
    this.codigo,
    this.nome,
    this.criador,
    this.foto,
    this.dicas,
  });

  num? codigo;
  String? nome;
  num? criador;
  String? foto;
  DicasNutri? dicas;
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class RetornoConclusao {
  factory RetornoConclusao.fromJson(Map<String, dynamic> json) => _$RetornoConclusaoFromJson(json);
  Map<String, dynamic> toJson() => _$RetornoConclusaoToJson(this);
  RetornoConclusao clone() => _$RetornoConclusaoFromJson(this.toJson());

  num? valor;

  RetornoConclusao({
    this.valor,
  });
}

enum StatusTelaInicial { Carregando, Erro, ConfigurarPlano, ConfirmarRefeicoes, ConfigurarInicioTermino, Refeicoes, ResponderAnamnese, SemPlano, DadosMocados }

@JsonSerializable(explicitToJson: true, anyMap: true)
class PlanoNutricao {
  factory PlanoNutricao.fromJson(Map<String, dynamic> json) => _$PlanoNutricaoFromJson(json);
  Map<String, dynamic> toJson() => _$PlanoNutricaoToJson(this);
  num? quantidadeRefeicoes;
  num? nota;
  String? descricao;
  List<RefeicoesDoPlano>? receitas;
  String? variedade;
  num? duracao;
  String? orientacoes;
  String? nomePlano;
  Nutrientes? nutrientes;
  String? refPlano;
  String? corGradienteFinal;
  String? corGradienteInicial;
  String? urlImagem;
  String? subtitulo;
  List<String>? avaliacaoDoPlano;

  PlanoNutricao(
      {this.quantidadeRefeicoes,
      this.nota,
      this.descricao,
      this.receitas,
      this.variedade,
      this.duracao,
      this.orientacoes,
      this.nomePlano,
      this.nutrientes,
      this.refPlano,
      this.corGradienteFinal,
      this.corGradienteInicial,
      this.urlImagem,
      this.subtitulo,
      this.avaliacaoDoPlano});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Nutrientes {
  factory Nutrientes.fromJson(Map<String, dynamic> json) => _$NutrientesFromJson(json);
  Map<String, dynamic> toJson() => _$NutrientesToJson(this);
  num? proteinas;
  num? carboidratos;
  num? kcal;
  num? gorduras;

  Nutrientes({this.proteinas, this.carboidratos, this.kcal, this.gorduras});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Novidades {
  factory Novidades.fromJson(Map<String, dynamic> json) => _$NovidadesFromJson(json);
  Map<String, dynamic> toJson() => _$NovidadesToJson(this);
  DateTime? dataFim;
  String? subitituloReceitas;
  bool? excluido;
  String? descricao;
  List<String>? receitas;
  String? titulo;
  DateTime? dataPostagem;
  String? urlImagem;

  Novidades({this.dataFim, this.subitituloReceitas, this.excluido, this.descricao, this.receitas, this.titulo, this.dataPostagem, this.urlImagem});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class PlanosNutricionaisFitStream {
  factory PlanosNutricionaisFitStream.fromJson(Map<String, dynamic> json) => _$PlanosNutricionaisFitStreamFromJson(json);
  Map<String, dynamic> toJson() => _$PlanosNutricionaisFitStreamToJson(this);
  String? thumb;
  String? criador;
  String? fotoCriador;
  int? codEmpresa;
  String? chave;
  int? quantidadeContents;
  String? nome;
  String? descricao;
  String? tipoObjetivo;
  String? ref;
  String? dataCriado;
  String? dataEditado;
  List<Extracontent>? extracontent;
  List<Content>? content;

  PlanosNutricionaisFitStream(
      {this.thumb,
      this.criador,
      this.fotoCriador,
      this.codEmpresa,
      this.chave,
      this.quantidadeContents,
      this.nome,
      this.descricao,
      this.tipoObjetivo,
      this.ref,
      this.dataCriado,
      this.dataEditado,
      this.extracontent});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Content {
  factory Content.fromJson(Map<String, dynamic> json) => _$ContentFromJson(json);
  Map<String, dynamic> toJson() => _$ContentToJson(this);

  int? ordem;
  String? titulo;
  String? refPlano;
  String? ref;
  String? text;
  bool? concluido;
  bool? like;
  String? thumb;
  List<Media>? media;

  Content({this.ordem, this.titulo, this.refPlano, this.ref, this.text, this.concluido, this.like, this.thumb, this.media});
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Extracontent {
  factory Extracontent.fromJson(Map<String, dynamic> json) => _$ExtracontentFromJson(json);
  Map<String, dynamic> toJson() => _$ExtracontentToJson(this);
  String? tipo;
  String? titulo;
  int? duracao;
  String? url;
  String? descricao;

  Extracontent({this.tipo, this.titulo, this.duracao, this.url, this.descricao});
}
