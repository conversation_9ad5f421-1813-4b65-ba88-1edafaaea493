import 'dart:convert';

import 'package:app_treino/ServiceProvider/PrescricaoTreinoService.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:diacritic/diacritic.dart';
import 'package:get_it/get_it.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:mobx/mobx.dart';

import 'package:app_treino/model/ServiceStatus.dart';
part 'ControladorPrescricaoTreino.g.dart';

class  ControladorPrescricaoTreino = _ControladorPrescricaoTreinoBase with _$ControladorPrescricaoTreino;

abstract class _ControladorPrescricaoTreinoBase with Store {

  var service = GetIt.I.get<PrescricaoTreinoService>();

  @observable
  ObservableList<ProgramaTreino> listaProgramasTreino = ObservableList<ProgramaTreino>();

  @observable
  ObservableList<Fichas> listaFichas = ObservableList<Fichas>();

  @observable
  ObservableList<AtividadeFicha> listaAtividades = ObservableList<AtividadeFicha>();

  @observable
  ObservableList<AtividadeFicha> listaAtividadesAdicionadas = ObservableList<AtividadeFicha>();

  @observable
  ObservableList<Fichas> listaFichasAdicionadasNoPrograma = ObservableList<Fichas>();

  @observable
  ServiceStatus statusConsultarAtividades = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultarProgramasPredefinidos = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultarFichasPredefinidas = ServiceStatus.Waiting;

  @observable
  num series = 0;

  @observable
  String repeticoes = '';

  @observable
  String peso = '';

  @observable
  String cadencia = '';

  @observable
  num? velocidade;

  @observable  
  int? duracao;
  
  @observable
  int? distancia;

  @observable
  int descanso = 0;

  @observable
  bool houveModificacao = false;

  @observable
  bool estaCriandoProgramaTreino = false;

  @observable
  bool fluxoPredefinido = false;

  /**
   * Consulta os programas de treinos predefinidos
   * Utiliza o [statusConsultarProgramasPredefinidos] para gerenciar o estado
   * retorna uma lista de [ProgramaTreino]
   */
  @action
  consultarProgramaTreinoPredefinidas() async {
    statusConsultarProgramasPredefinidos = ServiceStatus.Waiting;
    listaProgramasTreino.clear();
    var filtros = {'quicksearchValue': null,'quicksearchFields':['nome','genero','professorMontou.nome','situacao']};
    service.consultarProgramaTreinoPredefinido(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0, jsonEncode(filtros)).then((value) {
      listaProgramasTreino.addAll(value);
      statusConsultarProgramasPredefinidos = listaProgramasTreino.isEmpty ? ServiceStatus.Empty : ServiceStatus.Done;
    }).catchError((error) {
      statusConsultarProgramasPredefinidos = ServiceStatus.Error;
    });
  }

  /**
   * Consulta os programas de treinos predefinidos por nome
   * requer o parametro [Nome][String]
   * Utiliza o [statusConsultarProgramasPredefinidos] para gerenciar o estado
   * retorna uma lista de [ProgramaTreino]
   */
  @action
  consultarProgramaTreinoPredefinidasPorNome({String? nome}) async {
    statusConsultarProgramasPredefinidos = ServiceStatus.Waiting;
    listaProgramasTreino.clear();
   String textoNormalizado = '';
  if (nome != null && nome.isNotEmpty) {
    textoNormalizado = removeDiacritics(nome.trim().toLowerCase());
  }
  
  var filtros = {
    'quicksearchValue': textoNormalizado, // Usa o texto normalizado aqui
    'quicksearchFields': ['nome', 'genero', 'professorMontou.nome', 'situacao']
  };
  
    service.consultarProgramaTreinoPredefinidoPorNome(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0, jsonEncode(filtros),20, 0 ).then((value) {
      listaProgramasTreino.addAll(value);
      statusConsultarProgramasPredefinidos = listaProgramasTreino.isEmpty ? ServiceStatus.Error : ServiceStatus.Done;
    }).catchError((error) {
      statusConsultarProgramasPredefinidos = ServiceStatus.Error;
    });
  }

  /**
   * Consulta as fichas predefinidas
   * Utiliza o [statusConsultarFichasPredefinidas] para gerenciar o estado
   * retorna uma lista de [Fichas]
   */
  @action
  consultarFichasPredefinidas() async {
    listaFichas.clear();
    statusConsultarFichasPredefinidas = ServiceStatus.Waiting;
    service.consultarFichasPredefinidas(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0).then((value) {
      listaFichas.addAll(value);
      statusConsultarFichasPredefinidas = listaFichas.isEmpty ? ServiceStatus.Empty : ServiceStatus.Done;
    }).catchError((error) {
      statusConsultarFichasPredefinidas = ServiceStatus.Error;
    });
  }

  /**
   * Consulta as fichas predefinidas por nome
   * requer o parametro [Nome][String]
   * Utiliza o [statusConsultarFichasPredefinidas] para gerenciar o estado
   * retorna uma lista de [Fichas]
   */
  @action
  consultarFichasPredefinidasPorNome({String? nome}) async {
    statusConsultarFichasPredefinidas = ServiceStatus.Waiting;
    listaFichas.clear();
  String textoNormalizado = '';
  if (nome != null && nome.isNotEmpty) {
    textoNormalizado = removeDiacritics(nome.trim().toLowerCase());
  }
  var filtros = {
    'quicksearchFields': ['nome', 'ativo'],
    'quicksearchValue': textoNormalizado  // Usa o texto normalizado aqui
  };
    service.consultarFichasPredefinidasPorNome(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0, jsonEncode(filtros)).then((value) {
      listaFichas.addAll(value);
      statusConsultarFichasPredefinidas = listaFichas.isEmpty ? ServiceStatus.Empty : ServiceStatus.Done;
    }).catchError((error) {
      statusConsultarFichasPredefinidas = ServiceStatus.Error;
    });
  }

  /**
   * Consulta todas as atividades
   * requer o parametro [Nome][String]
   * Utiliza o [statusConsultarAtividades] para gerenciar o estado
   * retorna uma lista de [AtividadeFicha]
   */
  @action
  consultarAtividadesFicha({String? nome}) async {
    statusConsultarAtividades = ServiceStatus.Waiting;
    listaAtividades.clear();
    var filtros = {'situacaoAtividade':['ATIVO'],'quicksearchValue':nome,'quicksearchFields':['nome'],'grupoMuscularesIds':[]};
    service.consultarAtividades(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0, jsonEncode(filtros), 200, 0).then((value) {
      listaAtividades.addAll(value);
      statusConsultarAtividades = listaAtividades.isEmpty ? ServiceStatus.Empty : ServiceStatus.Done;
    }).catchError((error) {
      statusConsultarAtividades = ServiceStatus.Error;
    });
  }


  /**
   * Grava uma nova ficha
   * requer o parametro [Fichas], [falha], [carregando], [sucesso], [ProgramaTreino]
   * Remove dados do JSON que não são utilizados como versao, categoria, estaNoPrograma por padrão.
   * Se categoriaID ou tipoDeExecucao for nula, também remove do JSON
   * Se a ficha for cadastrada pela tela de fichas, ela vem como predefinida true, caso contrário vem com predefinida false
   * Ex. Se estiver sendo cadastrada por dentro de um programa ou tela de aluno.
   * retorna um sucesso com o objeto do tipo [Fichas]
   */
  @action
  gravarFicha({required Function(Fichas ficha) sucesso,required Function() falha, required Function() carregando, required Fichas ficha, ProgramaTreino? dadosPrograma}) async {
    carregando();
    var fichaJson = ficha.toJson();
    fichaJson.remove('versao');
    fichaJson.remove('categoria');
    fichaJson.remove('estaNoPrograma');
    fichaJson.remove('dataInicio');
    fichaJson.remove('dataTermino');
    fichaJson.remove('idPrograma');
    fichaJson.remove('aparelhos');
    if (ficha.categoria != null) {
      fichaJson.addAll({'categoriaId': ficha.categoria!.id});
    }
    if (ficha.tipo_execucao == null) fichaJson.remove('tipoExecucao');    
    if (ficha.id == null) {
      fichaJson.update('predefinida', (value) => dadosPrograma == null ? true : false);
      fichaJson.update('ativo', (value) => true);
      service.criarFichaPredefinida(fichaJson).then((value) {
        sucesso(value);
      }).catchError((error) {        
        falha();
      });
    } else {
      if (dadosPrograma != null) {
        var item = {'programaId': dadosPrograma.id};
        fichaJson.addAll(item);
      }
      service.gravarFicha(ficha.id!, fichaJson).then((value) {
        sucesso(value);
      }).catchError((error) {        
        falha();
      });
    }
  }


  /**
   * Grava um novo programa de treino
   * requer o parametro [ProgramaTreino], [falha], [carregando], [sucesso], [alunoId]
   * Remove dados do JSON que não são utilizados como dataLancamento, estaNoPrograma, fichas, situacao, professorMontou por padrão.
   * retorna um sucesso com o objeto do tipo [ProgramaTreino]
   */
  @action
  gravarProgramaPredefinido({required ProgramaTreino programa, int? alunoId, required Function(ProgramaTreino programaTreino) sucesso,required Function() falha, required Function() carregando}) async {
    carregando();
    var codigo = GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codigoColaborador;
    programa.professorId = codigo!.toInt();
    var json = programa.toJson();
    json.remove('dataLancamento');
    json.remove('estaNoPrograma');
    json.remove('fichas');
    json.remove('professorMontou');
    json.remove('situacao');    
    json.remove('aparelhos');
    programa.predefinido ?? json.update('predefinido', (value) => false);
    if (programa.id == null) {
      json.remove('id');
      json.remove('predefinido');
      service.criarProgramaPreDefinido(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0, json).then((value) {
      sucesso(value);
      }).catchError((error) {
        falha();
      });
    } else {
      if (alunoId != null) {
        json.addAll({'alunoId': alunoId});
      }
      service.gravarProgramaPredefinido(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0,programa.id!, json).then((value) {
        sucesso(value);
      }).catchError((error) {
        falha();
      });
    }
  }

  /**
   * Torna um programa prescrito no auno como predefinido.
   * requer o parametro [ProgramaTreino], [falha], [carregando], [sucesso], [alunoId]
   * Remove dados do JSON que não são utilizados como dataLancamento, estaNoPrograma, fichas, situacao, professorMontou por padrão.
   * retorna um sucesso com o objeto do tipo [ProgramaTreino]
   */
  @action
  tornarProgramaEmPredefinido({required Function(ProgramaTreino programaTreino) sucesso,required Function() falha, required Function() carregando, required ProgramaTreino programa}) async {
    carregando();
    service.tornarProgramaPredefinido(programa.id!).then((value) {
      sucesso(ProgramaTreino());
    }).catchError((error) {
      falha();
    });
  }

    /**
   * Torna um programa prescrito no auno como predefinido.
   * requer o parametro [ProgramaTreino], [falha], [carregando], [sucesso], [alunoId]
   * Remove dados do JSON que não são utilizados como dataLancamento, estaNoPrograma, fichas, situacao, professorMontou por padrão.
   * retorna um sucesso com o objeto do tipo [ProgramaTreino]
   */
  @action
  tornarFichaPredefinida({required Function() sucesso,required Function() falha, required Function() carregando, required idFicha}) async {
    carregando();
    service.tornarFichaPredefinida(idFicha).then((value) {
      sucesso();
    }).catchError((error) {
      falha();
    });
  }


  

  /**
   * Inclui uma ficha em branco dentro do programa de treino. 
   * A ficha é gerada pelo TreinoWeb com todos os dados default. 
   * requer o parametro [ProgramaTreino], [falha], [carregando], [sucesso]
   * retorna um sucesso com o objeto do tipo [Fichas]
   */
  @action
  incluirFichaEmBrancoNoProgramaTreino({required Function(Fichas ficha) sucesso, required Function() falha, required Function() carregando, required ProgramaTreino dadosPrograma}) async {
    carregando();
    service.incluirFichaEmBrancoNoProgramaTreino(dadosPrograma.id!).then((value) {
      sucesso(value);
    }).catchError((error) {
      falha();
    });
  }

  /**
   * Inclui uma ficha existente (predefinida) dentro do programa de treino. 
   * A ficha é gerada pelo TreinoWeb com todos os dados default. 
   * requer o parametro [ProgramaTreino], [Fichas], [falha], [carregando], [sucesso]
   * retorna um sucesso com o objeto do tipo [Fichas]
   */
  @action
  incluirFichaNoProgramaTreino({required Function(Fichas ficha) sucesso, required Function() falha, required Function() carregando, required Fichas ficha, required ProgramaTreino dadosPrograma}) async {
    carregando();
    var json = ficha.toJson();
    service.incluirFichaNoProgramaTreino(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0, ficha.id!, dadosPrograma.id!, json).then((value) {
      sucesso(value);
    }).catchError((error) {
      falha();
    });
  }

  /**
   * Inclui o programa de treino em um aluno selecionado.
   * Se o aluno não tiver um programa, cadastra como um novo. 
   * Se o aluno tiver um programa, adiciona um segundo.
   * requer o parametro [ProgramaTreino], [idPrograma], [idAluno], [falha], [carregando], [sucesso]
   * retorna um sucesso com o objeto do tipo [ProgramaTreino]
   */
  @action
  incluirProgramaTreinoNoAluno({required Function(ProgramaTreino programa) sucesso, required Function() falha, required Function() carregando, required int idPrograma, required int idAluno, required num empresaId}) async {
    carregando();
    var json = {'alunoId':idAluno};
    service.incluirProgramaTreinoNoAluno(idPrograma, json, empresaId).then((value) {
      sucesso(value);
    }).catchError((error) {
      falha();
    });
  }

  /**
   * Inclui uma ficha em um programa de treino do aluno selecionado
   * Se o aluno nao tiver um programa, o metodo retorna erro [semPrograma].
   * requer o parametro [semPrograma], [Fichas], [idAluno], [falha], [carregando], [sucesso]
   * retorna um [sucesso] se a operação foi concluida.
   */
  @action
  incluirFichaNoAluno({required Function() sucesso, required Function() falha, required Function() semPrograma, required Function() carregando, required int idAluno, required Fichas ficha}) async {
    carregando();
    service.consultarAlunoDetalhado(idAluno, GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0).then((alunoDetalhado) {
      if (alunoDetalhado.programas == null || (alunoDetalhado.programas?.isEmpty ?? true)){
        semPrograma();
      } else {
        service.incluirFichaNoProgramaTreino(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0, ficha.id!, alunoDetalhado.programas?.first.id ?? 0, ficha.toJson()).then((value) {
            sucesso();
          }).catchError((error) {
            falha();
        });
      }
    }).catchError((error) {
      falha();
    });
  }

  /**
   * Apaga o programa de treino predefinido.
   * requer o parametro [idPrograma], [falha], [carregando], [sucesso]
   */
  @action
  deletarProgramTreino({required Function() sucesso, required Function() falha, required Function() carregando, required int idPrograma}) async {
    carregando();
    service.deletarProgramaTreino(idPrograma).then((value) {
      sucesso();
    }).catchError((error) {
      falha();
    });
  }

  /**
   * Apaga uma ficha de treino predefinida.
   * requer o parametro [idPrograma], [falha], [carregando], [sucesso]
   */
  @action
  deletarFicha({required Function() sucesso, required Function() falha, required Function() carregando, required int idFicha}) async {
    carregando();
    service.deletarFicha(idFicha).then((value) {
      sucesso();
    }).catchError((error) {
      falha();
    });
  }

  consultarProgramaTreinoAluno({required Function(ProgramaTreino) sucesso, required Function() falha, required Function() carregando, required int codigoPrograma}) async {
    carregando();
    service.consultarProgramaAluno(codigoPrograma, GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0).then((value) {
      sucesso(value);
    }).catchError((error) {
      falha();
    });
  }

  criarProgramaTreinoVazio({required Function(ProgramaTreino) sucesso, required Function() falha, required Function() carregando, required num idAluno, }) async {
    carregando();
     var json = {'alunoId': idAluno};
    service.criarProgramaTreinoVazio(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0, json).then((value) {
      sucesso(value);
    }).catchError((error) {
      falha();
    });
  }

  Future<void> adicionarAtividadeNaListaDeSelecao({required Function() inicio, required Function()? carregando, required Function()? sucesso, required AtividadeFicha dadosAtividade}) async {
    inicio();
    await Future.delayed(const Duration(milliseconds: 500));
    carregando?.call();
    listaAtividadesAdicionadas.add(dadosAtividade);
    await Future.delayed(const Duration(milliseconds: 800));
    sucesso?.call();
  }

    Future<void> limparListaAtividadesSelecionadas(String? nome) async {
    if (nome == null) {
      listaAtividadesAdicionadas.clear();
    } else {
      for (var i = 0; i < listaAtividadesAdicionadas.length; i++) {
        if (listaAtividadesAdicionadas[i].nome == nome) {
          listaAtividadesAdicionadas.removeAt(i);
          break;
        }
      }
    }
  }

  int calculaDatasEAulasPrevistas({required inicio, required fim, required diasPorSemana}) {
    var dias = UtilDataHora.daysBetween(inicioMilis: fim, fimMilis: inicio);
    return (dias.toDouble() / 7 * (diasPorSemana ?? 0)).toInt();
  }

  int calcularDataFinal({required totalAulasPrevistas, required inicio, required diasPorSemana}){
    var dias = (totalAulasPrevistas! / (diasPorSemana ?? 0).toInt() * 7).toInt();
    return inicio + (dias * 86400000);
  }

  @action
  consultarAlunoDetalhado({required Function(ProgramaTreino) sucesso, required Function() falha, required Function() semPrograma, required Function() carregando, required int idAluno}) async {
    carregando();
    service.consultarAlunoDetalhado(idAluno, GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0).then((alunoDetalhado) {
      if(alunoDetalhado.programas == null || (alunoDetalhado.programas?.isEmpty ?? true)){
        semPrograma();
      } else {
        service.consultarProgramaAluno(alunoDetalhado.programaAtual?.id ?? 0, GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0).then((value) {
          sucesso(value);
        }).catchError((error) {
          falha();
        });
      }
    }).catchError((error) {
      falha();
    });
  }
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class RetornoFichas {

  factory RetornoFichas.fromJson(Map<String, dynamic> json) => _$RetornoFichasFromJson(json);
  Map<String, dynamic> toJson() => _$RetornoFichasToJson(this);
  RetornoFichas clone() => _$RetornoFichasFromJson(this.toJson());

  List<Fichas>? content;
  int? totalElements;
  int? totalPages;
  bool? first;
  bool? last;
  int? size;
  int? number;

  RetornoFichas(
      {this.content,
      this.totalElements,
      this.totalPages,
      this.first,
      this.last,
      this.size,
      this.number});
  
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class Fichas {

  factory Fichas.fromJson(Map<String, dynamic> json) => _$FichasFromJson(json);
  Map<String, dynamic> toJson() => _$FichasToJson(this);
  Fichas clone() => _$FichasFromJson(this.toJson());

  int? id;
  int? idPrograma;
  int? versao;
  String? nome;
  CategoriaFicha? categoria;
  String? tipo_execucao;
  String? mensagem;
  List<Atividades>? atividades;
  bool? ativo;
  bool? predefinida;
  bool? estaNoPrograma;
  List<String>? dias_semana;
  List<String>? aparelhos;
  String? dataInicio;
  String? dataTermino;

  List<Atividades>? get atividadesNaoConcluidas{
    return atividades?.where((e) =>!(e.concluida)).toList();
  }

  Fichas(
      {this.id,
      this.idPrograma,
      this.versao,
      this.nome,
      this.categoria,
      this.tipo_execucao,
      this.mensagem,
      this.atividades,
      this.ativo,
      this.predefinida, 
      this.estaNoPrograma,
      this.dias_semana,
      this.aparelhos,
      this.dataInicio,
      this.dataTermino});
  
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class CategoriaFicha {
  factory CategoriaFicha.fromJson(Map<String, dynamic> json) => _$CategoriaFichaFromJson(json);
  Map<String, dynamic> toJson() => _$CategoriaFichaToJson(this);
  CategoriaFicha clone() => _$CategoriaFichaFromJson(this.toJson());

  num? id;
  String? nome;

  CategoriaFicha({this.id, this.nome});
  
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class Atividades {
  factory Atividades.fromJson(Map<String, dynamic> json) => _$AtividadesFromJson(json);
  Map<String, dynamic> toJson() => _$AtividadesToJson(this);
  Atividades clone() => _$AtividadesFromJson(this.toJson());
  
  int? id;
  int? sequencia;
  Atividade? atividade;
  String? metodoExecucao;
  List<Series>? series;
  int? fichaId;
  List<SetAtividades>? setAtividades;
  String? nomeNaFicha;
  String? complementoNomeAtividade;
  int? esforco;
  int? atividadeId;
  bool? selecionada;
  List<int>? atividadesSequenciaSet;
  List<String>? nomesAtividadesAgrupadas;
  List<String>? aparelhos;
  @JsonKey(includeToJson: false, includeFromJson: false)
  bool concluida;

  Atividades(
      {this.id,
      this.sequencia,
      this.atividade,
      this.metodoExecucao,
      this.series,
      this.fichaId,
      this.setAtividades,
      this.nomeNaFicha,
      this.complementoNomeAtividade,
      this.esforco,
      this.atividadeId,
      this.selecionada, 
      this.atividadesSequenciaSet, 
      this.nomesAtividadesAgrupadas,
      this.aparelhos,
      this.concluida = false});
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class Atividade {
  factory Atividade.fromJson(Map<String, dynamic> json) => _$AtividadeFromJson(json);
  Map<String, dynamic> toJson() => _$AtividadeToJson(this);
  Atividade clone() => _$AtividadeFromJson(this.toJson());
  
  int? id;
  String? nome;
  bool? ativa;
  String? tipo;
  String? descricao;
  String? videoUri;
  List<Images>? images;
  List<Empresas>? empresas;
  bool? serieApenasDuracao;
  List<UrlLinkVideos>? urlLinkVideos;

  Atividade(
      {this.id,
      this.nome,
      this.ativa,
      this.tipo,
      this.descricao,
      this.videoUri,
      this.images,
      this.empresas,
      this.serieApenasDuracao,
      this.urlLinkVideos
    });

  }

@JsonSerializable(explicitToJson: true,anyMap: true)
class Images {
  factory Images.fromJson(Map<String, dynamic> json) => _$ImagesFromJson(json);
  Map<String, dynamic> toJson() => _$ImagesToJson(this);
  Images clone() => _$ImagesFromJson(this.toJson());
  
  String? type;
  int? id;
  String? uri;
  String? nome;
  String? fotoKeyPequena;
  String? fotoKeyMiniatura;
  bool? professor;

  Images({this.type,
    this.id,
    this.uri,
    this.nome,
    this.fotoKeyPequena,
    this.fotoKeyMiniatura,
    this.professor,
  });
  
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class UrlLinkVideos {
  int? id;
  String? linkVideo;
  bool? professor;
  UrlLinkVideos({
    this.id,
    this.linkVideo,
    this.professor
  });

  factory UrlLinkVideos.fromJson(Map<String, dynamic> json) => _$UrlLinkVideosFromJson(json);
  Map<String, dynamic> toJson() => _$UrlLinkVideosToJson(this);
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class Empresas {
  factory Empresas.fromJson(Map<String, dynamic> json) => _$EmpresasFromJson(json);
  Map<String, dynamic> toJson() => _$EmpresasToJson(this);
  Empresas clone() => _$EmpresasFromJson(this.toJson());
  
  int? id;
  String? identificador;
  CategoriaFicha? empresa;

  Empresas({this.id, this.identificador, this.empresa});

  
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class Series {
  factory Series.fromJson(Map<String, dynamic> json) => _$SeriesFromJson(json);
  Map<String, dynamic> toJson() => _$SeriesToJson(this);
  Series clone() => _$SeriesFromJson(this.toJson());
  
  int? id;
  int? atividadeFichaId;
  int? sequencia;
  String? repeticoes;
  String? carga;
  String? cadencia;
  int? descanso;
  String? complemento;
  num? velocidade;
  int? duracao;
  int? distancia;
  String? cargaApp;
  String? repeticaoApp;

  Series(
      {this.id,
      this.atividadeFichaId,
      this.sequencia,
      this.repeticoes,
      this.carga,
      this.cadencia,
      this.descanso,
      this.complemento,
      this.velocidade,
      this.duracao,
      this.distancia,
      this.cargaApp,
      this.repeticaoApp});

  
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class SetAtividades {
  factory SetAtividades.fromJson(Map<String, dynamic> json) => _$SetAtividadesFromJson(json);
  Map<String, dynamic> toJson() => _$SetAtividadesToJson(this);
  SetAtividades clone() => _$SetAtividadesFromJson(this.toJson());
  
  int? id;

  SetAtividades({this.id});
  
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class ProgramaTreino {

  factory ProgramaTreino.fromJson(Map<String, dynamic> json) => _$ProgramaTreinoFromJson(json);
  Map<String, dynamic> toJson() => _$ProgramaTreinoToJson(this);
  ProgramaTreino clone() => _$ProgramaTreinoFromJson(this.toJson());

  int? id;
  String? nome;
  int? dataLancamento;
  int? totalTreinos;
  int? qtdDiasSemana;
  List<Fichas>? fichas;
  ProfessorMontou? professorMontou;
  String? genero;
  String? situacao;
  bool? predefinido;
  int? professorId;
  bool? estaNoPrograma;
  num? inicio;
  num? termino;
  num? alunoId;


  ProgramaTreino(
      {this.id,
      this.nome,
      this.dataLancamento,
      this.totalTreinos,
      this.qtdDiasSemana,
      this.fichas,
      this.professorMontou,
      this.genero,
      this.situacao,
      this.predefinido,
      this.professorId, 
      this.estaNoPrograma,
      this.inicio,
      this.termino,
      this.alunoId});  
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class ProfessorMontou {

  factory ProfessorMontou.fromJson(Map<String, dynamic> json) => _$ProfessorMontouFromJson(json);
  Map<String, dynamic> toJson() => _$ProfessorMontouToJson(this);
  ProfessorMontou clone() => _$ProfessorMontouFromJson(this.toJson());
  
  int? id;
  int? codigoColaborador;
  String? nome;
  String? imageUri;

  ProfessorMontou({this.id, this.codigoColaborador, this.nome, this.imageUri}); 
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class AtividadeFicha {

  factory AtividadeFicha.fromJson(Map<String, dynamic> json) => _$AtividadeFichaFromJson(json);
  Map<String, dynamic> toJson() => _$AtividadeFichaToJson(this);
  AtividadeFicha clone() => _$AtividadeFichaFromJson(this.toJson());

  int? id;
  String? nome;
  bool? estaNoTreino;
  String? image;
  String? tipo;

  AtividadeFicha({this.id, this.nome, this.estaNoTreino, this.image, this.tipo});

  
}

@JsonSerializable(explicitToJson: true,anyMap: true)
class FiltrosAtividade {

  factory FiltrosAtividade.fromJson(Map<String, dynamic> json) => _$FiltrosAtividadeFromJson(json);
  Map<String, dynamic> toJson() => _$FiltrosAtividadeToJson(this);
  FiltrosAtividade clone() => _$FiltrosAtividadeFromJson(this.toJson());

  List<String>? situacaoAtividade;
  String? quicksearchValue;
  List<String>? quicksearchFields;
  List<String>? grupoMuscularesIds;

  FiltrosAtividade(
      {this.situacaoAtividade,
      this.quicksearchValue,
      this.quicksearchFields,
      this.grupoMuscularesIds});
}