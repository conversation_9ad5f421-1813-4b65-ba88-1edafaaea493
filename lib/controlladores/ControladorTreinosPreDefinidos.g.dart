// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorTreinosPreDefinidos.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorTreinosPreDefinidos
    on _ControladorTreinosPreDefinidosBase, Store {
  late final _$mConfiguracoesBaseFichaAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.mConfiguracoesBaseFicha',
      context: context);

  @override
  ConfiguracoesBaseFicha? get mConfiguracoesBaseFicha {
    _$mConfiguracoesBaseFichaAtom.reportRead();
    return super.mConfiguracoesBaseFicha;
  }

  @override
  set mConfiguracoesBaseFicha(ConfiguracoesBaseFicha? value) {
    _$mConfiguracoesBaseFichaAtom
        .reportWrite(value, super.mConfiguracoesBaseFicha, () {
      super.mConfiguracoesBaseFicha = value;
    });
  }

  late final _$statusConsultaConfBaseFichaAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.statusConsultaConfBaseFicha',
      context: context);

  @override
  ServiceStatus get statusConsultaConfBaseFicha {
    _$statusConsultaConfBaseFichaAtom.reportRead();
    return super.statusConsultaConfBaseFicha;
  }

  @override
  set statusConsultaConfBaseFicha(ServiceStatus value) {
    _$statusConsultaConfBaseFichaAtom
        .reportWrite(value, super.statusConsultaConfBaseFicha, () {
      super.statusConsultaConfBaseFicha = value;
    });
  }

  late final _$statusConsultaFichaPreDefinidaAtom = Atom(
      name:
          '_ControladorTreinosPreDefinidosBase.statusConsultaFichaPreDefinida',
      context: context);

  @override
  ServiceStatus get statusConsultaFichaPreDefinida {
    _$statusConsultaFichaPreDefinidaAtom.reportRead();
    return super.statusConsultaFichaPreDefinida;
  }

  @override
  set statusConsultaFichaPreDefinida(ServiceStatus value) {
    _$statusConsultaFichaPreDefinidaAtom
        .reportWrite(value, super.statusConsultaFichaPreDefinida, () {
      super.statusConsultaFichaPreDefinida = value;
    });
  }

  late final _$statusConsultaAtividadesAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.statusConsultaAtividades',
      context: context);

  @override
  ServiceStatus get statusConsultaAtividades {
    _$statusConsultaAtividadesAtom.reportRead();
    return super.statusConsultaAtividades;
  }

  @override
  set statusConsultaAtividades(ServiceStatus value) {
    _$statusConsultaAtividadesAtom
        .reportWrite(value, super.statusConsultaAtividades, () {
      super.statusConsultaAtividades = value;
    });
  }

  late final _$mFichasPredefinidasAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.mFichasPredefinidas',
      context: context);

  @override
  ObservableList<ProgramaFicha?> get mFichasPredefinidas {
    _$mFichasPredefinidasAtom.reportRead();
    return super.mFichasPredefinidas;
  }

  @override
  set mFichasPredefinidas(ObservableList<ProgramaFicha?> value) {
    _$mFichasPredefinidasAtom.reportWrite(value, super.mFichasPredefinidas, () {
      super.mFichasPredefinidas = value;
    });
  }

  late final _$mFichasPredefinidasFiltradasAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.mFichasPredefinidasFiltradas',
      context: context);

  @override
  ObservableList<ProgramaFicha?> get mFichasPredefinidasFiltradas {
    _$mFichasPredefinidasFiltradasAtom.reportRead();
    return super.mFichasPredefinidasFiltradas;
  }

  @override
  set mFichasPredefinidasFiltradas(ObservableList<ProgramaFicha?> value) {
    _$mFichasPredefinidasFiltradasAtom
        .reportWrite(value, super.mFichasPredefinidasFiltradas, () {
      super.mFichasPredefinidasFiltradas = value;
    });
  }

  late final _$quantidadeFiltrosAplicadosAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.quantidadeFiltrosAplicados',
      context: context);

  @override
  num get quantidadeFiltrosAplicados {
    _$quantidadeFiltrosAplicadosAtom.reportRead();
    return super.quantidadeFiltrosAplicados;
  }

  @override
  set quantidadeFiltrosAplicados(num value) {
    _$quantidadeFiltrosAplicadosAtom
        .reportWrite(value, super.quantidadeFiltrosAplicados, () {
      super.quantidadeFiltrosAplicados = value;
    });
  }

  late final _$filtroNiveisAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.filtroNiveis',
      context: context);

  @override
  ObservableList<Nivel> get filtroNiveis {
    _$filtroNiveisAtom.reportRead();
    return super.filtroNiveis;
  }

  @override
  set filtroNiveis(ObservableList<Nivel> value) {
    _$filtroNiveisAtom.reportWrite(value, super.filtroNiveis, () {
      super.filtroNiveis = value;
    });
  }

  late final _$filtroCategoriasAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.filtroCategorias',
      context: context);

  @override
  ObservableList<Categoria> get filtroCategorias {
    _$filtroCategoriasAtom.reportRead();
    return super.filtroCategorias;
  }

  @override
  set filtroCategorias(ObservableList<Categoria> value) {
    _$filtroCategoriasAtom.reportWrite(value, super.filtroCategorias, () {
      super.filtroCategorias = value;
    });
  }

  late final _$filtroObjetivosAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.filtroObjetivos',
      context: context);

  @override
  ObservableList<ObjetivoAluno> get filtroObjetivos {
    _$filtroObjetivosAtom.reportRead();
    return super.filtroObjetivos;
  }

  @override
  set filtroObjetivos(ObservableList<ObjetivoAluno> value) {
    _$filtroObjetivosAtom.reportWrite(value, super.filtroObjetivos, () {
      super.filtroObjetivos = value;
    });
  }

  late final _$filtroSexoAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.filtroSexo', context: context);

  @override
  ObservableList<String> get filtroSexo {
    _$filtroSexoAtom.reportRead();
    return super.filtroSexo;
  }

  @override
  set filtroSexo(ObservableList<String> value) {
    _$filtroSexoAtom.reportWrite(value, super.filtroSexo, () {
      super.filtroSexo = value;
    });
  }

  late final _$todasAtividadesAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.todasAtividades',
      context: context);

  @override
  ObservableList<AtividadesFicha> get todasAtividades {
    _$todasAtividadesAtom.reportRead();
    return super.todasAtividades;
  }

  @override
  set todasAtividades(ObservableList<AtividadesFicha> value) {
    _$todasAtividadesAtom.reportWrite(value, super.todasAtividades, () {
      super.todasAtividades = value;
    });
  }

  late final _$todasAtividadesFiltroAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.todasAtividadesFiltro',
      context: context);

  @override
  ObservableList<AtividadesFicha> get todasAtividadesFiltro {
    _$todasAtividadesFiltroAtom.reportRead();
    return super.todasAtividadesFiltro;
  }

  @override
  set todasAtividadesFiltro(ObservableList<AtividadesFicha> value) {
    _$todasAtividadesFiltroAtom.reportWrite(value, super.todasAtividadesFiltro,
        () {
      super.todasAtividadesFiltro = value;
    });
  }

  late final _$atividadesAdicionarNoTreinoAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.atividadesAdicionarNoTreino',
      context: context);

  @override
  ObservableList<AtividadesFicha> get atividadesAdicionarNoTreino {
    _$atividadesAdicionarNoTreinoAtom.reportRead();
    return super.atividadesAdicionarNoTreino;
  }

  @override
  set atividadesAdicionarNoTreino(ObservableList<AtividadesFicha> value) {
    _$atividadesAdicionarNoTreinoAtom
        .reportWrite(value, super.atividadesAdicionarNoTreino, () {
      super.atividadesAdicionarNoTreino = value;
    });
  }

  late final _$mAlunosAdicionarFichaAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.mAlunosAdicionarFicha',
      context: context);

  @override
  ObservableList<AlunoDeProfessor> get mAlunosAdicionarFicha {
    _$mAlunosAdicionarFichaAtom.reportRead();
    return super.mAlunosAdicionarFicha;
  }

  @override
  set mAlunosAdicionarFicha(ObservableList<AlunoDeProfessor> value) {
    _$mAlunosAdicionarFichaAtom.reportWrite(value, super.mAlunosAdicionarFicha,
        () {
      super.mAlunosAdicionarFicha = value;
    });
  }

  late final _$isPesquisaAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.isPesquisa', context: context);

  @override
  bool get isPesquisa {
    _$isPesquisaAtom.reportRead();
    return super.isPesquisa;
  }

  @override
  set isPesquisa(bool value) {
    _$isPesquisaAtom.reportWrite(value, super.isPesquisa, () {
      super.isPesquisa = value;
    });
  }

  late final _$treinoModeloAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.treinoModelo',
      context: context);

  @override
  ProgramaFicha? get treinoModelo {
    _$treinoModeloAtom.reportRead();
    return super.treinoModelo;
  }

  @override
  set treinoModelo(ProgramaFicha? value) {
    _$treinoModeloAtom.reportWrite(value, super.treinoModelo, () {
      super.treinoModelo = value;
    });
  }

  late final _$mudouAlgoAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.mudouAlgo', context: context);

  @override
  bool get mudouAlgo {
    _$mudouAlgoAtom.reportRead();
    return super.mudouAlgo;
  }

  @override
  set mudouAlgo(bool value) {
    _$mudouAlgoAtom.reportWrite(value, super.mudouAlgo, () {
      super.mudouAlgo = value;
    });
  }

  late final _$semAtividadeAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.semAtividade',
      context: context);

  @override
  bool get semAtividade {
    _$semAtividadeAtom.reportRead();
    return super.semAtividade;
  }

  @override
  set semAtividade(bool value) {
    _$semAtividadeAtom.reportWrite(value, super.semAtividade, () {
      super.semAtividade = value;
    });
  }

  late final _$mudouAlgoParaoSerieAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.mudouAlgoParaoSerie',
      context: context);

  @override
  bool get mudouAlgoParaoSerie {
    _$mudouAlgoParaoSerieAtom.reportRead();
    return super.mudouAlgoParaoSerie;
  }

  @override
  set mudouAlgoParaoSerie(bool value) {
    _$mudouAlgoParaoSerieAtom.reportWrite(value, super.mudouAlgoParaoSerie, () {
      super.mudouAlgoParaoSerie = value;
    });
  }

  late final _$isFiltroAtivoAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.isFiltroAtivo',
      context: context);

  @override
  bool get isFiltroAtivo {
    _$isFiltroAtivoAtom.reportRead();
    return super.isFiltroAtivo;
  }

  @override
  set isFiltroAtivo(bool value) {
    _$isFiltroAtivoAtom.reportWrite(value, super.isFiltroAtivo, () {
      super.isFiltroAtivo = value;
    });
  }

  late final _$filtroAtividadesAtom = Atom(
      name: '_ControladorTreinosPreDefinidosBase.filtroAtividades',
      context: context);

  @override
  String get filtroAtividades {
    _$filtroAtividadesAtom.reportRead();
    return super.filtroAtividades;
  }

  @override
  set filtroAtividades(String value) {
    _$filtroAtividadesAtom.reportWrite(value, super.filtroAtividades, () {
      super.filtroAtividades = value;
    });
  }

  @override
  String toString() {
    return '''
mConfiguracoesBaseFicha: ${mConfiguracoesBaseFicha},
statusConsultaConfBaseFicha: ${statusConsultaConfBaseFicha},
statusConsultaFichaPreDefinida: ${statusConsultaFichaPreDefinida},
statusConsultaAtividades: ${statusConsultaAtividades},
mFichasPredefinidas: ${mFichasPredefinidas},
mFichasPredefinidasFiltradas: ${mFichasPredefinidasFiltradas},
quantidadeFiltrosAplicados: ${quantidadeFiltrosAplicados},
filtroNiveis: ${filtroNiveis},
filtroCategorias: ${filtroCategorias},
filtroObjetivos: ${filtroObjetivos},
filtroSexo: ${filtroSexo},
todasAtividades: ${todasAtividades},
todasAtividadesFiltro: ${todasAtividadesFiltro},
atividadesAdicionarNoTreino: ${atividadesAdicionarNoTreino},
mAlunosAdicionarFicha: ${mAlunosAdicionarFicha},
isPesquisa: ${isPesquisa},
treinoModelo: ${treinoModelo},
mudouAlgo: ${mudouAlgo},
semAtividade: ${semAtividade},
mudouAlgoParaoSerie: ${mudouAlgoParaoSerie},
isFiltroAtivo: ${isFiltroAtivo},
filtroAtividades: ${filtroAtividades}
    ''';
  }
}
