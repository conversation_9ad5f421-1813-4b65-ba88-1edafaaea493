
import 'package:app_treino/controlladores/ControladorWod.dart';
import 'package:app_treino/model/wod/unidadeMedida.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:shared_preferences/shared_preferences.dart';
part 'ControladorConfiguracao.g.dart';

class ControladorConfiguracao = _ControladorConfiguracaoBase with _$ControladorConfiguracao;

abstract class _ControladorConfiguracaoBase with Store {
  

  @observable
  bool isMetroDistancia = false;
  @observable
  bool isLibraCorporal = false;
  @observable
  bool isFtAltura = false;
  @observable
  int? countDownExercicio = 3;

  @observable
  int? horasEncerrarTreinoAutomaticamente = 3;

  @observable
  bool previewFotoExecucaoHabilitado = true;

  @observable
  bool descansoHabilitado = true;

  @observable
  bool meusDadosHabilitado = true;

  @observable
  bool agiteParaReportarHabilitado = true;

  @observable
  bool statusAtividadeHabilitado = true;

  @observable
  bool avisoSonoroHabilitado = false;

  @observable
  bool volumeHabilitado = true;
  
  EnumDistancia selectedDistancia = EnumDistancia.QUILOMETRO;
  EnumPesoCorporal selectedCorporal = EnumPesoCorporal.QUILOS;
  EnumAltura selectedAltura = EnumAltura.CENTIMETRO;

  final _controladorWod = GetIt.I.get<ControladorWod>();

  usuarioSelecionouLibras(bool selecionado) async {
    SharedPreferences mUnindadeMedida = await SharedPreferences.getInstance();
    await mUnindadeMedida.setBool('usuario_selecionou_libras', selecionado);
    _controladorWod.isLibra = selecionado;
  }

  usuarioSelecionouPesoCorporal(bool selecionado) async {
    SharedPreferences mPesoCorporal = await SharedPreferences.getInstance();
    await mPesoCorporal.setBool('usuario_selecionou_pesoCorporal', selecionado);
    isLibraCorporal = selecionado;
  }

  usuarioSelecionouDistancia(bool selecionado) async {
    SharedPreferences mDistancia = await SharedPreferences.getInstance();
    await mDistancia.setBool('usuario_selecionou_distancia', selecionado);
    isMetroDistancia = selecionado;
  }

  usuarioSelecionouAltura(bool selecionado) async {
    SharedPreferences mAltura = await SharedPreferences.getInstance();
    await mAltura.setBool('usuario_selecionou_altura', selecionado);
    isFtAltura = selecionado;
  }


  void consultarUnidadeMedidaDistancia() {
    SharedPreferences.getInstance().then((value) {
      isMetroDistancia = value.getBool('usuario_selecionou_distancia') ?? false;
    });
  }

  void consultarUnidadeMedidaCorporal() {
    SharedPreferences.getInstance().then((value) {
      isLibraCorporal = value.getBool('usuario_selecionou_pesoCorporal') ?? false;
    });
  }

  void consultarUnidadeMedidaAltura() {
    SharedPreferences.getInstance().then((value) {
      isFtAltura = value.getBool('usuario_selecionou_altura') ?? false;
    });
  }

  double converterQuiloEmLibraPeso(double valor) {
    if (isLibraCorporal) {
      return valor * 2.205;
    } else {
      return valor;
    }
  }

  double converterDistanciaKmEmMetros(double valor) {
    if (isMetroDistancia) {
      return (valor * 1000);
    } else {
      return valor;
    }
  }

  double converterAlturaCmEmFt(double valor) {
    if (isFtAltura) {
      return (valor / 2.54).roundToDouble();
    } else {
      return (valor / 100);
    }
  }

  void subtrairCount({required bool countdown}){
    var numberSubtrair;
    if (countdown) {
      if(countDownExercicio! >= 1 || countDownExercicio == 10){
        numberSubtrair = countDownExercicio! - 1;
        countDownExercicio = numberSubtrair;
      }
    } else {
       if(horasEncerrarTreinoAutomaticamente! >= 2 || horasEncerrarTreinoAutomaticamente == 10){
        numberSubtrair = horasEncerrarTreinoAutomaticamente! - 1;
        horasEncerrarTreinoAutomaticamente = numberSubtrair;
      }
    }
   
  }

  

  void addCount({required bool countdown}){
    var numberAdd;
    if (countdown) {
      if(countDownExercicio! >= 0){
        numberAdd = countDownExercicio! + 1;
        countDownExercicio = numberAdd;
      }
    } else {
      if(horasEncerrarTreinoAutomaticamente! >= 0){
        numberAdd = horasEncerrarTreinoAutomaticamente! + 1;
        horasEncerrarTreinoAutomaticamente = numberAdd;
      }
    }
    
  }
  
  @action
  Future<void> descansoExercicio(bool habilitado) async {
    SharedPreferences db = await SharedPreferences.getInstance();
    await db.setBool('descanso_exercicio', habilitado);
  }

  @action
   consultarDescansoExercicio() async {
    SharedPreferences.getInstance().then((value) {
      descansoHabilitado = value.getBool('descanso_exercicio') ?? true;
    });
  }

  @action
  Future<void> previewFotoExecucao(bool habilitado) async {
    SharedPreferences db = await SharedPreferences.getInstance();
    await db.setBool('preview_foto_execucao', habilitado);
  }

  @action
  consultarPreviewFotoExecucao() async {
    SharedPreferences.getInstance().then((value) {
      previewFotoExecucaoHabilitado = value.getBool('preview_foto_execucao') ?? false;
    });
  }

  @action
   consultarMeusDados() async {
    SharedPreferences.getInstance().then((value) {
      meusDadosHabilitado = value.getBool('meus_dados') ?? true;
    });
  }

  @action
  Future<void> meusDados(bool habilitado) async {
    SharedPreferences db = await SharedPreferences.getInstance();
    await db.setBool('meus_dados', habilitado);
  }

  @action
   consultarStatusAtividades() async {
    SharedPreferences.getInstance().then((value) {
      statusAtividadeHabilitado = value.getBool('status_atividade') ?? true;
    });
  }

    @action
  Future<void> statusAtividade(bool habilitado) async {
    SharedPreferences db = await SharedPreferences.getInstance();
    await db.setBool('status_atividade', habilitado);
  }

  @action
  Future<void> avisoSonoro(bool habilitado) async {
    SharedPreferences db = await SharedPreferences.getInstance();
    await db.setBool('aviso_sonoro_exercicio', habilitado);
  }

  @action
   consultarAvisoSonoro() async {
    SharedPreferences.getInstance().then((value) {
      avisoSonoroHabilitado = value.getBool('aviso_sonoro_exercicio') ?? true;
    });
  }

  Future<void> contagemCronometroExercicio(int tempo) async {
    SharedPreferences mTempoDefinido = await SharedPreferences.getInstance();
    await mTempoDefinido.setInt('tempo_cronometro_exercicio', tempo);
  }

    Future<void> contagemTempoEncerrarTreinoAutomaticamente(int tempo) async {
    SharedPreferences mTempoDefinido = await SharedPreferences.getInstance();
    await mTempoDefinido.setInt('HORAS_ENCERRAR_TREINO_AUTOMATICAMENTE', tempo);
  }


  void consultarTempoCronometroExercicio(){
    SharedPreferences.getInstance().then((value) {
      countDownExercicio = value.getInt('tempo_cronometro_exercicio') ?? 3;
    });
  }

   void consultarHorasEncerrarTreinoAutomaticamente(){
    SharedPreferences.getInstance().then((value) {
      horasEncerrarTreinoAutomaticamente = value.getInt('HORAS_ENCERRAR_TREINO_AUTOMATICAMENTE') ?? 3;
    });
  }

  @action
   consultarAgiteParaReportar() async {
    SharedPreferences.getInstance().then((value) {
      agiteParaReportarHabilitado = value.getBool('agite_para_reportar') ?? true;
    });
  }

  @action
  Future<void> agiteParaReportar(bool habilitado) async {
    SharedPreferences db = await SharedPreferences.getInstance();
    await db.setBool('agite_para_reportar', habilitado);
  }

}