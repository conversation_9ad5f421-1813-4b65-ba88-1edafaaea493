import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/geral/AvaliacaoNPS.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorNPS.g.dart';

class ControladorNPS = _ControladorNPSBase with _$ControladorNPS;

abstract class _ControladorNPSBase with Store {
  var mService = GetIt.I.get<PersonalService>();

  void limparTudo() {
    this.nota = null;
    this.problema = null;
    this.comentario = null;
  }

  @observable
  int? nota;
  @observable
  String? problema;
  @observable
  String? comentario;

  void setComentario(String value) {
    this.comentario = value;
  }

  void setNota(int value) {
    this.nota = value;
  }

  @computed
  bool get liberarBotoes => nota != null && nota! < 4;

  void setMotivoNota(String value) => problema = value;

  String get tituloComentario {
    switch (nota) {
      case 0:
      case 1:
      case 2:
        return 'O que está errado?';
      case 3:
        return 'Como podemos melhorar?';
      case 4:
        return 'Gostaria de deixar um comentário?';
        
      default:
        return '';
    }
  }

  String get getNivelSatisfacao {
    if (nota == 0) return 'Péssima';
    if (nota == 1) return 'Ruim';
    if (nota == 2) return 'Regular';
    if (nota == 3) return 'Boa';
    if (nota == 4) return 'Ótima';
    return '';
  }

  void salvarNPs({required Function()? carregando, Function()? sucesso, Function(String? falha)? falha}) async {
    carregando?.call();
    mService
        .avaliarClienteApp(AvaliacaoNPS(
                comentario: comentario,
                problema: problema,
                nota: nota,
                reacao: getNivelSatisfacao,
                clienteApp: GetIt.I.get<ControladorApp>().mClienteAppSelecionado!.id,
                userDocumentID: GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid)
            .toJson())
        .then((value) {
      sucesso!();
    }).catchError((onError) {
      falha!(onError.message);
    });
  }
}
