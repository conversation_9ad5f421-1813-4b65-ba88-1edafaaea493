// ignore: unused_import
import 'dart:convert';
import 'package:app_treino/ServiceProvider/NiveisAlunoServico.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoDeTreino.dart';
import 'package:app_treino/model/NivelAluno.dart';
import 'package:app_treino/model/personal/Aluno.dart';
import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorDetalheAluno.g.dart';

class ControladorDetalheAluno = _ControladorDetalheAlunoBase with _$ControladorDetalheAluno;

abstract class _ControladorDetalheAlunoBase with Store {

  
  List<NivelAluno>? niveisAluno;
  Aluno? alunoDetalhado;
  final _prescrService = GetIt.I.get<PersonalService>();
  final _nivelService = GetIt.I.get<Niveisalunoservico>();

  @observable
  ServiceStatus statusConsultaDetalhesDoAluno = ServiceStatus.Waiting;
  @observable
  ServiceStatus statusConsultaNivesAluno = ServiceStatus.Waiting;
  @observable
  ServiceStatus statusEdicaoNivelAluno = ServiceStatus.Waiting;
  @computed
  ServiceStatus get statusConsultaCardNiveis {
    if (statusConsultaDetalhesDoAluno == ServiceStatus.Waiting || statusConsultaNivesAluno == ServiceStatus.Waiting) {
      return ServiceStatus.Waiting;
    } else if (statusConsultaDetalhesDoAluno == ServiceStatus.Error || statusConsultaNivesAluno == ServiceStatus.Error) {
      return ServiceStatus.Error;
    } else {
      return ServiceStatus.Done;
    }
  }

    void consultarDetalhesDoAluno({num? matricula, required num idAluno, Function()? onLoading, Function(Aluno aluno)? onSuccess, Function()? onError}) {
    Future<Aluno> service;
    statusConsultaDetalhesDoAluno = ServiceStatus.Waiting;
    onLoading?.call();
    if (matricula != null) {
      service = _prescrService.consultarAlunoDetalhadoMatricula(matricula: matricula, empresaId: GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0);
    } else {
      service = _prescrService.consultarAlunoDetalhado(codigoAluno: idAluno, empresaId: GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0);
    }
    service.then((alunoDetalhadoRet) {
      statusConsultaDetalhesDoAluno = ServiceStatus.Done;
      alunoDetalhado = alunoDetalhadoRet;
      GetIt.I.get<ControladorPrescricaoDeTreino>().alunoExibir = alunoDetalhadoRet;
      onSuccess?.call(alunoDetalhadoRet);
    }).catchError((error) {
      statusConsultaDetalhesDoAluno = ServiceStatus.Error;
      onError?.call();
    });
  }
    void consultarNivelAluno({ Function()? onLoading, Function(List<NivelAluno> niveis)? onSuccess, Function()? onError}) {
    statusConsultaNivesAluno = ServiceStatus.Waiting;
     _nivelService.obterNivel(empresaId:  GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa ?? 1).then((niveis) {
      statusConsultaNivesAluno = ServiceStatus.Done;
      niveisAluno = niveis;
      onSuccess?.call(niveis);
    }).catchError((error) {
      statusConsultaNivesAluno = ServiceStatus.Error;
      onError?.call();
    });
  }
  void editarNivel({ Function()? onLoading, Function()? onSuccess, Function()? onError,required num nivelId,required num matricula}) {
    statusEdicaoNivelAluno = ServiceStatus.Waiting;
     _nivelService.editarNivel(empresaId:  GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa ?? 1, nivel: {'nivelId':nivelId},matricula:matricula ).then((niveis) {
      statusEdicaoNivelAluno = ServiceStatus.Done;
      onSuccess?.call();
    }).catchError((error) {
      statusEdicaoNivelAluno = ServiceStatus.Error;
      onError?.call();
    });
  }
   
}
