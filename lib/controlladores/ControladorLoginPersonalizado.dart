import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorAppLoading.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/Usuario.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

class ControladorLoginPersonalizado {

  ControladorApp get _mControladorApp => GetIt.I.get<ControladorApp>();
  // Chave para app de testes
  String chave = 'aa5abb60d28e4583f5438c7d4a1cb376';
  ClienteApp mClienteApp = ClienteApp(documentkey: 'qvD3JGjyQfNpRsymVTVi');

  Future<void> validarLoginTestePersonalizado(Usuario? usuario) async {
    if (usuario == null) {
      return;
    }
    String nomeEmpresaTeste = 'REVISÃO';
    if (usuario.nomeEmpresa == nomeEmpresaTeste && usuario.codUsuario == 6) {
      GetIt.I.get<ControladorAppLoading>().mLoadedChave = chave;
      _mControladorApp.chave = chave;
      await loadClienteApp();
    }
  }

  bool isTestePersonalizado(String usuario, String pass) {
    return (usuario.toLowerCase().replaceAll(' ', '') == '<EMAIL>' && (pass.toLowerCase().replaceAll(' ', '') == 'e92629ca'));
  }

  Future<void> loadClienteApp() async {
    await GetIt.I.get<ControladorAppLoading>().retryClienteApp(mClienteApp);
  }

  void validarLoginPersonalizado({required String? userName, required String? pass, required Function() continueLogin}) {
    if (userName?.toLowerCase().replaceAll(' ', '') == '<EMAIL>') {
      _mControladorApp.chave = chave;
    }
    continueLogin();
  }
}
