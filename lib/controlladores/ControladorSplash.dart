import 'dart:async';
import 'dart:convert';

import 'package:app_treino/ServiceProvider/StuckTrace.dart';
import 'package:app_treino/ServiceProvider/authServices/ServiceAuth.dart';
import 'package:app_treino/appWidgets/BasicWdigetUtil.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorNovoLogin.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/model/TokenBody.dart';
import 'package:app_treino/model/UserDataKeys.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/doClienteApp/DadosQrLogin.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/Usuario.dart'; 
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:app_treino/Utilitario.dart';
part 'ControladorSplash.g.dart';

class ControladorSplash = _ControladorSplashBase with _$ControladorSplash;

abstract class _ControladorSplashBase with Store {
  final _mServiceAuth = GetIt.I.get<ServiceAuth>();
  late Future<SharedPreferences> _userData;
  ControladorNovoLogin get mControladorNovoLogin => GetIt.I.get<ControladorNovoLogin>();
  String urlLogoApp = '';
  String currentToken = '';
  String currentTokenPsec = '';
  initFake401() {
    // if (kDebugMode) {
    //   Timer.periodic(const Duration(seconds: 25), (x) {
    //     print('Invaludou token');
    //     GetIt.I.get<ControladorSplash>().currentTokenPsec = '';
    //     GetIt.I.get<ControladorSplash>().currentToken = '';
    //   });
    // }
  }

  void pegarTokenAuthDaAPI(Function() prosseguir) {
    _userData = SharedPreferences.getInstance();
    _userData.then((db) async {
      urlLogoApp = db.getString('urlLogoApp') ?? '';
      _consultarAuthClienteApp(prosseguir, db);
    });
  }

  Future<String>? pegaTokenAgora(Function(String? token) onSucesso, {Function()? falha}) {
    SharedPreferences? db;
    return SharedPreferences.getInstance().then((xdb) async {
      db = xdb;
      return GetIt.I.get<ServiceAuth>().consultarAuth(TokenBody('e6719eb96f4175f32fac6c7b1cf9cd55').toJson());
    }).then((value) {
      db!.setString(UserDataKeys.TOKENDEAUTHAPI.toString(), value.content!);
      db!.setInt(UserDataKeys.VALIDADETOKEN.toString(), DateTime.now().add(const Duration(hours: 8)).millisecondsSinceEpoch);
      if (GetIt.I.get<ControladorCliente>().mUsuarioLogado == null) {
        db!.remove(UserDataKeys.TOKENUSUARIO.toString());
      }
      onSucesso.call(value.content);
      currentToken = value.content!;
      return value.content!;
    }).catchError((onError) {
      falha?.call();
      StuckTrace().putExtra('Erro ao gerar token', onError);
      return db?.getString(UserDataKeys.TOKENDEAUTHAPI.toString()) ?? '';
    });
  }

  Future<String>? gerarTokenColaboradorLogado(Function(String? token) onSucesso) async {
    SharedPreferences? db;
    return SharedPreferences.getInstance().then((xdb) async {
      db = xdb;
      var dados = {'chave': GetIt.I.get<ControladorApp>().chave, 'username': GetIt.I.get<ControladorCliente>().mUsuarioLogado!.username ?? ''};
      return await GetIt.I.get<ServiceAuth>().gerarTokenAuthColaborador(dados);
    }).then((value) {
      db!.setString(UserDataKeys.TOKENCOLABORADOR.toString(), value);
      onSucesso.call(value);
      //validarTokenColaborador(sucesso: () {}, falha: () {});
      currentToken = value;
      return value;
    }).then((tokenMS) {
      return db?.getString(UserDataKeys.TOKENCOLABORADOR.toString()) ?? '';
    }).catchError((onError) {
      return db?.getString(UserDataKeys.TOKENDEAUTHAPI.toString()) ?? '';
    });
  }

  Future<String>? gerarTokenUsuarioLogado(
    Function(String? token) onSucesso, {
    bool relogin = false,
    String? chave,
    Usuario? loadedUser,
  }) async {
    SharedPreferences? db;
    return SharedPreferences.getInstance().then((xdb) async {
      db = xdb;

      var dados = {
        'chave': chave ?? GetIt.I.get<ControladorApp>().chave,
        'codUsuario': loadedUser?.codUsuario ?? (relogin ? GetIt.I.get<ControladorApp>().userRelogin!.codigoUsuario : GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codUsuario ?? '')
      };
      if (relogin) {
        dados['chave'] = GetIt.I.get<ControladorApp>().userRelogin!.chave;
        dados['codUsuario'] = GetIt.I.get<ControladorApp>().userRelogin!.codigoUsuario;
      }
      return await GetIt.I.get<ServiceAuth>().gerarTokenAuthColaboradorV3(dados);
    }).then((value) async {
      db!.setString(UserDataKeys.TOKENDEAUTHAPI.toString(), value);
      currentTokenPsec = value;
      onSucesso.call(value);
      return value;
    }).catchError((onError) {
      onSucesso.call('');
      return db?.getString(UserDataKeys.TOKENDEAUTHAPI.toString()) ?? '';
      
    });
  }

  Future<String>? gerarTokenUsuarioMs({Function(String? token)? onSucesso}) async {
    SharedPreferences? db;
    return SharedPreferences.getInstance().then((xdb) async {
      db = xdb;
      var dados = {'chave': GetIt.I.get<ControladorApp>().chave, 'username': GetIt.I.get<ControladorCliente>().mUsuarioLogado!.username ?? '', 'app': true};
      return await GetIt.I.get<ServiceAuth>().loginMs(dados);
    }).then((value) {
      db!.setString(UserDataKeys.TOKENUSUARIOMS.toString(), value.content!.token!);
      onSucesso?.call(value.content!.token!);
      return value.content!.token!;
    }).catchError((onError) {
      onSucesso?.call('');
      return db?.getString(UserDataKeys.TOKENDEAUTHAPI.toString()) ?? '';
    });
  }

  PerfilAcesso? mPerfilUsuario;

  Future<void> validarTokenColaborador({Function()? sucesso, Function()? falha}) async {
    SharedPreferences db = await SharedPreferences.getInstance();
    _mServiceAuth.validarToken(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa!).then((value) {
      mPerfilUsuario = value;
      db.setString('perfilUsuario', jsonEncode(mPerfilUsuario!.toJson()));
      sucesso?.call();
    }).catchError((onError) {
      falha!.call();
    });
  }

  void _consultarAuthClienteApp(Function() prosseguir, SharedPreferences db) {
    GetIt.I.get<ServiceAuth>().consultarAuth(TokenBody('e6719eb96f4175f32fac6c7b1cf9cd55').toJson()).then((value) {
      db.setString(UserDataKeys.TOKENDEAUTHAPI.toString(), value.content!);
      db.setInt(UserDataKeys.VALIDADETOKEN.toString(), DateTime.now().add(const Duration(hours: 8)).millisecondsSinceEpoch);

      prosseguir();
    }).catchError((onError) {
      prosseguir();
    });
  }

  initDynamicLinks({Function()? linkInvalido}) async {
    linkInvalido?.call();
    // try {
    //   FirebaseDynamicLinksPlatform.instance.getInitialLink().then((data) {
    //   final Uri? deepLink = data?.link;
    //   if (deepLink?.path.contains('firstOpen') ?? false) {
    //     fazLoginPorDynamicLink(DadosLoginDynamico.fromDeepLink(deepLink!));
    //   } else {
    //     linkInvalido?.call();
    //   }
    //   FirebaseDynamicLinksPlatform.instance.onLink.listen((event) async {
    //     final Uri deepLink = event.link;
    //     String linkCompleto = '';
    //     linkCompleto = deepLink.origin + deepLink.path;
    //     GetIt.I.get<ControladorJPinApp>().campanha = linkCompleto;
    //     if (deepLink.path.contains('firstOpen')) {
    //       fazLoginPorDynamicLink(DadosLoginDynamico.fromDeepLink(deepLink));
    //     }
    //   }, onError: (e) async {
    //     linkInvalido?.call();
    //   });
    // }).catchError((onError) {
    //   linkInvalido?.call();
    // });
    // } catch (e) {
    //   linkInvalido?.call();
    // }
  }

  fazLoginPorDynamicLink(DadosLoginDynamico data) {
    StuckTrace().cancelSendLog();
    if ((data.codigoValidacao?.isNotEmpty ?? false) && mControladorNovoLogin.email.isNotEmpty) {
      mControladorNovoLogin.deepLinkCodigo = data.codigoValidacao!;
      mControladorNovoLogin.callLoginCodigoDeepLink();
      analytic(EventosKey.lgn_veio_de_link_email);
      return;
    }
    final NavigationService _navGlobal = GetIt.I.get();
    var falhaLister = ([String? mensagem]) {
      BasicWdigetUtil().showDialogMensagem(_navGlobal.context, titulo: 'Ops!', mensagem: 'O link do login não é mais valido');
    };
    final ControladorApp _controladorClienteApp = GetIt.I.get();
    final ControladorCliente _controladorCliente = GetIt.I.get();
    UtilitarioApp().showDialogCarregando(_navGlobal.context);
    _controladorClienteApp.consultarClienteAppCompleto(ClienteApp(documentkey: data.appid), sucesso: () {
      _controladorClienteApp.setEmpresaSelecionada(EmpresaApp(chave: data.chave, nome: ''), () {
        _controladorCliente.logarUsuarioPorSenha(
          user: data.username,
          pass: '',
          carregando: () => {},
          sucesso: () {
            GetIt.I
                .get<ControladorApp>()
                .prepararTelasBaseadoNasConfiguracoes(context: _navGlobal.context, eColaborador: GetIt.I.get<ControladorCliente>().isUsuarioColaborador, tipoApp: F.appFlavor!);
            Navigator.pop(_navGlobal.context);
            Navigator.of(_navGlobal.context).pushNamedAndRemoveUntil('/homePageApp', (Route<dynamic> route) => false);
          },
          falha: falhaLister,
        );
      }, falhaLister);
    }, falha: falhaLister);
  }

  void limparTudo() {
    currentToken = '';
    currentTokenPsec = '';
  }
}
