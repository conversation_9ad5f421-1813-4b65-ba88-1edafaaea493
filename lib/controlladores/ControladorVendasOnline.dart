import 'package:app_treino/ServiceProvider/VendasOnlineService.dart';
import 'package:app_treino/model/vendasOnline/DadosUsuarioVenda.dart';
import 'package:app_treino/model/vendasOnline/PlanosUnidadeVendaOnline.dart';
import 'package:app_treino/model/vendasOnline/UnidadeVendaOnline.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorVendasOnline.g.dart';

class ControladorVendasOnline = _ControladorVendasOnlineBase with _$ControladorVendasOnline;

abstract class _ControladorVendasOnlineBase with Store {
  final _mServiceVendasOnline = GetIt.I.get<VendasOnlineService>();

  @observable
  ObservableList<UnidadeVendaOnline> mListaUnidades = ObservableList<UnidadeVendaOnline>();
  @observable
  bool naoEncontrouPeloTermo = false;
  @observable
  ObservableList<UnidadeVendaOnline> mListaUnidadesFiltro = ObservableList<UnidadeVendaOnline>();
  @observable
  ObservableList<PlanosUnidadeVendaOnline> mPlanosDaUnidade = ObservableList<PlanosUnidadeVendaOnline>();
  @observable
  UnidadeVendaOnline? unidadeSelecionada;
  @observable
  PlanosUnidadeVendaOnline? planoSeleciondo;
  @observable
  DadosUsuarioVenda mDadosDoUsuario = DadosUsuarioVenda();

  void selecionarPlanoDaUnidade(PlanosUnidadeVendaOnline plano) {
    planoSeleciondo = plano;
  }

  void filtrarUnidadesPorNome(String filtro) {
    mListaUnidadesFiltro.clear();
    var termo = filtro.toLowerCase();
    if (termo.isNotEmpty) {
      mListaUnidadesFiltro.addAll(mListaUnidades.where((element) => element.nome!.toLowerCase().contains(termo) || element.endereco.toString().contains(termo)));
      naoEncontrouPeloTermo = mListaUnidadesFiltro.isEmpty;
    } else {
      mListaUnidadesFiltro.clear();
      naoEncontrouPeloTermo = false;
    }
  }

  void consultarUnidades({Function()? suceso, required Function()? carregando, Function()? erro}) {
    carregando?.call();
    _mServiceVendasOnline.getUnidadesEmpresa().then((value) {
      mListaUnidades.clear();
      mListaUnidades.addAll(value);
      suceso!();
    }).catchError((onError) {
      erro!();
    });
  }

  void consultarPlanosDaUnidade(UnidadeVendaOnline unidade, {Function()? suceso, required Function()? carregando, Function()? erro}) {
    carregando?.call();
    _mServiceVendasOnline.getPlanosUnidade(unidade.chave!, unidade.codigo!).then((value) {
      unidadeSelecionada = unidade;
      mPlanosDaUnidade.clear();
      mPlanosDaUnidade.addAll(value);
      suceso!();
    }).catchError((onError) {
      erro!();
    });
  }

  void limparDados() {
    mListaUnidades.clear();
    naoEncontrouPeloTermo = false;
    mListaUnidadesFiltro.clear();
    mPlanosDaUnidade.clear();
    unidadeSelecionada = UnidadeVendaOnline();
    planoSeleciondo = PlanosUnidadeVendaOnline();
    mDadosDoUsuario = DadosUsuarioVenda();
  }

  void validarFormulario({required Function()? sucesso, Function()? erro}) {
    sucesso?.call();
    // if(
    //   mDadosDoUsuario.nome  !=null && mDadosDoUsuario.nome.isNotEmpty &&
    //   mDadosDoUsuario.cpf !=null && mDadosDoUsuario.cpf.isNotEmpty &&
    //   mDadosDoUsuario.email  !=null && mDadosDoUsuario.email.isNotEmpty &&
    //   mDadosDoUsuario.telefone !=null && mDadosDoUsuario.telefone.isNotEmpty &&
    //   mDadosDoUsuario.endereco !=null && mDadosDoUsuario.endereco.isNotEmpty &&
    //   mDadosDoUsuario.complemento  !=null && mDadosDoUsuario.complemento.isNotEmpty &&
    //   mDadosDoUsuario.cep!=null && mDadosDoUsuario.cep.isNotEmpty){
    //     sucesso?.call();
    //   }else erro?.call();
  }
}
