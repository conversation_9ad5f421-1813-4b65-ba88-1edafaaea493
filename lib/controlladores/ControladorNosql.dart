import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:mobx/mobx.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sembast/sembast_io.dart';
import 'package:sembast_web/sembast_web.dart';

part 'ControladorNosql.g.dart';

class ControladorNoSql = _ControladorNoSqlBase with _$ControladorNoSql;

abstract class _ControladorNoSqlBase with Store {
  final DatabaseFactory _dbFactory = (!kIsWeb) ? databaseFactoryIo : databaseFactoryWeb;

  late Database db;

  Database get getDb => db;

  Future<void> initDatabase() async {
    if (kIsWeb) {
      db = await _dbFactory.openDatabase('test');
    } else {
      Directory appDocDirectory = await getApplicationDocumentsDirectory();
      await Directory(appDocDirectory.path + '/' + 'dir').create(recursive: true);
      var path = await getApplicationDocumentsDirectory();
      db = await _dbFactory.openDatabase('${path.path}/dbapp.treino');
    }
  }
}

class DeslogarDoApp {
  void onUsuarioSaiuDoApp() {}
}
