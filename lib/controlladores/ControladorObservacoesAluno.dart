import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/personal/Aluno.dart';
import 'package:app_treino/model/personal/AlunoDeProfessor.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorObservacoesAluno.g.dart';

class ControladorObservacoesAluno = _ControladorObservacoesAlunoBase with _$ControladorObservacoesAluno;

abstract class _ControladorObservacoesAlunoBase with Store {
  final _service = GetIt.I.get<PersonalService>();
  @observable
  ObservableList<ObservacaoAlunoColaborador> mObservacoes = ObservableList<ObservacaoAlunoColaborador>();
  @observable
  ServiceStatus statusConsultaObservacoes = ServiceStatus.Waiting;
  @observable
  String observacaoGravar = '';
  @observable
  bool observacaoGravarImportante = false;

  void limparTudo() {
    observacaoGravar = '';
    mObservacoes.clear();
    observacaoGravarImportante = false;
  }

  // void consultarObservacoesDoAluno({required num id, Function()? sucesso, Function(String? mensagem)? falha}) {
  //   statusConsultaObservacoes = ServiceStatus.Waiting;
  //   _service.consultarObservacoesAluno(id).then((observarcoes) {
  //     statusConsultaObservacoes = observarcoes.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
  //     mObservacoes.clear();
  //     mObservacoes.addAll(observarcoes);
  //     mObservacoes.sort((a, b) => b.dataLong!.compareTo(a.dataLong!));
  //     sucesso!();
  //   }).catchError((onError) {
  //     falha!(onError.message);
  //     statusConsultaObservacoes = ServiceStatus.Error;
  //   });
  // }

  void removerObservacaoDeAluno(ObservacaoAluno observacao, {Function()? sucesso, Function(String? mensagem)? erro}) {
    _service.removerObservacao(observacao.codigo!).then((value) {
      mObservacoes.removeWhere((e) => e.codigo == observacao.codigo);
      statusConsultaObservacoes = ServiceStatus.Done;
      if (mObservacoes.isEmpty) statusConsultaObservacoes = ServiceStatus.Empty;
      sucesso!();
    }).catchError((onError) {
      //http.delete(onError.)
      erro!(onError.message);
    });
  }
  ObservacaoAlunoColaborador? valor;

  void escreverObservacaoParaAluno({required Aluno aluno, required String observacao,required num codigoUsuarioZW, Function()? carregando, Function()? sucesso, Function(String? mensagem)? erro, required bool isImportant}) {
    carregando?.call();
    if (observacao.isNotEmpty) {
      _service.gravarObservacao({
        'observacao':observacao,
        'importante': isImportant,
        'cliente': {'codigo': aluno.codigoCliente},
        'usuario': {'codigo': codigoUsuarioZW}
      }).then((value) {
        valor = value;
        sucesso?.call();
      }).catchError((onError) {
        erro?.call(onError.message);
      });
    } else {
      erro?.call(localizedString('the_note_cant_be_empty'));
    }
  }
}
