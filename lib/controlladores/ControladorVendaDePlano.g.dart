// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorVendaDePlano.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorVendaDePlano on _ControladorVendaDePlanoBase, Store {
  Computed<bool>? _$isDadosPessoaisValidoComputed;

  @override
  bool get isDadosPessoaisValido => (_$isDadosPessoaisValidoComputed ??=
          Computed<bool>(() => super.isDadosPessoaisValido,
              name: '_ControladorVendaDePlanoBase.isDadosPessoaisValido'))
      .value;
  Computed<bool>? _$isCPFCartaoValidoComputed;

  @override
  bool get isCPFCartaoValido => (_$isCPFCartaoValidoComputed ??= Computed<bool>(
          () => super.isCPFCartaoValido,
          name: '_ControladorVendaDePlanoBase.isCPFCartaoValido'))
      .value;
  Computed<bool>? _$isCPFDadosPessoaisValidoComputed;

  @override
  bool get isCPFDadosPessoaisValido => (_$isCPFDadosPessoaisValidoComputed ??=
          Computed<bool>(() => super.isCPFDadosPessoaisValido,
              name: '_ControladorVendaDePlanoBase.isCPFDadosPessoaisValido'))
      .value;
  Computed<bool>? _$isValidadeCartaoValidaComputed;

  @override
  bool get isValidadeCartaoValida => (_$isValidadeCartaoValidaComputed ??=
          Computed<bool>(() => super.isValidadeCartaoValida,
              name: '_ControladorVendaDePlanoBase.isValidadeCartaoValida'))
      .value;
  Computed<bool>? _$isDAdosCartaoValidoComputed;

  @override
  bool get isDAdosCartaoValido => (_$isDAdosCartaoValidoComputed ??=
          Computed<bool>(() => super.isDAdosCartaoValido,
              name: '_ControladorVendaDePlanoBase.isDAdosCartaoValido'))
      .value;
  Computed<bool>? _$isVencimentoFaturaValidoComputed;

  @override
  bool get isVencimentoFaturaValido => (_$isVencimentoFaturaValidoComputed ??=
          Computed<bool>(() => super.isVencimentoFaturaValido,
              name: '_ControladorVendaDePlanoBase.isVencimentoFaturaValido'))
      .value;
  Computed<bool>? _$isCPFCartaoValidoPagamentoParcelaComputed;

  @override
  bool get isCPFCartaoValidoPagamentoParcela =>
      (_$isCPFCartaoValidoPagamentoParcelaComputed ??= Computed<bool>(
              () => super.isCPFCartaoValidoPagamentoParcela,
              name:
                  '_ControladorVendaDePlanoBase.isCPFCartaoValidoPagamentoParcela'))
          .value;
  Computed<bool>? _$isValidadeCartaoValidaPagamentoParcelaComputed;

  @override
  bool get isValidadeCartaoValidaPagamentoParcela =>
      (_$isValidadeCartaoValidaPagamentoParcelaComputed ??= Computed<bool>(
              () => super.isValidadeCartaoValidaPagamentoParcela,
              name:
                  '_ControladorVendaDePlanoBase.isValidadeCartaoValidaPagamentoParcela'))
          .value;
  Computed<bool>? _$isDAdosCartaoValidoPagamentoParcelaComputed;

  @override
  bool get isDAdosCartaoValidoPagamentoParcela =>
      (_$isDAdosCartaoValidoPagamentoParcelaComputed ??= Computed<bool>(
              () => super.isDAdosCartaoValidoPagamentoParcela,
              name:
                  '_ControladorVendaDePlanoBase.isDAdosCartaoValidoPagamentoParcela'))
          .value;

  late final _$vendaIserirAtom =
      Atom(name: '_ControladorVendaDePlanoBase.vendaIserir', context: context);

  @override
  VendaNova get vendaIserir {
    _$vendaIserirAtom.reportRead();
    return super.vendaIserir;
  }

  @override
  set vendaIserir(VendaNova value) {
    _$vendaIserirAtom.reportWrite(value, super.vendaIserir, () {
      super.vendaIserir = value;
    });
  }

  late final _$statusConsultaDePlanosAtom = Atom(
      name: '_ControladorVendaDePlanoBase.statusConsultaDePlanos',
      context: context);

  @override
  ServiceStatus get statusConsultaDePlanos {
    _$statusConsultaDePlanosAtom.reportRead();
    return super.statusConsultaDePlanos;
  }

  @override
  set statusConsultaDePlanos(ServiceStatus value) {
    _$statusConsultaDePlanosAtom
        .reportWrite(value, super.statusConsultaDePlanos, () {
      super.statusConsultaDePlanos = value;
    });
  }

  late final _$statusSimulacaoPlanoAtom = Atom(
      name: '_ControladorVendaDePlanoBase.statusSimulacaoPlano',
      context: context);

  @override
  ServiceStatus get statusSimulacaoPlano {
    _$statusSimulacaoPlanoAtom.reportRead();
    return super.statusSimulacaoPlano;
  }

  @override
  set statusSimulacaoPlano(ServiceStatus value) {
    _$statusSimulacaoPlanoAtom.reportWrite(value, super.statusSimulacaoPlano,
        () {
      super.statusSimulacaoPlano = value;
    });
  }

  late final _$statusConsultaContratoAtom = Atom(
      name: '_ControladorVendaDePlanoBase.statusConsultaContrato',
      context: context);

  @override
  ServiceStatus get statusConsultaContrato {
    _$statusConsultaContratoAtom.reportRead();
    return super.statusConsultaContrato;
  }

  @override
  set statusConsultaContrato(ServiceStatus value) {
    _$statusConsultaContratoAtom
        .reportWrite(value, super.statusConsultaContrato, () {
      super.statusConsultaContrato = value;
    });
  }

  late final _$statusConsultarParcelasAtrasadasAtom = Atom(
      name: '_ControladorVendaDePlanoBase.statusConsultarParcelasAtrasadas',
      context: context);

  @override
  ServiceStatus get statusConsultarParcelasAtrasadas {
    _$statusConsultarParcelasAtrasadasAtom.reportRead();
    return super.statusConsultarParcelasAtrasadas;
  }

  @override
  set statusConsultarParcelasAtrasadas(ServiceStatus value) {
    _$statusConsultarParcelasAtrasadasAtom
        .reportWrite(value, super.statusConsultarParcelasAtrasadas, () {
      super.statusConsultarParcelasAtrasadas = value;
    });
  }

  late final _$statusPagamentoParcelasAtrasadasAtom = Atom(
      name: '_ControladorVendaDePlanoBase.statusPagamentoParcelasAtrasadas',
      context: context);

  @override
  ServiceStatus get statusPagamentoParcelasAtrasadas {
    _$statusPagamentoParcelasAtrasadasAtom.reportRead();
    return super.statusPagamentoParcelasAtrasadas;
  }

  @override
  set statusPagamentoParcelasAtrasadas(ServiceStatus value) {
    _$statusPagamentoParcelasAtrasadasAtom
        .reportWrite(value, super.statusPagamentoParcelasAtrasadas, () {
      super.statusPagamentoParcelasAtrasadas = value;
    });
  }

  late final _$alunoParcelasAtom = Atom(
      name: '_ControladorVendaDePlanoBase.alunoParcelas', context: context);

  @override
  ClienteParcelas? get alunoParcelas {
    _$alunoParcelasAtom.reportRead();
    return super.alunoParcelas;
  }

  @override
  set alunoParcelas(ClienteParcelas? value) {
    _$alunoParcelasAtom.reportWrite(value, super.alunoParcelas, () {
      super.alunoParcelas = value;
    });
  }

  late final _$mPagarParcelasAtom = Atom(
      name: '_ControladorVendaDePlanoBase.mPagarParcelas', context: context);

  @override
  PagamentoParcelasNova get mPagarParcelas {
    _$mPagarParcelasAtom.reportRead();
    return super.mPagarParcelas;
  }

  @override
  set mPagarParcelas(PagamentoParcelasNova value) {
    _$mPagarParcelasAtom.reportWrite(value, super.mPagarParcelas, () {
      super.mPagarParcelas = value;
    });
  }

  late final _$mCobrarParcelasPixAtom = Atom(
      name: '_ControladorVendaDePlanoBase.mCobrarParcelasPix',
      context: context);

  @override
  CobrarParcelaPix get mCobrarParcelasPix {
    _$mCobrarParcelasPixAtom.reportRead();
    return super.mCobrarParcelasPix;
  }

  @override
  set mCobrarParcelasPix(CobrarParcelaPix value) {
    _$mCobrarParcelasPixAtom.reportWrite(value, super.mCobrarParcelasPix, () {
      super.mCobrarParcelasPix = value;
    });
  }

  late final _$dadosPixPagamentoAtom = Atom(
      name: '_ControladorVendaDePlanoBase.dadosPixPagamento', context: context);

  @override
  Pix get dadosPixPagamento {
    _$dadosPixPagamentoAtom.reportRead();
    return super.dadosPixPagamento;
  }

  @override
  set dadosPixPagamento(Pix value) {
    _$dadosPixPagamentoAtom.reportWrite(value, super.dadosPixPagamento, () {
      super.dadosPixPagamento = value;
    });
  }

  late final _$statusConsultarParcelasAtrasadasPixAtom = Atom(
      name: '_ControladorVendaDePlanoBase.statusConsultarParcelasAtrasadasPix',
      context: context);

  @override
  ServiceStatus get statusConsultarParcelasAtrasadasPix {
    _$statusConsultarParcelasAtrasadasPixAtom.reportRead();
    return super.statusConsultarParcelasAtrasadasPix;
  }

  @override
  set statusConsultarParcelasAtrasadasPix(ServiceStatus value) {
    _$statusConsultarParcelasAtrasadasPixAtom
        .reportWrite(value, super.statusConsultarParcelasAtrasadasPix, () {
      super.statusConsultarParcelasAtrasadasPix = value;
    });
  }

  late final _$configVendasOnlineAtom = Atom(
      name: '_ControladorVendaDePlanoBase.configVendasOnline',
      context: context);

  @override
  ConfiguracaoVendasUnidade get configVendasOnline {
    _$configVendasOnlineAtom.reportRead();
    return super.configVendasOnline;
  }

  @override
  set configVendasOnline(ConfiguracaoVendasUnidade value) {
    _$configVendasOnlineAtom.reportWrite(value, super.configVendasOnline, () {
      super.configVendasOnline = value;
    });
  }

  late final _$configLinkPagamentoAtom = Atom(
      name: '_ControladorVendaDePlanoBase.configLinkPagamento',
      context: context);

  @override
  ConfigLinkPagamento get configLinkPagamento {
    _$configLinkPagamentoAtom.reportRead();
    return super.configLinkPagamento;
  }

  @override
  set configLinkPagamento(ConfigLinkPagamento value) {
    _$configLinkPagamentoAtom.reportWrite(value, super.configLinkPagamento, () {
      super.configLinkPagamento = value;
    });
  }

  late final _$mParcelasPagamentoAtom = Atom(
      name: '_ControladorVendaDePlanoBase.mParcelasPagamento',
      context: context);

  @override
  ParcelasPagamentoDto get mParcelasPagamento {
    _$mParcelasPagamentoAtom.reportRead();
    return super.mParcelasPagamento;
  }

  @override
  set mParcelasPagamento(ParcelasPagamentoDto value) {
    _$mParcelasPagamentoAtom.reportWrite(value, super.mParcelasPagamento, () {
      super.mParcelasPagamento = value;
    });
  }

  late final _$parcelasSelecionadasAtom = Atom(
      name: '_ControladorVendaDePlanoBase.parcelasSelecionadas',
      context: context);

  @override
  String get parcelasSelecionadas {
    _$parcelasSelecionadasAtom.reportRead();
    return super.parcelasSelecionadas;
  }

  @override
  set parcelasSelecionadas(String value) {
    _$parcelasSelecionadasAtom.reportWrite(value, super.parcelasSelecionadas,
        () {
      super.parcelasSelecionadas = value;
    });
  }

  late final _$parcelasSelecionadasParaBoletoAtom = Atom(
      name: '_ControladorVendaDePlanoBase.parcelasSelecionadasParaBoleto',
      context: context);

  @override
  List<num> get parcelasSelecionadasParaBoleto {
    _$parcelasSelecionadasParaBoletoAtom.reportRead();
    return super.parcelasSelecionadasParaBoleto;
  }

  @override
  set parcelasSelecionadasParaBoleto(List<num> value) {
    _$parcelasSelecionadasParaBoletoAtom
        .reportWrite(value, super.parcelasSelecionadasParaBoleto, () {
      super.parcelasSelecionadasParaBoleto = value;
    });
  }

  late final _$parcelasContratoSelecionadosAtom = Atom(
      name: '_ControladorVendaDePlanoBase.parcelasContratoSelecionados',
      context: context);

  @override
  List<ParcelaContrato> get parcelasContratoSelecionados {
    _$parcelasContratoSelecionadosAtom.reportRead();
    return super.parcelasContratoSelecionados;
  }

  @override
  set parcelasContratoSelecionados(List<ParcelaContrato> value) {
    _$parcelasContratoSelecionadosAtom
        .reportWrite(value, super.parcelasContratoSelecionados, () {
      super.parcelasContratoSelecionados = value;
    });
  }

  late final _$boletoResponseAtom = Atom(
      name: '_ControladorVendaDePlanoBase.boletoResponse', context: context);

  @override
  BoletoResponse? get boletoResponse {
    _$boletoResponseAtom.reportRead();
    return super.boletoResponse;
  }

  @override
  set boletoResponse(BoletoResponse? value) {
    _$boletoResponseAtom.reportWrite(value, super.boletoResponse, () {
      super.boletoResponse = value;
    });
  }

  late final _$boletoGeradoAtom =
      Atom(name: '_ControladorVendaDePlanoBase.boletoGerado', context: context);

  @override
  bool get boletoGerado {
    _$boletoGeradoAtom.reportRead();
    return super.boletoGerado;
  }

  @override
  set boletoGerado(bool value) {
    _$boletoGeradoAtom.reportWrite(value, super.boletoGerado, () {
      super.boletoGerado = value;
    });
  }

  late final _$concordoComOTermosAtom = Atom(
      name: '_ControladorVendaDePlanoBase.concordoComOTermos',
      context: context);

  @override
  bool get concordoComOTermos {
    _$concordoComOTermosAtom.reportRead();
    return super.concordoComOTermos;
  }

  @override
  set concordoComOTermos(bool value) {
    _$concordoComOTermosAtom.reportWrite(value, super.concordoComOTermos, () {
      super.concordoComOTermos = value;
    });
  }

  @override
  String toString() {
    return '''
vendaIserir: ${vendaIserir},
statusConsultaDePlanos: ${statusConsultaDePlanos},
statusSimulacaoPlano: ${statusSimulacaoPlano},
statusConsultaContrato: ${statusConsultaContrato},
statusConsultarParcelasAtrasadas: ${statusConsultarParcelasAtrasadas},
statusPagamentoParcelasAtrasadas: ${statusPagamentoParcelasAtrasadas},
alunoParcelas: ${alunoParcelas},
mPagarParcelas: ${mPagarParcelas},
mCobrarParcelasPix: ${mCobrarParcelasPix},
dadosPixPagamento: ${dadosPixPagamento},
statusConsultarParcelasAtrasadasPix: ${statusConsultarParcelasAtrasadasPix},
configVendasOnline: ${configVendasOnline},
configLinkPagamento: ${configLinkPagamento},
mParcelasPagamento: ${mParcelasPagamento},
parcelasSelecionadas: ${parcelasSelecionadas},
parcelasSelecionadasParaBoleto: ${parcelasSelecionadasParaBoleto},
parcelasContratoSelecionados: ${parcelasContratoSelecionados},
boletoResponse: ${boletoResponse},
boletoGerado: ${boletoGerado},
concordoComOTermos: ${concordoComOTermos},
isDadosPessoaisValido: ${isDadosPessoaisValido},
isCPFCartaoValido: ${isCPFCartaoValido},
isCPFDadosPessoaisValido: ${isCPFDadosPessoaisValido},
isValidadeCartaoValida: ${isValidadeCartaoValida},
isDAdosCartaoValido: ${isDAdosCartaoValido},
isVencimentoFaturaValido: ${isVencimentoFaturaValido},
isCPFCartaoValidoPagamentoParcela: ${isCPFCartaoValidoPagamentoParcela},
isValidadeCartaoValidaPagamentoParcela: ${isValidadeCartaoValidaPagamentoParcela},
isDAdosCartaoValidoPagamentoParcela: ${isDAdosCartaoValidoPagamentoParcela}
    ''';
  }
}
