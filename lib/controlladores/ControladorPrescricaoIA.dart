import 'package:app_treino/ServiceProvider/PrescricaoTreinoService.dart';
import 'package:app_treino/ServiceProvider/TreinoService.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoTreino.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/screens/prescricaoDeTreino/mockia/mock.ia.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';

part 'ControladorPrescricaoIA.g.dart';

class ControladorPrescricaoIA = _ControladorPrescricaoIABase with _$ControladorPrescricaoIA;

abstract class _ControladorPrescricaoIABase with Store {
  final treinoService = GetIt.I.get<TreinoService>();
  final prescricaoService = GetIt.I.get<PrescricaoTreinoService>();
  final controladorCliente = GetIt.I.get<ControladorCliente>();
  List<int> treinosAprovados = [];

  @observable
  ServiceStatus statusConsultarTreinosRevisao = ServiceStatus.Waiting;

  @observable
  ObservableList<DadosUsuarioProgramaIA> listaProgramasTreinoAguardandoRevisao = ObservableList<DadosUsuarioProgramaIA>();

  @observable
  DadosUsuarioProgramaIA? programaRevisaoIA;

  @observable
  Fichas? ficha;

  @observable
  ObservableList<ProgramaTreino> listaProgramasAprovados = ObservableList<ProgramaTreino>();

  @observable
  String tokenIA = '';

  var urlTreino = '';

  /**
   * Consulta os programas de treinos gerados pela IA para serem revisados
   * Utiliza o [statusConsultarTreinosRevisao] para gerenciar o estado
   * retorna uma lista de [DadosUsuarioProgramaIA]
   */
  @action
  consultarTreinosPorSituacao() async {
    statusConsultarTreinosRevisao = ServiceStatus.Waiting;
    listaProgramasTreinoAguardandoRevisao.clear();
    treinoService.consultarTreinosPorSituacao(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0, 10).then((value) {
      listaProgramasTreinoAguardandoRevisao.addAll(value);
      //   listaProgramasTreinoAguardandoRevisao.removeWhere((t) => treinosAprovados.any((e) => (t.ref ?? '').contains(e)));
      statusConsultarTreinosRevisao = ServiceStatus.Done;
    }).catchError((error) {
      statusConsultarTreinosRevisao = ServiceStatus.Error;
    });
  }

  @action
  iniciarRevisaoTreinoIA({required DadosUsuarioProgramaIA treinoIA, required Function() sucesso, required Function() carregando, required Function(String) erro}) {
    carregando();
    programaRevisaoIA = treinoIA;
    popularListaProgramasNoProgramaIA(programaRevisaoIA: programaRevisaoIA!).then((x) {
      sucesso();
    }).catchError((error) {
      programaRevisaoIA = null;
      error(error);
    });
  }

  /**
   * Cria uma fila de fichas pre-definidas para serem consultadas e após , adiciona em um programa de treino. 
   */
  Future popularListaProgramasNoProgramaIA({required DadosUsuarioProgramaIA programaRevisaoIA}) async {
    listaProgramasAprovados.clear();
    List<Future<dynamic>> consultas = [];
    for (final programas in programaRevisaoIA.programasGerados!) {
      consultas.add(GetIt.I.get<PrescricaoTreinoService>().consultarProgramaAluno(programas.id ?? 0, GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0));
    }
    return await Future.wait(consultas).then((value) {
      for (final element in value) {
        listaProgramasAprovados.add(element);
      }
    });
  }

  concluirRevisaoTreinoIA({required DadosUsuarioProgramaIA treinoIA, required Function() sucesso, required Function() carregando, required Function(String) erro}) {
    carregando();

    treinoService.aprovarTreinoPorIA(treinoIA.codigoPrograma!, controladorCliente.mUsuarioLogado!.codEmpresa!).then((value) {
      statusConsultarTreinosRevisao = ServiceStatus.Waiting;
      treinosAprovados.add(treinoIA.codigoPrograma!);
      consultarTreinosPorSituacao();
      sucesso();
    }).catchError((error) {
      statusConsultarTreinosRevisao = ServiceStatus.Error;
      erro(error);
    });
  }

  @action
  consultarTreinosPredefinidosIA({required num codigoFicha, required Function() sucesso, required Function() carregando, required Function(String) erro}) {
    carregando();
    treinoService.consultarFichasPredefinidasIA(urlTreino, GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 0, codigoFicha).then((value) {
      ficha = (value);
      sucesso();
    }).catchError((error) {
      erro(error);
    });
  }
}
