// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorDetalheAluno.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorDetalheAluno on _ControladorDetalheAlunoBase, Store {
  Computed<ServiceStatus>? _$statusConsultaCardNiveisComputed;

  @override
  ServiceStatus get statusConsultaCardNiveis =>
      (_$statusConsultaCardNiveisComputed ??= Computed<ServiceStatus>(
              () => super.statusConsultaCardNiveis,
              name: '_ControladorDetalheAlunoBase.statusConsultaCardNiveis'))
          .value;

  late final _$statusConsultaDetalhesDoAlunoAtom = Atom(
      name: '_ControladorDetalheAlunoBase.statusConsultaDetalhesDoAluno',
      context: context);

  @override
  ServiceStatus get statusConsultaDetalhesDoAluno {
    _$statusConsultaDetalhesDoAlunoAtom.reportRead();
    return super.statusConsultaDetalhesDoAluno;
  }

  @override
  set statusConsultaDetalhesDoAluno(ServiceStatus value) {
    _$statusConsultaDetalhesDoAlunoAtom
        .reportWrite(value, super.statusConsultaDetalhesDoAluno, () {
      super.statusConsultaDetalhesDoAluno = value;
    });
  }

  late final _$statusConsultaNivesAlunoAtom = Atom(
      name: '_ControladorDetalheAlunoBase.statusConsultaNivesAluno',
      context: context);

  @override
  ServiceStatus get statusConsultaNivesAluno {
    _$statusConsultaNivesAlunoAtom.reportRead();
    return super.statusConsultaNivesAluno;
  }

  @override
  set statusConsultaNivesAluno(ServiceStatus value) {
    _$statusConsultaNivesAlunoAtom
        .reportWrite(value, super.statusConsultaNivesAluno, () {
      super.statusConsultaNivesAluno = value;
    });
  }

  late final _$statusEdicaoNivelAlunoAtom = Atom(
      name: '_ControladorDetalheAlunoBase.statusEdicaoNivelAluno',
      context: context);

  @override
  ServiceStatus get statusEdicaoNivelAluno {
    _$statusEdicaoNivelAlunoAtom.reportRead();
    return super.statusEdicaoNivelAluno;
  }

  @override
  set statusEdicaoNivelAluno(ServiceStatus value) {
    _$statusEdicaoNivelAlunoAtom
        .reportWrite(value, super.statusEdicaoNivelAluno, () {
      super.statusEdicaoNivelAluno = value;
    });
  }

  @override
  String toString() {
    return '''
statusConsultaDetalhesDoAluno: ${statusConsultaDetalhesDoAluno},
statusConsultaNivesAluno: ${statusConsultaNivesAluno},
statusEdicaoNivelAluno: ${statusEdicaoNivelAluno},
statusConsultaCardNiveis: ${statusConsultaCardNiveis}
    ''';
  }
}
