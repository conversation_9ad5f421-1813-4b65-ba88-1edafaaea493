import 'dart:convert';
import 'dart:typed_data';

import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorEventBus.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoDeTreino.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/model/personal/AlunoDeProfessor.dart';
import 'package:app_treino/model/personal/CadastroAluno.dart';
import 'package:app_treino/model/personal/ObjetivoAluno.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorManterAluno.g.dart';

class ControladorManterAluno = _ControladorManterAlunoBase with _$ControladorManterAluno;

abstract class _ControladorManterAlunoBase with Store {
  @observable
  var mService = GetIt.I.get<PersonalService>();

  var mControladorApp = GetIt.I.get<ControladorApp>();

  @observable
  CadastroAluno cadastroAlunoManter = CadastroAluno();

  @observable
  ObservableList<ObjetivoAluno> mListaObjetivos = ObservableList<ObjetivoAluno>();

  @observable
  ObjetivoAluno? objetivoSalvarNoAluno;

  @observable
  bool consultouObjetivos = false;

  @observable
  bool falhaConsultaObjetivos = false;

  @observable
  Uint8List? imagemMemory;

  void clean() {
    imagemMemory = null;
    objetivoSalvarNoAluno = null;
    cadastroAlunoManter.clear();
  }

  @computed
  bool get isFotoValida => cadastroAlunoManter.imagemData != null && cadastroAlunoManter.imagemData!.isNotEmpty || imagemMemory != null;

  @computed
  bool get liberadoOCadastro => cadastroAlunoManter.nome!.isNotEmpty && cadastroAlunoManter.celular!.isNotEmpty && cadastroAlunoManter.email!.isNotEmpty;

  bool get isEdicao => cadastroAlunoManter.idAluno != null;

  void temAlunoParaEditar(AlunoDeProfessor aluno, Function() tem) {
    cadastroAlunoManter.idAluno = aluno.codigo;
      cadastroAlunoManter.nome = aluno.nome ?? '';
      cadastroAlunoManter.imagemData = aluno.urlFoto ?? '';
      cadastroAlunoManter.email = aluno.email ?? '';
      cadastroAlunoManter.celular = aluno.telefones ?? '';
      cadastroAlunoManter.idAluno = aluno.codigo;
      cadastroAlunoManter.situacao = aluno.situacao;
      cadastroAlunoManter.sexo = aluno.sexo;
      cadastroAlunoManter.objetivo = aluno.objetivos ?? '';
      if (cadastroAlunoManter.objetivo!.isNotEmpty && mListaObjetivos.isNotEmpty) {
        this.objetivoSalvarNoAluno = mListaObjetivos.firstWhere((element) => element.nome == cadastroAlunoManter.objetivo);
      }
      cadastroAlunoManter.dataNascimento = UtilDataHora.parseStringToDate(aluno.dataNascimento!) != null ? UtilDataHora.parseStringToDate(aluno.dataNascimento!)!.millisecondsSinceEpoch : null;

      tem();
  }

  void salvarAluno({Function()? sucesso, Function(String? mensagem)? falha}) {
    Future<CadastroAluno> mCall;
    var urlFoto = cadastroAlunoManter.imagemData;
    if (cadastroAlunoManter.imagemData != null && cadastroAlunoManter.imagemData!.contains('http')) {
      cadastroAlunoManter.imagemData = null;
    }
    if (imagemMemory != null) {
      cadastroAlunoManter.imagemData = const Base64Codec().encode(imagemMemory!);
      imagemMemory = null;
    }
    if (objetivoSalvarNoAluno != null) {
      cadastroAlunoManter.objetivo = objetivoSalvarNoAluno!.nome;
    }
    if (!isEdicao) {
      cadastroAlunoManter.situacao = 'AT';
      mCall = mService.salvarAluno(cadastroAlunoManter.toJson());
    } else {
      mCall = mService.manterAluno(cadastroAlunoManter.idAluno!, cadastroAlunoManter.toJson());
    }
    mCall.then((reponseValue) {
      if (reponseValue.imagemData != null) cadastroAlunoManter.imagemData = reponseValue.imagemData;
      if (urlFoto != null && urlFoto.contains('http') && cadastroAlunoManter.imagemData == null) cadastroAlunoManter.imagemData = urlFoto;
      cadastroAlunoManter.id = reponseValue.id;
      if (objetivoSalvarNoAluno != null) cadastroAlunoManter.objetivo = objetivoSalvarNoAluno!.nome;
      GetIt.I.get<ControladorPrescricaoDeTreino>().updateOrInsertAluno(cadastroAlunoManter);
      GetIt.I.get<ControladorEventBus>().eventBus.fire(AlunoCriadoEvent(cadastroAlunoManter));
      sucesso!();
    }).catchError((onError) {
      falha!(onError.message);
    });
  }

  void consultarObjetivos({Function()? carregando, Function()? sucesso, Function(String erro)? erro}) {
    if (consultouObjetivos && falhaConsultaObjetivos) {
      consultouObjetivos = false;
      falhaConsultaObjetivos = false;
    }
    mService.consultarObjetivos().then((value) {
      mListaObjetivos.clear();
      mListaObjetivos.addAll(value.objetivos!);
      consultouObjetivos = true;
      falhaConsultaObjetivos = false;
    }).catchError((onError) {
      falhaConsultaObjetivos = true;
      consultouObjetivos = true;
    });
  }

  limparTudo() {
    mListaObjetivos.clear();
    consultouObjetivos = false;
    falhaConsultaObjetivos = false;
    clean();
  }

  String get corpoMensagemInvite =>
      'Agora você pode seguir seus treinos pelo meu app ${F.title}.\n\nBaixe o app: https://personalfit.page.link/download\n\nProcure por: *${mControladorApp.mClienteAppSelecionado!.nomeDoApp}*\nInsira seu número de telefone: ${cadastroAlunoManter.celular}\n\nE acesse seu treino!\nBora treinar?!';
}
