// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorExecucaoTreino.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorExecucaoTreino on _ControladorExecucaoTreinoBase, Store {
  late final _$treinoIniciadoAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.treinoIniciado', context: context);

  @override
  bool get treinoIniciado {
    _$treinoIniciadoAtom.reportRead();
    return super.treinoIniciado;
  }

  @override
  set treinoIniciado(bool value) {
    _$treinoIniciadoAtom.reportWrite(value, super.treinoIniciado, () {
      super.treinoIniciado = value;
    });
  }

  late final _$descansoAtom =
      Atom(name: '_ControladorExecucaoTreinoBase.descanso', context: context);

  @override
  bool get descanso {
    _$descansoAtom.reportRead();
    return super.descanso;
  }

  @override
  set descanso(bool value) {
    _$descansoAtom.reportWrite(value, super.descanso, () {
      super.descanso = value;
    });
  }

  late final _$proximoExercicioAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.proximoExercicio',
      context: context);

  @override
  bool get proximoExercicio {
    _$proximoExercicioAtom.reportRead();
    return super.proximoExercicio;
  }

  @override
  set proximoExercicio(bool value) {
    _$proximoExercicioAtom.reportWrite(value, super.proximoExercicio, () {
      super.proximoExercicio = value;
    });
  }

  late final _$duracaoTotalDescansoExercicioAtualAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.duracaoTotalDescansoExercicioAtual',
      context: context);

  @override
  int get duracaoTotalDescansoExercicioAtual {
    _$duracaoTotalDescansoExercicioAtualAtom.reportRead();
    return super.duracaoTotalDescansoExercicioAtual;
  }

  @override
  set duracaoTotalDescansoExercicioAtual(int value) {
    _$duracaoTotalDescansoExercicioAtualAtom
        .reportWrite(value, super.duracaoTotalDescansoExercicioAtual, () {
      super.duracaoTotalDescansoExercicioAtual = value;
    });
  }

  late final _$contadorDescansoExercicioAtualAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.contadorDescansoExercicioAtual',
      context: context);

  @override
  int get contadorDescansoExercicioAtual {
    _$contadorDescansoExercicioAtualAtom.reportRead();
    return super.contadorDescansoExercicioAtual;
  }

  @override
  set contadorDescansoExercicioAtual(int value) {
    _$contadorDescansoExercicioAtualAtom
        .reportWrite(value, super.contadorDescansoExercicioAtual, () {
      super.contadorDescansoExercicioAtual = value;
    });
  }

  late final _$duracaoTreinoAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.duracaoTreino', context: context);

  @override
  int get duracaoTreino {
    _$duracaoTreinoAtom.reportRead();
    return super.duracaoTreino;
  }

  @override
  set duracaoTreino(int value) {
    _$duracaoTreinoAtom.reportWrite(value, super.duracaoTreino, () {
      super.duracaoTreino = value;
    });
  }

  late final _$duracaoTreinoStringAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.duracaoTreinoString',
      context: context);

  @override
  String get duracaoTreinoString {
    _$duracaoTreinoStringAtom.reportRead();
    return super.duracaoTreinoString;
  }

  @override
  set duracaoTreinoString(String value) {
    _$duracaoTreinoStringAtom.reportWrite(value, super.duracaoTreinoString, () {
      super.duracaoTreinoString = value;
    });
  }

  late final _$horarioQueDispositivoInativouAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.horarioQueDispositivoInativou',
      context: context);

  @override
  int get horarioQueDispositivoInativou {
    _$horarioQueDispositivoInativouAtom.reportRead();
    return super.horarioQueDispositivoInativou;
  }

  @override
  set horarioQueDispositivoInativou(int value) {
    _$horarioQueDispositivoInativouAtom
        .reportWrite(value, super.horarioQueDispositivoInativou, () {
      super.horarioQueDispositivoInativou = value;
    });
  }

  late final _$treinoEmExecucaoAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.treinoEmExecucao',
      context: context);

  @override
  Ficha? get treinoEmExecucao {
    _$treinoEmExecucaoAtom.reportRead();
    return super.treinoEmExecucao;
  }

  @override
  set treinoEmExecucao(Ficha? value) {
    _$treinoEmExecucaoAtom.reportWrite(value, super.treinoEmExecucao, () {
      super.treinoEmExecucao = value;
    });
  }

  late final _$indexExercicioEmExecucaoAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.indexExercicioEmExecucao',
      context: context);

  @override
  int get indexExercicioEmExecucao {
    _$indexExercicioEmExecucaoAtom.reportRead();
    return super.indexExercicioEmExecucao;
  }

  @override
  set indexExercicioEmExecucao(int value) {
    _$indexExercicioEmExecucaoAtom
        .reportWrite(value, super.indexExercicioEmExecucao, () {
      super.indexExercicioEmExecucao = value;
    });
  }

  late final _$indexSelecionadoAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.indexSelecionado',
      context: context);

  @override
  int get indexSelecionado {
    _$indexSelecionadoAtom.reportRead();
    return super.indexSelecionado;
  }

  @override
  set indexSelecionado(int value) {
    _$indexSelecionadoAtom.reportWrite(value, super.indexSelecionado, () {
      super.indexSelecionado = value;
    });
  }

  late final _$indexSerieExercicioEmExecucaoAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.indexSerieExercicioEmExecucao',
      context: context);

  @override
  int get indexSerieExercicioEmExecucao {
    _$indexSerieExercicioEmExecucaoAtom.reportRead();
    return super.indexSerieExercicioEmExecucao;
  }

  @override
  set indexSerieExercicioEmExecucao(int value) {
    _$indexSerieExercicioEmExecucaoAtom
        .reportWrite(value, super.indexSerieExercicioEmExecucao, () {
      super.indexSerieExercicioEmExecucao = value;
    });
  }

  late final _$temSuporteAppleWatchAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.temSuporteAppleWatch',
      context: context);

  @override
  bool get temSuporteAppleWatch {
    _$temSuporteAppleWatchAtom.reportRead();
    return super.temSuporteAppleWatch;
  }

  @override
  set temSuporteAppleWatch(bool value) {
    _$temSuporteAppleWatchAtom.reportWrite(value, super.temSuporteAppleWatch,
        () {
      super.temSuporteAppleWatch = value;
    });
  }

  late final _$estaConectadoAppleWatchAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.estaConectadoAppleWatch',
      context: context);

  @override
  bool get estaConectadoAppleWatch {
    _$estaConectadoAppleWatchAtom.reportRead();
    return super.estaConectadoAppleWatch;
  }

  @override
  set estaConectadoAppleWatch(bool value) {
    _$estaConectadoAppleWatchAtom
        .reportWrite(value, super.estaConectadoAppleWatch, () {
      super.estaConectadoAppleWatch = value;
    });
  }

  late final _$estaNoProximoAppleWachAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.estaNoProximoAppleWach',
      context: context);

  @override
  bool get estaNoProximoAppleWach {
    _$estaNoProximoAppleWachAtom.reportRead();
    return super.estaNoProximoAppleWach;
  }

  @override
  set estaNoProximoAppleWach(bool value) {
    _$estaNoProximoAppleWachAtom
        .reportWrite(value, super.estaNoProximoAppleWach, () {
      super.estaNoProximoAppleWach = value;
    });
  }

  late final _$concluiuExercicioAppleWatchTelaDetalhesAtom = Atom(
      name:
          '_ControladorExecucaoTreinoBase.concluiuExercicioAppleWatchTelaDetalhes',
      context: context);

  @override
  bool get concluiuExercicioAppleWatchTelaDetalhes {
    _$concluiuExercicioAppleWatchTelaDetalhesAtom.reportRead();
    return super.concluiuExercicioAppleWatchTelaDetalhes;
  }

  @override
  set concluiuExercicioAppleWatchTelaDetalhes(bool value) {
    _$concluiuExercicioAppleWatchTelaDetalhesAtom
        .reportWrite(value, super.concluiuExercicioAppleWatchTelaDetalhes, () {
      super.concluiuExercicioAppleWatchTelaDetalhes = value;
    });
  }

  late final _$filtrandoNomeExercicioAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.filtrandoNomeExercicio',
      context: context);

  @override
  ServiceStatus get filtrandoNomeExercicio {
    _$filtrandoNomeExercicioAtom.reportRead();
    return super.filtrandoNomeExercicio;
  }

  @override
  set filtrandoNomeExercicio(ServiceStatus value) {
    _$filtrandoNomeExercicioAtom
        .reportWrite(value, super.filtrandoNomeExercicio, () {
      super.filtrandoNomeExercicio = value;
    });
  }

  late final _$listaExerciciosTreinoExtraAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.listaExerciciosTreinoExtra',
      context: context);

  @override
  ObservableList<ExerciciosTreinoExtra> get listaExerciciosTreinoExtra {
    _$listaExerciciosTreinoExtraAtom.reportRead();
    return super.listaExerciciosTreinoExtra;
  }

  @override
  set listaExerciciosTreinoExtra(ObservableList<ExerciciosTreinoExtra> value) {
    _$listaExerciciosTreinoExtraAtom
        .reportWrite(value, super.listaExerciciosTreinoExtra, () {
      super.listaExerciciosTreinoExtra = value;
    });
  }

  late final _$fichaSelecionadaTreinoExtraAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.fichaSelecionadaTreinoExtra',
      context: context);

  @override
  Ficha? get fichaSelecionadaTreinoExtra {
    _$fichaSelecionadaTreinoExtraAtom.reportRead();
    return super.fichaSelecionadaTreinoExtra;
  }

  @override
  set fichaSelecionadaTreinoExtra(Ficha? value) {
    _$fichaSelecionadaTreinoExtraAtom
        .reportWrite(value, super.fichaSelecionadaTreinoExtra, () {
      super.fichaSelecionadaTreinoExtra = value;
    });
  }

  late final _$listaExerciciosTreinoExtraBackupAtom = Atom(
      name: '_ControladorExecucaoTreinoBase.listaExerciciosTreinoExtraBackup',
      context: context);

  @override
  ObservableList<ExerciciosTreinoExtra> get listaExerciciosTreinoExtraBackup {
    _$listaExerciciosTreinoExtraBackupAtom.reportRead();
    return super.listaExerciciosTreinoExtraBackup;
  }

  @override
  set listaExerciciosTreinoExtraBackup(
      ObservableList<ExerciciosTreinoExtra> value) {
    _$listaExerciciosTreinoExtraBackupAtom
        .reportWrite(value, super.listaExerciciosTreinoExtraBackup, () {
      super.listaExerciciosTreinoExtraBackup = value;
    });
  }

  late final _$getTreinoEmExecucaoAsyncAction = AsyncAction(
      '_ControladorExecucaoTreinoBase.getTreinoEmExecucao',
      context: context);

  @override
  Future<Ficha?> getTreinoEmExecucao() {
    return _$getTreinoEmExecucaoAsyncAction
        .run(() => super.getTreinoEmExecucao());
  }

  late final _$iniciarTimerAsyncAction = AsyncAction(
      '_ControladorExecucaoTreinoBase.iniciarTimer',
      context: context);

  @override
  Future iniciarTimer(
      {required TipoContador tipo,
      required dynamic Function() finalizouDescanso}) {
    return _$iniciarTimerAsyncAction.run(() =>
        super.iniciarTimer(tipo: tipo, finalizouDescanso: finalizouDescanso));
  }

  late final _$finalizarTreinoEmExecucaoAsyncAction = AsyncAction(
      '_ControladorExecucaoTreinoBase.finalizarTreinoEmExecucao',
      context: context);

  @override
  Future finalizarTreinoEmExecucao() {
    return _$finalizarTreinoEmExecucaoAsyncAction
        .run(() => super.finalizarTreinoEmExecucao());
  }

  late final _$_ControladorExecucaoTreinoBaseActionController =
      ActionController(
          name: '_ControladorExecucaoTreinoBase', context: context);

  @override
  dynamic concluirTodasSeries(
      {required dynamic Function() concluiu,
      required dynamic Function() concluiuTreino}) {
    final _$actionInfo =
        _$_ControladorExecucaoTreinoBaseActionController.startAction(
            name: '_ControladorExecucaoTreinoBase.concluirTodasSeries');
    try {
      return super.concluirTodasSeries(
          concluiu: concluiu, concluiuTreino: concluiuTreino);
    } finally {
      _$_ControladorExecucaoTreinoBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic concluirExercicio(
      {required bool veioDoDescanso,
      required dynamic Function() iniciarDescanso,
      required dynamic Function() concluiuExercicio,
      required dynamic Function() concluiuTreino,
      required dynamic Function() concluiuDescanso}) {
    final _$actionInfo = _$_ControladorExecucaoTreinoBaseActionController
        .startAction(name: '_ControladorExecucaoTreinoBase.concluirExercicio');
    try {
      return super.concluirExercicio(
          veioDoDescanso: veioDoDescanso,
          iniciarDescanso: iniciarDescanso,
          concluiuExercicio: concluiuExercicio,
          concluiuTreino: concluiuTreino,
          concluiuDescanso: concluiuDescanso);
    } finally {
      _$_ControladorExecucaoTreinoBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  double quantidadeDeAtividadesConcluidas() {
    final _$actionInfo =
        _$_ControladorExecucaoTreinoBaseActionController.startAction(
            name:
                '_ControladorExecucaoTreinoBase.quantidadeDeAtividadesConcluidas');
    try {
      return super.quantidadeDeAtividadesConcluidas();
    } finally {
      _$_ControladorExecucaoTreinoBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  int quantidadeDeSeriesConcluidas() {
    final _$actionInfo =
        _$_ControladorExecucaoTreinoBaseActionController.startAction(
            name:
                '_ControladorExecucaoTreinoBase.quantidadeDeSeriesConcluidas');
    try {
      return super.quantidadeDeSeriesConcluidas();
    } finally {
      _$_ControladorExecucaoTreinoBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic salvarTreinoEmExecucao({required Ficha fichaEmExecucao}) {
    final _$actionInfo =
        _$_ControladorExecucaoTreinoBaseActionController.startAction(
            name: '_ControladorExecucaoTreinoBase.salvarTreinoEmExecucao');
    try {
      return super.salvarTreinoEmExecucao(fichaEmExecucao: fichaEmExecucao);
    } finally {
      _$_ControladorExecucaoTreinoBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void iniciarCronometroTreino() {
    final _$actionInfo =
        _$_ControladorExecucaoTreinoBaseActionController.startAction(
            name: '_ControladorExecucaoTreinoBase.iniciarCronometroTreino');
    try {
      return super.iniciarCronometroTreino();
    } finally {
      _$_ControladorExecucaoTreinoBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void editarCargaOnline(
      {required List<SeriesAtividade> series,
      required dynamic Function() carregando,
      required dynamic Function() sucesso,
      required dynamic Function() error}) {
    final _$actionInfo = _$_ControladorExecucaoTreinoBaseActionController
        .startAction(name: '_ControladorExecucaoTreinoBase.editarCargaOnline');
    try {
      return super.editarCargaOnline(
          series: series,
          carregando: carregando,
          sucesso: sucesso,
          error: error);
    } finally {
      _$_ControladorExecucaoTreinoBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void pularDescanso() {
    final _$actionInfo = _$_ControladorExecucaoTreinoBaseActionController
        .startAction(name: '_ControladorExecucaoTreinoBase.pularDescanso');
    try {
      return super.pularDescanso();
    } finally {
      _$_ControladorExecucaoTreinoBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void consultarExercicioPorNome({required String nome}) {
    final _$actionInfo =
        _$_ControladorExecucaoTreinoBaseActionController.startAction(
            name: '_ControladorExecucaoTreinoBase.consultarExercicioPorNome');
    try {
      return super.consultarExercicioPorNome(nome: nome);
    } finally {
      _$_ControladorExecucaoTreinoBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
treinoIniciado: ${treinoIniciado},
descanso: ${descanso},
proximoExercicio: ${proximoExercicio},
duracaoTotalDescansoExercicioAtual: ${duracaoTotalDescansoExercicioAtual},
contadorDescansoExercicioAtual: ${contadorDescansoExercicioAtual},
duracaoTreino: ${duracaoTreino},
duracaoTreinoString: ${duracaoTreinoString},
horarioQueDispositivoInativou: ${horarioQueDispositivoInativou},
treinoEmExecucao: ${treinoEmExecucao},
indexExercicioEmExecucao: ${indexExercicioEmExecucao},
indexSelecionado: ${indexSelecionado},
indexSerieExercicioEmExecucao: ${indexSerieExercicioEmExecucao},
temSuporteAppleWatch: ${temSuporteAppleWatch},
estaConectadoAppleWatch: ${estaConectadoAppleWatch},
estaNoProximoAppleWach: ${estaNoProximoAppleWach},
concluiuExercicioAppleWatchTelaDetalhes: ${concluiuExercicioAppleWatchTelaDetalhes},
filtrandoNomeExercicio: ${filtrandoNomeExercicio},
listaExerciciosTreinoExtra: ${listaExerciciosTreinoExtra},
fichaSelecionadaTreinoExtra: ${fichaSelecionadaTreinoExtra},
listaExerciciosTreinoExtraBackup: ${listaExerciciosTreinoExtraBackup}
    ''';
  }
}
