// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorContratoUsuario.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorContratoUsuario on _ControladorContratoUsuarioBase, Store {
  late final _$mStatusConsultaContratosAtom = Atom(
      name: '_ControladorContratoUsuarioBase.mStatusConsultaContratos',
      context: context);

  @override
  ServiceStatus get mStatusConsultaContratos {
    _$mStatusConsultaContratosAtom.reportRead();
    return super.mStatusConsultaContratos;
  }

  @override
  set mStatusConsultaContratos(ServiceStatus value) {
    _$mStatusConsultaContratosAtom
        .reportWrite(value, super.mStatusConsultaContratos, () {
      super.mStatusConsultaContratos = value;
    });
  }

  late final _$mStatusSimulacaoRenovacaoAtom = Atom(
      name: '_ControladorContratoUsuarioBase.mStatusSimulacaoRenovacao',
      context: context);

  @override
  ServiceStatus get mStatusSimulacaoRenovacao {
    _$mStatusSimulacaoRenovacaoAtom.reportRead();
    return super.mStatusSimulacaoRenovacao;
  }

  @override
  set mStatusSimulacaoRenovacao(ServiceStatus value) {
    _$mStatusSimulacaoRenovacaoAtom
        .reportWrite(value, super.mStatusSimulacaoRenovacao, () {
      super.mStatusSimulacaoRenovacao = value;
    });
  }

  late final _$mStatusConsultaTrancamentoAtom = Atom(
      name: '_ControladorContratoUsuarioBase.mStatusConsultaTrancamento',
      context: context);

  @override
  ServiceStatus get mStatusConsultaTrancamento {
    _$mStatusConsultaTrancamentoAtom.reportRead();
    return super.mStatusConsultaTrancamento;
  }

  @override
  set mStatusConsultaTrancamento(ServiceStatus value) {
    _$mStatusConsultaTrancamentoAtom
        .reportWrite(value, super.mStatusConsultaTrancamento, () {
      super.mStatusConsultaTrancamento = value;
    });
  }

  late final _$mStatusValidacaoContratoAtom = Atom(
      name: '_ControladorContratoUsuarioBase.mStatusValidacaoContrato',
      context: context);

  @override
  ServiceStatus get mStatusValidacaoContrato {
    _$mStatusValidacaoContratoAtom.reportRead();
    return super.mStatusValidacaoContrato;
  }

  @override
  set mStatusValidacaoContrato(ServiceStatus value) {
    _$mStatusValidacaoContratoAtom
        .reportWrite(value, super.mStatusValidacaoContrato, () {
      super.mStatusValidacaoContrato = value;
    });
  }

  late final _$mStatusSimulacaoCarenciaAtom = Atom(
      name: '_ControladorContratoUsuarioBase.mStatusSimulacaoCarencia',
      context: context);

  @override
  ServiceStatus get mStatusSimulacaoCarencia {
    _$mStatusSimulacaoCarenciaAtom.reportRead();
    return super.mStatusSimulacaoCarencia;
  }

  @override
  set mStatusSimulacaoCarencia(ServiceStatus value) {
    _$mStatusSimulacaoCarenciaAtom
        .reportWrite(value, super.mStatusSimulacaoCarencia, () {
      super.mStatusSimulacaoCarencia = value;
    });
  }

  late final _$mStatusConsultaHistoricoCreditoAtom = Atom(
      name: '_ControladorContratoUsuarioBase.mStatusConsultaHistoricoCredito',
      context: context);

  @override
  ServiceStatus get mStatusConsultaHistoricoCredito {
    _$mStatusConsultaHistoricoCreditoAtom.reportRead();
    return super.mStatusConsultaHistoricoCredito;
  }

  @override
  set mStatusConsultaHistoricoCredito(ServiceStatus value) {
    _$mStatusConsultaHistoricoCreditoAtom
        .reportWrite(value, super.mStatusConsultaHistoricoCredito, () {
      super.mStatusConsultaHistoricoCredito = value;
    });
  }

  late final _$isPlanoSelecionadoAtom = Atom(
      name: '_ControladorContratoUsuarioBase.isPlanoSelecionado',
      context: context);

  @override
  bool get isPlanoSelecionado {
    _$isPlanoSelecionadoAtom.reportRead();
    return super.isPlanoSelecionado;
  }

  @override
  set isPlanoSelecionado(bool value) {
    _$isPlanoSelecionadoAtom.reportWrite(value, super.isPlanoSelecionado, () {
      super.isPlanoSelecionado = value;
    });
  }

  late final _$isMeuscontratoSelecionadoAtom = Atom(
      name: '_ControladorContratoUsuarioBase.isMeuscontratoSelecionado',
      context: context);

  @override
  bool get isMeuscontratoSelecionado {
    _$isMeuscontratoSelecionadoAtom.reportRead();
    return super.isMeuscontratoSelecionado;
  }

  @override
  set isMeuscontratoSelecionado(bool value) {
    _$isMeuscontratoSelecionadoAtom
        .reportWrite(value, super.isMeuscontratoSelecionado, () {
      super.isMeuscontratoSelecionado = value;
    });
  }

  late final _$mContratoSelecionadoAtom = Atom(
      name: '_ControladorContratoUsuarioBase.mContratoSelecionado',
      context: context);

  @override
  ContratoUsuario? get mContratoSelecionado {
    _$mContratoSelecionadoAtom.reportRead();
    return super.mContratoSelecionado;
  }

  @override
  set mContratoSelecionado(ContratoUsuario? value) {
    _$mContratoSelecionadoAtom.reportWrite(value, super.mContratoSelecionado,
        () {
      super.mContratoSelecionado = value;
    });
  }

  late final _$contratoAssinaturaAtom = Atom(
      name: '_ControladorContratoUsuarioBase.contratoAssinatura',
      context: context);

  @override
  ObservableList<ContratoAssinatura> get contratoAssinatura {
    _$contratoAssinaturaAtom.reportRead();
    return super.contratoAssinatura;
  }

  @override
  set contratoAssinatura(ObservableList<ContratoAssinatura> value) {
    _$contratoAssinaturaAtom.reportWrite(value, super.contratoAssinatura, () {
      super.contratoAssinatura = value;
    });
  }

  late final _$operacaoesContratoAtom = Atom(
      name: '_ControladorContratoUsuarioBase.operacaoesContrato',
      context: context);

  @override
  List<ContratoOperacao> get operacaoesContrato {
    _$operacaoesContratoAtom.reportRead();
    return super.operacaoesContrato;
  }

  @override
  set operacaoesContrato(List<ContratoOperacao> value) {
    _$operacaoesContratoAtom.reportWrite(value, super.operacaoesContrato, () {
      super.operacaoesContrato = value;
    });
  }

  late final _$mCreditosPermitidosTransferirAtom = Atom(
      name: '_ControladorContratoUsuarioBase.mCreditosPermitidosTransferir',
      context: context);

  @override
  CreditoPermitidos? get mCreditosPermitidosTransferir {
    _$mCreditosPermitidosTransferirAtom.reportRead();
    return super.mCreditosPermitidosTransferir;
  }

  @override
  set mCreditosPermitidosTransferir(CreditoPermitidos? value) {
    _$mCreditosPermitidosTransferirAtom
        .reportWrite(value, super.mCreditosPermitidosTransferir, () {
      super.mCreditosPermitidosTransferir = value;
    });
  }

  late final _$mStatusConsultaCreditosPermitidosAtom = Atom(
      name: '_ControladorContratoUsuarioBase.mStatusConsultaCreditosPermitidos',
      context: context);

  @override
  ServiceStatus get mStatusConsultaCreditosPermitidos {
    _$mStatusConsultaCreditosPermitidosAtom.reportRead();
    return super.mStatusConsultaCreditosPermitidos;
  }

  @override
  set mStatusConsultaCreditosPermitidos(ServiceStatus value) {
    _$mStatusConsultaCreditosPermitidosAtom
        .reportWrite(value, super.mStatusConsultaCreditosPermitidos, () {
      super.mStatusConsultaCreditosPermitidos = value;
    });
  }

  late final _$mConsultaAlunoDestinatarioCreditoAtom = Atom(
      name: '_ControladorContratoUsuarioBase.mConsultaAlunoDestinatarioCredito',
      context: context);

  @override
  ServiceStatus get mConsultaAlunoDestinatarioCredito {
    _$mConsultaAlunoDestinatarioCreditoAtom.reportRead();
    return super.mConsultaAlunoDestinatarioCredito;
  }

  @override
  set mConsultaAlunoDestinatarioCredito(ServiceStatus value) {
    _$mConsultaAlunoDestinatarioCreditoAtom
        .reportWrite(value, super.mConsultaAlunoDestinatarioCredito, () {
      super.mConsultaAlunoDestinatarioCredito = value;
    });
  }

  late final _$mAlunoDestinatarioCreditoAtom = Atom(
      name: '_ControladorContratoUsuarioBase.mAlunoDestinatarioCredito',
      context: context);

  @override
  AlunoDestinatario? get mAlunoDestinatarioCredito {
    _$mAlunoDestinatarioCreditoAtom.reportRead();
    return super.mAlunoDestinatarioCredito;
  }

  @override
  set mAlunoDestinatarioCredito(AlunoDestinatario? value) {
    _$mAlunoDestinatarioCreditoAtom
        .reportWrite(value, super.mAlunoDestinatarioCredito, () {
      super.mAlunoDestinatarioCredito = value;
    });
  }

  @override
  String toString() {
    return '''
mStatusConsultaContratos: ${mStatusConsultaContratos},
mStatusSimulacaoRenovacao: ${mStatusSimulacaoRenovacao},
mStatusConsultaTrancamento: ${mStatusConsultaTrancamento},
mStatusValidacaoContrato: ${mStatusValidacaoContrato},
mStatusSimulacaoCarencia: ${mStatusSimulacaoCarencia},
mStatusConsultaHistoricoCredito: ${mStatusConsultaHistoricoCredito},
isPlanoSelecionado: ${isPlanoSelecionado},
isMeuscontratoSelecionado: ${isMeuscontratoSelecionado},
mContratoSelecionado: ${mContratoSelecionado},
contratoAssinatura: ${contratoAssinatura},
operacaoesContrato: ${operacaoesContrato},
mCreditosPermitidosTransferir: ${mCreditosPermitidosTransferir},
mStatusConsultaCreditosPermitidos: ${mStatusConsultaCreditosPermitidos},
mConsultaAlunoDestinatarioCredito: ${mConsultaAlunoDestinatarioCredito},
mAlunoDestinatarioCredito: ${mAlunoDestinatarioCredito}
    ''';
  }
}
