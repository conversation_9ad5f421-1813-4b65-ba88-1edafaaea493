import 'package:app_treino/ServiceProvider/PlannerService.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorTreinoAluno.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/planner/RefeicaoPlanner.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:app_treino/screens/prescricaoDeTreino/mockia/mock.ia.dart';
import 'package:app_treino/util/debug_utils.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:diacritic/diacritic.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:app_treino/model/planner/DicasPlanner.dart';
import 'package:app_treino/model/planner/RefeicaoPlanner.dart' as enumMap;
part 'controladorPlanner.g.dart';

class ControladorPlanner = _ControladorPlannerBase with _$ControladorPlanner;

abstract class _ControladorPlannerBase with Store {
  bool mUsuarioSemPlano = false;
  bool empresaComPoucasRefeicoes = false;
  ControladorTreinoAluno mControladorTreinoAluno = GetIt.I.get();

  PlanoMontado? mPlanoUsuario = PlanoMontado();

  @observable
  ServiceStatus statusCarregandoDicas = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusCarregandoHistoricoCompras = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusCarregandoReceitas = ServiceStatus.Waiting;

  bool estaPesquisando = false;

  @observable
  ServiceStatus statusCarregandoFichaDoDia = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusCarregandoPlanoRefeicao = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusCarregandoDia = ServiceStatus.Done;

  @observable
  String tituloErro = '';

  @observable
  String subtituloErro = '';

  DateTime? dataSelecionada = DateTime.now();

  void setDataSelecionada(DateTime? dateTime, Function()? sucesso) {
    statusCarregandoDia = ServiceStatus.Waiting;
    Future.delayed(const Duration(seconds: 1)).then((value) {
      dataSelecionada = dateTime;
      statusCarregandoDia = ServiceStatus.Done;
      sucesso?.call();
    });
  }

  List<DicaPlanner> mDicas = [];
  List<RefeicaoPlanner> mRefeicoes = [];
  List<RefeicaoPlanner> mRefeicoesFiltradas = [];
  List<ResponsavelRefeicao> mResponsaveis = [];

  final _service = GetIt.I.get<PlannerService>();
  final _mCliente = GetIt.I.get<ControladorCliente>();
  // ignore: unused_field
  final _mApp = GetIt.I.get<ControladorCliente>();
  String listaIngredientesAmigavel(RefeicaoPlanner? refeicaoPlanner) {
    String amigavel = '';
    refeicaoPlanner?.ingredientes?.forEach((element) {
      amigavel += '${element.nome}\n'; //${element.quantidade} ${element.unidadeDeMedida} -
    });
    return amigavel;
  }

  num? obterNotaQueUsuarioDeu(RefeicaoPlanner? refeicaoPlanner) {
    if (refeicaoPlanner?.notas?.any((element) => element.refUsuario == _mCliente.mUsuarioAuth!.uid) ?? false) {
      return refeicaoPlanner!.notas!.firstWhere((element) => element.refUsuario == _mCliente.mUsuarioAuth!.uid).nota;
    } else {
      return 0;
    }
  }

  PlanoUsuarioExibir? mPlanoRefeicaoAluno;
  void consultarPlanoDeRefeicoes({Function()? carregando, Function(PlanoUsuarioExibir? nota)? sucesso, Function(String? falha)? falha, bool force = false}) {
    carregando?.call();

    statusCarregandoPlanoRefeicao = ServiceStatus.Waiting;
    bool temRefeicoesParahj =
        mPlanoRefeicaoAluno?.proximosDias?.where((element) => UtilDataHora.getDiaMesAno(dateTime: element.data).contains(UtilDataHora.getDiaMesAno(dateTime: dataSelecionada))).isNotEmpty ??
            false;
    if (mPlanoRefeicaoAluno != null && temRefeicoesParahj && !force) {
      sucesso?.call(mPlanoRefeicaoAluno);
      statusCarregandoPlanoRefeicao = ServiceStatus.Done;
    } else {
      _service.obeterListaRefeicoes(_mCliente.mUsuarioAuth!.uid!, true, doDia: !temRefeicoesParahj ? UtilDataHora.getDiaMesAno(dateTime: dataSelecionada) : null).then((planoRefeicao) {
        mPlanoRefeicaoAluno = planoRefeicao;
        mPlanoUsuario = planoRefeicao.planoMontado;
        sucesso?.call(planoRefeicao);
        mUsuarioSemPlano = false;
        statusCarregandoPlanoRefeicao = ServiceStatus.Done;
      }).catchError((onError) {
        empresaComPoucasRefeicoes = 'Não ha refeições suficientes na base para gerar o plano'.toLowerCase().contains(onError?.response?.data['erro']?.toString().toLowerCase() ?? '---');
        if (onError?.response?.data['erro']?.contains('O usuário não possui um programa') ?? false) {
          mUsuarioSemPlano = true;
          statusCarregandoPlanoRefeicao = ServiceStatus.Done;
        } else {
          statusCarregandoPlanoRefeicao = ServiceStatus.Error;
          falha?.call(onError.message);
        }
      });
    }
  }

  void obterFichaDoDia({Function()? sucesso, Function(String falha)? falha, Function()? carregando}) {
    carregando?.call();
    if (GetIt.I.get<ControladorCliente>().isUsuarioColaborador) {
      mControladorTreinoAluno.carregarProgramaDeTreinoColaborador(
        true,
        sucesso: () {
          statusCarregandoFichaDoDia = ServiceStatus.Waiting;
          if (mControladorTreinoAluno.mProgramaCarregado == null) {
            if (mControladorTreinoAluno.statusTreinoIASituacao == SituacaoValidacao.LIBERADO) {
              statusCarregandoFichaDoDia = ServiceStatus.Done;
            } else {
              statusCarregandoFichaDoDia = ServiceStatus.Empty;
            }
            sucesso?.call();
          } else {
            try {
              var resultado = mControladorTreinoAluno.mProgramaCarregado!.clone();
              for (var i = 0; i < dataSelecionada!.difference(DateTime.now()).inDays; i++) {
                var ficha = resultado.programa!.getFichaDoDia;
                var index = resultado.programa!.fichas!.indexWhere((element) => element!.cod == ficha!.cod);
                resultado.programa!.fichas![index]!.concluida = true;

                resultado.programa!.fichas![index]!.ultimaExecucaoLong = DateTime.now().add(Duration(days: i)).millisecondsSinceEpoch;
              }
              sucesso?.call();
              statusCarregandoFichaDoDia = (resultado.programa!.fichas?.isEmpty ?? false) ? ServiceStatus.Empty : ServiceStatus.Done;
            } catch (error) {
              statusCarregandoFichaDoDia = ServiceStatus.Error;
            }
          }
        },
        falha: (e) {
          if (mControladorTreinoAluno.statusTreinoIASituacao == SituacaoValidacao.LIBERADO ||
              mControladorTreinoAluno.statusTreinoIASituacao == SituacaoValidacao.aguardando ||
              mControladorTreinoAluno.statusTreinoIASituacao == SituacaoValidacao.emRevisao) {
            statusCarregandoFichaDoDia = ServiceStatus.Done;
          } else {
            statusCarregandoFichaDoDia = ServiceStatus.Empty;
          }
          falha?.call(e);
        },
      );
    } else {
      DebugUtils.debugLog('carregarProgramaDeTreino Planner');
      mControladorTreinoAluno.carregarProgramaDeTreino(
        true,
        sucesso: () {
          statusCarregandoFichaDoDia = ServiceStatus.Waiting;
          if (mControladorTreinoAluno.mProgramaCarregado == null) {
            if (mControladorTreinoAluno.statusTreinoIASituacao == SituacaoValidacao.LIBERADO) {
              statusCarregandoFichaDoDia = ServiceStatus.Done;
            } else {
              statusCarregandoFichaDoDia = ServiceStatus.Empty;
            }
            sucesso?.call();
          } else {
            try {
              var resultado = mControladorTreinoAluno.mProgramaCarregado!.clone();
              for (var i = 0; i < dataSelecionada!.difference(DateTime.now()).inDays; i++) {
                var ficha = resultado.programa!.getFichaDoDia;
                var index = resultado.programa!.fichas!.indexWhere((element) => element!.cod == ficha!.cod);
                resultado.programa!.fichas![index]!.concluida = true;

                resultado.programa!.fichas![index]!.ultimaExecucaoLong = DateTime.now().add(Duration(days: i)).millisecondsSinceEpoch;
              }
              sucesso?.call();
              statusCarregandoFichaDoDia = (resultado.programa!.fichas?.isEmpty ?? false) ? ServiceStatus.Empty : ServiceStatus.Done;
            } catch (error) {
              statusCarregandoFichaDoDia = ServiceStatus.Error;
            }
          }
        },
        falha: (e) {
          if (mControladorTreinoAluno.statusTreinoIASituacao == SituacaoValidacao.LIBERADO ||
              mControladorTreinoAluno.statusTreinoIASituacao == SituacaoValidacao.aguardando ||
              mControladorTreinoAluno.statusTreinoIASituacao == SituacaoValidacao.emRevisao) {
            statusCarregandoFichaDoDia = ServiceStatus.Done;
          } else {
            statusCarregandoFichaDoDia = ServiceStatus.Empty;
          }
          falha?.call(e);
        },
      );
    }
  }

  void obterResponsavel(RefeicaoPlanner refeicao, {Function(ResponsavelRefeicao responsavel)? sucesso, Function()? falha, Function()? sem}) {
    if (refeicao.responsavelRef == null || refeicao.responsavelRef == 'null') {
      refeicao.responsavelRef = 'mwNf9ztLYm8PUmdgSptu';
    }
    if (mResponsaveis.any((element) => element.refKey == refeicao.responsavelRef)) {
      sucesso!(mResponsaveis.firstWhere((element) => element.refKey == refeicao.responsavelRef));
    } else {
      FirebaseFirestore.instance.doc('ResponsavelRefeicoes/${refeicao.responsavelRef}').get().then((value) {
        mResponsaveis.add(ResponsavelRefeicao.fromJson(value.data()!));
        sucesso?.call(mResponsaveis.last);
      }).catchError((onError) {
        falha?.call();
      });
    }
  }

  IconData iconeDeAcordoComTipoDeRefeicao(String tipo) {
    switch (tipo) {
      case 'CAFE_MANHA':
        return FontAwesomeIcons.mugSaucer;
      case 'LANCHE_MANHA':
        return FontAwesomeIcons.egg;
      case 'ALMOCO':
        return FontAwesomeIcons.utensils;
      case 'LANCHE_TARDE':
        return FontAwesomeIcons.breadSlice;
      case 'JANTAR':
        return FontAwesomeIcons.utensils;
      case 'CEIA':
        return FontAwesomeIcons.carrot;
      default:
        return FontAwesomeIcons.utensils;
    }
  }

  String get restricoesAmigaveis {
    var retorno = (mPlanoUsuario?.restricoes?.isEmpty ?? true) ? 'Nenhuma' : '';
    mPlanoRefeicaoAluno?.planoMontado?.restricoes?.forEach((element) {
      retorno += element == 'FRUTOS_DO_MAR'
          ? 'Futros do mar'
          : element == 'FRUTOSE'
              ? 'Frutose'
              : element == 'GLUTEN'
                  ? 'Glutén'
                  : 'Lactose';
      retorno += ', ';
    });
    if (!retorno.contains('Nenhuma') && retorno.isNotEmpty) {
      retorno = retorno.substring(0, retorno.length - 2);
    }
    return retorno;
  }

  String get quantidadeRefeicoesAmigavel {
    switch (mPlanoUsuario!.refeicoesAGerar!.length) {
      case 4:
        return '3 Refeições e 1 lanche';
      case 5:
        return '3 Refeições e 2 lanche';
      case 6:
        return '3 Refeições e 3 lanche';
      default:
        return '';
    }
  }

  String tipoObjetivoAmigavel(enumMap.Objetivo? objetivo) {
    switch (objetivo) {
      case enumMap.Objetivo.GANHO_MASSA:
        return localizedString('gain_muscle_mass');
      case enumMap.Objetivo.EMAGRECIMENTO:
        return "${localizedString("fat_loss")} / ${localizedString("lose_weight")}";
      default:
        return '';
    }
  }

  String tipoRefeicaoAmigavel(String? tipo) {
    switch (tipo) {
      case 'CAFE_MANHA':
        return localizedString('breakfast');
      case 'LANCHE_MANHA':
        return localizedString('brunch');
      case 'ALMOCO':
        return localizedString('lunch');
      case 'LANCHE_TARDE':
        return localizedString('afternoon_snack');
      case 'JANTAR':
        return localizedString('dinner');
      case 'CEIA':
        return localizedString('supper_meal');
      default:
        return '';
    }
  }

  List<RefeicaoPlanner> refeicoesAlternativas(List<String>? tiposRefeicoes) {
    List<RefeicaoPlanner> alternativas = [];
    for (final r in mRefeicoes) {
      r.tipoRefeicao?.forEach((element) {
        if (tiposRefeicoes!.contains(element)) alternativas.add(r);
      });
    }
    return alternativas;
  }

  void pesquisarReceitasPorNome(String termo) {
    statusCarregandoReceitas = ServiceStatus.Waiting;
    estaPesquisando = true;
    if (termo.isNotEmpty) {
      mRefeicoesFiltradas = mRefeicoes.where((element) => removeDiacritics(element.nome!.toUpperCase()).contains(removeDiacritics(termo.toUpperCase()))).toList();
      statusCarregandoReceitas = mRefeicoesFiltradas.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    } else {
      estaPesquisando = false;
      statusCarregandoReceitas = ServiceStatus.Done;
    }
  }

  Map<String, List<RefeicaoPlanner>> get refeicoesAgrupadasPorTipo {
    Map<String, List<RefeicaoPlanner>> mapa = {'CAFE_MANHA': [], 'LANCHE_MANHA': [], 'ALMOCO': [], 'LANCHE_TARDE': [], 'JANTAR': [], 'CEIA': []};
    for (final refeicao in mRefeicoes) {
      refeicao.tipoRefeicao?.forEach((tipoRefeicao) {
        mapa[tipoRefeicao]!.add(refeicao);
      });
    }
    return mapa;
  }

  double obterNotaMediaRefeicao(RefeicaoPlanner? refeicaoPlanner) {
    var nota = 0.0;
    refeicaoPlanner?.notas?.forEach((element) {
      nota += element.nota!;
    });
    return nota / (refeicaoPlanner?.notas?.length ?? 1);
  }

  void avaliarUmaReceita(RefeicaoPlanner receita, num nota, {Function()? carregando, Function(RefeicaoPlanner nota)? sucesso, Function(String falha)? falha}) {
    carregando?.call();
    _service.avaliarRefeicao(receita.docKey!, nota, _mCliente.mUsuarioAuth!.uid!).then((value) {
      receita.notas ??= [];
      if (receita.notas!.any((element) => element.refUsuario == _mCliente.mUsuarioAuth!.uid)) {
        var index = receita.notas!.indexWhere((element) => element.refUsuario == _mCliente.mUsuarioAuth!.uid);
        receita.notas![index].nota = nota;
      } else {
        receita.notas!.add(Nota(nota: nota, refUsuario: _mCliente.mUsuarioAuth!.uid));
      }
      sucesso?.call(receita);
    }).catchError((onError) {
      falha?.call('Não foi possível registrar sua avalição no momento, tente novamente em alguns instantes');
    });
  }

  void consultarReceitas({Function()? carregando, Function()? sucesso, Function(String? falha)? falha}) {
    statusCarregandoReceitas = ServiceStatus.Waiting;
    carregando?.call();
    _service.consultarReceitas().then((value) {
      mRefeicoes.clear();
      mRefeicoes.addAll(value);
      sucesso?.call();
      statusCarregandoReceitas = mRefeicoes.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    }).catchError((onError) {
      statusCarregandoReceitas = ServiceStatus.Error;
      falha?.call(onError.message);
    });
  }

  void consultarDicas({Function()? carregando, Function()? sucesso, Function(String? falha)? falha}) {
    statusCarregandoDicas = ServiceStatus.Waiting;
    carregando?.call();
    _service.consultarDicas(10, _mCliente.mUsuarioLogado!.codUsuario!, 2).then((value) {
      mDicas.clear();
      mDicas.addAll(value);
      statusCarregandoDicas = ServiceStatus.Done;
      sucesso?.call();
    }).catchError((onError) {
      statusCarregandoDicas = ServiceStatus.Error;
      falha?.call(onError.message);
    });
  }

  void manterPlanoRefeicoesUsuario(PlanoMontado plano, {Function()? sucesso, Function()? carregando, Function(String? falha)? falha}) {
    carregando?.call();
    plano.refUsuarioApp = _mCliente.mUsuarioAuth!.uid;
    var json = plano.toJson();
    json.remove('dataCriacaoPlano');
    _service.manterPlanoRefeicao(json, _mCliente.mUsuarioAuth!.uid!).then((value) {
      mPlanoUsuario = plano.clone();
      mPlanoRefeicaoAluno?.planoMontado?.restricoes = plano.restricoes;
      mPlanoRefeicaoAluno?.planoMontado?.objetivo = plano.objetivo;
      mPlanoRefeicaoAluno?.planoMontado?.refeicoesAGerar = plano.refeicoesAGerar;
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  void limparTudo() {
    mUsuarioSemPlano = false;
    empresaComPoucasRefeicoes = false;
    mPlanoUsuario = PlanoMontado();
    mPlanoRefeicaoAluno = null;
    statusCarregandoDicas = ServiceStatus.Waiting;
    statusCarregandoHistoricoCompras = ServiceStatus.Waiting;
    statusCarregandoReceitas = ServiceStatus.Waiting;
    statusCarregandoFichaDoDia = ServiceStatus.Waiting;
    statusCarregandoPlanoRefeicao = ServiceStatus.Waiting;
    statusCarregandoDia = ServiceStatus.Done;

    estaPesquisando = false;

    tituloErro = '';
    subtituloErro = '';

    dataSelecionada = DateTime.now();

    mDicas = [];
    mRefeicoes = [];
    mRefeicoesFiltradas = [];
    mResponsaveis = [];
  }
}
