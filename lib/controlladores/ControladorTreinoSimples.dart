// ignore_for_file: avoid_print

import 'dart:convert';
import 'package:app_treino/ServiceProvider/TreinoSimples.service.dart';
import 'package:app_treino/ServiceProvider/authServices/ClienteAppService.dart';
import 'package:app_treino/ServiceProvider/authServices/ServiceAuth.dart';
import 'package:app_treino/screens/homefit/urlValidator.dart';
import 'package:ds_pacto/botao_primario.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/SituacaoCliente.dart';
import 'package:app_treino/model/homefit/ImagemPreSetList.dart';
import 'package:app_treino/model/homefit/models.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:app_treino/screens/homefit/criarTreinoDetalhesViewController.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:app_treino/appWidgets/componentWidgets/TextWidgets.dart';
import 'package:youtube_parser/youtube_parser.dart';
import 'package:http/http.dart' as http;
import 'dart:convert' show base64;
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/VideoYoutbeInfo.dart';
import 'package:app_treino/controlladores/YouTubeAPIResult.dart';
import 'package:shared_preferences/shared_preferences.dart';
part 'ControladorTreinoSimples.g.dart';

class ControladorTreinoSimples = _ControladorTreinoSimplesBase with _$ControladorTreinoSimples;

abstract class _ControladorTreinoSimplesBase with Store {
  final TreinoSimplesService _mService = GetIt.I.get<TreinoSimplesService>();
  var controllApp = GetIt.I.get<ControladorApp>();
  var controlCliente = GetIt.I.get<ControladorCliente>();
  @observable
  UsuarioApp usuarioApp = UsuarioApp(nome: '', urlFoto: '');
  @observable
  Treino? treinoCriado;
  @observable
  bool carregandoInfo = true;

  @observable
  String? errorINVOKE;

  @observable
  bool? eProfessor;
  @observable
  String logoAcademiaBase64 = '';

  @observable
  ObservableList<Atividades> mListaUrlsVideos = ObservableList<Atividades>();

  @observable
  bool estaFiltrando = false;

  @observable
  Filtro filtro = Filtro();

  @observable
  ObservableList<VideoAjuda> listaVideoAjuda = ObservableList<VideoAjuda>();

  @observable
  ObservableList<String?> listaItensSelecionadosString = ObservableList<String?>();

  @observable
  ObservableList<EstruturaDetalhes> filtrosAluno = ObservableList<EstruturaDetalhes>();

  @observable
  ObservableList<EstruturaDetalhes> listaDetalhes = ObservableList<EstruturaDetalhes>();

  @observable
  ObservableList<Treino> listaOriginal = ObservableList<Treino>();

  @observable
  Map<String, bool> estadoFiltros = <String, bool>{}.asObservable();

  @observable
  ObservableList<String> filtrosSelecionados = ObservableList<String>();

  @observable
  ServiceStatus statusConsulta = ServiceStatus.Waiting;

  @observable
  bool? usuarioPodeAcessar;
  String mensagemExibirCasoAcessoNegado = localizedString('it_was_not_possible_to_validate_your_situation_with_the_gym');

  SituacaoCliente? consultaSituacaoAluno;

  bool tagAoVivo(Treino treino) {
    var isAoVivo = false;
    isAoVivo = treino.tags?.any((mTreino) => mTreino.descricao!.contains('treino ao vivo')) ?? false;
    return isAoVivo;
  }

  usuarioPodeAcessarOHomeFit({Function()? sucesso, Function()? carregando, Function(String? falha)? falha}) {
    statusConsulta = ServiceStatus.Waiting;
    var moduloHabilitado = controllApp.getConfiguracaoApp(ModuloApp.MODULO_VALIDAR_VISITANTE_HABILITADO).habilitado;
    carregando?.call();
    if (controlCliente.isUsuarioColaborador) {
      statusConsulta = ServiceStatus.Done;
      usuarioPodeAcessar = true;
      sucesso?.call();
      return;
    }
    if (controlCliente.mUsuarioLogado!.treinoIndependente ?? false) {
      usuarioPodeAcessar = true;
      sucesso?.call();
      return;
    }
    if (controllApp.getConfiguracaoApp(ModuloApp.VISITANTE_ACESSO_LIBERADO).habilitado!) {
      usuarioPodeAcessar = true;
      statusConsulta = ServiceStatus.Done;
      sucesso?.call();
      return;
    }
    if (!moduloHabilitado! && !controllApp.getConfiguracaoApp(ModuloApp.VISITANTE_ACESSO_LIBERADO).habilitado!) {
      usuarioPodeAcessar = true;
      Future.delayed(const Duration(milliseconds: 4000)).then((value) {
        usuarioPodeAcessar = controlCliente.isVisitante || controlCliente.isDesistente || controlCliente.isVencido || controlCliente.isCancelado ? false : true;
        mensagemExibirCasoAcessoNegado = controlCliente.isVisitante || controlCliente.isDesistente || controlCliente.isVencido || controlCliente.isCancelado
            ? localizedString('voce_nao_possui_aut_acessar_treinos_entre_contato_adm')
            : '';
        statusConsulta = ServiceStatus.Done;
        sucesso?.call();
      });
      return;
    }
    // Primeiro conseguimos a URL do ZW do cliente
    late ProdutoHomeFit consultaProduto;
    SituacaoCliente? consultaSituacao;
    Future.wait([
      _mService.validarSeAcademiaVendeHomeFit(controllApp.chave!, controlCliente.mUsuarioLogado!.codEmpresa!, controlCliente.mUsuarioLogado!.codigoCliente),
      GetIt.I.get<ServiceAuth>().consultarSituacaoAluno(controlCliente.mUsuarioLogado!.matricula!)
    ]).then((value) {
      for (final element in value) {
        if (element is ProdutoHomeFit) {
          consultaProduto = element;
        }
        if (element is SituacaoCliente) {
          consultaSituacao = element;
        }
      }
      if (consultaProduto.retorno!.alunoTemNoContrato! && consultaProduto.retorno!.empresaVendeHomeFit!) {
        usuarioPodeAcessar = true;
        sucesso?.call();
        statusConsulta = ServiceStatus.Done;
        return;
      } else if (consultaProduto.retorno!.empresaVendeHomeFit! && !consultaProduto.retorno!.alunoTemNoContrato!) {
        usuarioPodeAcessar = false;
        mensagemExibirCasoAcessoNegado = localizedString('treino_em_casa.nao_possivel_validar');
        sucesso?.call();
        statusConsulta = ServiceStatus.Done;
        return;
      }

      if (consultaSituacao != null) {
        switch (consultaSituacao!.situacao) {
          case 'TR':
            if (consultaSituacao!.tipoAcesso != null) {
              usuarioPodeAcessar = true;
            } else {
              mensagemExibirCasoAcessoNegado = localizedString('treino_em_casa.contato_trancado');
              usuarioPodeAcessar = false;
            }
            break;
          case 'IN':
            if (consultaSituacao!.tipoAcesso != null) {
              usuarioPodeAcessar = true;
            } else {
              mensagemExibirCasoAcessoNegado = localizedString('treino_em_casa.nao_possivel_autorizar');
              usuarioPodeAcessar = false;
            }
            break;
          case 'VI':
            if (consultaSituacao!.tipoAcesso != null) {
              usuarioPodeAcessar = true;
            } else {
              mensagemExibirCasoAcessoNegado = localizedString('treino_em_casa.nao_possivel_autorizar');
              usuarioPodeAcessar = false;
            }
            break;
          case 'AT':
            switch (consultaSituacao!.situacaoContrato) {
              case 'CR':
                if (consultaSituacao!.tipoAcesso != null && !consultaProduto.retorno!.empresaVendeHomeFit!) {
                  usuarioPodeAcessar = true;
                } else {
                  mensagemExibirCasoAcessoNegado = localizedString('treino_em_casa.contrato_ferias');
                  usuarioPodeAcessar = false;
                }
                break;
              case 'AE':
                if (consultaSituacao!.tipoAcesso != null && !consultaProduto.retorno!.empresaVendeHomeFit!) {
                  usuarioPodeAcessar = true;
                } else {
                  mensagemExibirCasoAcessoNegado = localizedString('treino_em_casa.contrato_atestado');
                  usuarioPodeAcessar = false;
                }
                break;
              default:
                usuarioPodeAcessar = true;
            }
            break;

          default:
            {
              mensagemExibirCasoAcessoNegado = localizedString('treino_em_casa.nao_possivel_validar');
              usuarioPodeAcessar = false;
            }
        }
      }

      sucesso?.call();
      statusConsulta = ServiceStatus.Done;
    }).catchError((onError) {
      if (((onError?.response?.statusCode == 500))) {
        usuarioPodeAcessar = true;
        statusConsulta = ServiceStatus.Done;
      } else {
        falha?.call(onError.message);
        statusConsulta = ServiceStatus.Error;
      }
    });
  }

  @action
  void salvarEstadoFiltros() {
    filtrosSelecionados.clear();
    for (final filtro in filtrosAluno) {
      for (final item in filtro.itens!) {
        if (item.selecionado!) {
          filtrosSelecionados.add(item.nome!);
        }
      }
    }
  }

  @action
  void restaurarEstadoFiltros() {
    if (estadoFiltros.isEmpty) return;

    for (final filtro in filtrosAluno) {
      for (final item in filtro.itens!) {
        item.selecionado = estadoFiltros[item.nome] ?? false;
      }
    }
  }

  void resetarFiltrosELista() {
    estaFiltrando = false;
    filtrosSelecionados.clear();
    for (final filtro in filtrosAluno) {
      for (final item in filtro.itens!) {
        item.selecionado = false;
      }
    }
    if (listaOriginal.isNotEmpty) {
      mListaTreinosSimples = ObservableList.of(listaOriginal);
    }
    listaOriginal.clear();
    carregarTreinosSimples();
  }

  void _adicionarFiltros(String separador, List<String> nomes, List<EstruturaDetalhes> lista) {
    lista.add(
      EstruturaDetalhes(
        separador: localizedString(separador),
        itens: nomes.map((nome) => Itens(nome: localizedString(nome), selecionado: false)).toList(),
      ),
    );
  }

  void preencherFiltros() {
    listaDetalhes.clear();
    filtrosAluno.clear();

    _adicionarFiltros('objetivos', ['lose_weight', 'manter_peso', 'gain_muscle_mass'], listaDetalhes);
    _adicionarFiltros('nivel_de_dificuldade', ['beginner', 'intermediary', 'advanced'], listaDetalhes);
    _adicionarFiltros('grupos_musculares', ['abdomen', 'bracos', 'costas'], listaDetalhes);
    _adicionarFiltros(
      'equipments',
      ['no_equipments', 'anilhas', 'halters', 'fita_supensa', 'barra_fixa', 'corda', 'superband', 'miniband', 'colchonete', 'tornozeleira', 'bola_suica', 'roda'],
      listaDetalhes,
    );

    filtrosAluno.addAll(listaDetalhes);
  }

  apenasItensSelecionados() {
    listaItensSelecionadosString.clear();
    var listaTemp = ObservableList<String?>();
    for (final filtro in listaDetalhes) {
      for (final item in filtro.itens!) {
        if (item.selecionado!) {
          listaTemp.add(item.nome);
        }
      }
    }
    listaItensSelecionadosString.addAll(listaTemp);
  }

  void adicionarItensNaListaFiltros() {
    for (final filtro in listaDetalhes) {
      if (filtro.separador == localizedString('objetivos')) {
        if (dadosTreino?.objetivo?.isNotEmpty ?? false) {
          for (final objetivo in dadosTreino!.objetivo!) {
            for (final item in filtro.itens!) {
              if (objetivo.descricao?.toLowerCase() == item.nome?.toLowerCase()) {
                item.selecionado = true;
                if (!detalhesTreinoSelecionados.contains(item.nome)) {
                  detalhesTreinoSelecionados.add(item.nome!);
                }
              }
            }
          }
        }
      }
      if (filtro.separador == localizedString('nivel_de_dificuldade')) {
        if (dadosTreino?.dificuldade?.isNotEmpty ?? false) {
          for (final dificuldade in dadosTreino!.dificuldade!) {
            for (final item in filtro.itens!) {
              if (dificuldade.descricao?.toLowerCase() == item.nome?.toLowerCase()) {
                item.selecionado = true;
                if (!detalhesTreinoSelecionados.contains(item.nome)) {
                  detalhesTreinoSelecionados.add(item.nome!);
                }
              }
            }
          }
        }
      }
      if (filtro.separador == localizedString('grupos_musculares')) {
        if (dadosTreino?.grupoMuscular?.isNotEmpty ?? false) {
          for (final muscular in dadosTreino!.grupoMuscular!) {
            for (final item in filtro.itens!) {
              if (muscular.descricao?.toLowerCase() == item.nome?.toLowerCase()) {
                item.selecionado = true;
                if (!detalhesTreinoSelecionados.contains(item.nome)) {
                  detalhesTreinoSelecionados.add(item.nome!);
                }
              }
            }
          }
        }
      }
      if (filtro.separador == localizedString('equipments')) {
        if (dadosTreino?.equipamentos?.isNotEmpty ?? false) {
          for (final equipamento in dadosTreino!.equipamentos!) {
            for (final item in filtro.itens!) {
              if (equipamento.descricao?.toLowerCase() == item.nome?.toLowerCase()) {
                item.selecionado = true;
                if (!detalhesTreinoSelecionados.contains(item.nome)) {
                  detalhesTreinoSelecionados.add(item.nome!);
                }
              }
            }
          }
        }
      }
    }
  }

  bool aplicarFiltro(List<Objetivo>? listaObjetivos, EstruturaDetalhes filtro) {
    if (listaObjetivos == null || listaObjetivos.isEmpty) return false;
    for (final objetivo in listaObjetivos) {
      for (final item in filtro.itens!) {
        if (item.selecionado! && objetivo.descricao?.toLowerCase() == item.nome?.toLowerCase()) {
          return true;
        }
      }
    }
    return false;
  }

  bool _correspondeFiltro(List<dynamic>? atributosTreino, List<Itens> filtrosSelecionados) {
    if (filtrosSelecionados.isEmpty) return true;
    if (atributosTreino == null || atributosTreino.isEmpty) return false;

    // Verifica se QUALQUER filtro selecionado corresponde a QUALQUER atributo do treino
    for (final filtro in filtrosSelecionados) {
      for (final atributo in atributosTreino) {
        if (atributo.descricao?.toLowerCase() == filtro.nome?.toLowerCase()) {
          return true;
        }
      }
    }
    return false;
  }

  @action
  void filtrarLista() {
    // Guarda a lista original se for o primeiro filtro
    if (listaOriginal.isEmpty) {
      listaOriginal.addAll(mListaTreinosSimples);
    }

    // Salva o estado atual dos filtros
    for (final filtro in filtrosAluno) {
      for (final item in filtro.itens!) {
        estadoFiltros[item.nome!] = item.selecionado!;
      }
    }

    final filtrosSelecionados = filtrosAluno.map((f) => f.itens!.where((i) => i.selecionado!).toList()).toList();

    final temFiltrosSelecionados = filtrosSelecionados.any((lista) => lista.isNotEmpty);

    if (!temFiltrosSelecionados) {
      mListaTreinosSimples = ObservableList.of(listaOriginal);
      estaFiltrando = false;
      return;
    }

    final listaTemporaria = listaOriginal.where((treino) {
      return _correspondeFiltro(treino.objetivo, filtrosSelecionados[0]) &&
          _correspondeFiltro(treino.dificuldade, filtrosSelecionados[1]) &&
          _correspondeFiltro(treino.grupoMuscular, filtrosSelecionados[2]) &&
          _correspondeFiltro(treino.equipamentos, filtrosSelecionados[3]);
    }).toList();

    estaFiltrando = true;
    mListaTreinosSimples = ObservableList.of(listaTemporaria);
  }

  void modalFiltros(BuildContext context) {
    if (!estaFiltrando) {
      limparFiltros();
    } else {
      restaurarEstadoFiltros();
    }
  }

  @action
  void limparFiltros() {
    estaFiltrando = false;
    estadoFiltros.clear();
    for (final filtro in filtrosAluno) {
      for (final item in filtro.itens!) {
        item.selecionado = false;
      }
    }
    if (listaOriginal.isNotEmpty) {
      mListaTreinosSimples = ObservableList.of(listaOriginal);
      listaOriginal.clear();
    }
  }

  void criarNovoTreino() {
    dadosTreino = Treino();
    mListaUrlsVideos.clear();
    listaItensSelecionadosString.clear();
    for (final item in listaDetalhes) {
      for (final selecionado in item.itens!) {
        selecionado.selecionado = false;
      }
    }
  }

  void removerAtividade(Atividades atividades) {
    mListaUrlsVideos.remove(atividades);
  }

  Future abrirCamera(BuildContext context) async {
    final result = await const MethodChannel('samples.flutter.dev/battery').invokeMethod('pickImage');
    String? base64Upload;
    if (result is Uint8List) {
      base64Upload = base64.encode(result);
    } else {
      base64Upload = result;
    }

    UtilitarioApp().showDialogCarregando(context);
    // ignore: body_might_complete_normally_catch_error
    return _mService.uploadImagem(base64Upload!).catchError((onError) {
      Navigator.pop(context);
      DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Ops', subtitulo: onError.message, tituloBotao: 'got_it');
    });
  }

  @action
  Future<void> removerTreino({required Treino treino, Function? callback, required BuildContext context}) async {
    UtilitarioApp().showDialogCarregando(context);
    try {
      await _mService.excluirTreinoSimples(treino.documentKey!);
      
      // Remove o treino da lista
      mListaTreinosSimples.removeWhere((element) => element.documentKey == treino.documentKey);
      
      // Se for o último treino, limpa também a lista original e estados de filtro
      if (mListaTreinosSimples.isEmpty) {
        listaOriginal.clear();
        estaFiltrando = false;
        estadoFiltros.clear();
        statusCarregarTreinos = STATUSREQUEST.SEM_DADOS;
      } else {
        statusCarregarTreinos = STATUSREQUEST.SUCESSO;
      }
      
      Navigator.of(context).pop();
      callback?.call();
      
    } catch (error) {
      Navigator.of(context).pop();
      DSalerta().exibirAlertaSimplificado(
        context: context, 
        titulo: 'Ops', 
        subtitulo: localizedString('nao_foi_possivel_excluir_treino'), 
        tituloBotao: 'got_it'
      );
    }
  }


  Future<void> carregarDadosDoUsuario(BuildContext context) async {
    _mService.getInfoProfessor(GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid!).then((value) {
      usuarioApp = value;
    }).catchError((onError) {});
  }

  void reordernarAtividades(int origem, int destino) {
    var tempA = mListaUrlsVideos[origem];
    var tempB = mListaUrlsVideos[destino];
    mListaUrlsVideos[origem] = tempB;
    mListaUrlsVideos[destino] = tempA;
    for (var i = 0; i < mListaUrlsVideos.length; i++) {
      mListaUrlsVideos[i].orderm = i;
    }
  }

  void adicionarVideo({String? urlVideo, required BuildContext context, String? descricao}) {
    String videoID;
    UtilitarioApp().showDialogCarregando(context);
    if (urlVideo == null) {
      Navigator.of(context).pop();
      showDialog(
          context: context,
          builder: (BuildContext context) {
            // retorna um objeto do tipo Dialog
            return AlertDialog(
              elevation: 0,
              backgroundColor: Theme.of(context).canvasColor,
              content: Wrap(
                runAlignment: WrapAlignment.center,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: <Widget>[
                  Container(
                    alignment: Alignment.center,
                    child: Column(
                      children: <Widget>[
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: TextHeadLine1('Ops!', textAlign: TextAlign.center),
                        ),
                        TextBody2('para_adicionar_video_add_url', textAlign: TextAlign.center),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 25),
                    child: BotaoPrimario(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        value: localizedString('ok_got_it')),
                  )
                ],
              ),
            );
          });
    } else {
      if (urlVideo.contains('youtube')) {
        videoID = getIdFromUrl(urlVideo)!;
        obterInfoDoVideo(videoID, (atividadeMontada) {
          mListaUrlsVideos.add(atividadeMontada);
          atividadeMontada.descricao = descricao;
          Navigator.pop(context);
          Navigator.pop(context);
        });
      } else {
        mListaUrlsVideos.add(Atividades(descricao: descricao, idYouTube: urlVideo, nome: 'Conteúdo externo'));
        Navigator.pop(context);
        Navigator.pop(context);
      }
    }
  }

  Future<void> obterInfoDoVideo(String videoID, Function(Atividades atividade) callback) async {
    try {
      final responseInfo = await http.get(Uri.parse('https://www.youtube.com/oembed?url=http://www.youtube.com/watch?v=$videoID&format=json'));
      final responseAPI = await http.get(Uri.parse('https://www.googleapis.com/youtube/v3/videos?id=$videoID&part=contentDetails&key=AIzaSyAfK46oR9lKhpRTRFwoMHMKS6hMQ_R08hk'));

      var minfoAtividade = Atividades();
      minfoAtividade.idYouTube = videoID;

      if (responseInfo.statusCode == 200) {
        final ytJsonInfo = videoYoutbeInfoFromJson(responseInfo.body);
        minfoAtividade.nome = ytJsonInfo.title;
      } else {
        print('Erro ao obter informações do vídeo: ${responseInfo.statusCode}');
      }

      if (responseAPI.statusCode == 200) {
        final apiInfo = youTubeApiResultFromJson(responseAPI.body);
        minfoAtividade.duracao = apiInfo.items!.first.contentDetails!.duration!.replaceAll('PT', '').toLowerCase();
      } else {
        print('Erro ao obter API do vídeo: ${responseAPI.statusCode}');
      }

      // Garante que temos pelo menos um nome
      if (minfoAtividade.nome == null || minfoAtividade.nome!.isEmpty) {
        minfoAtividade.nome = 'Vídeo $videoID';
      }

      callback(minfoAtividade);
    } catch (error) {
      print('Erro ao obter info do vídeo: $error');
      rethrow;
    }
  }

  void atualizarDetalhes() {
    dadosTreino!.dificuldade = [];
    dadosTreino!.objetivo = [];
    dadosTreino!.grupoMuscular = [];
    dadosTreino!.equipamentos = [];
    for (final item in listaDetalhes) {
      if (item.separador == localizedString('objetivos')) {
        for (final selecionado in item.itens!) {
          if (selecionado.selecionado!) {
            dadosTreino!.objetivo!.add(Objetivo(descricao: selecionado.nome));
          }
        }
      } else if (item.separador == localizedString('nivel_de_dificuldade')) {
        for (final selecionado in item.itens!) {
          if (selecionado.selecionado!) {
            dadosTreino!.dificuldade!.add(Objetivo(descricao: selecionado.nome));
          }
        }
      } else if (item.separador == localizedString('grupos_musculares')) {
        for (final selecionado in item.itens!) {
          if (selecionado.selecionado!) {
            dadosTreino!.grupoMuscular!.add(Objetivo(descricao: selecionado.nome));
          }
        }
      } else if (item.separador == localizedString('equipments')) {
        for (final selecionado in item.itens!) {
          if (selecionado.selecionado!) {
            dadosTreino!.equipamentos!.add(Objetivo(descricao: selecionado.nome));
          }
        }
      } else {}
    }
  }

  Future<void> getConsultaVideosAjuda() async {
    listaVideoAjuda.clear();
    _mService.listarVideosAjuda().then((value) {
      listaVideoAjuda.addAll(value);
    }).catchError((onError) {});
  }

  @computed
  String? get urlVideo => listaVideoAjuda.first.url;

  @computed
  int get index => listaDetalhes[4].itens!.length;

  @observable
  ObservableList<Filtro> listaFiltros = ObservableList<Filtro>();

  Future<void> getConsultaFiltros() async {
    if (filtrosAluno.isEmpty || filtrosAluno[0].itens == null) {
      preencherFiltros();
    }

    listaFiltros.clear();
    try {
      final value = await _mService.listarFiltros(GetIt.I.get<ControladorApp>().mClienteAppSelecionado!.documentkey!);

      if (listaDetalhes.isNotEmpty && filtrosAluno.isNotEmpty) {
        listaDetalhes[0].itens?.clear();
        filtrosAluno[0].itens?.clear();

        for (final item in value) {
          listaDetalhes[0].itens?.add(Itens(nome: item.nome, selecionado: false, refFiltro: item.refFiltro));
          filtrosAluno[0].itens?.add(Itens(nome: item.nome, selecionado: false, refFiltro: item.refFiltro));
        }
      }
    } catch (error) {}
  }

  Future<void> salvarFiltro(BuildContext context, Filtro dadosFiltro) async {
    _mService.salvarFiltro(GetIt.I.get<ControladorApp>().mClienteAppSelecionado!.documentkey!, dadosFiltro.toJson()).then((value) {
      filtro = dadosFiltro;
      var list = listaDetalhes[0].itens!;
      list.add(Itens(nome: dadosFiltro.nome, selecionado: false));
      listaDetalhes[0].itens!.addAll(list);
      var list2 = listaDetalhes[0].itens!;
      list2.add(Itens(nome: dadosFiltro.nome, selecionado: false));
      filtrosAluno[0].itens!.addAll(list);
    }).catchError((onError) {});
  }

  Future<void> excluirFiltro(BuildContext context, Filtro dadosFiltro) async {
    _mService.excluirFiltro(GetIt.I.get<ControladorApp>().mClienteAppSelecionado!.documentkey!, dadosFiltro.toJson()).then((value) {
      List<Itens> listaTemp = listaDetalhes[0].itens!;
      for (final item in listaTemp) {
        if (item.refFiltro == dadosFiltro.refFiltro) {
          listaTemp.remove(item);
          break;
        }
      }
      listaDetalhes[0].itens!.addAll(listaTemp);
      filtrosAluno[0].itens!.addAll(listaTemp);
    });
  }

  Future<Treino?> salvarTreino(BuildContext context) async {
    UtilitarioApp().showDialogCarregando(context);

    try {
      if (GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid == null) {
        await SharedPreferences.getInstance().then((value) => GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid = value.getString('userId'));
      }

      atualizarDetalhes();

      // Preparação dos dados do treino
      dadosTreino!.nomeDoTreino = dadosTreino!.nomeDoTreino;
      dadosTreino!.tipoColaboradorZW = GetIt.I.get<ControladorCliente>().mUsuarioLogado!.professor;

      if (dadosTreino!.tipoColaboradorZW?.isEmpty ?? false) {
        dadosTreino!.tipoColaboradorZW = null;
      }

      dadosTreino!.descricao = dadosTreino!.descricao ?? '';

      // Tratamento da imagem
      if (ImagensPreTreinoSet().listaImagensShow[imagemTreino!] == null && !imagemTreino!.contains('http')) {
        imagemTreino = await GetIt.I.get<ClienteAppService>().uploadImagem(imagemTreino!);
      }

      dadosTreino!.media = [Media(url: imagemTreino!.contains('https://') ? imagemTreino : ImagensPreTreinoSet().listaImagensShow[imagemTreino!])];

      dadosTreino!.clienteApp = GetIt.I.get<ControladorApp>().mClienteAppSelecionado!.documentkey;
      dadosTreino!.refUsuarioApp = GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid;

      // Organizar atividades
      dadosTreino!.atividades = List<Atividades>.from(mListaUrlsVideos);
      for (var i = 0; i < dadosTreino!.atividades!.length; i++) {
        dadosTreino!.atividades![i].orderm = i;
      }

      // Definir data de criação
      dadosTreino!.dataCricao = DateTime.now().millisecondsSinceEpoch;

      // Verificar se é uma edição
      final isEdicao = dadosTreino!.documentKey != null;

      // Salvar treino
      final treinoCriado = await _mService.salvarTreinoSimples(dadosTreino!.toJson());

      // Atualizar lista local
      if (isEdicao) {
        final index = mListaTreinosSimples.indexWhere((t) => t.documentKey == treinoCriado.documentKey);
        if (index != -1) {
          // Substitui o treino existente
          mListaTreinosSimples[index] = treinoCriado;
        } else {
          // Se não encontrar, adiciona no início
          mListaTreinosSimples.insert(0, treinoCriado);
        }
      } else {
        // Para novos treinos, adiciona no início
        mListaTreinosSimples.insert(0, treinoCriado);
      }

      // Reordenar lista por data de criação
      mListaTreinosSimples.sort((a, b) => (b.dataCricao ?? 0).compareTo(a.dataCricao ?? 0));

      Navigator.of(context).pop();
      return treinoCriado;
    } catch (error) {
      Navigator.of(context).pop();
      rethrow;
    }
  }

  @observable
  ObservableList<ExecucaoTreino> mQuemExecutou = ObservableList<ExecucaoTreino>();
  @observable
  bool isLoadingExecucoes = false;
  String? _ultimoTreinoKey;

  @action
  Future<void> getQuemRealizouOTreino(Treino treino, Function() onComplete) async {
    // Se já estiver carregando, não faz nada
    if (isLoadingExecucoes) return;

    isLoadingExecucoes = true;

    try {
      // Se for um treino diferente, limpa a lista
      if (treino.documentKey != _ultimoTreinoKey) {
        mQuemExecutou.clear();
      }

      final execucoes = await _mService.consultarQuemFezOTreino(treino.documentKey!);

      // Atualiza a lista
      mQuemExecutou.clear();
      mQuemExecutou.addAll(execucoes);

      _ultimoTreinoKey = treino.documentKey;
      onComplete();
    } catch (error) {
      print('Erro ao carregar execuções: $error');
    } finally {
      isLoadingExecucoes = false;
    }
  }

  @observable
  ObservableList<RankingInfo> mRankingAcademia = ObservableList<RankingInfo>();

  @observable
  STATUSREQUEST statusCarregarRanking = STATUSREQUEST.CARREGANDO;

  getRankingHomeFit(BuildContext context) async {
    mRankingAcademia.clear();
    statusCarregarRanking = STATUSREQUEST.CARREGANDO;
    _mService.consultarRankingTreinoSimples(GetIt.I.get<ControladorApp>().mClienteAppSelecionado!.documentkey!).then((value) {
      mRankingAcademia.addAll(value);
    }).catchError((onError) {});
    statusCarregarRanking = STATUSREQUEST.SUCESSO;
  }

  @observable
  ObservableList<Treino> mListaTreinosSimples = ObservableList<Treino>();

  @observable
  STATUSREQUEST statusCarregarTreinos = STATUSREQUEST.CARREGANDO;

  Future<void> carregarTreinosSimples({Function()? carregando, Function()? sucesso, Function(String?)? falha}) async {
    statusCarregarTreinos = STATUSREQUEST.CARREGANDO;
    carregando?.call();

    try {
      final treinos = await _mService.consultarTreinosSimples(GetIt.I.get<ControladorApp>().mClienteAppSelecionado!.documentkey!, GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid!);

      // Ordena os treinos pela data de criação
      treinos.sort((a, b) => (b.dataCricao ?? 0).compareTo(a.dataCricao ?? 0));

      runInAction(() {
        mListaTreinosSimples.clear();
        mListaTreinosSimples.addAll(treinos);
        statusCarregarTreinos = treinos.isEmpty ? STATUSREQUEST.SEM_DADOS : STATUSREQUEST.SUCESSO;
      });

      sucesso?.call();
    } catch (error) {
      statusCarregarTreinos = STATUSREQUEST.FALHA;
      falha?.call(error.toString());
    }
  }

  @action
  Future<void> atualizarTreino(Treino treino) async {
    final index = mListaTreinosSimples.indexWhere((t) => t.documentKey == treino.documentKey);
    if (index != -1) {
      mListaTreinosSimples[index] = treino;
    }
  }

  @action
  Future<void> marcarExeucao({required Treino treino, required Function(STATUSREQUEST status) callback}) async {
    try {
      callback(STATUSREQUEST.CARREGANDO);

      final date = DateTime.now().millisecondsSinceEpoch;
      await _mService.salvarExecucao(treino.documentKey!, GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid!, date);

      // Atualiza apenas o treino específico
      treino.ultimaExecucao = date;
      await atualizarTreino(treino);

      callback(STATUSREQUEST.SUCESSO);
    } catch (error) {
      callback(STATUSREQUEST.FALHA);
    }
  }

  //------------------------- treino on-Line ----------------------------

  @observable
  ObservableList<String> urlsVideos = ObservableList<String>();

  @observable
  ObservableList<VideoValidationStatus> urlsStatus = ObservableList<VideoValidationStatus>();

  @observable
  ObservableList<String> detalhesTreinoSelecionados = ObservableList<String>();

  @observable
  String? imagemTreino;

  @observable
  String? selectedImagePath;

  @observable
  Uint8List? imageUInt;

  @observable
  Treino? dadosTreino;

  @computed
  List<bool> get urlsValidos => urlsStatus.map((status) => status.isValid).toList();

  @computed
  List<bool> get urlsInvalidas => urlsStatus.map((status) => status.isInvalid).toList();

  @action
  void initializeUrlStatus() {
    while (urlsStatus.length < urlsVideos.length) {
      urlsStatus.add(VideoValidationStatus());
    }
  }

  @computed
  bool get temNomePreenchido {
    return dadosTreino?.nomeDoTreino?.isNotEmpty ?? false;
  }

  @computed
  bool get temDescricaoPreenchida {
    return dadosTreino?.descricao?.isNotEmpty ?? false;
  }

  @action
  void setNome(String value) {
    dadosTreino ??= Treino();
    dadosTreino!.nomeDoTreino = value;
  }

  @action
  void setDescricao(String value) {
    dadosTreino ??= Treino();
    dadosTreino!.descricao = value;
  }

  @computed
  bool get temUrlValida {
    if (urlsVideos.isEmpty) {
      return false;
    }
    final possuiUrlValida = urlsValidos.any((valido) => valido);
    return possuiUrlValida;
  }

  @computed
  bool get temDetalhesSelecionados => detalhesTreinoSelecionados.isNotEmpty;

  @computed
  bool get temImagemSelecionada => imagemTreino != null;

  @computed
  bool get podeAdicionarNovoVideo {
    return urlsVideos.isNotEmpty && urlsStatus.any((status) => status.isValid) && !urlsVideos.any((url) => url.isEmpty) && !urlsStatus.any((status) => status.isInvalid);
  }

  @action
  Future<void> setUrlVideo(int index, String value, BuildContext context) async {
    if (index >= urlsVideos.length) return;

    final trimmedValue = value.trim();
    urlsVideos[index] = trimmedValue;

    if (trimmedValue.isEmpty) {
      urlsStatus[index] = VideoValidationStatus();
      _cleanupVideo(index);
      return;
    }

    final videoId = UrlValidator.extractVideoId(trimmedValue);
    final isDuplicate = UrlValidator.isDuplicateInList(trimmedValue, urlsVideos, excludeIndex: index);

    if (videoId != null && UrlValidator.isValidYoutubeUrl(trimmedValue) && !isDuplicate) {
      urlsStatus[index] = VideoValidationStatus(isValid: true, lastValidatedUrl: trimmedValue);
      await _addVideoToList(trimmedValue, index, context);
    } else {
      urlsStatus[index] = VideoValidationStatus(isInvalid: true);
      _cleanupVideo(index);
    }
  }

  void _cleanupVideo(int index) {
    if (index < mListaUrlsVideos.length) {
      mListaUrlsVideos.removeAt(index);
    }
  }

  Future<void> _addVideoToList(String urlVideo, int index, BuildContext context) async {
    if (urlVideo.isEmpty) return;

    try {
      String? videoID = getIdFromUrl(urlVideo);

      if (videoID != null) {
        // Remove existing video at this index if it exists
        if (index < mListaUrlsVideos.length) {
          mListaUrlsVideos.removeAt(index);
        }

        // Add the new video
        await obterInfoDoVideo(videoID, (atividadeMontada) {
          if (index < mListaUrlsVideos.length) {
            mListaUrlsVideos[index] = atividadeMontada;
          } else {
            mListaUrlsVideos.add(atividadeMontada);
          }
        });
      } else {
        // Handle external content
        final atividade = Atividades(idYouTube: urlVideo, nome: localizedString('conteudo_externo'));

        if (index < mListaUrlsVideos.length) {
          mListaUrlsVideos[index] = atividade;
        } else {
          mListaUrlsVideos.add(atividade);
        }
      }
    } catch (e) {
      print('Erro ao adicionar vídeo: $e');
      rethrow;
    }
  }

  @action
  Future<void> adicionarVideoSemPoupap({String? urlVideo, required BuildContext context, String? descricao}) async {
    if (urlVideo == null || urlVideo.isEmpty) return;

    try {
      String? videoID = getIdFromUrl(urlVideo);

      if (videoID != null) {
        // Verifica se o vídeo já existe na lista
        int existingIndex = mListaUrlsVideos.indexWhere((a) => a.idYouTube == videoID);
        if (existingIndex != -1) {
          throw Exception('Vídeo duplicado');
        }

        // Obtém as informações do vídeo
        await obterInfoDoVideo(videoID, (atividadeMontada) {
          if (!mListaUrlsVideos.any((a) => a.idYouTube == videoID)) {
            atividadeMontada.descricao = descricao;
            mListaUrlsVideos.add(atividadeMontada);
          }
        });
      } else {
        // Para conteúdo externo
        if (!mListaUrlsVideos.any((a) => a.idYouTube == urlVideo)) {
          mListaUrlsVideos.add(Atividades(descricao: descricao, idYouTube: urlVideo, nome: localizedString('conteudo_externo')));
        }
      }
    } catch (e) {
      print('Erro ao adicionar vídeo: $e');
      rethrow;
    }
  }

  @action
  void removerUrl(int index) {
    if (index < urlsVideos.length) {
      urlsVideos.removeAt(index);
      urlsStatus.removeAt(index);
      if (index < mListaUrlsVideos.length) {
        mListaUrlsVideos.removeAt(index);
      }
    }

    if (urlsVideos.isEmpty) {
      urlsVideos.add('');
      urlsStatus.add(VideoValidationStatus());
    }
  }

  @action
  void limparUrl(int index) {
    if (index == 0 && urlsVideos.length == 1 && urlsStatus[0].isValid) {
      return;
    }

    if (index == 0) {
      final validIndex = urlsVideos.asMap().entries.where((e) => e.value.isNotEmpty && urlsStatus[e.key].isValid && e.key != 0).map((e) => e.key).firstOrNull;

      if (validIndex != null) {
        _swapUrls(0, validIndex);
      } else {
        return;
      }
    } else {
      urlsVideos.removeAt(index);
      urlsStatus.removeAt(index);
      if (index < mListaUrlsVideos.length) {
        mListaUrlsVideos.removeAt(index);
      }
    }
  }

  void _swapUrls(int index1, int index2) {
    final tempUrl = urlsVideos[index1];
    final tempStatus = urlsStatus[index1];
    final tempVideo = index1 < mListaUrlsVideos.length ? mListaUrlsVideos[index1] : null;

    urlsVideos[index1] = urlsVideos[index2];
    urlsStatus[index1] = urlsStatus[index2];

    urlsVideos[index2] = tempUrl;
    urlsStatus[index2] = tempStatus;

    if (tempVideo != null && index2 < mListaUrlsVideos.length) {
      final tempListVideo = mListaUrlsVideos[index2];
      mListaUrlsVideos[index2] = mListaUrlsVideos[index1];
      mListaUrlsVideos[index1] = tempListVideo;
    }
  }

  @action
  void adicionarNovoVideo() {
    if (podeAdicionarNovoVideo) {
      urlsVideos.add('');
      urlsStatus.add(VideoValidationStatus());
    }
  }

  @action
  void limparTudo() {
    dadosTreino = Treino();
    imagemTreino = null;
    selectedImagePath = null;
    imageUInt = null;
    urlsVideos = ObservableList<String>();
    urlsStatus = ObservableList<VideoValidationStatus>();
    detalhesTreinoSelecionados.clear();
    for (final detalhe in listaDetalhes) {
      for (final item in detalhe.itens ?? []) {
        item.selecionado = false;
      }
    }
    listaDetalhes.clear();
    filtrosAluno.clear();
    preencherFiltros();
  }
}
