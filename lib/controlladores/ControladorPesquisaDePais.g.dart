// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorPesquisaDePais.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorPesquiaDePais on _ControladorPesquiaDePaisBase, Store {
  late final _$mPaisesAtom =
      Atom(name: '_ControladorPesquiaDePaisBase.mPaises', context: context);

  @override
  ObservableList<CountryPhone> get mPaises {
    _$mPaisesAtom.reportRead();
    return super.mPaises;
  }

  @override
  set mPaises(ObservableList<CountryPhone> value) {
    _$mPaisesAtom.reportWrite(value, super.mPaises, () {
      super.mPaises = value;
    });
  }

  late final _$mPaisesPesquisaAtom = Atom(
      name: '_ControladorPesquiaDePaisBase.mPaisesPesquisa', context: context);

  @override
  ObservableList<CountryPhone> get mPaisesPesquisa {
    _$mPaisesPesquisaAtom.reportRead();
    return super.mPaisesPesquisa;
  }

  @override
  set mPaisesPesquisa(ObservableList<CountryPhone> value) {
    _$mPaisesPesquisaAtom.reportWrite(value, super.mPaisesPesquisa, () {
      super.mPaisesPesquisa = value;
    });
  }

  @override
  String toString() {
    return '''
mPaises: ${mPaises},
mPaisesPesquisa: ${mPaisesPesquisa}
    ''';
  }
}
