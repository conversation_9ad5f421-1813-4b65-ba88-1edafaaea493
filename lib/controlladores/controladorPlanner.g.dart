// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'controladorPlanner.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorPlanner on _ControladorPlannerBase, Store {
  late final _$statusCarregandoDicasAtom = Atom(
      name: '_ControladorPlannerBase.statusCarregandoDicas', context: context);

  @override
  ServiceStatus get statusCarregandoDicas {
    _$statusCarregandoDicasAtom.reportRead();
    return super.statusCarregandoDicas;
  }

  @override
  set statusCarregandoDicas(ServiceStatus value) {
    _$statusCarregandoDicasAtom.reportWrite(value, super.statusCarregandoDicas,
        () {
      super.statusCarregandoDicas = value;
    });
  }

  late final _$statusCarregandoHistoricoComprasAtom = Atom(
      name: '_ControladorPlannerBase.statusCarregandoHistoricoCompras',
      context: context);

  @override
  ServiceStatus get statusCarregandoHistoricoCompras {
    _$statusCarregandoHistoricoComprasAtom.reportRead();
    return super.statusCarregandoHistoricoCompras;
  }

  @override
  set statusCarregandoHistoricoCompras(ServiceStatus value) {
    _$statusCarregandoHistoricoComprasAtom
        .reportWrite(value, super.statusCarregandoHistoricoCompras, () {
      super.statusCarregandoHistoricoCompras = value;
    });
  }

  late final _$statusCarregandoReceitasAtom = Atom(
      name: '_ControladorPlannerBase.statusCarregandoReceitas',
      context: context);

  @override
  ServiceStatus get statusCarregandoReceitas {
    _$statusCarregandoReceitasAtom.reportRead();
    return super.statusCarregandoReceitas;
  }

  @override
  set statusCarregandoReceitas(ServiceStatus value) {
    _$statusCarregandoReceitasAtom
        .reportWrite(value, super.statusCarregandoReceitas, () {
      super.statusCarregandoReceitas = value;
    });
  }

  late final _$statusCarregandoFichaDoDiaAtom = Atom(
      name: '_ControladorPlannerBase.statusCarregandoFichaDoDia',
      context: context);

  @override
  ServiceStatus get statusCarregandoFichaDoDia {
    _$statusCarregandoFichaDoDiaAtom.reportRead();
    return super.statusCarregandoFichaDoDia;
  }

  @override
  set statusCarregandoFichaDoDia(ServiceStatus value) {
    _$statusCarregandoFichaDoDiaAtom
        .reportWrite(value, super.statusCarregandoFichaDoDia, () {
      super.statusCarregandoFichaDoDia = value;
    });
  }

  late final _$statusCarregandoPlanoRefeicaoAtom = Atom(
      name: '_ControladorPlannerBase.statusCarregandoPlanoRefeicao',
      context: context);

  @override
  ServiceStatus get statusCarregandoPlanoRefeicao {
    _$statusCarregandoPlanoRefeicaoAtom.reportRead();
    return super.statusCarregandoPlanoRefeicao;
  }

  @override
  set statusCarregandoPlanoRefeicao(ServiceStatus value) {
    _$statusCarregandoPlanoRefeicaoAtom
        .reportWrite(value, super.statusCarregandoPlanoRefeicao, () {
      super.statusCarregandoPlanoRefeicao = value;
    });
  }

  late final _$statusCarregandoDiaAtom = Atom(
      name: '_ControladorPlannerBase.statusCarregandoDia', context: context);

  @override
  ServiceStatus get statusCarregandoDia {
    _$statusCarregandoDiaAtom.reportRead();
    return super.statusCarregandoDia;
  }

  @override
  set statusCarregandoDia(ServiceStatus value) {
    _$statusCarregandoDiaAtom.reportWrite(value, super.statusCarregandoDia, () {
      super.statusCarregandoDia = value;
    });
  }

  late final _$tituloErroAtom =
      Atom(name: '_ControladorPlannerBase.tituloErro', context: context);

  @override
  String get tituloErro {
    _$tituloErroAtom.reportRead();
    return super.tituloErro;
  }

  @override
  set tituloErro(String value) {
    _$tituloErroAtom.reportWrite(value, super.tituloErro, () {
      super.tituloErro = value;
    });
  }

  late final _$subtituloErroAtom =
      Atom(name: '_ControladorPlannerBase.subtituloErro', context: context);

  @override
  String get subtituloErro {
    _$subtituloErroAtom.reportRead();
    return super.subtituloErro;
  }

  @override
  set subtituloErro(String value) {
    _$subtituloErroAtom.reportWrite(value, super.subtituloErro, () {
      super.subtituloErro = value;
    });
  }

  @override
  String toString() {
    return '''
statusCarregandoDicas: ${statusCarregandoDicas},
statusCarregandoHistoricoCompras: ${statusCarregandoHistoricoCompras},
statusCarregandoReceitas: ${statusCarregandoReceitas},
statusCarregandoFichaDoDia: ${statusCarregandoFichaDoDia},
statusCarregandoPlanoRefeicao: ${statusCarregandoPlanoRefeicao},
statusCarregandoDia: ${statusCarregandoDia},
tituloErro: ${tituloErro},
subtituloErro: ${subtituloErro}
    ''';
  }
}
