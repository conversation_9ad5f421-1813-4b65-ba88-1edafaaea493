// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorManterWod.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorManterWod on _ControladorManterWodBase, Store {
  late final _$mStatusConsultaAtom =
      Atom(name: '_ControladorManterWodBase.mStatusConsulta', context: context);

  @override
  ServiceStatus get mStatusConsulta {
    _$mStatusConsultaAtom.reportRead();
    return super.mStatusConsulta;
  }

  @override
  set mStatusConsulta(ServiceStatus value) {
    _$mStatusConsultaAtom.reportWrite(value, super.mStatusConsulta, () {
      super.mStatusConsulta = value;
    });
  }

  late final _$mStatusConsultaAparelhosAtom = Atom(
      name: '_ControladorManterWodBase.mStatusConsultaAparelhos',
      context: context);

  @override
  ServiceStatus get mStatusConsultaAparelhos {
    _$mStatusConsultaAparelhosAtom.reportRead();
    return super.mStatusConsultaAparelhos;
  }

  @override
  set mStatusConsultaAparelhos(ServiceStatus value) {
    _$mStatusConsultaAparelhosAtom
        .reportWrite(value, super.mStatusConsultaAparelhos, () {
      super.mStatusConsultaAparelhos = value;
    });
  }

  late final _$mStatusConsultaAtividadesAtom = Atom(
      name: '_ControladorManterWodBase.mStatusConsultaAtividades',
      context: context);

  @override
  ServiceStatus get mStatusConsultaAtividades {
    _$mStatusConsultaAtividadesAtom.reportRead();
    return super.mStatusConsultaAtividades;
  }

  @override
  set mStatusConsultaAtividades(ServiceStatus value) {
    _$mStatusConsultaAtividadesAtom
        .reportWrite(value, super.mStatusConsultaAtividades, () {
      super.mStatusConsultaAtividades = value;
    });
  }

  late final _$mListaAparelhosWodAtom = Atom(
      name: '_ControladorManterWodBase.mListaAparelhosWod', context: context);

  @override
  ObservableList<AparelhoWod> get mListaAparelhosWod {
    _$mListaAparelhosWodAtom.reportRead();
    return super.mListaAparelhosWod;
  }

  @override
  set mListaAparelhosWod(ObservableList<AparelhoWod> value) {
    _$mListaAparelhosWodAtom.reportWrite(value, super.mListaAparelhosWod, () {
      super.mListaAparelhosWod = value;
    });
  }

  late final _$mListaAtividadesWodAtom = Atom(
      name: '_ControladorManterWodBase.mListaAtividadesWod', context: context);

  @override
  ObservableList<AtividadeWod> get mListaAtividadesWod {
    _$mListaAtividadesWodAtom.reportRead();
    return super.mListaAtividadesWod;
  }

  @override
  set mListaAtividadesWod(ObservableList<AtividadeWod> value) {
    _$mListaAtividadesWodAtom.reportWrite(value, super.mListaAtividadesWod, () {
      super.mListaAtividadesWod = value;
    });
  }

  late final _$mListaTiposWodAtom =
      Atom(name: '_ControladorManterWodBase.mListaTiposWod', context: context);

  @override
  ObservableList<TipoWodTabela> get mListaTiposWod {
    _$mListaTiposWodAtom.reportRead();
    return super.mListaTiposWod;
  }

  @override
  set mListaTiposWod(ObservableList<TipoWodTabela> value) {
    _$mListaTiposWodAtom.reportWrite(value, super.mListaTiposWod, () {
      super.mListaTiposWod = value;
    });
  }

  late final _$mListaWodsAtom =
      Atom(name: '_ControladorManterWodBase.mListaWods', context: context);

  @override
  ObservableList<WorkoutOfDay> get mListaWods {
    _$mListaWodsAtom.reportRead();
    return super.mListaWods;
  }

  @override
  set mListaWods(ObservableList<WorkoutOfDay> value) {
    _$mListaWodsAtom.reportWrite(value, super.mListaWods, () {
      super.mListaWods = value;
    });
  }

  @override
  String toString() {
    return '''
mStatusConsulta: ${mStatusConsulta},
mStatusConsultaAparelhos: ${mStatusConsultaAparelhos},
mStatusConsultaAtividades: ${mStatusConsultaAtividades},
mListaAparelhosWod: ${mListaAparelhosWod},
mListaAtividadesWod: ${mListaAtividadesWod},
mListaTiposWod: ${mListaTiposWod},
mListaWods: ${mListaWods}
    ''';
  }
}
