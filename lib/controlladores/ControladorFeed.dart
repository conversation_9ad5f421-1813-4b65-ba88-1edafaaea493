import 'package:app_treino/ServiceProvider/authServices/ClienteAppService.dart';
import 'package:app_treino/ServiceProvider/authServices/FeedService.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/feed/ComentarioPostagem.dart';
import 'package:app_treino/model/feed/PostagemFeed.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorFeed.g.dart';

class ControladorFeed = _ControladorFeedBase with _$ControladorFeed;

abstract class _ControladorFeedBase with Store {
  final _feedService = GetIt.I.get<FeedService>();
  final _clientAppService = GetIt.I.get<ClienteAppService>();
  final _controlladorApp = GetIt.I.get<ControladorApp>();
  final _controlladorCliente = GetIt.I.get<ControladorCliente>();
  ServiceStatus statusFeed = ServiceStatus.Empty;
  String? mensagemErroFeed = '';
  @observable
  ObservableList<PostagemFeed> mItensFeed = ObservableList<PostagemFeed>();

  @observable
  ObservableList<PostagemFeed> mItensMeusPost = ObservableList<PostagemFeed>();

  @observable
  ObservableList<ComentarioPostagem> mComentPost = ObservableList<ComentarioPostagem>();

  void consultarPublicacoes({required bool resetar, required Function()? carregando, Function()? falha, Function()? sucesso}) {
    late var loadServiceIs;
    if (resetar || mItensFeed.isEmpty) {
      loadServiceIs = _feedService.consultarFeed(clienteApp: _controlladorApp.mClienteAppSelecionado!.documentkey, codEmpresa:  _controlladorCliente.mUsuarioLogado!.codEmpresa!.toInt() , refUsuario: _controlladorCliente.mUsuarioAuth!.uid, quantidade: 10);
    } else if (mItensFeed.isNotEmpty) {
      loadServiceIs = _feedService.consultarFeed(
          clienteApp: _controlladorApp.mClienteAppSelecionado!.documentkey, codEmpresa: _controlladorCliente.mUsuarioLogado!.codEmpresa!.toInt(), refUsuario: _controlladorCliente.mUsuarioAuth!.uid, quantidade: 10, ultimoPostData: mItensFeed.last.dataMilis);
    }
    carregando?.call();
    loadServiceIs.then((value) {
      if (resetar) {
        mItensFeed.clear();
      }
      List<PostagemFeed> temp = [];
      value.forEach((element) {
        element.comentarios ??= [].cast<ComentarioPostagem>();
        temp.add(element);
      });
      mItensFeed.addAll(temp);
      if (mItensFeed.isNotEmpty) {
        var fisrt = mItensFeed.first.clone();
      fisrt.docKey = 'AD';
      if (!'AD'.contains(mItensFeed.first.docKey!)) mItensFeed.insert(0, fisrt);
      var last = mItensFeed.last.clone();
      last.docKey = 'AD';
      if (!'AD'.contains(mItensFeed.last.docKey!)) mItensFeed.add(last);

      }
      statusFeed = mItensFeed.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      sucesso!();
    }).catchError((onError) {
      statusFeed = ServiceStatus.Error;
      mensagemErroFeed = onError.message;
      
      falha!();
    });
  }

  void consultarMeusPosts({required bool resetar, required Function()? carregando, Function()? falha, Function()? sucesso}) {
    late var loadServiceIs;
    if (resetar || mItensMeusPost.isEmpty) {
      loadServiceIs = _feedService.consultarPostsDe(_controlladorCliente.mUsuarioAuth!.uid!, 10, DateTime.now().millisecondsSinceEpoch);
    } else if (mItensMeusPost.isNotEmpty) {
      loadServiceIs = _feedService.consultarPostsDe(_controlladorCliente.mUsuarioAuth!.uid!, 10, mItensMeusPost.last.dataMilis!);
    }
    carregando?.call();
    loadServiceIs.then((value) {
      if (resetar) mItensMeusPost.clear();
      List<PostagemFeed> temp = [];
      value.forEach((element) {
        temp.add(element);
      });
      mItensMeusPost.addAll(temp);
      statusFeed = temp.isEmpty ? ServiceStatus.Empty : ServiceStatus.Done;
      sucesso!();
    }).catchError((onError) {
      
      falha!();
    });
  }

  void darlikeNaPublicacao(PostagemFeed post, {Function()? carregando, Function()? sucesso, Function()? falha}) {
    _feedService
        .registrarReacaoAoPost(
            post.docKey!,
            _controlladorCliente.mUsuarioAuth!.uid!,
            post.minhaReacao == null
                ? 'LIKE'
                : post.minhaReacao!.tipoReacao == 'LIKE'
                    ? 'UNLIKE'
                    : 'LIKE')
        .then((value) {
      post.reacoes!.clear();
      post.reacoes!.addAll(value);
      if (post.reacoes!.where((element) => element.refUsuario == _controlladorCliente.mUsuarioAuth!.uid).length == 1) {
        post.minhaReacao = post.reacoes!.firstWhere((element) => element.refUsuario == _controlladorCliente.mUsuarioAuth!.uid);
      } else {
        post.minhaReacao = null;
      }

      int index = mItensFeed.indexWhere((element) => post.docKey == element.docKey);
      mItensFeed.removeAt(index);
      mItensFeed.insert(index, post);
      sucesso!();
      UtilitarioApp.showAppReview();
    }).catchError((onError) {
      falha!();
    });
  }

  @observable
  String comentarioPreenchido = '';

  void comentarPublicaco(PostagemFeed post, String comentario, bool telaFeed, {required Function()? carregando, Function()? sucesso, Function()? falha}) {
    carregando?.call();
    _feedService.registrarComentarioFeed(post.docKey!, _controlladorCliente.mUsuarioAuth!.uid!, ComentarioPostagem(comentario: comentario).toJson()).then((value) {
      post.comentarios!.add(value);
      post.quantidadeComentarios = post.quantidadeComentarios! + 1;
      if (!telaFeed) {
        if (!mComentPost.contains(value)) {
          mComentPost.add(value);
        }
      }
      if (post.comentarios!.length > 3) post.comentarios = post.comentarios!.sublist(1);
      int index = mItensFeed.indexWhere((element) => post.docKey == element.docKey);
      mItensFeed.removeAt(index);
      mItensFeed.insert(index, post);
      sucesso!();
    }).catchError((onError) {
      falha!();
      
    });
  }

  void consultarComentarios({bool? resetar, required PostagemFeed post, required Function()? carregando, Function()? sucesso, Function()? falha}) {
    carregando?.call();
    _feedService.obterComentariosPost(post.docKey!).then((value) {
      if (resetar!) mComentPost.clear();
      mComentPost.addAll(value);
      for (final element in value) {
        if(element.emModeracao ?? false){
          mComentPost.remove(element);
        }
      }
      sucesso!();
    }).catchError((onError) {
      falha!();
    });
  }

  void deletarPost(PostagemFeed post, {required Function()? carregando, Function()? sucesso, Function()? falha}) {
    carregando?.call();
    _feedService.deletarPost(post.docKey!).then((value) {
      sucesso!();
      mItensFeed.removeWhere((element) => element.docKey == post.docKey);
      mItensMeusPost.removeWhere((element) => element.docKey == post.docKey);
      if (mItensFeed.isEmpty) {
        _feedService.consultarFeed(clienteApp: _controlladorApp.mClienteAppSelecionado!.documentkey, codEmpresa: 0, refUsuario: _controlladorCliente.mUsuarioAuth!.uid, quantidade: 10).then((value) {
          List<PostagemFeed> temp = [];
          for (final element in value) {
            element.comentarios ??= [];
            temp.add(element);
          }
          mItensFeed.addAll(temp);
        });
      }
    }).catchError((onError) {
      falha!();
    });
  }

  void deletarComentario(PostagemFeed post, ComentarioPostagem comentarioPostagem, {required Function()? carregando, Function()? sucesso, Function()? falha}) {
    carregando?.call();
    _feedService.deletarComentarioFeed(post.docKey!, comentarioPostagem.refKey!).then((value) {
      mComentPost.removeWhere((element) => element.refKey == comentarioPostagem.refKey);
      post.quantidadeComentarios = post.quantidadeComentarios! - 1;
      if (mComentPost.length > 3) {
        post.comentarios = mComentPost.sublist(mComentPost.length - 3, mComentPost.length);
      } else {
        post.comentarios = mComentPost;
      }
      int index = mItensFeed.indexWhere((element) => post.docKey == element.docKey);
      mItensFeed.removeAt(index);
      mItensFeed.insert(index, post);
      sucesso!();
    }).catchError((onError) {
      falha!();
    });
  }

  void denunciarPost(PostagemFeed post, {required Function()? carregando, Function()? sucesso, Function()? falha}) {
    carregando?.call();
    _feedService.denunciarPost(post.docKey!, _controlladorCliente.mUsuarioAuth!.uid!, 'SPAM').then((value) {
      mItensFeed.removeWhere((element) => element.docKey == post.docKey);
      if (mItensFeed.isEmpty) {
        _feedService.consultarFeed(clienteApp: _controlladorApp.mClienteAppSelecionado!.documentkey, codEmpresa: 0, refUsuario: _controlladorCliente.mUsuarioAuth!.uid, quantidade: 10).then((value) {
          List<PostagemFeed> temp = [];
          for (final element in value) {
            element.comentarios ??= [];
            temp.add(element);
          }
          mItensFeed.addAll(temp);
          sucesso!();
        });
      } else {
        sucesso!();
      }
    }).catchError((onError) {
      falha!();
    });
  }

  void denunciarComentario(PostagemFeed post, ComentarioPostagem comentario, {required Function()? carregando, Function()? sucesso, Function(String)? falha}) {
    carregando?.call();
    var bodyComentario = {
      'refComentario': comentario.refKey!,
      'refPostagem': post.docKey!,
      'motivo': 'SPAM',
      'refDenunciante': _controlladorCliente.mUsuarioAuth!.uid!
    };
    _feedService.denunciarComentario(bodyComentario).then((value) {
      sucesso?.call();
      /* mItensFeed.removeWhere((element) => element.docKey == post.docKey);
      if (mItensFeed.isEmpty) {
        _feedService.consultarFeed(clienteApp: _controlladorApp.mClienteAppSelecionado!.documentkey, codEmpresa: 0, refUsuario: _controlladorCliente.mUsuarioAuth!.uid, quantidade: 10).then((value) {
          List<PostagemFeed> temp = [];
          for (final element in value) {
            element.comentarios ??= [];
            temp.add(element);
          }
          mItensFeed.addAll(temp);
          sucesso!();
        });
      } else {
        sucesso?.call();
      } */
    }).catchError((onError) {
      falha?.call(onError);
    });
  }

  void bloquearPosts(PostagemFeed post, {Function()? carregando, Function()? sucesso, Function()? falha}) {
    _feedService.bloquearUsuarioFeed(post.refUsuario!, _controlladorCliente.mUsuarioAuth!.uid!).then((value) {
      mItensFeed.removeWhere((element) => element.refUsuario == post.refUsuario);
      if (mItensFeed.isEmpty) {
        _feedService.consultarFeed(clienteApp: _controlladorApp.mClienteAppSelecionado!.documentkey, codEmpresa: 0, refUsuario: _controlladorCliente.mUsuarioAuth!.uid, quantidade: 10).then((value) {
          List<PostagemFeed> temp = [];
          for (final element in value) {
            element.comentarios ??= [];
            temp.add(element);
          }
          mItensFeed.addAll(temp);
          sucesso!();
        });
      } else {
        sucesso!();
      }
    }).catchError((onError) {
      falha!();
    });
  }

  void publicarPost(String legenda, String? fotoBase64, {required Function()? carregando, Function()? sucesso, Function()? falha}) {
    carregando?.call();
    if (fotoBase64 == null) {
      _feedService.publicarPost(_controlladorApp.mClienteAppSelecionado!.documentkey!, _controlladorCliente.mUsuarioAuth!.uid!, _controlladorCliente.mUsuarioLogado!.codEmpresa!.toInt(), {
        'descricao': legenda,
        'medias': [
          {'tipo': 'TEXT', 'duracao': '', 'url': ''}
        ]
      }).then((value) {
        if (mItensFeed.length == 1 && mItensFeed.first.docKey == 'publiqueSeuPost') mItensFeed.clear();
        mItensFeed.insert(0, value);
        sucesso!();
        UtilitarioApp.showAppReview();
      }).catchError((onError) {
        falha!();
      });
    } else {
      _clientAppService.uploadImagem(fotoBase64).then((url) {
        _feedService.publicarPost(_controlladorApp.mClienteAppSelecionado!.documentkey!, _controlladorCliente.mUsuarioAuth!.uid!, _controlladorCliente.mUsuarioLogado!.codEmpresa!.toInt(), {
          'descricao': legenda,
          'medias': [
            {'tipo': 'IMAGE', 'url': url, 'duracao': ''}
          ]
        }).then((value) {
          if (mItensFeed.length == 1 && mItensFeed.first.docKey == 'publiqueSeuPost') mItensFeed.clear();
          mItensFeed.insert(0, value);
          sucesso!();
          UtilitarioApp.showAppReview();
        }).catchError((onError) {
          falha!();
        });
      });
    }
  }

  limparTudo() {
    mComentPost.clear();
    mItensFeed.clear();
    mItensMeusPost.clear();
  }
}
