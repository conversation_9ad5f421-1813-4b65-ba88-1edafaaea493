// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorChat.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorChat on _ControladorChatBase, Store {
  late final _$mUsuariosConversaAtom =
      Atom(name: '_ControladorChatBase.mUsuariosConversa', context: context);

  @override
  ObservableList<UsuarioChat> get mUsuariosConversa {
    _$mUsuariosConversaAtom.reportRead();
    return super.mUsuariosConversa;
  }

  @override
  set mUsuariosConversa(ObservableList<UsuarioChat> value) {
    _$mUsuariosConversaAtom.reportWrite(value, super.mUsuariosConversa, () {
      super.mUsuariosConversa = value;
    });
  }

  late final _$carregandoConatosAtom =
      Atom(name: '_ControladorChatBase.carregandoConatos', context: context);

  @override
  bool get carregandoConatos {
    _$carregandoConatosAtom.reportRead();
    return super.carregandoConatos;
  }

  @override
  set carregandoConatos(bool value) {
    _$carregandoConatosAtom.reportWrite(value, super.carregandoConatos, () {
      super.carregandoConatos = value;
    });
  }

  late final _$carregandoConversasAtom =
      Atom(name: '_ControladorChatBase.carregandoConversas', context: context);

  @override
  bool get carregandoConversas {
    _$carregandoConversasAtom.reportRead();
    return super.carregandoConversas;
  }

  @override
  set carregandoConversas(bool value) {
    _$carregandoConversasAtom.reportWrite(value, super.carregandoConversas, () {
      super.carregandoConversas = value;
    });
  }

  late final _$mContatosAtom =
      Atom(name: '_ControladorChatBase.mContatos', context: context);

  @override
  ObservableList<UsuarioChat> get mContatos {
    _$mContatosAtom.reportRead();
    return super.mContatos;
  }

  @override
  set mContatos(ObservableList<UsuarioChat> value) {
    _$mContatosAtom.reportWrite(value, super.mContatos, () {
      super.mContatos = value;
    });
  }

  late final _$mContatosExibirAtom =
      Atom(name: '_ControladorChatBase.mContatosExibir', context: context);

  @override
  ObservableList<UsuarioChat> get mContatosExibir {
    _$mContatosExibirAtom.reportRead();
    return super.mContatosExibir;
  }

  @override
  set mContatosExibir(ObservableList<UsuarioChat> value) {
    _$mContatosExibirAtom.reportWrite(value, super.mContatosExibir, () {
      super.mContatosExibir = value;
    });
  }

  late final _$mConversasAtom =
      Atom(name: '_ControladorChatBase.mConversas', context: context);

  @override
  ObservableList<ConversaChat> get mConversas {
    _$mConversasAtom.reportRead();
    return super.mConversas;
  }

  @override
  set mConversas(ObservableList<ConversaChat> value) {
    _$mConversasAtom.reportWrite(value, super.mConversas, () {
      super.mConversas = value;
    });
  }

  late final _$mMensagensConversaAtom =
      Atom(name: '_ControladorChatBase.mMensagensConversa', context: context);

  @override
  ObservableList<MensagemChat> get mMensagensConversa {
    _$mMensagensConversaAtom.reportRead();
    return super.mMensagensConversa;
  }

  @override
  set mMensagensConversa(ObservableList<MensagemChat> value) {
    _$mMensagensConversaAtom.reportWrite(value, super.mMensagensConversa, () {
      super.mMensagensConversa = value;
    });
  }

  @override
  String toString() {
    return '''
mUsuariosConversa: ${mUsuariosConversa},
carregandoConatos: ${carregandoConatos},
carregandoConversas: ${carregandoConversas},
mContatos: ${mContatos},
mContatosExibir: ${mContatosExibir},
mConversas: ${mConversas},
mMensagensConversa: ${mMensagensConversa}
    ''';
  }
}
