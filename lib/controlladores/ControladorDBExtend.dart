import 'dart:convert';

import 'package:get_it/get_it.dart';
import 'package:sembast/sembast.dart';

import 'package:app_treino/controlladores/ControladorNosql.dart';

abstract class UtilDataBase {
  var store = StoreRef.main();
//   Database getDb() => GetIt.I.get<ControladorNoSql>().db;

  Database get getDb => GetIt.I.get<ControladorNoSql>().db;

  Future<dynamic> inserirNoBanco(String key, Map<String, dynamic> object, [bool merge = true]) {
    return store.record(key).put(getDb, const JsonCodec().decode(const JsonCodec().encode(object)), merge: merge).then((value) => value);
  }

  Future<dynamic> inserirAtividadesNoBanco(String key, List<dynamic> object, [bool merge = true]) {
    return store.record(key).put(getDb, const JsonCodec().decode(const JsonCodec().encode(object)), merge: merge).then((value) => value);
  }
Future<String?> buscarTextoNoBanco(String key) {
    return store.record(key).get(getDb).then((value) {
      if (value is String) {
        return value;
      }
      return null;
    });
  }

  Future<dynamic> inserirTextoNoBanco(String key, String text, [bool merge = true]) {
    return store.record(key).put(getDb, text, merge: merge).then((value) => value);
  }
  Future<dynamic> inserirListaNoBanco(String key, List<Object?> object, [bool merge = true]) {
    return store.record(key).put(getDb, const JsonCodec().decode(const JsonCodec().encode(object)), merge: merge).then((value) => value);
  }

  Future buscarNoBanco(String key) {
    return store.record(key).get(getDb).then((value) => value).then((value) => value);
  }

  buscarListaNoBanco(String key) {
    return store.record(key).get(getDb).then((value) => value).then((value) => value);
  }

  Future<dynamic> deletarDoBanco(String key) {
    return store.record(key).delete(getDb).then((value) => value);
  }

  Future<dynamic> inserirStringNoBanco(String key, String object, [bool merge = true]) {
    return store.record(key).put(getDb, object, merge: merge).then((value) => value);
  }

  Future<dynamic> inserirIntNoBanco(String key, int object, [bool merge = true]) {
    return store.record(key).put(getDb, object, merge: merge).then((value) => value);
  }

  Future<String?> buscarStringNoBanco(String key) {
    return store.record(key).get(getDb).then((value) => value as String?);
  }

  Future<int?> buscarIntNoBanco(String key) {
    return store.record(key).get(getDb).then((value) {
      if (value is int) {
        return value;
      }
      return null;
    });
  }
}
