import 'package:app_treino/model/homefit/GdataAPI.dart';
import 'package:app_treino/model/homefit/models.dart';
import 'package:mobx/mobx.dart';
import 'package:http/http.dart' as http;
import 'package:youtube_parser/youtube_parser.dart';

import 'package:app_treino/controlladores/YouTubeAPIResult.dart';
part 'ControladorValidadorYouTube.g.dart';

class ControladorValidadorYouTube = _ControladorValidadorYouTubeBase with _$ControladorValidadorYouTube;

abstract class _ControladorValidadorYouTubeBase with Store {
  @observable
  String? thumb;
  @observable
  String? videoName;
  @observable
  String? videoDuration;
  @observable
  String? ytVideoID;
  @observable
  bool falha = false;

  void reset() {
    videoName = null;
    falha = false;
  }

  validarURL({required String? urlVideo, required Function(STATUSREQUEST status) callback}) async {
    if (urlVideo != null && getIdFromUrl(urlVideo) != null) {
      var videoID = getIdFromUrl(urlVideo);
      callback(STATUSREQUEST.CARREGANDO);
      final responseInfo = await http.get(Uri.parse('https://www.googleapis.com/youtube/v3/videos?id=$videoID&key=AIzaSyAfK46oR9lKhpRTRFwoMHMKS6hMQ_R08hk&part=snippet'));
      final responseAPI = await http.get(Uri.parse('https://www.googleapis.com/youtube/v3/videos?id=$videoID&part=contentDetails&key=AIzaSyAfK46oR9lKhpRTRFwoMHMKS6hMQ_R08hk'));
      if (responseInfo.statusCode == 200 && responseAPI.statusCode == 200) {
        falha = false;
        final ytJsonInfo = gdataApiFromJson(responseInfo.body);
        final apiInfo = youTubeApiResultFromJson(responseAPI.body);
        // Monta os dados de acordo
        try {
          videoDuration = apiInfo.items!.first.contentDetails!.duration!.replaceAll('PT', '').toLowerCase();
        } catch (e) {}
        try {
          videoName = ytJsonInfo.items!.first.snippet!.localized!.title;
        } catch (e) {}
        try {
          thumb = ytJsonInfo.items!.first.snippet!.thumbnails!.medium!.url;
        } catch (err) {
          thumb = 'https://img.youtube.com/vi/$videoID/0.jpg';
        }

        ytVideoID = getIdFromUrl(urlVideo);
        callback(STATUSREQUEST.SUCESSO);
      } else {
        callback(STATUSREQUEST.FALHA);
      }
    } else if (urlVideo!.isEmpty) {
      falha = false;
      videoName = null;
      thumb = '';
    } else if (urlVideo.isNotEmpty) {
      falha = true;
    }
  }
}
