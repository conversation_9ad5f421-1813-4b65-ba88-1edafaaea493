// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorObservacoesAluno.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorObservacoesAluno on _ControladorObservacoesAlunoBase, Store {
  late final _$mObservacoesAtom = Atom(
      name: '_ControladorObservacoesAlunoBase.mObservacoes', context: context);

  @override
  ObservableList<ObservacaoAlunoColaborador> get mObservacoes {
    _$mObservacoesAtom.reportRead();
    return super.mObservacoes;
  }

  @override
  set mObservacoes(ObservableList<ObservacaoAlunoColaborador> value) {
    _$mObservacoesAtom.reportWrite(value, super.mObservacoes, () {
      super.mObservacoes = value;
    });
  }

  late final _$statusConsultaObservacoesAtom = Atom(
      name: '_ControladorObservacoesAlunoBase.statusConsultaObservacoes',
      context: context);

  @override
  ServiceStatus get statusConsultaObservacoes {
    _$statusConsultaObservacoesAtom.reportRead();
    return super.statusConsultaObservacoes;
  }

  @override
  set statusConsultaObservacoes(ServiceStatus value) {
    _$statusConsultaObservacoesAtom
        .reportWrite(value, super.statusConsultaObservacoes, () {
      super.statusConsultaObservacoes = value;
    });
  }

  late final _$observacaoGravarAtom = Atom(
      name: '_ControladorObservacoesAlunoBase.observacaoGravar',
      context: context);

  @override
  String get observacaoGravar {
    _$observacaoGravarAtom.reportRead();
    return super.observacaoGravar;
  }

  @override
  set observacaoGravar(String value) {
    _$observacaoGravarAtom.reportWrite(value, super.observacaoGravar, () {
      super.observacaoGravar = value;
    });
  }

  late final _$observacaoGravarImportanteAtom = Atom(
      name: '_ControladorObservacoesAlunoBase.observacaoGravarImportante',
      context: context);

  @override
  bool get observacaoGravarImportante {
    _$observacaoGravarImportanteAtom.reportRead();
    return super.observacaoGravarImportante;
  }

  @override
  set observacaoGravarImportante(bool value) {
    _$observacaoGravarImportanteAtom
        .reportWrite(value, super.observacaoGravarImportante, () {
      super.observacaoGravarImportante = value;
    });
  }

  @override
  String toString() {
    return '''
mObservacoes: ${mObservacoes},
statusConsultaObservacoes: ${statusConsultaObservacoes},
observacaoGravar: ${observacaoGravar},
observacaoGravarImportante: ${observacaoGravarImportante}
    ''';
  }
}
