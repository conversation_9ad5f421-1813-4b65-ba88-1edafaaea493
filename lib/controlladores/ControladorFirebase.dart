import 'dart:io';

import 'package:app_treino/firebase_options.dart';
import 'package:app_treino/flavors.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';



class ControladorFirebase {
  // Instância estática para o Singleton
  static final ControladorFirebase _instance = ControladorFirebase._internal();

  // Construtor privado
  ControladorFirebase._internal();

  // Método para acessar a instância
  factory ControladorFirebase() {
    return _instance;
  }

  late FirebaseRemoteConfig remoteConfig;
  late Future<void> initializeFlutterFireFuture;

  Future<void> _initializeFlutterFire() async {
    await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
    // Pass all uncaught errors to Crashlytics.
    Function originalOnError = FlutterError.onError!;
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;
    FlutterError.onError = (FlutterErrorDetails errorDetails) async {
      await FirebaseCrashlytics.instance.recordFlutterError(errorDetails);
      // Forward to original handler.
      originalOnError(errorDetails);
    };
  }

  initFirebase() async {
    try {
      if (Platform.isIOS && F.isPersonalizado) {
        await Firebase.initializeApp();
      } else {
        await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
}
    remoteConfig = FirebaseRemoteConfig.instance;
    } catch (e) {
      print('Erro ao inicializar o Firebase: $e');
    }
  }

  initCrashlytics() async {
    initializeFlutterFireFuture = _initializeFlutterFire();
  }

  fetchRemoteConfig() async {
    try {
      await remoteConfig.fetch();
      await remoteConfig.fetchAndActivate();
    } catch (exception) {
      // Handle fetch exception
    }
  }

  String urlChatBotGoogle() {
    return remoteConfig.getString('url_google_chat_api');
  }

  bool showFluxoCompraAntesAnamnese() {
    return remoteConfig.getBool('exibir_contrato_assinatura');
  }

  bool showFluxoMock() {
    return remoteConfig.getBool('nutri_premium_teste_c');
  }

  String chavePemitirEditarCarga() {
    return remoteConfig.getString('permitir_editar_carga');
  }
}
