// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorAvaliarProfessor.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorAvaliarProfessor on _ControladorAvaliarProfessorBase, Store {
  late final _$listaProfessoresAvaliadosAtom = Atom(
      name: '_ControladorAvaliarProfessorBase.listaProfessoresAvaliados',
      context: context);

  @override
  ObservableList<AvaliacaoDeProfessor> get listaProfessoresAvaliados {
    _$listaProfessoresAvaliadosAtom.reportRead();
    return super.listaProfessoresAvaliados;
  }

  @override
  set listaProfessoresAvaliados(ObservableList<AvaliacaoDeProfessor> value) {
    _$listaProfessoresAvaliadosAtom
        .reportWrite(value, super.listaProfessoresAvaliados, () {
      super.listaProfessoresAvaliados = value;
    });
  }

  late final _$statusConsultaProfessoresAtom = Atom(
      name: '_ControladorAvaliarProfessorBase.statusConsultaProfessores',
      context: context);

  @override
  ServiceStatus get statusConsultaProfessores {
    _$statusConsultaProfessoresAtom.reportRead();
    return super.statusConsultaProfessores;
  }

  @override
  set statusConsultaProfessores(ServiceStatus value) {
    _$statusConsultaProfessoresAtom
        .reportWrite(value, super.statusConsultaProfessores, () {
      super.statusConsultaProfessores = value;
    });
  }

  @override
  String toString() {
    return '''
listaProfessoresAvaliados: ${listaProfessoresAvaliados},
statusConsultaProfessores: ${statusConsultaProfessores}
    ''';
  }
}
