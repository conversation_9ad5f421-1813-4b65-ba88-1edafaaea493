// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorIndicacao.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorInicacao on _ControladorInicacaoBase, Store {
  late final _$viuOnBoardAtom =
      Atom(name: '_ControladorInicacaoBase.viuOnBoard', context: context);

  @override
  bool? get viuOnBoard {
    _$viuOnBoardAtom.reportRead();
    return super.viuOnBoard;
  }

  @override
  set viuOnBoard(bool? value) {
    _$viuOnBoardAtom.reportWrite(value, super.viuOnBoard, () {
      super.viuOnBoard = value;
    });
  }

  late final _$carregouDadosAtom =
      Atom(name: '_ControladorInicacaoBase.carregouDados', context: context);

  @override
  bool get carregouDados {
    _$carregouDadosAtom.reportRead();
    return super.carregouDados;
  }

  @override
  set carregouDados(bool value) {
    _$carregouDadosAtom.reportWrite(value, super.carregouDados, () {
      super.carregouDados = value;
    });
  }

  late final _$mResultadoIndicacaoAtom = Atom(
      name: '_ControladorInicacaoBase.mResultadoIndicacao', context: context);

  @override
  InfoIndicacoesPersonal? get mResultadoIndicacao {
    _$mResultadoIndicacaoAtom.reportRead();
    return super.mResultadoIndicacao;
  }

  @override
  set mResultadoIndicacao(InfoIndicacoesPersonal? value) {
    _$mResultadoIndicacaoAtom.reportWrite(value, super.mResultadoIndicacao, () {
      super.mResultadoIndicacao = value;
    });
  }

  @override
  String toString() {
    return '''
viuOnBoard: ${viuOnBoard},
carregouDados: ${carregouDados},
mResultadoIndicacao: ${mResultadoIndicacao}
    ''';
  }
}
