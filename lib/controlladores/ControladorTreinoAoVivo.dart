import 'dart:convert';

import 'package:app_treino/ServiceProvider/TreinoSimples.service.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/homefit/models.dart';
import 'package:app_treino/model/live/TreinoAoVivo.dart';
import 'package:app_treino/screens/homefit/urlValidator.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:http/http.dart' as http;
import 'package:app_treino/controlladores/ControladorTreinoSimples.dart';
part 'ControladorTreinoAoVivo.g.dart';

class ControladorTreinoAoVivo = _ControladorTreinoAoVivoBase with _$ControladorTreinoAoVivo;

abstract class _ControladorTreinoAoVivoBase with Store {
  @observable
  bool abaAulaAoVivoSelecionada = false;
  @observable
  ServiceStatus mStatusConsultaAulasAoVivo = ServiceStatus.Waiting;
  @observable
  bool notify = false;
  @observable
  bool passoUmLiberado = false;
  @observable
  bool passoDoisLiberado = false;
  @observable
  bool acabouDeCriarTreinoAoVivo = false;
  @observable
  String tipoDeLiveCriar = 'AGORA';
  @observable
  // ignore: prefer_final_fields
  ControladorTreinoSimples _mainController = GetIt.I.get<ControladorTreinoSimples>();
  @observable
  ObservableList<TreinoAoVivo?> mListaTreinosAoVivo = ObservableList<TreinoAoVivo?>();
  @observable
  DateTime podePesquisarAoVivoNovamente = new DateTime.now();
  @observable
  bool isEdicao = false;
  @observable
  TreinoAoVivo? treinoAoVivoCriar = TreinoAoVivo();

  final TreinoSimplesService _mService = GetIt.I.get<TreinoSimplesService>();

  @computed
  bool get urlValido {
    final url = treinoAoVivoCriar?.liveYTurl ?? '';
    return UrlValidator.isValidYoutubeUrl(url);
  }

  void formatAndSetUrl(String url) {
    if (url.isEmpty) return;

    try {
      final videoId = UrlValidator.extractVideoId(url);
      if (videoId != null) {
        String formattedUrl = 'https://www.youtube.com/embed/$videoId';
        treinoAoVivoCriar?.liveYTurl = formattedUrl;
      } else {
        treinoAoVivoCriar?.liveYTurl = url;
      }
      validateUrl();
    } catch (e) {
      treinoAoVivoCriar?.liveYTurl = url;
      validateUrl();
    }
  }

  void validateUrl() {
    final url = treinoAoVivoCriar?.liveYTurl ?? '';
    if (UrlValidator.isValidYoutubeUrl(url)) {
      treinoAoVivoCriar = TreinoAoVivo(
        liveYTurl: url,
        tituloLive: treinoAoVivoCriar?.tituloLive,
        descricao: treinoAoVivoCriar?.descricao,
      );
    } else {
      treinoAoVivoCriar = TreinoAoVivo(
        liveYTurl: url,
        tituloLive: treinoAoVivoCriar?.tituloLive,
        descricao: treinoAoVivoCriar?.descricao,
      );
    }
    validaPassoUm();
  }

  void iniciarEdicao(TreinoAoVivo treino) {
    isEdicao = true;
    final documentKey = treino.documentKey;
    treinoAoVivoCriar = TreinoAoVivo.fromJson(treino.toJson());
    treinoAoVivoCriar!.documentKey = documentKey;
    if (treino.dataProgramada != null) {
      dataProgramada = treino.dataProgramada;
      tipoDeLiveCriar = 'AGENDADA';
    }

    if (treino.horaInicio != null) {
      final dateTime = DateTime.fromMillisecondsSinceEpoch(treino.horaInicio!);
      horaInicioLivePrograma = TimeOfDay(hour: dateTime.hour, minute: dateTime.minute);
    }

    validaPassoUm();
  }

  void resetCriarTreinoAoVivo() {
    treinoAoVivoCriar = TreinoAoVivo();
    passoUmLiberado = false;
    passoDoisLiberado = false;
    tipoDeLiveCriar = 'AGORA';
    validaPassoUm();
    resetPassaDois();
  }

  void resetPassaDois() {
    // Não limpar dados se estiver em edição
    if (!isEdicao) {
      treinoAoVivoCriar!.horarioFim = null;
      treinoAoVivoCriar!.horaInicio = null;
      treinoAoVivoCriar!.dataProgramada = null;
      dataProgramada = null;
      horaInicioLivePrograma = null;
      horaFimLiveProgramada = null;
      passoDoisLiberado = false;
    }
  }

  void setTipoDeLiveCriar(String? value) {
    tipoDeLiveCriar = value!;
    if (value == 'AGORA') {
      treinoAoVivoCriar!.horaInicio = DateTime.now().millisecondsSinceEpoch;
      treinoAoVivoCriar!.dataProgramada = DateTime.now();
    }
    validarPassoDois(); // Adicionar validação após mudança
  }

  void setNomeLive(String value) {
    treinoAoVivoCriar!.tituloLive = value.trim();
    validaPassoUm();
  }

  void setDescricao(String value) {
    treinoAoVivoCriar!.descricao = value.trim();
    validaPassoUm();
  }

  void setYoutubeID(String value) {
    treinoAoVivoCriar!.liveYTurl = value.trim();
    validaPassoUm();
  }

  @observable
  TimeOfDay? horaInicioLivePrograma;

  @observable
  TimeOfDay? horaFimLiveProgramada;

  @observable
  DateTime? dataProgramada;

  void setHoraInicioLivePrograma(TimeOfDay novaHora) {
    horaInicioLivePrograma = novaHora;

    if (dataProgramada != null) {
      final dateTime = DateTime(dataProgramada!.year, dataProgramada!.month, dataProgramada!.day, novaHora.hour, novaHora.minute);
      treinoAoVivoCriar!.horaInicio = dateTime.millisecondsSinceEpoch;
      validarPassoDois();
    }
  }

  void setHoraFimLivePrograma(TimeOfDay novaHora) {
    horaFimLiveProgramada = novaHora;
  }

  void setInfoTerminoInicioProgramada(DateTime? dia, TimeOfDay? inicio, DateTime? fim) {
    if (dia != null) {
      dataProgramada = dia;
      treinoAoVivoCriar!.dataProgramada = dia;
    }
    if (inicio != null && dataProgramada != null) {
      horaInicioLivePrograma = inicio;
      final dateTime = DateTime(dataProgramada!.year, dataProgramada!.month, dataProgramada!.day, horaInicioLivePrograma!.hour, horaInicioLivePrograma!.minute);
      treinoAoVivoCriar!.horaInicio = dateTime.millisecondsSinceEpoch;
    }
    validarPassoDois();
  }

  void validaPassoUm() {
    final isTituloPreenchido = treinoAoVivoCriar?.tituloLive?.trim().isNotEmpty ?? false;
    final isDescricaoPreenchida = treinoAoVivoCriar?.descricao?.trim().isNotEmpty ?? false;
    final isUrlValida = UrlValidator.isValidYoutubeUrl(treinoAoVivoCriar?.liveYTurl ?? '');

    passoUmLiberado = isTituloPreenchido && isDescricaoPreenchida && isUrlValida;
  }

  void validarPassoDois() {
    if (tipoDeLiveCriar == 'AGORA') {
      passoDoisLiberado = treinoAoVivoCriar!.horaInicio != null;
    } else {
      // Validação para live programada
      final temDataValida = dataProgramada != null;
      final temHoraInicioValida = horaInicioLivePrograma != null;
      passoDoisLiberado = temDataValida && temHoraInicioValida;
    }
  }

  Future<void> excluirTreinoAoVivo({required TreinoAoVivo treino, required Function(STATUSREQUEST request) callback}) async {
    callback(STATUSREQUEST.CARREGANDO);
    _mService.deletarLive(treino.documentKey!).then((value) {
      consultarTransmissoesAoVivo();
      callback(STATUSREQUEST.SUCESSO);
    }).catchError((onError) => callback(STATUSREQUEST.FALHA));
  }

  Future<void> salvarLiveOnline({required Function(STATUSREQUEST status) callback}) async {
    try {
      callback(STATUSREQUEST.CARREGANDO);

      // Preserva o documentKey se for edição
      final documentKey = treinoAoVivoCriar?.documentKey;

      // Setup básico
      treinoAoVivoCriar!.clienteApp = GetIt.I.get<ControladorApp>().mClienteAppSelecionado!.documentkey;
      treinoAoVivoCriar!.refUsuarioApp = GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid;
      treinoAoVivoCriar!.tipoColaboradorZW = GetIt.I.get<ControladorCliente>().mUsuarioLogado!.professor;

      // Setup horários
      if (tipoDeLiveCriar == 'AGORA') {
        final now = DateTime.now();
        treinoAoVivoCriar!.horaInicio = now.millisecondsSinceEpoch;
        treinoAoVivoCriar!.horarioFim = DateTime(now.year, now.month, now.day, 23, 59, 59).millisecondsSinceEpoch;
      } else if (dataProgramada != null && horaInicioLivePrograma != null) {
        final dataHoraInicio = DateTime(dataProgramada!.year, dataProgramada!.month, dataProgramada!.day, horaInicioLivePrograma!.hour, horaInicioLivePrograma!.minute);
        treinoAoVivoCriar!.horaInicio = dataHoraInicio.millisecondsSinceEpoch;
        treinoAoVivoCriar!.horarioFim = DateTime(dataProgramada!.year, dataProgramada!.month, dataProgramada!.day, 23, 59, 59).millisecondsSinceEpoch;
      }
      if (isEdicao) {
        treinoAoVivoCriar!.documentKey = documentKey;
      }
      final resultado = await _mService.manterLive(treinoAoVivoCriar!.toJson());
      treinoAoVivoCriar = resultado;

      await atualizarListaTreinos();

      acabouDeCriarTreinoAoVivo = true;
      callback(STATUSREQUEST.SUCESSO);
    } catch (error) {
      callback(STATUSREQUEST.FALHA);
    }
  }

  @action
  Future<void> atualizarListaTreinos() async {
    final lives = await _mService.consultarLives(GetIt.I.get<ControladorApp>().mClienteAppSelecionado!.documentkey!, GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid!);
    mListaTreinosAoVivo.clear();
    mListaTreinosAoVivo.addAll(lives);
  }

  Future<void> consultarTransmissoesAoVivo({Function()? sucesso, Function()? carregando, Function(String? mensagem)? falha}) async {
    carregando?.call();
    mStatusConsultaAulasAoVivo = ServiceStatus.Waiting;
    mListaTreinosAoVivo.clear();

    await _mService.consultarLives(GetIt.I.get<ControladorApp>().mClienteAppSelecionado!.documentkey!, GetIt.I.get<ControladorCliente>().mUsuarioAuth!.uid!).then((value) {
      mListaTreinosAoVivo.clear(); // Garante limpeza antes de adicionar
      mListaTreinosAoVivo.addAll(value);
      mStatusConsultaAulasAoVivo = mListaTreinosAoVivo.isEmpty ? ServiceStatus.Empty : ServiceStatus.Done;
      sucesso?.call();
    }).catchError((onError) {
      mStatusConsultaAulasAoVivo = ServiceStatus.Error;
      falha?.call(onError.message);
    });
  }

  void limparDados() {
    if (!isEdicao) {
      treinoAoVivoCriar = TreinoAoVivo(tituloLive: '', descricao: '', liveYTurl: '');
      passoUmLiberado = false;
      passoDoisLiberado = false;
      tipoDeLiveCriar = 'AGORA';
      resetPassaDois();
    }
    isEdicao = false;
  }

  String formatIdYouTube(String? url) {
    if (url == null || url.isEmpty) return '';
    return UrlValidator.extractVideoId(url) ?? '';
  }

  // No ControladorTreinoAoVivo
  @observable
  Map<String, String> statusLives = ObservableMap<String, String>();

  Future<String> getLiveStatus(String videoUrl) async {
    final videoId = formatIdYouTube(videoUrl);
    if (videoId.isEmpty) return '--:--';

    try {
      final url = Uri.parse('https://www.googleapis.com/youtube/v3/videos?id=$videoId&part=liveStreamingDetails,snippet&key=AIzaSyANFHJROZJnUm-dY9dlaB5yqzAJnqs3KCI');

      final response = await http.get(url);
      if (response.statusCode != 200) return '--:--';

      final data = json.decode(response.body);
      final items = data['items'] as List<dynamic>;
      if (items.isEmpty) return '--:--';

      final video = items.first;
      final liveDetails = video['liveStreamingDetails'];
      final snippet = video['snippet'];

      if (liveDetails != null) {
        final actualEndTime = liveDetails['actualEndTime'];
        final actualStartTime = liveDetails['actualStartTime'];
        final scheduledStart = liveDetails['scheduledStartTime'];

        if (actualEndTime != null) return 'encerrado';
        if (actualStartTime != null && actualEndTime == null) return 'ao_vivo';
        if (scheduledStart != null) return 'programado';
        return 'encerrado';
      }

      final publishedAt = snippet['publishedAt'];
      if (publishedAt != null) {
        return DateTime.parse(publishedAt).isAfter(DateTime.now()) ? 'programado' : 'encerrado';
      }

      return '--:--';
    } catch (e) {
      return '--:--';
    }
  }
}
