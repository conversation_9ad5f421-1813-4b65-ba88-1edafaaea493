import 'package:app_treino/model/doClienteApp/doUsuario/Usuario.dart';
import 'package:app_treino/model/personal/CadastroAluno.dart';
import 'package:event_bus/event_bus.dart';

class ControladorEventBus {
  EventBus eventBus = EventBus();
}

class AlunoCriadoEvent {
  CadastroAluno user;

  AlunoCriadoEvent(this.user);
}

class TrocouDeEmpresaEvent {
  Empresa empresa;
  TrocouDeEmpresaEvent(this.empresa);
}

class AgiteParaSuporteEvent {
  bool user;

  AgiteParaSuporteEvent(this.user);
}


class ConcluiuExercicioEvent {
  bool concluiu;

  ConcluiuExercicioEvent(this.concluiu);
}

class ConcluiuSerieEvent {
  bool concluiu;

  ConcluiuSerieEvent(this.concluiu);
}

class ConcluiuTreinoEvent {
  bool concluiu;

  ConcluiuTreinoEvent(this.concluiu);
}

class ConcluiuSeriePeloWatchEvent {
  bool concluiu;

  ConcluiuSeriePeloWatchEvent(this.concluiu);
}

class PausouOTreinoPeloWatchEvent {
  bool concluiu;

  PausouOTreinoPeloWatchEvent(this.concluiu);
}


