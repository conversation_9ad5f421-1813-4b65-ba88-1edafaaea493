import 'dart:io';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:health/health.dart';
import 'package:mobx/mobx.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:carp_serializable/carp_serializable.dart';
part 'ControladorHealthKit.g.dart';

class ControladorHealthKit = _ControladorHealthKitBase with _$ControladorHealthKit;

abstract class _ControladorHealthKitBase with Store {
  final health = Health();
  @observable
  int? quantidadePassosHoje = 0;
  @observable
  double quantidadeCaloriasHoje = 0;
  @observable
  AppState _state = AppState.DATA_NOT_FETCHED;
  List<HealthDataPoint> _healthDataList = [];
  List<RecordingMethod> recordingMethodsToFilter = [];

  static final types = [
    HealthDataType.STEPS,
    HealthDataType.SLEEP_IN_BED,
    HealthDataType.SLEEP_ASLEEP,
    HealthDataType.SLEEP_AWAKE,
    HealthDataType.ACTIVE_ENERGY_BURNED,
    HealthDataType.HEART_RATE,
    if (Platform.isIOS) HealthDataType.DISTANCE_WALKING_RUNNING,
    if (Platform.isAndroid) HealthDataType.DISTANCE_DELTA
  ];

  List<HealthDataAccess> get permissions => types
      .map((type) => [
            HealthDataType.EXERCISE_TIME,
          ].contains(type)
              ? HealthDataAccess.READ
              : HealthDataAccess.READ_WRITE)
      .toList();

  /// Install Google Health Connect on this phone.
  Future<void> installHealthConnect() async => await health.installHealthConnect();

  Future<void> authorize() async {
    await Permission.activityRecognition.request();
    await Permission.location.request();
    bool? hasPermissions = await health.hasPermissions(types, permissions: permissions);
    hasPermissions = false;
    bool authorized = false;
    if (!hasPermissions) {
      try {
        authorized = await health.requestAuthorization(types, permissions: permissions);
        await health.requestHealthDataHistoryAuthorization();
      } catch (error) {
        debugPrint('Exception in authorize: $error');
      }
    }
    _state = (authorized) ? AppState.AUTHORIZED : AppState.AUTH_NOT_GRANTED;
    if (authorized) {
      fetchData();
    }
  }

  Future<void> fetchData() async {
    _state = AppState.FETCHING_DATA;

    DateTime startDate = UtilDataHora.getDataComHorasPersonalizada(dateTime: DateTime.now(), horas: 0, minutos: 0, segundos: 0)!;
    DateTime endDate = UtilDataHora.getDataComHorasPersonalizada(dateTime: DateTime.now(), horas: 23, minutos: 59, segundos: 59)!;
    _healthDataList.clear();

    try {
      List<HealthDataPoint> healthData = await health.getHealthDataFromTypes(
        types: types,
        startTime: startDate,
        endTime: endDate,
        recordingMethodsToFilter: recordingMethodsToFilter,
      );
      healthData.sort((a, b) => b.dateTo.compareTo(a.dateTo));
      _healthDataList.addAll(healthData);
    } catch (error) {
      debugPrint('Exception in getHealthDataFromTypes: $error');
    }
    _healthDataList = health.removeDuplicates(_healthDataList);

    for (final data in _healthDataList) {
      debugPrint(toJsonString(data));
    }
    final listaKcal = _healthDataList.where((element) => element.type == HealthDataType.ACTIVE_ENERGY_BURNED).toList();
    for (final x in listaKcal) {
      try {
        quantidadeCaloriasHoje += double.parse(x.value.toString().split(': ')[1]);
      } catch (e) {
        quantidadeCaloriasHoje += 0;
      }
    }
    quantidadePassosHoje = await Health().getTotalStepsInInterval(startDate, endDate) ?? 0;
    _state = _healthDataList.isEmpty ? AppState.NO_DATA : AppState.DATA_READY;
  }
}

enum AppState {
  DATA_NOT_FETCHED,
  FETCHING_DATA,
  DATA_READY,
  NO_DATA,
  AUTHORIZED,
  AUTH_NOT_GRANTED,
  DATA_ADDED,
  DATA_DELETED,
  DATA_NOT_ADDED,
  DATA_NOT_DELETED,
  STEPS_READY,
  HEALTH_CONNECT_STATUS,
  PERMISSIONS_REVOKING,
  PERMISSIONS_REVOKED,
  PERMISSIONS_NOT_REVOKED,
}
