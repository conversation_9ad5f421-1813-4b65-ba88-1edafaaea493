// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorNPS.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorNPS on _ControladorNPSBase, Store {
  Computed<bool>? _$liberarBotoesComputed;

  @override
  bool get liberarBotoes =>
      (_$liberarBotoesComputed ??= Computed<bool>(() => super.liberarBotoes,
              name: '_ControladorNPSBase.liberarBotoes'))
          .value;

  late final _$notaAtom =
      Atom(name: '_ControladorNPSBase.nota', context: context);

  @override
  int? get nota {
    _$notaAtom.reportRead();
    return super.nota;
  }

  @override
  set nota(int? value) {
    _$notaAtom.reportWrite(value, super.nota, () {
      super.nota = value;
    });
  }

  late final _$problemaAtom =
      Atom(name: '_ControladorNPSBase.problema', context: context);

  @override
  String? get problema {
    _$problemaAtom.reportRead();
    return super.problema;
  }

  @override
  set problema(String? value) {
    _$problemaAtom.reportWrite(value, super.problema, () {
      super.problema = value;
    });
  }

  late final _$comentarioAtom =
      Atom(name: '_ControladorNPSBase.comentario', context: context);

  @override
  String? get comentario {
    _$comentarioAtom.reportRead();
    return super.comentario;
  }

  @override
  set comentario(String? value) {
    _$comentarioAtom.reportWrite(value, super.comentario, () {
      super.comentario = value;
    });
  }

  @override
  String toString() {
    return '''
nota: ${nota},
problema: ${problema},
comentario: ${comentario},
liberarBotoes: ${liberarBotoes}
    ''';
  }
}
