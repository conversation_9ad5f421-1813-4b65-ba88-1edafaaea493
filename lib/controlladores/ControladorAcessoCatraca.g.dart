// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorAcessoCatraca.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorAcessoCatraca on _ControladorAcessoCatracaBase, Store {
  late final _$mLocaisAcessoExibirAtom = Atom(
      name: '_ControladorAcessoCatracaBase.mLocaisAcessoExibir',
      context: context);

  @override
  List<LocalDeAcesso> get mLocaisAcessoExibir {
    _$mLocaisAcessoExibirAtom.reportRead();
    return super.mLocaisAcessoExibir;
  }

  @override
  set mLocaisAcessoExibir(List<LocalDeAcesso> value) {
    _$mLocaisAcessoExibirAtom.reportWrite(value, super.mLocaisAcessoExibir, () {
      super.mLocaisAcessoExibir = value;
    });
  }

  late final _$registroAcessoAtom = Atom(
      name: '_ControladorAcessoCatracaBase.registroAcesso', context: context);

  @override
  AcessoRegistrado get registroAcesso {
    _$registroAcessoAtom.reportRead();
    return super.registroAcesso;
  }

  @override
  set registroAcesso(AcessoRegistrado value) {
    _$registroAcessoAtom.reportWrite(value, super.registroAcesso, () {
      super.registroAcesso = value;
    });
  }

  @override
  String toString() {
    return '''
mLocaisAcessoExibir: ${mLocaisAcessoExibir},
registroAcesso: ${registroAcesso}
    ''';
  }
}
