// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorTreinoAoVivo.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorTreinoAoVivo on _ControladorTreinoAoVivoBase, Store {
  Computed<bool>? _$urlValidoComputed;

  @override
  bool get urlValido =>
      (_$urlValidoComputed ??= Computed<bool>(() => super.urlValido,
              name: '_ControladorTreinoAoVivoBase.urlValido'))
          .value;

  late final _$abaAulaAoVivoSelecionadaAtom = Atom(
      name: '_ControladorTreinoAoVivoBase.abaAulaAoVivoSelecionada',
      context: context);

  @override
  bool get abaAulaAoVivoSelecionada {
    _$abaAulaAoVivoSelecionadaAtom.reportRead();
    return super.abaAulaAoVivoSelecionada;
  }

  @override
  set abaAulaAoVivoSelecionada(bool value) {
    _$abaAulaAoVivoSelecionadaAtom
        .reportWrite(value, super.abaAulaAoVivoSelecionada, () {
      super.abaAulaAoVivoSelecionada = value;
    });
  }

  late final _$mStatusConsultaAulasAoVivoAtom = Atom(
      name: '_ControladorTreinoAoVivoBase.mStatusConsultaAulasAoVivo',
      context: context);

  @override
  ServiceStatus get mStatusConsultaAulasAoVivo {
    _$mStatusConsultaAulasAoVivoAtom.reportRead();
    return super.mStatusConsultaAulasAoVivo;
  }

  @override
  set mStatusConsultaAulasAoVivo(ServiceStatus value) {
    _$mStatusConsultaAulasAoVivoAtom
        .reportWrite(value, super.mStatusConsultaAulasAoVivo, () {
      super.mStatusConsultaAulasAoVivo = value;
    });
  }

  late final _$notifyAtom =
      Atom(name: '_ControladorTreinoAoVivoBase.notify', context: context);

  @override
  bool get notify {
    _$notifyAtom.reportRead();
    return super.notify;
  }

  @override
  set notify(bool value) {
    _$notifyAtom.reportWrite(value, super.notify, () {
      super.notify = value;
    });
  }

  late final _$passoUmLiberadoAtom = Atom(
      name: '_ControladorTreinoAoVivoBase.passoUmLiberado', context: context);

  @override
  bool get passoUmLiberado {
    _$passoUmLiberadoAtom.reportRead();
    return super.passoUmLiberado;
  }

  @override
  set passoUmLiberado(bool value) {
    _$passoUmLiberadoAtom.reportWrite(value, super.passoUmLiberado, () {
      super.passoUmLiberado = value;
    });
  }

  late final _$passoDoisLiberadoAtom = Atom(
      name: '_ControladorTreinoAoVivoBase.passoDoisLiberado', context: context);

  @override
  bool get passoDoisLiberado {
    _$passoDoisLiberadoAtom.reportRead();
    return super.passoDoisLiberado;
  }

  @override
  set passoDoisLiberado(bool value) {
    _$passoDoisLiberadoAtom.reportWrite(value, super.passoDoisLiberado, () {
      super.passoDoisLiberado = value;
    });
  }

  late final _$acabouDeCriarTreinoAoVivoAtom = Atom(
      name: '_ControladorTreinoAoVivoBase.acabouDeCriarTreinoAoVivo',
      context: context);

  @override
  bool get acabouDeCriarTreinoAoVivo {
    _$acabouDeCriarTreinoAoVivoAtom.reportRead();
    return super.acabouDeCriarTreinoAoVivo;
  }

  @override
  set acabouDeCriarTreinoAoVivo(bool value) {
    _$acabouDeCriarTreinoAoVivoAtom
        .reportWrite(value, super.acabouDeCriarTreinoAoVivo, () {
      super.acabouDeCriarTreinoAoVivo = value;
    });
  }

  late final _$tipoDeLiveCriarAtom = Atom(
      name: '_ControladorTreinoAoVivoBase.tipoDeLiveCriar', context: context);

  @override
  String get tipoDeLiveCriar {
    _$tipoDeLiveCriarAtom.reportRead();
    return super.tipoDeLiveCriar;
  }

  @override
  set tipoDeLiveCriar(String value) {
    _$tipoDeLiveCriarAtom.reportWrite(value, super.tipoDeLiveCriar, () {
      super.tipoDeLiveCriar = value;
    });
  }

  late final _$_mainControllerAtom = Atom(
      name: '_ControladorTreinoAoVivoBase._mainController', context: context);

  @override
  ControladorTreinoSimples get _mainController {
    _$_mainControllerAtom.reportRead();
    return super._mainController;
  }

  @override
  set _mainController(ControladorTreinoSimples value) {
    _$_mainControllerAtom.reportWrite(value, super._mainController, () {
      super._mainController = value;
    });
  }

  late final _$mListaTreinosAoVivoAtom = Atom(
      name: '_ControladorTreinoAoVivoBase.mListaTreinosAoVivo',
      context: context);

  @override
  ObservableList<TreinoAoVivo?> get mListaTreinosAoVivo {
    _$mListaTreinosAoVivoAtom.reportRead();
    return super.mListaTreinosAoVivo;
  }

  @override
  set mListaTreinosAoVivo(ObservableList<TreinoAoVivo?> value) {
    _$mListaTreinosAoVivoAtom.reportWrite(value, super.mListaTreinosAoVivo, () {
      super.mListaTreinosAoVivo = value;
    });
  }

  late final _$podePesquisarAoVivoNovamenteAtom = Atom(
      name: '_ControladorTreinoAoVivoBase.podePesquisarAoVivoNovamente',
      context: context);

  @override
  DateTime get podePesquisarAoVivoNovamente {
    _$podePesquisarAoVivoNovamenteAtom.reportRead();
    return super.podePesquisarAoVivoNovamente;
  }

  @override
  set podePesquisarAoVivoNovamente(DateTime value) {
    _$podePesquisarAoVivoNovamenteAtom
        .reportWrite(value, super.podePesquisarAoVivoNovamente, () {
      super.podePesquisarAoVivoNovamente = value;
    });
  }

  late final _$isEdicaoAtom =
      Atom(name: '_ControladorTreinoAoVivoBase.isEdicao', context: context);

  @override
  bool get isEdicao {
    _$isEdicaoAtom.reportRead();
    return super.isEdicao;
  }

  @override
  set isEdicao(bool value) {
    _$isEdicaoAtom.reportWrite(value, super.isEdicao, () {
      super.isEdicao = value;
    });
  }

  late final _$treinoAoVivoCriarAtom = Atom(
      name: '_ControladorTreinoAoVivoBase.treinoAoVivoCriar', context: context);

  @override
  TreinoAoVivo? get treinoAoVivoCriar {
    _$treinoAoVivoCriarAtom.reportRead();
    return super.treinoAoVivoCriar;
  }

  @override
  set treinoAoVivoCriar(TreinoAoVivo? value) {
    _$treinoAoVivoCriarAtom.reportWrite(value, super.treinoAoVivoCriar, () {
      super.treinoAoVivoCriar = value;
    });
  }

  late final _$horaInicioLiveProgramaAtom = Atom(
      name: '_ControladorTreinoAoVivoBase.horaInicioLivePrograma',
      context: context);

  @override
  TimeOfDay? get horaInicioLivePrograma {
    _$horaInicioLiveProgramaAtom.reportRead();
    return super.horaInicioLivePrograma;
  }

  @override
  set horaInicioLivePrograma(TimeOfDay? value) {
    _$horaInicioLiveProgramaAtom
        .reportWrite(value, super.horaInicioLivePrograma, () {
      super.horaInicioLivePrograma = value;
    });
  }

  late final _$horaFimLiveProgramadaAtom = Atom(
      name: '_ControladorTreinoAoVivoBase.horaFimLiveProgramada',
      context: context);

  @override
  TimeOfDay? get horaFimLiveProgramada {
    _$horaFimLiveProgramadaAtom.reportRead();
    return super.horaFimLiveProgramada;
  }

  @override
  set horaFimLiveProgramada(TimeOfDay? value) {
    _$horaFimLiveProgramadaAtom.reportWrite(value, super.horaFimLiveProgramada,
        () {
      super.horaFimLiveProgramada = value;
    });
  }

  late final _$dataProgramadaAtom = Atom(
      name: '_ControladorTreinoAoVivoBase.dataProgramada', context: context);

  @override
  DateTime? get dataProgramada {
    _$dataProgramadaAtom.reportRead();
    return super.dataProgramada;
  }

  @override
  set dataProgramada(DateTime? value) {
    _$dataProgramadaAtom.reportWrite(value, super.dataProgramada, () {
      super.dataProgramada = value;
    });
  }

  late final _$statusLivesAtom =
      Atom(name: '_ControladorTreinoAoVivoBase.statusLives', context: context);

  @override
  Map<String, String> get statusLives {
    _$statusLivesAtom.reportRead();
    return super.statusLives;
  }

  @override
  set statusLives(Map<String, String> value) {
    _$statusLivesAtom.reportWrite(value, super.statusLives, () {
      super.statusLives = value;
    });
  }

  late final _$atualizarListaTreinosAsyncAction = AsyncAction(
      '_ControladorTreinoAoVivoBase.atualizarListaTreinos',
      context: context);

  @override
  Future<void> atualizarListaTreinos() {
    return _$atualizarListaTreinosAsyncAction
        .run(() => super.atualizarListaTreinos());
  }

  @override
  String toString() {
    return '''
abaAulaAoVivoSelecionada: ${abaAulaAoVivoSelecionada},
mStatusConsultaAulasAoVivo: ${mStatusConsultaAulasAoVivo},
notify: ${notify},
passoUmLiberado: ${passoUmLiberado},
passoDoisLiberado: ${passoDoisLiberado},
acabouDeCriarTreinoAoVivo: ${acabouDeCriarTreinoAoVivo},
tipoDeLiveCriar: ${tipoDeLiveCriar},
mListaTreinosAoVivo: ${mListaTreinosAoVivo},
podePesquisarAoVivoNovamente: ${podePesquisarAoVivoNovamente},
isEdicao: ${isEdicao},
treinoAoVivoCriar: ${treinoAoVivoCriar},
horaInicioLivePrograma: ${horaInicioLivePrograma},
horaFimLiveProgramada: ${horaFimLiveProgramada},
dataProgramada: ${dataProgramada},
statusLives: ${statusLives},
urlValido: ${urlValido}
    ''';
  }
}
