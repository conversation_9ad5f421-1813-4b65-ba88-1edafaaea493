import 'dart:convert';
import 'dart:io';
import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/controlladores/ControladorFirebase.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/doClienteApp/UsuarioKeep.dart';
import 'package:app_treino/util/debug_utils.dart';
import 'package:app_treino/util/service.push.dart';
import 'package:dart_ipify/dart_ipify.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:app_treino/ServiceProvider/authServices/ClienteAppService.dart';
import 'package:app_treino/ServiceProvider/authServices/ServiceAuth.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorSplash.dart';
import 'package:app_treino/fabricaGetIt.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/model/UserDataKeys.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/doClienteApp/ConfiguracoesTreinoWeb.dart';
import 'package:app_treino/model/doClienteApp/DadosDoUsuario.dart';
import 'package:app_treino/model/doClienteApp/UrlDiscorver.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/BodyInfoAuth.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/UserAuthFirebase.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/Usuario.dart';
import 'package:app_treino/model/util/UtilColor.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:app_treino/controlladores/ControladorDBExtend.dart';
import 'package:shared_preferences/shared_preferences.dart';
part 'ControladorAppLoading.g.dart';

class ControladorAppLoading = _ControladorAppLoading with _$ControladorAppLoading;

enum DataBaseKeys {
  usuarioLogado,
  dadosFirebase,
  dadosDoUsuario,
  clienteAppSelecionado;

  String get key => this.toString().split('.').last;
}

abstract class _ControladorAppLoading extends UtilDataBase with Store {
  int? codEmpresaRelogin;
  ClienteAppService get _mServiceCliente => GetIt.I.get<ClienteAppService>();
  ServiceAuth get _mServiceAuth => GetIt.I.get<ServiceAuth>();
  // ControladorApp & ControladoCliente
  ControladorApp get _mControladorApp => GetIt.I.get<ControladorApp>();
  ControladorCliente get _mControladorCliente => GetIt.I.get<ControladorCliente>();
  ControladorSplash get _mControladorSplash => GetIt.I.get<ControladorSplash>();
  // ClienteApp
  ClienteApp? mLoadedClienteApp;
  // Usuario
  Usuario? mLoadedUsuario;
  // UserAuthFirebase
  UserAuthFirebase? mLoadedUserAuthFirebase;
  // Dados do Usuario
  DadosDoUsuario? mLoadedDadosDoUsuario;
  // Chave
  String? mLoadedChave;
  // Is Usuario do tipo Colaborador
  bool get isUsuarioColaborador => mLoadedUsuario == null
      ? false
      : mLoadedUsuario != null && (mLoadedUsuario!.perfilUsuario?.isNotEmpty ?? false) ||
          ['PROFESSOR', 'COLABORADOR'].any((element) => (mLoadedUsuario?.descricaoPerfil ?? '').toLowerCase().contains(element)) ||
          (mLoadedUsuario?.matricula?.isEmpty ?? true);
  // Is usuario do tipo Aluno
  bool get isUsuarioAluno => !isUsuarioColaborador;
  // Carregar Dados do app
  Future<void> verificarUsuarioLogado({
    Function()? onDadosCarregados,
    Function()? onApenasClienteApp,
    Function()? onClienteAppDesativado,
    Function()? onSemDados,
    Function()? onFalha,
    Function()? onApenasClienteAppSemUnidadeSelecionada,
    required Null Function() onLoadClienteApp,
    bool carregouAppPersonalizado = false,
  }) async {
    final stopwatchTotal = _initWatch();
    DebugUtils.debugLog('🚀 [LOADING] Iniciando carregamento de dados do usuário...');

    try {
      if (F.isPersonalizado && !carregouAppPersonalizado) {
        mLoadedClienteApp = ClienteApp(documentkey: F.firebaseClienteAppRef);
        _buscarERealizarUpdateClienteApp().then((onValue) {
          onLoadClienteApp();
          verificarUsuarioLogado(
            onDadosCarregados: onDadosCarregados,
            onApenasClienteApp: onApenasClienteApp,
            onClienteAppDesativado: onClienteAppDesativado,
            onSemDados: onSemDados,
            onFalha: onFalha,
            onApenasClienteAppSemUnidadeSelecionada: onApenasClienteAppSemUnidadeSelecionada,
            carregouAppPersonalizado: true,
            onLoadClienteApp: onLoadClienteApp,
          );
        }).catchError((onError) {
          onFalha?.call();
        });
        return;
      }
      Map<String, dynamic> checkDados = {};

      // Primeiro verificamos se existe Cliente App, Usuario Logado e Dados do Usuario
      SharedPreferences prefs = await SharedPreferences.getInstance();
      mLoadedChave = prefs.getString(UserDataKeys.CHAVE.toString());
      _loadDadosBaseApp(prefs);
      if ((mLoadedChave?.isEmpty ?? true)) {
        Future.delayed(const Duration(seconds: 2), () {
          if (F.isPersonalizado) {
            _mControladorApp.mClienteAppSelecionado = mLoadedClienteApp;
            _mControladorApp.defaultColor = HexColor.fromHex(mLoadedClienteApp!.corPadrao);
            _loadDadosBaseApp(prefs);
            if (mLoadedClienteApp!.empresaApps?.length == 1) {
              mLoadedChave = mLoadedClienteApp!.empresaApps!.first.chave;
              _mControladorApp.chave = mLoadedChave;
              prefs.setString(UserDataKeys.CHAVE.toString(), mLoadedChave!);
              onApenasClienteApp?.call();
            } else {
              onApenasClienteAppSemUnidadeSelecionada?.call();
            }
          } else {
            onSemDados?.call();
          }
        });
        return;
      }
      _mControladorApp.chave = mLoadedChave;

      // Carrega dados de tema do app em paralelo (versão otimizada)
      final stopwatchDB = kDebugMode ? (Stopwatch()..start()) : null;
      final futures = DataBaseKeys.values.where((x) => x != DataBaseKeys.dadosDoUsuario).map((key) async {
        return MapEntry(key.key, await buscarNoBanco(key.key));
      });
      final results = await Future.wait(futures);
      stopwatchDB?.stop();
      DebugUtils.debugLog('💾 [LOADING] Dados do banco local carregados em ${stopwatchDB?.elapsedMilliseconds}ms');
      for (final entry in results) {
        checkDados[entry.key] = entry.value;
      }
      // Se não existir dados do usuario, não existe usuario logado
      if (checkDados.values.where((x) => x == null).length == checkDados.length) {
        Future.delayed(const Duration(seconds: 2), () {
          onSemDados?.call();
        });
        return;
      }

      // Se existir apenas clienteApp Selecionado vai para a tela de login
      if (checkDados[DataBaseKeys.clienteAppSelecionado.key] != null && checkDados[DataBaseKeys.usuarioLogado.key] == null && checkDados[DataBaseKeys.dadosDoUsuario.key] == null) {
        mLoadedClienteApp = ClienteApp.fromJson(checkDados[DataBaseKeys.clienteAppSelecionado.key]);
        _mControladorApp.mClienteAppSelecionado = mLoadedClienteApp;
        if (((mLoadedClienteApp!.empresaApps?.length ?? 0) > 1) && (mLoadedChave?.isEmpty ?? true)) {
          Future.delayed(const Duration(seconds: 2), () {
            _mControladorApp.statusCarregandoAcademias = ServiceStatus.Done;
            onApenasClienteAppSemUnidadeSelecionada?.call();
          });
          return;
        } else {
          if (mLoadedChave?.isEmpty ?? false) {
            onApenasClienteAppSemUnidadeSelecionada?.call();
            return;
          }
          prefs.setString(UserDataKeys.CHAVE.toString(), mLoadedChave!);
          _buscarUrls().then((onValue) {
            _mControladorApp.mUrls = onValue;
            _loadDadosBaseApp(prefs);
            Future.delayed(const Duration(seconds: 2), () {
              onApenasClienteApp?.call();
            });
          });
        }
        //   Future.delayed(const Duration(seconds: 4), () {
        //     onApenasClienteApp?.call();
        //   });
        return;
      }

      // Se existir dados do usuario, carrega os dados do usuario
      _mControladorApp.chave = mLoadedChave;
      mLoadedClienteApp = ClienteApp.fromJson(checkDados[DataBaseKeys.clienteAppSelecionado.key]);
      mLoadedUsuario = Usuario.fromJson(checkDados[DataBaseKeys.usuarioLogado.key]);
      if (checkDados[DataBaseKeys.dadosDoUsuario.key] != null) {
        mLoadedDadosDoUsuario = DadosDoUsuario.fromJson(checkDados[DataBaseKeys.dadosDoUsuario.key]);
      }
      if (checkDados[DataBaseKeys.dadosDoUsuario.key] != null) {
        mLoadedUserAuthFirebase = UserAuthFirebase.fromJson(checkDados[DataBaseKeys.dadosFirebase.key]);
      }
      return ((F.isPersonalizado && carregouAppPersonalizado) ? Future.value(mLoadedClienteApp) : _buscarERealizarUpdateClienteApp()).then((onValue) {
        _mControladorApp.mClienteAppSelecionado = mLoadedClienteApp;
        _mControladorApp.defaultColor = HexColor.fromHex(mLoadedClienteApp!.corPadrao);
        if (mLoadedClienteApp!.aplicativoAtivo == false) {
          onClienteAppDesativado?.call();
          throw Exception('Cliente App Desativado');
        }
        return _loadTokenUsuario();
      }).then((x) {
        return _buscarUrls();
      }).then((onValue) {
        _mControladorApp.mUrls = onValue;
        return _buscarERealizarUpdateUsuario();
      }).then((mUsuario) {
        _mControladorCliente.mUsuarioLogado = mUsuario;
        return _buscarERealizarUpdateUserAuthFirebase();
      }).then((mUsuarioAuth) {
        _mControladorCliente.mUsuarioAuth = mUsuarioAuth;
        return _buscarERealizarUpdateDadosDoUsuario();
      }).then((mDadosUsuarios) {
        _mControladorCliente.mDadosDoUsuario = mDadosUsuarios;
        return _buscarERealizarUpdateConfiguracoesTreinoWeb();
      }).then((mConfiguracoesTreinoWeb) {
        _mControladorApp.mConfiguracoesTreinoWeb.clear();
        for (final config in (mConfiguracoesTreinoWeb ?? [])) {
          _mControladorApp.mConfiguracoesTreinoWeb.add(config);
        }
        _mControladorApp.prepararTelasBaseadoNasConfiguracoes(
          eColaborador: isUsuarioColaborador,
          tipoApp: F.appFlavor!,
        );

        // Execução otimizada com operações em paralelo
        DebugUtils.debugLog('🔄 [LOADING] Iniciando carregamento otimizado...');
        return _executarCarregamentoOtimizado(onDadosCarregados, onClienteAppDesativado, onFalha, stopwatchTotal);
      }).catchError((onError) {
        stopwatchTotal?.stop();
        DebugUtils.debugLog('❌ [LOADING] Erro no carregamento após ${stopwatchTotal?.elapsedMilliseconds}ms: $onError');
        if (onError.toString().contains('Usuario não encontrado')) {
          GetIt.I.get<ControladorCliente>().deslogarUsuario(sucesso: () {
            // Usuário removido, retorna para tela de login
          });
          return;
        }
        onFalha?.call();
      });
    } catch (e) {
      stopwatchTotal?.stop();
      DebugUtils.debugLog('❌ [LOADING] Erro no carregamento após ${stopwatchTotal?.elapsedMilliseconds}ms: $e');
      onFalha?.call();
    }
  }

  Stopwatch? _initWatch() {
    if (kDebugMode) {
      return Stopwatch()..start();
    }
    return null;
  }

  Future<void> verificarSeIpEstaBloqueado() async {
    try {
      GetIt.I.get<ServiceAuth>().consultarRedesSuspeitas().then((listaIpsBloqueados) async {
        final ipv4 = await Ipify.ipv4();
        final ipTratado = '${ipv4.split('.')[0]}.${ipv4.split('.')[1]}';
        final ipEstaBloqueado = listaIpsBloqueados
            .where((element) {
              final ipBloqueadoTratado = '${element.split('.')[0]}.${element.split('.')[1]}';
              return ipBloqueadoTratado == ipTratado;
            })
            .toList()
            .isNotEmpty;
        if (ipEstaBloqueado) {
          DebugUtils.debugLog('⚠️ [SECURITY] IP bloqueado detectado: $ipTratado');
          final userAgent = await GetIt.I.get<ControladorApp>().gerarHeaderDeOrigin();
          final urlTreino = await ConfigURL().url(ConfigURL.TREINO);
          var json = jsonEncode({
            'text': '⚠️ Usuário com o IP bloqueado no app ${F.nomeApp}\n'
                '➡️ Ip do Usuário: ${ipv4}\n'
                '➡️ Url do Treino: ${urlTreino}\n'
                '➡️ User-Agent: ${userAgent}\n\n'
                '**Dados do Usuário** \n'
                'Nome do Usuario: ${GetIt.I.get<ControladorCliente>().mUsuarioLogado?.nome} \n'
                'Email: ${GetIt.I.get<ControladorCliente>().mUsuarioLogado?.email} \n'
                'Tipo Usuário: ${GetIt.I.get<ControladorCliente>().isUsuarioColaborador ? 'Colaborador' : 'Aluno'} \n'
                'Chave: ${GetIt.I.get<ControladorApp>().chave ?? ''} \n'
                'Dispositivo: ${Platform.operatingSystem.toUpperCase()}'
          });
          await http.post(
            Uri.parse(GetIt.I.get<ControladorFirebase>().urlChatBotGoogle()),
            headers: <String, String>{
              'Content-Type': 'application/json; charset=UTF-8',
            },
            body: json,
          );
        }
      }).catchError((error) {
        DebugUtils.debugLog('❌ [SECURITY] Erro ao verificar IPs bloqueados: $error');
      });
    } catch (e) {
      DebugUtils.debugLog('❌ [SECURITY] Erro geral na verificação de IP: $e');
    }
  }

  Future<void> _executarCarregamentoOtimizado(
    Function()? onDadosCarregados,
    Function()? onClienteAppDesativado,
    Function()? onFalha,
    Stopwatch? stopwatchTotal,
  ) async {
    try {
      // Fase 1: Atualizar ClienteApp e carregar token em paralelo com URLs
      DebugUtils.debugLog('🔄 [LOADING] Fase 1 - Carregando ClienteApp e URLs...');
      final stopwatchFase1 = _initWatch();
      final futures1 = await Future.wait([
        _buscarERealizarUpdateClienteApp(),
        _buscarUrls(),
      ]);
      stopwatchFase1?.stop();
      DebugUtils.debugLog('✅ [LOADING] Fase 1 concluída em ${stopwatchFase1?.elapsedMilliseconds}ms');

      final clienteApp = futures1[0] as ClienteApp?;
      final urls = futures1[1] as UrlDiscorver?;

      _mControladorApp.mClienteAppSelecionado = clienteApp;
      _mControladorApp.defaultColor = HexColor.fromHex(clienteApp!.corPadrao);
      _mControladorApp.mUrls = urls;

      if (clienteApp.aplicativoAtivo == false) {
        stopwatchTotal?.stop();
        DebugUtils.debugLog('❌ [LOADING] ClienteApp desativado após ${stopwatchTotal?.elapsedMilliseconds}ms');
        onClienteAppDesativado?.call();
        throw Exception('Cliente App Desativado');
      }

      // Carrega token do usuário
      DebugUtils.debugLog('🔑 [LOADING] Carregando token do usuário...');
      final stopwatchToken = _initWatch();
      await _loadTokenUsuario();
      stopwatchToken?.stop();
      DebugUtils.debugLog('✅ [LOADING] Token carregado em ${stopwatchToken?.elapsedMilliseconds}ms');

      // Fase 2: Executar operações que dependem do usuário em paralelo
      DebugUtils.debugLog('🔄 [LOADING] Fase 2 - Carregando dados do usuário...');
      final stopwatchFase2 = _initWatch();
      final futures2 = await Future.wait([
        _buscarERealizarUpdateUsuario(),
        _buscarERealizarUpdateUserAuthFirebase(),
        _buscarERealizarUpdateConfiguracoesTreinoWeb(),
      ]);
      stopwatchFase2?.stop();
      DebugUtils.debugLog('✅ [LOADING] Fase 2 concluída em ${stopwatchFase2?.elapsedMilliseconds}ms');

      final usuario = futures2[0] as Usuario?;
      final userAuth = futures2[1] as UserAuthFirebase?;
      final configuracoesTreinoWeb = futures2[2] as List<ConfiguracaoTreino?>?;

      _mControladorCliente.mUsuarioLogado = usuario;
      _mControladorCliente.mUsuarioAuth = userAuth;

      // Atualizar dados do usuário (pode depender do tipo de usuário)
      DebugUtils.debugLog('👤 [LOADING] Carregando dados específicos do usuário...');
      final stopwatchDadosUsuario = _initWatch();
      final dadosUsuario = await _buscarERealizarUpdateDadosDoUsuario();
      stopwatchDadosUsuario?.stop();
      DebugUtils.debugLog('✅ [LOADING] Dados do usuário carregados em ${stopwatchDadosUsuario?.elapsedMilliseconds}ms');

      _mControladorCliente.mDadosDoUsuario = dadosUsuario;

      // Configurar telas e notificações
      DebugUtils.debugLog('⚙️ [LOADING] Configurando telas e preparando interface...');
      final stopwatchConfig = _initWatch();
      _mControladorApp.mConfiguracoesTreinoWeb.clear();
      for (final config in (configuracoesTreinoWeb ?? [])) {
        _mControladorApp.mConfiguracoesTreinoWeb.add(config);
      }

      _mControladorApp.prepararTelasBaseadoNasConfiguracoes(
        eColaborador: isUsuarioColaborador,
        tipoApp: F.appFlavor!,
      );
      stopwatchConfig?.stop();
      DebugUtils.debugLog('✅ [LOADING] Configurações aplicadas em ${stopwatchConfig?.elapsedMilliseconds}ms');

      // Fase 3: Inicializar serviços em paralelo (não bloqueantes)
      DebugUtils.debugLog('🔄 [LOADING] Fase 3 - Inicializando serviços auxiliares...');
      _initializeServicesInParallel(onDadosCarregados, stopwatchTotal);
    } catch (onError) {
      stopwatchTotal?.stop();
      DebugUtils.debugLog('❌ [LOADING] Erro no carregamento otimizado após ${stopwatchTotal?.elapsedMilliseconds}ms: $onError');
      if (onError.toString().contains('Usuario não encontrado')) {
        GetIt.I.get<ControladorCliente>().deslogarUsuario(sucesso: () {
          // Usuário removido, retorna para tela de login
        });
        return;
      }
      onFalha?.call();
    }
  }

  void _initializeServicesInParallel(Function()? onDadosCarregados, Stopwatch? stopwatchTotal) {
    // Executa push notifications e dynamic links em paralelo
    final stopwatchServices = _initWatch();
    final pushFuture = _initializePushNotifications();
    final dynamicLinksFuture = _initializeDynamicLinks(onDadosCarregados, stopwatchTotal);

    // Não precisa aguardar estes serviços para considerar o carregamento completo
    Future.wait([pushFuture, dynamicLinksFuture]).then((_) {
      stopwatchServices?.stop();
      DebugUtils.debugLog('✅ [LOADING] Serviços auxiliares inicializados em ${stopwatchServices?.elapsedMilliseconds}ms');
    }).catchError((error) {
      stopwatchServices?.stop();
      DebugUtils.debugLog('⚠️ [LOADING] Erro nos serviços auxiliares após ${stopwatchServices?.elapsedMilliseconds}ms: $error');
      // Se houver erro nos serviços auxiliares, ainda considera o carregamento concluído
      stopwatchTotal?.stop();
      DebugUtils.debugLog('✅ [LOADING] Carregamento TOTAL finalizado em ${stopwatchTotal?.elapsedMilliseconds}ms (com erros nos serviços)');
      onDadosCarregados?.call();
      return null;
    });
  }

  Future<void> _initializePushNotifications() async {
    try {
      DebugUtils.debugLog('📱 [LOADING] Verificando permissões de push...');
      final stopwatch = _initWatch();
      final liberado = await PushNotificationService.checkNotificationPermissions();
      if (liberado) {
        PushNotificationService().initialize();
        _mControladorCliente.inscreverTopicosParaPush();
      }
      stopwatch?.stop();
      DebugUtils.debugLog('✅ [LOADING] Push notifications ${liberado ? 'inicializadas' : 'sem permissão'} em ${stopwatch?.elapsedMilliseconds}ms');
    } catch (e) {
      DebugUtils.debugLog('⚠️ [LOADING] Erro nas push notifications: $e');
    }
  }

  Future<void> _initializeDynamicLinks(Function()? onDadosCarregados, Stopwatch? stopwatchTotal) async {
    try {
      DebugUtils.debugLog('🔗 [LOADING] Inicializando dynamic links...');
      final stopwatch = _initWatch();
      _mControladorSplash.initDynamicLinks(linkInvalido: () {
        stopwatch?.stop();
        stopwatchTotal?.stop();
        DebugUtils.debugLog('✅ [LOADING] Dynamic links inicializados em ${stopwatch?.elapsedMilliseconds}ms');
        DebugUtils.debugLog('🎉 [LOADING] Carregamento TOTAL finalizado com SUCESSO em ${stopwatchTotal?.elapsedMilliseconds}ms');
        onDadosCarregados?.call();
      });
    } catch (e) {
      DebugUtils.debugLog('⚠️ [LOADING] Erro nos dynamic links: $e');
      stopwatchTotal?.stop();
      DebugUtils.debugLog('✅ [LOADING] Carregamento finalizado com erro nos dynamic links em ${stopwatchTotal?.elapsedMilliseconds}ms');
      onDadosCarregados?.call();
    }
  }

  Future<ClienteApp?> _buscarERealizarUpdateClienteApp() async {
    return _mServiceCliente.consultarDadosDoClienteAppNovo(mLoadedClienteApp?.documentkey ?? '').then((clienteApp) async {
      mLoadedClienteApp = clienteApp;
      return inserirNoBanco(DataBaseKeys.clienteAppSelecionado.key, clienteApp!.toJson());
    }).then((cApp) {
      return mLoadedClienteApp;
    }).catchError((onError) {
      return mLoadedClienteApp;
    });
  }

  Future<Usuario?> _buscarERealizarUpdateUsuario() async {
    return _mServiceAuth.loginRedeSocialV2(BodyLoginAppPorCod(codUsuario: mLoadedUsuario!.codUsuario!, aluno: isUsuarioAluno)).then((valueUsuario) {
      mLoadedUsuario = valueUsuario;
      return inserirNoBanco(DataBaseKeys.dadosDoUsuario.key, valueUsuario.toJson());
    }).then((onValue) {
      return mLoadedUsuario;
    }).catchError((onError) async {
      if (onError.toString().contains('Não foi possível encontrar o usuário!')) {
        await _mControladorCliente.removerUsuarioDoKeepUser(UsuarioKeep(
          chave: mLoadedChave,
          clienteApp: mLoadedClienteApp?.documentkey,
          codigoUsuario: (mLoadedUsuario?.codUsuario ?? -1).toInt(),
          codigoUsuarioPrincipal: (mLoadedUsuario?.codUsuario ?? -1).toInt(),
        ));
        throw Exception('Usuario não encontrado');
      } else {
        mLoadedUsuario = Usuario.fromJson(onError);
      }
      return mLoadedUsuario;
    });
  }

  Future<ClienteApp?> retryClienteApp(ClienteApp c) {
    return _mServiceCliente.consultarDadosDoClienteAppNovo(c.documentkey ?? '').then((clienteApp) {
      //   clienteApp?.corPadrao = mLoadedClienteApp?.corPadrao ?? F.colorDefault;
      //   clienteApp?.nomeDoApp = mLoadedClienteApp?.nomeDoApp ?? F.nomeApp;
      clienteApp?.urlLogoPrincipal = mLoadedClienteApp?.urlLogoPrincipal;
      _mControladorApp.mClienteAppSelecionado = clienteApp;
    }).then((cApp) {
      return mLoadedClienteApp;
    }).catchError((onError) {
      return mLoadedClienteApp;
    });
  }

  Future<UserAuthFirebase?> _buscarERealizarUpdateUserAuthFirebase() {
    return _mServiceAuth
        .pegaInfoAuthFirebase(BodyInfoAuth(
                chave: mLoadedChave!,
                email: mLoadedUsuario?.username ?? mLoadedUsuario?.email,
                telefone: mLoadedUsuario?.telefone == null ? '' : mLoadedUsuario?.telefone.replaceAll('(', '').replaceAll(')', '').replaceAll(' ', '').replaceAll('+', ''),
                codigoUsuario: mLoadedUsuario?.codUsuario?.toInt(),
                usuarioMovel: mLoadedUsuario?.username)
            .toJson())
        .then((onValue) {
      mLoadedUserAuthFirebase = onValue;
      return inserirNoBanco(DataBaseKeys.dadosFirebase.key, onValue.toJson());
    }).then((onValue) {
      return FirebaseAuth.instance.signInWithEmailAndPassword(email: mLoadedUserAuthFirebase!.email!, password: 'JOJOvsDIO');
    }).then((onValueFirebase) {
      FirebaseCrashlytics.instance.setUserIdentifier(onValueFirebase.user!.uid);
      FirebaseAnalytics.instance.setUserId(id: onValueFirebase.user!.uid);
      FirebaseAnalytics.instance.setUserProperty(name: 'chave_academia_unidade', value: '${mLoadedChave}_${(mLoadedUsuario?.codEmpresa ?? 0)}');
      FirebaseAnalytics.instance.setUserId(id: mLoadedUsuario?.username?.replaceAll('@', '_PACTO_'));
      FirebaseAnalytics.instance.setUserProperty(name: 'CHAVE', value: mLoadedChave);
      FirebaseAnalytics.instance.setUserProperty(name: 'CODEMPRESA', value: '${mLoadedUsuario?.codEmpresa}');
      FirebaseAnalytics.instance.setUserProperty(name: 'ID_APP', value: '${mLoadedClienteApp?.documentkey}');
      if (isUsuarioColaborador) {
        FirebaseAnalytics.instance.setUserProperty(name: 'COD_COLABORADOR', value: '${mLoadedUsuario?.codigoColaborador}');
      } else {
        FirebaseAnalytics.instance.setUserProperty(name: 'MATRICULA', value: '${mLoadedUsuario?.matricula}');
      }
      return mLoadedUserAuthFirebase;
    }).catchError((onError) {
      return mLoadedUserAuthFirebase;
    });
  }

  Future<UrlDiscorver?> _buscarUrls() {
    _mControladorApp.chave = mLoadedChave;
    return _mServiceCliente.descobrirURLTreinoNovo(mLoadedChave!).then((onValue) {
      SharedPreferences.getInstance().then((db) {
        if (_mControladorApp.utilizaDescovery) {
          db.setString(ConfigURL.TREINO.toString(), onValue!.serviceUrls!.treinoUrl!);
          db.setString(ConfigURL.OAMD.toString(), onValue.serviceUrls!.oamdUrl!);
          db.setString(ConfigURL.URLZW.toString(), onValue.serviceUrls!.zwUrl!.replaceAll('/app', ''));
          db.setString(ConfigURL.PERSONAGEM.toString(), onValue.serviceUrls!.personagemMsUrl!);
          db.setString(ConfigURL.TOKENWEB.toString(), onValue.serviceUrls!.autenticacaoUrl!);
          db.setString(ConfigURL.GRADUACAO.toString(), onValue.serviceUrls!.graduacaoMsUrl!);
          db.setString(ConfigURL.ACESSOMS.toString(), onValue.serviceUrls!.acessoSistemaMsUrl!);
        }
      });
      return onValue;
    }).catchError((onError) {
      return null;
    });
  }

  Future<List<ConfiguracaoTreino?>?> _buscarERealizarUpdateConfiguracoesTreinoWeb() {
    List<ConfiguracaoTreino>? mConfiguracoesTreinoWeb = [];
    return _mServiceCliente.consultarConfiguracoesTWeb().then((value) async {
      await inserirNoBanco(value.key!, const JsonCodec().decode(const JsonCodec().encode(value.toJson())), false);
      return value.configuracoes;
    }).catchError((onError) async {
      for (final element in ConfiguracoesTreinoWeb.fromJson((await buscarNoBanco(mLoadedChave!)) as Map<String, dynamic>).configuracoes!) {
        mConfiguracoesTreinoWeb.add(element);
      }
      return mConfiguracoesTreinoWeb;
    });
  }

  Future<DadosDoUsuario?> _buscarERealizarUpdateDadosDoUsuario() {
    if ((mLoadedUsuario?.treinoIndependente ?? false) || isUsuarioColaborador) {
      return Future.delayed(const Duration(seconds: 1), () {
        mLoadedDadosDoUsuario = DadosDoUsuario();
        return mLoadedDadosDoUsuario;
      });
    } else {
      return _mServiceAuth.consultarDadosCliente(mLoadedUsuario!.matricula!).then((value) {
        mLoadedDadosDoUsuario = value;
        inserirNoBanco(DataBaseKeys.dadosDoUsuario.key, value.toJson());
        return mLoadedDadosDoUsuario;
      }).catchError((onError) {
        return mLoadedDadosDoUsuario;
      });
    }
  }

  Future<void> _loadTokenUsuario() async {
    if (isUsuarioColaborador) {
      _mControladorSplash.currentTokenPsec = await _mControladorSplash.gerarTokenUsuarioLogado((x) => {}, chave: mLoadedChave, loadedUser: mLoadedUsuario) ?? '';
    } else if (_mControladorApp.userRelogin != null || mLoadedUsuario != null) {
      _mControladorSplash.currentTokenPsec =
          await _mControladorSplash.gerarTokenUsuarioLogado((x) => {}, relogin: _mControladorApp.userRelogin != null, chave: mLoadedChave, loadedUser: mLoadedUsuario) ?? '';
    }
    _mControladorSplash.currentToken = await _mControladorSplash.pegaTokenAgora((x) => {}) ?? '';
  }

  void _loadDadosBaseApp(SharedPreferences prefs) {
    if (mLoadedClienteApp != null) {
      _mControladorApp.defaultColor = HexColor.fromHex(mLoadedClienteApp!.corPadrao);
    }
    var tema = prefs.getString('TEMA');
    if (tema != null) {
      _mControladorApp.themeMode = tema == 'dark'
          ? ThemeMode.dark
          : tema == 'ligth'
              ? ThemeMode.light
              : ThemeMode.system;
      _mControladorApp.themeModeSystem = ThemeMode.system == _mControladorApp.themeMode;
    } else {
      _mControladorApp.themeModeSystem = true;
    }
    _mControladorApp.pularSenhaLogin = prefs.getBool('SkipLogin') ?? false;
    modoHomologacao = prefs.getBool('showHtpLog') ?? false;
    _mControladorApp.utilizaDescovery = prefs.getBool('utilizaDiscovery') ?? true;
    _mControladorApp.setThemeModeSystem();
  }


}