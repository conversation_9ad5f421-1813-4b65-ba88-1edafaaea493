// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorFeed.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorFeed on _ControladorFeedBase, Store {
  late final _$mItensFeedAtom =
      Atom(name: '_ControladorFeedBase.mItensFeed', context: context);

  @override
  ObservableList<PostagemFeed> get mItensFeed {
    _$mItensFeedAtom.reportRead();
    return super.mItensFeed;
  }

  @override
  set mItensFeed(ObservableList<PostagemFeed> value) {
    _$mItensFeedAtom.reportWrite(value, super.mItensFeed, () {
      super.mItensFeed = value;
    });
  }

  late final _$mItensMeusPostAtom =
      Atom(name: '_ControladorFeedBase.mItensMeusPost', context: context);

  @override
  ObservableList<PostagemFeed> get mItensMeusPost {
    _$mItensMeusPostAtom.reportRead();
    return super.mItensMeusPost;
  }

  @override
  set mItensMeusPost(ObservableList<PostagemFeed> value) {
    _$mItensMeusPostAtom.reportWrite(value, super.mItensMeusPost, () {
      super.mItensMeusPost = value;
    });
  }

  late final _$mComentPostAtom =
      Atom(name: '_ControladorFeedBase.mComentPost', context: context);

  @override
  ObservableList<ComentarioPostagem> get mComentPost {
    _$mComentPostAtom.reportRead();
    return super.mComentPost;
  }

  @override
  set mComentPost(ObservableList<ComentarioPostagem> value) {
    _$mComentPostAtom.reportWrite(value, super.mComentPost, () {
      super.mComentPost = value;
    });
  }

  late final _$comentarioPreenchidoAtom =
      Atom(name: '_ControladorFeedBase.comentarioPreenchido', context: context);

  @override
  String get comentarioPreenchido {
    _$comentarioPreenchidoAtom.reportRead();
    return super.comentarioPreenchido;
  }

  @override
  set comentarioPreenchido(String value) {
    _$comentarioPreenchidoAtom.reportWrite(value, super.comentarioPreenchido,
        () {
      super.comentarioPreenchido = value;
    });
  }

  @override
  String toString() {
    return '''
mItensFeed: ${mItensFeed},
mItensMeusPost: ${mItensMeusPost},
mComentPost: ${mComentPost},
comentarioPreenchido: ${comentarioPreenchido}
    ''';
  }
}
