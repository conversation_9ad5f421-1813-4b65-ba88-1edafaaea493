// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorPushIa.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorPushIA on _ControladorPushIABase, Store {
  late final _$statusConsultaPreferenciasAtom = Atom(
      name: '_ControladorPushIABase.statusConsultaPreferencias',
      context: context);

  @override
  ServiceStatus get statusConsultaPreferencias {
    _$statusConsultaPreferenciasAtom.reportRead();
    return super.statusConsultaPreferencias;
  }

  @override
  set statusConsultaPreferencias(ServiceStatus value) {
    _$statusConsultaPreferenciasAtom
        .reportWrite(value, super.statusConsultaPreferencias, () {
      super.statusConsultaPreferencias = value;
    });
  }

  @override
  String toString() {
    return '''
statusConsultaPreferencias: ${statusConsultaPreferencias}
    ''';
  }
}
