// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorVendasOnline.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorVendasOnline on _ControladorVendasOnlineBase, Store {
  late final _$mListaUnidadesAtom = Atom(
      name: '_ControladorVendasOnlineBase.mListaUnidades', context: context);

  @override
  ObservableList<UnidadeVendaOnline> get mListaUnidades {
    _$mListaUnidadesAtom.reportRead();
    return super.mListaUnidades;
  }

  @override
  set mListaUnidades(ObservableList<UnidadeVendaOnline> value) {
    _$mListaUnidadesAtom.reportWrite(value, super.mListaUnidades, () {
      super.mListaUnidades = value;
    });
  }

  late final _$naoEncontrouPeloTermoAtom = Atom(
      name: '_ControladorVendasOnlineBase.naoEncontrouPeloTermo',
      context: context);

  @override
  bool get naoEncontrouPeloTermo {
    _$naoEncontrouPeloTermoAtom.reportRead();
    return super.naoEncontrouPeloTermo;
  }

  @override
  set naoEncontrouPeloTermo(bool value) {
    _$naoEncontrouPeloTermoAtom.reportWrite(value, super.naoEncontrouPeloTermo,
        () {
      super.naoEncontrouPeloTermo = value;
    });
  }

  late final _$mListaUnidadesFiltroAtom = Atom(
      name: '_ControladorVendasOnlineBase.mListaUnidadesFiltro',
      context: context);

  @override
  ObservableList<UnidadeVendaOnline> get mListaUnidadesFiltro {
    _$mListaUnidadesFiltroAtom.reportRead();
    return super.mListaUnidadesFiltro;
  }

  @override
  set mListaUnidadesFiltro(ObservableList<UnidadeVendaOnline> value) {
    _$mListaUnidadesFiltroAtom.reportWrite(value, super.mListaUnidadesFiltro,
        () {
      super.mListaUnidadesFiltro = value;
    });
  }

  late final _$mPlanosDaUnidadeAtom = Atom(
      name: '_ControladorVendasOnlineBase.mPlanosDaUnidade', context: context);

  @override
  ObservableList<PlanosUnidadeVendaOnline> get mPlanosDaUnidade {
    _$mPlanosDaUnidadeAtom.reportRead();
    return super.mPlanosDaUnidade;
  }

  @override
  set mPlanosDaUnidade(ObservableList<PlanosUnidadeVendaOnline> value) {
    _$mPlanosDaUnidadeAtom.reportWrite(value, super.mPlanosDaUnidade, () {
      super.mPlanosDaUnidade = value;
    });
  }

  late final _$unidadeSelecionadaAtom = Atom(
      name: '_ControladorVendasOnlineBase.unidadeSelecionada',
      context: context);

  @override
  UnidadeVendaOnline? get unidadeSelecionada {
    _$unidadeSelecionadaAtom.reportRead();
    return super.unidadeSelecionada;
  }

  @override
  set unidadeSelecionada(UnidadeVendaOnline? value) {
    _$unidadeSelecionadaAtom.reportWrite(value, super.unidadeSelecionada, () {
      super.unidadeSelecionada = value;
    });
  }

  late final _$planoSeleciondoAtom = Atom(
      name: '_ControladorVendasOnlineBase.planoSeleciondo', context: context);

  @override
  PlanosUnidadeVendaOnline? get planoSeleciondo {
    _$planoSeleciondoAtom.reportRead();
    return super.planoSeleciondo;
  }

  @override
  set planoSeleciondo(PlanosUnidadeVendaOnline? value) {
    _$planoSeleciondoAtom.reportWrite(value, super.planoSeleciondo, () {
      super.planoSeleciondo = value;
    });
  }

  late final _$mDadosDoUsuarioAtom = Atom(
      name: '_ControladorVendasOnlineBase.mDadosDoUsuario', context: context);

  @override
  DadosUsuarioVenda get mDadosDoUsuario {
    _$mDadosDoUsuarioAtom.reportRead();
    return super.mDadosDoUsuario;
  }

  @override
  set mDadosDoUsuario(DadosUsuarioVenda value) {
    _$mDadosDoUsuarioAtom.reportWrite(value, super.mDadosDoUsuario, () {
      super.mDadosDoUsuario = value;
    });
  }

  @override
  String toString() {
    return '''
mListaUnidades: ${mListaUnidades},
naoEncontrouPeloTermo: ${naoEncontrouPeloTermo},
mListaUnidadesFiltro: ${mListaUnidadesFiltro},
mPlanosDaUnidade: ${mPlanosDaUnidade},
unidadeSelecionada: ${unidadeSelecionada},
planoSeleciondo: ${planoSeleciondo},
mDadosDoUsuario: ${mDadosDoUsuario}
    ''';
  }
}
