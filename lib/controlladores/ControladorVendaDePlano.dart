import 'dart:convert';

import 'package:app_treino/ServiceProvider/ServiceVendaDePlano.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorContratoUsuario.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/contrato/ContratoUsuario.dart';
import 'package:app_treino/model/contrato/ParcelaContrato.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/doClienteApp/DadosDoUsuario.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/model/vendaplanos/BoletoResponse.dart';
import 'package:app_treino/model/vendaplanos/ConfiguracaoVendasUnidade.dart';
import 'package:app_treino/model/vendaplanos/PagamentoParcelaIserir.dart';
import 'package:app_treino/model/vendaplanos/PagamentoParcelasDto.dart';
import 'package:app_treino/model/vendaplanos/ParcelasVencidas.dart';
import 'package:app_treino/model/vendaplanos/PlanoVendidoUnidade.dart';
import 'package:app_treino/model/vendaplanos/VendaIserir.dart';
import 'package:awesome_card/awesome_card.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorVendaDePlano.g.dart';

class ControladorVendaDePlano = _ControladorVendaDePlanoBase with _$ControladorVendaDePlano;

abstract class _ControladorVendaDePlanoBase with Store {
  final ServiceVendaDePlano _serviceVendas = GetIt.I.get();

  final ControladorCliente _mCliente = GetIt.I.get();

  ControladorApp mApp = GetIt.I.get();

  List<PlanoVendidoUnidade> mPlanosUnidade = [];

  List<PlanoVendidoUnidade> mPlanosUnidadeJaTem = [];

  DadosDoUsuario? mDadosDoUsuario = DadosDoUsuario.buildEmpty();


  final _controladorContratoUsuario = GetIt.I.get<ControladorContratoUsuario>();

  bool veioDaHome = false;

  String? cupomValidado;

  late ContratoUsuario mContratoSimulado;
  PlanoVendidoUnidade? planoVer;
  @computed
  bool get isDadosPessoaisValido {
    return (vendaIserir.nome?.isNotEmpty ?? false) &&
        UtilDataHora.isValidDate(vendaIserir.dataNascimento ?? '') &&
        (vendaIserir.email?.contains('@') ?? false) &&
        (vendaIserir.telefone?.isNotEmpty ?? false) &&
        UtilitarioApp.isCPFValid((vendaIserir.cpf ?? '').replaceAll('.', '').replaceAll('-', ''));
  }

  @computed
  bool get isCPFCartaoValido => (vendaIserir.cpftitularcard?.isNotEmpty ?? false) && UtilitarioApp.isCPFValid((vendaIserir.cpftitularcard ?? '').replaceAll('.', '').replaceAll('-', ''));

  @computed
  bool get isCPFDadosPessoaisValido => (vendaIserir.cpf?.isNotEmpty ?? false) && UtilitarioApp.isCPFValid((vendaIserir.cpf ?? '').replaceAll('.', '').replaceAll('-', ''));

  @computed
  bool get isValidadeCartaoValida => (vendaIserir.validade?.isNotEmpty ?? false) && vendaIserir.isValidadeValida;

  @computed
  bool get isDAdosCartaoValido {
    return (vendaIserir.numeroCartao?.isNotEmpty ?? false) &&
        (vendaIserir.validade?.isNotEmpty ?? false) &&
        vendaIserir.isValidadeValida &&
        (vendaIserir.nomeCartao?.isNotEmpty ?? false) &&
        (vendaIserir.cvv?.isNotEmpty ?? false) &&
        UtilitarioApp.isCPFValid((vendaIserir.cpftitularcard ?? '').replaceAll('.', '').replaceAll('-', ''));
  }

  @computed
  bool get isVencimentoFaturaValido {
    return (vendaIserir.vencimentoFatura != null) || (vendaIserir.nrVezesDividir?.isNotEmpty ?? false);
  }

  @observable
  VendaNova vendaIserir = VendaNova();

  @observable
  ServiceStatus statusConsultaDePlanos = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusSimulacaoPlano = ServiceStatus.Waiting;

  @observable
  ServiceStatus statusConsultaContrato = ServiceStatus.Waiting;

  String? htmlContrato;


  void validarCupom(String cupom, String nomePlano, String codEmpresa, {Function()? carregando, Function()? sucesso, Function(String)? falha}){
    carregando!.call();
    final body = '${nomePlano};empresa=${codEmpresa}';
    _serviceVendas.validarCupomDesconto(cupom, body).then((value) {
      cupomValidado = value;
      sucesso!.call();
    }).catchError((onError) {
      falha!.call(onError.message);
    });
  }

  void consultarPlanosDaUnidade({Function()? carregando, Function()? sucesso, Function(String?)? falha, bool force = true}) {
    carregando?.call();
    statusConsultaDePlanos = ServiceStatus.Waiting;
    if (force || mPlanosUnidade.where((p) => p.codUnidade == _mCliente.mUsuarioLogado!.codEmpresa).isEmpty) {
      mPlanosUnidade.clear();
      _serviceVendas.obterTodosPlanosDaUnidade(_mCliente.mUsuarioLogado!.codEmpresa!).then((value) {
        for (final element in value) {
          element.codUnidade = _mCliente.mUsuarioLogado!.codEmpresa;
        }
        mPlanosUnidade.addAll(value);
        _mCliente.consultarDadosDoUsuario(
          sucesso: () {
            mDadosDoUsuario = _mCliente.mDadosDoUsuario;
            vendaIserir.nome = mDadosDoUsuario!.nome;
            vendaIserir.dataNascimento = mDadosDoUsuario!.dataNascimento;
            vendaIserir.cpf = mDadosDoUsuario!.cpf;
            vendaIserir.cpftitularcard = mDadosDoUsuario!.cpf;
            vendaIserir.telefone = mDadosDoUsuario!.telCelular;
            vendaIserir.email = mDadosDoUsuario!.email;
            sucesso?.call();
            statusConsultaDePlanos = mPlanosUnidade.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
          },
          falha: (mensage) {
            sucesso?.call();
            statusConsultaDePlanos = mPlanosUnidade.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
          },
        );
      }).catchError((erro) {
        falha?.call(erro.message);
        statusConsultaDePlanos = ServiceStatus.Error;
      });
    } else {
      sucesso?.call();
      statusConsultaDePlanos = mPlanosUnidade.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    }
  }

  void obterContratoDoplano({PlanoVendidoUnidade? plano, Function()? carregando, Function()? sucesso, Function(String)? falha, bool force = true}) {
    carregando?.call();
    statusConsultaContrato = ServiceStatus.Waiting;
    _serviceVendas.obterInformacaoContratual(_mCliente.mUsuarioLogado!.codEmpresa!, plano!.codigo!).then((value) {
      htmlContrato = value;
      sucesso?.call();
      statusConsultaContrato = ServiceStatus.Done;
    }).catchError((onError) {
      falha?.call('Não foi possível consultar o contrato no momento');
      statusConsultaContrato = ServiceStatus.Waiting;
    });
  }

  void simularPlano({Function()? carregando, Function()? sucesso, Function(String)? falha, bool eRenovacao = false}) {
    carregando?.call();
    statusSimulacaoPlano = ServiceStatus.Waiting;
    Future<ContratoUsuario> mCall;
    if (!eRenovacao) {
      mCall = _serviceVendas.simularVenda(vendaIserir.toJsonBody(), vendaIserir.plano!, vendaIserir.unidade!);
    } else {
      mCall = _serviceVendas.renovarContrato(_mCliente.mLogadoSituacao!.codigoContrato!, true, _mCliente.mUsuarioLogado!.codigoCliente, 4);
    }
    mCall.then((value) {
      mContratoSimulado = value;
      statusSimulacaoPlano = ServiceStatus.Done;
    }).catchError((onError) {
      statusSimulacaoPlano = ServiceStatus.Error;
    });
  }

  void realizarCompra({Function()? carregando, Function()? sucesso, Function(String)? falha, bool force = true}) {
    carregando?.call();
    _serviceVendas.gerarTokenVenda(vendaIserir.toJsonBody()).then((value) {
      return _serviceVendas.cadastrarUmaNovaVenda(vendaIserir.toJsonBody(), value);
    }).then((_) {
      _controladorContratoUsuario.consultarOsContratosDoUsuario(
          sucesso: sucesso,
          falha: (falha) {
            _mCliente.validarSituacaoAluno(sucesso: sucesso, ignorarConsultaContrato: false);
          });
    }).catchError((onError) {
      falha?.call(onError?.message?.toString() ?? '');
    });
  }

  @observable
  ServiceStatus statusConsultarParcelasAtrasadas = ServiceStatus.Waiting;
  @observable
  ServiceStatus statusPagamentoParcelasAtrasadas = ServiceStatus.Waiting;
  @observable
  ClienteParcelas? alunoParcelas;
  @observable 
  PagamentoParcelasNova mPagarParcelas = PagamentoParcelasNova();
  @observable
  CobrarParcelaPix mCobrarParcelasPix = CobrarParcelaPix();
  @observable
  Pix dadosPixPagamento = Pix();
  @observable
  ServiceStatus statusConsultarParcelasAtrasadasPix = ServiceStatus.Waiting;
  @observable
  ConfiguracaoVendasUnidade configVendasOnline = ConfiguracaoVendasUnidade();

  String? retornoCobranca;

  @computed
  bool get isCPFCartaoValidoPagamentoParcela => (mPagarParcelas.cpftitularcard?.isNotEmpty ?? false) && UtilitarioApp.isCPFValid((mPagarParcelas.cpftitularcard ?? '').replaceAll('.', '').replaceAll('-', ''));

  @computed
  bool get isValidadeCartaoValidaPagamentoParcela => (mPagarParcelas.validade?.isNotEmpty ?? false) && mPagarParcelas.isValidadeValida;

  bool get isDAdosCartaoValidoPagamentoParcela {
    return (mPagarParcelas.numeroCartao?.isNotEmpty ?? false) &&
      (mPagarParcelas.validade?.isNotEmpty ?? false) &&
      mPagarParcelas.isValidadeValida &&
      (mPagarParcelas.nomeCartao?.isNotEmpty ?? false) &&
      (mPagarParcelas.cvv?.isNotEmpty ?? false) &&
      UtilitarioApp.isCPFValid((mPagarParcelas.cpftitularcard ?? '').replaceAll('.', '').replaceAll('-', ''));
  }

   void consultarParcelasAtrasadas({Function()? carregando, Function()? sucesso, Function(String)? falha}){
    carregando!.call();
    statusConsultarParcelasAtrasadas = ServiceStatus.Waiting;
    _serviceVendas.consultarClienteParcelas(num.tryParse(_mCliente.mUsuarioLogado!.matricula!)!).then((value) {
      alunoParcelas = value;
      statusConsultarParcelasAtrasadas = alunoParcelas != null ? ServiceStatus.Done : ServiceStatus.Empty;
      sucesso?.call();
    }).catchError((onError){
      falha!.call(onError.toString());
      statusConsultarParcelasAtrasadas = ServiceStatus.Error;
    });
  }

  void pagarParcelas({Function()? carregando, Function()? sucesso, Function(String)? falha}){
    var matricula = num.tryParse(_mCliente.mUsuarioLogado!.matricula!);
    carregando!.call();
    statusPagamentoParcelasAtrasadas = ServiceStatus.Waiting;
    mPagarParcelas.unidade = _mCliente.mUsuarioLogado!.codEmpresa;
    mPagarParcelas.cobrarParcelasEmAberto = true;
    mPagarParcelas.origemCobranca = 12;
    _serviceVendas.cobrarParcelas(matricula!, mPagarParcelas.toJsonBody()).then((value) {
      retornoCobranca = value;
      sucesso!.call();
      statusPagamentoParcelasAtrasadas = retornoCobranca != null ? ServiceStatus.Done : ServiceStatus.Empty;
    }).catchError((onError){
      falha!.call(onError);
      statusPagamentoParcelasAtrasadas = ServiceStatus.Error;
    });
  }

  void consultarConfigVendasOnline({Function()? carregando, Function()? sucesso, Function(String)? falha}){
    carregando?.call();
    _serviceVendas.obterConfiguracaoVendasOnline(_mCliente.mUsuarioLogado!.codEmpresa ?? 1).then((value) {
      configVendasOnline = value;
      sucesso?.call();
    }).catchError((onError){
      falha?.call(onError);
    });
  }

  @observable
  ConfigLinkPagamento configLinkPagamento = ConfigLinkPagamento();

  void consultarConfigLinkPagamente({Function()? carregando, Function()? sucesso, Function(String)? falha}){
    carregando?.call();
    _serviceVendas.obterConfiguracaoLinkPagamento(_mCliente.mUsuarioLogado!.codEmpresa ?? 1).then((value) {
      configLinkPagamento = value;
      sucesso?.call();
    }).catchError((onError){
      falha?.call(onError);
    });
  }



  bool get isPagamentoCartaoHabilitado => (mApp.getConfiguracaoApp(ModuloApp.HABILITAR_PAGAMENTO_CARTAO_PARCELAS).habilitado ?? false) && (configLinkPagamento.convenioCartaoDeCredito ?? '').isNotEmpty;
  bool get isPagamentoBoletoHabilitado => (mApp.getConfiguracaoApp(ModuloApp.MODULO_PAGAMENTO_COM_BOLETO).habilitado ?? false) && (configLinkPagamento.convenioBoleto ?? '').isNotEmpty;
  bool get isPagamentoPixHabilitado => (mApp.getConfiguracaoApp(ModuloApp.HABILITAR_PAGAMENTO_PIX_PARCELAS).habilitado ?? false) && (configLinkPagamento.convenioPix ?? '').isNotEmpty;
  bool get isHabilitarPagamentoParcelas => isPagamentoCartaoHabilitado || isPagamentoBoletoHabilitado || isPagamentoPixHabilitado;


  void cobrarParcelasComPix({Function()? carregando, Function()? sucesso, Function(String)? falha}){
    var matricula = num.tryParse(_mCliente.mUsuarioLogado!.matricula!);
    carregando!.call();
    statusConsultarParcelasAtrasadasPix = ServiceStatus.Waiting;
    mCobrarParcelasPix.cobrarParcelasEmAberto = true;
    mCobrarParcelasPix.origemCobranca = 12;
    mCobrarParcelasPix.unidade = _mCliente.mUsuarioLogado!.codEmpresa ?? 1;
    _serviceVendas.cobrarParcelasComPix(matricula!, mCobrarParcelasPix.toJsonBody()).then((value) {
      dadosPixPagamento = value;
      sucesso!.call();
      statusConsultarParcelasAtrasadasPix = ServiceStatus.Done;
    }).catchError((onError){
      falha?.call(onError);
      statusConsultarParcelasAtrasadasPix = ServiceStatus.Error;
    });
  }

  @observable
  ParcelasPagamentoDto mParcelasPagamento = ParcelasPagamentoDto();
  @observable
  String parcelasSelecionadas = '';
  @observable
  List<num>  parcelasSelecionadasParaBoleto = [];
  @observable
  List<ParcelaContrato> parcelasContratoSelecionados = [];

  bool get isCPFCartaoValidoPagamentoParcelaFuturas => UtilitarioApp.isCPFValid((mParcelasPagamento.cpftitularcard ?? '').replaceAll('.', '').replaceAll('-', ''));

  bool validarDataCartao(String data){
    try {
      var ano = num.parse(mParcelasPagamento.validade!.split('/')[1]);
      var mes = num.parse(mParcelasPagamento.validade!.split('/')[0]);
      return mes <= 12 && ano > num.parse(DateTime.now().year.toString()) - 1;
    } catch (e) {
      return false;
    }
  }

  /// Verifica se os dados do cartão são válidos para o pagamento de parcelas futuras.
  ///
  /// Retorna true se todos os seguintes critérios forem atendidos:
  /// - O número do cartão não é vazio.
  /// - A validade do cartão não é vazia.
  /// - A data de validade do cartão é válida.
  /// - O nome do cartão não é vazio.
  /// - O CVV do cartão não é nulo.
  /// - O CPF do titular do cartão é válido.
  ///
  /// Caso contrário, retorna false.
  bool get isDAdosCartaoValidoPagamentoParcelaFuturas {
    return (mParcelasPagamento.numeroCartao?.isNotEmpty ?? false) &&
      (mParcelasPagamento.validade?.isNotEmpty ?? false) &&
      validarDataCartao(mParcelasPagamento.validade ?? '') &&
      (mParcelasPagamento.nomeCartao?.isNotEmpty ?? false) &&
      (mParcelasPagamento.cvv != null) &&
      UtilitarioApp.isCPFValid((mParcelasPagamento.cpftitularcard ?? '').replaceAll('.', '').replaceAll('-', ''));
  }

  /// Método responsável por cobrar as parcelas selecionadas utilizando o método de pagamento Pix.
  /// Retorna o QRCode para efetuar o pagamento.
  ///
  /// Parâmetros:
  /// - [carregando]: Função de callback que será chamada antes de iniciar o processo de cobrança.
  /// - [sucesso]: Função de callback que será chamada em caso de sucesso na cobrança.
  /// - [falha]: Função de callback que será chamada em caso de falha na cobrança, recebendo como parâmetro a mensagem de erro.
  void cobrarParcelasFuturasPix({Function()? carregando, Function()? sucesso, Function(String)? falha}) {
    var matricula = num.tryParse(_mCliente.mUsuarioLogado!.matricula!);
    statusConsultarParcelasAtrasadasPix = ServiceStatus.Waiting;
    carregando!.call();
    _serviceVendas.cobrarParcelasSelecionadasComPix(matricula!, _mCliente.mUsuarioLogado!.codEmpresa!, 12, parcelasSelecionadas, true).then((onValue) {
      dadosPixPagamento = onValue;
      statusConsultarParcelasAtrasadasPix = ServiceStatus.Done;
      sucesso!.call();
    }).catchError((onError) {
      falha!.call(onError);
      statusConsultarParcelasAtrasadasPix = ServiceStatus.Error;
    });
  }

  /// Cobrar parcelas futuras do cartão de crédito.
  ///
  /// Este método é responsável por realizar a cobrança das parcelas futuras de um cartão de crédito.
  /// Ele recebe três funções opcionais como parâmetros: [carregando], [sucesso] e [falha].
  /// A função [carregando] é chamada antes de iniciar a cobrança das parcelas.
  /// A função [sucesso] é chamada quando a cobrança é realizada com sucesso.
  /// A função [falha] é chamada caso ocorra algum erro durante a cobrança, e recebe uma mensagem de erro como parâmetro.
  void cobrarParcelasFuturasCartao({Function()? carregando, Function()? sucesso, Function(String)? falha}) {
    var matricula = num.tryParse(_mCliente.mUsuarioLogado!.matricula!);
    carregando!.call();
    mParcelasPagamento.cobrarParcelasEmAberto = true;
    mParcelasPagamento.origemCobranca = 12;
    mParcelasPagamento.unidade = _mCliente.mUsuarioLogado?.codEmpresa ?? 1;
    mParcelasPagamento.parcelasSelecionadas = parcelasSelecionadas.replaceAll('_', ';');
    _serviceVendas.cobrarParcelasSelecionadasComCartao(matricula!, mParcelasPagamento).then((onValue) {
      retornoCobranca = onValue;
      sucesso!.call();
    }).catchError((onError) {
      falha!.call(onError);
    });
  }

  int get indexDefaultTabController {
    int index = 0;
    if (isPagamentoCartaoHabilitado) index++;
    if (isPagamentoPixHabilitado) index++;
    if (isPagamentoBoletoHabilitado) index++;
    return index;
  }

  @observable
  BoletoResponse? boletoResponse;
  @observable
  bool boletoGerado = false;

  void cobrarParcelasFuturasBoleto({Function()? carregando, Function()? sucesso, Function(String)? falha}) {
    carregando!.call();
    var matricula = num.tryParse(_mCliente.mUsuarioLogado!.matricula!);
    mParcelasPagamento.cobrarParcelasEmAberto = true;
    mParcelasPagamento.origemCobranca = 12;
    mParcelasPagamento.unidade = _mCliente.mUsuarioLogado?.codEmpresa ?? 1;
    mParcelasPagamento.parcelasSelecionadas = parcelasSelecionadas.replaceAll('_', ';');
    mParcelasPagamento.todasEmAberto = false;
    mParcelasPagamento.plano = 0;
    mParcelasPagamento.vencimentoFatura = 0;
    boletoResponse = null;
    _serviceVendas.cobrarParcelasSelecionadasComBoleto(matricula ?? 0, mParcelasPagamento).then((value) {
      Map<String, dynamic> jsonBoletoGerado = json.decode(value);
      boletoResponse = BoletoResponse.fromJson(jsonBoletoGerado);
      if (boletoResponse?.sucesso ?? false) {
        boletoGerado = true;
        mParcelasPagamento = ParcelasPagamentoDto();
        parcelasSelecionadas = '';
        sucesso?.call();
      } else {
        falha?.call(value.toString());
      }
    }).catchError((onError) {
      falha?.call(onError);
    });
  }

  void cobrarParcelasVencidasBoleto({Function()? carregando, Function()? sucesso, Function(String)? falha}) {
    carregando!.call();
    var matricula = num.tryParse(_mCliente.mUsuarioLogado!.matricula!);
    mParcelasPagamento.cobrarParcelasEmAberto = true;
    mParcelasPagamento.origemCobranca = 12;
    mParcelasPagamento.unidade = _mCliente.mUsuarioLogado?.codEmpresa ?? 1;
    mParcelasPagamento.todasEmAberto = true;
    boletoResponse = null;
    _serviceVendas.cobrarParcelasSelecionadasComBoleto(matricula ?? 0, mParcelasPagamento).then((value) {
      Map<String, dynamic> jsonBoletoGerado = json.decode(value);
      boletoResponse = BoletoResponse.fromJson(jsonBoletoGerado);
      if (boletoResponse?.sucesso ?? false) {
        boletoGerado = true;
        mParcelasPagamento = ParcelasPagamentoDto();
        sucesso?.call();
      } else {
        falha?.call(value.toString());
      }
    }).catchError((onError) {
      falha?.call(onError);
    });
  }

  @observable
  bool concordoComOTermos = false;

  bool termoConcorda(bool termo){
    return concordoComOTermos = termo;
  }


  Widget getCardIcon({CardType? cardType, String? cardNumber}) {
  switch (cardType ?? getCardType(cardNumber!)) {
    case CardType.americanExpress:
      return Image.asset(
        'images/card_provider/american_express.png',
        width: 15,
        height: 10,
        package: 'awesome_card',
      );
    case CardType.dinersClub:
      return Image.asset(
        'images/card_provider/diners_club.png',
        width: 10,
        height: 10,
        package: 'awesome_card',
      );
    case CardType.discover:
      return Image.asset(
        'images/card_provider/discover.png',
        width: 15,
        height: 10,
        package: 'awesome_card',
      );
    case CardType.jcb:
      return Image.asset(
        'images/card_provider/jcb.png',
        width: 10,
        height: 10,
        package: 'awesome_card',
      );
    case CardType.masterCard:
      return Image.asset(
        'images/card_provider/master_card.png',
        width: 15,
        height: 10,
        package: 'awesome_card',
      );
    case CardType.maestro:
      return Image.asset(
        'images/card_provider/maestro.png',
        width: 15,
        height: 10,
        package: 'awesome_card',
      );
    case CardType.rupay:
      return Image.asset(
        'assets/images/icones/hipercard.png',
        width: 30,
        height: 20,
        fit: BoxFit.cover,
        //package: 'awesome_card',
      );
    case CardType.visa:
      return Image.asset(
        'assets/images/icones/visa.png',
        width: 30,
        height: 20,
        fit: BoxFit.cover,
        //package: 'awesome_card',
      );
    case CardType.elo:
      return Image.asset(
        'images/card_provider/elo.png',
        width: 15,
        height: 10,
        package: 'awesome_card',
      );
    default:
      return Icon(TreinoIcon.credit_card, size: 24, color: DSLib.theme == ThemeMode.dark ? const Color(0xFFB3B3B3) : const Color(0xFF555555));
    }
  }

  void limparDados() {
    mPagarParcelas = PagamentoParcelasNova();
    vendaIserir = VendaNova();
    mDadosDoUsuario = DadosDoUsuario.buildEmpty();
    parcelasContratoSelecionados.clear();
    parcelasSelecionadas = '';  
    mParcelasPagamento = ParcelasPagamentoDto();
  }
}
