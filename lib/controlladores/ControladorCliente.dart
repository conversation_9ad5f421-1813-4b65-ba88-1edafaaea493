// ignore_for_file: sdk_version_since

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:app_treino/ServiceProvider/ConexaoFracaInterceptor.dart';
import 'package:app_treino/ServiceProvider/ContratoUsuarioService.dart';
import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/ServiceProvider/TokenDioInterceptor.dart';
import 'package:app_treino/ServiceProvider/authServices/ServiceAuth.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorContratoUsuario.dart';
import 'package:app_treino/controlladores/ControladorDBExtend.dart';
import 'package:app_treino/controlladores/ControladorEventBus.dart';
import 'package:app_treino/controlladores/ControladorLoginPersonalizado.dart';
import 'package:app_treino/controlladores/ControladorNotificacoesCrm.dart';
import 'package:app_treino/controlladores/ControladorNovoLogin.dart';
import 'package:app_treino/controlladores/ControladorPushIa.dart';
import 'package:app_treino/controlladores/ControladorSplash.dart';
import 'package:app_treino/credit_card_type_detector.dart';
import 'package:app_treino/fabricaGetIt.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/UserDataKeys.dart';
import 'package:app_treino/model/bodyscrypto.dart';
import 'package:app_treino/model/contrato/ContratoUsuario.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/doClienteApp/DadosDoUsuario.dart';
import 'package:app_treino/model/doClienteApp/UsuarioAppTelefone.dart';
import 'package:app_treino/model/doClienteApp/UsuarioKeep.dart';
import 'package:app_treino/model/doClienteApp/ValidarCheckInTotalPass.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/BodyInfoAuth.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/DadosUsuario.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/PontuacaoAluno.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/SituacaoCliente.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/UserAuthFirebase.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/Usuario.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/registrar_uso_app.dart';
import 'package:app_treino/model/geral/UsuarioFireBaseManter.dart';
import 'package:app_treino/model/historicoPresenca/historico_presenca.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:app_treino/screens/_treino6/novo_chat/chat_controller.dart';
import 'package:diacritic/diacritic.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get_ip_address/get_ip_address.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sembast/sembast.dart';
import 'package:shared_preferences/shared_preferences.dart';

// // import 'package:smartlook/smartlook.dart';
part 'ControladorCliente.g.dart';

class ControladorCliente = _ControladorClienteBase with _$ControladorCliente;

abstract class _ControladorClienteBase extends UtilDataBase with Store {
  final _mServiceAuth = GetIt.I.get<ServiceAuth>();
  final _mServiceUsuario = GetIt.I.get<ContratoUsuarioService>();
  final _controladorClienteApp = GetIt.I.get<ControladorApp>();
  final _controladorPerfil = GetIt.I.get<ControladorSplash>();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  String? codigoDDI = '';
  bool consultouPremium = false;

  bool veioDeTokenAnterior = false, bloquearNavegacao = false, exibirCompraDePlano = false, permitirRenovarPlano = false, exibirAlertaDeRenovacao = false, exibirAlertaPlanoVencido = false;
  ContratoUsuario? contratoUsuarioVencido;
  late String _verificationId;
  SituacaoCliente? mLogadoSituacao;
  @observable
  Usuario? mUsuarioLogado;
  @observable
  UserAuthFirebase? mUsuarioAuth;
  @observable
  UsuarioAppTelefone? usuarioLogar;
  bool consultarClienteappN2b = true;
  bool get isUsuarioAluno => !isUsuarioColaborador;
  DadosDoUsuario? mDadosDoUsuario;

  TipoDePremium tipoDePremiumContratado = TipoDePremium.NENHUM;

  String get enderecoComDadosDoUsuario {
    return "${(mDadosDoUsuario!.cep ?? '')}, ${(mDadosDoUsuario!.bairro ?? '')}, ${(mDadosDoUsuario!.endereco ?? '')}, ${(mDadosDoUsuario!.complemento ?? '')}   ";
  }

  @observable
  ServiceStatus mConsultaDadosUsuario = ServiceStatus.Waiting;

  @observable
  ServiceStatus mStatusConsultaConveniosPagamento = ServiceStatus.Waiting;

  @observable
  ServiceStatus mStatusHisoricoPresenca = ServiceStatus.Waiting;

  @observable
  HistoricoPresenca? mHistoricoPresenca;

  @observable
  ServiceStatus mStatusPontuacaoAluno = ServiceStatus.Waiting;

  @observable
  ServiceStatus mStatusBrindes = ServiceStatus.Waiting;

  @observable
  List<Brindes>? mBrindes;

  @observable
  bool contratoTermos = false;

  @observable
  bool contratoAssinatura = false;

  @observable
  bool acessoUnidadeFoto = false;

  num? mPontuacaoAluno = 0;

  BandeirasConvenio mBandeiras = BandeirasConvenio(bandeirasDCC: []);

  String smsEnviadoPacto = '';

  bool get isUsuarioColaborador => mUsuarioLogado == null
      ? false
      : mUsuarioLogado != null && (mUsuarioLogado!.perfilUsuario?.isNotEmpty ?? false) ||
          ['PROFESSOR', 'COLABORADOR', 'COORDENADOR'].any((element) => (mUsuarioLogado?.descricaoPerfil ?? '').toLowerCase().contains(element)) ||
          (mUsuarioLogado?.matricula?.isEmpty ?? true);

  void updateEmpresaPadraoUsuario(Empresa empresa) {
    mUsuarioLogado!.codEmpresa = empresa.codigo;
    if (empresa.codigoProfessor != null) {
      mUsuarioLogado!.codigoProfessor = empresa.codigoProfessor;
      //mUsuarioLogado!.codigoColaborador = empresa.codigoColaborador;
    }
    GetIt.I.get<ControladorEventBus>().eventBus.fire(TrocouDeEmpresaEvent(empresa));
    atualizarBanco('usuarioLogado', {'codEmpresa': empresa.codigo});
  }

  num get getCodigoProfessor {
    num codigo = 0;
    if (mUsuarioLogado?.empresas?.isNotEmpty ?? false) {
      for (final empresa in mUsuarioLogado!.empresas!) {
        if (empresa.codigo == (mUsuarioLogado?.codEmpresa ?? 1)) {
          codigo = (empresa.codigoProfessor ?? 0);
        }
      }
    }
    return codigo;
  }

  @observable
  ObservableList<UsuarioAppTelefone> mUsuariosEncontrados = ObservableList<UsuarioAppTelefone>();
  @observable
  ObservableList<UsuarioAppTelefone> mUsuariosEncontradosCPF = ObservableList<UsuarioAppTelefone>();
  @observable
  ObservableList<UsuarioKeep> mUsuariosSalvos = ObservableList<UsuarioKeep>();

  @observable
  String? nomeEmpresaSelecionada;

  @action
  String nomeEmpresa() {
    var empresa = mUsuarioLogado!.empresas!.firstWhere((element) => element.codigo == mUsuarioLogado!.codEmpresa, orElse: () {
      return Empresa(nome: 'Selecionar unidade');
    });
    return empresa.nome ?? 'Selecionar unidade';
  }

  @action
  atualizarNomeEmpresa() {
    nomeEmpresaSelecionada = nomeEmpresa();
    GetIt.I.get<ControladorEventBus>().eventBus.on<TrocouDeEmpresaEvent>().listen((event) {
      nomeEmpresaSelecionada = nomeEmpresa();
    });
  }

  String contatoBodyFit = '';
  void validarColaboradorBodyFit(Function() onShowLock) {
    SharedPreferences.getInstance().then((db) {
      if ((GetIt.I.get<ControladorApp>().mClienteAppSelecionado?.documentkey?.contains('YPyTIpAxUABew44qomEz') ?? false) && isUsuarioColaborador) {
        GetIt.I.get<PersonalService>().situacaoEduzz(mUsuarioLogado!.email ?? mUsuarioLogado!.username!).then((value) {
          db.setBool('eduzzBloq', value.situacao!.contains('Bloquear'));
          db.setString('contatoBodyf', value.telefoneContato!);
          contatoBodyFit = value.telefoneContato!;
          if (value.situacao!.contains('Bloquear')) onShowLock();
        }).catchError((onError) {
          if (db.getBool('eduzzBloq') ?? false) onShowLock();
          contatoBodyFit = db.getString('contatoBodyf') ?? '';
        });
      }
    });
  }

  void pesquisarUsuarioPorTelefone(
      {required String ddd,
      required String ddi,
      required String numero,
      required Function() loading,
      required Function() encontrouUm,
      required Function() encontrouVarios,
      required Function() naoEncontrou,
      required Function() onErro}) {
    loading();
    _mServiceAuth.consultarPeloNumeroTelefone(BodyDescrobrirUsuariosCrypto(celular: numero, ddi: ddi, ddd: ddd)).then((usuariosAppTelefone) {
      mUsuariosEncontrados.clear();
      if (usuariosAppTelefone.isEmpty) {
        naoEncontrou();
      }
      mUsuariosEncontrados.addAll(usuariosAppTelefone);
      if (usuariosAppTelefone.length == 1) {
        usuarioLogar = usuariosAppTelefone.first;
        encontrouUm();
      } else {
        encontrouVarios();
      }
    }).catchError((onError) {
      if (onError.error.toString().contains('não encontrado')) {
        naoEncontrou();
      } else {
        onErro.call();
      }
    });
  }

  void pesquisarusuarioPorCPF({required String cpf, required Function() loading, required Function() encontrou, required Function() naoEncontrou, required Function() onErro}) {
    loading();
    mUsuariosEncontradosCPF.clear();
    _mServiceAuth.consultarPeloNumeroCPF(cpf).then((value) {
      mUsuariosEncontradosCPF.addAll(value);
      if (mUsuariosEncontradosCPF.isNotEmpty) {
        encontrou();
      } else {
        naoEncontrou();
      }
    }).catchError((onError) {
      onErro.call();
    });
  }

  @observable
  List<Empresa> mListaEmpresas = [];

  void pesquisarUnidade(String termo, Function(bool pesquisaAtiva) pesquisaAtiva) {
    mListaEmpresas.clear();
    if (termo.isNotEmpty) {
      var termoLimpo = removeDiacritics(termo);
      mListaEmpresas.addAll((mUsuarioLogado?.empresas ?? []).where((element) => removeDiacritics(element.nome!.toLowerCase()).contains(termoLimpo.toLowerCase())));
      pesquisaAtiva.call(true);
    } else {
      pesquisaAtiva.call(false);
      mListaEmpresas.addAll(mUsuarioLogado?.empresas ?? []);
    }
  }

  void puxarEmpresasDoCadastro() {
    mListaEmpresas.clear();
    mListaEmpresas.addAll(mUsuarioLogado?.empresas ?? []);
  }

  Future<void> verificaSeTemUsuarioLogado({Function()? tem, Function()? naoTem, Function()? parouEnvioSMS}) async {
    var userRecord = await buscarNoBanco('usuarioLogado');
    var userRecordFirebase = await buscarNoBanco('dadosFirebase');
    var sharedPrefres = await SharedPreferences.getInstance();
    if (userRecord == null || userRecordFirebase == null) {
      validarSeusuarioJaEnviouToken(tokenEnviado: parouEnvioSMS, segueFluxo: naoTem);
    } else {
      _controladorClienteApp.chave = sharedPrefres.getString(UserDataKeys.CHAVE.toString());
      mUsuarioLogado = Usuario.fromJson(userRecord);
      mUsuarioLogado!.codEmpresa ??= 1;
      mUsuarioAuth = UserAuthFirebase.fromJson(userRecordFirebase);
      if (FirebaseAuth.instance.currentUser == null) {
        FirebaseAuth.instance.signInWithEmailAndPassword(email: mUsuarioAuth!.email!, password: 'JOJOvsDIO').then((value) {
          validarSituacaoAluno(sucesso: tem);
          try {
            FirebaseCrashlytics.instance.setUserIdentifier(value.user!.uid);
          } catch (e) {}
        }).catchError((onError) {
          validarSeusuarioJaEnviouToken(tokenEnviado: parouEnvioSMS, segueFluxo: naoTem);
        });
      } else {
        GetIt.I.get<ControladorNotificacoesCrm>().consultarNotificacoesCrm();
        validarSituacaoAluno(sucesso: tem);
        try {
          FirebaseCrashlytics.instance.setUserIdentifier(mUsuarioAuth!.uid!);
        } catch (e) {}
      }
    }
  }

  void passouDataConsultaContrato(int data) async {
    SharedPreferences dataConsultaContrato = await SharedPreferences.getInstance();
    await dataConsultaContrato.setInt('data_consulta_contrato', data);
  }

  @observable
  SituacaoCliente? consultaSituacao = SituacaoCliente();
  @observable
  String? situacao = 'Visitante';

  void consultarSituacaoAlunoAPP({Function()? carregando, Function()? sucesso, Function(String)? falha}) {
    carregando?.call();
    try {
      _mServiceAuth.consultarSituacaoAluno(mUsuarioLogado!.matricula!).then((value) {
        consultaSituacao = value;
        situacao = consultaSituacao!.situacao;
        sucesso?.call();
      }).catchError((onError) {
        falha?.call(onError);
      });
    } catch (e) {
      falha?.call('load_falha');
    }
  }

  Future<void> verificarSeAssinouOsTermos({Function(bool eColaborador)? assinou, Function(bool eColaborador)? naoAssinou}) async {
    var db = await SharedPreferences.getInstance();
    if (isUsuarioColaborador) {
      if (!(db.getBool('aceitouNovosTermos') ?? false)) {
        naoAssinou?.call(isUsuarioColaborador);
      } else {
        assinou?.call(isUsuarioColaborador);
      }
    } else {
      if (db.getBool('aceitouNovosTermos-${GetIt.I.get<ControladorApp>().chave!}-${mUsuarioLogado?.codEmpresa}-${mUsuarioLogado!.matricula}') == null) {
        consultarAssinaturaTermos(mUsuarioLogado!.matricula ?? '', sucesso: (termoAssinado) {
          if (!termoAssinado) {
            db.setBool('aceitouNovosTermos-${GetIt.I.get<ControladorApp>().chave!}-${mUsuarioLogado?.codEmpresa}-${mUsuarioLogado!.matricula}', false);
            naoAssinou?.call(isUsuarioColaborador);
          } else {
            db.setBool('aceitouNovosTermos', true);
            db.setBool('aceitouNovosTermos-${GetIt.I.get<ControladorApp>().chave!}-${mUsuarioLogado?.codEmpresa}-${mUsuarioLogado!.matricula}', true);
            assinou?.call(isUsuarioColaborador);
          }
        },falha: (e){
            assinou?.call(isUsuarioColaborador);
        });
      } else {
        if (!(db.getBool('aceitouNovosTermos-${GetIt.I.get<ControladorApp>().chave!}-${mUsuarioLogado?.codEmpresa}-${mUsuarioLogado!.matricula}') ?? false)) {
          naoAssinou?.call(isUsuarioColaborador);
        } else {
          assinou?.call(isUsuarioColaborador);
        }
      }
    }
  }

  void Function(void Function())? stateHomePage;
  Future<void> validarSituacaoAluno({Function()? carregando, Function()? sucesso, Function(String)? falha, bool ignorarConsultaContrato = true}) async {
    carregando?.call();
    late SharedPreferences _db;
    if (isUsuarioColaborador) {
      sucesso?.call();
      return;
    }
    if (!(mUsuarioLogado!.treinoIndependente ?? false)) {
      await SharedPreferences.getInstance().then((db) {
        _db = db;
        return _mServiceAuth.consultarSituacaoAluno(mUsuarioLogado!.matricula!);
      }).then((value) async {
        if ('CA-VE-DE-AV'.contains(value.situacaoContrato ?? '---') && (value.situacaoContrato?.isNotEmpty ?? false)) {
          await SharedPreferences.getInstance().then((data) {
            if (data.getInt('data_consulta_contrato') == null) {
              var data = DateTime.now().add(const Duration(days: 1)).millisecondsSinceEpoch;
              passouDataConsultaContrato(data);
              GetIt.I.get<ControladorContratoUsuario>().consultarOsContratosDoUsuario(
                  sucesso: () {
                    contratoUsuarioVencido = GetIt.I.get<ControladorContratoUsuario>().contratoVisualizar;
                    value.codigoContrato = contratoUsuarioVencido!.codigo;
                    value.nomeContrato = contratoUsuarioVencido!.nomePlano;
                    tratarSituacao(value, _db);
                    sucesso?.call();
                  },
                  falha: (falha) {
                    tratarSituacao(value, _db);
                    sucesso?.call();
                  },
                  ignorar: ignorarConsultaContrato);
            } else if (data.getInt('data_consulta_contrato')! < DateTime.now().millisecondsSinceEpoch) {
              var data = DateTime.now().add(const Duration(days: 1)).millisecondsSinceEpoch;
              passouDataConsultaContrato(data);
              GetIt.I.get<ControladorContratoUsuario>().consultarOsContratosDoUsuario(
                  sucesso: () {
                    contratoUsuarioVencido = GetIt.I.get<ControladorContratoUsuario>().contratoVisualizar;
                    value.codigoContrato = contratoUsuarioVencido!.codigo;
                    value.nomeContrato = contratoUsuarioVencido!.nomePlano;
                    tratarSituacao(value, _db);
                    sucesso?.call();
                  },
                  falha: (falha) {
                    tratarSituacao(value, _db);
                    sucesso?.call();
                  },
                  ignorar: ignorarConsultaContrato);
            } else {
              tratarSituacao(value, _db);
              sucesso?.call();
            }
          });
        } else {
          tratarSituacao(value, _db);
          sucesso?.call();
        }
      }).catchError((onError) {
        if (_db.getString('situacao') != null) {
          tratarSituacao(SituacaoCliente.fromJson(const JsonCodec().decode(_db.getString('situacao')!)), _db);
        }
        sucesso?.call();
      });
    } else {
      sucesso?.call();
    }
  }

  bool get isVisitante => consultaSituacao != null ? consultaSituacao?.situacao?.contains('VI') ?? false : true;
  bool get isDesistente => consultaSituacao?.situacaoContrato?.contains('DE') ?? false;
  bool get isVencido => consultaSituacao?.situacaoContrato?.contains('VE') ?? false;
  bool get isCancelado => consultaSituacao?.situacaoContrato?.contains('CA') ?? false;
  bool get isAVencer => consultaSituacao?.situacaoContrato?.contains('AV') ?? false;
  bool get isAtivo => (consultaSituacao?.situacaoContrato?.contains('AT') ?? false) || (consultaSituacao?.situacaoContrato?.contains('NO') ?? false);
  bool get isHabilitarAlunoVerOpcoes => isVisitante || isDesistente || isVencido;

  void tratarSituacao(SituacaoCliente situacao, SharedPreferences _db) {
    mLogadoSituacao = situacao;
    _db.setString('situacao', const JsonCodec().encode(mLogadoSituacao!.toJson()));
    stateHomePage?.call(() {});
  }

  Future<void> recuperarSenha(String email, {Function()? sucesso, Function(bool? falha)? falha}) async {
    _mServiceAuth.enviarLembreteSenha(BodyRecuperarSenhaCrypto(cliente: true, email: email)).then((value) {
      sucesso!();
    }).catchError((onError) {
      falha!(onError.message.contains('inválido'));
    });
  }

  @observable
  bool erroValidarTotalPass = false;
  @observable
  String cpfAlunoValidarTotalPass = '';

  void validarAcessoTotalPass({required String cpf, Function()? carregando, Function()? sucesso, Function(String? mensage)? falha}) {
    carregando?.call();
    erroValidarTotalPass = false;
    ObjValidarTotalPass obj =
        ObjValidarTotalPass(cpf: cpf, empresa: mUsuarioLogado?.codEmpresa ?? 1, matricula: mUsuarioLogado?.matricula ?? '', pessoa: mUsuarioLogado?.codigoPessoaCliente ?? 0);
    _mServiceUsuario.validarTotalPass(obj.toJsonBody()).then((value) {
      cpfAlunoValidarTotalPass = '';
      sucesso?.call();
    }).catchError((onError) {
      erroValidarTotalPass = true;
      cpfAlunoValidarTotalPass = '';
      falha?.call(onError.message.toString());
    });
  }

  Future<void> deslogarUsuario({
    Function()? sucesso,
  }) async {
    veioDeTokenAnterior = false;
    await SembastCacheStore().checkAndCleanCacheIfNecessary(forceClean: true);
    desinscreverTopicoFirebase();
    String currentChave = GetIt.I.get<ControladorApp>().chave ?? '';
    GetIt.I.get<ControladorApp>().limparTodosControladores();
    nomeEmpresaSelecionada = null;
    contratoTermos = false;
    contratoAssinatura = false;
    acessoUnidadeFoto = false;
    mUsuariosSalvos.clear();
    _controladorClienteApp.veioDoSplash = false;
    StoreRef<int, Map<String, dynamic>> storeSalvos = intMapStoreFactory.store('usuariosKeep');
    var listaDeUsuariosSalvos = await storeSalvos.find(getDb, finder: Finder());
    if (listaDeUsuariosSalvos.isNotEmpty) {
      for (final element in listaDeUsuariosSalvos) {
        var usu = UsuarioKeep.fromJson(element.value);
        if (usu.firebaseUid != null) mUsuariosSalvos.add(usu);
      }
    }
    int indexOfUser = mUsuariosSalvos.indexWhere((element) => element.firebaseUid == mUsuarioAuth?.uid);
    // List<UsuarioKeep> usuarioAtual = mUsuariosSalvos.where((element) => element.firebaseUid == mUsuarioAuth?.uid).toList();
    if (!indexOfUser.isNegative) {
      mUsuariosSalvos[indexOfUser].nome = mUsuarioLogado?.nome;
      mUsuariosSalvos[indexOfUser].chave = currentChave;
      mUsuariosSalvos[indexOfUser].urlFoto = mUsuarioLogado?.srcImg;
      mUsuariosSalvos[indexOfUser].usuarioMovel = mUsuarioLogado?.username;
      mUsuariosSalvos[indexOfUser].nomeUnidade = (mUsuarioLogado?.nomeEmpresa ?? '').isNotEmpty
          ? (mUsuarioLogado?.nomeEmpresa ?? '')
          : (mUsuarioLogado?.empresas ?? []).isNotEmpty
              ? (mUsuarioLogado?.empresas?.first.nome ?? '')
              : '';
    }
    var storeDados = store.record('dadosDoUsuario');
    await storeDados.delete(getDb);
    StoreRef<int, Map<String, dynamic>> storeKeepUsuer = intMapStoreFactory.store('usuariosKeep');
    storeKeepUsuer.delete(getDb);
    await getDb.transaction((txn) async {
      await Future.wait(mUsuariosSalvos.map((v) => storeKeepUsuer.add(txn, v.toJson())));
      deletarDoBanco('usuarioLogado');
      mDadosDoUsuario = null;
      mUsuariosSalvos.clear();
      veioDeTokenAnterior = false;
      bloquearNavegacao = false;
      exibirAlertaPlanoVencido = false;
      exibirCompraDePlano = false;
      permitirRenovarPlano = false;
      exibirAlertaDeRenovacao = false;
      mLogadoSituacao = null;
      stateHomePage = null;
      sucesso?.call();
      intMapStoreFactory.store('inappView').delete(getDb);
    });
    mUsuarioLogado = null;
  }

  Function()? removeuTodosUsuariosKeepUser;

  removerUsuarioDoKeepUser(UsuarioKeep? keep) async {
    StoreRef<int, Map<String, dynamic>> storeKeepUsuer = intMapStoreFactory.store('usuariosKeep');
    storeKeepUsuer.delete(getDb);
    if (keep?.firebaseUid != null) {
      mUsuariosSalvos.removeWhere((element) => element.firebaseUid == keep!.firebaseUid);
    } else {
      try {
        if (keep?.codigoProfessor == null) {
          mUsuariosSalvos.removeWhere((element) => element.chave == keep?.chave && element.codigoUsuario == keep?.codigoUsuario);
        } else {
          mUsuariosSalvos.removeWhere((element) => element.chave == keep?.chave && element.codigoProfessor == keep?.codigoProfessor);
        }
      } catch (e) {}
    }
    await getDb.transaction((txn) async {
      await Future.wait(mUsuariosSalvos.map((v) => storeKeepUsuer.add(txn, v.toJson())));
    });
    if (mUsuariosSalvos.isEmpty) removeuTodosUsuariosKeepUser?.call();
  }

  void logarUsuarioPorSenha(
      {bool? precisaSalvarUsuario,
      Usuario? mCurrentUser,
      bool? isColaborador,
      String? user,
      String? pass,
      num? codigoUsuarioTreino,
      required Function()? carregando,
      Function()? sucesso,
      Function(String? falha)? falha}) {
    var usuarioRestService;
    carregando?.call();
    _controladorClienteApp.consultarUrlTreino(
        suceso: () {
          isColaborador = isColaborador ?? GetIt.I.get<ControladorNovoLogin>().mUsuario.mUserTel?.codigocolaborador == 0;
          if (GetIt.I.get<ControladorApp>().userRelogin != null) {
            isColaborador = GetIt.I.get<ControladorApp>().userRelogin!.eColaborador;
          }
          Future<Usuario> call;
          if (kDebugMode || (GetIt.I.get<ControladorNovoLogin>().pularSenha) || modoHomologacao) {
            pass = '';
          }
          UsuarioAppTelefone? mUserTel = GetIt.I.get<ControladorNovoLogin>().mUsuario.mUserTel;
          // Verifica no controlador do novo Login
          if (mUserTel?.codigousuariotreino != null) {
            codigoUsuarioTreino = mUserTel?.codigousuariotreino;
          }
          if ((pass?.isEmpty ?? false) && mCurrentUser != null) {
            call = Future.value(mCurrentUser);
          } else if (pass?.isNotEmpty ?? false) {
            call = _mServiceAuth.loginSenha(BodyLoginPasswordCrypto(userName: user!, pwd: pass!));
          } else if (codigoUsuarioTreino != null) {
            call = _mServiceAuth.loginRedeSocialV2(BodyLoginAppPorCod(codUsuario: codigoUsuarioTreino, aluno: !(mUserTel?.isColaborador ?? false)));
          } else {
            call = _mServiceAuth.loginRedeSocial(BodyLoginCrypto(email: user!, aluno: !(isColaborador ?? true)));
          }
          call.then((usuarioResult) async {
            GetIt.I.get<ControladorApp>().userRelogin = null;
            GetIt.I.get<ControladorNovoLogin>().mUsuario.mUserTel = null;
            usuarioRestService = usuarioResult;
            return _mServiceAuth.pegaInfoAuthFirebase(BodyInfoAuth(
                    chave: _controladorClienteApp.chave,
                    email: usuarioResult.username ?? usuarioResult.email,
                    telefone: usuarioResult.telefone == null ? '' : usuarioResult.telefone.replaceAll('(', '').replaceAll(')', '').replaceAll(' ', '').replaceAll('+', ''),
                    codigoUsuario: usuarioResult.codUsuario?.toInt(),
                    usuarioMovel: usuarioResult.username)
                .toJson());
          }).then((value) async {
            if (usuarioRestService!.empresas != null) {
              try {
                usuarioRestService!.empresas!.firstWhere((empresa) => empresa.codigo == usuarioRestService!.codEmpresa);
              } catch (e) {
                if ((usuarioRestService!.empresas as List<dynamic>).isNotEmpty) {
                  usuarioRestService!.codEmpresa = usuarioRestService!.empresas!.first.codigo ?? (usuarioRestService!.codEmpresa ?? 1);
                }
              }
            }
            mUsuarioLogado = usuarioRestService;
            mUsuarioAuth = value;
            await TokenDioInterceptor.fetchNewToken();
            if (!F.isPersonalizado) {
              return FirebaseAuth.instance.signInWithEmailAndPassword(email: mUsuarioAuth!.email!, password: 'JOJOvsDIO');
            } else {
              return Future.value();
            }
          }).then((user) {
            manterUsuarioFirebase(() {
              inserirNoBanco('dadosFirebase', mUsuarioAuth!.toJson());
              mUsuarioAuth = mUsuarioAuth;
              inserirNoBanco('usuarioLogado', usuarioRestService.toJson());
              mUsuarioLogado = usuarioRestService;
              if (mUsuarioLogado!.codUsuario == null) {
                falha?.call('Não foi possível concluir sua solicitação. O servidor retornou um erro inesperado. Tente novamente em alguns instantes.');
                return;
              }
              if (precisaSalvarUsuario ?? true) {
                gravaDadosParaLoginAoSair();
              }
              inscreverTopicosParaPush();
              if (GetIt.I.get<ControladorCliente>().isUsuarioColaborador) {
                GetIt.I.get<ControladorSplash>().gerarTokenColaboradorLogado((token) {
                  validarSituacaoAluno(sucesso: sucesso, ignorarConsultaContrato: true);
                });
              } else {
                GetIt.I.get<ControladorSplash>().gerarTokenUsuarioLogado((token) {
                  validarSituacaoAluno(sucesso: sucesso, ignorarConsultaContrato: true);
                });
              }
              GetIt.I.get<ControladorSplash>().gerarTokenUsuarioMs(onSucesso: (token) => {});
              GetIt.I.get<ControladorNotificacoesCrm>().consultarNotificacoesCrm();
              analytic(EventosKey.login_usuario);
            }, falha);
          }).catchError((onError) {
            if (onError is FirebaseAuthException) {
              if (onError.code == 'user-disabled') {
                falha?.call('user-disabled');
                return;
              }
            }
            if (!(isColaborador ?? false)) {
              logarUsuarioPorSenha(user: user, sucesso: sucesso, falha: falha, isColaborador: true, mCurrentUser: mCurrentUser, pass: pass, carregando: () {});
              return;
            }
            falha!(onError.message == 'Usuário não encontrado.'
                ? localizedString('user_not_found')
                : onError.message == 'Dados incorretos!'
                    ? localizedString('incorrect_data')
                    : onError.message);
          });
        },
        erro: (mensagem) => falha!(mensagem ?? 'Problemas para consultar alguns serviços'));
  }

  Future<void> aceitarTermosDeUso({String? termo, Function()? carregando, Function(String error)? falha, Function()? sucesso}) async {
    carregando?.call();
    if (mUsuarioLogado?.treinoIndependente ?? false) {
      Future.delayed(const Duration(seconds: 1), () {
        sucesso?.call();
      });
      return;
    }
    try {
      var ipAddress = IpAddress(type: RequestType.json);
      Map<String, dynamic> ip = await ipAddress.getIpAddress();

      _mServiceAuth.buscarTermosDeAceite().then((value) async {
        if (value.isEmpty) {
          _mServiceAuth.salvarTermosAceite(termo ?? 'https://sistemapacto.com.br/privacidade/').then((value) {
            _mServiceAuth.buscarTermosDeAceite().then((value) async {
              _mServiceAuth
                  .aceitarTermosAplicativo(
                      termo: value.first.codigo as num,
                      cpf: mDadosDoUsuario?.cpf?.replaceAll(new RegExp(r'[^0-9]'), '') ?? '',
                      data: DateTime.now().millisecondsSinceEpoch,
                      ip: ip['ip'] ?? '',
                      nome: mUsuarioLogado?.nome ?? '',
                      codigoMatricula: mUsuarioLogado!.matricula!,
                      email: mUsuarioLogado?.email ?? '')
                  .then((value) {
                sucesso?.call();
              }).catchError((onError) {
                falha?.call(onError.message.toString());
              });
            });
          });
        } else {
          _mServiceAuth
              .aceitarTermosAplicativo(
                  termo: value.first.codigo as num,
                  cpf: mDadosDoUsuario?.cpf?.replaceAll(new RegExp(r'[^0-9]'), '') ?? '',
                  data: DateTime.now().millisecondsSinceEpoch,
                  ip: ip['ip'] ?? '',
                  nome: mUsuarioLogado?.nome ?? '',
                  codigoMatricula: mUsuarioLogado!.matricula!,
                  email: mDadosDoUsuario?.email ?? '')
              .then((value) {
            sucesso?.call();
          }).catchError((e) {
            if (e.response.toString().toLowerCase().contains('o termo já foi assinado por esse')) {
              sucesso?.call();
            } else if (e.error.toString().toLowerCase().contains('o termo já foi assinado por esse')) {
              falha?.call(e.toString());
            } else {
              falha?.call(e.error.toString());
            }
          });
        }
      });
    } catch (e) {
      falha?.call(e.toString());
    }
  }

  //Atualmente o termo de aceite é salvo no ADM, e no caso o Treino independente não tem acesso,
  //criamos esse fluxo para pular esse etapa.

  Future<void> consultarAssinaturaTermos(String codigoMatricula, {Function()? carregando, Function(String error)? falha, Function(bool termoAssinado)? sucesso}) async {
    carregando?.call();
    if (mUsuarioLogado?.treinoIndependente ?? false) {
      sucesso?.call(true);
    } else {
      _mServiceAuth.consultarAssinaturaTermos(codigoMatricula).then((value) async {
        sucesso?.call(value != null && value.isNotEmpty);
      }).catchError((e) {
        falha?.call(e);
      });
    }
  }

  bool get isColaboradorAtivo => mUsuarioLogado?.status?.toLowerCase().contains('ativo') ?? false;

  void consultarSeTemUsuariosQueJaLogaram(Function(List<UsuarioKeep> usuarios) callback) async {
    mUsuariosSalvos.clear();
    StoreRef<int, Map<String, dynamic>> store = intMapStoreFactory.store('usuariosKeep');

    // Buscar pelo atributo codigoUsuario onde não seja nulo, ordenando por ID de forma decrescente
    var listaDeUsuariosSalvos = await store.find(getDb,
        finder: Finder(filter: Filter.notNull('codigoUsuario'), sortOrders: [SortOrder(Field.key, false)] // false para ordem decrescente
            ));

    if (listaDeUsuariosSalvos.isNotEmpty) {
      for (final element in listaDeUsuariosSalvos) {
        var usu = UsuarioKeep.fromJson(element.value);
        if (usu.firebaseUid != null && !mUsuariosSalvos.any((element) => element.firebaseUid == usu.firebaseUid)) {
          mUsuariosSalvos.add(usu);
        }
      }
    }

    List<UsuarioKeep> dependentes = [];
    if (mUsuarioLogado?.dependenteDTOS != null && mUsuarioLogado!.dependenteDTOS!.isNotEmpty) {
      for (final dependente in mUsuarioLogado!.dependenteDTOS!) {
        int codigoDependente = dependente.codigoUsuarioTreino?.toInt() ?? 0;
        int indexExistente = mUsuariosSalvos.indexWhere((u) => u.codigoUsuario == codigoDependente);
        if (indexExistente == -1) {
          dependentes.add(UsuarioKeep(
            nome: dependente.nome,
            urlFoto: dependente.foto,
            usuarioMovel: dependente.username,
            codigoUsuario: codigoDependente,
            eColaborador: false,
            isDependente: true,
            codigoUsuarioPrincipal: mUsuarioLogado!.codUsuario?.toInt(),
            chave: _controladorClienteApp.chave,
            clienteApp: _controladorClienteApp.mClienteAppSelecionado!.documentkey,
            nomeUnidade: mUsuarioLogado?.nomeEmpresa,
          ));
        } else {
          mUsuariosSalvos[indexExistente].isDependente = true;
          mUsuariosSalvos[indexExistente].codigoUsuarioPrincipal = mUsuarioLogado!.codUsuario?.toInt();
        }
      }
    }

    // Ordenar apenas os dependentes por nome
    dependentes.sort((a, b) => (a.nome ?? '').compareTo(b.nome ?? ''));

    // Combinar as listas (dependentes primeiro, depois usuários na ordem original)
    List<UsuarioKeep> listaFinal = [...dependentes, ...mUsuariosSalvos];

    // Remover duplicatas mantendo a ordem
    List<UsuarioKeep> listaSemDuplicatas = [];
    Set<String?> codigosProcessados = {};
    for (final user in listaFinal) {
      if (!codigosProcessados.contains(user.firebaseUid)) {
        listaSemDuplicatas.add(user);
        codigosProcessados.add(user.firebaseUid);
      }
    }

    if (listaSemDuplicatas.isEmpty) {
      listaSemDuplicatas.addAll(GetIt.I.get<ControladorNovoLogin>().mUsuariosKeepPrevius);
    }

    mUsuariosSalvos
      ..clear()
      ..addAll(listaSemDuplicatas);

    callback(listaSemDuplicatas);
  }

  atualizarBanco(String banco, Map value) {
    store.record(banco).update(getDb, value);
    manterUsuarioFirebase(null, null);
  }

  gravaDadosParaLoginAoSair() async {
    var dbLocal = await SharedPreferences.getInstance();
    dbLocal.remove(UserDataKeys.SNDTOKN.toString().replaceAll('UserDataKeys.', ''));
    dbLocal.remove('situacao');
    dbLocal.remove('showRenv');
    consultarSeTemUsuariosQueJaLogaram((usuarios) async {
      StoreRef<int, Map<String, dynamic>> storeKeepUsuer = intMapStoreFactory.store('usuariosKeep');
      var keepThis = UsuarioKeep(
          chave: this._controladorClienteApp.chave,
          clienteApp: _controladorClienteApp.mClienteAppSelecionado!.documentkey,
          eColaborador: isUsuarioColaborador,
          firebaseUid: mUsuarioAuth!.uid,
          nomeNoAplicativo: _controladorClienteApp.mClienteAppSelecionado!.nomeDoApp,
          codigoUsuario: (mUsuarioLogado!.codUsuario ?? -1).toInt(),
          matricula: mUsuarioLogado!.matricula ?? -1 as String?,
          codigoProfessor: mUsuarioLogado!.codigoProfessor as int? ?? -1,
          nome: mUsuarioLogado!.nome,
          urlFoto: mUsuarioLogado!.srcImg,
          usuarioMovel: mUsuarioLogado!.username ?? mUsuarioLogado!.email);
      bool usuarioExistente = usuarios.any((element) => element.firebaseUid == mUsuarioAuth!.uid);
      await getDb.transaction((txn) async {
        if (usuarioExistente) {
          await storeKeepUsuer.delete(txn, finder: Finder(filter: Filter.equals('firebaseUid', mUsuarioAuth!.uid)));
        }
        await storeKeepUsuer.add(txn, keepThis.toJson());
      });
    });
  }

  Future<void> validarSeusuarioJaEnviouToken({Function()? tokenEnviado, Function()? segueFluxo}) async {
    var db = await SharedPreferences.getInstance();
    smsEnviadoPacto = db.getString(UserDataKeys.SNDTOKN.toString().replaceAll('UserDataKeys.', '')) ?? '';
    if (smsEnviadoPacto.isNotEmpty) {
      usuarioLogar = UsuarioAppTelefone.fromJson(await store.record('usuarioLogar').get(getDb) as Map<String, dynamic>);
      codigoDDI = usuarioLogar!.codigoDDI;
      veioDeTokenAnterior = true;
      tokenEnviado?.call();
    } else {
      segueFluxo?.call();
    }
  }

  Timer? _timerEnvioSms;

  sendCodeToPhoneNumber(String phone, {Function()? carregando, Function(String error)? falha, Function()? sucesso}) async {
    // Se modo debug então tá liberado
    if (kDebugMode || _controladorClienteApp.pularSenhaLogin) {
      Future.delayed(const Duration(seconds: 2)).then((value) {
        carregando?.call();
        sucesso?.call();
      });
      return;
    }
    if (_timerEnvioSms?.isActive ?? false) {
      return;
    } else {
      _timerEnvioSms = Timer(const Duration(seconds: 4), () {});
    }
    if (phone.substring(0, 3).contains('+55')) {
      _mServiceAuth.gerarTokenSMSNovo('Utilize o código verificação', F.title, phone).then((value) async {
        if (value.toLowerCase().contains('erro')) {
          falha?.call('Não foi possível enviar o código de verificação');
        } else {
          try {
            smsEnviadoPacto += value.split(' ')[2];
            var db = await SharedPreferences.getInstance();
            db.setString(UserDataKeys.SNDTOKN.toString().replaceAll('UserDataKeys.', ''), smsEnviadoPacto);
            usuarioLogar!.codigoDDI = codigoDDI;
            store.record('usuarioLogar').put(getDb, usuarioLogar!.toJson());
            sucesso?.call();
          } catch (e) {
            _timerEnvioSms = null;
            falha?.call('Não foi possível enviar o código de verificação');
          }
        }
      }).catchError((onError) {
        _timerEnvioSms = null;
        falha?.call('Não foi possível enviar o código de verificação');
      });
    } else {
      final PhoneVerificationCompleted verificationCompleted = (AuthCredential credential) {};
      final PhoneVerificationFailed verificationFailed = (FirebaseAuthException authException) {
        String erro = '';
        if (authException.message!.contains('blocked all requests')) {
          erro = 'Não foi possível enviar o código devido às várias tentativas. Tente novamente mais tarde.';
        } else if (authException.message!.contains('number')) {
          erro = 'O número informado não é válido para receber o SMS. Verifique seu número e tente novamente.';
        } else {
          erro = 'Ocorreu um erro inesperado ao tentar enviar o código de verificação. Tente novamente em instantes';
        }
        _timerEnvioSms = null;
        falha?.call(erro);
      };
      final PhoneCodeSent codeSent = (String verificationId, [int? forceResendingToken]) {
        _verificationId = verificationId;
        sucesso!();
      };
      final PhoneCodeAutoRetrievalTimeout codeAutoRetrievalTimeout = (String verificationId) {
        _verificationId = verificationId;
        sucesso!();
      };
      await FirebaseAuth.instance.verifyPhoneNumber(
        phoneNumber: phone,
        timeout: const Duration(seconds: 30),
        verificationCompleted: verificationCompleted,
        verificationFailed: verificationFailed,
        codeSent: codeSent,
        codeAutoRetrievalTimeout: codeAutoRetrievalTimeout,
      );
    }
  }

  signInWithPhoneNumber(String smsCode, {Function()? carregando, Function()? falha, Function()? sucesso}) async {
    //Se modo debug então tá liberado
    carregando?.call();
    // var kDebugMode = false;
    if (kDebugMode || _controladorClienteApp.pularSenhaLogin) {
      Future.delayed(const Duration(seconds: 2)).then((value) {
        sucesso?.call();
      });
      return;
    }
    if (smsCode.isEmpty) {
      falha?.call();
      return;
    }
    if ((smsEnviadoPacto.isNotEmpty)) {
      if ((smsEnviadoPacto.contains(smsCode)) && (smsEnviadoPacto.isNotEmpty)) {
        sucesso?.call();
      } else {
        falha?.call();
      }
    } else {
      final AuthCredential credential = PhoneAuthProvider.credential(
        verificationId: _verificationId,
        smsCode: smsCode,
      );
      carregando!();
      await FirebaseAuth.instance.signInWithCredential(credential).then((onValue) {
        final User? currentUser = onValue.user;
        if (currentUser != null) {
          sucesso!();
        } else {
          falha!();
        }
      }).catchError((onError) {
        falha!();
      });
    }
  }

  void logarPorTelefone(UsuarioAppTelefone mClienteAppTelefone, {required Function() loading, required Function()? sucesso, required Function(String? mensagem)? onErro}) {
    if (mClienteAppTelefone.usernameLoginRedeSocial != '' && mClienteAppTelefone.matricula != null && mClienteAppTelefone.matricula == mClienteAppTelefone.usernameLoginRedeSocial) {
      bool gerarAluno = true;
      if (mClienteAppTelefone.codigocolaborador == null || mClienteAppTelefone.codigocolaborador == 0) {
        mClienteAppTelefone.email = 'tempappal_${mClienteAppTelefone.codigocliente}@pacto.com';
        mClienteAppTelefone.nomeUsuarioMovel = mClienteAppTelefone.email;
      } else {
        gerarAluno = false;
        mClienteAppTelefone.email = 'tempappcol_${mClienteAppTelefone.codigocolaborador}@pacto.com';
        mClienteAppTelefone.usernameusuario = mClienteAppTelefone.email;
      }
      gerarUsuarioTreinoAluno(
        mClienteAppTelefone,
        gerarAluno,
      ).then((gerou) {
        logarUsuarioPorSenha(
            user: mClienteAppTelefone.nomeUsuarioMovel ?? mClienteAppTelefone.usernameLoginRedeSocial,
            isColaborador: mClienteAppTelefone.isColaborador,
            codigoUsuarioTreino: mClienteAppTelefone.codigousuariotreino,
            pass: '',
            carregando: loading,
            falha: onErro,
            sucesso: sucesso);
      }).catchError((e) {
        logarUsuarioPorSenha(
            user: mClienteAppTelefone.nomeUsuarioMovel ?? mClienteAppTelefone.usernameLoginRedeSocial,
            isColaborador: mClienteAppTelefone.isColaborador,
            pass: '',
            carregando: loading,
            falha: onErro,
            sucesso: sucesso);
      });
    } else {
      logarUsuarioPorSenha(
          user: mClienteAppTelefone.nomeUsuarioMovel ?? mClienteAppTelefone.usernameLoginRedeSocial,
          isColaborador: mClienteAppTelefone.isColaborador,
          pass: '',
          carregando: loading,
          falha: onErro,
          sucesso: sucesso);
    }
  }

  Future<void> consultarDadosUsuario({Function()? carregando, Function(DadosUsuario)? sucesso, Function(String? falha)? falha}) async {
    carregando?.call();

    _mServiceAuth.consultarDadosUsuario(mUsuarioLogado!.username!).then((value) {
      manterUsuarioFirebase(() => {sucesso?.call(value)}, falha);
    }).catchError((onError) {
      falha!(onError.message);
    });
  }

  Future<void> registrarUsoNoTreinoWeb() async {
    String key = 'rudapp';
    try {
      final dataBase = await SharedPreferences.getInstance();

      if (dataBase.getBool(key) ?? false) return;

      final packageInfo = await PackageInfo.fromPlatform();

      // Validações de segurança
      if (mUsuarioLogado?.username == null ||
          _controladorClienteApp.mClienteAppSelecionado?.id == null) {
        return;
      }

      final response = await _mServiceAuth.registarUsoAppTreinoWeb([
        RegistarUsoApp(
          appUtilizado: F.app,
          codEmpresa: mUsuarioLogado?.codEmpresa?.toInt(),
          codUsuario: mUsuarioLogado?.codUsuario?.toInt(),
          matricula: num.tryParse(mUsuarioLogado?.matricula ?? '0'),
          versaoDoApp: packageInfo.version,
          idClienteApp: _controladorClienteApp.mClienteAppSelecionado?.id,
          userName: mUsuarioLogado?.username
        )
      ]);

      await dataBase.setBool(key, response.retornoUsoAppReturn == 'sucesso');
    } catch (e) {
      // Log do erro e define como falso
      try {
        final dataBase = await SharedPreferences.getInstance();
        await dataBase.setBool(key, false);
      } catch (dbError) {
        // Se não conseguir nem acessar SharedPreferences, apenas ignora
      }
    }
  }

  void manterUsuarioFirebase(Function()? sucesso, Function(String? erro)? falha) async {
    PackageInfo? packageInfo;
    if (!kIsWeb) packageInfo = await PackageInfo.fromPlatform();
    _mServiceAuth
        .manterUsuarioFirebase(UsuarioFireBaseManter(
      app: F.app,
      userDeviceId: (modoHomologacao || kDebugMode) ? 'NOTSAVE' : thedeviceID,
      nomeNoAplicativo: _controladorClienteApp.mClienteAppSelecionado!.nomeDoApp,
      codigoCliente: mUsuarioLogado!.codigoCliente ?? -1,
      matricula: mUsuarioLogado!.matricula ?? -1 as String?,
      codigoProfessor: mUsuarioLogado!.codigoProfessor as int? ?? -1,
      usuarioMovel: mUsuarioLogado!.username ?? mUsuarioLogado!.email,
      nomeUnidade: mUsuarioLogado!.nomeEmpresa ?? '',
      email: mUsuarioLogado!.username,
      chave: GetIt.I.get<ControladorApp>().chave,
      telefone: mUsuarioLogado!.telefone,
      idClienteApp: _controladorClienteApp.mClienteAppSelecionado!.id,
      codigoDaEmpresa: mUsuarioLogado!.codEmpresa as int?,
      codUsuario: mUsuarioLogado!.codUsuario,
      nome: mUsuarioLogado!.nome,
      plataforma: kIsWeb
          ? 'WEB'
          : Platform.isAndroid
              ? 'Android'
              : 'Ios',
      iddocumento: mUsuarioAuth!.uid,
      urlFoto: mUsuarioLogado!.srcImg,
      refUsuario: mUsuarioAuth!.uid,
      nomeDaVersaoApp: packageInfo?.version ?? 'WEB',
      tipoDePlano: tipoDePremiumContratado,
      tipoPerfil: isUsuarioColaborador ? 'COLABORADOR' : 'ALUNO',
    ).toJson())
        .then((value) {
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  Future gerarUsuarioTreinoAluno(UsuarioAppTelefone mClienteAppTelefone, bool aluno) {
    var ab = '0123456789abcdefghijklmnopqrstuvwxyz';
    var randomPass = '';
    for (var i = 0; i < 8; i++) {
      var index = Random().nextInt(ab.length);
      randomPass += String.fromCharCode(ab.codeUnitAt(index));
    }
    return aluno
        ? _mServiceAuth.gerarUsuarioTR(mClienteAppTelefone.email!, randomPass, codigoCliente: mClienteAppTelefone.codigocliente)
        : _mServiceAuth.gerarUsuarioTR(mClienteAppTelefone.email!, randomPass, codigoColaborador: mClienteAppTelefone.codigocolaborador);
  }

  String converterParaHexa(int codigo) {
    String hexConvertido = codigo.toRadixString(16);
    if (hexConvertido.length == 1) {
      hexConvertido = '0' + hexConvertido;
    }
    return hexConvertido;
  }

  String encriptar(String textoPlano, String chave) {
    int faixa = 256;
    int tamanhoChave = chave.length;
    int posChave = -1;
    int offset = Random().nextInt(faixa);
    int posTextoPlano = 0;
    int codigo;

    if (tamanhoChave == 0) {
      return '';
    }

    StringBuffer result = StringBuffer();
    result.write(converterParaHexa(offset));

    for (; posTextoPlano < textoPlano.length; posTextoPlano++) {
      codigo = (textoPlano.codeUnitAt(posTextoPlano) + offset) % 255;

      if (posChave < (tamanhoChave - 1)) {
        posChave++;
      } else {
        posChave = 0;
      }

      codigo = codigo ^ chave.codeUnitAt(posChave);
      result.write(converterParaHexa(codigo));
      offset = codigo;
    }

    return result.toString();
  }

  @observable
  NotificarRecursoEmpresa dadosEmpresa = NotificarRecursoEmpresa();
  String retornoNotification = '';

  // void _notificarEventosEmpresa({required String recurso, Function()? carregando, Function()? sucesso, Function(String? falha)? falha}) {
  //   carregando?.call();
  //   dadosEmpresa.recurso = recurso;
  //   dadosEmpresa.uf = GetIt.I.get<ControladorApp>().empresaFinanceira.estado ?? '';
  //   dadosEmpresa.cidade = GetIt.I.get<ControladorApp>().empresaFinanceira.cidade ?? '';
  //   dadosEmpresa.data = UtilDataHora.getDiaMesAnoHorasMinutosSegundos(dateTime: DateTime.now()).replaceAll(' ', ' - ');
  //   dadosEmpresa.nomeEmpresa = mUsuarioLogado!.nomeEmpresa!;
  //   dadosEmpresa.chave = GetIt.I.get<ControladorApp>().chave!;
  //   dadosEmpresa.usuario = mUsuarioLogado!.username!;
  //   dadosEmpresa.empresa = mUsuarioLogado!.codEmpresa!;
  //   dadosEmpresa.pais = GetIt.I.get<ControladorApp>().empresaFinanceira.pais ?? '';
  //   _mServiceAuth.notificarRecursoEmpresa(dadosEmpresa.toJsonBody()).then((value) {
  //     retornoNotification = value;
  //     sucesso?.call();
  //   }).catchError((onError) {
  //     falha?.call(onError);
  //   });
  // }

  limparTudo() async {
    mUsuarioAuth = null;
    mUsuarioLogado = null;
    mUsuariosEncontrados.clear();
    mUsuariosEncontradosCPF.clear();
    smsEnviadoPacto = '';
    veioDeTokenAnterior = false;
    bloquearNavegacao = false;
    exibirCompraDePlano = false;
    permitirRenovarPlano = false;
    exibirAlertaDeRenovacao = false;
    mLogadoSituacao = null;
    mDadosDoUsuario = null;
    mUsuariosSalvos.clear();
    mConsultaDadosUsuario = ServiceStatus.Waiting;
    mStatusConsultaConveniosPagamento = ServiceStatus.Waiting;
    mStatusPontuacaoAluno = ServiceStatus.Waiting;
    await store.record('dadosDoUsuario').delete(getDb);
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.remove('rudapp');
  }

  List<String> get topicosPush {
    final plataforma = kIsWeb
        ? 'WEB'
        : Platform.isAndroid
            ? 'ANDROID'
            : 'IOS';
    final tipoUsuario = isUsuarioColaborador ? 'COLABORADOR' : 'ALUNO';
    final chaveApp = _controladorClienteApp.chave;
    final username = mUsuarioLogado!.username!;
    final codigoCliente = mUsuarioLogado?.codigoCliente;
    final documentKey = _controladorClienteApp.mClienteAppSelecionado!.documentkey;
    final uid = mUsuarioAuth!.uid;
    final codEmpresa = mUsuarioLogado!.codEmpresa;

    return [
      '${chaveApp}_${username}_${plataforma}',
      '${chaveApp}_${codigoCliente}',
      '${documentKey}_${tipoUsuario}_${plataforma}',
      'GLOBAL_${plataforma.toLowerCase()}',
      '${uid}_${plataforma.toLowerCase()}',
      '${chaveApp}_${plataforma}',
      '${chaveApp}_${codEmpresa}',
      '${chaveApp}_${codEmpresa}_${tipoUsuario}',
      '${GetIt.I.get<ControladorApp>().chave}_${codEmpresa}',
      '${GetIt.I.get<ControladorApp>().chave}_${codEmpresa}_${tipoUsuario}',
      tipoUsuario
    ].map(gerarTopicoFirebase).toList();
  }

  String gerarTopicoFirebase(String topico) {
    return topico.replaceAll(RegExp('[^a-zA-Z0-9-_.~%]'), '');
  }

  void inscreverTopicosParaPush() {
    if (!kIsWeb) {
      for (final topico in topicosPush) {
        _firebaseMessaging.subscribeToTopic(topico);
      }
    }
    startUserAnalytics();
    ChatController().escutarTopicoPush();
  }

  Future<void> startUserAnalytics() async {
    try {
      final mUsuarioLogado = GetIt.I.get<ControladorCliente>().mUsuarioLogado;
      FirebaseAnalytics.instance.setUserProperty(name: 'chave_academia_unidade', value: '${_controladorClienteApp.chave}_${(mUsuarioLogado?.codEmpresa ?? 0)}');
      FirebaseAnalytics.instance.setUserId(id: mUsuarioLogado?.username?.replaceAll('@', '_PACTO_'));
      FirebaseAnalytics.instance.setUserProperty(name: 'CHAVE', value: _controladorClienteApp.chave);
      var appVerion = await PackageInfo.fromPlatform();
      FirebaseAnalytics.instance.setUserProperty(name: 'APPVERSION', value: '${appVerion.version}');
      FirebaseAnalytics.instance.setUserProperty(
          name: 'PLATFORM',
          value: '${Platform.isAndroid ? 'ANDROID' : Platform.isIOS ? 'IOS' : 'WEB'}');
      FirebaseAnalytics.instance.setUserProperty(name: 'CODEMPRESA', value: '${mUsuarioLogado?.codEmpresa}');
      FirebaseAnalytics.instance.setUserProperty(name: 'ID_APP', value: '${_controladorClienteApp.mClienteAppSelecionado?.documentkey}');
      if (GetIt.I.get<ControladorCliente>().isUsuarioColaborador) {
        FirebaseAnalytics.instance.setUserProperty(name: 'COD_COLABORADOR', value: '${mUsuarioLogado?.codigoColaborador}');
      } else {
        FirebaseAnalytics.instance.setUserProperty(name: 'MATRICULA', value: '${mUsuarioLogado?.matricula}');
      }
    } catch (e) {}
  }

  void desinscreverTopicoFirebase() {
    try {
      GetIt.I.get<ControladorPushIA>().limparTopicosPushIA();
      ChatController().escutarTopicoPush(limpar: true);
      if (!kIsWeb) {
        for (final topico in topicosPush) {
          _firebaseMessaging.unsubscribeFromTopic(topico);
        }
      }
    } catch (e) {}
  }

  void mandarAvisoPersonal(String titulo, String mensagem) {
    //     _mPersonalService.registrarPushPersonal(PushPersonal(
    //     titulo: titulo,
    //   content: mensagem
    //   ).toJson()).catchError((onError){
    //
    //   });
  }
  num entrou = 0;
  Future<void> alterarFotoDePerfil(Uint8List imagemEmBytes, {Function()? carregando, Function()? sucesso, Function(String? mensage)? falha}) async {
    entrou++;
    carregando?.call();
    int? matricula;
    if (mUsuarioLogado?.matricula != null && mUsuarioLogado!.matricula!.isNotEmpty) {
      matricula = int.parse(mUsuarioLogado!.matricula ?? '0');
    }
    _mServiceAuth
        .alterarFotoPerfil(
      mUsuarioLogado!.username!,
      {'imagem': base64Encode(imagemEmBytes)},
      matricula: matricula,
      usuario: matricula != null ? null : mUsuarioLogado?.codUsuarioZW?.toInt(),
      atualizaFotoZW: false,
    )
        .then((novaURL) {
      mUsuarioLogado?.setprofilePic(novaURL);
      atualizarBanco('usuarioLogado', {'srcImg': novaURL});
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  void alteraSenhaDoUsuario(String novaSenha, {Function()? carregando, Function()? sucesso, Function(String? mensage)? falha}) {
    carregando?.call();
    _mServiceAuth.alterarSenhaUsuario(BodyAlterarSenhaCrypto(email: mUsuarioLogado!.username!, novaSenha: novaSenha)).then((value) {
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  @observable
  bool vaiValidarOCodigoPorEmail = false;
  @observable
  String codigoValidacaoEmail = '';

  void solicitarValidarUsuarioPeloEmail({String? email, Function()? carregando, Function()? sucesso, Function(String? falha)? falha}) {
    carregando?.call();
    vaiValidarOCodigoPorEmail = true;
    var json = {'email': email, 'nomeAcademia': UtilitarioApp.sentenseCaseFirst(mUsuarioLogado?.nomeEmpresa ?? 'academia')};
    _mServiceUsuario.enviarEmailComCodigo(json).then((value) {
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  void validarCodigoUsuarioPeloEmail({String? email, String? codigo, Function()? carregando, Function(bool)? sucesso, Function(String? falha)? falha}) {
    carregando?.call();
    var json = {
      'email': email,
      'codigoValidar': codigo,
    };
    _mServiceUsuario.validarEmailComCodigo(json).then((value) {
      sucesso?.call(value.acessoLiberado ?? false);
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  Future<void> enviarContatoEmailOtp({
    required BodyContatoEmail body,
    Function()? carregando,
    Function()? sucesso,
    Function(String? erro)? falha,
  }) async {
    carregando?.call();
    body.urlLogo = _controladorClienteApp.mClienteAppSelecionado?.urlLogoPrincipal;
    body.corPrincipal = _controladorClienteApp.mClienteAppSelecionado?.corPadrao;
    body.nomeDaEmpresa = mUsuarioLogado!.nomeEmpresa;
    body.nome = mUsuarioLogado!.nome;
    _mServiceAuth.enviarContatoEmailOtp(body).then((value) {
      sucesso?.call();
    }).catchError((x) {
      falha?.call(e.toString());
    });
  }

  Future<void> validarContatoEmailOtp({
    required BodyContatoEmail body,
    Function()? carregando,
    Function()? sucesso,
    Function(String? erro)? falha,
  }) async {
    carregando?.call();
    _mServiceAuth.validarContatoEmailOtp(body).then((value) {
      sucesso?.call();
    }).catchError((e) {
      falha?.call(e.toString());
    });
  }

  Future<void> alterarFotoZW({required Uint8List imagemEmBytes, required String codigoPessoa, Function()? carregando, Function()? sucesso, Function(String? mensage)? falha}) async {
    carregando?.call();
    var imagem = await FlutterImageCompress.compressWithList(
      imagemEmBytes,
      minWidth: 1024,
      minHeight: 1024,
      quality: 100,
    );
    var json = {'fotoBase64': base64Encode(imagem)};
    _mServiceUsuario.alterarFotoAlunoZW(num.parse(codigoPessoa), json).then((value) {
      mUsuarioLogado?.setprofilePic(value);
      sucesso?.call();
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  void alterarDadosDeContatoUsuario(DadosDoUsuario mDadosModificar, {Function()? carregando, Function()? sucesso, Function(String? mensage)? falha}) {
    carregando?.call();
    // TO DO
    num codigoEmail, codigoCelular, codEndereco;
    try {
      codigoEmail = mDadosDoUsuario!.listaEmails?.firstWhere((element) => element.email!.contains(mDadosDoUsuario!.email!)).codigo ?? 0;
    } catch (e) {
      codigoEmail = 0;
    }
    try {
      codigoCelular = mDadosDoUsuario!.listaTelefones?.firstWhere((element) => element.numero!.contains(mDadosDoUsuario!.telCelular!)).codigo ?? 0;
    } catch (e) {
      codigoCelular = 0;
    }
    try {
      codEndereco = mDadosDoUsuario!.listaEnderecos?.firstWhere((element) => element.endereco!.contains(mDadosDoUsuario!.endereco!)).codigo ?? 0;
    } catch (e) {
      codEndereco = 0;
    }

    Future.wait([
      _mServiceAuth.alterarEmailUsuario(codigoEmail, mDadosModificar.email!, true, false, mUsuarioLogado!.matricula!, 4),
      _mServiceAuth.alterarTelefoneUsuario(codigoCelular, 'CE', mDadosModificar.telCelular!, '', false, mUsuarioLogado!.matricula!, 4),
      _mServiceAuth.alterarEnderecoUsuario(codEndereco, 'RE', mDadosModificar.numero!, mDadosModificar.cep!, false, true, mDadosModificar.complemento!, mDadosModificar.bairro!,
          mDadosModificar.endereco!, mUsuarioLogado!.matricula!, 4)
    ]).then((value) {
      consultarDadosDoUsuario(sucesso: sucesso, falha: falha, force: true);
    }).catchError((onError) {
      falha?.call(onError.message);
    });
  }

  bool get mostrarBotaoAddCartao {
    return _controladorClienteApp.getConfiguracaoApp(ModuloApp.MODULO_ALUNO_ADICIONAR_CARTAO).habilitado! &&
        mBandeiras.bandeirasDCC!.isNotEmpty &&
        (mDadosDoUsuario!.listaAutorizacaoCobranca?.where((element) => (element.numeroCartao?.isNotEmpty ?? false)).length ?? 0) < 1;
  }

  void manterCartaoOnline(ListaAutorizacaoCobranca cartaoCobranca, {Function()? sucesso, Function()? carregando, Function(String? falha)? falha}) {
    carregando?.call();
    cartaoCobranca.numeroCartao = cartaoCobranca.numeroCartao?.replaceAll(' ', '');
    if (cartaoCobranca.cvv!.isEmpty) {
      falha?.call('O CVV (código de verificação) do cartão deve ser preenchido');
    } else if (cartaoCobranca.nomeTitularCartao!.isEmpty) {
      falha?.call('O nome do titular do cartão deve ser preenchido');
    } else if (cartaoCobranca.numeroCartao!.isEmpty) {
      falha?.call('O Número do cartão deve ser preenchido');
    } else if (cartaoCobranca.validadeCartao!.isEmpty) {
      falha?.call('A validade do cartão deve ser valida');
    } else {
      cartaoCobranca.operadoraCartao = detectCCType(cartaoCobranca.numeroCartao!).toString().replaceAll('CreditCardType.', '');
      _mServiceAuth
          .incluirAutorizacaoCobrancaCartaoCredito(mUsuarioLogado!.codigoCliente, (cartaoCobranca.operadoraCartao ?? '').toUpperCase(), cartaoCobranca.validadeCartao!, 0,
              cartaoCobranca.numeroCartao!, cartaoCobranca.cvv!, cartaoCobranca.nomeTitularCartao!, true, 12)
          .then((value) {
        consultarDadosDoUsuario(sucesso: sucesso, falha: falha, force: true);
      }).catchError((onError) {
        falha?.call(onError.message.toString().replaceAll('ERRO! ', ''));
      });
    }
  }

  void consultarPontuacaoDoAluno({Function()? sucesso, Function()? carregando, Function(String? falha)? falha}) {
    if (isUsuarioColaborador) {
      mStatusPontuacaoAluno = ServiceStatus.Empty;
    } else {
      mStatusPontuacaoAluno = ServiceStatus.Waiting;
      _mServiceAuth.obterPontosPorCliente(mUsuarioLogado!.matricula!, false).then((value) {
        sucesso?.call();
        if (value.isNotEmpty) mPontuacaoAluno = value.first.totalPontos;
        mStatusPontuacaoAluno = value.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      }).catchError((onError) {
        falha?.call(onError.message);
        mStatusPontuacaoAluno = ServiceStatus.Error;
      });
    }
  }

  void consultarBrindes({Function()? sucesso, Function()? carregando, Function(String? falha)? falha}) {
    if (isUsuarioColaborador) {
      mStatusBrindes = ServiceStatus.Empty;
    } else {
      mStatusBrindes = ServiceStatus.Waiting;
      _mServiceAuth
          .obterDadosBrinde(
        ativo: true,
      )
          .then((value) {
        mBrindes = value;
        mStatusBrindes = value.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
        sucesso?.call();
      }).catchError((onError) {
        falha?.call(onError.message);
        mStatusBrindes = ServiceStatus.Error;
      });
    }
  }

  void consultarConveniosDePagamento({Function()? sucesso, Function()? carregando, Function(String? falha)? falha}) {
    if (!_controladorClienteApp.getConfiguracaoApp(ModuloApp.MODULO_ALUNO_ADICIONAR_CARTAO).habilitado!) {
      mStatusConsultaConveniosPagamento = ServiceStatus.Done;
      return;
    }
    mStatusConsultaConveniosPagamento = ServiceStatus.Waiting;
    _mServiceAuth.obterBandeirasConvenio(mUsuarioLogado!.matricula!).then((convenios) {
      mBandeiras = convenios;
      sucesso?.call();
      mStatusConsultaConveniosPagamento = ServiceStatus.Done;
    }).catchError((onError) {
      if (onError.message.contains('Size: 0')) {
        sucesso?.call();
        mStatusConsultaConveniosPagamento = ServiceStatus.Done;
      } else {
        falha?.call(onError.message);
        mStatusConsultaConveniosPagamento = ServiceStatus.Error;
      }
    });
  }

  String iconeBandeiraCartao(String? bandeira) {
    var b = (bandeira ?? '').toLowerCase();
    if (b.contains('banco')) {
      return 'assets/images/icones/icone_banco.png';
    } else if (b.contains('visa')) {
      return 'assets/images/icones/icone_visa.png';
    } else if (b.contains('master')) {
      return 'assets/images/icones/icone_master.png';
    } else if (b.contains('americam')) {
      return 'assets/images/icones/icone_american_express.png';
    } else if (b.contains('amex')) {
      return 'assets/images/icones/icone_amex.png';
    } else {
      return 'assets/images/icones/icone_cartao_generico.png';
    }
  }

  Future<void> consultarDadosDoUsuario({Function()? carregando, Function()? sucesso, Function(String? mensage)? falha, bool force = false}) async {
    carregando?.call();
    mConsultaDadosUsuario = ServiceStatus.Waiting;
    if (mUsuarioLogado?.treinoIndependente ?? false) {
      Future.delayed(const Duration(seconds: 1), () {
        mDadosDoUsuario = DadosDoUsuario();
        sucesso?.call();
        mConsultaDadosUsuario = ServiceStatus.Done;
      });
      return;
    }
    var dadosFromDb = await buscarNoBanco('dadosDoUsuario');
    if (isUsuarioColaborador) {
      mDadosDoUsuario = DadosDoUsuario();
      sucesso?.call();
      mConsultaDadosUsuario = ServiceStatus.Done;
      return;
    }
    if (dadosFromDb != null) {
      mDadosDoUsuario = DadosDoUsuario.fromJson(dadosFromDb);
    }
    if (!force && mDadosDoUsuario != null) {
      sucesso?.call();
      mConsultaDadosUsuario = ServiceStatus.Done;
    } else {
      _mServiceAuth.consultarDadosCliente(mUsuarioLogado!.matricula!).then((value) {
        mDadosDoUsuario = value;
        sucesso?.call();
        mConsultaDadosUsuario = ServiceStatus.Done;
        inserirNoBanco('dadosDoUsuario', value.toJson());
      }).catchError((onError) {
        falha?.call(onError.message);
        mConsultaDadosUsuario = ServiceStatus.Error;
      });
    }
  }

  ListaAutorizacaoCobranca get convenioAtual {
    // Null safety: checagem explícita
    if (mDadosDoUsuario == null || mDadosDoUsuario!.listaAutorizacaoCobranca == null || mDadosDoUsuario!.listaAutorizacaoCobranca!.isEmpty) {
      return ListaAutorizacaoCobranca();
    }
    var lista = mDadosDoUsuario!.listaAutorizacaoCobranca!;
    if (lista.length == 1) {
      return lista.first;
    }
    for (final element in lista) {
      if (element.numeroCartao != null && element.numeroCartao!.isNotEmpty) {
        return element;
      }
    }
    return lista.first;
  }

  bool get getPermissaoTocarProfessorAluno {
    var possuiPermissao = false;
    if (_controladorPerfil.mPerfilUsuario != null) {
      for (final element in _controladorPerfil.mPerfilUsuario!.perfilUsuario!.funcionalidades!) {
        if (element.nome == 'trocar_professor') {
          possuiPermissao = element.possuiFuncionalidade ?? false;
        }
      }
    }
    return possuiPermissao;
  }

  //Metodo valida se a permissão de Cadastrar Programa pre-definido esta habilitado
  bool get getPermissaoProgramaPreDefinido {
    if (_controladorPerfil.mPerfilUsuario?.perfilUsuario?.recursos == null) {
      return false;
    }
    for (final element in _controladorPerfil.mPerfilUsuario!.perfilUsuario!.recursos!) {
      if (element.recurso == 'programas_predefinidos' && element.tipoPermissoes != null && element.tipoPermissoes!.isNotEmpty) {
        return true;
      }
    }
    return false;
  }

//Metodo valida se a permissão de Cadastrar Ficha pre-definido esta habilitado
  bool get getPermissaoFichaPreDefinido {
    var possuiPermissao = false;
    if (_controladorPerfil.mPerfilUsuario != null) {
      for (final element in _controladorPerfil.mPerfilUsuario!.perfilUsuario!.recursos!) {
        if (element.recurso == 'fichas_pre_definidas') {
          if (element.tipoPermissoes!.isNotEmpty) {
            possuiPermissao = true;
          } else {
            possuiPermissao = false;
          }
        }
      }
    }
    return possuiPermissao;
  }

  //Tornar fucha predefinida
  bool get getPermissaoTornarFichaPreDefinido {
    var possuiPermissao = false;
    if (_controladorPerfil.mPerfilUsuario != null) {
      for (final element in _controladorPerfil.mPerfilUsuario!.perfilUsuario!.funcionalidades!) {
        if (element.nome == 'tornar_ficha_predefinida') {
          possuiPermissao = element.possuiFuncionalidade ?? false;
        }
      }
    }
    return possuiPermissao;
  }

  //Tornar programa pre-definido
  bool get getPermissaoTornarProgramaPreDefinido {
    var possuiPermissao = false;
    if (_controladorPerfil.mPerfilUsuario != null) {
      for (final element in _controladorPerfil.mPerfilUsuario!.perfilUsuario!.funcionalidades!) {
        if (element.nome == 'tornar_programa_predefinido') {
          possuiPermissao = element.possuiFuncionalidade ?? false;
        }
      }
    }
    return possuiPermissao;
  }

  bool _checkPermissao(String recurso, String permissao) {
    if (_controladorPerfil.mPerfilUsuario != null) {
      for (final element in _controladorPerfil.mPerfilUsuario!.perfilUsuario!.recursos!) {
        if (element.recurso == recurso) {
          if (element.tipoPermissoes!.isNotEmpty) {
            for (final perm in element.tipoPermissoes!) {
              if (perm.name == permissao) {
                return true;
              }
            }
          }
        }
      }
    }
    return true;
  }

  bool _checkPermissaoTotal(String recurso) {
    // Verifica se o perfil do usuário existe
    if (_controladorPerfil.mPerfilUsuario?.perfilUsuario?.recursos == null) {
      return false;
    }

    final recursos = _controladorPerfil.mPerfilUsuario!.perfilUsuario!.recursos!;

    // Se a lista de recursos estiver vazia, não tem permissão
    if (recursos.isEmpty) {
      return false;
    }

    // Procura pelo recurso específico
    for (final element in recursos) {
      if (element.recurso == recurso) {
        // Verifica se existem permissões para este recurso
        if (element.tipoPermissoes == null || element.tipoPermissoes!.isEmpty) {
          return false;
        }

        // Verifica se possui permissão TOTAL ou CONSULTAR
        for (final perm in element.tipoPermissoes!) {
          if (perm.name.contains('TOTAL') || perm.name.contains('CONSULTAR')) {
            return true;
          }
        }

        // Se chegou até aqui, encontrou o recurso mas não tem as permissões necessárias
        return false;
      }
    }

    // Se não encontrou o recurso, não tem permissão
    return false;
  }

  bool get getPermissaoProgramaPreDefinidoEditar => _checkPermissao('programas_predefinidos', 'EDITAR');
  bool get getPermissaoEditarNivelAluno => _checkPermissao('niveis', 'EDITAR');
  bool get getPermissaoProgramaPreDefinidoIncluir => _checkPermissao('programas_predefinidos', 'INCLUIR');
  bool get getPermissaoProgramaPreDefinidoExcluir => _checkPermissao('programas_predefinidos', 'EXCLUIR');
  bool get getPermissaoProgramaPreDefinidoConsultar => _checkPermissao('programas_predefinidos', 'CONSULTAR');
  bool get getPermissaoProgramaPreDefinidoTotal => _checkPermissao('programas_predefinidos', 'TOTAL');

  bool get getPermissaoFichaPreDefinidoEditar => _checkPermissao('fichas_pre_definidas', 'EDITAR');
  bool get getPermissaoFichaPreDefinidoIncluir => _checkPermissao('fichas_pre_definidas', 'INCLUIR');
  bool get getPermissaoFichaPreDefinidoExcluir => _checkPermissao('fichas_pre_definidas', 'EXCLUIR');
  bool get getPermissaoFichaPreDefinidoConsultar => _checkPermissao('fichas_pre_definidas', 'CONSULTAR');
  bool get getPermissaoFichaPreDefinidoTotal => _checkPermissao('fichas_pre_definidas', 'TOTAL');

  bool get getPermissaoProgramaTreinoExcluir => _checkPermissao('programa_treino', 'EXCLUIR');
  bool get getPermissaoProgramaTreinoEditar => _checkPermissao('programa_treino', 'EDITAR');
  bool get getPermissaoProgramaTreinoIncluir => _checkPermissao('programa_treino', 'INCLUIR');
  bool get getPermissaoProgramaTreinoConsultar => _checkPermissao('programa_treino', 'CONSULTAR');
  bool get getPermissaoProgramaTreinoTotal => _checkPermissao('programa_treino', 'TOTAL');

  bool get getPermissaoAluno => _checkPermissaoTotal('alunos');

  void consultarHistoricoPresenca({Function()? sucesso, Function()? carregando, Function(String? falha)? falha}) {
    if (isUsuarioColaborador) {
      mStatusHisoricoPresenca = ServiceStatus.Empty;
    } else {
      mStatusHisoricoPresenca = ServiceStatus.Waiting;
      _mServiceAuth.obterHistoricoPresenca(matricula: int.parse(mUsuarioLogado!.matricula!), empresa: mUsuarioLogado!.codEmpresa ?? 1, atualizaCache: true).then((value) {
        mHistoricoPresenca = value;
        mStatusHisoricoPresenca = ServiceStatus.Done;
        sucesso?.call();
      }).catchError((onError) {
        falha?.call(onError.message);
        mStatusHisoricoPresenca = ServiceStatus.Error;
      });
    }
  }
}
