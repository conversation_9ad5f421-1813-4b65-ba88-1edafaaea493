// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorDashBoardPersonal.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorDashBoardPersonal
    on _ControladorDashBoardPersonalBase, Store {
  late final _$mBoardAtom =
      Atom(name: '_ControladorDashBoardPersonalBase.mBoard', context: context);

  @override
  DashBoardTreinoIdependente? get mBoard {
    _$mBoardAtom.reportRead();
    return super.mBoard;
  }

  @override
  set mBoard(DashBoardTreinoIdependente? value) {
    _$mBoardAtom.reportWrite(value, super.mBoard, () {
      super.mBoard = value;
    });
  }

  late final _$mDashboardAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mDashboard', context: context);

  @override
  DashboardNovoTreino? get mDashboard {
    _$mDashboardAtom.reportRead();
    return super.mDashboard;
  }

  @override
  set mDashboard(DashboardNovoTreino? value) {
    _$mDashboardAtom.reportWrite(value, super.mDashboard, () {
      super.mDashboard = value;
    });
  }

  late final _$mStatusDashAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mStatusDash', context: context);

  @override
  ServiceStatus get mStatusDash {
    _$mStatusDashAtom.reportRead();
    return super.mStatusDash;
  }

  @override
  set mStatusDash(ServiceStatus value) {
    _$mStatusDashAtom.reportWrite(value, super.mStatusDash, () {
      super.mStatusDash = value;
    });
  }

  late final _$mStatusDashboardAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mStatusDashboard',
      context: context);

  @override
  ServiceStatus get mStatusDashboard {
    _$mStatusDashboardAtom.reportRead();
    return super.mStatusDashboard;
  }

  @override
  set mStatusDashboard(ServiceStatus value) {
    _$mStatusDashboardAtom.reportWrite(value, super.mStatusDashboard, () {
      super.mStatusDashboard = value;
    });
  }

  late final _$consultouSatisfacaoAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.consultouSatisfacao',
      context: context);

  @override
  bool get consultouSatisfacao {
    _$consultouSatisfacaoAtom.reportRead();
    return super.consultouSatisfacao;
  }

  @override
  set consultouSatisfacao(bool value) {
    _$consultouSatisfacaoAtom.reportWrite(value, super.consultouSatisfacao, () {
      super.consultouSatisfacao = value;
    });
  }

  late final _$consultouExecucoesAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.consultouExecucoes',
      context: context);

  @override
  bool get consultouExecucoes {
    _$consultouExecucoesAtom.reportRead();
    return super.consultouExecucoes;
  }

  @override
  set consultouExecucoes(bool value) {
    _$consultouExecucoesAtom.reportWrite(value, super.consultouExecucoes, () {
      super.consultouExecucoes = value;
    });
  }

  late final _$consultouPrescricoesAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.consultouPrescricoes',
      context: context);

  @override
  bool get consultouPrescricoes {
    _$consultouPrescricoesAtom.reportRead();
    return super.consultouPrescricoes;
  }

  @override
  set consultouPrescricoes(bool value) {
    _$consultouPrescricoesAtom.reportWrite(value, super.consultouPrescricoes,
        () {
      super.consultouPrescricoes = value;
    });
  }

  late final _$biAvaliacaoAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.biAvaliacao', context: context);

  @override
  BiAvaliacao? get biAvaliacao {
    _$biAvaliacaoAtom.reportRead();
    return super.biAvaliacao;
  }

  @override
  set biAvaliacao(BiAvaliacao? value) {
    _$biAvaliacaoAtom.reportWrite(value, super.biAvaliacao, () {
      super.biAvaliacao = value;
    });
  }

  late final _$listaPeriodoAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.listaPeriodo', context: context);

  @override
  List<Periodo?> get listaPeriodo {
    _$listaPeriodoAtom.reportRead();
    return super.listaPeriodo;
  }

  @override
  set listaPeriodo(List<Periodo?> value) {
    _$listaPeriodoAtom.reportWrite(value, super.listaPeriodo, () {
      super.listaPeriodo = value;
    });
  }

  late final _$alunosTreinoVencidoAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.alunosTreinoVencido',
      context: context);

  @override
  List<AlunoSimplificadoCarteira> get alunosTreinoVencido {
    _$alunosTreinoVencidoAtom.reportRead();
    return super.alunosTreinoVencido;
  }

  @override
  set alunosTreinoVencido(List<AlunoSimplificadoCarteira> value) {
    _$alunosTreinoVencidoAtom.reportWrite(value, super.alunosTreinoVencido, () {
      super.alunosTreinoVencido = value;
    });
  }

  late final _$alunosTreinoVencidoBKPAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.alunosTreinoVencidoBKP',
      context: context);

  @override
  List<AlunoSimplificadoCarteira> get alunosTreinoVencidoBKP {
    _$alunosTreinoVencidoBKPAtom.reportRead();
    return super.alunosTreinoVencidoBKP;
  }

  @override
  set alunosTreinoVencidoBKP(List<AlunoSimplificadoCarteira> value) {
    _$alunosTreinoVencidoBKPAtom
        .reportWrite(value, super.alunosTreinoVencidoBKP, () {
      super.alunosTreinoVencidoBKP = value;
    });
  }

  late final _$alunosTreinoAVencerAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.alunosTreinoAVencer',
      context: context);

  @override
  List<AlunoSimplificadoCarteira> get alunosTreinoAVencer {
    _$alunosTreinoAVencerAtom.reportRead();
    return super.alunosTreinoAVencer;
  }

  @override
  set alunosTreinoAVencer(List<AlunoSimplificadoCarteira> value) {
    _$alunosTreinoAVencerAtom.reportWrite(value, super.alunosTreinoAVencer, () {
      super.alunosTreinoAVencer = value;
    });
  }

  late final _$alunosTreinoAVencerBKPAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.alunosTreinoAVencerBKP',
      context: context);

  @override
  List<AlunoSimplificadoCarteira> get alunosTreinoAVencerBKP {
    _$alunosTreinoAVencerBKPAtom.reportRead();
    return super.alunosTreinoAVencerBKP;
  }

  @override
  set alunosTreinoAVencerBKP(List<AlunoSimplificadoCarteira> value) {
    _$alunosTreinoAVencerBKPAtom
        .reportWrite(value, super.alunosTreinoAVencerBKP, () {
      super.alunosTreinoAVencerBKP = value;
    });
  }

  late final _$alunosTreinoEmDiaAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.alunosTreinoEmDia',
      context: context);

  @override
  List<AlunoSimplificadoCarteira> get alunosTreinoEmDia {
    _$alunosTreinoEmDiaAtom.reportRead();
    return super.alunosTreinoEmDia;
  }

  @override
  set alunosTreinoEmDia(List<AlunoSimplificadoCarteira> value) {
    _$alunosTreinoEmDiaAtom.reportWrite(value, super.alunosTreinoEmDia, () {
      super.alunosTreinoEmDia = value;
    });
  }

  late final _$alunosTreinoEmDiaBKPAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.alunosTreinoEmDiaBKP',
      context: context);

  @override
  List<AlunoSimplificadoCarteira> get alunosTreinoEmDiaBKP {
    _$alunosTreinoEmDiaBKPAtom.reportRead();
    return super.alunosTreinoEmDiaBKP;
  }

  @override
  set alunosTreinoEmDiaBKP(List<AlunoSimplificadoCarteira> value) {
    _$alunosTreinoEmDiaBKPAtom.reportWrite(value, super.alunosTreinoEmDiaBKP,
        () {
      super.alunosTreinoEmDiaBKP = value;
    });
  }

  late final _$alunosContratoAtivoAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.alunosContratoAtivo',
      context: context);

  @override
  List<AlunoSimplificadoCarteira> get alunosContratoAtivo {
    _$alunosContratoAtivoAtom.reportRead();
    return super.alunosContratoAtivo;
  }

  @override
  set alunosContratoAtivo(List<AlunoSimplificadoCarteira> value) {
    _$alunosContratoAtivoAtom.reportWrite(value, super.alunosContratoAtivo, () {
      super.alunosContratoAtivo = value;
    });
  }

  late final _$alunosContratoInativoAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.alunosContratoInativo',
      context: context);

  @override
  List<AlunoSimplificadoCarteira> get alunosContratoInativo {
    _$alunosContratoInativoAtom.reportRead();
    return super.alunosContratoInativo;
  }

  @override
  set alunosContratoInativo(List<AlunoSimplificadoCarteira> value) {
    _$alunosContratoInativoAtom.reportWrite(value, super.alunosContratoInativo,
        () {
      super.alunosContratoInativo = value;
    });
  }

  late final _$alunosContratoAVencerAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.alunosContratoAVencer',
      context: context);

  @override
  List<AlunoSimplificadoCarteiraAVencer> get alunosContratoAVencer {
    _$alunosContratoAVencerAtom.reportRead();
    return super.alunosContratoAVencer;
  }

  @override
  set alunosContratoAVencer(List<AlunoSimplificadoCarteiraAVencer> value) {
    _$alunosContratoAVencerAtom.reportWrite(value, super.alunosContratoAVencer,
        () {
      super.alunosContratoAVencer = value;
    });
  }

  late final _$alunosContratoAtivoBKPAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.alunosContratoAtivoBKP',
      context: context);

  @override
  List<AlunoSimplificadoCarteira> get alunosContratoAtivoBKP {
    _$alunosContratoAtivoBKPAtom.reportRead();
    return super.alunosContratoAtivoBKP;
  }

  @override
  set alunosContratoAtivoBKP(List<AlunoSimplificadoCarteira> value) {
    _$alunosContratoAtivoBKPAtom
        .reportWrite(value, super.alunosContratoAtivoBKP, () {
      super.alunosContratoAtivoBKP = value;
    });
  }

  late final _$alunosContratoInativoBKPAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.alunosContratoInativoBKP',
      context: context);

  @override
  List<AlunoSimplificadoCarteira> get alunosContratoInativoBKP {
    _$alunosContratoInativoBKPAtom.reportRead();
    return super.alunosContratoInativoBKP;
  }

  @override
  set alunosContratoInativoBKP(List<AlunoSimplificadoCarteira> value) {
    _$alunosContratoInativoBKPAtom
        .reportWrite(value, super.alunosContratoInativoBKP, () {
      super.alunosContratoInativoBKP = value;
    });
  }

  late final _$alunosContratoAVencerBKPAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.alunosContratoAVencerBKP',
      context: context);

  @override
  List<AlunoSimplificadoCarteiraAVencer> get alunosContratoAVencerBKP {
    _$alunosContratoAVencerBKPAtom.reportRead();
    return super.alunosContratoAVencerBKP;
  }

  @override
  set alunosContratoAVencerBKP(List<AlunoSimplificadoCarteiraAVencer> value) {
    _$alunosContratoAVencerBKPAtom
        .reportWrite(value, super.alunosContratoAVencerBKP, () {
      super.alunosContratoAVencerBKP = value;
    });
  }

  late final _$itemSelecionadoAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.itemSelecionado',
      context: context);

  @override
  String get itemSelecionado {
    _$itemSelecionadoAtom.reportRead();
    return super.itemSelecionado;
  }

  @override
  set itemSelecionado(String value) {
    _$itemSelecionadoAtom.reportWrite(value, super.itemSelecionado, () {
      super.itemSelecionado = value;
    });
  }

  late final _$mListaAlunosAVencerAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mListaAlunosAVencer',
      context: context);

  @override
  List<AlunoSimplificadoCarteira> get mListaAlunosAVencer {
    _$mListaAlunosAVencerAtom.reportRead();
    return super.mListaAlunosAVencer;
  }

  @override
  set mListaAlunosAVencer(List<AlunoSimplificadoCarteira> value) {
    _$mListaAlunosAVencerAtom.reportWrite(value, super.mListaAlunosAVencer, () {
      super.mListaAlunosAVencer = value;
    });
  }

  late final _$filtroAtom =
      Atom(name: '_ControladorDashBoardPersonalBase.filtro', context: context);

  @override
  num get filtro {
    _$filtroAtom.reportRead();
    return super.filtro;
  }

  @override
  set filtro(num value) {
    _$filtroAtom.reportWrite(value, super.filtro, () {
      super.filtro = value;
    });
  }

  late final _$mDadosSatisfacaoAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mDadosSatisfacao',
      context: context);

  @override
  ObservableList<ReportSatisfacao> get mDadosSatisfacao {
    _$mDadosSatisfacaoAtom.reportRead();
    return super.mDadosSatisfacao;
  }

  @override
  set mDadosSatisfacao(ObservableList<ReportSatisfacao> value) {
    _$mDadosSatisfacaoAtom.reportWrite(value, super.mDadosSatisfacao, () {
      super.mDadosSatisfacao = value;
    });
  }

  late final _$mDadosSatisfacaoFiltradosAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mDadosSatisfacaoFiltrados',
      context: context);

  @override
  ObservableList<ReportSatisfacao> get mDadosSatisfacaoFiltrados {
    _$mDadosSatisfacaoFiltradosAtom.reportRead();
    return super.mDadosSatisfacaoFiltrados;
  }

  @override
  set mDadosSatisfacaoFiltrados(ObservableList<ReportSatisfacao> value) {
    _$mDadosSatisfacaoFiltradosAtom
        .reportWrite(value, super.mDadosSatisfacaoFiltrados, () {
      super.mDadosSatisfacaoFiltrados = value;
    });
  }

  late final _$mDadosExecucoesAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mDadosExecucoes',
      context: context);

  @override
  ObservableList<ReportExecucao> get mDadosExecucoes {
    _$mDadosExecucoesAtom.reportRead();
    return super.mDadosExecucoes;
  }

  @override
  set mDadosExecucoes(ObservableList<ReportExecucao> value) {
    _$mDadosExecucoesAtom.reportWrite(value, super.mDadosExecucoes, () {
      super.mDadosExecucoes = value;
    });
  }

  late final _$mDadosExecucoesFiltradosAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mDadosExecucoesFiltrados',
      context: context);

  @override
  ObservableList<ReportExecucao> get mDadosExecucoesFiltrados {
    _$mDadosExecucoesFiltradosAtom.reportRead();
    return super.mDadosExecucoesFiltrados;
  }

  @override
  set mDadosExecucoesFiltrados(ObservableList<ReportExecucao> value) {
    _$mDadosExecucoesFiltradosAtom
        .reportWrite(value, super.mDadosExecucoesFiltrados, () {
      super.mDadosExecucoesFiltrados = value;
    });
  }

  late final _$filtroNotasAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.filtroNotas', context: context);

  @override
  ObservableList<String> get filtroNotas {
    _$filtroNotasAtom.reportRead();
    return super.filtroNotas;
  }

  @override
  set filtroNotas(ObservableList<String> value) {
    _$filtroNotasAtom.reportWrite(value, super.filtroNotas, () {
      super.filtroNotas = value;
    });
  }

  late final _$mDadosPrescricoesAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mDadosPrescricoes',
      context: context);

  @override
  ObservableList<AlunoDeProfessor> get mDadosPrescricoes {
    _$mDadosPrescricoesAtom.reportRead();
    return super.mDadosPrescricoes;
  }

  @override
  set mDadosPrescricoes(ObservableList<AlunoDeProfessor> value) {
    _$mDadosPrescricoesAtom.reportWrite(value, super.mDadosPrescricoes, () {
      super.mDadosPrescricoes = value;
    });
  }

  late final _$treinosExecutadosAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.treinosExecutados',
      context: context);

  @override
  int get treinosExecutados {
    _$treinosExecutadosAtom.reportRead();
    return super.treinosExecutados;
  }

  @override
  set treinosExecutados(int value) {
    _$treinosExecutadosAtom.reportWrite(value, super.treinosExecutados, () {
      super.treinosExecutados = value;
    });
  }

  late final _$treinosPrevistosAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.treinosPrevistos',
      context: context);

  @override
  int get treinosPrevistos {
    _$treinosPrevistosAtom.reportRead();
    return super.treinosPrevistos;
  }

  @override
  set treinosPrevistos(int value) {
    _$treinosPrevistosAtom.reportWrite(value, super.treinosPrevistos, () {
      super.treinosPrevistos = value;
    });
  }

  late final _$mStatusAlunosCarteiraAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mStatusAlunosCarteira',
      context: context);

  @override
  ServiceStatus get mStatusAlunosCarteira {
    _$mStatusAlunosCarteiraAtom.reportRead();
    return super.mStatusAlunosCarteira;
  }

  @override
  set mStatusAlunosCarteira(ServiceStatus value) {
    _$mStatusAlunosCarteiraAtom.reportWrite(value, super.mStatusAlunosCarteira,
        () {
      super.mStatusAlunosCarteira = value;
    });
  }

  late final _$isLastPageAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.isLastPage', context: context);

  @override
  bool get isLastPage {
    _$isLastPageAtom.reportRead();
    return super.isLastPage;
  }

  @override
  set isLastPage(bool value) {
    _$isLastPageAtom.reportWrite(value, super.isLastPage, () {
      super.isLastPage = value;
    });
  }

  late final _$mStatusAlunosCarteiraAVencerAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mStatusAlunosCarteiraAVencer',
      context: context);

  @override
  ServiceStatus get mStatusAlunosCarteiraAVencer {
    _$mStatusAlunosCarteiraAVencerAtom.reportRead();
    return super.mStatusAlunosCarteiraAVencer;
  }

  @override
  set mStatusAlunosCarteiraAVencer(ServiceStatus value) {
    _$mStatusAlunosCarteiraAVencerAtom
        .reportWrite(value, super.mStatusAlunosCarteiraAVencer, () {
      super.mStatusAlunosCarteiraAVencer = value;
    });
  }

  late final _$mStatusAlunosCarteiraEmDiaAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mStatusAlunosCarteiraEmDia',
      context: context);

  @override
  ServiceStatus get mStatusAlunosCarteiraEmDia {
    _$mStatusAlunosCarteiraEmDiaAtom.reportRead();
    return super.mStatusAlunosCarteiraEmDia;
  }

  @override
  set mStatusAlunosCarteiraEmDia(ServiceStatus value) {
    _$mStatusAlunosCarteiraEmDiaAtom
        .reportWrite(value, super.mStatusAlunosCarteiraEmDia, () {
      super.mStatusAlunosCarteiraEmDia = value;
    });
  }

  late final _$mStatusContratoAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mStatusContrato',
      context: context);

  @override
  ServiceStatus get mStatusContrato {
    _$mStatusContratoAtom.reportRead();
    return super.mStatusContrato;
  }

  @override
  set mStatusContrato(ServiceStatus value) {
    _$mStatusContratoAtom.reportWrite(value, super.mStatusContrato, () {
      super.mStatusContrato = value;
    });
  }

  late final _$mStatusContratoAtivoAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mStatusContratoAtivo',
      context: context);

  @override
  ServiceStatus get mStatusContratoAtivo {
    _$mStatusContratoAtivoAtom.reportRead();
    return super.mStatusContratoAtivo;
  }

  @override
  set mStatusContratoAtivo(ServiceStatus value) {
    _$mStatusContratoAtivoAtom.reportWrite(value, super.mStatusContratoAtivo,
        () {
      super.mStatusContratoAtivo = value;
    });
  }

  late final _$mStatusContratoInativoAtom = Atom(
      name: '_ControladorDashBoardPersonalBase.mStatusContratoInativo',
      context: context);

  @override
  ServiceStatus get mStatusContratoInativo {
    _$mStatusContratoInativoAtom.reportRead();
    return super.mStatusContratoInativo;
  }

  @override
  set mStatusContratoInativo(ServiceStatus value) {
    _$mStatusContratoInativoAtom
        .reportWrite(value, super.mStatusContratoInativo, () {
      super.mStatusContratoInativo = value;
    });
  }

  @override
  String toString() {
    return '''
mBoard: ${mBoard},
mDashboard: ${mDashboard},
mStatusDash: ${mStatusDash},
mStatusDashboard: ${mStatusDashboard},
consultouSatisfacao: ${consultouSatisfacao},
consultouExecucoes: ${consultouExecucoes},
consultouPrescricoes: ${consultouPrescricoes},
biAvaliacao: ${biAvaliacao},
listaPeriodo: ${listaPeriodo},
alunosTreinoVencido: ${alunosTreinoVencido},
alunosTreinoVencidoBKP: ${alunosTreinoVencidoBKP},
alunosTreinoAVencer: ${alunosTreinoAVencer},
alunosTreinoAVencerBKP: ${alunosTreinoAVencerBKP},
alunosTreinoEmDia: ${alunosTreinoEmDia},
alunosTreinoEmDiaBKP: ${alunosTreinoEmDiaBKP},
alunosContratoAtivo: ${alunosContratoAtivo},
alunosContratoInativo: ${alunosContratoInativo},
alunosContratoAVencer: ${alunosContratoAVencer},
alunosContratoAtivoBKP: ${alunosContratoAtivoBKP},
alunosContratoInativoBKP: ${alunosContratoInativoBKP},
alunosContratoAVencerBKP: ${alunosContratoAVencerBKP},
itemSelecionado: ${itemSelecionado},
mListaAlunosAVencer: ${mListaAlunosAVencer},
filtro: ${filtro},
mDadosSatisfacao: ${mDadosSatisfacao},
mDadosSatisfacaoFiltrados: ${mDadosSatisfacaoFiltrados},
mDadosExecucoes: ${mDadosExecucoes},
mDadosExecucoesFiltrados: ${mDadosExecucoesFiltrados},
filtroNotas: ${filtroNotas},
mDadosPrescricoes: ${mDadosPrescricoes},
treinosExecutados: ${treinosExecutados},
treinosPrevistos: ${treinosPrevistos},
mStatusAlunosCarteira: ${mStatusAlunosCarteira},
isLastPage: ${isLastPage},
mStatusAlunosCarteiraAVencer: ${mStatusAlunosCarteiraAVencer},
mStatusAlunosCarteiraEmDia: ${mStatusAlunosCarteiraEmDia},
mStatusContrato: ${mStatusContrato},
mStatusContratoAtivo: ${mStatusContratoAtivo},
mStatusContratoInativo: ${mStatusContratoInativo}
    ''';
  }
}
