// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorUsuarioApp.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorUsuarioApp on _ControladorUsuarioAppBase, Store {
  late final _$podeUsarHeatlhAtom =
      Atom(name: '_ControladorUsuarioAppBase.podeUsarHeatlh', context: context);

  @override
  bool get podeUsarHeatlh {
    _$podeUsarHeatlhAtom.reportRead();
    return super.podeUsarHeatlh;
  }

  @override
  set podeUsarHeatlh(bool value) {
    _$podeUsarHeatlhAtom.reportWrite(value, super.podeUsarHeatlh, () {
      super.podeUsarHeatlh = value;
    });
  }

  late final _$pesoAtualUsuarioAtom = Atom(
      name: '_ControladorUsuarioAppBase.pesoAtualUsuario', context: context);

  @override
  num? get pesoAtualUsuario {
    _$pesoAtualUsuarioAtom.reportRead();
    return super.pesoAtualUsuario;
  }

  @override
  set pesoAtualUsuario(num? value) {
    _$pesoAtualUsuarioAtom.reportWrite(value, super.pesoAtualUsuario, () {
      super.pesoAtualUsuario = value;
    });
  }

  late final _$permissaoHealthKitLiberadaAtom = Atom(
      name: '_ControladorUsuarioAppBase.permissaoHealthKitLiberada',
      context: context);

  @override
  bool get permissaoHealthKitLiberada {
    _$permissaoHealthKitLiberadaAtom.reportRead();
    return super.permissaoHealthKitLiberada;
  }

  @override
  set permissaoHealthKitLiberada(bool value) {
    _$permissaoHealthKitLiberadaAtom
        .reportWrite(value, super.permissaoHealthKitLiberada, () {
      super.permissaoHealthKitLiberada = value;
    });
  }

  late final _$quantidadePassosHojeAtom = Atom(
      name: '_ControladorUsuarioAppBase.quantidadePassosHoje',
      context: context);

  @override
  int? get quantidadePassosHoje {
    _$quantidadePassosHojeAtom.reportRead();
    return super.quantidadePassosHoje;
  }

  @override
  set quantidadePassosHoje(int? value) {
    _$quantidadePassosHojeAtom.reportWrite(value, super.quantidadePassosHoje,
        () {
      super.quantidadePassosHoje = value;
    });
  }

  late final _$quantidadeCaloriasHojeAtom = Atom(
      name: '_ControladorUsuarioAppBase.quantidadeCaloriasHoje',
      context: context);

  @override
  double get quantidadeCaloriasHoje {
    _$quantidadeCaloriasHojeAtom.reportRead();
    return super.quantidadeCaloriasHoje;
  }

  @override
  set quantidadeCaloriasHoje(double value) {
    _$quantidadeCaloriasHojeAtom
        .reportWrite(value, super.quantidadeCaloriasHoje, () {
      super.quantidadeCaloriasHoje = value;
    });
  }

  late final _$listaAulasFavoritasAtom = Atom(
      name: '_ControladorUsuarioAppBase.listaAulasFavoritas', context: context);

  @override
  ObservableList<AulasFavoritas> get listaAulasFavoritas {
    _$listaAulasFavoritasAtom.reportRead();
    return super.listaAulasFavoritas;
  }

  @override
  set listaAulasFavoritas(ObservableList<AulasFavoritas> value) {
    _$listaAulasFavoritasAtom.reportWrite(value, super.listaAulasFavoritas, () {
      super.listaAulasFavoritas = value;
    });
  }

  late final _$dadosUsuarioAtom =
      Atom(name: '_ControladorUsuarioAppBase.dadosUsuario', context: context);

  @override
  UsuarioFireBaseManter? get dadosUsuario {
    _$dadosUsuarioAtom.reportRead();
    return super.dadosUsuario;
  }

  @override
  set dadosUsuario(UsuarioFireBaseManter? value) {
    _$dadosUsuarioAtom.reportWrite(value, super.dadosUsuario, () {
      super.dadosUsuario = value;
    });
  }

  late final _$statusConsultaUsuarioAppAtom = Atom(
      name: '_ControladorUsuarioAppBase.statusConsultaUsuarioApp',
      context: context);

  @override
  ServiceStatus get statusConsultaUsuarioApp {
    _$statusConsultaUsuarioAppAtom.reportRead();
    return super.statusConsultaUsuarioApp;
  }

  @override
  set statusConsultaUsuarioApp(ServiceStatus value) {
    _$statusConsultaUsuarioAppAtom
        .reportWrite(value, super.statusConsultaUsuarioApp, () {
      super.statusConsultaUsuarioApp = value;
    });
  }

  late final _$quantidadeMlConsumidoNoDiaAtom = Atom(
      name: '_ControladorUsuarioAppBase.quantidadeMlConsumidoNoDia',
      context: context);

  @override
  num get quantidadeMlConsumidoNoDia {
    _$quantidadeMlConsumidoNoDiaAtom.reportRead();
    return super.quantidadeMlConsumidoNoDia;
  }

  @override
  set quantidadeMlConsumidoNoDia(num value) {
    _$quantidadeMlConsumidoNoDiaAtom
        .reportWrite(value, super.quantidadeMlConsumidoNoDia, () {
      super.quantidadeMlConsumidoNoDia = value;
    });
  }

  late final _$isRespostaAtom =
      Atom(name: '_ControladorUsuarioAppBase.isResposta', context: context);

  @override
  bool get isResposta {
    _$isRespostaAtom.reportRead();
    return super.isResposta;
  }

  @override
  set isResposta(bool value) {
    _$isRespostaAtom.reportWrite(value, super.isResposta, () {
      super.isResposta = value;
    });
  }

  @override
  String toString() {
    return '''
podeUsarHeatlh: ${podeUsarHeatlh},
pesoAtualUsuario: ${pesoAtualUsuario},
permissaoHealthKitLiberada: ${permissaoHealthKitLiberada},
quantidadePassosHoje: ${quantidadePassosHoje},
quantidadeCaloriasHoje: ${quantidadeCaloriasHoje},
listaAulasFavoritas: ${listaAulasFavoritas},
dadosUsuario: ${dadosUsuario},
statusConsultaUsuarioApp: ${statusConsultaUsuarioApp},
quantidadeMlConsumidoNoDia: ${quantidadeMlConsumidoNoDia},
isResposta: ${isResposta}
    ''';
  }
}
