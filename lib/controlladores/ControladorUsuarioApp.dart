import 'dart:io';

import 'package:app_treino/ServiceProvider/UsuarioAppService.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorAvaliacaoFisica.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorNutricao.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/fabricaGetIt.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/Usuario.dart';
import 'package:app_treino/model/geral/UsuarioFireBaseManter.dart';
import 'package:app_treino/model/treinoAluno/BodyCriarTreinoIA.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/util/healt_util.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:health/health.dart';
import 'package:mobx/mobx.dart';
import 'package:permission_handler/permission_handler.dart' as permissao;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

part 'ControladorUsuarioApp.g.dart';

class ControladorUsuarioApp = _ControladorUsuarioAppBase with _$ControladorUsuarioApp;

abstract class _ControladorUsuarioAppBase with Store {
  var mService = GetIt.I.get<UsuarioAppService>();
  final _controllerCliente = GetIt.I.get<ControladorCliente>();

  @observable
  bool podeUsarHeatlh = false;

  @observable
  num? pesoAtualUsuario = 0;

  @observable
  bool permissaoHealthKitLiberada = false;

  List<HealthKitPermissoes> mPermissoesHealth = [];

  @observable
  int? quantidadePassosHoje = 0;

  @observable
  double quantidadeCaloriasHoje = 0;

  List<HealthDataPoint> _healthDataList = [];

  @observable
  ObservableList<AulasFavoritas> listaAulasFavoritas = ObservableList<AulasFavoritas>();

  @observable
  UsuarioFireBaseManter? dadosUsuario = UsuarioFireBaseManter();

  BodyCriarTreinoIa mCriarTreinoAluno = BodyCriarTreinoIa();

  @observable
  ServiceStatus statusConsultaUsuarioApp = ServiceStatus.Waiting;

  @observable
  num quantidadeMlConsumidoNoDia = 0;

  void atualizarConsumoAgua({required Function() sucesso, required Function() definirMeta}) {
    if (((dadosUsuario?.tamanhoCopo ?? 0) <= 0) || (dadosUsuario?.metaHidratacao ?? 0) <= 0) {
      definirMeta();
      return;
    }

    dadosUsuario?.tamanhoCopo ??= 250;

    quantidadeMlConsumidoNoDia = quantidadeMlConsumidoNoDia + (dadosUsuario!.tamanhoCopo ?? 250); //tamanho copo está vindo como nulo

    mService.atualizarConsumoAgua(_controllerCliente.mUsuarioAuth!.uid!, dadosUsuario!.tamanhoCopo ?? 250).then((value) {
      quantidadeMlConsumidoNoDia = somarItens(value);
      sucesso();
    }).catchError((e) {});
  }

  void removerConsumoAgua({required Function() sucesso}) {
    if (quantidadeMlConsumidoNoDia > 0) {
      quantidadeMlConsumidoNoDia = quantidadeMlConsumidoNoDia - (dadosUsuario!.tamanhoCopo ?? 250);
      mService.removerConsumoAgua(_controllerCliente.mUsuarioAuth!.uid!, dadosUsuario!.tamanhoCopo ?? 250).then((value) {
        quantidadeMlConsumidoNoDia = somarItens(value);
        sucesso();
      }).catchError((e) {});
    }
  }

  void consultarConsumoAguaPorData({required DateTime dataInicial, DateTime? dataFinal}) {
    try {
      mService.consultarConsumoAguaPorData(_controllerCliente.mUsuarioAuth!.uid!, dataInicial.toString(), (dataFinal == null ? DateTime.now() : dataFinal).toString()).then((value) {
        quantidadeMlConsumidoNoDia = somarItens(value);
      }).catchError((e) {});
    } catch (e) {}
  }

  void marcarAulaComoFavorita({required num codigo, required bool favorita, required Function carregando, required Function error, required Function sucesso}) {
    carregando();
    listaAulasFavoritas.clear();
    mService.marcarAulaComoFavorita(_controllerCliente.mUsuarioAuth!.uid!, codigo, favorita).then((value) {
      listaAulasFavoritas.addAll(value);
      sucesso();
    }).catchError((e) {
      error();
    });
  }

  void consultarAulasFavoritas() {
    listaAulasFavoritas.clear();
    mService.consultarAulasFavoritas(_controllerCliente.mUsuarioAuth!.uid!).then((value) {
      listaAulasFavoritas.addAll(value);
    }).catchError((e) {});
  }

  int somarItens(List<HistoricoHidratacao> numbers) {
    int sum = 0;
    for (final i in numbers) {
      if (i.adicao ?? true) {
        sum = sum + (i.ml as int);
      } else {
        sum = sum - (i.ml as int);
      }
    }
    return sum < 0 ? 0 : sum;
  }

  atualizarWidgetsSaude({required Function() sucesso, required Function() falha, required Function() carregando}) async {
    statusConsultaUsuarioApp = ServiceStatus.Waiting;
    carregando();
    var json = WidgetsSaude(
            agua: dadosUsuario?.widgetsSaude?.agua ?? true,
            calorias: dadosUsuario?.widgetsSaude?.calorias ?? true,
            cronometro: dadosUsuario?.widgetsSaude?.cronometro ?? true,
            passos: dadosUsuario?.widgetsSaude?.passos ?? true,
            personalRecords: dadosUsuario?.widgetsSaude?.personalRecords ?? true,
            peso: dadosUsuario?.widgetsSaude?.peso ?? true)
        .toJson();
    var teste = {'usuarioApp': _controllerCliente.mUsuarioAuth!.uid!, 'widgetsSaude': json};
    mService.habilitarWidgetsSaude(teste).then((value) {
      statusConsultaUsuarioApp = ServiceStatus.Done;
      sucesso();
    }).catchError((e) {
      falha();
    });
  }

  atualizarDadosUsuario({required Function() sucesso, required Function() falha, required Function() carregando, UsuarioFireBaseManter? dadosUsuarioAtualizar}) async {
    statusConsultaUsuarioApp = ServiceStatus.Waiting;
    carregando();
    var json = dadosUsuarioAtualizar?.toJson() ??
        UsuarioFireBaseManter(
          altura: dadosUsuario?.altura ?? 170,
          userDeviceId: (modoHomologacao || kDebugMode) ? 'NOTSAVE' : thedeviceID,
          nomeNoAplicativo: GetIt.I.get<ControladorApp>().mClienteAppSelecionado?.nomeDoApp ?? '',
          codigoCliente: _controllerCliente.mUsuarioLogado!.codigoCliente ?? -1,
          matricula: _controllerCliente.mUsuarioLogado!.matricula ?? -1 as String?,
          codigoProfessor: _controllerCliente.mUsuarioLogado!.codigoProfessor as int? ?? -1,
          usuarioMovel: _controllerCliente.mUsuarioLogado!.username ?? _controllerCliente.mUsuarioLogado!.email,
          nomeUnidade: _controllerCliente.mUsuarioLogado!.nomeEmpresa ?? '',
          pesoAtual: ((dadosUsuario?.pesoAtual ?? 700)),
          atividadeFisica: dadosUsuario?.atividadeFisica ?? 1,
          dataNascimento: dadosUsuario?.dataNascimento ?? DateTime.now().subtract(const Duration(days: 6750)),
          genero: dadosUsuario?.genero ?? 'outro',
        ).toJson();

    json.addAll({'usuarioApp': _controllerCliente.mUsuarioAuth!.uid!});
    mService.atualizarDadosSaude(json).then((value) {
      statusConsultaUsuarioApp = ServiceStatus.Done;
      sucesso();
    }).catchError((e) {
      falha();
    });
  }

  consultarDadosUsuarioAPP({required Function() responderAnamnese, required Function() sucesso, required Function() falha, required Function() carregando}) async {
    statusConsultaUsuarioApp = ServiceStatus.Waiting;
    final controladorAval = GetIt.I.get<ControladorAvaliacaoFisica>();
    mService.consultarDadosUsuario(_controllerCliente.mUsuarioAuth!.uid!).then((value) {
      dadosUsuario = value;
      controladorAval.dadosBasicos.altura = value.altura;
      controladorAval.dadosBasicos.peso = value.pesoAtual ?? 10;
      controladorAval.dadosBasicos.nivelAtividade = value.atividadeFisica;
      controladorAval.dadosBasicos.sexo_biologico = value.genero;
      pesoAtualUsuario = value.pesoAtual ?? 10;
      if (value.widgetsSaude == null) {
        dadosUsuario?.widgetsSaude = WidgetsSaude(agua: true, calorias: true, cronometro: true, passos: true, personalRecords: true, peso: true);
        if (!isResposta) {
          responderAnamnese();
        }
      } else {
        if (!isResposta) {
          responderAnamnese();
        } else {
          sucesso();
        }
      }
      statusConsultaUsuarioApp = ServiceStatus.Done;
    }).catchError((e) {
      falha.call();
      statusConsultaUsuarioApp = ServiceStatus.Done;
    });
  }

  @observable
  bool isResposta = false;

  usuarioRespondeuAnamnese(bool selecionado) async {
    SharedPreferences mRespostaAnamnese = await SharedPreferences.getInstance();
    await mRespostaAnamnese.setBool('usuario_respondeu_anamnese', selecionado);
    isResposta = selecionado;
  }

  void consultarUsuarioRespondeuAnamnese() {
    SharedPreferences.getInstance().then((value) {
      isResposta = value.getBool('usuario_respondeu_anamnese') ?? false;
    });
  }

  atualizarPesoUsuario({Function()? atingiuMetaPeso}) async {
    mService.atualizarPesoUsuario(_controllerCliente.mUsuarioAuth!.uid!, double.parse(pesoAtualUsuario!.toStringAsFixed(1))).then((value) {
      dadosUsuario?.pesoAtual = pesoAtualUsuario;
    }).catchError((e) {});
  }

  void configureHealthKit(Function() callback, {bool chamarView = false}) async {
    if (Platform.isIOS) {
      podeUsarHeatlh = true;
    } else {
      podeUsarHeatlh = !(await UtilitarioApp.isAndroidVersionBelowOrEqual10());
    }
    if (!podeUsarHeatlh || !chamarView) {
      return;
    }
    Health().getHealthConnectSdkStatus();
    Health().configure().then((value) {
      callback();
    }).catchError((onError) {
      if (kDebugMode) {
        print(onError);
      }
    });
  }

  Future<void> solicitarPermissaoHealthKit({bool deuPermissao = false}) async {
    quantidadePassosHoje = 0;
    List<HealthDataType> types = [
      HealthDataType.STEPS,
      HealthDataType.SLEEP_IN_BED,
      HealthDataType.SLEEP_ASLEEP,
      HealthDataType.SLEEP_AWAKE,
      HealthDataType.ACTIVE_ENERGY_BURNED,
      HealthDataType.HEART_RATE,
      if (Platform.isIOS) HealthDataType.DISTANCE_WALKING_RUNNING,
      if (Platform.isAndroid) HealthDataType.DISTANCE_DELTA
    ];

    if (Platform.isIOS) {
      Health().configure();
      Health().getHealthConnectSdkStatus();
      types = [
        HealthDataType.ACTIVE_ENERGY_BURNED,
        HealthDataType.HEART_RATE,
        HealthDataType.SLEEP_IN_BED,
        HealthDataType.SLEEP_ASLEEP,
        HealthDataType.SLEEP_AWAKE,
        HealthDataType.STEPS,
        HealthDataType.DISTANCE_WALKING_RUNNING
      ];
    }

    try {
      for (final type in types) {
        mPermissoesHealth.add(HealthKitPermissoes(type: type, habilitada: await Health().hasPermissions([type])));
      }
    } catch (e) {
      if (e.toString().contains('Google Health Connect is not available')) {
        DSalerta().exibirAlerta(
            context: context,
            titulo: 'Google Health',
            subtitulo: 'Para utilizar o recurso, precisamos que tenha o app Google Health Connect',
            tituloBotao: 'Baixar via google Play',
            tituloBotaoSecundario: 'voltar',
            onTap: () {
              launchUrl(Uri.parse('https://play.google.com/store/apps/details?id=com.google.android.apps.healthdata')).then((value) {
                solicitarPermissaoHealthKit(deuPermissao: deuPermissao);
              }).catchError((e) {});
            });
      }
    }

    permissaoHealthKitLiberada = mPermissoesHealth.any((x) => x.habilitada ?? false);
    // if (Platform.isIOS) {
    //   permissaoHealthKitLiberada = await Health().isHealthConnectAvailable();
    // }

    if (!permissaoHealthKitLiberada) {
      if (!deuPermissao) {
        DSalerta().exibirAlerta(
          context: GetIt.I.get<NavigationService>().context,
          titulo: 'heatlh_titulo',
          subtitulo: 'heatlh_content',
          tituloBotao: 'acao_permitir',
          tituloBotaoSecundario: 'cancel',
          tipoAlerta: TipoAlerta.erroSemDisclaimer,
          textoAlerta: '',
          onTap: () async {
            Navigator.of(GetIt.I.get<NavigationService>().context).pop();
            HealthPermissionsUtil().requestPermissions().then((onValue) {
              if (!onValue) {
                DSalerta().exibirAlertaSimplificado(
                  context: GetIt.I.get<NavigationService>().context,
                  titulo: 'Ops! Permissões foram negadas',
                  subtitulo: 'Para permitir acesse as configurações do app e habilite diretamente nas permissões do app',
                  tituloBotao: 'conceder',
                  tipoAlerta: TipoAlerta.erroSemDisclaimer,
                  onTap: () async {
                    await permissao.openAppSettings().then((x) {
                      Navigator.of(GetIt.I.get<NavigationService>().context).pop();
                    }).catchError((onError) {
                      Navigator.of(GetIt.I.get<NavigationService>().context).pop();
                    });
                  },
                );
              } else {
                solicitarPermissaoHealthKit(deuPermissao: true);
              }
            }).catchError((onError) {
              if (kDebugMode) {
                print(onError);
              }
            });
          },
        );
      } else {
        Health().requestAuthorization(types).then((autorizado) {
          permissaoHealthKitLiberada = autorizado;
        }).catchError((e) {
          DSalerta().exibirAlertaSimplificado(
            context: GetIt.I.get<NavigationService>().context,
            titulo: 'erro',
            subtitulo: '${e.toString()}',
            tituloBotao: 'ok',
          );
          if (kDebugMode) {
            print('Erro ao solicitar permissão HealthKit: $e');
          }
          permissaoHealthKitLiberada = false;
        });
      }
    }
  }

  Future consultarPassosUsuarioHealthKit() async {
    _healthDataList.clear();
    quantidadePassosHoje = 0;
    quantidadeCaloriasHoje = 0;
    DateTime startDate = UtilDataHora.getDataComHorasPersonalizada(dateTime: DateTime.now(), horas: 0, minutos: 0, segundos: 0)!;
    DateTime endDate = UtilDataHora.getDataComHorasPersonalizada(dateTime: DateTime.now(), horas: 23, minutos: 59, segundos: 59)!;
    List<HealthDataType> types = [HealthDataType.STEPS, HealthDataType.ACTIVE_ENERGY_BURNED];
    if (permissaoHealthKitLiberada) {
      List<HealthDataPoint> healthData = await Health().getHealthDataFromTypes(types: types, startTime: startDate, endTime: endDate);
      _healthDataList = Health().removeDuplicates(healthData);
      final listaKcal = _healthDataList.where((element) => element.type == HealthDataType.ACTIVE_ENERGY_BURNED).toList();
      for (final x in listaKcal) {
        try {
          quantidadeCaloriasHoje += double.parse(x.value.toString().split(': ')[1]);
        } catch (e) {
          quantidadeCaloriasHoje += 0;
        }
      }
      quantidadePassosHoje = await Health().getTotalStepsInInterval(startDate, endDate);
    }
  }
}
