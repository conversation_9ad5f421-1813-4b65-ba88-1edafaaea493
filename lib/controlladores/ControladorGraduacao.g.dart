// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorGraduacao.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorGraduacao on _ControladorGraduacaoBase, Store {
  late final _$mStatusConsultaFichasGraduacaoAtom = Atom(
      name: '_ControladorGraduacaoBase.mStatusConsultaFichasGraduacao',
      context: context);

  @override
  ServiceStatus get mStatusConsultaFichasGraduacao {
    _$mStatusConsultaFichasGraduacaoAtom.reportRead();
    return super.mStatusConsultaFichasGraduacao;
  }

  @override
  set mStatusConsultaFichasGraduacao(ServiceStatus value) {
    _$mStatusConsultaFichasGraduacaoAtom
        .reportWrite(value, super.mStatusConsultaFichasGraduacao, () {
      super.mStatusConsultaFichasGraduacao = value;
    });
  }

  late final _$mStatusConsultaAtividadesAtom = Atom(
      name: '_ControladorGraduacaoBase.mStatusConsultaAtividades',
      context: context);

  @override
  ServiceStatus get mStatusConsultaAtividades {
    _$mStatusConsultaAtividadesAtom.reportRead();
    return super.mStatusConsultaAtividades;
  }

  @override
  set mStatusConsultaAtividades(ServiceStatus value) {
    _$mStatusConsultaAtividadesAtom
        .reportWrite(value, super.mStatusConsultaAtividades, () {
      super.mStatusConsultaAtividades = value;
    });
  }

  @override
  String toString() {
    return '''
mStatusConsultaFichasGraduacao: ${mStatusConsultaFichasGraduacao},
mStatusConsultaAtividades: ${mStatusConsultaAtividades}
    ''';
  }
}
