// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ControladorBeberAgua.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ControladorBeberAgua on _ControladorBeberAguaBase, Store {
  late final _$mCarregouDadosUsuarioAtom = Atom(
      name: '_ControladorBeberAguaBase.mCarregouDadosUsuario',
      context: context);

  @override
  ServiceStatus get mCarregouDadosUsuario {
    _$mCarregouDadosUsuarioAtom.reportRead();
    return super.mCarregouDadosUsuario;
  }

  @override
  set mCarregouDadosUsuario(ServiceStatus value) {
    _$mCarregouDadosUsuarioAtom.reportWrite(value, super.mCarregouDadosUsuario,
        () {
      super.mCarregouDadosUsuario = value;
    });
  }

  late final _$mInseriuRegistroAguaAtom = Atom(
      name: '_ControladorBeberAguaBase.mInseriuRegistroAgua', context: context);

  @override
  ServiceStatus get mInseriuRegistroAgua {
    _$mInseriuRegistroAguaAtom.reportRead();
    return super.mInseriuRegistroAgua;
  }

  @override
  set mInseriuRegistroAgua(ServiceStatus value) {
    _$mInseriuRegistroAguaAtom.reportWrite(value, super.mInseriuRegistroAgua,
        () {
      super.mInseriuRegistroAgua = value;
    });
  }

  late final _$mCarregouHistoricoAtom = Atom(
      name: '_ControladorBeberAguaBase.mCarregouHistorico', context: context);

  @override
  ServiceStatus get mCarregouHistorico {
    _$mCarregouHistoricoAtom.reportRead();
    return super.mCarregouHistorico;
  }

  @override
  set mCarregouHistorico(ServiceStatus value) {
    _$mCarregouHistoricoAtom.reportWrite(value, super.mCarregouHistorico, () {
      super.mCarregouHistorico = value;
    });
  }

  late final _$mListaSemanasAtom =
      Atom(name: '_ControladorBeberAguaBase.mListaSemanas', context: context);

  @override
  ObservableList<SemanaAgua> get mListaSemanas {
    _$mListaSemanasAtom.reportRead();
    return super.mListaSemanas;
  }

  @override
  set mListaSemanas(ObservableList<SemanaAgua> value) {
    _$mListaSemanasAtom.reportWrite(value, super.mListaSemanas, () {
      super.mListaSemanas = value;
    });
  }

  @override
  String toString() {
    return '''
mCarregouDadosUsuario: ${mCarregouDadosUsuario},
mInseriuRegistroAgua: ${mInseriuRegistroAgua},
mCarregouHistorico: ${mCarregouHistorico},
mListaSemanas: ${mListaSemanas}
    ''';
  }
}
