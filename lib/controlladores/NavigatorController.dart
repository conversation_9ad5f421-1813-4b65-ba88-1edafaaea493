import 'package:app_treino/util/util_nav_history.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
// // import 'package:smartlook/smartlook.dart';

class NavigationService {
  String? ultimaTelaAberta = '';
  static Future<T> pushNamed<T extends Object>(String routeName, {required Object arguments}) {
    GetIt.I.get<NavigationService>().ultimaTelaAberta = routeName;
    // // Smartlook.trackNavigationEvent(routeName, SmartlookNavigationEventType.enter);
    return GetIt.I.get<NavigationService>().navigatorKey.currentState!.pushNamed(routeName, arguments: arguments).then((value) {
      GetIt.I.get<NavigationService>().ultimaTelaAberta = null;
      //   // Smartlook.trackNavigationEvent(routeName, SmartlookNavigationEventType.exit);
      return value;
    }).catchError((onError) {
      return onError;
    }).then((value) => value as T);
  }

  static popUntil(bool Function(Route<dynamic>) predicate) {
    // // Smartlook.trackNavigationEvent(GetIt.I.get<NavigationService>().ultimaTelaAberta!, SmartlookNavigationEventType.exit);
    return GetIt.I.get<NavigationService>().navigatorKey.currentState!.popUntil(predicate);
  }

  static pop<T extends Object>([T? result]) {
    // // Smartlook.trackNavigationEvent(GetIt.I.get<NavigationService>().ultimaTelaAberta!, SmartlookNavigationEventType.exit);
    return GetIt.I.get<NavigationService>().navigatorKey.currentState!.pop();
  }

  static Future<T> popAndPushNamed<T extends Object, TO extends Object>(String routeName, {required TO result, required Object arguments}) {
    // // Smartlook.trackNavigationEvent(GetIt.I.get<NavigationService>().ultimaTelaAberta!, SmartlookNavigationEventType.exit);
    GetIt.I.get<NavigationService>().ultimaTelaAberta = routeName;
    return GetIt.I.get<NavigationService>().navigatorKey.currentState!.popAndPushNamed(routeName, result: result, arguments: arguments).then((value) {
      //   // Smartlook.trackNavigationEvent(routeName, SmartlookNavigationEventType.exit);
      return value;
    }).catchError((onError) {
      return onError;
    }).then((value) => value as T);
  }

  static Future<T?> pushAndRemoveUntil<T extends Object>(Route<T> newRoute, bool Function(Route<dynamic>) predicate) {
    // // Smartlook.trackNavigationEvent(newRoute.settings.name!, SmartlookNavigationEventType.enter);
    GetIt.I.get<NavigationService>().ultimaTelaAberta = newRoute.settings.name;
    return GetIt.I.get<NavigationService>().navigatorKey.currentState!.pushAndRemoveUntil(newRoute, predicate).then((value) {
      //   // Smartlook.trackNavigationEvent(GetIt.I.get<NavigationService>().ultimaTelaAberta!, SmartlookNavigationEventType.exit);
      return value;
    }).catchError((onError) {
      return onError;
    });
  }

  static Future<T> pushReplacementNamed<T extends Object, TO extends Object>(String routeName, {required TO result, required Object arguments}) {
    return GetIt.I.get<NavigationService>().navigatorKey.currentState!.pushReplacementNamed(routeName, result: result, arguments: arguments).then((value) {
      return value;
    }).catchError((onError) {
      return onError;
    }).then((value) => value as T);
  }

  get context => navigatorKey.currentState!.context;
  final GlobalKey<NavigatorState> navigatorKey = new GlobalKey<NavigatorState>();
  Future<dynamic> navigateTo(String routeName) {    
    final currentRoute = UserActivityTracker.lastValidScreenName;
    if (currentRoute != routeName) {
      return navigatorKey.currentState!.pushNamed(routeName);
    }
    return Future.value(null);
  }

  void goBack({String? routeName}) {
    if (routeName != null) {
      return Navigator.popUntil(navigatorKey.currentContext!, ModalRoute.withName(routeName));
    } else {
      return navigatorKey.currentState!.pop();
    }
  }
}
