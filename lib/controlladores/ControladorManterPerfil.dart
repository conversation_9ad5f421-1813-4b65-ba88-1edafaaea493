import 'dart:convert';
import 'dart:typed_data';

import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/DadosUsuario.dart';
import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import 'package:mobx/mobx.dart';

part 'ControladorManterPerfil.g.dart';

class ControladorManterPerfil = _ControladorManterPerfilBase with _$ControladorManterPerfil;

abstract class _ControladorManterPerfilBase with Store {
  var mService = GetIt.I.get<PersonalService>();

  var mCliente = GetIt.I.get<ControladorCliente>();

  @observable
  DadosUsuario? mUsuarioManter;

  @observable
  Uint8List? unitImageFoto;

  @observable
  String? urlFotoPersonal;

  void clean() {
    unitImageFoto = null;
    nome = '';
    dataNascimento = '';
    sexo = '';
    celular = '';
    email = '';
    foto = '';
    foto = '';
    urlFotoPersonal = '';
    mUsuarioManter = DadosUsuario();
  }

  @observable
  String? nome = '';
  @observable
  String dataNascimento = '';
  @observable
  String sexo = '';
  @observable
  String? celular = '';
  @observable
  String? email = '';
  @observable
  String? foto = '';
  @observable
  int? codigo = 0;
  @observable
  String? situacao;

  void setnome(String? value) {
    nome = value;
    mUsuarioManter!.nome = value;
  }

  void setCodigo(int? value) {
    codigo = value;
    mUsuarioManter!.codigo = value;
  }

  void setNascimento(String value) {
    dataNascimento = value;
    DateFormat format = DateFormat('dd/MM/yyyy');
    mUsuarioManter!.dataNascimento = format.parse(value).millisecondsSinceEpoch;
  }

  void setcelular(String? value) {
    celular = value;
    mUsuarioManter!.celular = celular;
  }

  void setemail(String? value) {
    email = value;
    mUsuarioManter!.email = email;
  }

  void setimagemData(String value) {
    foto = value.contains('fotoPadrao') ? null : value;
    mUsuarioManter!.foto = value.contains('fotoPadrao') ? null : value;
  }

  void setSexo(bool masc) {
    sexo = masc ? 'M' : 'F';
    mUsuarioManter!.sexo = masc ? 'M' : 'F';
  }

  editarPersonal({required Function()? carregando, Function()? sucesso, Function(String? erro)? erro}) {
    if (unitImageFoto != null && unitImageFoto!.isNotEmpty) {
      mUsuarioManter!.foto = const Base64Codec().encode(unitImageFoto!);
    } else {
      mUsuarioManter!.foto = null;
    }
    Map<String, dynamic> json = mUsuarioManter!.toJson();
    if (json['foto'] != null) {
      json.addAll({'imagemData': json['foto']});
    }
    json.addAll({'telefone': json['celular']});
    json.remove('celular');
    json.remove('foto');
    json.remove('codigo');
    json.remove('sexo');
    json.remove('dataNascimento');
    carregando?.call();
    mService.salvarPersonal(mUsuarioManter!.codigo!, json).then((retorno) async {
      Map novosDados = Map();
      if (retorno.nome != null) {
        mCliente.mUsuarioLogado!.nome = retorno.nome;
        novosDados['nome'] = retorno.nome;
      }
      if (retorno.email != null) {
        mCliente.mUsuarioLogado!.username = retorno.email;
        novosDados['username'] = retorno.email;
      }
      if (retorno.telefone != null) {
        mCliente.mUsuarioLogado!.telefone = retorno.telefone;
        novosDados['telefone'] = retorno.telefone;
      }
      if (retorno.imagemData != null) {
        mCliente.mUsuarioLogado!.setprofilePic(retorno.imagemData);
        novosDados['srcImg'] = retorno.imagemData;
      }
      mCliente.atualizarBanco('usuarioLogado', novosDados);
      sucesso!();
    }).catchError((onError) {
      erro!(onError.message);
    });
  }

  editarAluno({required Function()? carregando, Function()? sucesso, Function(String? erro)? erro}) {
    if (unitImageFoto != null && unitImageFoto!.isNotEmpty) {
      mUsuarioManter!.foto = const Base64Codec().encode(unitImageFoto!);
    } else {
      mUsuarioManter!.foto = null;
    }
    Map<String, dynamic> json = mUsuarioManter!.toJson();
    if (json['foto'] != null) {
      json.addAll({'imagemData': json['foto']});
    }

    json.remove('foto');
    json.remove('codigo');

    carregando?.call();
    mService.manterAluno(mCliente.mUsuarioLogado!.codigoCliente, json).then((retorno) {
      Map novosDados = Map();
      if (retorno.nome != null) {
        mCliente.mUsuarioLogado!.nome = retorno.nome;
        novosDados['nome'] = retorno.nome;
      }
      if (retorno.email != null) {
        mCliente.mUsuarioLogado!.username = retorno.email;
        novosDados['username'] = retorno.email;
      }
      if (retorno.celular != null) {
        mCliente.mUsuarioLogado!.telefone = retorno.celular;
        novosDados['telefone'] = retorno.celular;
      }
      if (retorno.imagemData != null) {
        mCliente.mUsuarioLogado!.setprofilePic(retorno.imagemData);
        novosDados['srcImg'] = retorno.imagemData;
      }
      mCliente.atualizarBanco('usuarioLogado', novosDados);
      sucesso!();
    }).catchError((onError) {
      erro!(onError.message);
    });
  }

  limparTudo() {
    clean();
  }
}
