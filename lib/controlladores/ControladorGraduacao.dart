import 'dart:core';

import 'package:app_treino/ServiceProvider/TreinoService.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/graduacao/ResultadoGraduacao.dart';
import 'package:app_treino/model/graduacao/NivelGraduacao.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:get_it/get_it.dart';
import 'package:mobx/mobx.dart';
part 'ControladorGraduacao.g.dart';

class ControladorGraduacao = _ControladorGraduacaoBase with _$ControladorGraduacao;

abstract class _ControladorGraduacaoBase with Store {
  final _mService = GetIt.I.get<TreinoService>();
  final _controladorCliente = GetIt.I.get<ControladorCliente>();

  @observable
  ServiceStatus mStatusConsultaFichasGraduacao = ServiceStatus.Waiting;

  @observable
  ServiceStatus mStatusConsultaAtividades = ServiceStatus.Waiting;

  List<ResultadoGraduacao> mListasResultadoGraduacao = [];

  /* 
  trocarValorTeste() {
    teste = 'valor';
  }

*/

  void consultarFichasGraduacaoAluno({Function()? sucesso, Function()? carregando, Function(String erro)? falha}) {
    carregando?.call();

    mStatusConsultaFichasGraduacao = ServiceStatus.Waiting;

    //CONFIRMAR QUAL CODIGO COM O ALCIDES
    _mService.obterFichasGraduacaoAluno(_controladorCliente.mUsuarioLogado!.codigoCliente).then((resultadoGraduacao) {
      resultadoGraduacao.sort((a, b) => a.ficha!.id!.compareTo(b.ficha!.id!));
      mListasResultadoGraduacao.clear();
      mListasResultadoGraduacao.addAll(resultadoGraduacao);
      sucesso?.call();
      mStatusConsultaFichasGraduacao = resultadoGraduacao.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;

      //  teste = resultadoGraduacao[0].ficha.id.toString();

      //resultadoGraduacao[0].ficha.niveis[0].tecnica
    }).catchError((onError) {
      if (onError?.message?.toString().contains('No element') ?? false) {
        mStatusConsultaFichasGraduacao = ServiceStatus.Empty;
        sucesso?.call();
      } else {
        mStatusConsultaFichasGraduacao = ServiceStatus.Error;
        falha?.call('Houve um erro');
      }
    });
  }

  bool isNivelConcluido(ResultadoGraduacao resultadoGraduacao, NivelGraduacao nivelGraduacao) {
    var nivelAtual = resultadoGraduacao.ficha!.niveis!.firstWhere((element) => element.id == resultadoGraduacao.nivelId);

    var atualTemporaria = (nivelAtual.index! - 1);

    if (nivelGraduacao.ativo == true) {
      return nivelGraduacao.index == atualTemporaria;
    } else {
      return false;
    }
  }

  bool isNivelConcluidoAnterior(ResultadoGraduacao resultadoGraduacao, NivelGraduacao nivelGraduacao) {
    var nivelAtual = resultadoGraduacao.ficha!.niveis!.firstWhere((element) => element.id == resultadoGraduacao.nivelId);

    var atualTemporaria = (nivelAtual.index! - 2);

    if (nivelGraduacao.ativo == true) {
      return nivelGraduacao.index == atualTemporaria;
    } else {
      return false;
    }
  }

  bool isNiveisConcluidoAnterior(ResultadoGraduacao resultadoGraduacao, NivelGraduacao nivelGraduacao) {
    var nivelAtual = resultadoGraduacao.ficha!.niveis!.firstWhere((element) => element.id == resultadoGraduacao.nivelId);

    if (nivelGraduacao.ativo == true) {
      return nivelGraduacao.index! < nivelAtual.index!;
    } else {
      return false;
    }
  }

  bool isFaixasConcluidoAnterior(ResultadoGraduacao resultadoGraduacao, NivelGraduacao nivelGraduacao) {
    var nivelAtual = resultadoGraduacao.ficha!.niveis!.firstWhere((element) => element.id == resultadoGraduacao.nivelId);

    var atualTemporaria = (nivelAtual.index! - 1);

    if (nivelGraduacao.ativo == true) {
      return nivelGraduacao.index! < atualTemporaria;
    } else {
      return false;
    }
  }

  bool isNiveisTodosProximos(ResultadoGraduacao resultadoGraduacao, NivelGraduacao nivelGraduacao) {
    var nivelAtual = resultadoGraduacao.ficha!.niveis!.firstWhere((element) => element.id == resultadoGraduacao.nivelId);

    if (nivelGraduacao.ativo == true) {
      return nivelGraduacao.index! > nivelAtual.index!;
    } else {
      return false;
    }
  }

  bool isProximoNivel(ResultadoGraduacao resultadoGraduacao, NivelGraduacao nivelGraduacao) {
    resultadoGraduacao.ficha!.niveis!.sort((a, b) => a.index!.compareTo(b.index!));

    var nivelAtual = resultadoGraduacao.ficha!.niveis!.firstWhere((element) => element.id == resultadoGraduacao.nivelId);

    var query = resultadoGraduacao.ficha!.niveis!.where((element) => element.index == nivelAtual.index! + 1);

    if (nivelGraduacao.ativo == true && query.isNotEmpty) {
      return nivelGraduacao.id == query.toList().first.id;
    } else if (query.isEmpty) {
      return false;
    } else {
      return false;
    }

    //nivelGraduacao.id == resultadoGraduacao.ficha.niveis.last.id;
  }

  bool isUltimo(ResultadoGraduacao resultadoGraduacao, NivelGraduacao nivelGraduacao) {
    resultadoGraduacao.ficha!.niveis!.sort((a, b) => a.index!.compareTo(b.index!));

    var nivelAtual = resultadoGraduacao.ficha!.niveis!.firstWhere((element) => element.id == resultadoGraduacao.nivelId);

    var query = resultadoGraduacao.ficha!.niveis!.where((element) => element.index! > nivelAtual.index! + 1);

    if (query.isEmpty) {
      return false;
    }

    return nivelGraduacao.id == query.toList().last.id && nivelGraduacao.ativo!;

    //nivelGraduacao.id == resultadoGraduacao.ficha.niveis.last.id;
  }

  bool isDepoisProximo(ResultadoGraduacao resultadoGraduacao, NivelGraduacao nivelGraduacao) {
    resultadoGraduacao.ficha!.niveis!.sort((a, b) => a.index!.compareTo(b.index!));

    var nivelAtual = resultadoGraduacao.ficha!.niveis!.firstWhere((element) => element.id == resultadoGraduacao.nivelId);

    var query = resultadoGraduacao.ficha!.niveis!.where((element) => element.index == nivelAtual.index! + 2);

    if (query.isEmpty) {
      return false;
    }

    if (nivelGraduacao.ativo == true) {
      return nivelGraduacao.id == query.toList().first.id;
    } else {
      return false;
    }

    //nivelGraduacao.id == resultadoGraduacao.ficha.niveis.last.id;
  }

  bool isNivelAtual(ResultadoGraduacao resultadoGraduacao, NivelGraduacao nivelGraduacao) {
    var nivelAtual = resultadoGraduacao.ficha!.niveis!.firstWhere((element) => element.id == resultadoGraduacao.nivelId);

    if (nivelGraduacao.ativo == true) {
      return nivelGraduacao.id == nivelAtual.id;
    } else {
      return false;
    }
  }

  int? indexNivelAtual(ResultadoGraduacao resultadoGraduacao, NivelGraduacao nivelGraduacao) {
    var nivelAtual = resultadoGraduacao.ficha!.niveis!.firstWhere((element) => element.id == resultadoGraduacao.nivelId);

    return nivelAtual.index as int?;
  }

  int? indexPrimeiroNivel(ResultadoGraduacao resultadoGraduacao, NivelGraduacao nivelGraduacao) {
    var nivelAtual = resultadoGraduacao.ficha!.niveis!.firstWhere((element) => element.id! <= resultadoGraduacao.nivelId!);

    return nivelAtual.index as int?;
  }

  int? indexNivelUltimo(ResultadoGraduacao resultadoGraduacao, NivelGraduacao nivelGraduacao) {
    resultadoGraduacao.ficha!.niveis!.sort((a, b) => a.index!.compareTo(b.index!));
    var nivelAtual = resultadoGraduacao.ficha!.niveis!.last;

    return nivelAtual.index as int?;
  }
}
