import 'dart:convert';

import 'package:app_treino/ServiceProvider/PersonalService.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/personal/AlunoDeProfessor.dart';
import 'package:app_treino/model/personal/BashBoardTreinoIdependente.dart';
import 'package:app_treino/model/personal/ObjectConsultaAluno.dart';
import 'package:app_treino/model/personal/ReportExecucao.dart';
import 'package:app_treino/model/personal/ReportSatisfacao.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:diacritic/diacritic.dart';
import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import 'package:mobx/mobx.dart';
part 'ControladorDashBoardPersonal.g.dart';

class ControladorDashBoardPersonal = _ControladorDashBoardPersonalBase with _$ControladorDashBoardPersonal;

abstract class _ControladorDashBoardPersonalBase with Store {
  final _mServicePersonalPersonal = GetIt.I.get<PersonalService>();
  final _controladorUsuario = GetIt.I.get<ControladorCliente>();

  @observable
  DashBoardTreinoIdependente? mBoard;
  @observable
  DashboardNovoTreino? mDashboard;
  @observable
  ServiceStatus mStatusDash = ServiceStatus.Waiting;
  @observable
  ServiceStatus mStatusDashboard = ServiceStatus.Waiting;
  @observable
  var consultouSatisfacao = false;
  @observable
  var consultouExecucoes = false;
  @observable
  var consultouPrescricoes = false;
  @observable
  BiAvaliacao? biAvaliacao;
  @observable
  List<Periodo?> listaPeriodo = [];
  @observable
  List<AlunoSimplificadoCarteira> alunosTreinoVencido = [];
  @observable
  List<AlunoSimplificadoCarteira> alunosTreinoVencidoBKP = [];
  @observable
  List<AlunoSimplificadoCarteira> alunosTreinoAVencer = [];
  @observable
  List<AlunoSimplificadoCarteira> alunosTreinoAVencerBKP = [];
  @observable
  List<AlunoSimplificadoCarteira> alunosTreinoEmDia = [];
  @observable
  List<AlunoSimplificadoCarteira> alunosTreinoEmDiaBKP = [];
  @observable
  List<AlunoSimplificadoCarteira> alunosContratoAtivo = [];
  @observable
  List<AlunoSimplificadoCarteira> alunosContratoInativo = [];
  @observable
  List<AlunoSimplificadoCarteiraAVencer> alunosContratoAVencer = [];
  @observable
  List<AlunoSimplificadoCarteira> alunosContratoAtivoBKP = [];
  @observable
  List<AlunoSimplificadoCarteira> alunosContratoInativoBKP = [];
  @observable
  List<AlunoSimplificadoCarteiraAVencer> alunosContratoAVencerBKP = [];
  @observable
  String itemSelecionado = '';
  @observable
  List<AlunoSimplificadoCarteira> mListaAlunosAVencer = [];

  void convertObjeto(){
    mListaAlunosAVencer.clear();
    for (final alunos in alunosContratoAVencer) {
      mListaAlunosAVencer.add(AlunoSimplificadoCarteira(
        dataPrograma: alunos.dataVigenciaAteAjustadaApresentar,
        matricula: alunos.matricula,
        nomeAbreviado: alunos.nome,
        nomeProfessor: alunos.nomeProfessor,
        dataUltimoacesso: ''
      ));
    }
  }

  void buscarPorNomeBiTreino(String termo, String tipo) {
    if(tipo == localizedString('vencidos')) {
      alunosTreinoVencido.clear();
      mStatusAlunosCarteira = ServiceStatus.Waiting;
      if(termo.isNotEmpty){
        var aux = alunosTreinoVencidoBKP.where((element) => removeDiacritics((element.nomeAbreviado ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())) || removeDiacritics((element.matricula ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())));
        alunosTreinoVencido.addAll(aux);
        mStatusAlunosCarteira = alunosTreinoVencido.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      } else {
        alunosTreinoVencido.addAll(alunosTreinoVencidoBKP);
        mStatusAlunosCarteira = alunosTreinoVencido.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      }
    }else if(tipo == localizedString('dasBoardColaborador.a_renovar')) {
      alunosTreinoAVencer.clear();
      mStatusAlunosCarteira = ServiceStatus.Waiting;
      if(termo.isNotEmpty){
        var aux = alunosTreinoAVencerBKP.where((element) => removeDiacritics((element.nomeAbreviado ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())) || removeDiacritics((element.matricula ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())));
        alunosTreinoAVencer.addAll(aux);
        mStatusAlunosCarteira = alunosTreinoAVencer.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      } else {
        alunosTreinoAVencer.addAll(alunosTreinoAVencerBKP);
        mStatusAlunosCarteira = alunosTreinoAVencer.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      }
    }else {
      alunosTreinoEmDia.clear();
      mStatusAlunosCarteira = ServiceStatus.Waiting;
      if(termo.isNotEmpty){
        var aux = alunosTreinoEmDiaBKP.where((element) => removeDiacritics((element.nomeAbreviado ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())) || removeDiacritics((element.matricula ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())));
        alunosTreinoEmDia.addAll(aux);
        mStatusAlunosCarteira = alunosTreinoEmDia.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      } else {
        alunosTreinoEmDia.addAll(alunosTreinoEmDiaBKP);
        mStatusAlunosCarteira = alunosTreinoEmDia.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      }
    }
  }

  void buscarPorNomeBiContrato(String termo, String tipo) {
    if(tipo == 'ativos') {
      alunosContratoAtivo.clear();
      mStatusContratoAtivo = ServiceStatus.Waiting;
      if(termo.isNotEmpty){
        var aux = alunosContratoAtivoBKP.where((element) => removeDiacritics((element.nomeAbreviado ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())) || removeDiacritics((element.matricula ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())));
        alunosContratoAtivo.addAll(aux);
        mStatusContratoAtivo = alunosContratoAtivo.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      } else {
        alunosContratoAtivo.addAll(alunosContratoAtivoBKP);
        mStatusContratoAtivo = alunosContratoAtivo.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      }
    }else if(tipo == 'inativos') {
      alunosContratoInativo.clear();
      mStatusContratoInativo = ServiceStatus.Waiting;
      if(termo.isNotEmpty){
        var aux = alunosContratoInativoBKP.where((element) => removeDiacritics((element.nomeAbreviado ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())) || removeDiacritics((element.matricula ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())));
        alunosContratoInativo.addAll(aux);
        mStatusContratoInativo = alunosContratoInativo.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      } else {
        alunosContratoInativo.addAll(alunosContratoInativoBKP);
        mStatusContratoInativo = alunosContratoInativo.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
      }
    }else {
      alunosContratoAVencer.clear();
      mStatusContrato = ServiceStatus.Waiting;
      if(termo.isNotEmpty){
        var aux = alunosContratoAVencerBKP.where((element) => removeDiacritics((element.nome ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())) || removeDiacritics((element.matricula ?? '').toLowerCase()).contains(removeDiacritics(termo.toLowerCase())));
        alunosContratoAVencer.addAll(aux);
        mStatusContrato = alunosContratoAVencer.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
        convertObjeto();
      } else {
        alunosContratoAVencer.addAll(alunosContratoAVencerBKP);
        mStatusContrato = alunosContratoAVencer.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
        convertObjeto();
      }
    }
  }

  void carregarDashBoard({bool? forceUpdate, Function()? sucesso, Function(String? erro)? falha, Function()? carregando}) {
    //if(mBoard!=null && !forceUpdate) return Future.value(mBoard);
    carregando?.call();
    mStatusDash = ServiceStatus.Waiting;
    _mServicePersonalPersonal.consultarDashBoardFiti(_controladorUsuario.mUsuarioLogado!.empresas?.first.codigoProfessor ?? _controladorUsuario.mUsuarioLogado!.codigoProfessor ?? 0, _controladorUsuario.mUsuarioLogado!.codEmpresa!).then((value) {
      this.mBoard = value;
      return _mServicePersonalPersonal.consultarAvaliacoes(_controladorUsuario.mUsuarioLogado!.empresas?.first.codigoProfessor ?? _controladorUsuario.mUsuarioLogado!.codigoProfessor ?? 0);
    }).then((value) {
      this.mBoard!.biMediaAvalicaoProf = value;
      mStatusDash = ServiceStatus.Done;
      sucesso?.call();
    }).catchError((onError) {
      if (mBoard != null) {
        Future.value(mBoard);
        mStatusDash = ServiceStatus.Done;
      } else {
        falha?.call(onError.message);
        mStatusDash = ServiceStatus.Error;
      }
    });
  }

  descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada() {
    if (GetIt.I.get<ControladorCliente>().nomeEmpresaSelecionada == null || (GetIt.I.get<ControladorCliente>().nomeEmpresaSelecionada?.isEmpty ?? true)) {
      return (_controladorUsuario.mUsuarioLogado?.empresas ?? []).isNotEmpty ? (_controladorUsuario.mUsuarioLogado?.empresas?.first.codigoColaborador ?? 0) : 0;
    } else {
      var empresaSelecionada = _controladorUsuario.mUsuarioLogado?.empresas
          ?.where((element) => element.nome?.toLowerCase() == GetIt.I.get<ControladorCliente>().nomeEmpresaSelecionada?.toLowerCase())
          .firstOrNull;
      return empresaSelecionada?.codigoColaborador ?? 0;
    }
  }

  descobrirCodEmpresaBaseadoNaEmpresaSelecionada() {
    var nomeEmpresaSelecionada = GetIt.I.get<ControladorCliente>().nomeEmpresaSelecionada;
    if (nomeEmpresaSelecionada == null || nomeEmpresaSelecionada.isEmpty) {
      return _controladorUsuario.mUsuarioLogado?.empresas?.firstOrNull?.codigo ?? 0;
    } else {
      var empresaSelecionada = _controladorUsuario.mUsuarioLogado?.empresas?.where((element) => element.nome?.toLowerCase() == nomeEmpresaSelecionada.toLowerCase()).firstOrNull;
      return empresaSelecionada?.codigo ?? (_controladorUsuario.mUsuarioLogado?.empresas?.firstOrNull?.codigo ?? 0);
    }
  }


  void carregarDashNovoTreino({bool? forceUpdate, required Function()? sucesso, required Function(String? erro)? falha, required Function()? carregando}) async{
    if(mDashboard!=null && !forceUpdate!) return Future.value(mDashboard);
    carregando!.call();
    mStatusDashboard = ServiceStatus.Waiting;
    _mServicePersonalPersonal.consultarDashboardNovoTreino(
      _controladorUsuario.mUsuarioLogado!.codEmpresa!, 
      descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada(), 
      _controladorUsuario.mUsuarioLogado!.codigoPessoa ?? 0).then((value) {
      mDashboard = value;
      return _mServicePersonalPersonal.consultarDashBoardFiti(_controladorUsuario.mUsuarioLogado!.empresas?.first.codigoProfessor ?? _controladorUsuario.mUsuarioLogado!.codigoProfessor ?? 0, _controladorUsuario.mUsuarioLogado!.codEmpresa!);
    }).then((value) {
      mBoard = value;
      mStatusDashboard = ServiceStatus.Done;
      sucesso!.call();
    }).catchError((onError){
      if(mDashboard != null){
        Future.value(mDashboard);
        mStatusDashboard = ServiceStatus.Done;
      } else {
        if (onError.message.toString().contains('401')) {
          _mServicePersonalPersonal.consultarDashboardNovoTreino(_controladorUsuario.mUsuarioLogado!.codEmpresa!, 
           descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada(), 0).then((value) {
            mDashboard = value;
             return _mServicePersonalPersonal.consultarDashBoardFiti(_controladorUsuario.mUsuarioLogado!.empresas?.first.codigoProfessor ?? _controladorUsuario.mUsuarioLogado!.codigoProfessor ?? 0, _controladorUsuario.mUsuarioLogado!.codEmpresa!);
            }).then((value) {
              mBoard = value;
              mStatusDashboard = ServiceStatus.Done;
              sucesso!.call();
            }).catchError((e) {
               falha!.call(onError.message);
               mStatusDashboard = ServiceStatus.Error;
            });
        } else {
          falha!.call(onError.message);
          mStatusDashboard = ServiceStatus.Error;
        }
      }
    });
  }

  void atualizarDashboard({bool? forceUpdate, required Function()? sucesso, required Function(String? erro)? falha, required Function()? carregando}) {
    //if(mDashboard!=null && !forceUpdate!) return Future.value(mDashboard);
    carregando!.call();
    mStatusDashboard = ServiceStatus.Waiting;
    _mServicePersonalPersonal.atualizarDashboardNovoTreino(_controladorUsuario.mUsuarioLogado!.codEmpresa!, 
       descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada(), _controladorUsuario.mUsuarioLogado!.codigoPessoa ?? 0).then((value) {
        mDashboard = value; 
        sucesso!.call();
        mStatusDashboard = ServiceStatus.Done;
        return _mServicePersonalPersonal.consultarDashBoardFiti(_controladorUsuario.mUsuarioLogado!.empresas?.first.codigoProfessor ?? _controladorUsuario.mUsuarioLogado!.codigoProfessor ?? 0, _controladorUsuario.mUsuarioLogado!.codEmpresa!);
      }).then((value) {
        mBoard = value;
        mStatusDashboard = ServiceStatus.Done;
        sucesso!.call();
    }).catchError((onError){
      if(mDashboard != null){
        Future.value(mDashboard);
        mStatusDashboard = ServiceStatus.Done;
      } else {
        if (onError.message.toString().contains('401')) {
          _mServicePersonalPersonal.atualizarDashboardNovoTreino(_controladorUsuario.mUsuarioLogado!.codEmpresa!, 
           descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada(), _controladorUsuario.mUsuarioLogado!.codigoPessoa!).then((value) {
            mDashboard = value;
             return _mServicePersonalPersonal.consultarDashBoardFiti(_controladorUsuario.mUsuarioLogado!.empresas?.first.codigoProfessor ?? _controladorUsuario.mUsuarioLogado!.codigoProfessor ?? 0, _controladorUsuario.mUsuarioLogado!.codEmpresa!);
            }).then((value) {
              mBoard = value;
              mStatusDashboard = ServiceStatus.Done;
              sucesso!.call();
            }).catchError((e) {
               falha!.call(onError.message);
               mStatusDashboard = ServiceStatus.Error;
            });
        } else {
          falha!.call(onError.message);
          mStatusDashboard = ServiceStatus.Error;
        }
      }
    });
  }

  @observable 
  num filtro = 12;

  String get getLabelFiltro {
    switch (filtro) {
      case 3:
        return localizedString('up_to_date');
      case 12:
        return localizedString('total_a_fazer');
      case 7:
        return localizedString('renewals');
      case 6:
        return localizedString('new_members');
      default:
        return '';
    }
  }

  @observable
  ObservableList<ReportSatisfacao> mDadosSatisfacao = ObservableList<ReportSatisfacao>();

  @observable
  ObservableList<ReportSatisfacao> mDadosSatisfacaoFiltrados = ObservableList<ReportSatisfacao>();

  @observable
  ObservableList<ReportExecucao> mDadosExecucoes = ObservableList<ReportExecucao>();

  @observable
  ObservableList<ReportExecucao> mDadosExecucoesFiltrados = ObservableList<ReportExecucao>();

  @observable
  ObservableList<String> filtroNotas = ObservableList<String>();

  @observable
  ObservableList<AlunoDeProfessor> mDadosPrescricoes = ObservableList<AlunoDeProfessor>();

  void consultarAlunosPrescricoes({Function()? carregando, Function()? sucesso, Function(String mensagem)? erro}) {
    mDadosPrescricoes.clear();
    consultouPrescricoes = false;
    _mServicePersonalPersonal.consultarAlunos(filtro, _controladorUsuario.mUsuarioLogado!.codUsuario!, _controladorUsuario.mUsuarioLogado!.codEmpresa ?? 0, 1000).then((value) {
      consultouPrescricoes = true;
      mDadosPrescricoes.addAll(value.where((element) => element.professor!.toLowerCase() == _controladorUsuario.mUsuarioLogado!.nome!.toLowerCase()).toList());
      sucesso!();
    }).catchError((onError) {
      erro!('Falha');
      consultouPrescricoes = true;
    });
  }

  void entrarEmContatoWpp(ReportSatisfacao resultado) {
    UtilitarioApp().launchWhatsApp(phone: "${resultado.telefoneCodigoPais ?? '55'}${resultado.telefone}", message: 'Olá ${resultado.nome}');
  }

  void filtrarListaPorNota({String? termo}) {
    mDadosSatisfacaoFiltrados.clear();
    if (termo != null && termo.isNotEmpty) {
      mDadosSatisfacaoFiltrados.addAll(mDadosSatisfacao.where((element) => element.nome!.toLowerCase().contains(termo.toLowerCase())));
    } else if (filtroNotas.isEmpty) {
      mDadosSatisfacaoFiltrados.addAll(mDadosSatisfacao);
    } else {
      mDadosSatisfacaoFiltrados.addAll(mDadosSatisfacao.where((element) => filtroNotas.contains(element.nota)));
    }
  }

  void filtrarExecucoes({String? termo}) {
    mDadosExecucoesFiltrados.clear();
    if (termo != null && termo.isNotEmpty) {
      mDadosExecucoesFiltrados.addAll(mDadosExecucoes.where((element) => element.nome!.toLowerCase().contains(termo.toLowerCase())));
    } else {
      mDadosExecucoesFiltrados.addAll(mDadosExecucoes);
    }
  }

  @observable
  int treinosExecutados = 0;
  @observable
  int treinosPrevistos = 0;

  String get porcentagemRealizado {
    treinosExecutados = 0;
    treinosPrevistos = 0;
    for (final element in mDadosExecucoes) {
      treinosPrevistos += element.vezesNaSemana as int;
      treinosExecutados += element.execucoes as int;
    }
    if (treinosExecutados == 0) return NumberFormat.percentPattern().format(0.0);
    return NumberFormat.percentPattern().format(treinosExecutados / treinosPrevistos <= 1 ? treinosExecutados / treinosPrevistos : 1.0);
  }

  String porcentagemTreinos(ReportExecucao execucao) {
    if (execucao.execucoes == 0) return NumberFormat.percentPattern().format(0.0);
    return NumberFormat.percentPattern().format(execucao.execucoes! / execucao.vezesNaSemana!);
  }

  String get mediaAvalicao {
    var valor = 0.0;
    for (final element in mDadosSatisfacao) {
      valor += num.parse(element.nota!);
    }
    var numberFormat = NumberFormat.compact();
    numberFormat.maximumFractionDigits = 1;
    numberFormat.maximumIntegerDigits = 1;
    numberFormat.significantDigitsInUse = false;
    return numberFormat.format((valor / (mDadosSatisfacao.length > 0 ? mDadosSatisfacao.length.toDouble() : 1.0)));
  }

  double mediaQuantidadeAvalicao(int estrelas) {
    Iterable<ReportSatisfacao> query = mDadosSatisfacao.where((element) => num.parse(element.nota!) == estrelas);
    return query.length / (mDadosSatisfacao.length > 0 ? mDadosSatisfacao.length.toDouble() : 1.0);
  }

  void consultarDadosSatisfacao({required Function()? carregando, Function()? sucesso, Function(String? mensagem)? erro}) {
    carregando?.call();
    consultouSatisfacao = false;
    _mServicePersonalPersonal.resultadoNivelSatisfacao().then((value) {
      mDadosSatisfacao.clear();
      mDadosSatisfacao.addAll(value);
      mDadosSatisfacaoFiltrados.clear();
      mDadosSatisfacaoFiltrados.addAll(value);
      sucesso!();
      consultouSatisfacao = true;
    }).catchError((onError) {
      erro!(onError.message);
      consultouSatisfacao = true;
    });
  }

  void consultarDadosHistorico({required Function()? carregando, Function()? sucesso, Function(String? mensagem)? erro}) {
    carregando?.call();
    consultouExecucoes = false;
    mDadosExecucoesFiltrados.clear();
    _mServicePersonalPersonal.resultadoExecucoes().then((value) {
      mDadosExecucoes.clear();
      mDadosExecucoes.addAll(value);
      mDadosExecucoesFiltrados.addAll(value);
      sucesso!();
      consultouExecucoes = true;
    }).catchError((onError) {
      consultouExecucoes = true;
      erro!(onError.message);
    });
  }

  void carregarBiAvaliacaoFisica({Function()? carregando, Function()? sucesso, Function(String? mensagem)? erro}){
    var dataInicio = DateTime.now().subtract(const Duration(days: 28)).millisecondsSinceEpoch;
    var dataFim = DateTime.now().millisecondsSinceEpoch;
    carregando?.call();
    mStatusDashboard = ServiceStatus.Waiting;
    _mServicePersonalPersonal.consultarDashAvaliacao(_controladorUsuario.mUsuarioLogado!.codEmpresa!, dataInicio, dataFim, descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada()).then((value) {
      biAvaliacao = value;
      mStatusDashboard = ServiceStatus.Done;
      sucesso?.call();
    }).catchError((onError){
      erro?.call(onError.message);
    });
  }

   List<Periodo?> sortPeriodoManha = [];
   List<Periodo?> sortPeriodoTarde = [];
   List<Periodo?> sortPeriodoNoite = [];
  
  void sortPeriodo(){
    sortPeriodoManha.clear();
    sortPeriodoTarde.clear();
    sortPeriodoNoite.clear();
    mDashboard!.biTreinamento!.mediaExecucao!.segunda!.enumerador = EnumSemana.SEGUNDA;
    mDashboard!.biTreinamento!.mediaExecucao!.terca!.enumerador = EnumSemana.TERCA;
    mDashboard!.biTreinamento!.mediaExecucao!.quarta!.enumerador = EnumSemana.QUARTA;
    mDashboard!.biTreinamento!.mediaExecucao!.quinta!.enumerador = EnumSemana.QUINTA;
    mDashboard!.biTreinamento!.mediaExecucao!.sexta!.enumerador = EnumSemana.SEXTA;
    mDashboard!.biTreinamento!.mediaExecucao!.sabado!.enumerador = EnumSemana.SABADO;
    mDashboard!.biTreinamento!.mediaExecucao!.domingo!.enumerador = EnumSemana.DOMINGO;
    listaPeriodo = [mDashboard!.biTreinamento!.mediaExecucao!.segunda,
      mDashboard!.biTreinamento!.mediaExecucao!.terca,
      mDashboard!.biTreinamento!.mediaExecucao!.quarta,
      mDashboard!.biTreinamento!.mediaExecucao!.quinta,
      mDashboard!.biTreinamento!.mediaExecucao!.sexta,
      mDashboard!.biTreinamento!.mediaExecucao!.sabado,
      mDashboard!.biTreinamento!.mediaExecucao!.domingo];
    listaPeriodo.sort((a,b)=>b!.manha!.compareTo(a!.manha!));
    sortPeriodoManha.addAll(listaPeriodo);
    listaPeriodo.sort((a,b)=>b!.tarde!.compareTo(a!.tarde!));
    sortPeriodoTarde.addAll(listaPeriodo);
    listaPeriodo.sort((a,b)=>b!.noite!.compareTo(a!.noite!));
    sortPeriodoNoite.addAll(listaPeriodo);
  }

  MaiorPeriodo? get maiorPeriodo{
    sortPeriodo();
    sortPeriodoManha.first!.enumperiodoDia = EnumPeriodo.MANHA;
    sortPeriodoTarde.first!.enumperiodoDia = EnumPeriodo.TARDE;
    sortPeriodoNoite.first!.enumperiodoDia = EnumPeriodo.NOITE;
    var manha = MaiorPeriodo(diaSemana: sortPeriodoManha.first!.enumerador.toString(), valor: sortPeriodoManha.first!.manha, periodo: sortPeriodoManha.first!.enumperiodoDia.toString());
    var tarde = MaiorPeriodo(diaSemana: sortPeriodoTarde.first!.enumerador.toString(), valor: sortPeriodoTarde.first!.tarde, periodo: sortPeriodoTarde.first!.enumperiodoDia.toString());
    var noite = MaiorPeriodo(diaSemana: sortPeriodoNoite.first!.enumerador.toString(), valor: sortPeriodoNoite.first!.noite, periodo: sortPeriodoNoite.first!.enumperiodoDia.toString());
    List<MaiorPeriodo> listaMaiorExecucao = [manha, tarde, noite];

    var valor = listaMaiorExecucao.reduce((value, element) => value.valor! > element.valor!? value: element);
    return valor;
  }

  Periodo? get menorPeriodo{
    sortPeriodo();
    List<Periodo?> lista = [];
    //periodo da manha
    if(sortPeriodoManha.last!.manha!.toInt() != 0){
      sortPeriodoManha.last!.enumperiodoDia = EnumPeriodo.MANHA;
    }else if(sortPeriodoManha[5]!.manha!.toInt() != 0){
      sortPeriodoManha[5]!.enumperiodoDia = EnumPeriodo.MANHA;
    }else{
      sortPeriodoManha[4]!.enumperiodoDia = EnumPeriodo.MANHA;
    }
    //periodo da tarde
    if(sortPeriodoTarde.last!.tarde!.toInt() != 0){
      sortPeriodoTarde.last!.enumperiodoDia = EnumPeriodo.TARDE;
    }else if(sortPeriodoTarde[5]!.tarde!.toInt() != 0){
      sortPeriodoTarde[5]!.enumperiodoDia = EnumPeriodo.TARDE;
    }else{
      sortPeriodoTarde[4]!.enumperiodoDia = EnumPeriodo.TARDE;
    }
    //Perido da noite
    if(sortPeriodoNoite.last!.noite!.toInt() != 0){
      sortPeriodoNoite.last!.enumperiodoDia = EnumPeriodo.NOITE;
    }else if(sortPeriodoNoite[5]!.noite!.toInt() != 0){
      sortPeriodoNoite[5]!.enumperiodoDia = EnumPeriodo.NOITE;
    }else{
      sortPeriodoNoite[4]!.enumperiodoDia = EnumPeriodo.NOITE;
    }
    lista = [
      sortPeriodoManha.last!.manha!.toInt() == 0 ? sortPeriodoManha[5]!.manha!.toInt() == 0 ? sortPeriodoManha[4]!.manha!.toInt() == 0 ? sortPeriodoManha[3] : sortPeriodoManha[4] : sortPeriodoManha[5] : sortPeriodoManha.last, 
      sortPeriodoTarde.last!.tarde!.toInt() == 0 ? sortPeriodoTarde[5]!.tarde!.toInt() == 0 ? sortPeriodoTarde[4]!.tarde!.toInt() == 0 ? sortPeriodoTarde[3] : sortPeriodoTarde[4] : sortPeriodoTarde[5] : sortPeriodoTarde.last, 
      sortPeriodoNoite.last!.noite!.toInt() == 0 ? sortPeriodoNoite[5]!.noite!.toInt() == 0 ? sortPeriodoNoite[4]!.noite!.toInt() == 0 ? sortPeriodoNoite[3] : sortPeriodoNoite[4] : sortPeriodoNoite[5] : sortPeriodoNoite.last];
    return lista.first;
  }

  int? get periodoMenor {
    List<int?> aux = [];
    aux = [menorPeriodo!.manha, menorPeriodo!.tarde, menorPeriodo!.noite];
    aux.sort((b, a)=>a!.compareTo(b!));
    if(aux.last == 0){
      if(aux[1] == 0){
        return aux.first;
      }else{
        return aux[1];
      }
    }else{
      return aux.last;
    }
  }


  int get maxY{
    List<int> maximoPeriodo = [];
    int segunda, terca, quarta, quinta, sexta, sabado, domingo;

    segunda = mDashboard!.biTreinamento!.mediaExecucao!.segunda!.manha!.toInt() + mDashboard!.biTreinamento!.mediaExecucao!.segunda!.tarde!.toInt()+ mDashboard!.biTreinamento!.mediaExecucao!.segunda!.noite!.toInt();
    terca = mDashboard!.biTreinamento!.mediaExecucao!.terca!.manha!.toInt() + mDashboard!.biTreinamento!.mediaExecucao!.terca!.tarde!.toInt()+ mDashboard!.biTreinamento!.mediaExecucao!.terca!.noite!.toInt();
    quarta = mDashboard!.biTreinamento!.mediaExecucao!.quarta!.manha!.toInt() + mDashboard!.biTreinamento!.mediaExecucao!.quarta!.tarde!.toInt()+ mDashboard!.biTreinamento!.mediaExecucao!.quarta!.noite!.toInt();
    quinta = mDashboard!.biTreinamento!.mediaExecucao!.quinta!.manha!.toInt()+ mDashboard!.biTreinamento!.mediaExecucao!.quinta!.tarde!.toInt()+ mDashboard!.biTreinamento!.mediaExecucao!.quinta!.noite!.toInt();
    sexta = mDashboard!.biTreinamento!.mediaExecucao!.sexta!.manha!.toInt()+ mDashboard!.biTreinamento!.mediaExecucao!.sexta!.tarde!.toInt()+ mDashboard!.biTreinamento!.mediaExecucao!.sexta!.noite!.toInt();
    sabado = mDashboard!.biTreinamento!.mediaExecucao!.sabado!.manha!.toInt()+ mDashboard!.biTreinamento!.mediaExecucao!.sabado!.tarde!.toInt()+ mDashboard!.biTreinamento!.mediaExecucao!.sabado!.noite!.toInt();
    domingo = mDashboard!.biTreinamento!.mediaExecucao!.domingo!.manha!.toInt() + mDashboard!.biTreinamento!.mediaExecucao!.domingo!.tarde!.toInt()+ mDashboard!.biTreinamento!.mediaExecucao!.domingo!.noite!.toInt();

  maximoPeriodo.addAll([segunda, terca, quarta, quinta, sexta, sabado, domingo]);
  maximoPeriodo.sort((a, b) => num.parse(b.toString()).compareTo(num.parse(a.toString())));

  return maximoPeriodo.first;
  }

  bool get execucaoVazio{
    var retornoExecucao = false;
    if(mDashboard!.biTreinamento!.mediaExecucao!.segunda!.total == 0 || mDashboard!.biTreinamento!.mediaExecucao!.segunda!.total == null){
      if(mDashboard!.biTreinamento!.mediaExecucao!.terca!.total == 0 || mDashboard!.biTreinamento!.mediaExecucao!.terca!.total == null){
        if(mDashboard!.biTreinamento!.mediaExecucao!.quarta!.total == 0 || mDashboard!.biTreinamento!.mediaExecucao!.quarta!.total == null){
          if(mDashboard!.biTreinamento!.mediaExecucao!.quinta!.total == 0 || mDashboard!.biTreinamento!.mediaExecucao!.quinta!.total == null){
            if (mDashboard!.biTreinamento!.mediaExecucao!.sexta!.total == 0 || mDashboard!.biTreinamento!.mediaExecucao!.sexta!.total == null){
              if(mDashboard!.biTreinamento!.mediaExecucao!.sabado!.total == 0 || mDashboard!.biTreinamento!.mediaExecucao!.sabado!.total == null){
                if(mDashboard!.biTreinamento!.mediaExecucao!.domingo!.total == 0 || mDashboard!.biTreinamento!.mediaExecucao!.domingo!.total == null){
                  return retornoExecucao = true;
                }
              }
            }
          }
        }
      }
    }else{
      return retornoExecucao = false;
    }
    return retornoExecucao;
  }

  String get valorFinalExecucaoMenor{
    var valor;
    if(menorPeriodo!.enumerador == EnumSemana.SEGUNDA){
      if(menorPeriodo!.enumperiodoDia == EnumPeriodo.MANHA){
        return valor = localizedString('segunda_manha');
      }else if(menorPeriodo!.enumperiodoDia == EnumPeriodo.TARDE){
        return valor = localizedString('segunda_tarde');
      }else{
        return valor = localizedString('segunda_noite');
      }
    }else if(menorPeriodo!.enumerador == EnumSemana.TERCA){
      if(menorPeriodo!.enumperiodoDia == EnumPeriodo.MANHA){
        return valor = localizedString('terca_manha');
      }else if(menorPeriodo!.enumperiodoDia == EnumPeriodo.TARDE){
        return valor = localizedString('terca_tarde');
      }else{
        return valor = localizedString('terca_noite');
      }
    }else if(menorPeriodo!.enumerador == EnumSemana.QUARTA){
      if(menorPeriodo!.enumperiodoDia == EnumPeriodo.MANHA){
        return valor = localizedString('quarta_manha');
      }else if(menorPeriodo!.enumperiodoDia == EnumPeriodo.TARDE){
        return valor = localizedString('quarta_tarde');
      }else{
        return valor = localizedString('quarta_noite');
      }
    }else if(menorPeriodo!.enumerador == EnumSemana.QUINTA){
      if(menorPeriodo!.enumperiodoDia == EnumPeriodo.MANHA){
        return valor = localizedString('quinta_manha');
      }else if(menorPeriodo!.enumperiodoDia == EnumPeriodo.TARDE){
        return valor = localizedString('quinta_tarde');
      }else{
        return valor = localizedString('quinta_noite');
      }
    }else if(menorPeriodo!.enumerador == EnumSemana.SEXTA){
      if(menorPeriodo!.enumperiodoDia == EnumPeriodo.MANHA){
        return valor = localizedString('sexta_manha');
      }else if(menorPeriodo!.enumperiodoDia == EnumPeriodo.TARDE){
        return valor = localizedString('sexta_tarde');
      }else{
        return valor = localizedString('sexta_noite');
      }
    }else if(menorPeriodo!.enumerador == EnumSemana.SABADO){
      if(menorPeriodo!.enumperiodoDia == EnumPeriodo.MANHA){
        return valor = localizedString('sabado_manha');
      }else if(menorPeriodo!.enumperiodoDia == EnumPeriodo.TARDE){
        return valor = localizedString('sabado_tarde');
      }else{
        return valor = localizedString('sabado_noite');
      }
    }else if(menorPeriodo!.enumerador == EnumSemana.DOMINGO){
      if(menorPeriodo!.enumperiodoDia == EnumPeriodo.MANHA){
        return valor = localizedString('domingo_manha');
      }else if(menorPeriodo!.enumperiodoDia == EnumPeriodo.TARDE){
        return valor = localizedString('domingo_tarde');
      }else{
        return valor = localizedString('domingo_noite');
      }
    }
    return valor;
  }

  String get valorFinalExecucaoMaior{
    var valor;
    if(maiorPeriodo!.diaSemana!.contains('EnumSemana.SEGUNDA')){
      if(maiorPeriodo!.periodo == 'EnumPeriodo.MANHA'){
        return valor = localizedString('segunda_manha');
      }else if(maiorPeriodo!.periodo == 'EnumPeriodo.TARDE'){
        return valor = localizedString('segunda_tarde');
      }else{
        return valor = localizedString('segunda_noite');
      }
    }else if(maiorPeriodo!.diaSemana == 'EnumSemana.TERCA'){
      if(maiorPeriodo!.periodo == 'EnumPeriodo.MANHA'){
        return valor = localizedString('terca_manha');
      }else if(maiorPeriodo!.periodo == 'EnumPeriodo.TARDE'){
        return valor = localizedString('terca_tarde');
      }else{
        return valor = localizedString('terca_noite');
      }
    }else if(maiorPeriodo!.diaSemana == 'EnumSemana.QUARTA'){
      if(maiorPeriodo!.periodo == 'EnumPeriodo.MANHA'){
        return valor = localizedString('quarta_manha');
      }else if(maiorPeriodo!.periodo == 'EnumPeriodo.TARDE'){
        return valor = localizedString('quarta_tarde');
      }else{
        return valor = localizedString('quarta_noite');
      }
    }else if(maiorPeriodo!.diaSemana == 'EnumSemana.QUINTA'){
      if(maiorPeriodo!.periodo == 'EnumPeriodo.MANHA'){
        return valor = localizedString('quinta_manha');
      }else if(maiorPeriodo!.periodo == 'EnumPeriodo.TARDE'){
        return valor = localizedString('quinta_tarde');
      }else{
        return valor = localizedString('quinta_noite');
      }
    }else if(maiorPeriodo!.diaSemana == 'EnumSemana.SEXTA'){
      if(maiorPeriodo!.periodo == 'EnumPeriodo.MANHA'){
        return valor = localizedString('sexta_manha');
      }else if(maiorPeriodo!.periodo == 'EnumPeriodo.TARDE'){
        return valor = localizedString('sexta_tarde');
      }else{
        return valor = localizedString('sexta_noite');
      }
    }else if(maiorPeriodo!.diaSemana == 'EnumSemana.SABADO'){
      if(maiorPeriodo!.periodo == 'EnumPeriodo.MANHA'){
        return valor = localizedString('sabado_manha');
      }else if(maiorPeriodo!.periodo == 'EnumPeriodo.TARDE'){
        return valor = localizedString('sabado_tarde');
      }else{
        return valor = localizedString('sabado_noite');
      }
    }else if(maiorPeriodo!.diaSemana == 'EnumSemana.DOMINGO'){
      if(maiorPeriodo!.periodo == 'EnumPeriodo.MANHA'){
        return valor = localizedString('domingo_manha');
      }else if(maiorPeriodo!.periodo == 'EnumPeriodo.TARDE'){
        return valor = localizedString('domingo_tarde');
      }else{
        return valor = localizedString('domingo_noite');
      }
    }
    return valor;
  }

  @observable
  ServiceStatus mStatusAlunosCarteira = ServiceStatus.Waiting;
  @observable
  bool isLastPage = false;
  int pageConsultaAlunos = 0;

  void consultarAlunosTreinoVencido({Function()? carregando, Function()? sucesso, Function(String? mensagem)? erro}) {
    carregando!.call();
    alunosTreinoVencido.clear();
    alunosTreinoVencidoBKP.clear();
    mStatusAlunosCarteira = ServiceStatus.Waiting;
     var filtros = {'quicksearchFields':['matricula', 'nomeAbreviado', 'nomeProfessor', 'dataPrograma', 'dataUltimoacesso'],'professorId': descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada().toString(), 'codigoPessoa': GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codigoPessoa ?? 0};
   // aqui
    _mServicePersonalPersonal.treinosVencidosPorProfessor(1, jsonEncode(filtros), 200, descobrirCodEmpresaBaseadoNaEmpresaSelecionada()).then((value){
      alunosTreinoVencido.addAll(value);
      alunosTreinoVencidoBKP.addAll(alunosTreinoVencido);
      sucesso!.call();
      mStatusAlunosCarteira = alunosTreinoVencido.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    }).catchError((onError){
      erro!.call(onError);
      mStatusAlunosCarteira = ServiceStatus.Error;
    });
  }

  @observable
  ServiceStatus mStatusAlunosCarteiraAVencer = ServiceStatus.Waiting;

  void consultarAlunosTreinoAVencer({Function()? carregando, Function()? sucesso, Function(String? mensagem)? erro}) {
    carregando!.call();
    alunosTreinoAVencer.clear();
    alunosTreinoAVencerBKP.clear();
    mStatusAlunosCarteira = ServiceStatus.Waiting;
    var filtros = {'quicksearchFields':['matricula', 'nomeAbreviado', 'nomeProfessor', 'dataPrograma', 'dataUltimoacesso'],'professorId': descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada().toString(), 'codigoPessoa': GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codigoPessoa ?? 0};
    _mServicePersonalPersonal.treinosAVencerPorProfessor(1, jsonEncode(filtros), 200, descobrirCodEmpresaBaseadoNaEmpresaSelecionada()).then((value){
      alunosTreinoAVencer.addAll(value);
      alunosTreinoAVencerBKP.addAll(alunosTreinoAVencer);
      sucesso!.call();
      mStatusAlunosCarteira = alunosTreinoAVencer.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    }).catchError((onError){
      erro!.call(onError);
      mStatusAlunosCarteira = ServiceStatus.Error;
    });
  }

  @observable
  ServiceStatus mStatusAlunosCarteiraEmDia = ServiceStatus.Waiting;

  void consultarAlunosTreinoEmDia({Function()? carregando, Function()? sucesso, Function(String? mensagem)? erro}) {
    carregando!.call();
    alunosTreinoEmDia.clear();
    alunosTreinoEmDiaBKP.clear();
    mStatusAlunosCarteira = ServiceStatus.Waiting;
    //GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codigoPessoa ?? 0
    var filtros = {'quicksearchFields':['matricula', 'nomeAbreviado', 'nomeProfessor', 'dataPrograma', 'dataUltimoacesso'],'professorId': descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada().toString(), 'codigoPessoa': GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codigoPessoa ?? 0};
    _mServicePersonalPersonal.treinosEmDiaPorProfessor((descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada()), jsonEncode(filtros), 200, descobrirCodEmpresaBaseadoNaEmpresaSelecionada()).then((value){
      alunosTreinoEmDia.addAll(value);
      alunosTreinoEmDiaBKP.addAll(alunosTreinoEmDia);
      sucesso!.call();
      mStatusAlunosCarteira = alunosTreinoEmDia.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    }).catchError((onError){
      erro!.call(onError);
      mStatusAlunosCarteira = ServiceStatus.Error;
    });
  }

  @observable
  ServiceStatus mStatusContrato = ServiceStatus.Waiting;

  void consultarContratosAVencer({Function()? carregando, Function()? sucesso, Function(String? mensagem)? erro}) {
    mStatusContrato = ServiceStatus.Waiting;
    carregando!.call();
    var filtros = {'quicksearchFields':['matricula','nome','nomeProfessor','dataVigenciaAteAjustadaApresentar'],'professorId': codigoProfessor() ?? _controladorUsuario.mUsuarioLogado!.codigoProfessor ?? 0, 'codigoPessoa': _controladorUsuario.mUsuarioLogado!.codigoPessoa ?? 0};
    _mServicePersonalPersonal.contratoAvencer(descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada(), jsonEncode(filtros), 0, 100, (_controladorUsuario.mUsuarioLogado!.codEmpresa ?? 1)).then((value){
      alunosContratoAVencer.clear();
      alunosContratoAVencerBKP.clear();
      alunosContratoAVencer.addAll(value);
      alunosContratoAVencerBKP.addAll(alunosContratoAVencer);
      sucesso!.call();
      mStatusContrato = alunosContratoAVencer.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    }).catchError((onError){
      erro!.call(onError);
      mStatusContrato = ServiceStatus.Error;
    });
  }

  num? codigoProfessor() {
    if ((_controladorUsuario.mUsuarioLogado!.empresas?.length ?? 0) > 0) {
      return _controladorUsuario.mUsuarioLogado!.empresas?.first.codigoProfessor;
    } else {
      return null;
    }
  }

  @observable
  ServiceStatus mStatusContratoAtivo = ServiceStatus.Waiting;

  void consultarContratosAtivo({Function()? carregando, Function()? sucesso, Function(String? mensagem)? erro}) {
    carregando!.call();
    mStatusContratoAtivo = ServiceStatus.Waiting;
    var filtros = {'quicksearchFields':['matricula', 'nomeAbreviado', 'nomeProfessor'],'professorId': descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada(), 'codigoPessoa': GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codigoPessoa ?? 0};
    _mServicePersonalPersonal.contratoAtivo(descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada(), jsonEncode(filtros), 200, (_controladorUsuario.mUsuarioLogado!.codEmpresa ?? 1)).then((value){
      alunosContratoAtivo.clear();
      alunosContratoAtivoBKP.clear();
      alunosContratoAtivo.addAll(value);
      alunosContratoAtivoBKP.addAll(alunosContratoAtivo);
      sucesso!.call();
      mStatusContratoAtivo = alunosContratoAtivo.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    }).catchError((onError){
      erro!.call(onError);
      mStatusContratoAtivo = ServiceStatus.Error;
    });
  }

  @observable
  ServiceStatus mStatusContratoInativo = ServiceStatus.Waiting;

  void consultarContratosInativo({Function()? carregando, Function()? sucesso, Function(String? mensagem)? erro}) {
    carregando!.call();
    mStatusContratoInativo = ServiceStatus.Waiting;
    var filtros = {'quicksearchFields':['matricula', 'nomeAbreviado', 'nomeProfessor'],'professorId': descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada(), 'codigoPessoa': GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codigoPessoa ?? 0};
    _mServicePersonalPersonal.contratoInativo(descobrirCodigoColaboradorBaseadoNaEmpresaSelecionada(), jsonEncode(filtros), 200, (_controladorUsuario.mUsuarioLogado!.codEmpresa ?? 1)).then((value){
      alunosContratoInativo.clear();
      alunosContratoInativoBKP.clear();
      alunosContratoInativo.addAll(value);
      alunosContratoInativoBKP.addAll(alunosContratoInativo);
      sucesso!.call();
      mStatusContratoInativo = alunosContratoInativo.isNotEmpty ? ServiceStatus.Done : ServiceStatus.Empty;
    }).catchError((onError){
      erro!.call(onError);
      mStatusContratoInativo = ServiceStatus.Error;
    });
  }

  limparTudo() {
    mStatusDash = ServiceStatus.Waiting;
    mStatusDashboard = ServiceStatus.Waiting;
    mBoard = null;
    mDashboard = null;
    alunosContratoAtivo.clear(); 
    alunosContratoInativo.clear();
    alunosContratoAVencer.clear();
    alunosContratoAtivoBKP.clear(); 
    alunosContratoInativoBKP.clear();
    alunosContratoAVencerBKP.clear();
    alunosTreinoVencido.clear();
    alunosTreinoAVencer.clear();
    alunosTreinoEmDia.clear();
    alunosTreinoVencidoBKP.clear();
    alunosTreinoAVencerBKP.clear();
    alunosTreinoEmDiaBKP.clear();
    mDadosExecucoes.clear();
    mDadosExecucoesFiltrados.clear();
    mDadosPrescricoes.clear();
    mDadosSatisfacao.clear();
    mDadosSatisfacaoFiltrados.clear();
  }
}

class MaiorPeriodo{
  String? diaSemana;
  String? periodo;
  int? valor;

  MaiorPeriodo({this.diaSemana, this.valor, this.periodo});
}