import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/model/util/UtilColor.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:google_fonts/google_fonts.dart';

class TextUtil {
  static TextSpan display1({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? styledisplay1().copyWith(fontWeight: weight));

  static TextSpan display2({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? styledisplay2().copyWith(fontWeight: weight));

  static TextSpan display3({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? styledisplay3().copyWith(fontWeight: weight));

  static TextSpan display4({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? styledisplay4().copyWith(fontWeight: weight));

  static TextSpan header({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? styleheader().copyWith(fontWeight: weight));

  static TextSpan headLine1({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? styleheadLine1().copyWith(fontWeight: weight));

  static TextSpan headLine2({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? styleheadLine2().copyWith(fontWeight: weight));

  static TextSpan headLine3({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? styleheadLine3().copyWith(fontWeight: weight));

  static TextSpan body1({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? stylebody1().copyWith(fontWeight: weight));

  static TextSpan body1Chart({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? stylebody1Chart().copyWith(fontWeight: weight));

  static TextSpan body2({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? stylebody2().copyWith(fontWeight: weight));

  static TextSpan overLine1({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? styleoverLine1().copyWith(fontWeight: weight));

  static TextSpan overLine2({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? styleoverLine2().copyWith(fontWeight: weight));

  static TextSpan overLine3({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? styleoverLine2().copyWith(fontWeight: weight));

  static TextSpan button({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? stylebutton().copyWith(fontWeight: weight));

  static TextSpan action({String? text, TextStyle? style, FontWeight? weight}) => TextSpan(text: text, style: style ?? styleaction().copyWith(fontWeight: weight));

  static TextStyle stylebutton1() =>
      GoogleFonts.nunitoSans(fontSize: 16, fontWeight: FontWeight.bold, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#80858C')! : HexColor.fromHex('#B4B7BB')!);

  static TextStyle styleMiniTimer() =>
      GoogleFonts.nunitoSans(fontSize: 36, fontWeight: FontWeight.normal, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#80858C')! : HexColor.fromHex('#B4B7BB')!);

  static TextStyle styledisplayTimer() =>
      GoogleFonts.nunitoSans(fontWeight: FontWeight.w700, fontSize: 144, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#51555A')! : HexColor.fromHex('#B4B7BB')!);

  static TextStyle styledisplayTimerClock() =>
      GoogleFonts.nunitoSans(fontWeight: FontWeight.w700, fontSize: 96, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#51555A')! : HexColor.fromHex('#B4B7BB')!);

  static TextStyle styledisplay1() =>
      GoogleFonts.nunitoSans(fontWeight: FontWeight.w700, fontSize: 64, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#51555A')! : HexColor.fromHex('#B4B7BB')!);
  static TextStyle styledisplay2() =>
      GoogleFonts.nunitoSans(fontWeight: FontWeight.w700, fontSize: 32, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#51555A')! : HexColor.fromHex('#B4B7BB')!);
  static TextStyle styledisplay3() =>
      GoogleFonts.nunitoSans(fontWeight: FontWeight.bold, fontSize: 26, height: 0, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#51555A')! : HexColor.fromHex('#B4B7BB')!);
  static TextStyle styledisplay4() =>
      GoogleFonts.nunitoSans(fontWeight: FontWeight.bold, fontSize: 45, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#51555A')! : HexColor.fromHex('#B4B7BB')!);
  static TextStyle styleheader() =>
      GoogleFonts.nunitoSans(fontWeight: FontWeight.bold, fontSize: 20, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#51555A')! : HexColor.fromHex('#B4B7BB')!);
  static TextStyle styleheadLine1() =>
      GoogleFonts.nunitoSans(fontWeight: FontWeight.w700, fontSize: 18, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#51555A')! : HexColor.fromHex('#B4B7BB')!);
  static TextStyle styleheadLine2() =>
      GoogleFonts.nunitoSans(fontWeight: FontWeight.w700, fontSize: 16, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#51555A')! : HexColor.fromHex('#B4B7BB')!);
  static TextStyle styleheadLine2CorPrimaria(BuildContext context) =>
      GoogleFonts.nunitoSans(fontWeight: FontWeight.w700, fontSize: 16, color: Theme.of(context).primaryColor);
  static TextStyle styleheadLine3() =>
      GoogleFonts.nunitoSans(fontWeight: FontWeight.w700, fontSize: 14, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#51555A')! : HexColor.fromHex('#B4B7BB')!);
  static TextStyle stylebody1() =>
      GoogleFonts.nunitoSans(fontSize: 16, fontWeight: FontWeight.normal, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#80858C')! : HexColor.fromHex('#83888F')!);
  static TextStyle stylebody1Chart() =>
      GoogleFonts.nunitoSans(fontSize: 16, fontWeight: FontWeight.normal, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#B4B7BB')! : HexColor.fromHex('#B4B7BB')!);
  static TextStyle stylebody2Chart() =>
      GoogleFonts.nunitoSans(fontSize: 14, fontWeight: FontWeight.w400, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#B4B7BB')! : HexColor.fromHex('#B4B7BB')!);
  static TextStyle styleOverline1Chart() =>
      GoogleFonts.nunitoSans(fontSize: 10, fontWeight: FontWeight.w400, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#B4B7BB')! : HexColor.fromHex('#B4B7BB')!);

  static TextStyle stylebody2() =>
      GoogleFonts.nunitoSans(fontSize: 14, fontWeight: FontWeight.normal, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#80858C')! : HexColor.fromHex('#83888F')!);
  static TextStyle styleoverLine1() =>
      GoogleFonts.nunitoSans(fontSize: 12, fontWeight: FontWeight.w400, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#80858C')! : HexColor.fromHex('#83888F')!);
  static TextStyle styleoverLine2() =>
      GoogleFonts.nunitoSans(fontSize: 12, fontWeight: FontWeight.w600, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#80858C')! : HexColor.fromHex('#83888F')!);
  static TextStyle styleoverLine3() =>
      GoogleFonts.nunitoSans(fontSize: 10, fontWeight: FontWeight.w400, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#80858C')! : HexColor.fromHex('#B4B7BB')!);
  static TextStyle styleoverLineNutriCalendarSelecionado() => GoogleFonts.nunitoSans(fontSize: 12, fontWeight: FontWeight.normal, color: Colors.white);
  static TextStyle styleoverLineNutriCalendar() => GoogleFonts.nunitoSans(fontSize: 12, fontWeight: FontWeight.normal, color: const Color(0xff48D567));
  static TextStyle stylOverLineTimeLineCalendar(BuildContext context) => GoogleFonts.nunitoSans(fontSize: 12, fontWeight: FontWeight.normal, color: Theme.of(context).primaryColor);
  static TextStyle stylebutton() =>
      GoogleFonts.nunitoSans(fontSize: 16, fontWeight: FontWeight.w600, color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? HexColor.fromHex('#51555A')! : HexColor.fromHex('#B4B7BB')!);
  static TextStyle styleBotaoPrimario({ Color? color }) =>
      GoogleFonts.nunitoSans(fontSize: 16, fontWeight: FontWeight.w600, color: color ?? (GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? Colors.white : Colors.black));

  static TextStyle styleaction() => GoogleFonts.nunitoSans(fontSize: 14, fontWeight: FontWeight.normal, color: GetIt.I.get<ControladorApp>().defaultColor ?? HexColor.fromHex('#80858C'));
  static TextStyle styleaction2() => GoogleFonts.nunitoSans(fontSize: 14, fontWeight: FontWeight.w600, color: GetIt.I.get<ControladorApp>().defaultColor ?? HexColor.fromHex('#80858C'));
  static TextStyle styleCircleAvatar() => GoogleFonts.nunitoSans(fontSize: 10, fontWeight: FontWeight.w400, color: Colors.white);
}
