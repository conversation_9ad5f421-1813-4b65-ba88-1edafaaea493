// // import 'package:smartlook/smartlook.dart';

logEventoTela(EventosKey eventoKey) {
//   // Smartlook.trackNavigationEvent(eventoKey.toString().replaceAll("EventosKey.", ""), SmartlookNavigationEventType.enter);
}

enum EventosKey {
  veio_de_banner_inapp,
  clickCompraReceita,
  abriu_app,
  clickCompraDicas,
  podomretro,
  ativar_premium,
  avaliou_treino,
  share_result_treino,
  avaliou_app,
  treino_cinco_estrelas,
  tabbar_home_fit,
  click_atv_hj,
  avaliou_professor,
  abrir_ad_premium,
  click_push,
  veioPorDeep,
  meuDia_Hoje,
  meuDia_Dietas,
  meuDia_Receitas,
  meuDia_Novidades,
  click_unlock_prm,
  meuDia_Cafe,
  preLogin_sair,
  meuDia_Lanche1,
  meuDia_Almoco,
  meuDia_Jantar,
  meu<PERSON><PERSON>_<PERSON><PERSON>,
  meuDia_Lanche2,
  preLogin_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  preLog<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  preLogin_professor,
  naoTenhoUsuario_sair,
  naoTenhoUsuario_localizarUnidades,
  localizarAcademia_sair,
  localizarAcademia_adicMarcador,
  localizarAcademia_ligar,
  localizarAcademia_pesquisar,
  loginProfessor_sair,
  loginProfessor_usuario,
  loginProfessor_senha,
  loginProfessor_fazerLogin,
  loginProfessor_politicaDePrivacidade,
  login_sair,
  login_sucesso,
  login_usuario,
  login_senha,
  login_fazerLogin,
  login_NaoTenhoUsuario,
  login_esqueceuSenha,
  login_politicaDePrivacidade,
  primeiroAcesso_sair,
  primeiroAcesso_email,
  primeiroAcesso_celular,
  primeiroAcesso_buscar,
  primeiroAcesso_conectarFacebook,
  primeiroAcesso_conectarGoogle,
  verificacao_sair,
  verificacao_receberSms,
  verificacao_receberEmail,
  verificacao_NaoEncontrou,
  verificacaoCodigo_sair,
  verificacaoCodigo_digitarCodigo,
  verificacaoCodigo_validar,
  feed_menuLateral,
  feed_publicarComCamera,
  feed_publicarComGaleria,
  feed_curtir,
  feed_comentar,
  feed_compartilhar,
  feed_removerPublicacao,
  feed_salvarImagem,
  feed_denunciarPublicacao,
  feed_cancelar,
  menuLateral_perfil,
  menuLateral_voltouFeed,
  menuLateral_avaliacaoFisica,
  menuLateral_minhaAgenda,
  menuLateral_dicasDeSaude,
  menuLateral_refeicoes,
  menuLateral_beberAgua,
  menuLateral_loja,
  menuLateral_conversor,
  menuLateral_configuracoes,
  menuLateral_sobre,
  menuLateral_sair,
  menuLateral_academia,
  menuLateral_biomonitor,
  menulateral_creditos,
  aulas_calendario,
  aulas_marcarAula,
  marcarAula_confirmar,
  aulas_desmarcarAula,
  aulas_favoritar,
  aulas_abrirAula,
  detalhesAulas_marcar,
  detalhesAulas_desmarcar,
  detalhesAulas_sair,
  personalRecords_filtro,
  personalRecords_buscar,
  personalRecords_abrirRecord,
  detalhesRecords_adicionar,
  detalhesRecords_sair,
  adicionarRecord_data,
  adicionarRecord_informacao,
  adicionarRecord_salvar,
  cross_adicionar,
  cross_calendario,
  cross_ranking,
  cross_Wod,
  cross_music,
  detalhesWod_sair,
  detalhesWod_adicionar,
  detalhesWod_enviarComentario,
  adicionarResultadoWod_Nivel,
  adicionarResultadoWod_valor,
  adicionarResultadoWod_salvar,
  adicionarResultadoWod_compartilhar,
  rankings_adicionar,
  rankings_abrir,
  detalheRanking_filtros,
  detalheRanking_sair,
  filtroRanking_aplicar,
  treinoFichas_abrirDetalhesPrograma,
  treinoFichas_abrirFicha,
  detalhesPrograma_fechar,
  treino_sair,
  treino_music,
  treino_comprimir,
  treino_expandir,
  treino_detalheAtividade,
  treino_iniciar,
  treino_online,
  treinoExecucao_concluir,
  treinoExecucao_concluir_automaticamente,
  treinoExecucao_sair,
  treinoExecucao_pausa,
  treinoExecucao_detalheAtividade,
  treinoExecucao_concluirAtividade,
  treinoExecucao_editarCarga,
  treinoFichas_desabilitou_descanso,
  treinoFichas_habilitou_descanso,
  treinoFichas_souAluno,
  treino_mensagemProfessor,
  pauseTreino_abandonar,
  pauseTreino_continuar,
  pauseTreino_feed,
  parabensTreino_clickSwipe_nivel,
  parabensTreino_salvar,
  feedPublicou_sucesso,
  notificacoes_expandir,
  notificacoes_swipeLeftorRigh_excluir,
  avaliacaoFisica_bioImpedancia,
  avaliacaoFisica_agendar,
  avaliacaoFisica_sair,
  configBioAvaliacaoFisica_iniciar,
  configBioAvaliacaoFisica_sair,
  configBioAvaliacaoFisica_altura,
  configBioAvaliacaoFisica_idade,
  configBioAvaliacaoFisica_proximo,
  configBioAvaliacaoFisica_alimentacao,
  configBioAvaliacaoFisica_roupasLeves,
  configBioAvaliacaoFisica_aparelhosEletronicos,
  configBioAvaliacaoFisica_acessorios,
  configBioAvaliacaoFisica_confirmar,
  agendarAvaliacaoFisica_sair,
  agendarAvaliacaoFisica_marcar,
  detalhesAgendarAvaliacaoFisica_sair,
  detalhesAgendarAvaliacaoFisica_marcar,
  avalicaoFisica_fotoFrontal,
  avalicaoFisica_fotoFrontalContraido,
  avalicaoFisica_costasFrontalContraido,
  avalicaoFisica_lateral,
  avalicaoFisica_costas,
  agenda_sair,
  dicasDeSaude_sair,
  dicasDeSaude_destaque,
  dicasDeSaude_Novidade,
  dicasDeSaude_maisAcessada,
  dicasDeSaude_ultimasAcessada,
  detalhesSaude_sair,
  detalhesSaude_curtir,
  detalhesSaude_descurtir,
  detalhesSaude_outrasDicas,
  configRefeicoes_inicio,
  configRefeicoes_ganhoMassa,
  configRefeicoes_emagrecimento,
  configRefeicoes_frutosDoMar,
  configRefeicoes_frutose,
  configRefeicoes_gluten,
  configRefeicoes_lactose,
  configRefeicoes_proximo,
  configRefeicoes_1Lanche,
  configRefeicoes_2Lanche,
  configRefeicoes_3Lanche,
  configRefeicoes_clickView_sair,
  refeicoes_config,
  refeicoes_sair,
  refeicoes_jantar,
  refeicoes_lanche,
  refeicoes_almoco,
  refeicoes_ceia,
  refeicoes_cafe,
  refeicoes_cafe_hoje,
  refeicoes_almoco_hoje,
  refeicoes_lanche_hoje,
  refeicoes_jantar_hoje,
  detalhesRefeicoes_sair,
  detalhesRefeicoes_infoNutricao,
  refeicoesInfoNutricao_sair,
  refeicoesOutras_sair,
  refeicoesOutras_subistituir,
  beberAgua_sair,
  beberAgua_adicionar,
  beberAgua_estatisticas,
  beberAgua_config,
  configuracoBeberAgua_sair,
  configuracoBeberAgua_slide_quantidade,
  configuracoBeberAgua_slide_frequencia,
  configuracoBeberAgua_input_inicial,
  configuracoBeberAgua_input_final,
  configuracoBeberAgua_domingo,
  configuracoBeberAgua_segunda,
  configuracoBeberAgua_terca,
  configuracoBeberAgua_quarta,
  configuracoBeberAgua_quinta,
  configuracoBeberAgua_sexta,
  configuracoBeberAgua_sabado,
  beberAguaEstatisticas_sair,
  shopping_sair,
  conversor_sair,
  configuracoes_sair,
  configuracoes_Notificacoes,
  configuracoes_som,
  configuracoes_Narracao,
  configuracoes_pularDescanso,
  configuracoes_idioma,
  configuracoes_unidadeDeMedidaLibras,
  configuracoes_unidadeDeMedidaQuilos,
  sobre_sair,
  sobre_politicaDePrivacidade,
  sobre_atendimento,
  sobre_versao,
  sobre_biblioteca_de_codigoAberto,
  sobre_tutorial,
  politicaDePrivacidade_sair,
  atendimento_sair,
  perfil_configuracaoPerfil,
  perfil_sair,
  perfil_contrato,
  perfil_conta,
  perfil_avaliacaoFisicaPerfil,
  perfil_minhasPublicacoesPerfil,
  conta_sair,
  conta_senha,
  conta_telefone,
  conta_email,
  conta_pagamento,
  contrato_sair,
  contrato_renovarContrato,
  contrato_trancarContrato,
  contrato_ferias,
  contaSenha_sair,
  contaSenha_senhaAtual,
  contaSenha_NovaSenha,
  contaSenha_confirmarNovaSenha,
  contaSenha_confirmar,
  contaTelefone_sair,
  contaTelefone_telefone,
  contaTelefone_confirmar,
  contaEmail_sair,
  contaEmail_email,
  contaEmail_confirmar,
  contaPagamento_sair,
  contaPagamento_NumeroCartao,
  contaPagamento_titularCartao,
  contaPagamento_validadeMes,
  contaPagamento_validadeAno,
  contaPagamento_cVV,
  contaPagamento_confirmar,
  minhasPublicacoes_sair,
  contratoFerias_sair,
  contratoFerias_dataInicio,
  contratoFerias_dataFim,
  contratoFerias_motivo,
  contratoFerias_confirmar,
  contratoTrancamento_sair,
  contratoTrancamento_proximo,
  contratoTrancamento_periodo,
  contratoRenovacao_sair,
  contratoRenovacao_confirmar,
  feedNewPost_sair,
  feedNewPost_publicar,
  feedNewPost_legendaPost,
  feedNewPost_clickSwipeButton_share,
  feed_usuarioAtualizou,
  feed_usuarioRolouPraBaixo,
  tabbar_feed,
  tabbar_aulas,
  tabbar_cross,
  tabbar_treino,
  tabbar_notificacoes,
  tabbar_refeicoes,
  tabbar_perfil,
  premium_avancarParaDetalhes,
  premium_desistiuAntesDosDetalhes,
  premium_clicouEmComprar,
  premium_clicouRestaurar,
  nome_academia,
  chave_academia,
  novoLoginBeta_sair,
  novoLoginBeta_celular,
  novoLoginBeta_buscar,
  novoLoginBeta_acessarComUsuario,
  validacaoBeta_sair,
  validacaoBeta_clicouEmValidar,
  primeiroAcesso_comecar,
  unidade_academia,
  iniciouNovoLogin,
  buscouSucesso,
  buscouNaoEncontrou,
  buscouDuplicado,
  logouNovoLoginTelefone,
  logouNovoLoginUsuario,
  logouVelhoLogin,
  iniciouVelhoLogin,
  nutri_banner_inicio,
  nutri_tabbar_inicio,
  nutri_anamnese_obterPlano,
  nutri_anamnese_pg1_objetivo,
  nutri_anamnese_pg2_sexo,
  nutri_anamnese_pg3_altura,
  nutri_anamnese_pg4_metaPeso,
  nutri_anamnese_pg5_qtRefeicoes,
  nutri_anamnese_pg6_variedade,
  nutri_anamnese_pg7_atvFisica,
  nutri_anamnese_pg8_restricao,
  nutri_anamnese_ob_emagrecer,
  nutri_anamnese_ob_manterPeso,
  nutri_anamnese_ob_ganharMassa,
  nutri_anamnese_sx_feminino,
  nutri_anamnese_sx_masculino,
  nutri_anamnese_qt_4,
  nutri_anamnese_qt_5,
  nutri_anamnese_qt_6,
  nutri_anamnese_variedade_nenhuma,
  nutri_anamnese_variedade_pouca,
  nutri_anamnese_variedade_alta,
  nutri_anamnese_atv_nenhuma,
  nutri_anamnese_atv_1a2,
  nutri_anamnese_atv_3a5,
  nutri_anamnese_atv_todosDias,
  nutri_anamnese_onboard_finalizou,
  nutri_calendario_mudouDia,
  nutri_meuplano_verMais,
  nutri_consumoagua_adicionou,
  nutri_consumoAgua_removeu,
  nutri_consumoAgua_abriuDetalhes,
  nutri_peso_aumentou,
  nutri_peso_diminuiu,
  nutri_peso_abriuDetalhes,
  nutri_minhasRefeicoes_verMais,
  nutri_minhasRefeicoes_verDetalhes,
  nutri_minhasRefeicoes_registrar,
  nutri_minhasRefeicoes_cancelarRegistro,
  nutri_minhasRefeicoes_favoritar,
  nutri_respiracao_acessar,
  nutri_respiracao_iniciar,
  nutri_maisReceitas_acessar,
  nutri_maisReceitas_detalhes,
  nutri_maisReceitas_filtros,
  nutri_maisPlanos_acessar,
  nutri_maisPlanos_acessarNovidade,
  nutri_maisPlanos_detalhes,
  nutri_maisPlanos_comecar,
  nutri_dicasRapidas_acessar,
  nutri_dicasRapidas_detalhes,
  nutri_listaCompras_acessar,
  nutri_listaCompras_definirPeriodo,
  nutri_listaCompras_marcarAlimento,
  nutri_listaCompras_exportar,
  nutri_minhaHidratacao_acessar,
  nutri_minhaHidratacao_salvarAlteracoes,
  nutri_metaPeso_acessar,
  nutri_metaPeso_salvarAlteracoes,
  nutri_lembretes_acessar,
  nutri_lembretes_salvarAlteracoes,
  nutri_planoClassico_comecar,
  nutri_confirmarReceitas_confirmarTodas,
  nutri_confirmarReceitas_finalizar,
  nutri_confirmarReceitas_gostei,
  nutri_confirmarReceitas_naoGostei,
  nutri_confirmarReceitas_iniciarAgora,
  nutri_confirmarReceitas_agendar,
  nutri_premium_abriuPremium,
  nutri_premium_aproveitarOferta,
  nutri_premium_conferirPlanos,
  nutri_premium_comprarMensal,
  nutri_premium_selecionouMensal,
  nutri_premium_comprouPlanoMensal,
  nutri_premium_selecionouAnual,
  nutri_premium_comprarAnual,
  nutri_premium_comprouPlanoAnual,
  usou_wod,

  //cross eventos
  cross_pressionou_calendario,
  cross_pressionou_notificacoes,
  cross_pressionou_dia,
  cross_pressionou_wod,
  cross_pressionou_timer,
  cross_pressionou_pr,
  cross_ranking_pressionou_ver_mais,
  wod_pressionou_exercicio,
  wod_pressionou_postar_resultado,
  wod_exercicio_pressionou_novo_record,
  wod_exercicio_pressionou_ver_mais,
  wod_ranking_pressionou_ver_mais,
  novo_pr_pressionou_sair,
  novo_pr_pressionou_salvar,
  meu_resultado_selecionou_categoria,
  meu_resultado_pressionou_postar,
  meu_resultado_saiu_sem_confirmar_alteracoes, //
  ranking_pressionou_editar_resultado, //
  ranking_selecionou_categoria, //
  cronometro_pressionou_iniciar_timer,
  emom_pressionou_iniciar_timer,
  tabata_pressionou_iniciar_timer,
  timer_pressionou_minimizar,
  timer_pressionou_play_pause,
  timer_pressionou_stop, //
  records_pessoais_selecionou_tipo, //
  records_pessoais_pressionou_exercicio, //

  //treino eventos
  treino_pressionou_notificacoes, //
  treino_pressionou_treino_do_dia,
  treino_pressionou_sua_ficha,
  ficha_pressionou_exercicio,
  ficha_pressionou_comecar_treino,
  ficha_exercicio_pressionou_add_foto,
  ficha_exercicio_pressionou_ver_video,
  ficha_exercicio_pressionou_editar,
  ficha_pressionou_pausar, //
  ficha_pressionou_finalizar, //
  ficha_pressionou_concluir_serie, //
  alterar_carga_salvou, //
  alterar_carga_checked_alterar_series, //
  controlar_descanso_pelo_app, //
  nao_controlar_descanso_pelo_app, //
  treino_pausado_pressionou_stop,
  treino_pausado_pressionou_play,
  treino_pausado_pressionou_home,
  pressionou_finalizar_execucao_treino,
  avaliacao_treino_selecionou_estrelas,
  avaliacao_treino_enviou_avaliacao,

  //aulas eventos
  aulas_pressionou_dia,
  aulas_pressionou_more_vertical,
  aulas_pressionou_calendario,
  aulas_pressionou_checkin,
  aulas_pressionou_cancelar_aula,
  aulas_checkin_presionou_cancelar,
  aulas_checkin_pressionou_qrcode,
  aulas_pressionou_trocar_unidade,
  aulas_calendario_pressionou_dia,
  aulas_calendario_pressionou_mes,
  aulas_pressionou_aula,
  aula_detalhes_pressionou_cancelar,
  aula_detalhes_pressionou_checkin,

  //em casa
  em_casa_pressionou_ranking,
  em_casa_pressionou_em_alta,
  em_casa_pressionou_filtro,
  em_casa_pressionou_treino,
  em_casa_pressionou_ao_vivo,
  ranking_tutorial_pressionou_acessar,
  ranking_pressionou_medalhas,
  detalhes_treino_pressionou_video,
  detalhes_treino_pressionou_completar,
  detalhes_do_treino_pressionou_compartilhar,
  compartilhar_pressionou_share,
  compartilhar_escolheu_instagram,
  compartilhar_salvou_na_galeria,

  //agenda
  agenda_pressionou_dia,
  pressionou_confirmar_agendamento,
  agenda_pressionou_marcar_disponivel,
  agenda_pressionou_desmarcar,
  confirmou_desmarcar_agendamento,
  pressionou_remarcar_agendamento,
  reagendar_pressionou_dia,
  reagendar_selecionou_horario,
  reagendar_confirmou_agendamento,


  //colab
  //aulas
  minhas_aulas_pressionou_calendario,
  minhas_aulas_pressionou_detalhes,
  minhas_aulas_pressionou_aula,
  aula_confirmou_presenca_aluno,
  presenca_qrcode_continuar,

  //drawer
  drawer_selecionou_estou_em,
  drawer_pressionou_fichas,
  drawer_pressionou_cadastrar_wod,
  drawer_pressionou_logout,
  drawer_confirmou_logout,

  //fichas
  fichas_pressionou_pular_tutorial,
  fichas_pressionou_filtrar,
  fichas_pressionou_nova_ficha,
  fichas_pressionou_ficha,
  filtrar_treinos_selecionou_sexo,
  filtrar_treinos_selecionou_nivel,
  filtrar_treinos_selecionou_categoria,
  filtrar_treinos_selecionou_objetivos,
  ficha_selecionou_sexo,
  ficha_selecionou_tipo,
  ficha_selecionou_nivel,
  ficha_selecionou_categoria,
  ficha_pressionou_avancar,
  ficha_salvou,
  dados_ficha_pressionou_editar,
  dados_ficha_gerar_padrao_serie,
  padrao_serie_pressionou_salvar,
  dados_ficha_pressionou_switcher,
  dados_ficha_atividades_adicionar,
  dados_ficha_escolher_aluno,
  dados_ficha_atividade_removeu,
  dados_ficha_pressionou_atividade,
  selecionar_atividades_selecionou,
  selecionar_atividades_adicionou,
  ficha_salvar_como_pre_definida,
  ficha_apenas_adicionar_ao_aluno,
  ficha_salva_adicionar_a_aluno,
  selecione_o_aluno_selecionou_categoria,
  selecione_o_aluno_selecionou,
  selecione_o_aluno_adicionou_ficha,
  tentou_selecionar_mais_alunos,
  pressionou_add_a_outro_aluno,

  //wods
  wods_pressionou_add_wod,
  wods_pressionou_wod,
  wods_pressionou_clonar,
  wods_pressionou_apagar,
  wod_pressionou_continuar,
  wod_selecionou_data,
  wod_selecionou_tipo,
  wod_pressionou_selecionar_imagem,
  wod_pressionou_adicionar_aparelho,
  wod_pressionou_remover_aparelho,
  wod_pressionou_salvar,
  wod_pressionou_adicionar_atividade,
  wod_pressionou_remover_atividade,
  aparelhos_cross_pressionou_adicionar,
  aparelhos_cross_selecionou,
  atividades_pressionou_adicionar,
  atividades_selecionou,
  wod_pressionou_trocar_imagem,



  //camera
  camera_pressionou_capturar,
  camera_pressionou_fotos,
  recortar_imagem_pressionou_avancar,
  aplicar_filtro_concluiu,
  aplicar_filtro_selecionou_filtro,


  //inicio
  inicio_meus_programas_detalhes,
  inicio_aulas_ver_mais,
  inicio_gerencie_ver_aulas,
  prescricoes_selecionou_total_fazer,
  prescricoes_selecionou_renovacoes,
  prescricoes_selecionou_sem_treino,
  prescricoes_pressionou_aluno,
  aluno_reconhecimento_facial,
  aluno_pressionou_mensagem,
  aluno_avaliacao_fisica_detalhes,
  aluno_programa_treino_detalhes,
  aluno_historico_ver_mais,
  aluno_observacoes_ver_mais,
  aluno_frequencia_selecionou,
  aluno_cadastrar_nova_ficha,
  aluno_programa_treino_renovar,
  aluno_programa_treino_criar,
  registro_facial_continuar_registro,
  programa_selecionou_inicio,
  programa_selecionou_termino,
  programa_selecionou_objetivo,
  programa_desselecionou_objetivo,
  programa_pressionou_salvar,
  coach_programa_treino_pulou,

  //alunos
  alunos_selecionou_vencidos,
  alunos_selecionou_minha_carteira,
  alunos_selecionou_sem_treino,
  alunos_selecionou_inativos,
  alunos_pressionou_aluno,

  //treino ao vivo
  criar_treino_on_detalhes_definir,
  criar_treino_on_pressionou_cam,
  criar_treino_on_selecionou_capa,
  detalhes_pressionou_detalhe,
  criar_treino_on_pressionou_proximo,
  detalhes_pressionou_criar_tag,
  criar_tag_pressionou_salvar,
  criar_treino_on_adicionar_video,
  criar_treino_on_pressionou_salvar,
  adicionar_video_pressionou_salvar,
  criar_treino_on_remover_video,

  //login
  selecione_academia_selecionou_academia,
  acessar_com_telefone_selecionou_pais,
  acessar_com_telefone_continuar,
  acessar_com_telefone_acessar_com_email,
  acessar_com_telefone_ainda_nao_e_aluno,
  bem_vindo_entrar_com_outro_usuario,
  bem_vindo_entrou_com_usuario_logado,
  acessar_com_telefone_usuarios_logados,
  acessar_com_email_continuar,
  acessar_com_email_acessar_com_telefone,
  acessar_com_email_esqueci_senha,
  recuperacao_senha_continuar,
  login_cpf_continuar,
  login_cpf_acessar_com_email,

  //inicio
  inicio_pressionou_mensagens,
  inicio_pressionou_notificacoes,
  inicio_pressionou_dia,
  inicio_pressionou_treino_do_dia,
  inicio_proximas_aulas_agendar,
  inicio_cross_ver_mais,
  inicio_ativar_monitor_de_passos,

  //feed
  feed_pressionou_notificacoes,
  feed_pressionou_nova_publicacao,
  feed_popup_pressionou_comentar,
  feed_popup_pressionou_remover,
  feed_popup_pressionou_denunciar,
  feed_popup_pressionou_bloquear,
  bloquear_confirmou_bloqueio,
  pressionou_popup_postagem,
  post_pressionou_like,
  feed_pressionou_comentar,
  notificacoes_pressionou_notificacao,
  nova_publicacao_pressionou_enviar,
  nova_publicacao_adicionar_foto,
  proximas_aulas_cancelar_aula,
  comentarios_pressionou_enviar_comentario,
  denuncia_pressionou_denunciar_post,


  //drawer
  drawer_pressionou_configurar_app,
  drawer_pressionou_campanhas_inapp,
  drawer_pressionou_contratos,
  drawer_pressionou_minha_conta,
  drawer_pressionou_avaliacao_fisica,
  drawer_pressionou_publicacoes,
  drawer_pressionou_avaliar_professor,
  drawer_pressionou_sobre_o_app,
  escolha_tema_selecionou_claro,
  escolha_tema_selecionou_escuro,
  escolha_tema_selecionou_automatico,
  escolha_tema_pressionou_ok,
  configuracoes_pressionou_tema,
  configuracoes_descanso_switcher,
  configuracoes_unidade_libras,
  configuracoes_unidade_quilos,
  campanhas_cadastrar_campanha,
  campanhas_pressionou_campanha,
  campanha_validar_campanha,
  campanha_switcher_campanha_ativar,
  campanha_upload_de_imagem,
  campanha_definiu_periodicidade,
  campanha_definiu_inicio,
  campanha_definiu_fim,
  campanha_definiu_hora_inicio,
  campanha_definiu_hora_fim,
  campanha_pressionou_novo_range,
  campanha_deletou_range_horarios,
  campanha_definiu_range_horarios,
  contrato_pressionou_trancamento,
  contrato_pressionou_renovar,
  contrato_pressionou_credito,
  trancamento_selecionou_produto,
  trancamento_selecionou_motivo,
  trancamento_pressionou_avancar,
  trancamento_confirmou_trancamento,
  perfil_pressionou_minha_conta,
  perfil_pressionou_foto,
  perfil_pressionou_graduacao,
  perfil_pressionou_premium,
  perfil_pressionou_avaliacao_fisica,
  perfil_pressionou_meus_contratos,
  minha_conta_trocar_foto_perfil,
  creditos_contrato_selecionou_extrato,
  minha_conta_acesso_editar,
  minha_conta_contato_editar,
  alterar_senha_pressionou_salvar,
  dados_contato_pressionou_salvar,
  minhas_assinaturas_pressionou_ticket,
  avaliacao_fisica_selecionou_perimetria,
  avaliar_professor_selecionar_professor,
  avaliar_professor_selecionou_estrelas,
  avaliar_professor_pressionou_confirmar,
  selecione_professor_selecionou_professor,
  
  //ajuda
  ajuda_pressionou_video,
  ajuda_pressionou_refazer_tutorial,
  ajuda_pressionou_informacao,
  refazer_tutorial_pressionou_refazer,

  
  wod_postar_resultado_home,
  entrou_fila_de_espera,
  acessou_acompanhamento_aluno,
  acessou_pagamento_parcelas_atrasadas,
  pagamento_parcelas_atrasadas_aprovado,
  pagamento_parcelas_atrasadas_negado,
  click_aprovouTreinoRevisadoIA,
  abriuTelaRevisarTreinoDeAlunoIA,
  click_revisarTreinoIA,
  click_aprovarTreinoIA,
  abriu_treinos_ia_revisar,
  click_obterMeuTreinoIA,
  click_alunoTemRestricaoIA,
  click_alunoNaoTemRestricaoIA,
  click_AlunoAvancarQuestinarioIA,
  click_alunoConfirmouSemRestricoes,
  click_AlunoObjEmagrecimento,
  click_AlunoObjEmaGanhoDeMassa,
  click_AlunoObjQualidadeVida,
  click_AlunoObjControlarDoenca,
  click_AlunoGeneroMasculino,
  click_AlunoGeneroFeminino,
  aluno_escolheuIdadeIA,
  aluno_escolheuAlturaIA,
  aluno_escolheuNivelIA,
  click_alunoSelecionouExperienciaIA,
  alunoSelecionouDiasPorSemanaIA,
  // Novo Login
  lgn_apple,
  lgn_google,
  lgn_facebook,
  lgn_com_user,
  USUARIO_FEZ_LOGIN_APP_TREINO,
  CHECKIN_AULA_APP_TREINO,
  TREINO_CONCLUIDO_APP_TREINO,
  lgn_solicitarsms,
  lgn_vincularApple,
  lgn_validousms,
  lgn_userPass,
  lgn_telefone,
  lgn_novoSMS,
  lgn_novoEmail,
  lgn_recent_user,
  lgn_solicitar_email,
  lgn_por_senha,
  lgn_validarsms,
  lgn_veio_de_link_email,
  lgn_resolicitarsms,
  alunoSelecionouMinutosIA,
  fechou_banner_inapp,
  log_out_preso_splash,
  sem_health,
  sem_health_baixar_google_fit,
  stuck_to_home,
  aluno_escolheuPesoIA,
  abriu_tela_chat

}
