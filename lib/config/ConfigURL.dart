import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/fabricaGetIt.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ConfigURL {
  static const TREINO = '{TREINO}';
  static const UCP = '{UCP}';
  static const TOKENWEB = '{TOKENWEB}';
  static const OAMD = '{OAMD}';
  static const APIZW = '{APIZW}';
  static const PUSH = '{PUSH}';
  static const DICASNUTRI = '{DICASNUTRI}';
  static const FIREBASE = '{FIREBASE}';
  static const PERSONAGEM = '{PERSONAGEM}';
  static const ALCIDES = '{ALCIDDES}';
  static const URLZW = '{URLZW}';
  static const DESCOVERY = '{DESCOVERY}';
  static const TREINOSIMPLES = '{TREINOSIMPLES}';
  static const GRADUACAO = '{GRADU<PERSON>A<PERSON>}';
  static const MOCK = '{MOCK}';
  static const VITIO = '{VITIO}';
  static const ACESSOMS = '{ACESSOMS}';
  static const ADMCORE = '{ADMCORE}';
  static const CONTATOMSURL = '{contatoMsUrl}';
  static upDateURLS(Map<String, String> mapa, Function()? sucesso) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    for (final element in mapa.entries) {
      prefs.setString(element.key, element.value);
    }
    prefs.setBool('SkipLogin', GetIt.I.get<ControladorApp>().pularSenhaLogin);
    prefs.setBool('showHtpLog', modoHomologacao);
    sucesso?.call();
  }

  static goBackChild(Function()? sucesso) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString(ConfigURL.TREINO.toString(), '');
    prefs.setString(ConfigURL.UCP.toString(), 'https://app.pactosolucoes.com.br/ucp/');
    prefs.setString(ConfigURL.OAMD.toString(), 'https://app.pactosolucoes.com.br/oamd/');
    prefs.setString(ConfigURL.APIZW.toString(), 'https://app.pactosolucoes.com.br/api/');
    prefs.setString(ConfigURL.PERSONAGEM.toString(), 'https://personagem.ms.pactosolucoes.com.br');
    prefs.setString(ConfigURL.GRADUACAO.toString(), 'https://graduacao.ms.pactosolucoes.com.br/');
    prefs.setString(ConfigURL.TOKENWEB.toString(), 'https://auth.ms.pactosolucoes.com.br/');
    prefs.setString(ConfigURL.DESCOVERY.toString(), 'https://discovery.ms.pactosolucoes.com.br/');
    prefs.setString(ConfigURL.MOCK.toString(), 'https://wbskt.pactosolucoes.com.br/');
    prefs.setString(ConfigURL.URLZW.toString(), '');
    prefs.setString(ConfigURL.VITIO.toString(), 'https://vitio-production-aej2ba9c.uk.gateway.dev/api/');
    prefs.setString(ConfigURL.ADMCORE.toString(), '');
    prefs.setString(ConfigURL.CONTATOMSURL.toString(), '');

    //prefs.setString(ConfigURL.VITIO.toString(), 'https://vitio-staging-cu9o0vpw.uk.gateway.dev/api/');
    prefs.setString(ConfigURL.ACESSOMS.toString(), '');
    prefs.setBool('SkipLogin', false);
    prefs.setBool('showHtpLog', false);
    prefs.setBool('utlizaDiscovery', true);
    sucesso?.call();
  }

  static getListaUrls({required Function(Map<String, Map<String, String>> lista) sucesso}) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    sucesso.call({
      'Url Discovery': {ConfigURL.DESCOVERY.toString(): prefs.getString(ConfigURL.DESCOVERY.toString()) ?? 'https://discovery.ms.pactosolucoes.com.br/'},
      'Url Treino': {ConfigURL.TREINO.toString(): prefs.getString(ConfigURL.TREINO.toString()) ?? ''},
      'Url Zw': {ConfigURL.URLZW.toString(): prefs.getString(ConfigURL.URLZW.toString()) ?? ''},
      'Url Acesso': {ConfigURL.ACESSOMS.toString(): prefs.getString(ConfigURL.ACESSOMS.toString()) ?? ''},
      'Url UCP': {ConfigURL.UCP.toString(): prefs.getString(ConfigURL.UCP.toString()) ?? 'https://app.pactosolucoes.com.br/ucp/'},
      'Url Oamd': {ConfigURL.OAMD.toString(): prefs.getString(ConfigURL.OAMD.toString()) ?? 'https://app.pactosolucoes.com.br/oamd/'},
      'Url Api ZW': {ConfigURL.APIZW.toString(): prefs.getString(ConfigURL.APIZW.toString()) ?? 'https://app.pactosolucoes.com.br/api/'},
      'Url Personagem': {ConfigURL.PERSONAGEM.toString(): prefs.getString(ConfigURL.PERSONAGEM.toString()) ?? 'https://personagem.ms.pactosolucoes.com.br'},
      'Url Graduação': {ConfigURL.GRADUACAO.toString(): prefs.getString(ConfigURL.GRADUACAO.toString()) ?? 'https://graduacao.ms.pactosolucoes.com.br/'},
      'Url Mock ZW': {ConfigURL.MOCK.toString(): prefs.getString(ConfigURL.MOCK.toString()) ?? 'https://wbskt.pactosolucoes.com.br/'},
      'Url Autenticação': {ConfigURL.TOKENWEB.toString(): prefs.getString(ConfigURL.TOKENWEB.toString()) ?? 'https://auth.ms.pactosolucoes.com.br/'},
     'Url AdmCore': {ConfigURL.ADMCORE.toString(): prefs.getString(ConfigURL.ADMCORE.toString()) ?? ''},
     'Url Contato Ms': {ConfigURL.CONTATOMSURL.toString(): prefs.getString(ConfigURL.CONTATOMSURL.toString()) ?? ''},
    });
  }


  static Future<String?> getKeyByUrl(String url) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    Map<String, String> urls = {
      prefs.getString(ConfigURL.TREINO.toString()) ?? '': ConfigURL.TREINO,
      prefs.getString(ConfigURL.TOKENWEB.toString()) ?? 'https://auth.ms.pactosolucoes.com.br/': ConfigURL.TOKENWEB,
      prefs.getString(ConfigURL.OAMD.toString()) ?? 'https://app.pactosolucoes.com.br/oamd/': ConfigURL.OAMD,
      prefs.getString(ConfigURL.APIZW.toString()) ?? 'https://app.pactosolucoes.com.br/api/': ConfigURL.APIZW,
      prefs.getString(ConfigURL.PUSH.toString()) ?? '': ConfigURL.PUSH,
      prefs.getString(ConfigURL.DICASNUTRI.toString()) ?? '': ConfigURL.DICASNUTRI,
      'https://sinai.pactosolucoes.com.br/': ConfigURL.FIREBASE,
      'https://vitio-production-aej2ba9c.uk.gateway.dev/api/': ConfigURL.VITIO,
      'https://personagem.ms.pactosolucoes.com.br': ConfigURL.PERSONAGEM,
      'https://indicacao.ms.pactosolucoes.com.br/': ConfigURL.ALCIDES,
      prefs.getString(ConfigURL.UCP.toString()) ?? 'https://app.pactosolucoes.com.br/ucp/': ConfigURL.UCP,
      prefs.getString(ConfigURL.GRADUACAO.toString()) ?? 'https://graduacao.ms.pactosolucoes.com.br/': ConfigURL.GRADUACAO,
      prefs.getString(ConfigURL.URLZW.toString()) ?? 'https://app.pactosolucoes.com.br/ucp/': ConfigURL.URLZW,
      prefs.getString(ConfigURL.DESCOVERY.toString()) ?? 'https://discovery.ms.pactosolucoes.com.br/': ConfigURL.DESCOVERY,
      prefs.getString(ConfigURL.MOCK.toString()) ?? 'https://wbskt.pactosolucoes.com.br/': ConfigURL.MOCK,
      prefs.getString(ConfigURL.ACESSOMS.toString()) ?? '': ConfigURL.ACESSOMS,
      prefs.getString(ConfigURL.CONTATOMSURL.toString()) ?? '': ConfigURL.CONTATOMSURL,
    };

    return urls[url];
  }

  Future<String?> url(String value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    switch (value) {
      case TREINO:
        return prefs.getString(ConfigURL.TREINO.toString());
      case TOKENWEB:
        return prefs.getString(ConfigURL.TOKENWEB.toString()) ?? 'https://auth.ms.pactosolucoes.com.br/';
      case OAMD:
        return prefs.getString(ConfigURL.OAMD.toString()) ?? 'https://app.pactosolucoes.com.br/oamd/';
      case APIZW:
        return prefs.getString(ConfigURL.APIZW.toString()) ?? 'https://app.pactosolucoes.com.br/api/';
      case PUSH:
        return prefs.getString(ConfigURL.PUSH.toString());
      case DICASNUTRI:
        return prefs.getString(ConfigURL.DICASNUTRI.toString());
      case FIREBASE:
        return 'https://sinai.pactosolucoes.com.br/';
      case VITIO:
        return 'https://vitio-production-aej2ba9c.uk.gateway.dev/api/';
      //case VITIO:
      //  return 'https://vitio-staging-cu9o0vpw.uk.gateway.dev/api/';
      case PERSONAGEM:
        return 'https://personagem.ms.pactosolucoes.com.br';
      case ALCIDES:
        return 'https://indicacao.ms.pactosolucoes.com.br/';
      case UCP:
        return prefs.getString(ConfigURL.UCP.toString()) ?? 'https://app.pactosolucoes.com.br/ucp/';
      case TREINOSIMPLES:
        var string = prefs.getString(ConfigURL.TREINO.toString());
        var parts = string!.split('/TreinoWeb');
        return parts[0].trim();
      case GRADUACAO:
        return prefs.getString(ConfigURL.GRADUACAO.toString()) ?? 'https://graduacao.ms.pactosolucoes.com.br/';
      case URLZW:
        return prefs.getString(ConfigURL.URLZW.toString()) ?? 'https://app.pactosolucoes.com.br/ucp/';
      case DESCOVERY:
        return prefs.getString(ConfigURL.DESCOVERY.toString()) ?? 'https://discovery.ms.pactosolucoes.com.br/';
      case MOCK:
        return prefs.getString(ConfigURL.MOCK.toString()) ?? 'https://wbskt.pactosolucoes.com.br/';
      case ACESSOMS:
        return prefs.getString(ConfigURL.ACESSOMS.toString());
      case ADMCORE:
        return prefs.getString(ConfigURL.ADMCORE.toString()) ?? '';
      case CONTATOMSURL:
        return prefs.getString(ConfigURL.CONTATOMSURL.toString()) ?? '';
      default:
        return null;
    }
  }
}
