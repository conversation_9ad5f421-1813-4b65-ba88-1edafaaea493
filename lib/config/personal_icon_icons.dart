/// Flutter icons PersonalIcon
/// Copyright (C) 2020 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  PersonalIcon
///      fonts:
///       - asset: fonts/PersonalIcon.ttf
///
///
///
import 'package:flutter/widgets.dart';

class PersonalIcon {
  PersonalIcon._();

  static const _kFontFam = 'PersonalIcon';

  static const IconData activity = IconData(0xe800, fontFamily: _kFontFam);
  static const IconData afternoon = IconData(0xe801, fontFamily: _kFontFam);
  static const IconData airplay = IconData(0xe802, fontFamily: _kFontFam);
  static const IconData alert_circle = IconData(0xe803, fontFamily: _kFontFam);
  static const IconData alert_octagon = IconData(0xe804, fontFamily: _kFontFam);
  static const IconData alert_triangle = IconData(0xe805, fontFamily: _kFontFam);
  static const IconData align_center = IconData(0xe806, fontFamily: _kFontFam);
  static const IconData align_justify = IconData(0xe807, fontFamily: _kFontFam);
  static const IconData align_left = IconData(0xe808, fontFamily: _kFontFam);
  static const IconData align_right = IconData(0xe809, fontFamily: _kFontFam);
  static const IconData anchor = IconData(0xe80a, fontFamily: _kFontFam);
  static const IconData aperture = IconData(0xe80b, fontFamily: _kFontFam);
  static const IconData archive = IconData(0xe80c, fontFamily: _kFontFam);
  static const IconData arrow_down_circle = IconData(0xe80d, fontFamily: _kFontFam);
  static const IconData arrow_down_left = IconData(0xe80e, fontFamily: _kFontFam);
  static const IconData arrow_down_right = IconData(0xe80f, fontFamily: _kFontFam);
  static const IconData arrow_down = IconData(0xe810, fontFamily: _kFontFam);
  static const IconData arrow_left_circle = IconData(0xe811, fontFamily: _kFontFam);
  static const IconData arrow_left = IconData(0xe812, fontFamily: _kFontFam);
  static const IconData arrow_right_circle = IconData(0xe813, fontFamily: _kFontFam);
  static const IconData arrow_right = IconData(0xe814, fontFamily: _kFontFam);
  static const IconData arrow_up_circle = IconData(0xe815, fontFamily: _kFontFam);
  static const IconData arrow_up_left = IconData(0xe816, fontFamily: _kFontFam);
  static const IconData arrow_up_right = IconData(0xe817, fontFamily: _kFontFam);
  static const IconData arrow_up = IconData(0xe818, fontFamily: _kFontFam);
  static const IconData at_sign = IconData(0xe819, fontFamily: _kFontFam);
  static const IconData aula_cheia = IconData(0xe81a, fontFamily: _kFontFam);
  static const IconData award = IconData(0xe81b, fontFamily: _kFontFam);
  static const IconData bar_chart_2 = IconData(0xe81c, fontFamily: _kFontFam);
  static const IconData bar_chart = IconData(0xe81d, fontFamily: _kFontFam);
  static const IconData battery_charging = IconData(0xe81e, fontFamily: _kFontFam);
  static const IconData battery = IconData(0xe81f, fontFamily: _kFontFam);
  static const IconData bell_off = IconData(0xe820, fontFamily: _kFontFam);
  static const IconData bell = IconData(0xe821, fontFamily: _kFontFam);
  static const IconData biset = IconData(0xe822, fontFamily: _kFontFam);
  static const IconData bluetooth = IconData(0xe823, fontFamily: _kFontFam);
  static const IconData bold = IconData(0xe824, fontFamily: _kFontFam);
  static const IconData book_open = IconData(0xe825, fontFamily: _kFontFam);
  static const IconData book = IconData(0xe826, fontFamily: _kFontFam);
  static const IconData bookmark = IconData(0xe827, fontFamily: _kFontFam);
  static const IconData box = IconData(0xe828, fontFamily: _kFontFam);
  static const IconData briefcase = IconData(0xe829, fontFamily: _kFontFam);
  static const IconData calendar = IconData(0xe82a, fontFamily: _kFontFam);
  static const IconData camera_off = IconData(0xe82b, fontFamily: _kFontFam);
  static const IconData camera = IconData(0xe82c, fontFamily: _kFontFam);
  static const IconData caret_down = IconData(0xe82d, fontFamily: _kFontFam);
  static const IconData caret_left = IconData(0xe82e, fontFamily: _kFontFam);
  static const IconData caret_right = IconData(0xe82f, fontFamily: _kFontFam);
  static const IconData caret_up = IconData(0xe830, fontFamily: _kFontFam);
  static const IconData cast = IconData(0xe831, fontFamily: _kFontFam);
  static const IconData check_circle = IconData(0xe832, fontFamily: _kFontFam);
  static const IconData check_square = IconData(0xe833, fontFamily: _kFontFam);
  static const IconData check = IconData(0xe834, fontFamily: _kFontFam);
  static const IconData chevron_down = IconData(0xe835, fontFamily: _kFontFam);
  static const IconData chevron_left = IconData(0xe836, fontFamily: _kFontFam);
  static const IconData chevron_right = IconData(0xe837, fontFamily: _kFontFam);
  static const IconData chevron_up = IconData(0xe838, fontFamily: _kFontFam);
  static const IconData chevrons_down = IconData(0xe839, fontFamily: _kFontFam);
  static const IconData chevrons_left = IconData(0xe83a, fontFamily: _kFontFam);
  static const IconData chevrons_right = IconData(0xe83b, fontFamily: _kFontFam);
  static const IconData chevrons_up = IconData(0xe83c, fontFamily: _kFontFam);
  static const IconData chrome = IconData(0xe83d, fontFamily: _kFontFam);
  static const IconData circle_filled = IconData(0xe83e, fontFamily: _kFontFam);
  static const IconData circle = IconData(0xe83f, fontFamily: _kFontFam);
  static const IconData circularcontroller = IconData(0xe840, fontFamily: _kFontFam);
  static const IconData clipboard = IconData(0xe841, fontFamily: _kFontFam);
  static const IconData clock = IconData(0xe842, fontFamily: _kFontFam);
  static const IconData cloud_drizzle = IconData(0xe843, fontFamily: _kFontFam);
  static const IconData cloud_lightning = IconData(0xe844, fontFamily: _kFontFam);
  static const IconData cloud_off = IconData(0xe845, fontFamily: _kFontFam);
  static const IconData cloud_rain = IconData(0xe846, fontFamily: _kFontFam);
  static const IconData cloud_snow = IconData(0xe847, fontFamily: _kFontFam);
  static const IconData cloud = IconData(0xe848, fontFamily: _kFontFam);
  static const IconData code = IconData(0xe849, fontFamily: _kFontFam);
  static const IconData codepen = IconData(0xe84a, fontFamily: _kFontFam);
  static const IconData codesandbox = IconData(0xe84b, fontFamily: _kFontFam);
  static const IconData coffee = IconData(0xe84c, fontFamily: _kFontFam);
  static const IconData columns = IconData(0xe84d, fontFamily: _kFontFam);
  static const IconData command = IconData(0xe84e, fontFamily: _kFontFam);
  static const IconData compass = IconData(0xe84f, fontFamily: _kFontFam);
  static const IconData copy = IconData(0xe850, fontFamily: _kFontFam);
  static const IconData corner_down_left = IconData(0xe851, fontFamily: _kFontFam);
  static const IconData corner_down_right = IconData(0xe852, fontFamily: _kFontFam);
  static const IconData corner_left_down = IconData(0xe853, fontFamily: _kFontFam);
  static const IconData corner_left_up = IconData(0xe854, fontFamily: _kFontFam);
  static const IconData corner_right_down = IconData(0xe855, fontFamily: _kFontFam);
  static const IconData corner_right_up = IconData(0xe856, fontFamily: _kFontFam);
  static const IconData corner_up_left = IconData(0xe857, fontFamily: _kFontFam);
  static const IconData corner_up_right = IconData(0xe858, fontFamily: _kFontFam);
  static const IconData cpu = IconData(0xe859, fontFamily: _kFontFam);
  static const IconData credit_card = IconData(0xe85a, fontFamily: _kFontFam);
  static const IconData crop = IconData(0xe85b, fontFamily: _kFontFam);
  static const IconData cross = IconData(0xe85c, fontFamily: _kFontFam);
  static const IconData crosshair = IconData(0xe85d, fontFamily: _kFontFam);
  static const IconData database = IconData(0xe85e, fontFamily: _kFontFam);
  static const IconData delete = IconData(0xe85f, fontFamily: _kFontFam);
  static const IconData digital = IconData(0xe860, fontFamily: _kFontFam);
  static const IconData disc = IconData(0xe861, fontFamily: _kFontFam);
  static const IconData dollar_sign = IconData(0xe862, fontFamily: _kFontFam);
  static const IconData download_cloud = IconData(0xe863, fontFamily: _kFontFam);
  static const IconData download = IconData(0xe864, fontFamily: _kFontFam);
  static const IconData drag = IconData(0xe865, fontFamily: _kFontFam);
  static const IconData drop_down = IconData(0xe866, fontFamily: _kFontFam);
  static const IconData droplet = IconData(0xe867, fontFamily: _kFontFam);
  static const IconData edit_2 = IconData(0xe868, fontFamily: _kFontFam);
  static const IconData edit_3 = IconData(0xe869, fontFamily: _kFontFam);
  static const IconData edit = IconData(0xe86a, fontFamily: _kFontFam);
  static const IconData evaluation = IconData(0xe86b, fontFamily: _kFontFam);
  static const IconData external_link = IconData(0xe86c, fontFamily: _kFontFam);
  static const IconData eye_off = IconData(0xe86d, fontFamily: _kFontFam);
  static const IconData eye = IconData(0xe86e, fontFamily: _kFontFam);
  static const IconData facebook = IconData(0xe86f, fontFamily: _kFontFam);
  static const IconData fast_forward = IconData(0xe870, fontFamily: _kFontFam);
  static const IconData feather = IconData(0xe871, fontFamily: _kFontFam);
  static const IconData figma = IconData(0xe872, fontFamily: _kFontFam);
  static const IconData file_minus = IconData(0xe873, fontFamily: _kFontFam);
  static const IconData file_plus = IconData(0xe874, fontFamily: _kFontFam);
  static const IconData file_text = IconData(0xe875, fontFamily: _kFontFam);
  static const IconData file = IconData(0xe876, fontFamily: _kFontFam);
  static const IconData film = IconData(0xe877, fontFamily: _kFontFam);
  static const IconData filter = IconData(0xe878, fontFamily: _kFontFam);
  static const IconData flag = IconData(0xe879, fontFamily: _kFontFam);
  static const IconData folder_minus = IconData(0xe87a, fontFamily: _kFontFam);
  static const IconData folder_plus = IconData(0xe87b, fontFamily: _kFontFam);
  static const IconData folder = IconData(0xe87c, fontFamily: _kFontFam);
  static const IconData footprint = IconData(0xe87d, fontFamily: _kFontFam);
  static const IconData frown = IconData(0xe87e, fontFamily: _kFontFam);
  static const IconData gdrive = IconData(0xe87f, fontFamily: _kFontFam);
  static const IconData gift = IconData(0xe880, fontFamily: _kFontFam);
  static const IconData git_branch = IconData(0xe881, fontFamily: _kFontFam);
  static const IconData git_commit = IconData(0xe882, fontFamily: _kFontFam);
  static const IconData git_merge = IconData(0xe883, fontFamily: _kFontFam);
  static const IconData git_pull_request = IconData(0xe884, fontFamily: _kFontFam);
  static const IconData github = IconData(0xe885, fontFamily: _kFontFam);
  static const IconData gitlab = IconData(0xe886, fontFamily: _kFontFam);
  static const IconData globe = IconData(0xe887, fontFamily: _kFontFam);
  static const IconData google = IconData(0xe888, fontFamily: _kFontFam);
  static const IconData grid = IconData(0xe889, fontFamily: _kFontFam);
  static const IconData gsheets = IconData(0xe88a, fontFamily: _kFontFam);
  static const IconData happy_01 = IconData(0xe88b, fontFamily: _kFontFam);
  static const IconData happy_02 = IconData(0xe88c, fontFamily: _kFontFam);
  static const IconData happy_03 = IconData(0xe88d, fontFamily: _kFontFam);
  static const IconData hard_drive = IconData(0xe88e, fontFamily: _kFontFam);
  static const IconData hash = IconData(0xe88f, fontFamily: _kFontFam);
  static const IconData headphones = IconData(0xe890, fontFamily: _kFontFam);
  static const IconData heart = IconData(0xe891, fontFamily: _kFontFam);
  static const IconData height = IconData(0xe892, fontFamily: _kFontFam);
  static const IconData help_circle = IconData(0xe893, fontFamily: _kFontFam);
  static const IconData hexagon = IconData(0xe894, fontFamily: _kFontFam);
  static const IconData home = IconData(0xe895, fontFamily: _kFontFam);
  static const IconData image = IconData(0xe896, fontFamily: _kFontFam);
  static const IconData inbox = IconData(0xe897, fontFamily: _kFontFam);
  static const IconData info = IconData(0xe898, fontFamily: _kFontFam);
  static const IconData ingredients = IconData(0xe899, fontFamily: _kFontFam);
  static const IconData insight = IconData(0xe89a, fontFamily: _kFontFam);
  static const IconData instagram = IconData(0xe89b, fontFamily: _kFontFam);
  static const IconData italic = IconData(0xe89c, fontFamily: _kFontFam);
  static const IconData key = IconData(0xe89d, fontFamily: _kFontFam);
  static const IconData keyboard = IconData(0xe89e, fontFamily: _kFontFam);
  static const IconData layers = IconData(0xe89f, fontFamily: _kFontFam);
  static const IconData layout = IconData(0xe8a0, fontFamily: _kFontFam);
  static const IconData level = IconData(0xe8a1, fontFamily: _kFontFam);
  static const IconData life_buoy = IconData(0xe8a2, fontFamily: _kFontFam);
  static const IconData link_2 = IconData(0xe8a3, fontFamily: _kFontFam);
  static const IconData link = IconData(0xe8a4, fontFamily: _kFontFam);
  static const IconData linkedin = IconData(0xe8a5, fontFamily: _kFontFam);
  static const IconData list = IconData(0xe8a6, fontFamily: _kFontFam);
  static const IconData loader = IconData(0xe8a7, fontFamily: _kFontFam);
  static const IconData lock = IconData(0xe8a8, fontFamily: _kFontFam);
  static const IconData log_in = IconData(0xe8a9, fontFamily: _kFontFam);
  static const IconData log_out = IconData(0xe8aa, fontFamily: _kFontFam);
  static const IconData logo_pacto = IconData(0xe8ab, fontFamily: _kFontFam);
  static const IconData mail = IconData(0xe8ac, fontFamily: _kFontFam);
  static const IconData map_pin = IconData(0xe8ad, fontFamily: _kFontFam);
  static const IconData map = IconData(0xe8ae, fontFamily: _kFontFam);
  static const IconData maximize_2 = IconData(0xe8af, fontFamily: _kFontFam);
  static const IconData maximize = IconData(0xe8b0, fontFamily: _kFontFam);
  static const IconData meh = IconData(0xe8b1, fontFamily: _kFontFam);
  static const IconData menu = IconData(0xe8b2, fontFamily: _kFontFam);
  static const IconData message_circle = IconData(0xe8b3, fontFamily: _kFontFam);
  static const IconData message_square = IconData(0xe8b4, fontFamily: _kFontFam);
  static const IconData mic_off = IconData(0xe8b5, fontFamily: _kFontFam);
  static const IconData mic = IconData(0xe8b6, fontFamily: _kFontFam);
  static const IconData minimize_2 = IconData(0xe8b7, fontFamily: _kFontFam);
  static const IconData minimize = IconData(0xe8b8, fontFamily: _kFontFam);
  static const IconData minus_circle = IconData(0xe8b9, fontFamily: _kFontFam);
  static const IconData minus_square = IconData(0xe8ba, fontFamily: _kFontFam);
  static const IconData minus = IconData(0xe8bb, fontFamily: _kFontFam);
  static const IconData modality = IconData(0xe8bc, fontFamily: _kFontFam);
  static const IconData monitor = IconData(0xe8bd, fontFamily: _kFontFam);
  static const IconData moon = IconData(0xe8be, fontFamily: _kFontFam);
  static const IconData more_horizontal = IconData(0xe8bf, fontFamily: _kFontFam);
  static const IconData more_vertical = IconData(0xe8c0, fontFamily: _kFontFam);
  static const IconData morning = IconData(0xe8c1, fontFamily: _kFontFam);
  static const IconData mouse_pointer = IconData(0xe8c2, fontFamily: _kFontFam);
  static const IconData move_2 = IconData(0xe8c3, fontFamily: _kFontFam);
  static const IconData move = IconData(0xe8c4, fontFamily: _kFontFam);
  static const IconData music = IconData(0xe8c5, fontFamily: _kFontFam);
  static const IconData navigation_2 = IconData(0xe8c6, fontFamily: _kFontFam);
  static const IconData navigation = IconData(0xe8c7, fontFamily: _kFontFam);
  static const IconData nutrition = IconData(0xe8c8, fontFamily: _kFontFam);
  static const IconData octagon = IconData(0xe8c9, fontFamily: _kFontFam);
  static const IconData package = IconData(0xe8ca, fontFamily: _kFontFam);
  static const IconData paperclip = IconData(0xe8cb, fontFamily: _kFontFam);
  static const IconData pause_circle = IconData(0xe8cc, fontFamily: _kFontFam);
  static const IconData pause = IconData(0xe8cd, fontFamily: _kFontFam);
  static const IconData pen_tool = IconData(0xe8ce, fontFamily: _kFontFam);
  static const IconData percent = IconData(0xe8cf, fontFamily: _kFontFam);
  static const IconData phone_call = IconData(0xe8d0, fontFamily: _kFontFam);
  static const IconData phone_forwarded = IconData(0xe8d1, fontFamily: _kFontFam);
  static const IconData phone_incoming = IconData(0xe8d2, fontFamily: _kFontFam);
  static const IconData phone_off = IconData(0xe8d3, fontFamily: _kFontFam);
  static const IconData phone_outgoing = IconData(0xe8d4, fontFamily: _kFontFam);
  static const IconData phone = IconData(0xe8d5, fontFamily: _kFontFam);
  static const IconData pie_chart = IconData(0xe8d6, fontFamily: _kFontFam);
  static const IconData pin = IconData(0xe8d7, fontFamily: _kFontFam);
  static const IconData play_1 = IconData(0xe8d8, fontFamily: _kFontFam);
  static const IconData play_circle = IconData(0xe8d9, fontFamily: _kFontFam);
  static const IconData play_filled = IconData(0xe8da, fontFamily: _kFontFam);
  static const IconData play = IconData(0xe8db, fontFamily: _kFontFam);
  static const IconData plus_circle = IconData(0xe8dc, fontFamily: _kFontFam);
  static const IconData plus_square = IconData(0xe8dd, fontFamily: _kFontFam);
  static const IconData plus = IconData(0xe8de, fontFamily: _kFontFam);
  static const IconData pocket = IconData(0xe8df, fontFamily: _kFontFam);
  static const IconData power = IconData(0xe8e0, fontFamily: _kFontFam);
  static const IconData printer = IconData(0xe8e1, fontFamily: _kFontFam);
  static const IconData radio = IconData(0xe8e2, fontFamily: _kFontFam);
  static const IconData refresh_ccw = IconData(0xe8e3, fontFamily: _kFontFam);
  static const IconData refresh_cw = IconData(0xe8e4, fontFamily: _kFontFam);
  static const IconData repeat = IconData(0xe8e5, fontFamily: _kFontFam);
  static const IconData rewind = IconData(0xe8e6, fontFamily: _kFontFam);
  static const IconData rotate_ccw = IconData(0xe8e7, fontFamily: _kFontFam);
  static const IconData rotate_cw = IconData(0xe8e8, fontFamily: _kFontFam);
  static const IconData rss = IconData(0xe8e9, fontFamily: _kFontFam);
  static const IconData save = IconData(0xe8ea, fontFamily: _kFontFam);
  static const IconData scissors = IconData(0xe8eb, fontFamily: _kFontFam);
  static const IconData search = IconData(0xe8ec, fontFamily: _kFontFam);
  static const IconData send = IconData(0xe8ed, fontFamily: _kFontFam);
  static const IconData server = IconData(0xe8ee, fontFamily: _kFontFam);
  static const IconData settings = IconData(0xe8ef, fontFamily: _kFontFam);
  static const IconData share_2 = IconData(0xe8f0, fontFamily: _kFontFam);
  static const IconData share = IconData(0xe8f1, fontFamily: _kFontFam);
  static const IconData shield_off = IconData(0xe8f2, fontFamily: _kFontFam);
  static const IconData shield = IconData(0xe8f3, fontFamily: _kFontFam);
  static const IconData shopping_bag = IconData(0xe8f4, fontFamily: _kFontFam);
  static const IconData shopping_cart = IconData(0xe8f5, fontFamily: _kFontFam);
  static const IconData shuffle = IconData(0xe8f6, fontFamily: _kFontFam);
  static const IconData sidebar = IconData(0xe8f7, fontFamily: _kFontFam);
  static const IconData skill = IconData(0xe8f8, fontFamily: _kFontFam);
  static const IconData skip_back = IconData(0xe8f9, fontFamily: _kFontFam);
  static const IconData skip_forward = IconData(0xe8fa, fontFamily: _kFontFam);
  static const IconData slack = IconData(0xe8fb, fontFamily: _kFontFam);
  static const IconData slash = IconData(0xe8fc, fontFamily: _kFontFam);
  static const IconData sliders = IconData(0xe8fd, fontFamily: _kFontFam);
  static const IconData smartphone = IconData(0xe8fe, fontFamily: _kFontFam);
  static const IconData smile = IconData(0xe8ff, fontFamily: _kFontFam);
  static const IconData speaker = IconData(0xe900, fontFamily: _kFontFam);
  static const IconData square = IconData(0xe901, fontFamily: _kFontFam);
  static const IconData stack = IconData(0xe902, fontFamily: _kFontFam);
  static const IconData star = IconData(0xe903, fontFamily: _kFontFam);
  static const IconData stop_circle = IconData(0xe904, fontFamily: _kFontFam);
  static const IconData sun = IconData(0xe905, fontFamily: _kFontFam);
  static const IconData sunrise = IconData(0xe906, fontFamily: _kFontFam);
  static const IconData sunset = IconData(0xe907, fontFamily: _kFontFam);
  static const IconData tablet = IconData(0xe908, fontFamily: _kFontFam);
  static const IconData tag = IconData(0xe909, fontFamily: _kFontFam);
  static const IconData target = IconData(0xe90a, fontFamily: _kFontFam);
  static const IconData terminal = IconData(0xe90b, fontFamily: _kFontFam);
  static const IconData thermometer = IconData(0xe90c, fontFamily: _kFontFam);
  static const IconData thumbs_down = IconData(0xe90d, fontFamily: _kFontFam);
  static const IconData thumbs_up = IconData(0xe90e, fontFamily: _kFontFam);
  static const IconData toggle_left = IconData(0xe90f, fontFamily: _kFontFam);
  static const IconData toggle_right = IconData(0xe910, fontFamily: _kFontFam);
  static const IconData trash_2 = IconData(0xe911, fontFamily: _kFontFam);
  static const IconData trash = IconData(0xe912, fontFamily: _kFontFam);
  static const IconData tray = IconData(0xe913, fontFamily: _kFontFam);
  static const IconData treino = IconData(0xe914, fontFamily: _kFontFam);
  static const IconData trello = IconData(0xe915, fontFamily: _kFontFam);
  static const IconData trending_down = IconData(0xe916, fontFamily: _kFontFam);
  static const IconData trending_up = IconData(0xe917, fontFamily: _kFontFam);
  static const IconData triangle = IconData(0xe918, fontFamily: _kFontFam);
  static const IconData triset = IconData(0xe919, fontFamily: _kFontFam);
  static const IconData trophy_filled = IconData(0xe91a, fontFamily: _kFontFam);
  static const IconData trophy = IconData(0xe91b, fontFamily: _kFontFam);
  static const IconData truck = IconData(0xe91c, fontFamily: _kFontFam);
  static const IconData tv = IconData(0xe91d, fontFamily: _kFontFam);
  static const IconData twitter = IconData(0xe91e, fontFamily: _kFontFam);
  static const IconData type = IconData(0xe91f, fontFamily: _kFontFam);
  static const IconData umbrella = IconData(0xe920, fontFamily: _kFontFam);
  static const IconData underline = IconData(0xe921, fontFamily: _kFontFam);
  static const IconData unlock = IconData(0xe922, fontFamily: _kFontFam);
  static const IconData unpin = IconData(0xe923, fontFamily: _kFontFam);
  static const IconData upload_cloud = IconData(0xe924, fontFamily: _kFontFam);
  static const IconData upload = IconData(0xe925, fontFamily: _kFontFam);
  static const IconData user_check = IconData(0xe926, fontFamily: _kFontFam);
  static const IconData user_minus = IconData(0xe927, fontFamily: _kFontFam);
  static const IconData user_plus = IconData(0xe928, fontFamily: _kFontFam);
  static const IconData user_x = IconData(0xe929, fontFamily: _kFontFam);
  static const IconData user = IconData(0xe92a, fontFamily: _kFontFam);
  static const IconData users = IconData(0xe92b, fontFamily: _kFontFam);
  // static const IconData community = IconData(0xe92b, fontFamily: _kFontFam);
  static const IconData video_off = IconData(0xe92c, fontFamily: _kFontFam);
  static const IconData video = IconData(0xe92d, fontFamily: _kFontFam);
  static const IconData viewed = IconData(0xe92e, fontFamily: _kFontFam);
  static const IconData voicemail = IconData(0xe92f, fontFamily: _kFontFam);
  static const IconData volume_1 = IconData(0xe930, fontFamily: _kFontFam);
  static const IconData volume_2 = IconData(0xe931, fontFamily: _kFontFam);
  static const IconData volume_x = IconData(0xe932, fontFamily: _kFontFam);
  static const IconData volume = IconData(0xe933, fontFamily: _kFontFam);
  static const IconData watch = IconData(0xe934, fontFamily: _kFontFam);
  static const IconData weight = IconData(0xe935, fontFamily: _kFontFam);
  static const IconData whatsapp = IconData(0xe936, fontFamily: _kFontFam);
  static const IconData wifi_off = IconData(0xe937, fontFamily: _kFontFam);
  static const IconData wifi = IconData(0xe938, fontFamily: _kFontFam);
  static const IconData wind = IconData(0xe939, fontFamily: _kFontFam);
  static const IconData wod = IconData(0xe93a, fontFamily: _kFontFam);
  static const IconData x_circle = IconData(0xe93b, fontFamily: _kFontFam);
  static const IconData x_octagon = IconData(0xe93c, fontFamily: _kFontFam);
  static const IconData x_square = IconData(0xe93d, fontFamily: _kFontFam);
  static const IconData x = IconData(0xe93e, fontFamily: _kFontFam);
  static const IconData youtube = IconData(0xe93f, fontFamily: _kFontFam);
  static const IconData zap_off = IconData(0xe940, fontFamily: _kFontFam);
  static const IconData zap = IconData(0xe941, fontFamily: _kFontFam);
  static const IconData zoom_in = IconData(0xe942, fontFamily: _kFontFam);
  static const IconData zoom_out = IconData(0xe943, fontFamily: _kFontFam);
}
