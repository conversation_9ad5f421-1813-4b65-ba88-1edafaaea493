
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/model/util/UtilColor.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';

class IconsTabbar {
  var corPrimaria = Theme.of(GetIt.I.get<NavigationService>().context).primaryColor.toHex(leadingHashSign: true).replaceRange(0, 3, '#').toUpperCase();
  var corIconeDesabilitado = GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? '#838383' : '#B3B3B3';
  SvgPicture icone({required IconeTabbar icone}) {
    switch (icone) {      
      case IconeTabbar.inicio:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.74 9.03C20.57 8.64 20.31 8.3 20 8.01L14 2.76C13.45 2.27 12.74 2 12 2C11.26 2 10.55 2.27 10 2.76L4 8.02C3.68 8.3 3.43 8.65 3.26 9.04C3.09 9.43 3 9.85 3 10.27V19C3 19.8 3.32 20.56 3.88 21.12C4.44 21.68 5.21 22 6 22H9C9.55 22 10 21.55 10 21V15.01C10 14.75 10.11 14.49 10.29 14.3C10.48 14.11 10.73 14.01 11 14.01H13C13.27 14.01 13.52 14.12 13.71 14.3C13.9 14.49 14 14.74 14 15.01V21C14 21.55 14.45 22 15 22H18C18.8 22 19.56 21.68 20.12 21.12C20.68 20.56 21 19.8 21 19V10.26C21 9.84 20.91 9.42 20.74 9.03ZM19 19C19 19.26 18.89 19.52 18.71 19.71C18.52 19.9 18.27 20 18 20H16.43C16.19 20 16 19.81 16 19.57V15C16 14.21 15.68 13.44 15.12 12.88C14.56 12.32 13.79 12 13 12H11C10.2 12 9.44 12.32 8.88 12.88C8.32 13.44 8 14.2 8 15V19.57C8 19.81 7.81 20 7.57 20H6C5.73 20 5.48 19.89 5.29 19.71C5.1 19.52 5 19.27 5 19V10.26C5 10.12 5.03 9.98 5.09 9.85C5.15 9.72 5.23 9.61 5.34 9.51L11.34 4.27C11.52 4.11 11.76 4.02 12 4.02C12.24 4.02 12.48 4.11 12.66 4.27L18.66 9.51C18.77 9.6 18.85 9.72 18.91 9.85C18.97 9.98 19 10.12 19 10.26V19Z" fill="${corIconeDesabilitado}"/></svg>');
      case IconeTabbar.inicio_ativo:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.74 9.03C20.57 8.64 20.31 8.3 20 8.01L14 2.76C13.45 2.27 12.74 2 12 2C11.26 2 10.55 2.27 10 2.76L4 8.02C3.68 8.3 3.43 8.65 3.26 9.04C3.09 9.43 3 9.85 3 10.27V19C3 19.8 3.32 20.56 3.88 21.12C4.44 21.68 5.21 22 6 22H9C9.55 22 10 21.55 10 21V20V18.48V15.01C10 14.75 10.11 14.49 10.29 14.3C10.48 14.11 10.73 14.01 11 14.01H13C13.27 14.01 13.52 14.12 13.71 14.3C13.9 14.49 14 14.74 14 15.01V21C14 21.55 14.45 22 15 22H18C18.8 22 19.56 21.68 20.12 21.12C20.68 20.56 21 19.8 21 19V10.26C21 9.84 20.91 9.42 20.74 9.03Z" fill="${corPrimaria}"/></svg>');
      case IconeTabbar.aulas:
       return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.3289 7.53287C12.3337 7.53287 12.3377 7.53287 12.3425 7.53287C13.8323 7.52574 15.0444 6.31546 15.0436 4.83589C15.0436 3.35156 13.8307 2.1397 12.3417 2.13574L12.3337 2.13574C11.6108 2.13574 10.9301 2.41613 10.4179 2.92622C9.8992 3.44186 9.61593 4.12778 9.61992 4.85727C9.62789 6.33606 10.8416 7.53366 12.3297 7.53366L12.3289 7.53287ZM11.6355 4.84935C11.6315 4.66797 11.6994 4.49689 11.827 4.36699C11.9579 4.23313 12.1358 4.15867 12.3274 4.15788L12.3313 4.15788C12.5213 4.15788 12.6984 4.22996 12.8301 4.36144C12.9593 4.48976 13.0287 4.66084 13.0272 4.84223C13.0256 5.02678 12.9506 5.19945 12.8173 5.32855C12.6832 5.45845 12.5061 5.52578 12.3194 5.52499C11.9507 5.51865 11.6443 5.21529 11.6355 4.84935Z" fill="${corIconeDesabilitado}"/><path d="M18.5347 11.29C18.5011 11.2678 15.1801 9.08725 13.9672 8.20568C13.7901 8.07737 13.577 8.00767 13.3576 8.00688C12.6051 8.0045 12.1192 8.00212 11.6324 7.99975C11.2111 7.99737 10.7898 7.99579 10.1945 7.99341L6.93089 2.50599C6.63964 2.01649 6.00368 1.85412 5.50975 2.14322C5.01662 2.43233 4.85304 3.0636 5.14429 3.5531L8.58185 9.332L8.58185 21.9702C8.58185 22.5389 9.04625 22.9999 9.61918 22.9999C10.1921 22.9999 10.6565 22.5389 10.6565 21.9702V16.5873L13.1118 15.777L13.956 16.5667L11.6452 17.5362C11.1177 17.7572 10.8704 18.3615 11.093 18.8859C11.2606 19.2787 11.6444 19.5148 12.0489 19.5148C12.1838 19.5148 12.3202 19.4886 12.4519 19.4332L16.2286 17.8491C16.5518 17.7136 16.784 17.4253 16.8462 17.0831C16.9084 16.741 16.7919 16.3901 16.5366 16.1517L14.2313 13.9957C14.2217 13.987 14.2122 13.979 14.2018 13.9711V10.8829C15.6134 11.8413 17.2915 12.943 17.3896 13.008C17.5659 13.1236 17.7646 13.1791 17.9609 13.1791C18.2977 13.1791 18.628 13.0167 18.8275 12.7173C19.1435 12.2428 19.0126 11.6036 18.5347 11.29ZM10.6565 14.4178L10.6565 10.0552C11.0212 10.0567 11.322 10.0583 11.6228 10.0599C11.788 10.0607 11.9532 10.0615 12.1279 10.0623L12.1279 13.9331L10.6565 14.4178Z" fill="${corIconeDesabilitado}"/></svg>');
      case IconeTabbar.aulas_ativo:
       return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.5349 11.29C18.5014 11.2679 15.1802 9.0873 13.9673 8.20573C13.7902 8.07741 13.5771 8.00771 13.3577 8.00692C12.6052 8.00454 12.1193 8.00216 11.6325 7.99979C11.2112 7.99741 10.7899 7.99583 10.1946 7.99345L6.93092 2.50599C6.63967 2.01649 6.00369 1.85412 5.50976 2.14322C5.01662 2.43233 4.85303 3.06361 5.14429 3.55311L8.5819 9.33205L8.5819 21.9703C8.5819 22.539 9.04632 23 9.61925 23C10.1922 23 10.6566 22.539 10.6566 21.9703V16.5874L13.1119 15.7771L13.9562 16.5668L11.6453 17.5363C11.1178 17.7573 10.8705 18.3616 11.0931 18.886C11.2607 19.2789 11.6445 19.5149 12.049 19.5149C12.1839 19.5149 12.3204 19.4888 12.452 19.4333L16.2288 17.8492C16.5519 17.7137 16.7841 17.4254 16.8464 17.0832C16.9086 16.7411 16.7921 16.3902 16.5368 16.1518L14.2315 13.9958C14.2219 13.987 14.2123 13.9791 14.2019 13.9712V10.8829C15.6135 11.8413 17.2916 12.9431 17.3898 13.008C17.5661 13.1237 17.7648 13.1791 17.9611 13.1791C18.2979 13.1791 18.6282 13.0168 18.8277 12.7174C19.1437 12.2429 19.0128 11.6037 18.5349 11.29Z" fill="${corPrimaria}"/><path d="M12.3289 7.53277C12.3337 7.53277 12.3377 7.53277 12.3425 7.53277C13.8323 7.52564 15.0444 6.31536 15.0436 4.83577C15.0436 3.35144 13.8307 2.13957 12.3417 2.13561L12.3337 2.13561C11.6108 2.13561 10.9301 2.416 10.4178 2.9261C9.89914 3.44173 9.61586 4.12766 9.61985 4.85716C9.62783 6.33595 10.8415 7.53356 12.3297 7.53356L12.3289 7.53277Z" fill="${corPrimaria}"/></svg>');
      case IconeTabbar.listaAlunos:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_2172_5304)"><path d="M12.49 11.02C15.09 11.27 17 13.6 17 16.21V21C17 21.55 16.55 22 16 22C15.45 22 15 21.55 15 21V16C15 14.4 13.75 13.1 12.18 13.01H12C10.4 13.01 9.1 14.25 9.01 15.83V16.01V21.01C9.01 21.56 8.56 22.01 8.01 22.01C7.46 22.01 7.01 21.56 7.01 21.01V16.01C7.01 13.09 9.52 10.75 12.5 11.03L12.49 11.02ZM5.03 14.03C5.48 13.97 5.91 14 6.31 14.09C6.14 14.59 6.04 15.12 6.01 15.67V16V16.09C5.9 16.05 5.78 16.02 5.65 16.01H5.51C4.73 16.01 4.09 16.6 4.02 17.36V17.5V21C4.02 21.55 3.57 22 3.02 22C2.45 22 2 21.55 2 21V17.65C2 15.87 3.26 14.26 5.03 14.03ZM18.55 14C20.49 14.03 22 15.74 22 17.69V21C22 21.55 21.55 22 21 22C20.45 22 20 21.55 20 21V17.5C20 16.72 19.41 16.08 18.64 16.01H18.5C18.32 16.01 18.16 16.03 18 16.09V16C18 15.33 17.89 14.69 17.69 14.1C17.96 14.03 18.25 14 18.55 14ZM5.5 8C6.88 8 8 9.12 8 10.5C8 11.88 6.88 13 5.5 13C4.12 13 3 11.88 3 10.5C3 9.12 4.12 8 5.5 8ZM18.5 8C19.88 8 21 9.12 21 10.5C21 11.88 19.88 13 18.5 13C17.12 13 16 11.88 16 10.5C16 9.12 17.12 8 18.5 8ZM5.5 10C5.22 10 5 10.22 5 10.5C5 10.78 5.22 11 5.5 11C5.78 11 6 10.78 6 10.5C6 10.22 5.78 10 5.5 10ZM18.5 10C18.22 10 18 10.22 18 10.5C18 10.78 18.22 11 18.5 11C18.78 11 19 10.78 19 10.5C19 10.22 18.78 10 18.5 10ZM12 2C14.21 2 16 3.79 16 6C16 8.21 14.21 10 12 10C9.79 10 8 8.21 8 6C8 3.79 9.79 2 12 2ZM12 4C10.9 4 10 4.9 10 6C10 7.1 10.9 8 12 8C13.1 8 14 7.1 14 6C14 4.9 13.1 4 12 4Z" fill="${corIconeDesabilitado}"/></g><defs><clipPath id="clip0_2172_5304"><rect width="20" height="20" fill="white" transform="translate(2 2)"/></clipPath></defs></svg>');
      case IconeTabbar.listaAlunos_ativo:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_2172_5302)"><path d="M12 10C14.21 10 16 8.21 16 6C16 3.79 14.21 2 12 2C9.79 2 8 3.79 8 6C8 8.21 9.79 10 12 10ZM5.5 13C6.88 13 8 11.88 8 10.5C8 9.12 6.88 8 5.5 8C4.12 8 3 9.12 3 10.5C3 11.88 4.12 13 5.5 13ZM21 10.5C21 11.88 19.88 13 18.5 13C17.12 13 16 11.88 16 10.5C16 9.12 17.12 8 18.5 8C19.88 8 21 9.12 21 10.5ZM12.49 11.02C15.09 11.27 17 13.6 17 16.21V20.99C17 21.54 16.55 21.99 16 21.99H8C7.45 21.99 7 21.54 7 20.99V15.99C7 13.07 9.51 10.73 12.49 11.01V11.02ZM5 16C5 15.31 5.1 14.64 5.29 14.01H5.12C3.37 14.21 2 15.7 2 17.5V21C2 21.55 2.45 22 3 22H5V16ZM21 22C21.55 22 22 21.55 22 21V17.5C22 15.64 20.55 14.12 18.71 14.01C18.9 14.64 19 15.31 19 16V22H21Z" fill="${corPrimaria}"/></g><defs><clipPath id="clip0_2172_5302"><rect width="20" height="20" fill="white" transform="translate(2 2)"/></clipPath></defs></svg>');
      case IconeTabbar.treino:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.12 12.41H17.88C16.84 12.41 16 13.25 16 14.29V14.99H8V14.29C8 13.25 7.16 12.41 6.12 12.41H3.88C2.84 12.41 2 13.25 2 14.29V19.88C2 20.92 2.84 21.76 3.88 21.76H6.12C7.12 21.76 7.93 20.97 7.99 19.99H16.01C16.07 20.98 16.88 21.76 17.88 21.76H20.12C21.16 21.76 22 20.92 22 19.88V14.29C22 13.25 21.16 12.41 20.12 12.41ZM4 14.41H6V19.76H4V14.41ZM8 16.99H16V17.99H8V16.99ZM20 19.76H18V14.41H20V19.76Z" fill="${corIconeDesabilitado}"/><path d="M20.12 2H17.88C16.84 2 16 2.84 16 3.88V4.58H8V3.88C8 2.84 7.16 2 6.12 2H3.88C2.84 2 2 2.84 2 3.88V9.47C2 10.51 2.84 11.35 3.88 11.35H6.12C7.12 11.35 7.93 10.56 7.99 9.58H16.01C16.07 10.57 16.88 11.35 17.88 11.35H20.12C21.16 11.35 22 10.51 22 9.47V3.88C22 2.84 21.16 2 20.12 2ZM4 4H6V9.35H4V4ZM8 6.58H16V7.58H8V6.58ZM20 9.35H18V4H20V9.35Z" fill="${corIconeDesabilitado}"/></svg>');
      case IconeTabbar.treino_ativo:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.12 21.76H3.88C2.84 21.76 2 20.92 2 19.88V14.29C2 13.25 2.84 12.41 3.88 12.41H6.12C7.16 12.41 8 13.25 8 14.29V19.88C8 20.92 7.16 21.76 6.12 21.76Z" fill="${corPrimaria}"/><path d="M20.12 21.76H17.88C16.84 21.76 16 20.92 16 19.88V14.29C16 13.25 16.84 12.41 17.88 12.41H20.12C21.16 12.41 22 13.25 22 14.29V19.88C22 20.92 21.16 21.76 20.12 21.76Z" fill="${corPrimaria}"/><path d="M14.41 19.99H9.59C9.26 19.99 9 19.73 9 19.4V15.58C9 15.25 9.26 14.99 9.59 14.99H14.41C14.74 14.99 15 15.25 15 15.58V19.4C15 19.73 14.74 19.99 14.41 19.99Z" fill="${corPrimaria}"/><path d="M6.12 11.35H3.88C2.84 11.35 2 10.51 2 9.47V3.88C2 2.84 2.84 2 3.88 2H6.12C7.16 2 8 2.84 8 3.88V9.47C8 10.51 7.16 11.35 6.12 11.35Z" fill="${corPrimaria}"/><path d="M20.12 11.35H17.88C16.84 11.35 16 10.51 16 9.47V3.88C16 2.84 16.84 2 17.88 2H20.12C21.16 2 22 2.84 22 3.88V9.47C22 10.51 21.16 11.35 20.12 11.35Z" fill="${corPrimaria}"/><path d="M14.41 9.58H9.59C9.26 9.58 9 9.32 9 8.99V5.17C9 4.84 9.26 4.58 9.59 4.58H14.41C14.74 4.58 15 4.84 15 5.17V8.99C15 9.32 14.74 9.58 14.41 9.58Z" fill="${corPrimaria}"/></svg>');
      case IconeTabbar.cross:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.18 10.168C18.1317 10.112 18.1317 10.088 18.1317 10.048C18.1639 9.6 18.1881 9.152 18.2122 8.704C18.2525 7.992 18.2928 7.272 18.3572 6.56C18.478 5.12 18.0914 4 17.1895 3.12C16.4405 2.384 15.5144 2 14.4595 2C12.4865 2 10.5216 2 8.54855 2C6.57556 2 4.90857 3.448 4.66698 5.368C4.60256 5.856 4.63477 6.336 4.66698 6.808L4.68309 7.08C4.7153 7.68 4.74751 8.288 4.78778 8.888C4.81194 9.288 4.8361 9.696 4.86026 10.096C3.97442 11.2 3.3946 12.464 3.1369 13.856C3.09664 14.056 3.07248 14.264 3.04027 14.464L3 16.016L3.05637 16.416C3.09664 16.664 3.1369 16.92 3.19327 17.168C3.58787 18.952 4.48176 20.48 5.86689 21.712C5.8991 21.744 5.93131 21.776 5.96352 21.808L6.14069 22H16.8593L17.262 21.6C17.4874 21.384 17.7049 21.168 17.9143 20.928C19.0658 19.616 19.7504 18.096 19.9436 16.4C20.2094 14.088 19.6215 11.984 18.1881 10.16L18.18 10.168ZM15.901 19.696C14.7011 19.696 13.5012 19.696 12.3093 19.696H10.6182C9.4505 19.696 8.2828 19.696 7.09901 19.696C6.05211 18.608 5.48034 17.36 5.35149 15.88C5.08574 12.92 7.02653 10.208 9.95784 9.432C10.4571 9.296 10.9806 9.232 11.496 9.232C12.6315 9.232 13.7669 9.544 14.7575 10.152C16.2151 11.048 17.2056 12.432 17.5438 14.064C17.9867 16.192 17.4311 18.088 15.909 19.688L15.901 19.696ZM16.0621 6.216C16.0137 6.76 15.9815 7.304 15.9493 7.848L15.9332 8.176C12.9938 6.528 10.0142 6.528 7.07485 8.176L7.01847 7.272C6.99431 6.904 6.97821 6.536 6.946 6.168C6.87352 5.272 7.36476 4.56 8.20227 4.36C8.42776 4.304 8.74183 4.312 9.03174 4.312C9.53103 4.312 10.0223 4.312 10.5135 4.312C11.7698 4.312 13.018 4.32 14.2743 4.296C14.8058 4.28 15.297 4.488 15.6352 4.84C15.9654 5.192 16.1184 5.672 16.0621 6.216Z" fill="${corIconeDesabilitado}"/><path d="M11.5039 11.2803C9.35372 11.2803 7.61426 13.0163 7.61426 15.1443C7.61426 17.2723 9.36177 19.0083 11.5039 19.0083C13.646 19.0083 15.3935 17.2723 15.3935 15.1443C15.3935 13.0163 13.646 11.2803 11.5039 11.2803ZM13.0823 15.1443C13.0823 16.0083 12.3736 16.7123 11.5039 16.7123C10.6342 16.7123 9.92548 16.0083 9.92548 15.1443C9.92548 14.2803 10.6342 13.5763 11.5039 13.5763C12.3736 13.5763 13.0823 14.2803 13.0823 15.1443Z" fill="${corIconeDesabilitado}"/></svg>',);
      case IconeTabbar.cross_ativo:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M18.1317 10.048C18.1317 10.088 18.1317 10.112 18.18 10.168L18.1881 10.16C19.6215 11.984 20.2094 14.088 19.9436 16.4C19.7504 18.096 19.0658 19.616 17.9143 20.928C17.7049 21.168 17.4874 21.384 17.262 21.6L16.8593 22H6.14069L5.96352 21.808L5.86689 21.712C4.48176 20.48 3.58787 18.952 3.19327 17.168C3.14081 16.9372 3.10229 16.6994 3.06475 16.4677C3.06195 16.4504 3.05916 16.4332 3.05637 16.416L3 16.016L3.04027 14.464C3.05147 14.3944 3.06171 14.3239 3.07198 14.253C3.09124 14.1202 3.11065 13.9864 3.1369 13.856C3.3946 12.464 3.97442 11.2 4.86026 10.096C4.84818 9.896 4.8361 9.694 4.82402 9.492C4.81194 9.29 4.79986 9.088 4.78778 8.888C4.74945 8.31691 4.71842 7.73857 4.68775 7.16678C4.68619 7.13783 4.68464 7.10891 4.68309 7.08L4.66698 6.808C4.63477 6.336 4.60256 5.856 4.66698 5.368C4.90857 3.448 6.57556 2 8.54855 2H14.4595C15.5144 2 16.4405 2.384 17.1895 3.12C18.0914 4 18.478 5.12 18.3572 6.56C18.2939 7.25915 18.2539 7.96601 18.2144 8.66544L18.2122 8.704C18.1881 9.15189 18.1639 9.60011 18.1317 10.048ZM15.9493 7.848C15.9815 7.304 16.0137 6.76 16.0621 6.216C16.1184 5.672 15.9654 5.192 15.6352 4.84C15.297 4.488 14.8058 4.28 14.2743 4.296C13.2694 4.3152 12.2698 4.31392 11.267 4.31264C11.0161 4.31232 10.7649 4.312 10.5135 4.312H9.03174C8.99967 4.312 8.9673 4.3119 8.93479 4.3118C8.67343 4.31101 8.40282 4.31019 8.20227 4.36C7.36476 4.56 6.87352 5.272 6.946 6.168C6.96747 6.41333 6.98179 6.65866 6.9961 6.90399C7.00326 7.02666 7.01042 7.14933 7.01847 7.272L7.07485 8.176C10.0142 6.528 12.9938 6.528 15.9332 8.176L15.9493 7.848ZM7.6145 15.1443C7.6145 13.0163 9.35396 11.2803 11.5041 11.2803C13.6462 11.2803 15.3938 13.0163 15.3938 15.1443C15.3938 17.2723 13.6462 19.0083 11.5041 19.0083C9.36202 19.0083 7.6145 17.2723 7.6145 15.1443Z" fill="${corPrimaria}"/></svg>');
      case IconeTabbar.vitio:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1525_25471)"><path d="M19.6298 6L19.3398 7.72C19.1898 8.6 18.4398 9.23 17.5498 9.23H15.2198L15.5598 7.5C15.7098 6.63 16.4698 6 17.3498 6H19.6398M5.17983 6.04L5.90983 18.72H5.12983C4.90983 18.72 4.71983 18.54 4.70983 18.32L4.07983 6.04H5.16983M21.9998 4H17.3398C15.4898 4 13.9098 5.33 13.5798 7.15L11.5998 17.01C11.4198 18.04 10.5298 18.78 9.48983 18.78H7.89983L7.07983 4.51C7.06983 4.24 6.84983 4.03 6.57983 4.03H2.49983C2.21983 4.04 1.98983 4.28 1.99983 4.57L2.69983 18.42C2.76983 19.71 3.82983 20.72 5.11983 20.72H11.0298C12.2098 20.72 13.2098 19.87 13.4098 18.72L14.7198 11.22L17.5298 11.24C17.5298 11.24 17.5398 11.24 17.5498 11.24C19.4098 11.24 20.9998 9.9 21.3098 8.06L21.9998 4.01V4Z" fill="${corIconeDesabilitado}"/></g><defs><clipPath id="clip0_1525_25471"><rect width="20" height="16.71" fill="white" transform="translate(2 4)"/></clipPath></defs></svg>');
      case IconeTabbar.vitio_ativo:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1525_25443)"><path d="M9.49983 18.78H7.90983L7.07983 4.51001C7.06983 4.24001 6.84983 4.03001 6.57983 4.03001H2.49983C2.21983 4.04001 1.98983 4.28001 1.99983 4.57001L2.69983 18.42C2.76983 19.71 3.82983 20.72 5.11983 20.72H11.0298C12.2098 20.72 13.2098 19.87 13.4098 18.72L14.7198 11.22L17.5298 11.24C19.3998 11.24 20.9898 9.90001 21.3098 8.06001L21.9998 4.01001H17.3398C15.4898 4.01001 13.9098 5.34001 13.5798 7.16001L11.5998 17.02C11.4198 18.05 10.5298 18.79 9.48983 18.79L9.49983 18.78Z" fill="#21F88C"/></g><defs><clipPath id="clip0_1525_25443"><rect width="20" height="16.71" fill="white" transform="translate(2 4)"/></clipPath></defs></svg>',);
      case IconeTabbar.saude:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13.9998 11H12.9998V9.99997C12.9998 9.73475 12.8944 9.4804 12.7069 9.29286C12.5194 9.10533 12.265 8.99997 11.9998 8.99997C11.7346 8.99997 11.4802 9.10533 11.2927 9.29286C11.1051 9.4804 10.9998 9.73475 10.9998 9.99997V11H9.99979C9.73457 11 9.48022 11.1053 9.29268 11.2929C9.10514 11.4804 8.99979 11.7348 8.99979 12C8.99979 12.2652 9.10514 12.5195 9.29268 12.7071C9.48022 12.8946 9.73457 13 9.99979 13H10.9998V14C10.9998 14.2652 11.1051 14.5195 11.2927 14.7071C11.4802 14.8946 11.7346 15 11.9998 15C12.265 15 12.5194 14.8946 12.7069 14.7071C12.8944 14.5195 12.9998 14.2652 12.9998 14V13H13.9998C14.265 13 14.5194 12.8946 14.7069 12.7071C14.8944 12.5195 14.9998 12.2652 14.9998 12C14.9998 11.7348 14.8944 11.4804 14.7069 11.2929C14.5194 11.1053 14.265 11 13.9998 11ZM20.1598 4.99997C19.0935 3.94467 17.6851 3.30524 16.1888 3.19705C14.6925 3.08886 13.2068 3.51904 11.9998 4.40997C10.7274 3.46361 9.14378 3.03448 7.56771 3.20901C5.99164 3.38353 4.54023 4.14875 3.50576 5.35055C2.47129 6.55236 1.93061 8.10149 1.99259 9.68598C2.05457 11.2705 2.71461 12.7726 3.83979 13.89L9.83979 19.94C10.4023 20.5018 11.1648 20.8173 11.9598 20.8173C12.7548 20.8173 13.5173 20.5018 14.0798 19.94L20.0798 13.89C20.6707 13.3126 21.1417 12.6242 21.4657 11.8642C21.7897 11.1042 21.9604 10.2877 21.9678 9.4616C21.9752 8.63548 21.8193 7.81603 21.5091 7.05035C21.1988 6.28467 20.7402 5.58786 20.1598 4.99997ZM18.7498 12.46L12.7498 18.46C12.6568 18.5537 12.5462 18.6281 12.4244 18.6789C12.3025 18.7296 12.1718 18.7558 12.0398 18.7558C11.9078 18.7558 11.7771 18.7296 11.6552 18.6789C11.5334 18.6281 11.4228 18.5537 11.3298 18.46L5.32979 12.46C4.54555 11.6583 4.1064 10.5814 4.1064 9.45997C4.1064 8.3385 4.54555 7.26162 5.32979 6.45997C6.12894 5.67096 7.20676 5.22854 8.32979 5.22854C9.45281 5.22854 10.5306 5.67096 11.3298 6.45997C11.4228 6.5537 11.5334 6.62809 11.6552 6.67886C11.7771 6.72963 11.9078 6.75577 12.0398 6.75577C12.1718 6.75577 12.3025 6.72963 12.4244 6.67886C12.5462 6.62809 12.6568 6.5537 12.7498 6.45997C13.5489 5.67096 14.6268 5.22854 15.7498 5.22854C16.8728 5.22854 17.9506 5.67096 18.7498 6.45997C19.54 7.26252 19.983 8.34365 19.983 9.46997C19.983 10.5963 19.54 11.6774 18.7498 12.48V12.46Z" fill="${corIconeDesabilitado}"/></svg>');
      case IconeTabbar.saude_ativo:
        return SvgPicture.string('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"> <path d="M21.5244 6.88013C21.2144 6.11013 20.7544 5.42013 20.1744 4.83013C19.1144 3.77013 17.7044 3.13013 16.2044 3.03013C14.7044 2.93013 13.2244 3.35013 12.0144 4.24013C10.7444 3.29013 9.16444 2.86013 7.58444 3.04013C6.00444 3.21013 4.55444 3.98013 3.52444 5.18013C2.49444 6.38013 1.94444 7.93013 2.00444 9.52013C2.06444 11.1001 2.72444 12.6101 3.85444 13.7201L9.85444 19.7701C10.4144 20.3301 11.1844 20.6501 11.9744 20.6501C12.7644 20.6501 13.5344 20.3301 14.0944 19.7701L20.0944 13.7201C20.6844 13.1401 21.1544 12.4501 21.4844 11.6901C21.8044 10.9301 21.9744 10.1101 21.9844 9.29013C21.9944 8.47013 21.8344 7.64013 21.5244 6.88013ZM14.7244 12.5401C14.5344 12.7301 14.2844 12.8301 14.0144 12.8301H13.0144V13.8301C13.0144 14.1001 12.9044 14.3501 12.7244 14.5401C12.5344 14.7301 12.2844 14.8301 12.0144 14.8301C11.7444 14.8301 11.4944 14.7201 11.3044 14.5401C11.1144 14.3501 11.0144 14.1001 11.0144 13.8301V12.8301H10.0144C9.74444 12.8301 9.49444 12.7201 9.30444 12.5401C9.11444 12.3501 9.01444 12.1001 9.01444 11.8301C9.01444 11.5601 9.12444 11.3101 9.30444 11.1201C9.49444 10.9301 9.74444 10.8301 10.0144 10.8301H11.0144V9.83013C11.0144 9.56013 11.1244 9.31013 11.3044 9.12013C11.4944 8.93013 11.7444 8.83013 12.0144 8.83013C12.2844 8.83013 12.5344 8.94013 12.7244 9.12013C12.9144 9.31013 13.0144 9.56013 13.0144 9.83013V10.8301H14.0144C14.2844 10.8301 14.5344 10.9401 14.7244 11.1201C14.9144 11.3101 15.0144 11.5601 15.0144 11.8301C15.0144 12.1001 14.9044 12.3501 14.7244 12.5401Z" fill="${corPrimaria}"/> </svg>', );
      case IconeTabbar.agenda:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.29 2.29C7.48 2.11 7.73 2 8 2C8.27 2 8.52 2.1 8.71 2.29C8.89 2.48 9 2.73 9 3V4H15V3C15 2.73 15.1 2.48 15.29 2.29C15.48 2.11 15.73 2 16 2C16.27 2 16.52 2.1 16.71 2.29C16.89 2.48 17 2.73 17 3V4H19C19.79 4 20.56 4.32 21.12 4.88C21.68 5.44 22 6.2 22 7V10.25V12.25V19C22 19.79 21.68 20.56 21.12 21.12C20.56 21.68 19.8 22 19 22H5C4.21 22 3.44 21.68 2.88 21.12C2.32 20.56 2 19.8 2 19V12.25V10.25V7C2 6.21 2.32 5.44 2.88 4.88C3.44 4.32 4.2 4 5 4H7V3C7 2.73 7.1 2.48 7.29 2.29ZM4 10.25H20V7C20 6.73 19.9 6.48 19.71 6.29C19.52 6.11 19.26 6 19 6H17V7C17 7.27 16.9 7.52 16.71 7.71C16.52 7.89 16.27 8 16 8C15.73 8 15.48 7.9 15.29 7.71C15.11 7.52 15 7.27 15 7V6H9V7C9 7.27 8.9 7.52 8.71 7.71C8.52 7.89 8.27 8 8 8C7.73 8 7.48 7.9 7.29 7.71C7.11 7.52 7 7.27 7 7V6H5C4.73 6 4.48 6.1 4.29 6.29C4.1 6.48 4 6.73 4 7V10.25ZM4 12.25V19C4 19.27 4.1 19.52 4.29 19.71C4.48 19.9 4.73 20 5 20H19C19.27 20 19.52 19.9 19.71 19.71C19.9 19.52 20 19.27 20 19V12.25H4Z" fill="${corIconeDesabilitado}"/></svg>');
      case IconeTabbar.agenda_ativo:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C7.73 2 7.48 2.11 7.29 2.29C7.1 2.48 7 2.73 7 3V4H5C4.2 4 3.44 4.32 2.88 4.88C2.32 5.44 2 6.21 2 7V10.25V12.25V19C2 19.8 2.32 20.56 2.88 21.12C3.44 21.68 4.21 22 5 22H19C19.8 22 20.56 21.68 21.12 21.12C21.68 20.56 22 19.79 22 19V12.25V10.25V7C22 6.2 21.68 5.44 21.12 4.88C20.56 4.32 19.79 4 19 4H17V3C17 2.73 16.89 2.48 16.71 2.29C16.53 2.1 16.27 2 16 2C15.73 2 15.48 2.11 15.29 2.29C15.1 2.47 15 2.73 15 3V4H9V3C9 2.73 8.89 2.48 8.71 2.29C8.52 2.1 8.27 2 8 2ZM20 10.25V7C20 6.73 19.9 6.48 19.71 6.29C19.52 6.11 19.26 6 19 6H17V7C17 7.27 16.9 7.52 16.71 7.71C16.52 7.89 16.27 8 16 8C15.73 8 15.48 7.9 15.29 7.71C15.11 7.52 15 7.27 15 7V6H9V7C9 7.27 8.9 7.52 8.71 7.71C8.52 7.89 8.27 8 8 8C7.73 8 7.48 7.9 7.29 7.71C7.11 7.52 7 7.27 7 7V6H5C4.73 6 4.48 6.1 4.29 6.29C4.1 6.48 4 6.73 4 7V10.25H20Z" fill="${corPrimaria}"/></svg>');
      case IconeTabbar.perfil:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_2562_21049)"><path d="M12 5.02002C9.79 5.02002 8 6.81002 8 9.02002C8 11.23 9.79 13.02 12 13.02C14.21 13.02 16 11.23 16 9.02002C16 6.81002 14.21 5.02002 12 5.02002ZM12 11.02C10.9 11.02 10 10.12 10 9.02002C10 7.92002 10.9 7.02002 12 7.02002C13.1 7.02002 14 7.92002 14 9.02002C14 10.12 13.1 11.02 12 11.02Z" fill="${corIconeDesabilitado}"/><path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM8.05 18.95C8.28 16.96 9.95 15.4 12 15.4C14.05 15.4 15.73 16.96 15.95 18.95C14.78 19.62 13.43 20 12 20C10.57 20 9.21 19.61 8.05 18.95ZM17.72 17.59C16.95 15.16 14.68 13.4 12 13.4C9.32 13.4 7.05 15.16 6.28 17.59C4.87 16.15 4 14.18 4 12.01C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 14.17 19.13 16.14 17.72 17.58V17.59Z" fill="${corIconeDesabilitado}"/></g><defs><clipPath id="clip0_2562_21049"><rect width="20" height="20" fill="white" transform="translate(2 2)"/></clipPath></defs></svg>');
      case IconeTabbar.perfil_ativo:
        return SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_2562_21047)"><path d="M12 5.02002C9.79 5.02002 8 6.81002 8 9.02002C8 11.23 9.79 13.02 12 13.02C14.21 13.02 16 11.23 16 9.02002C16 6.81002 14.21 5.02002 12 5.02002Z" fill="${corPrimaria}"/><path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM17.72 17.58C16.95 15.15 14.68 13.39 12 13.39C9.32 13.39 7.05 15.15 6.28 17.58C4.87 16.14 4 14.17 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 14.17 19.13 16.14 17.72 17.58Z" fill="${corPrimaria}"/></g><defs><clipPath id="clip0_2562_21047"><rect width="20" height="20" fill="white" transform="translate(2 2)"/></clipPath></defs></svg>');
      case IconeTabbar.nutricao_fitstream:
        return SvgPicture.string('<svg width="19" height="21" viewBox="0 0 19 21" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_9853_36819)"><path d="M15.12 2.88C14.56 2.32 13.79 2 13 2H11.82C11.61 1.42 11.23 0.91 10.73 0.55C10.22 0.19 9.62 0 9 0H7C6.38 0 5.78 0.19 5.27 0.55C4.77 0.91 4.38 1.41 4.18 2H3C2.2 2 1.44 2.32 0.88 2.88C0.32 3.44 0 4.21 0 5V17C0 17.8 0.32 18.56 0.88 19.12C1.44 19.68 2.21 20 3 20H7.69C8.23 20 8.67 19.56 8.68 19.02C8.68 19.02 8.68 19 8.68 18.99C8.68 18.44 8.24 18 7.69 18H3C2.73 18 2.48 17.89 2.29 17.71C2.1 17.52 2 17.27 2 17V5C2 4.73 2.11 4.48 2.29 4.29C2.48 4.1 2.73 4 3 4H4V5C4 5.27 4.11 5.52 4.29 5.71C4.48 5.9 4.73 6 5 6H11C11.27 6 11.52 5.89 11.71 5.71C11.9 5.52 12 5.27 12 5V4H13C13.27 4 13.52 4.11 13.71 4.29C13.9 4.48 14 4.73 14 5V8.04C14 8.59 14.45 9.04 15 9.04C15.55 9.04 16 8.59 16 8.04V5C16 4.2 15.68 3.44 15.12 2.88ZM10 4H6V3C6 2.73 6.11 2.48 6.29 2.29C6.48 2.1 6.73 2 7 2H9C9.27 2 9.52 2.11 9.71 2.29C9.9 2.48 10 2.73 10 3V4Z" fill="${corIconeDesabilitado}"/><path d="M9.78289 9.71C9.92105 9.52 10 9.27 10 9C10 8.73 9.92105 8.48 9.78289 8.29C9.64474 8.1 9.44737 8 9.25 8H4.75C4.55263 8 4.35526 8.11 4.21711 8.29C4.07895 8.48 4 8.73 4 9C4 9.27 4.07895 9.52 4.21711 9.71C4.35526 9.9 4.55263 10 4.75 10H9.25C9.44737 10 9.64474 9.89 9.78289 9.71Z" fill="${corIconeDesabilitado}"/><path d="M4.66667 12C4.48667 12 4.32 12.11 4.19333 12.29C4.06667 12.48 4 12.73 4 13C4 13.27 4.07333 13.52 4.19333 13.71C4.32 13.9 4.48667 14 4.66667 14H7.33333C7.51333 14 7.68 13.89 7.80667 13.71C7.93333 13.53 8 13.27 8 13C8 12.73 7.92667 12.48 7.80667 12.29C7.68667 12.1 7.51333 12 7.33333 12H4.66667Z" fill="${corIconeDesabilitado}"/><path d="M16.3249 13.5944H16.3811C16.4485 13.5944 16.4597 13.5944 16.5047 13.6717C16.5496 13.738 16.5946 13.8263 16.6395 13.9368C16.6844 14.0582 16.7181 14.1797 16.7406 14.3344C16.7631 14.4779 16.7855 14.6105 16.7968 14.754C16.808 14.9197 16.808 15.1074 16.7968 15.3283C16.7968 15.4498 16.7743 15.5713 16.7631 15.7038C16.7406 15.9137 16.7069 16.1235 16.662 16.3223C16.5721 16.7309 16.4485 17.0843 16.2912 17.3825C16.1676 17.6255 16.0104 17.8464 15.8194 18.0341C15.7295 18.1335 15.6284 18.1998 15.5385 18.244C15.4823 18.2771 15.4374 18.2882 15.3812 18.2992C15.3588 18.2992 15.3363 18.2992 15.3251 18.2992C15.3138 18.2992 15.2913 18.2992 15.2689 18.2992C15.1341 18.2882 15.0105 18.2329 14.8757 18.1556L14.8083 18.1115L13.932 17.5703L13.0332 18.0783L12.9658 18.1115H12.9433C12.8197 18.1998 12.6849 18.244 12.5389 18.2771C12.4939 18.2771 12.449 18.2882 12.4041 18.2882H12.3367C12.258 18.2882 12.1906 18.255 12.1007 18.2219C11.9884 18.1667 11.8873 18.1004 11.7862 18.001C11.5615 17.7912 11.3817 17.5482 11.2469 17.2721C11.1121 17.007 11.011 16.7199 10.9211 16.3886C10.8762 16.2119 10.8425 16.0352 10.8088 15.8584C10.7863 15.748 10.7751 15.6376 10.7638 15.5161C10.7414 15.2952 10.7414 15.1185 10.7414 14.9639C10.7414 14.8424 10.7414 14.7209 10.7638 14.5994C10.7975 14.3454 10.8425 14.1245 10.9099 13.9478C10.9548 13.8374 10.9997 13.749 11.0559 13.6938C11.1009 13.6275 11.1346 13.6165 11.202 13.6054C11.2132 13.6054 11.2581 13.6054 11.3368 13.6054C11.4716 13.6054 11.6289 13.6275 11.8086 13.6827C12.0221 13.738 12.2468 13.8263 12.4939 13.9478C12.9209 14.1466 13.3815 14.257 13.8646 14.257C13.9432 14.257 14.0218 14.257 14.1005 14.257C14.4937 14.2239 14.8757 14.1245 15.2352 13.9478C15.37 13.8815 15.516 13.8153 15.6509 13.7601C15.8081 13.7048 15.9542 13.6607 16.1002 13.6386C16.1676 13.6275 16.2351 13.6165 16.28 13.6165M16.28 11.9267C16.1115 11.9267 15.9542 11.9378 15.7857 11.9709C15.5273 12.0151 15.2801 12.0924 15.0442 12.1807C14.842 12.258 14.651 12.3354 14.46 12.4348C14.3027 12.5121 14.1454 12.5562 13.9657 12.5673C13.932 12.5673 13.887 12.5673 13.8533 12.5673C13.6399 12.5673 13.4264 12.5231 13.2242 12.4237C12.9096 12.2801 12.5951 12.1476 12.258 12.0593C11.9547 11.9709 11.6514 11.9157 11.3368 11.9157H11.3143C11.157 11.9157 10.9998 11.9267 10.8425 11.9599C10.3819 12.0482 9.99987 12.2801 9.69654 12.6446C9.51679 12.8544 9.39321 13.0974 9.2921 13.3514C9.15728 13.6938 9.07864 14.0582 9.0337 14.4227C9.01123 14.5884 9 14.7651 9 14.9418C9 15.1958 9 15.4388 9.0337 15.6817C9.04494 15.8364 9.06741 15.9799 9.08988 16.1235C9.12358 16.3554 9.16852 16.5874 9.23593 16.8082C9.34827 17.2279 9.48309 17.6366 9.68531 18.0231C9.91 18.4759 10.2021 18.8735 10.5728 19.2269C10.7975 19.4368 11.0447 19.6245 11.3256 19.757C11.5952 19.8896 11.876 19.9669 12.1681 19.989C12.2355 19.989 12.303 19.989 12.3704 19.989C12.5164 19.989 12.6625 19.9779 12.8085 19.9558C13.168 19.8896 13.5051 19.7681 13.8084 19.5803C13.8196 19.5803 13.8533 19.5582 13.8533 19.5582C13.8533 19.5582 13.8758 19.5693 13.887 19.5803C14.2353 19.8012 14.606 19.9448 15.0217 19.989C15.1004 19.989 15.1902 20 15.2689 20C15.37 20 15.4711 20 15.561 19.9779C15.8081 19.9448 16.0441 19.8675 16.2688 19.757C16.5384 19.6245 16.7743 19.4368 16.9878 19.2269C17.3023 18.9066 17.572 18.5422 17.7742 18.1446C18.0101 17.6807 18.1674 17.1948 18.2797 16.6978C18.3359 16.4327 18.3808 16.1677 18.4146 15.9026C18.437 15.737 18.4483 15.5713 18.4595 15.4056C18.4595 15.1516 18.4707 14.8976 18.4595 14.6436C18.4483 14.4448 18.4258 14.246 18.3808 14.0472C18.3359 13.8153 18.2797 13.5833 18.2011 13.3625C18.1112 13.1195 17.9876 12.8765 17.8304 12.6667C17.4933 12.2139 17.0327 11.9709 16.471 11.9267C16.4036 11.9267 16.3362 11.9267 16.28 11.9267Z" fill="${corIconeDesabilitado}"/><path d="M11.0262 9C11.0262 9 11.0262 9.01833 11.0262 9.0275C11.0436 9.0825 11.0785 9.13749 11.1308 9.17416C11.1831 9.21999 11.2529 9.24749 11.314 9.26582C11.4448 9.30249 11.5756 9.31166 11.7064 9.29332C11.7151 9.29332 11.7238 9.29332 11.7413 9.29332C11.8721 9.25666 11.9942 9.22916 12.125 9.20166C12.2994 9.17416 12.4826 9.15583 12.657 9.17416C12.8401 9.18333 13.0233 9.22916 13.189 9.30249C13.4506 9.42165 13.6424 9.61415 13.782 9.87997C13.8779 10.0541 13.939 10.2466 13.9738 10.4391C13.9826 10.4941 13.9913 10.5583 14 10.6133C14 10.6316 14 10.6408 14 10.6591C13.9913 10.6591 13.9826 10.6683 13.9738 10.6774C13.7209 10.7783 13.468 10.8608 13.2064 10.9249C12.9709 10.9799 12.7355 11.0074 12.5 10.9983C12.3081 10.9983 12.125 10.9616 11.9506 10.8883C11.6541 10.7691 11.436 10.5674 11.2791 10.2833C11.1831 10.1091 11.1221 9.9258 11.0698 9.73331C11.0349 9.57748 11.0087 9.41249 11 9.24749C11 9.18333 11 9.12833 11 9.06416C11 9.04583 11 9.03666 11 9.01833C11 9.01833 11 9.01833 11.0087 9.01833L11.0262 9Z" fill="${corIconeDesabilitado}"/><path d="M14.0233 12C14.0233 12 14.0233 11.9852 14.0233 11.9778C14.0349 11.8741 14.0581 11.7704 14.0814 11.6593C14.1163 11.5111 14.1512 11.3704 14.2093 11.2296C14.3023 10.9926 14.4186 10.7556 14.5814 10.5333C14.6977 10.3778 14.8372 10.2296 15.0116 10.0963C15.0581 10.0593 15.093 10.0296 15.1512 10C15.1628 10 15.1744 10.0074 15.186 10.0148C15.4535 10.1185 15.7326 10.2222 16 10.3333C16 10.3333 15.9884 10.3333 15.9767 10.3333C15.7674 10.3704 15.5814 10.4296 15.407 10.5185C15.1977 10.6222 15.0349 10.7556 14.8953 10.8963C14.7558 11.0444 14.6512 11.2074 14.5698 11.3704C14.5 11.5185 14.4419 11.6667 14.407 11.8148C14.3953 11.8741 14.3837 11.9259 14.3721 11.9852C14.3256 11.9926 14.2907 11.9852 14.2442 11.9852C14.2093 11.9852 14.1628 11.9852 14.1279 11.9852C14.0814 11.9852 14.0465 11.9852 14 11.9852L14.0233 12Z" fill="${corIconeDesabilitado}"/></g><defs><clipPath id="clip0_9853_36819"><rect width="18.52" height="20.34" fill="white"/></clipPath></defs></svg>');
      case IconeTabbar.nutricao_fitstream_ativo:
        return SvgPicture.string('<svg width="19" height="21" viewBox="0 0 19 21" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_9853_36896)"><path d="M15.12 2.88C14.56 2.32 13.79 2 13 2H11.82C11.61 1.42 11.23 0.91 10.73 0.55C10.22 0.19 9.62 0 9 0H7C6.38 0 5.78 0.19 5.27 0.55C4.77 0.91 4.38 1.41 4.18 2H3C2.2 2 1.44 2.32 0.88 2.88C0.32 3.44 0 4.21 0 5V17C0 17.8 0.32 18.56 0.88 19.12C1.44 19.68 2.21 20 3 20H7.69C8.23 20 8.67 19.56 8.68 19.02C8.68 19.02 8.68 19 8.68 18.99C8.68 18.44 8.24 18 7.69 18H3C2.73 18 2.48 17.89 2.29 17.71C2.1 17.52 2 17.27 2 17V5C2 4.73 2.11 4.48 2.29 4.29C2.48 4.1 2.73 4 3 4H4V5C4 5.27 4.11 5.52 4.29 5.71C4.48 5.9 4.73 6 5 6H11C11.27 6 11.52 5.89 11.71 5.71C11.9 5.52 12 5.27 12 5V4H13C13.27 4 13.52 4.11 13.71 4.29C13.9 4.48 14 4.73 14 5V8.04C14 8.59 14.45 9.04 15 9.04C15.55 9.04 16 8.59 16 8.04V5C16 4.2 15.68 3.44 15.12 2.88ZM10 4H6V3C6 2.73 6.11 2.48 6.29 2.29C6.48 2.1 6.73 2 7 2H9C9.27 2 9.52 2.11 9.71 2.29C9.9 2.48 10 2.73 10 3V4Z" fill="${corPrimaria}"/><path d="M9.78289 9.71C9.92105 9.52 10 9.27 10 9C10 8.73 9.92105 8.48 9.78289 8.29C9.64474 8.1 9.44737 8 9.25 8H4.75C4.55263 8 4.35526 8.11 4.21711 8.29C4.07895 8.48 4 8.73 4 9C4 9.27 4.07895 9.52 4.21711 9.71C4.35526 9.9 4.55263 10 4.75 10H9.25C9.44737 10 9.64474 9.89 9.78289 9.71Z" fill="${corPrimaria}"/><path d="M4.66667 12C4.48667 12 4.32 12.11 4.19333 12.29C4.06667 12.48 4 12.73 4 13C4 13.27 4.07333 13.52 4.19333 13.71C4.32 13.9 4.48667 14 4.66667 14H7.33333C7.51333 14 7.68 13.89 7.80667 13.71C7.93333 13.53 8 13.27 8 13C8 12.73 7.92667 12.48 7.80667 12.29C7.68667 12.1 7.51333 12 7.33333 12H4.66667Z" fill="${corPrimaria}"/><path d="M16.28 11.9267C16.1115 11.9267 15.9542 11.9378 15.7857 11.9709C15.5273 12.0151 15.2801 12.0924 15.0442 12.1807C14.842 12.258 14.651 12.3354 14.46 12.4348C14.3027 12.5121 14.1454 12.5562 13.9657 12.5673C13.932 12.5673 13.887 12.5673 13.8533 12.5673C13.6399 12.5673 13.4264 12.5231 13.2242 12.4237C12.9096 12.2801 12.5951 12.1476 12.258 12.0593C11.9547 11.9709 11.6514 11.9157 11.3368 11.9157H11.3143C11.157 11.9157 10.9998 11.9267 10.8425 11.9599C10.3819 12.0482 9.99987 12.2801 9.69654 12.6446C9.51679 12.8544 9.39321 13.0974 9.2921 13.3514C9.15728 13.6938 9.07864 14.0582 9.0337 14.4227C9.01123 14.5884 9 14.7651 9 14.9418C9 15.1958 9 15.4388 9.0337 15.6817C9.04494 15.8364 9.06741 15.9799 9.08988 16.1235C9.12358 16.3554 9.16852 16.5874 9.23593 16.8082C9.34827 17.2279 9.48309 17.6366 9.68531 18.0231C9.91 18.4759 10.2021 18.8735 10.5728 19.2269C10.7975 19.4368 11.0447 19.6245 11.3256 19.757C11.5952 19.8896 11.876 19.9669 12.1681 19.989C12.2355 19.989 12.303 19.989 12.3704 19.989C12.5164 19.989 12.6625 19.9779 12.8085 19.9558C13.168 19.8896 13.5051 19.7681 13.8084 19.5803C13.8196 19.5803 13.8533 19.5582 13.8533 19.5582C13.8533 19.5582 13.8758 19.5693 13.887 19.5803C14.2353 19.8012 14.606 19.9448 15.0217 19.989C15.1004 19.989 15.1902 20 15.2689 20C15.37 20 15.4711 20 15.561 19.9779C15.8081 19.9448 16.0441 19.8675 16.2688 19.757C16.5384 19.6245 16.7743 19.4368 16.9878 19.2269C17.3023 18.9066 17.572 18.5422 17.7742 18.1446C18.0101 17.6807 18.1674 17.1948 18.2797 16.6978C18.3359 16.4327 18.3808 16.1677 18.4146 15.9026C18.437 15.737 18.4483 15.5713 18.4595 15.4056C18.4595 15.1516 18.4707 14.8976 18.4595 14.6436C18.4483 14.4448 18.4258 14.246 18.3808 14.0472C18.3359 13.8153 18.2797 13.5833 18.2011 13.3625C18.1112 13.1195 17.9876 12.8765 17.8304 12.6667C17.4933 12.2139 17.0327 11.9709 16.471 11.9267C16.4036 11.9267 16.3362 11.9267 16.28 11.9267Z" fill="${corPrimaria}"/><path d="M11.0262 9C11.0262 9 11.0262 9.01833 11.0262 9.0275C11.0436 9.0825 11.0785 9.13749 11.1308 9.17416C11.1831 9.21999 11.2529 9.24749 11.314 9.26582C11.4448 9.30249 11.5756 9.31166 11.7064 9.29332C11.7151 9.29332 11.7238 9.29332 11.7413 9.29332C11.8721 9.25666 11.9942 9.22916 12.125 9.20166C12.2994 9.17416 12.4826 9.15583 12.657 9.17416C12.8401 9.18333 13.0233 9.22916 13.189 9.30249C13.4506 9.42165 13.6424 9.61415 13.782 9.87997C13.8779 10.0541 13.939 10.2466 13.9738 10.4391C13.9826 10.4941 13.9913 10.5583 14 10.6133C14 10.6316 14 10.6408 14 10.6591C13.9913 10.6591 13.9826 10.6683 13.9738 10.6774C13.7209 10.7783 13.468 10.8608 13.2064 10.9249C12.9709 10.9799 12.7355 11.0074 12.5 10.9983C12.3081 10.9983 12.125 10.9616 11.9506 10.8883C11.6541 10.7691 11.436 10.5674 11.2791 10.2833C11.1831 10.1091 11.1221 9.9258 11.0698 9.73331C11.0349 9.57748 11.0087 9.41249 11 9.24749C11 9.18333 11 9.12833 11 9.06416C11 9.04583 11 9.03666 11 9.01833C11 9.01833 11 9.01833 11.0087 9.01833L11.0262 9Z" fill="${corPrimaria}"/><path d="M14.0233 12C14.0233 12 14.0233 11.9852 14.0233 11.9778C14.0349 11.8741 14.0581 11.7704 14.0814 11.6593C14.1163 11.5111 14.1512 11.3704 14.2093 11.2296C14.3023 10.9926 14.4186 10.7556 14.5814 10.5333C14.6977 10.3778 14.8372 10.2296 15.0116 10.0963C15.0581 10.0593 15.093 10.0296 15.1512 10C15.1628 10 15.1744 10.0074 15.186 10.0148C15.4535 10.1185 15.7326 10.2222 16 10.3333C16 10.3333 15.9884 10.3333 15.9767 10.3333C15.7674 10.3704 15.5814 10.4296 15.407 10.5185C15.1977 10.6222 15.0349 10.7556 14.8953 10.8963C14.7558 11.0444 14.6512 11.2074 14.5698 11.3704C14.5 11.5185 14.4419 11.6667 14.407 11.8148C14.3953 11.8741 14.3837 11.9259 14.3721 11.9852C14.3256 11.9926 14.2907 11.9852 14.2442 11.9852C14.2093 11.9852 14.1628 11.9852 14.1279 11.9852C14.0814 11.9852 14.0465 11.9852 14 11.9852L14.0233 12Z" fill="${corPrimaria}"/></g><defs><clipPath id="clip0_9853_36896"><rect width="18.52" height="20.34" fill="white"/></clipPath></defs></svg>');
    }
  }
}

enum IconeTabbar {
  inicio, inicio_ativo, aulas, aulas_ativo, listaAlunos, listaAlunos_ativo, treino, treino_ativo, cross, cross_ativo, vitio, vitio_ativo, saude, saude_ativo, agenda, agenda_ativo, perfil, perfil_ativo, nutricao_fitstream, nutricao_fitstream_ativo
}