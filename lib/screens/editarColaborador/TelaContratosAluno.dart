import 'dart:convert';
import 'dart:io';

import 'package:after_layout/after_layout.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/TextWidgets.dart';
import 'package:app_treino/appWidgets/componentWidgets/ValidaCondicoes.dart';
import 'package:app_treino/appWidgets/componentWidgets/skeletons/SkeletonCardCreditoTreino.dart';
import 'package:app_treino/appWidgets/componentWidgets/skeletons/SkeletonTelaContratos.dart';
import 'package:app_treino/controlladores/ControladorAulaTurma.dart';
import 'package:app_treino/controlladores/ControladorContrato.dart';
import 'package:app_treino/model/contrato/ModalidadeContrato.dart';
import 'package:app_treino/screens/editarColaborador/listaContratoAtivo.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/config/personal_icon_icons.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorVendaDePlano.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorContratoUsuario.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/controlladores/controladorPlanner.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/contrato/ContratoUsuario.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/screens/contrato/TelaOpcoesDeCompraNovoContrato.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:get_it/get_it.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TelaContratosAluno extends StatefulWidget {
  TelaContratosAluno({Key? key}) : super(key: key);
  @override
  _TelaContratosAlunoState createState() => _TelaContratosAlunoState();
}

class _TelaContratosAlunoState extends State<TelaContratosAluno> with TickerProviderStateMixin, AfterLayoutMixin<TelaContratosAluno> {
  int indexPage = 0;
  final _controladorContratoUsuario = GetIt.I.get<ControladorContratoUsuario>();

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (_) {
        return ValidaCondicoes(
          moduloApp: ModuloApp.MODULO_VENDAS_DE_PLANOS,
          validacaoExtra: !(GetIt.I.get<ControladorCliente>().mDadosDoUsuario?.permiteContratosConcomitante ?? false),
          child: DefaultTabController(
            initialIndex: indexPage,
            length: 2,
            child: Scaffold(
              backgroundColor: Theme.of(context).colorScheme.surface,
              appBar: DSappBar(
                titulo: 'menu_perfil.meus_contratos',
                backgroundColor: Theme.of(context).colorScheme.surface,
              ),
              body: SingleChildScrollView(
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                      child: Container(
                        height: 40,
                        width: MediaQuery.of(context).size.width - 32,
                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: const Color(0x12767680)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(4.0),
                              child: InkWell(
                                onTap: () {
                                  setState(() {
                                    _controladorContratoUsuario.isMeuscontratoSelecionado = true;
                                    _controladorContratoUsuario.isPlanoSelecionado = false;
                                  });
                                },
                                child: Container(
                                  height: 32,
                                  width: (MediaQuery.of(context).size.width - 48) / 2,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(20),
                                      color: _controladorContratoUsuario.isMeuscontratoSelecionado
                                          ? DSLib.theme == ThemeMode.dark
                                              ? const Color(0xff202020)
                                              : const Color(0xffFFFFFF)
                                          : Colors.transparent),
                                  child: Center(
                                      child: DStextSubheadline('menu_perfil.meus_contratos',
                                          eHeavy: true, corCustomizada: !_controladorContratoUsuario.isMeuscontratoSelecionado ? const Color(0xFFA0A0A0) : null)),
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(4.0),
                              child: InkWell(
                                onTap: () {
                                  setState(() {
                                    _controladorContratoUsuario.isMeuscontratoSelecionado = false;
                                    _controladorContratoUsuario.isPlanoSelecionado = true;
                                  });
                                },
                                child: Container(
                                  height: 32,
                                  width: (MediaQuery.of(context).size.width - 48) / 2,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(20),
                                      color: _controladorContratoUsuario.isPlanoSelecionado
                                          ? DSLib.theme == ThemeMode.dark
                                              ? const Color(0xff202020)
                                              : const Color(0xffFFFFFF)
                                          : Colors.transparent),
                                  child: Center(
                                      child: DStextSubheadline('opcoes', eHeavy: true, corCustomizada: !_controladorContratoUsuario.isPlanoSelecionado ? const Color(0xFFA0A0A0) : null)),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                    _controladorContratoUsuario.isMeuscontratoSelecionado ? TelaMeusContratos() : TelaOpcoesDeCompraNovoContrato()
                  ],
                ),
              ),
            ),
          ),
          childFalse: Scaffold(
            backgroundColor: Theme.of(context).colorScheme.surface,
            appBar: DSappBar(
              backgroundColor: Theme.of(context).colorScheme.surface,
              titulo: 'menu_perfil.meus_contratos',
            ),
            body: TelaMeusContratos(),
          ),
        );
      },
    );
  }

  @override
  void afterFirstLayout(BuildContext context) {
    setState(() {
      indexPage = ModalRoute.of(context)!.settings.arguments != null ? ModalRoute.of(context)!.settings.arguments as int : 0;
      if (indexPage == 1) {
        _controladorContratoUsuario.isPlanoSelecionado = true;
        _controladorContratoUsuario.isMeuscontratoSelecionado = false;
      }
    });
  }
}

class TelaMeusContratos extends StatefulWidget {
  TelaMeusContratos({Key? key}) : super(key: key);

  @override
  State<TelaMeusContratos> createState() => _TelaMeusContratosState();
}

class _TelaMeusContratosState extends State<TelaMeusContratos> {
  final _refreshController = new RefreshController();
  final _controladorContratoUsuario = GetIt.I.get<ControladorContratoUsuario>();
  final _controladorPlanner = GetIt.I.get<ControladorPlanner>();
  final _controladorCliente = GetIt.I.get<ControladorCliente>();
  final _controladorVenda = GetIt.I.get<ControladorVendaDePlano>();
  final _controllerAulasTurmas = GetIt.I.get<ControladorAulaTurma>();

  @override
  void initState() {
    _refresh();
    _controladorVenda.consultarParcelasAtrasadas(carregando: () {}, sucesso: () {}, falha: (erro) {});
    _controladorContratoUsuario.resetParaContratoAtual();
    _controladorVenda.consultarConfigVendasOnline(
      carregando: () {},
      falha: (p0) {},
      sucesso: () {},
    );
    _controladorVenda.consultarConfigLinkPagamente(carregando: () {}, falha: (p0) {}, sucesso: () {});
    _controladorContratoUsuario.consultarContratosAluno(
        retornarTodos: true,
        forceconsulta: true,
        carregando: () {},
        falha: (_) {},
        sucessoLista: (contratos) {
          setState(() {});
        });
    super.initState();
  }

  void _refresh() {
    _controladorContratoUsuario.consultarCreditosPermitidos();
    if ((GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.HABILITAR_ASSINATURA_CONTRATO_APP).habilitado!)) {
      GetIt.I.get<ControladorContratoUsuario>().consultarContratosAluno(
          retornarTodos: true,
          forceconsulta: true,
          carregando: () {},
          sucessoLista: (contratos) {
            processarContratosEAditivos(contratos);
          },
          falha: (e) {});
    }
    _controladorContratoUsuario.consultarOsContratosDoUsuario(sucesso: () {
      _controladorContratoUsuario.setarContratosDropDown();
      _refreshController.refreshCompleted();
    }, falha: (falha) {
      _refreshController.refreshFailed();
    });
    _controladorCliente.consultarConveniosDePagamento(carregando: () {}, falha: (falha) {}, sucesso: () {});
  }

  Future<void> processarContratosEAditivos(List<ContratoAssinatura> contratos) async {
    try {
      final capp = GetIt.I.get<ControladorApp>();
      if (!capp.getConfiguracaoApp(ModuloApp.HABILITAR_ASSINATURA_CONTRATO_APP).habilitado!) {
        return;
      }
      // Filtra aditivos e contratos não assinados
      final aditivosNaoAssinados = contratos.where((c) => c.ehAditivo == true && c.assinado == false).toList();
      
      final contratosNaoAssinados = contratos.where((c) => c.ehAditivo == false && c.assinado == false).toList();
      final prefs = await SharedPreferences.getInstance();

      // Processa aditivos não assinados
      if (aditivosNaoAssinados.isNotEmpty) {
        final aditivoMaisRecente = aditivosNaoAssinados.reduce((a, b) => (a.aditivo ?? 0) > (b.aditivo ?? 0) ? a : b);
            
        await _salvarENavegar(prefs, aditivoMaisRecente);
        return;
      }

      // Processa contratos não assinados
      if (contratosNaoAssinados.isNotEmpty) {
        final contrato = contratosNaoAssinados.first;
        await _salvarENavegar(prefs, contrato);
      }
    } catch (e) {
      print('Erro ao processar contratos: $e');
      // Adicionar tratamento de erro adequado
    }
  }

  Future<void> _salvarENavegar(SharedPreferences prefs, ContratoAssinatura contrato) async {
    await prefs.setString('contrato_assinado', jsonEncode(contrato));
    await Navigator.of(context).pushNamed('/telaContratoBloqueioAssinatura', arguments: {'dadosContrato': contrato, 'foto': false});
  }

  bool get getPagamentoLiberado {
    return (_controladorVenda.isPagamentoPixHabilitado) ||
        (_controladorVenda.configLinkPagamento.convenioCartaoDeCredito ?? '').isNotEmpty ||
        (_controladorVenda.configLinkPagamento.convenioBoleto ?? '').isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      primary: false,
      physics: const BouncingScrollPhysics(),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Observer(
              builder: (_) {
                switch (_controladorContratoUsuario.mStatusConsultaContratos) {
                  case ServiceStatus.Waiting:
                    return const SkeletonTelaContratos();
                  case ServiceStatus.Error:
                    return DScard(
                      paddingInterno: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          DSsvg(imagem: Imagem.contrato),
                          const SizedBox(height: 24),
                          DStextHeadline(
                            'Ops, ainda não há nenhum contrato por aqui',
                            eHeavy: true,
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                        ],
                      ),
                    );
                  case ServiceStatus.Done:
                    return Column(
                      children: [
                        _controladorContratoUsuario.listaContratosDropDown.length > 1
                            ? Column(
                                children: [
                                  dropOutrosContratos(),
                                  const SizedBox(height: 16),
                                ],
                              )
                            : Container(),
                        itemContrato(_controladorContratoUsuario, _controladorCliente),
                        ValidaCondicoes(
                            apenasAluno: true,
                            moduloApp: ModuloApp.MODULO_ALUNO_PODE_RETORNAR_TRANCAMENTO,
                            validacaoExtra: _controladorContratoUsuario.contratoVisualizarEstaTrancado && _controladorContratoUsuario.contratoTrancadoPodeVoltar,
                            child: cardTrancamentoAgendado(_controladorContratoUsuario)),
                        cardTrancamentoNaoPodeVoltar(_controladorContratoUsuario),
                        const SizedBox(height: 16),
                        (_controladorVenda.alunoParcelas?.valorCobrar ?? 0) > 0 && getPagamentoLiberado
                            ? Column(
                                children: [
                                  itemParcelasAtrasadas(_controladorContratoUsuario, _controladorVenda),
                                  const SizedBox(height: 16),
                                ],
                              )
                            : Container(),
                        itemCredidoTreino(),
                        itemParcelas(_controladorContratoUsuario, _controladorVenda),
                        const SizedBox(height: 16),
                        itemPremium(_controladorPlanner)
                      ],
                    );
                  case ServiceStatus.Empty:
                    return DScard(
                      paddingInterno: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          DSsvg(imagem: Imagem.contrato),
                          const SizedBox(height: 16),
                          DStextHeadline(
                            'ops_ainda_nenhum_contrato',
                            eHeavy: true,
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ValidaCondicoes(
                              moduloApp: ModuloApp.MODULO_VENDAS_DE_PLANOS,
                              childFalse: DStextBody('voce_ainda_nao_firmou_nenhum_contrato', eHeavy: false, textAlign: TextAlign.center),
                              child: Column(
                                children: [
                                  DStextBody('voce_ainda_nao_firmou_nenhum_contrato_vamos_fazer_agora', eHeavy: false, textAlign: TextAlign.center),
                                  // const SizedBox(height: 16),
                                  // DSbotaoPadrao(titulo: localizedString('assinar_contrato'),
                                  //   onTap: () {
                                  //     setState(() {
                                  //       _controladorContratoUsuario.isMeuscontratoSelecionado = false;
                                  //       _controladorContratoUsuario.isPlanoSelecionado = true;
                                  //     });
                                  //   },
                                  // )
                                ],
                              )),
                        ],
                      ),
                    );
                }
              },
            )
          ],
        ),
      ),
    );
  }

  dropOutrosContratos() {
    return DScard(
        onTap: () {
          showDialogoSelecaoContrato(_controladorContratoUsuario.contratoVisualizar);
        },
        paddingInterno: const EdgeInsets.all(16),
        child: SizedBox(
          height: 45,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                const DSbotaoCircular(
                  icone: TreinoIcon.file_check,
                  altura: 30,
                  categoria: Categoria.secundario,
                ),
                const SizedBox(width: 4),
                Padding(
                  padding: const EdgeInsets.only(left: 6),
                  child: DStextSubheadline('contract'),
                ),
                const Spacer(),
                SizedBox(
                    width: MediaQuery.of(context).size.width - 200,
                    child: DStextBody(
                      UtilitarioApp.sentenseCase(_controladorContratoUsuario.contratoVisualizar!.nomePlano ?? 'Escolha um contrato'),
                      eHeavy: false,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.right,
                    )),
                Icon(
                  TreinoIcon.angle_right,
                  color: Theme.of(context).primaryColor,
                )
              ],
            ),
          ),
        ));
  }

  showDialogoSelecaoContrato(ContratoUsuario? contrato) {
    showModalBottomSheet(
      isScrollControlled: true,
      enableDrag: false,
      isDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
          return DScard(
            paddingExterno: const EdgeInsets.all(16),
            child: Wrap(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const SizedBox(
                        height: 30,
                        width: 30,
                      ),
                      DStextHeadline('contratos', eHeavy: true),
                      DSbotaoCircular(
                          categoria: Categoria.secundario,
                          onTap: () {
                            GetIt.I.get<NavigationService>().goBack();
                          },
                          icone: PersonalIcon.x)
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                ListView.builder(
                    shrinkWrap: true,
                    itemBuilder: (_, index) {
                      return Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                        child: Column(
                          children: [
                            RadioListTile(
                              contentPadding: EdgeInsets.zero,
                              dense: false,
                              enableFeedback: true,
                              title: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  DStextSubheadline(UtilitarioApp.sentenseCase(_controladorContratoUsuario.listaContratosDropDown[index]!.nomePlano!), eHeavy: true),
                                  DStextBody(
                                    '${Platform.localeName.contains('en_') ? UtilitarioApp.formatarDataFormatoAmericano(_controladorContratoUsuario.listaContratosDropDown[index]!.vigenciaDe!) : UtilDataHora.getDiaMesAno(dateString: _controladorContratoUsuario.listaContratosDropDown[index]!.vigenciaDe!)} - ${Platform.localeName.contains('en_') ? UtilitarioApp.formatarDataFormatoAmericano(_controladorContratoUsuario.listaContratosDropDown[index]!.vigenciaAteAjustada!) : UtilDataHora.getDiaMesAno(dateString: _controladorContratoUsuario.listaContratosDropDown[index]!.vigenciaAteAjustada!)}',
                                    eHeavy: false,
                                    ePrimario: false,
                                  ),
                                  DStextBody(
                                    '${localizedString('modalidade')}: ${ajustarModalidades(modalidades: _controladorContratoUsuario.listaContratosDropDown[index]!.modalidades ?? [])}',
                                    eHeavy: false,
                                    ePrimario: false,
                                  ),
                                  DStextBody(
                                    '${UtilitarioApp.sentenseCaseFirst(localizedString('codigo'))}: ${_controladorContratoUsuario.listaContratosDropDown[index]!.codigo}',
                                    eHeavy: false,
                                    ePrimario: false,
                                  ),
                                ],
                              ),
                              activeColor: Theme.of(context).primaryColor,
                              value: _controladorContratoUsuario.listaContratosDropDown[index]!,
                              groupValue: contrato,
                              onChanged: (value) {
                                setState(() {
                                  contrato = value as ContratoUsuario;
                                });
                              },
                            ),
                            //index < idioma.length ? Divider() : Container()
                          ],
                        ),
                      );
                    },
                    itemCount: _controladorContratoUsuario.listaContratosDropDown.length),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: DSbotaoPadrao(
                    titulo: localizedString('save'),
                    onTap: () {
                      _controladorContratoUsuario.setContratoSelecionado(contrato as ContratoUsuario);
                      Navigator.of(context).pop();
                    },
                  ),
                ),
              ],
            ),
          );
        });
      },
    );
  }

  itemContrato(ControladorContratoUsuario controladorContrato, ControladorCliente controladorCliente) {
    bool exibirCancelamento = GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.PERMITIR_ALUNO_CANCELAR_CONTRATO).habilitado!;
    void renovarContrato(BuildContext context) {
      analytic(EventosKey.contrato_pressionou_renovar);
      Navigator.pushNamed(context, '/telaRenovarContrato', arguments: _controladorCliente).then((contratoInativo) {
        if (contratoInativo != null) {
          if (contratoInativo is bool) {
            DSalerta().exibirAlertaSimplificado(context: context, titulo: 'plano_inativo_titulo', subtitulo: 'plano_inativo_mensagem', tituloBotao: 'Ok', tipoAlerta: TipoAlerta.alerta);
          }
        }
      });
    }

    return DScard(
        paddingInterno: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                const DSbotaoCircular(altura: 30, categoria: Categoria.secundario, icone: TreinoIcon.file_check),
                const SizedBox(width: 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DStextSubheadline(
                        camelCase(controladorContrato.contratoVisualizar?.nomePlano ?? 'contract'),
                        eHeavy: true,
                      ),
                      DStextCaption1(
                        '${Platform.localeName.contains('en_') ? UtilitarioApp.formatarDataFormatoAmericano(controladorContrato.contratoVisualizar!.vigenciaDe!) : UtilDataHora.getDiaMesAno(dateString: controladorContrato.contratoVisualizar!.vigenciaDe!)} - ${Platform.localeName.contains('en_') ? UtilitarioApp.formatarDataFormatoAmericano(controladorContrato.contratoVisualizar!.vigenciaAteAjustada!) : UtilDataHora.getDiaMesAno(dateString: controladorContrato.contratoVisualizar!.vigenciaAteAjustada!)}',
                        eHeavy: false,
                      )
                    ],
                  ),
                ),
                IconButton(
                    onPressed: () {
                      mostarMaisOpcoes(context);
                    },
                    icon: const Icon(TreinoIcon.ellipsis_v))
              ],
            ),
            const SizedBox(height: 18),
            controladorContrato.contratoVisualizarEstaDeFerias
                ? Column(
                    children: [
                      DSsvg(imagem: Imagem.praia),
                      const SizedBox(height: 16),
                      DStextHeadline('ferias_agendada', eHeavy: true),
                      const SizedBox(height: 8),
                      controladorContrato.operacaoesContrato.isNotEmpty
                          ? DStextBody(
                              localizedString('de_a', args: [
                                UtilDataHora.getDiaMesAno(formatarPorLocalidade: true, dateString: controladorContrato.operacaoesContrato.first.dataInicio),
                                UtilDataHora.getDiaMesAno(formatarPorLocalidade: true, dateString: controladorContrato.operacaoesContrato.first.dataFim)
                              ]),
                              eHeavy: false,
                              ePrimario: false,
                            )
                          : Container(),
                      ValidaCondicoes(
                        moduloApp: ModuloApp.MODULO_ALUNO_PODE_RETORNAR_TRANCAMENTO_FERIAS,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(0, 16, 0, 0),
                          child: DSbotaoPadrao(
                            titulo: localizedString('retornar_das_ferias'),
                            onTap: () {
                              controladorContrato.retornarContratoDeTrancamento(
                                carregando: () => UtilitarioApp().showDialogCarregando(context),
                                sucesso: () {
                                  Navigator.pop(context);
                                  DSalertaSucesso().exibirAlerta(context: context);
                                },
                                falha: (falha) {
                                  Navigator.pop(context);
                                  DSalerta()
                                      .exibirAlertaSimplificado(context: context, titulo: 'pasta_contrato.pagamento_recusado_tituloMensagem', subtitulo: '${falha}', tituloBotao: 'continue');
                                },
                              );
                            },
                          ),
                        ),
                      )
                    ],
                  )
                : Column(
                    children: [
                      DSlinearProgress(largura: MediaQuery.of(context).size.width - 32, percentual: controladorContrato.porcentagemVigenciaCumpridaDoContrato),
                      const Padding(
                        padding: EdgeInsets.fromLTRB(0, 16, 0, 16),
                        child: Divider(),
                      ),
                      DScard(
                          paddingInterno: const EdgeInsets.all(16.0),
                          categoria: CategoriaCard.secundario,
                          child: Column(
                            children: [
                              campoTextoDetalhes(
                                  localizedString('valor_contrato'),
                                  controladorContrato.contratoVisualizar!.valorBase == 0 || !GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_VALORESDECONTRATO).habilitado!
                                      ? localizedString('valor_nao_informado')
                                      : '${UtilitarioApp.formatarDinheiro(controladorContrato.contratoVisualizar!.valorBase)}'),
                              const SizedBox(height: 14),
                              campoTextoDetalhes('duracao',
                                  '${controladorContrato.contratoVisualizar!.numeroMeses} ${controladorContrato.contratoVisualizar!.numeroMeses! > 1 ? localizedString('meses') : localizedString('Mes')}'),
                              const SizedBox(height: 14),
                              campoTextoDetalhes(
                                  'editarColaborador.numero_cartao',
                                  controladorCliente.convenioAtual.numeroCartao != null
                                      ? controladorCliente.convenioAtual.numeroCartao!.isNotEmpty
                                          ? '•••• •••• •••• ${controladorCliente.convenioAtual.numeroCartao!.substring(12)}'
                                          : localizedString('nenhum_cartao')
                                      : localizedString('nenhum_cartao')),
                              const SizedBox(height: 14),
                              campoTextoDetalhes('pasta_contrato.tipo_cobranca',
                                  controladorCliente.convenioAtual.transacaoOnline == true ? localizedString('pasta_contrato.recorrencia_mensal') : localizedString('normal')),
                              const SizedBox(height: 14),
                              campoTextoDetalhes('vencimento_da_fatura', '${controladorContrato.contratoVisualizar!.dataLancamento?.substring(0, 2) ?? '0'}'),
                              controladorContrato.contratoVisualizar!.vendaCreditoTreino!
                                  ? ValidaCondicoes(
                                      moduloApp: ModuloApp.MODULO_CREDITO_DE_CONTRATO,
                                      child: Padding(
                                        padding: const EdgeInsets.only(top: 14),
                                        child: campoTextoDetalhes('creditos_disponivel', '${_controllerAulasTurmas.saldoParaAulasTurmasCredito.toInt()}'),
                                      ))
                                  : Container(),
                            ],
                          )),
                      Visibility(
                        visible: !controladorContrato.isDesistente &&
                            !controladorContrato.isVisitante &&
                            !controladorContrato.contratoVisualizarEstaTrancado &&
                            !controladorContrato.contratoVisualizarEstaDeFerias &&
                            !controladorContrato.isCancelado,
                        child: Column(
                          children: [
                            const SizedBox(height: 24),
                            //esperando backend
                            ListView.separated(
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              separatorBuilder: (context, index) => const SizedBox(height: 24),
                              itemCount: _controladorContratoUsuario.obterAditivosDoContratoVisualizar.length <= 2 ? _controladorContratoUsuario.obterAditivosDoContratoVisualizar.length : 2,
                              itemBuilder: (context, index) {
                                final aditivo = _controladorContratoUsuario.obterAditivosDoContratoVisualizar[index];

                                return AditivoItem(
                                  titulo: aditivo.contrato.toString(),
                                  numeroAditivo: aditivo.contrato.toString(),
                                  textoAditivo: aditivo.contratotextopadrao,
                                );
                              },
                            ),
                            if (_controladorContratoUsuario.obterAditivosDoContratoVisualizar.length > 2)
                              Center(
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 24),
                                  child: DSbotaoPadrao(
                                    titulo: 'ver_mais_aditivos',
                                    tipoBotao: TipoBotao.sublinhado,
                                    alturaIcone: 20,
                                    onTap: () {
                                      Navigator.pushNamed(context, '/telaContratoAditivos', arguments: _controladorContratoUsuario.contratoVisualizar);
                                    },
                                  ),
                                ),
                              ),

                            const Padding(
                              padding: EdgeInsets.fromLTRB(0, 16, 0, 16),
                              child: Divider(),
                            ),
                            if (exibirCancelamento)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 24),
                                child: DSbotaoPadrao(
                                  titulo: 'renew',
                                  desabilitado: !(!(controladorContrato.contratoVisualizar?.renovacaoautomaticasimnao ?? false) &&
                                      GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_RENOVACAO_DE_CONTRATO).habilitado! &&
                                      controladorContrato.contratoVisualizar!.permiteRenovar! &&
                                      !controladorContrato.contratoVisualizar!.bolsa!),
                                  icone: TreinoIcon.file_redo_alt,
                                  posicaoIcone: PosicaoIcone.esquerda,
                                  onTap: !(controladorContrato.contratoVisualizar?.renovacaoautomaticasimnao ?? false) &&
                                          GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_RENOVACAO_DE_CONTRATO).habilitado! &&
                                          controladorContrato.contratoVisualizar!.permiteRenovar! &&
                                          !controladorContrato.contratoVisualizar!.bolsa!
                                      ? () => renovarContrato(context)
                                      : null,
                                ),
                              ),

                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                if (!exibirCancelamento)
                                  InkWell(
                                    onTap: !(controladorContrato.contratoVisualizar?.renovacaoautomaticasimnao ?? false) &&
                                            GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_RENOVACAO_DE_CONTRATO).habilitado! &&
                                            controladorContrato.contratoVisualizar!.permiteRenovar! &&
                                            !controladorContrato.contratoVisualizar!.bolsa!
                                        ? () {
                                            renovarContrato(context);
                                          }
                                        : null,
                                    child: Column(
                                      children: [
                                        DSbotaoCircular(
                                          corBordaCustomizada: !(controladorContrato.contratoVisualizar?.renovacaoautomaticasimnao ?? false) &&
                                                  GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_RENOVACAO_DE_CONTRATO).habilitado! &&
                                                  controladorContrato.contratoVisualizar!.permiteRenovar! &&
                                                  !controladorContrato.contratoVisualizar!.bolsa!
                                              ? Theme.of(context).primaryColor
                                              : DSLib.theme == ThemeMode.dark
                                                  ? const Color(0xFF424242)
                                                  : const Color(0xffDCDDDF),
                                          categoria: Categoria.comBorda,
                                          icone: TreinoIcon.file_redo_alt,
                                          corIcone: !(controladorContrato.contratoVisualizar?.permiterenovacaoautomatica ?? false) &&
                                                  GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_RENOVACAO_DE_CONTRATO).habilitado! &&
                                                  controladorContrato.contratoVisualizar!.permiteRenovar! &&
                                                  !controladorContrato.contratoVisualizar!.bolsa!
                                              ? DSLib.theme == ThemeMode.dark
                                                  ? const Color(0xffF5F5F5)
                                                  : const Color(0xff3F3F3F)
                                              : DSLib.theme == ThemeMode.dark
                                                  ? const Color(0xFF424242)
                                                  : const Color(0xffDCDDDF),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.only(top: 8),
                                          child: DStextCaption1('renew',
                                              eHeavy: false,
                                              corCustomizada: !(controladorContrato.contratoVisualizar?.renovacaoautomaticasimnao ?? false) &&
                                                      GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_RENOVACAO_DE_CONTRATO).habilitado! &&
                                                      controladorContrato.contratoVisualizar!.permiteRenovar! &&
                                                      !controladorContrato.contratoVisualizar!.bolsa!
                                                  ? DSLib.theme == ThemeMode.dark
                                                      ? const Color(0xffF5F5F5)
                                                      : const Color(0xff3F3F3F)
                                                  : DSLib.theme == ThemeMode.dark
                                                      ? const Color(0xFF424242)
                                                      : const Color(0xffDCDDDF)),
                                        )
                                      ],
                                    ),
                                  ),
                                InkWell(
                                  onTap: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_TRANCAMENTO_DE_CONTRATO).habilitado! &&
                                          !controladorContrato.isDesistente &&
                                          !controladorContrato.isVencido &&
                                          !UtilDataHora.dataJaPassouDeHojeHora0(dateTime: UtilDataHora.parseStringToDate(controladorContrato.contratoVisualizar!.vigenciaDe))
                                      ? () {
                                          analytic(EventosKey.contrato_pressionou_trancamento);
                                          Navigator.pushNamed(context, '/telaTrancamentoDeContrato');
                                        }
                                      : null,
                                  child: Column(
                                    children: [
                                      DSbotaoCircular(
                                          corBordaCustomizada: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_TRANCAMENTO_DE_CONTRATO).habilitado! &&
                                                  !controladorContrato.isDesistente &&
                                                  !controladorContrato.isVencido &&
                                                  !UtilDataHora.dataJaPassouDeHojeHora0(dateTime: UtilDataHora.parseStringToDate(controladorContrato.contratoVisualizar!.vigenciaDe))
                                              ? Theme.of(context).primaryColor
                                              : DSLib.theme == ThemeMode.dark
                                                  ? const Color(0xFF424242)
                                                  : const Color(0xffDCDDDF),
                                          categoria: Categoria.comBorda,
                                          icone: TreinoIcon.file_lock_alt,
                                          corIcone: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_TRANCAMENTO_DE_CONTRATO).habilitado! &&
                                                  !controladorContrato.isDesistente &&
                                                  !controladorContrato.isVencido &&
                                                  !UtilDataHora.dataJaPassouDeHojeHora0(dateTime: UtilDataHora.parseStringToDate(controladorContrato.contratoVisualizar!.vigenciaDe))
                                              ? DSLib.theme == ThemeMode.dark
                                                  ? const Color(0xffF5F5F5)
                                                  : const Color(0xff3F3F3F)
                                              : DSLib.theme == ThemeMode.dark
                                                  ? const Color(0xFF424242)
                                                  : const Color(0xffDCDDDF)),
                                      Padding(
                                        padding: const EdgeInsets.only(top: 8),
                                        child: DStextCaption1('tela_detalhes_contrato.trancamento',
                                            eHeavy: false,
                                            corCustomizada: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_TRANCAMENTO_DE_CONTRATO).habilitado! &&
                                                    !controladorContrato.isDesistente &&
                                                    !controladorContrato.isVencido &&
                                                    !UtilDataHora.dataJaPassouDeHojeHora0(dateTime: UtilDataHora.parseStringToDate(controladorContrato.contratoVisualizar!.vigenciaDe))
                                                ? DSLib.theme == ThemeMode.dark
                                                    ? const Color(0xffF5F5F5)
                                                    : const Color(0xff3F3F3F)
                                                : DSLib.theme == ThemeMode.dark
                                                    ? const Color(0xFF424242)
                                                    : const Color(0xffDCDDDF)),
                                      )
                                    ],
                                  ),
                                ),
                                InkWell(
                                  onTap: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_CONTRATO_FERIAS).habilitado! &&
                                          !controladorContrato.isDesistente &&
                                          !controladorContrato.isVencido &&
                                          !UtilDataHora.dataJaPassouDeHojeHora0(dateTime: UtilDataHora.parseStringToDate(controladorContrato.contratoVisualizar!.vigenciaDe))
                                      ? () {
                                          Navigator.pushNamed(context, '/telaContratoFerias');
                                        }
                                      : null,
                                  child: Column(
                                    children: [
                                      DSbotaoCircular(
                                          corBordaCustomizada: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_CONTRATO_FERIAS).habilitado! &&
                                                  !controladorContrato.isDesistente &&
                                                  !controladorContrato.isVencido &&
                                                  !UtilDataHora.dataJaPassouDeHojeHora0(dateTime: UtilDataHora.parseStringToDate(controladorContrato.contratoVisualizar!.vigenciaDe))
                                              ? Theme.of(context).primaryColor
                                              : DSLib.theme == ThemeMode.dark
                                                  ? const Color(0xFF424242)
                                                  : const Color(0xffDCDDDF),
                                          categoria: Categoria.comBorda,
                                          icone: TreinoIcon.plane_fly,
                                          corIcone: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_CONTRATO_FERIAS).habilitado! &&
                                                  !controladorContrato.isDesistente &&
                                                  !controladorContrato.isVencido &&
                                                  !UtilDataHora.dataJaPassouDeHojeHora0(dateTime: UtilDataHora.parseStringToDate(controladorContrato.contratoVisualizar!.vigenciaDe))
                                              ? DSLib.theme == ThemeMode.dark
                                                  ? const Color(0xffF5F5F5)
                                                  : const Color(0xff3F3F3F)
                                              : DSLib.theme == ThemeMode.dark
                                                  ? const Color(0xFF424242)
                                                  : const Color(0xffDCDDDF)),
                                      Padding(
                                        padding: const EdgeInsets.only(top: 8),
                                        child: DStextCaption1('tela_detalhes_contrato.ferias',
                                            eHeavy: false,
                                            corCustomizada: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_CONTRATO_FERIAS).habilitado! &&
                                                    !controladorContrato.isDesistente &&
                                                    !controladorContrato.isVencido &&
                                                    !UtilDataHora.dataJaPassouDeHojeHora0(dateTime: UtilDataHora.parseStringToDate(controladorContrato.contratoVisualizar!.vigenciaDe))
                                                ? DSLib.theme == ThemeMode.dark
                                                    ? const Color(0xffF5F5F5)
                                                    : const Color(0xff3F3F3F)
                                                : DSLib.theme == ThemeMode.dark
                                                    ? const Color(0xFF424242)
                                                    : const Color(0xffDCDDDF)),
                                      )
                                    ],
                                  ),
                                ),
                                if (exibirCancelamento)
                                  InkWell(
                                    onTap: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_CONTRATO_FERIAS).habilitado! &&
                                            !controladorContrato.isDesistente &&
                                            !controladorContrato.isVencido &&
                                            !UtilDataHora.dataJaPassouDeHojeHora0(dateTime: UtilDataHora.parseStringToDate(controladorContrato.contratoVisualizar!.vigenciaDe))
                                        ? () {
                                            Navigator.pushNamed(context, '/telaCancelarContrato').then((value) {
                                              if (GetIt.I.get<ControladorContrato>().statusCancelarContrato == ServiceStatus.Done) {
                                                _refresh();
                                                _controladorVenda.consultarParcelasAtrasadas(carregando: () {}, sucesso: () {}, falha: (erro) {});
                                                _controladorContratoUsuario.resetParaContratoAtual();
                                                _controladorVenda.consultarConfigVendasOnline(
                                                  carregando: () {},
                                                  falha: (p0) {},
                                                  sucesso: () {},
                                                );
                                                _controladorVenda.consultarConfigLinkPagamente(carregando: () {}, falha: (p0) {}, sucesso: () {});
                                                _controladorContratoUsuario.consultarContratosAluno(
                                                    retornarTodos: true,
                                                    forceconsulta: true,
                                                    carregando: () {},
                                                    falha: (_) {},
                                                    sucessoLista: (contratos) {
                                                      setState(() {});
                                                    });
                                              }
                                            });
                                          }
                                        : null,
                                    child: Column(
                                      children: [
                                        DSbotaoCircular(
                                            corBordaCustomizada: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_CONTRATO_FERIAS).habilitado! &&
                                                    !controladorContrato.isDesistente &&
                                                    !controladorContrato.isVencido &&
                                                    !UtilDataHora.dataJaPassouDeHojeHora0(dateTime: UtilDataHora.parseStringToDate(controladorContrato.contratoVisualizar!.vigenciaDe))
                                                ? Colors.red
                                                : DSLib.theme == ThemeMode.dark
                                                    ? const Color(0xFF424242)
                                                    : const Color(0xffDCDDDF),
                                            categoria: Categoria.comBorda,
                                            icone: TreinoIcon.times_circle,
                                            corIcone: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_CONTRATO_FERIAS).habilitado! &&
                                                    !controladorContrato.isDesistente &&
                                                    !controladorContrato.isVencido &&
                                                    !UtilDataHora.dataJaPassouDeHojeHora0(dateTime: UtilDataHora.parseStringToDate(controladorContrato.contratoVisualizar!.vigenciaDe))
                                                ? DSLib.theme == ThemeMode.dark
                                                    ? const Color(0xffF5F5F5)
                                                    : const Color(0xff3F3F3F)
                                                : DSLib.theme == ThemeMode.dark
                                                    ? const Color(0xFF424242)
                                                    : const Color(0xffDCDDDF)),
                                        Padding(
                                          padding: const EdgeInsets.only(top: 8),
                                          child: DStextCaption1(localizedString('cancelar_contrato.titulo'),
                                              eHeavy: false,
                                              corCustomizada: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_CONTRATO_FERIAS).habilitado! &&
                                                      !controladorContrato.isDesistente &&
                                                      !controladorContrato.isVencido &&
                                                      !UtilDataHora.dataJaPassouDeHojeHora0(dateTime: UtilDataHora.parseStringToDate(controladorContrato.contratoVisualizar!.vigenciaDe))
                                                  ? DSLib.theme == ThemeMode.dark
                                                      ? const Color(0xffF5F5F5)
                                                      : const Color(0xff3F3F3F)
                                                  : DSLib.theme == ThemeMode.dark
                                                      ? const Color(0xFF424242)
                                                      : const Color(0xffDCDDDF)),
                                        )
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                            (controladorContrato.contratoVisualizar?.renovacaoautomaticasimnao ?? false)
                                ? Padding(
                                    padding: const EdgeInsets.fromLTRB(0, 24, 0, 0),
                                    child: DStextCaption1(
                                      '* ${localizedString('renovacaoAutomatica')}',
                                      eHeavy: false,
                                      ePrimario: false,
                                      textAlign: TextAlign.center,
                                    ),
                                  )
                                : Container()
                          ],
                        ),
                      ),
                    ],
                  )
          ],
        ));
  }

  Widget cardFeriasAgendadas(ControladorContratoUsuario controladorContratoUsuario) {
    return DScard(
        paddingExterno: const EdgeInsets.only(top: 32),
        paddingInterno: const EdgeInsets.all(16),
        child: Column(
          children: [
            DSsvg(imagem: Imagem.praia),
            const SizedBox(height: 16),
            DStextHeadline('ferias_agendada', eHeavy: true),
            const SizedBox(height: 8),
            controladorContratoUsuario.operacaoesContrato.isNotEmpty
                ? DStextBody(
                    localizedString('de_a', args: [
                      UtilDataHora.getDiaMesAno(formatarPorLocalidade: true, dateString: controladorContratoUsuario.operacaoesContrato.first.dataInicio),
                      UtilDataHora.getDiaMesAno(formatarPorLocalidade: true, dateString: controladorContratoUsuario.operacaoesContrato.first.dataFim)
                    ]),
                    eHeavy: false,
                    ePrimario: false,
                  )
                : Container(),
            ValidaCondicoes(
              moduloApp: ModuloApp.MODULO_ALUNO_PODE_RETORNAR_TRANCAMENTO_FERIAS,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(0, 16, 0, 0),
                child: DSbotaoPadrao(
                  titulo: localizedString('retornar_das_ferias'),
                  onTap: () {
                    controladorContratoUsuario.retornarContratoDeTrancamento(
                      carregando: () => UtilitarioApp().showDialogCarregando(context),
                      sucesso: () async {
                        Navigator.pop(context);
                        await DSalertaSucesso().exibirAlerta(context: context);
                      },
                      falha: (falha) {
                        Navigator.pop(context);
                        DSalerta().exibirAlertaSimplificado(context: context, titulo: 'pasta_contrato.pagamento_recusado_tituloMensagem', subtitulo: '${falha}', tituloBotao: 'continue');
                      },
                    );
                  },
                ),
              ),
            )
          ],
        ));
  }

  Widget cardTrancamentoAgendado(ControladorContratoUsuario controladorContratoUsuario) {
    return DScard(
        paddingExterno: const EdgeInsets.only(top: 32),
        paddingInterno: const EdgeInsets.all(16),
        child: Column(
          children: [
            DSsvg(imagem: Imagem.contrato),
            const SizedBox(height: 16),
            DStextHeadline('contrato_trancado', eHeavy: true),
            const SizedBox(height: 8),
            controladorContratoUsuario.operacaoesContrato.isNotEmpty
                ? DStextBody(
                    localizedString('de_a', args: [
                      UtilDataHora.getDiaMesAno(dateString: controladorContratoUsuario.operacaoesContrato.first.dataInicio),
                      UtilDataHora.getDiaMesAno(dateString: controladorContratoUsuario.operacaoesContrato.first.dataFim)
                    ]),
                    eHeavy: false,
                    ePrimario: false,
                  )
                : Container(),
            Padding(
              padding: const EdgeInsets.fromLTRB(0, 16, 0, 0),
              child: DSbotaoPadrao(
                titulo: localizedString('retornar_do_trancamento'),
                onTap: () {
                  controladorContratoUsuario.retornarContratoDeTrancamento(
                    carregando: () => UtilitarioApp().showDialogCarregando(context),
                    sucesso: () {
                      Navigator.pop(context);
                      _controladorContratoUsuario.consultarOsContratosDoUsuario(
                          sucesso: () async {
                            await DSalertaSucesso().exibirAlerta(context: context);
                            _controladorContratoUsuario.setarContratosDropDown();
                          },
                          falha: (falha) {});
                    },
                    falha: (falha) {
                      Navigator.pop(context);
                      DSalerta().exibirAlertaSimplificado(context: context, titulo: 'pasta_contrato.pagamento_recusado_tituloMensagem', subtitulo: '${falha}', tituloBotao: 'continue');
                    },
                  );
                },
              ),
            )
          ],
        ));
  }

  Widget cardTrancamentoNaoPodeVoltar(ControladorContratoUsuario controladorContratoUsuario) {
    return Visibility(
      visible: !controladorContratoUsuario.contratoTrancadoPodeVoltar,
      child: DScard(
          paddingExterno: const EdgeInsets.only(top: 32),
          paddingInterno: const EdgeInsets.all(16),
          child: Column(
            children: [
              DSsvg(imagem: Imagem.contrato),
              const SizedBox(height: 16),
              DStextHeadline(
                'trancamento_vencido',
                eHeavy: true,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              DStextBody(
                'o_seu_contrato_esta_com_o_trancamento_vencido',
                eHeavy: false,
                ePrimario: false,
                textAlign: TextAlign.center,
              )
            ],
          )),
    );
  }

  itemParcelasAtrasadas(ControladorContratoUsuario controladorContrato, ControladorVendaDePlano controladorVenda) {
    return DScard(
        gradiente: true,
        vaiTerCirculo: true,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(66, 16, 16, 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DStextHeadline('parcelas_vencidas', textoSobFundoGradiente: true, eHeavy: true),
              DStextCaption1(
                '${controladorContrato.parcelasAtrasadas.length.toString()} ' + localizedString('parcelas_em_atraso'),
                textoSobFundoGradiente: true,
                eHeavy: false,
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(0, 16, 0, 2),
                child: Divider(
                  color: corQuandoFundoForGradiente(context).withValues(alpha: 0.3),
                ),
              ),
              ValidaCondicoes(
                validacaoExtra: getPagamentoLiberado,
                child: DSbotaoPadrao(
                    onTap: () {
                      abrirHistoricoDeParcelas(context);
                    },
                    tipoBotao: TipoBotao.secundario,
                    tamanhoReduzido: true,
                    sobFundoGradiente: true,
                    icone: TreinoIcon.angle_right,
                    titulo: 'acessar'),
              )
            ],
          ),
        ));
  }

  itemParcelas(ControladorContratoUsuario controladorContrato, ControladorVendaDePlano controladorVenda) {
    return DScard(
        paddingInterno: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                const DSbotaoCircular(altura: 30, categoria: Categoria.secundario, icone: TreinoIcon.bill),
                const SizedBox(width: 10),
                DStextSubheadline('parcela_atual', eHeavy: true),
                Padding(
                  padding: const EdgeInsets.fromLTRB(8, 0, 8, 0),
                  child: Container(
                    height: 16,
                    width: 1,
                    color: Theme.of(context).dividerColor,
                  ),
                ),
                controladorContrato.parcelaAtualContrato != null
                    ? DStextBody('${controladorContrato.parcelaAtualContrato!.nrParcela.toString()}/${controladorContrato.numeroTotalParcelaDoContrato}', eHeavy: false)
                    : Container()
              ],
            ),
            const SizedBox(height: 16),
            controladorContrato.parcelaAtualContrato == null
                ? Column(
                    children: [
                      CardPadrao(
                        radius: 8,
                        padding: const EdgeInsets.all(16),
                        child: TextBody1('tela_detalhes_contrato._sem_parcelas'),
                      )
                    ],
                  )
                : DScard(
                    categoria: CategoriaCard.secundario,
                    paddingInterno: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            DStextCaption1(
                              localizedString('parcela_atual') +
                                  ' ${(controladorContrato.parcelaAtualContrato?.nrParcela ?? 1).toString()}/${controladorContrato.numeroTotalParcelaDoContrato}',
                              eHeavy: true,
                            ),
                            controladorContrato.parcelaAtualContrato != null
                                ? DSchip(
                                    titulo: '${controladorContrato.parcelaAtualContrato!.situacao}',
                                    tipoChip: TipoChip.terciario,
                                  )
                                : Container()
                          ],
                        ),
                        const SizedBox(height: 16),
                        campoTextoDetalhes(localizedString('valor'), '${UtilitarioApp.formatarDinheiro(controladorContrato.parcelaAtualContrato?.valor ?? 0)}'),
                        const SizedBox(height: 6),
                        campoTextoDetalhes(localizedString('validade'),
                            '${UtilDataHora.getDiaMesAno(formatarPorLocalidade: true, dateString: controladorContrato.parcelaAtualContrato?.dataVencimento ?? '')}')
                      ],
                    )),
            const Padding(
              padding: EdgeInsets.fromLTRB(0, 16, 0, 0),
              child: Divider(),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(0, 8, 0, 12),
              child: DSbotaoPadrao(
                largura: MediaQuery.of(context).size.width,
                onTap: () {
                  abrirHistoricoDeParcelas(context);
                },
                titulo: 'ver_historico_parcel',
                icone: TreinoIcon.angle_right,
                tipoBotao: TipoBotao.secundario,
              ),
            ),
          ],
        ));
  }

  boletoVencido() {
    showModalBottomSheet(
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
          return FractionallySizedBox(
            child: DScard(
              paddingExterno: const EdgeInsets.all(16),
              child: Wrap(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            const DSbotaoCircular(
                                altura: 30,
                                alturaIcone: 18,
                                corPrimarioCustomizado: Color(0xFFF8EE09),
                                corIcone: Color(0xFF011319),
                                categoria: Categoria.primario,
                                icone: TreinoIcon.exclamation_octagon),
                            const SizedBox(
                              width: 4,
                            ),
                            DStextHeadline('o_seu_boleto_esta_vencido', eHeavy: true),
                          ],
                        ),
                        DSbotaoCircular(
                            altura: 30,
                            alturaIcone: 15,
                            categoria: Categoria.secundario,
                            onTap: () {
                              GetIt.I.get<NavigationService>().goBack();
                            },
                            icone: TreinoIcon.times)
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: DStextBody('fale_com_o_seu_consultor', eHeavy: false, textAlign: TextAlign.center),
                  ),
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
                    child: DSbotaoPadrao(
                      titulo: localizedString('got_it'),
                      onTap: () {
                        GetIt.I.get<NavigationService>().goBack();
                      },
                    ),
                  ),
                ],
              ),
            ),
          );
        });
      },
    );
  }

  boletoNaoGerado() {
    showModalBottomSheet(
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
          return FractionallySizedBox(
            child: DScard(
              paddingExterno: const EdgeInsets.all(16),
              child: Wrap(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            const DSbotaoCircular(
                                altura: 30,
                                alturaIcone: 18,
                                corPrimarioCustomizado: Color(0xFFF8EE09),
                                corIcone: Color(0xFF011319),
                                categoria: Categoria.primario,
                                icone: TreinoIcon.exclamation_octagon),
                            const SizedBox(
                              width: 4,
                            ),
                            DStextHeadline('ops_boleto_nao_gerado', eHeavy: true),
                          ],
                        ),
                        DSbotaoCircular(
                            altura: 30,
                            alturaIcone: 15,
                            categoria: Categoria.secundario,
                            onTap: () {
                              GetIt.I.get<NavigationService>().goBack();
                            },
                            icone: TreinoIcon.times)
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: DStextBody('fale_com_o_seu_consultor_solicite_geracao_boleto', eHeavy: false, textAlign: TextAlign.center),
                  ),
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
                    child: DSbotaoPadrao(
                      titulo: localizedString('got_it'),
                      onTap: () {
                        GetIt.I.get<NavigationService>().goBack();
                      },
                    ),
                  ),
                ],
              ),
            ),
          );
        });
      },
    );
  }

  itemPremium(ControladorPlanner controladorPlanner) {
    return Container();
  }

  mostarMaisOpcoes(
    BuildContext parentContext,
  ) {
    showModalBottomSheet(
      context: parentContext,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return DScard(
              paddingExterno: const EdgeInsets.all(16),
              child: Wrap(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 21),
                                child: DStextHeadline(localizedString('sobre_o_contrato')),
                              ),
                            ],
                          ),
                          Positioned(
                            right: 13,
                            child: DSbotaoCircular(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              icone: Icons.close,
                              alturaIcone: 23,
                              categoria: Categoria.secundario,
                            ),
                          ),
                        ],
                      ),
                      const Divider(),
                      InkWell(
                        onTap: () async {
                          try {
                            UtilitarioApp().showDialogCarregando(context);
                            ContratoAssinatura contrato =
                                await GetIt.I.get<ControladorContratoUsuario>().consultarTermosDeContratoPorContrato(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa!);
                            Navigator.of(context).pop();
                            Navigator.of(context).pop();
                            Navigator.of(parentContext).pushNamed('/telaContratoTermos', arguments: {'precisaAssinar': false, 'dadosContrato': contrato , 'somenteLeitura': true });
                          } catch (e) {
                            Navigator.of(context).pop();
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 4),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  const Icon(TreinoIcon.file_search_alt),
                                  const SizedBox(width: 8),
                                  DStextHeadline('termos_do_contrato'),
                                ],
                              ),
                              const Padding(
                                padding: EdgeInsets.only(top: 8),
                                child: Divider(),
                              )
                            ],
                          ),
                        ),
                      ),
                      /* InkWell(
                        onTap: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_RENOVACAO_DE_CONTRATO).habilitado! &&
                                _controladorContratoUsuario.contratoVisualizar!.permiteRenovar! &&
                                !_controladorContratoUsuario.contratoVisualizar!.bolsa!
                            ? () {
                                Navigator.of(context).pop();
                                analytic(EventosKey.contrato_pressionou_renovar);
                                Navigator.pushNamed(context, '/telaRenovarContrato', arguments: _controladorCliente);
                              }
                            : null
                        ,
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 16, right: 16, top: 4, bottom: 4),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Icon(TreinoIcon.file_redo_alt ,
                                  color: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_RENOVACAO_DE_CONTRATO).habilitado! &&
                                  _controladorContratoUsuario.contratoVisualizar!.permiteRenovar! &&
                                  !_controladorContratoUsuario.contratoVisualizar!.bolsa! ? null : DSLib.theme == ThemeMode.dark ? Color(0xFF424242) : Color(0xffDCDDDF),
                                  ),
                                  SizedBox(width: 8),
                                  DStextHeadline('Renovar contrato',
                                  corCustomizada: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_RENOVACAO_DE_CONTRATO).habilitado! &&
                                  _controladorContratoUsuario.contratoVisualizar!.permiteRenovar! &&
                                  !_controladorContratoUsuario.contratoVisualizar!.bolsa! ? null : DSLib.theme == ThemeMode.dark ? Color(0xFF424242) : Color(0xffDCDDDF),
                                  ),
                                ],
                              ),
                             Padding(
                                padding: const EdgeInsets.only(top: 8),
                                child: Divider(),
                              )
                            ],
                          ),
                        ),
                      ), */
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                          abrirHistoricoDeParcelas(parentContext);
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 4),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  const Icon(TreinoIcon.file_redo_alt),
                                  const SizedBox(width: 8),
                                  DStextHeadline('historico_parcel'),
                                ],
                              ),
                              const Padding(
                                padding: EdgeInsets.only(top: 8),
                                child: Divider(),
                              )
                            ],
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget campoTextoDetalhes(String campo, String valor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        DStextCaption1(campo, eHeavy: true),
        DStextCaption1(valor, eHeavy: false),
      ],
    );
  }

  LinearPercentIndicator percentIndicator(BuildContext context, double contador) {
    return LinearPercentIndicator(
        animateFromLastPercent: true,
        animation: true,
        lineHeight: 10.0,
        percent: contador,
        linearGradient: LinearGradient(colors: [
          HSLColor.fromColor(Theme.of(context).primaryColor).withLightness(0.86).toColor(),
          Theme.of(context).primaryColor,
        ]),
        backgroundColor: const Color(0xFFDBDBDB),
        widgetIndicator: Container(
          height: 16,
          width: 16,
          decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.white, border: Border.all(width: 1, color: Theme.of(context).primaryColor)),
        ));
  }

  itemCredidoTreino() {
    return Observer(
      builder: (_) {
        switch (_controladorContratoUsuario.mStatusConsultaCreditosPermitidos) {
          case ServiceStatus.Waiting:
            return const SkeletonCardCreditoTreino();
          case ServiceStatus.Done:
            return DScard(
              paddingInterno: const EdgeInsets.all(16),
              paddingExterno: const EdgeInsets.only(bottom: 16),
              child: Column(
                children: [
                  Row(
                    children: [
                      const DSbotaoCircular(altura: 30, categoria: Categoria.secundario, icone: TreinoIcon.coins),
                      const SizedBox(width: 10),
                      DStextSubheadline('creditos_de_treino', eHeavy: true),
                    ],
                  ),
                  const SizedBox(height: 16),
                  DScard(
                    categoria: CategoriaCard.secundario,
                    paddingInterno: const EdgeInsets.all(8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          TreinoIcon.coins,
                        ),
                        const SizedBox(width: 8),
                        DStextDisplay3(
                          '${_controladorContratoUsuario.mCreditosPermitidosTransferir!.quantidadeDeCreditosPermitidos}',
                          ePrimario: false,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Divider(
                    color: Theme.of(context).dividerColor,
                  ),
                  const SizedBox(height: 8),
                  DSbotaoPadrao(
                    titulo: 'transferir_creditos_de_treino',
                    onTap: () {
                      Navigator.of(context).pushNamed('/telaTransferirCredito', arguments: [_controladorContratoUsuario.mCreditosPermitidosTransferir!]);
                    },
                  )
                ],
              ),
            );
          case ServiceStatus.Error:
            return const SizedBox();
          case ServiceStatus.Empty:
            return const SizedBox();
        }
      },
    );
  }
}

ajustarModalidades({required List<ModalidadeContrato> modalidades}) {
  var modalidadesInclusas = '';
  for (int i = 0; i < modalidades.length; i++) {
    if (i == 0) {
      modalidadesInclusas = '${UtilitarioApp.sentenseCaseFirst(modalidades[i].modalidade)}';
    } else {
      modalidadesInclusas = '${modalidadesInclusas}, ${UtilitarioApp.sentenseCaseFirst(modalidades[i].modalidade)}';
    }
  }
  return modalidadesInclusas;
}

abrirHistoricoDeParcelas(context) {
  final _controladorContratoUsuario = GetIt.I.get<ControladorContratoUsuario>();
  GetIt.I.get<ControladorVendaDePlano>().consultarConfigVendasOnline(carregando: () {}, falha: (p0) {}, sucesso: () {});
  GetIt.I.get<ControladorVendaDePlano>().consultarConfigLinkPagamente(
      carregando: () {
        UtilitarioApp().showDialogCarregando(context);
      },
      falha: (p0) {},
      sucesso: () {
        GetIt.I.get<ControladorVendaDePlano>().consultarParcelasAtrasadas(
            carregando: () {},
            sucesso: () {
              _controladorContratoUsuario.consultarOsContratosDoUsuario(sucesso: () {
                _controladorContratoUsuario.resetParaContratoAtual();
                _controladorContratoUsuario.setarContratosDropDown();
                GetIt.I.get<ControladorCliente>().consultarConveniosDePagamento(carregando: () {}, falha: (falha) {}, sucesso: () {});
                if (_controladorContratoUsuario.parcelasContratoVisualizar.isEmpty) {
                  Navigator.of(context).pop();
                  DSalerta().exibirAlertaSimplificado(
                      context: context, titulo: 'historico_parcela.histórico_parcela', subtitulo: 'historico_parcela.sem_parcela', tituloBotao: 'got_it', tipoAlerta: TipoAlerta.alerta);
                } else {
                  Navigator.of(context).popAndPushNamed('/telaHistoricoDeParcela', arguments: _controladorContratoUsuario.mTodasParcelas);
                }
              }, falha: (falha) {
                Navigator.of(context).pop();
                DSalerta().exibirAlertaSimplificado(
                    context: context, titulo: 'historico_parcela.histórico_parcela', subtitulo: 'historico_parcela.sem_parcela', tituloBotao: 'got_it', tipoAlerta: TipoAlerta.alerta);
              });
            },
            falha: (erro) {
              Navigator.of(context).pop();
              DSalerta().exibirAlertaSimplificado(
                  context: context, titulo: 'historico_parcela.histórico_parcela', subtitulo: 'historico_parcela.sem_parcela', tituloBotao: 'got_it', tipoAlerta: TipoAlerta.alerta);
            });
      });
}
