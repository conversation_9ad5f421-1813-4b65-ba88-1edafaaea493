import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/ValidaCondicoes.dart';
import 'package:app_treino/controlladores/ControladorVendaDePlano.dart';
import 'package:app_treino/screens/contrato/TelaPagamentoParcelas.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/controlladores/ControladorContratoUsuario.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/model/contrato/FiltroParcelas.dart';
import 'package:app_treino/model/contrato/ParcelaContrato.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class TelaHistoricoDeParcela extends StatefulWidget {
  TelaHistoricoDeParcela({Key? key}) : super(key: key);

  @override
  State<TelaHistoricoDeParcela> createState() => _TelaHistoricoDeParcelaState();
}

class _TelaHistoricoDeParcelaState extends State<TelaHistoricoDeParcela> {
late List<ParcelaContrato> mParcelasDoContrato = [];
final _controladorContratoUsuario = GetIt.I.get<ControladorContratoUsuario>();
final _controladorVendas = GetIt.I.get<ControladorVendaDePlano>();

final List<FiltrarParcelas> filtrarParcelas = [
    FiltrarParcelas(titulo: localizedString('all'), enumerador: EnumFiltrarParcelas.TODOS),
    FiltrarParcelas(titulo: localizedString('historico_parcela.em_aberto'), enumerador: EnumFiltrarParcelas.ABERTO),
    FiltrarParcelas(titulo: localizedString('historico_parcela.canceladas'), enumerador: EnumFiltrarParcelas.CANCELADO),
    FiltrarParcelas(titulo: localizedString('historico_parcela.pagas'), enumerador: EnumFiltrarParcelas.PAGO),
  ];

  atualizarFiltros(FiltrarParcelas selecionado) {
    _controladorContratoUsuario.filtroSelecionadoParcelas = selecionado.enumerador!;
  }

  List<ParcelaContrato> parcelasSelecionadas = [];

  @override
  Widget build(BuildContext context) {
    mParcelasDoContrato = ModalRoute.of(context)!.settings.arguments as List<ParcelaContrato>;
    mParcelasDoContrato.removeWhere((element) => (element.descricao ?? '').isEmpty);
    var mapa = mParcelasDoContrato.where((parcelas) {
    if(_controladorContratoUsuario.filtroSelecionadoParcelas == EnumFiltrarParcelas.TODOS){
      return true;
    }else if(parcelas.situacao!.contains('Aberto')){
      return _controladorContratoUsuario.filtroSelecionadoParcelas.toString().substring(20).contains('ABERTO');
    }else{
      return _controladorContratoUsuario.filtroSelecionadoParcelas.toString().substring(20).contains(parcelas.situacao!.toUpperCase());
    }
  });
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: DSappBar(
        backgroundColor: Theme.of(context).colorScheme.surface,
        titulo: localizedString('historico_parcela.histórico_parcela'),
      ),
      bottomNavigationBar: (parcelasSelecionadas.isNotEmpty && _controladorVendas.isHabilitarPagamentoParcelas) ? Padding(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DSbotaoPadrao(
              onTap: () {
              _controladorVendas.parcelasContratoSelecionados.clear();
              _controladorVendas.parcelasSelecionadasParaBoleto.clear();
              _controladorVendas.parcelasSelecionadasParaBoleto.addAll(mapa.map((parcela) => parcela.codigo!).toList());
              _controladorVendas.parcelasSelecionadas = parcelasSelecionadas.map((parcela) => parcela.codigo).join('_');
              _controladorVendas.parcelasContratoSelecionados.addAll(parcelasSelecionadas);
              Navigator.of(context).push(MaterialPageRoute(builder: (context) => const TelaPagamentoParcelas()));
              },
              titulo: '${localizedString('pasta_contrato.pagar_parcelas')}: ${parcelasSelecionadas.length}',
            ),
          ],
        ),
      ) : null,
      body: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: SizedBox(
                height: 36,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: filtrarParcelas.length,
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return celulaFiltros(index, context);
                      }, separatorBuilder: (BuildContext context, int index) { return const SizedBox(width: 8); },
                    )),
            ),
            const SizedBox(height: 24),
            mapa.isNotEmpty ?
            Column(
              children:
                mapa.map((parcela) => celulaParcela(parcela)).toList()
            ) : 
            Padding(
              padding: const EdgeInsets.only(top: 64),
              child: Center(
                child: Padding(
                        padding: const EdgeInsets.fromLTRB(32, 32, 32, 0),
                        child: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 0, bottom: 8),
                              child: DSsvg(imagem: Imagem.cartao),
                            ),
                            DStextSubheadline(localizedString('historico_parcela.sem_parcela'),textAlign: TextAlign.center,),
                            Padding(
                              padding: const EdgeInsets.only(top: 8),
                              child: DStextBody(localizedString('historico_parcela.text_sem_parcelas'), eHeavy: false, ePrimario: false, textAlign: TextAlign.center,),
                            )
                          ],
                        ),
                      )
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget celulaFiltros(int index, BuildContext context) {
    bool selecionado = filtrarParcelas[index].enumerador == _controladorContratoUsuario.filtroSelecionadoParcelas;
    return DSbotaoPadrao(
      radius: 50,
      titulo: filtrarParcelas[index].titulo!, onTap: () {
      atualizarFiltros(filtrarParcelas[index]);
        selecionado = filtrarParcelas[index].enumerador == _controladorContratoUsuario.filtroSelecionadoParcelas;
        setState(() {});
    }, filtro: true, tipoBotao: selecionado ? TipoBotao.original : TipoBotao.secundario);   
  }

  Widget celulaParcela(ParcelaContrato parcela){
    bool parcelaNaLista = parcelasSelecionadas.any((element) => element.codigo == parcela.codigo);
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
      child: InkWell(
        onTap: () {
          if ((parcela.situacao ?? '').contains('Aberto') && _controladorVendas.isHabilitarPagamentoParcelas) {
            setState(() {
            if (parcelaNaLista) {
              parcelasSelecionadas.removeWhere((element) => element.codigo == parcela.codigo);
            } else {
              parcelasSelecionadas.add(parcela);
            }
          });
          } else {
            return;
          }
        },
        child: DScard(
          paddingInterno: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded( // Permite que o conteúdo ocupe o espaço disponível sem estourar
                      child: Row(
                        children: [
                          Expanded(
                            child: DStextBody(
                              parcela.descricao!.contains('-')
                                  ? UtilitarioApp.sentenseCase(parcela.descricao!.split('-').first)
                                  : UtilitarioApp.sentenseCase(parcela.descricao),
                              eHeavy: true,
                              overflow: TextOverflow.ellipsis, // Adiciona "..." caso o texto fique muito grande
                            ),
                          ),
                          UtilDataHora.dataJaPassouDeHoje(dateTime: UtilDataHora.parseStringToDate(parcela.dataVencimento)) && (parcela.situacao ?? '').contains('Aberto') ? DSchip(
                            titulo: 'Vencida',
                            tipoChip: TipoChip.secundario,
                            posicaoIcone: PosicaoIcone.esquerda,
                            icone: TreinoIcon.exclamation_triangle,
                          ) : Container(),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            child: DSchip(
                              titulo: '${parcela.situacao}',
                              tipoChip: TipoChip.secundario,
                              posicaoIcone: PosicaoIcone.esquerda,
                              icone: iconeTipoParcela(parcela.situacao ?? ''),
                            ),
                          ),
                        ],
                      ),
                    ),
                    ValidaCondicoes(
                      apenasAluno: true,
                      validacaoExtra: (parcela.situacao ?? '').contains('Aberto') && _controladorVendas.isHabilitarPagamentoParcelas,
                      child: Container(
                        height: 24,
                        width: 24,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: parcelaNaLista ? Theme.of(context).primaryColor : null,
                          border: Border.all(
                            color: parcela.situacao == 'Pago' ? (DSLib.theme == ThemeMode.dark ? const Color(0xff202020) : const Color(0xffDCDDDF)) : Theme.of(context).primaryColor,
                            width: 2,
                          ),
                        ),
                        child: Center(
                          child: parcelaNaLista ? Icon(TreinoIcon.check, color: corQuandoFundoForGradiente(context), size: 18) : Container(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
                campoTextoDetalhes('valor', '${UtilitarioApp.formatarDinheiro(parcela.valor)}'),
                const SizedBox(height: 6),
                campoTextoDetalhes(localizedString('historico_parcela.validade'), '${UtilDataHora.getDiaMesAno(formatarPorLocalidade: true,dateString: parcela.dataVencimento)}'),
                 ],
            ),
          ),
        ),
    );
  }

  IconData iconeTipoParcela(String situacao) {
    switch (situacao){
      case 'Em Aberto':
        return TreinoIcon.dollar_sign;
      case 'Pago':
        return TreinoIcon.check;
      default:
        return TreinoIcon.dollar_sign;
    }
  }


  Widget campoTextoDetalhes(String campo, String valor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        DStextCaption1(
          campo,
          eHeavy: false
        ),
        DStextCaption1(valor, eHeavy: false),
      ],
    );
  }

  boletoVencido(){
    showModalBottomSheet(
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState){
            return FractionallySizedBox(
              child: DScard(
                paddingExterno: const EdgeInsets.all(16),
                child: Wrap(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              const DSbotaoCircular(
                                altura: 30,
                                alturaIcone: 18,
                                corPrimarioCustomizado: Color(0xFFF8EE09),
                                corIcone: Color(0xFF011319),
                                categoria: Categoria.primario,
                                icone: TreinoIcon.exclamation_octagon),
                             const SizedBox(width: 4,),
                              DStextHeadline('o_seu_boleto_esta_vencido', eHeavy: true),
                            ],
                          ),
                          DSbotaoCircular(
                            altura: 30,
                            alturaIcone: 15,
                            categoria: Categoria.secundario,
                            onTap: () {
                              GetIt.I.get<NavigationService>().goBack();
                            },
                            icone: TreinoIcon.times)
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: DStextBody('fale_com_o_seu_consultor', eHeavy: false, textAlign: TextAlign.center),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
                      child: DSbotaoPadrao(
                        titulo: localizedString('got_it'),
                        onTap: () {
                          GetIt.I.get<NavigationService>().goBack();
                        },
                      ),
                    ),
                ],
              ),
            ),
          );
        });
      },
    );
  }

  boletoNaoGerado(){
    showModalBottomSheet(
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState){
            return FractionallySizedBox(
              child: DScard(
                paddingExterno: const EdgeInsets.all(16),
                child: Wrap(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: Row(
                              children: [
                                const DSbotaoCircular(
                                  altura: 30,
                                  alturaIcone: 18,
                                  corPrimarioCustomizado: Color(0xFFF8EE09),
                                  corIcone: Color(0xFF011319),
                                  categoria: Categoria.primario,
                                  icone: TreinoIcon.exclamation_octagon),
                               const SizedBox(width: 4,),
                                DStextHeadline('ops_boleto_nao_gerado', eHeavy: true),
                              ],
                            ),
                          ),
                          DSbotaoCircular(
                            altura: 30,
                            alturaIcone: 15,
                            categoria: Categoria.secundario,
                            onTap: () {
                              GetIt.I.get<NavigationService>().goBack();
                            },
                            icone: TreinoIcon.times)
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: DStextBody('fale_com_o_seu_consultor_solicite_geracao_boleto', eHeavy: false, textAlign: TextAlign.center),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
                      child: DSbotaoPadrao(
                        titulo: localizedString('got_it'),
                        onTap: () {
                          GetIt.I.get<NavigationService>().goBack();
                        },
                      ),
                    ),
                ],
              ),
            ),
          );
        });
      },
    );
  }
}