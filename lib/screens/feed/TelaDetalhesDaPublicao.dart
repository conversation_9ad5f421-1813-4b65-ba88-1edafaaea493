import 'dart:convert';
import 'dart:typed_data';
import 'package:app_treino/config/EventosKey.dart';
import 'package:detectable_text_field/widgets/detectable_text_editing_controller.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorFeed.dart';
import 'package:app_treino/screens/transparency/TelaPermissaoNotificacoes.dart';
import 'package:detectable_text_field/detector/sample_regular_expressions.dart';
import 'package:detectable_text_field/widgets/detectable_text_field.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_barraNavegacao.dart';
import 'package:ds_pacto/ds_botaoCircular.dart';
import 'package:ds_pacto/ds_fonts.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:app_treino/Utilitario.dart';

class TelaDetalhesDaPublicacao extends StatefulWidget {
  const TelaDetalhesDaPublicacao({Key? key}) : super(key: key);

  @override
  _TelaDetalhesDaPublicacaoState createState() => _TelaDetalhesDaPublicacaoState();
}

class _TelaDetalhesDaPublicacaoState extends State<TelaDetalhesDaPublicacao> {
  final _controladorFeed = GetIt.I.get<ControladorFeed>();

  Uint8List? _imageUInt;
  late final DetectableTextEditingController myController;
  late final FocusNode inputNode;

  @override
  void initState() {
    super.initState();
    myController = DetectableTextEditingController(
      regExp: detectionRegExp(),
      detectedStyle: DStextUtil.stylebody(eHeavy: false, ePrimario: true)
    );
    inputNode = FocusNode();
  }

  @override
  void dispose() {
    myController.dispose();
    inputNode.dispose();
    super.dispose();
  }

  // to open keyboard call this function;
  void openKeyboard() {
    FocusScope.of(context).requestFocus(inputNode);
  }

  bool liberaBotaoPostagem() {
    return myController.text.isNotEmpty || _imageUInt != null;
  }

  @override
  Widget build(BuildContext context) {
    // Safely get image from route arguments
    if (_imageUInt == null) {
      final route = ModalRoute.of(context);
      if (route?.settings.arguments != null) {
        try {
          _imageUInt = route!.settings.arguments as Uint8List?;
        } catch (e) {
          // Handle casting error gracefully
          _imageUInt = null;
        }
      }
    }
    return Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        appBar: DSappBar(
          tipoAppbar: TipoAppBar.original,
          titulo: localizedString('new_post'),
          actions: [
             Center(
               child: Semantics(
                  identifier: 'botao_postar_feed',
                 child: Padding(
                        padding: const EdgeInsets.only(left: 8, right: 22),
                        child: DSbotaoCircular(
                          selecionado: !liberaBotaoPostagem() ? false : true,
                          corIcone: corQuandoFundoForGradiente(context),
                          onTap: !liberaBotaoPostagem()
                        ? null
                        : () async {
                            try {
                              TelaPermissaoNotificacoes().abrirSolicitacaoPermissao(
                                context,
                                fluxo: FluxoPermissaoNotificacoes.PUBLICOU_NO_FEED_ALUNO,
                                fechouModal: () {
                                  analytic(EventosKey.nova_publicacao_pressionou_enviar);

                                  // Safely encode image if it exists
                                  String? imageBase64;
                                  if (_imageUInt != null) {
                                    try {
                                      imageBase64 = base64Encode(_imageUInt!);
                                    } catch (e) {
                                      // Handle encoding error
                                      imageBase64 = null;
                                    }
                                  }

                                  _controladorFeed.publicarPost(
                                    myController.text,
                                    imageBase64,
                                    carregando: () {
                                      if (mounted) {
                                        UtilitarioApp().showDialogCarregando(context);
                                      }
                                    },
                                    sucesso: () {
                                      if (mounted) {
                                        Navigator.pop(context, true);
                                        Navigator.pop(context, true);
                                      }
                                    },
                                    falha: () {
                                      if (mounted) {
                                        Navigator.pop(context);
                                      }
                                    }
                                  );
                                }
                              );
                            } catch (e) {
                              // Handle any unexpected errors
                              if (mounted) {
                                DSalerta().exibirToast(context, 'Erro ao publicar post: $e', categoria: TipoAlerta.erro);
                              }
                            }
                          },
                          icone: TreinoIcon.message, alturaIcone: 20,),
                      ),
               ),
             )
          ],
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            DetectableTextField(
              keyboardAppearance: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.light ? Brightness.light : Brightness.dark,
              keyboardType: TextInputType.text,
              focusNode: inputNode,
              autofocus: true,
              onChanged: (v) {
                setState(() {});
              },
              controller: myController,
              maxLines: null,
              decoration: InputDecoration(
                border: InputBorder.none,
                contentPadding: const EdgeInsets.only(left: 10, right: 10, top: 0),
                hintStyle: DStextUtil.stylebody(eHeavy: false, ePrimario: false),
                hintText: localizedString('pensando'),
              ),      
              style: DStextUtil.stylebody(eHeavy: false, ePrimario: false),        
            ),
            Column(
              children: [
                Container(height: 1, color: Theme.of(context).dividerColor,),
                Container(
                  color: Theme.of(context).cardColor,
                  height: 90,
                  width: MediaQuery.of(context).size.width,
                  child: SafeArea(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        _imageUInt != null ? Container(width: 16,)
                        : Padding(
                          padding: const EdgeInsets.only(left: 16, right: 16),
                          child: DSbotaoCircular(
                            categoria: Categoria.comBorda,
                            alturaIcone: 22,
                            altura: 40,
                            onTap: () async {
                              try {
                                analytic(EventosKey.nova_publicacao_adicionar_foto);
                                UtilitarioApp().abrirCameraPacto(
                                  context: context,
                                  abrirComGaleria: false,
                                  onImageSelected: (image) {
                                    if (mounted && image != null) {
                                      setState(() {
                                        _imageUInt = image;
                                      });
                                    }
                                  }
                                );
                              } catch (e) {
                                if (mounted) {
                                  DSalerta().exibirToast(context, 'Erro ao abrir câmera: $e', categoria: TipoAlerta.erro);
                                }
                              }
                            },
                            icone: TreinoIcon.camera,
                          ),
                        ),
                        _imageUInt != null ? DSbotaoCircular(
                          categoria: Categoria.comBorda,
                          alturaIcone: 22,
                          altura: 40,
                          onTap: () {
                            try {
                              DSalerta().exibirAlerta(
                                context: context,
                                titulo: 'Remover foto?',
                                subtitulo: 'Tem certeza que deseja apagar a foto?',
                                tituloBotao: 'Apagar',
                                tituloBotaoSecundario: 'cancel',
                                onTap: () {
                                  if (mounted) {
                                    setState(() {
                                      _imageUInt = null;
                                    });
                                    Navigator.of(context).pop();
                                  }
                                }
                              );
                            } catch (e) {
                              if (mounted) {
                                DSalerta().exibirToast(context, 'Erro ao remover foto: $e', categoria: TipoAlerta.erro);
                              }
                            }
                          },
                          icone: TreinoIcon.image_redo,
                        ) : Semantics(
                            identifier: 'bnt_abrir_galeria',
                          child: DSbotaoCircular(
                            categoria: Categoria.comBorda,
                            alturaIcone: 22,
                            altura: 40,
                            onTap: () async {
                              try {
                                analytic(EventosKey.nova_publicacao_adicionar_foto);
                                UtilitarioApp().abrirCameraPacto(
                                  context: context,
                                  abrirComGaleria: true,
                                  onImageSelected: (image) {
                                    if (mounted && image != null) {
                                      setState(() {
                                        _imageUInt = image;
                                      });
                                    }
                                  }
                                );
                              } catch (e) {
                                if (mounted) {
                                  DSalerta().exibirToast(context, 'Erro ao abrir galeria: $e', categoria: TipoAlerta.erro);
                                }
                              }
                            },
                            icone: TreinoIcon.image,
                          ),
                        ),  
                        _imageUInt != null ? Padding(
                          padding: const EdgeInsets.fromLTRB(16, 8, 0, 0),
                          child: SizedBox(
                            width: 60, height: 60,
                            child: Stack(
                              children: [
                                Positioned(
                                  top: 5,
                                  left: 5,
                                  right: 5,
                                  bottom: 5,
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: _imageUInt != null
                                      ? Image.memory(
                                          _imageUInt!,
                                          fit: BoxFit.cover,
                                          errorBuilder: (context, error, stackTrace) {
                                            return Container(
                                              color: Colors.grey[300],
                                              child: const Icon(
                                                Icons.error,
                                                color: Colors.red,
                                              ),
                                            );
                                          },
                                        )
                                      : Container(
                                          color: Colors.grey[300],
                                          child: const Icon(Icons.image),
                                        )
                                  )),
                                /* Positioned(
                                  top: 0, right: 0,
                                  child: DSbotaoCircular(
                                    corPrimarioCustomizado: Theme.of(context).primaryColor,
                                    corIcone: Colors.white,
                                    icone: TreinoIcon.trash_alt, altura: 20,)) */
                              ],
                            ),
                          ),
                        ) : Container(width: 0),                      
                      ],
                    ),
                  ),
                ),
              ],
            )
          ],
        ));
  }
}
