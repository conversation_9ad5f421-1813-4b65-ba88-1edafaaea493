import 'package:after_layout/after_layout.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/ValidaCondicoes.dart';
import 'package:app_treino/appWidgets/componentWidgets/image_widget.dart';
import 'package:app_treino/appWidgets/geral/AvatarComBorda.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/personal/MetodosDeExecucaoEnum.dart';
import 'package:app_treino/model/personal/ProgramaFicha.dart';
import 'package:app_treino/model/personal/ProgramaTreinoPerfilAluno.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoDeTreino.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoTreino.dart';
import 'package:app_treino/controlladores/ControladorTreinoAluno.dart';
import 'package:app_treino/model/personal/Aluno.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:get_it/get_it.dart';
import 'package:skeleton_text/skeleton_text.dart';

class TelaSelecionarFicha extends StatefulWidget {
  final ProgramaTreino? programa;
  TelaSelecionarFicha({Key? key, this.programa}) : super(key: key);

  @override
  State<TelaSelecionarFicha> createState() => _TelaSelecionarFichaState();
}

class _TelaSelecionarFichaState extends State<TelaSelecionarFicha> with AfterLayoutMixin<TelaSelecionarFicha>{
  Aluno? aluno;
  final _controladorPrescricao = GetIt.I.get<ControladorPrescricaoDeTreino>();
  final controladorProgramaTreino = GetIt.I.get<ControladorPrescricaoTreino>();
  final controladorProgramaTreinoAluno = GetIt.I.get<ControladorTreinoAluno>();
  final _controlladorCliente = GetIt.I.get<ControladorCliente>();
  ProgramaTreino? programaMapeado;
  Fichas? fichaSelecionada = Fichas();
  ProgramaDeTreino? programaAtual;
  //Fichas getFichaDia = Fichas();
  Fichas fichaDoDia = Fichas();
  ProgramaTreinoPerfilAluno mPrograma = ProgramaTreinoPerfilAluno();
  bool carregando = true;
  bool erroFichaDoDia = false;

  @override
  Future<void> afterFirstLayout(BuildContext context) async {
    _controladorPrescricao.consultarProgramaTreinoPerfilAluno(idAluno: aluno?.id ?? 0, idEmpresa: _controlladorCliente.mUsuarioLogado!.codEmpresa!,
      onError: (error) {
        carregando = false;
        erroFichaDoDia = true;
        setState(() {});
      }, onLoading: () {
        carregando = true;
        setState(() {});
      }, 
      onSuccess: (programa) {
        mPrograma = programa;
        if(mPrograma.fichaAtual?.id != null) {
          for (final element in (programaMapeado!.fichas ?? [])) {
            if(element.id == mPrograma.fichaAtual?.id) {
              fichaDoDia = element;
              carregando = false;
              setState(() {});
            } else {
              carregando = false;
              setState(() {});
            }
          }
        } else {
          carregando = false;
          setState(() {});
        }
      },
    );
  }


  @override
  Widget build(BuildContext context) {
    aluno ??= (ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>)['maluno'];
    programaMapeado ??= (ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>)['programaMapeado'];
    programaAtual ??= (ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>)['programaAtual'];
    return Scaffold(      
      backgroundColor: Theme.of(context).colorScheme.surface,
      // bottomNavigationBar:Container(
      //   height: 80,
      //   color: Theme.of(context).colorScheme.surface,
      //   child: Padding(
      //     padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
      //     child: DSbotaoPadrao(              
      //       desabilitado: fichaSelecionada!.id == null,            
      //       onTap: () {
      //         if(_controladorPrescricao.habilitarExecucaoFicha(fichaSelecionada)){
      //           _controladorPrescricao.adicionarAlunoExecucao(
      //             maluno: aluno,
      //             fichaSelecionada: fichaSelecionada,
      //             sucesso: () async {
      //               fichaSelecionada = Fichas();
      //               await _controladorPrescricao.consultarAlunosEmExecucao(aluno);
      //               Navigator.popUntil(context, ModalRoute.withName('/homePageApp'));
      //               Navigator.pushNamed(context, '/telaAcompanhar', arguments: 1);
      //             },
      //             falha: () {}
      //           );
      //         }else {
      //           DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Oops!', subtitulo: 'acompanhar.ficha_sem_atividade', tituloBotao: 'continue');
      //         }
      //       },
      //         titulo: localizedString('acompanhar.selecionar_ficha'),
      //       ),
      //     ),
      // ),
      body: SafeArea(
        child: carregando ? itemCarregandoFichas(context) : 
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              color: Theme.of(context).colorScheme.surface,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    DSbotaoCircular(icone: TreinoIcon.angle_left, onTap: () {
                      Navigator.of(context).pop();
                    },),
                    SizedBox(
                      width: MediaQuery.of(context).size.width - 210,
                      child: Row(
                        children: [
                          Hero(
                            tag: aluno!.codigoCliente!,
                            child: AvatarComBorda(
                              radius: 17,
                              corDaBorda: Colors.transparent,
                              urlFotoUsuario: aluno!.imageUri ?? '',
                            ),
                          ),
                          const SizedBox(
                            width: 6,
                          ),
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                DStextHeadline(
                                  UtilitarioApp.sentenseCase(aluno!.nome),
                                  maximoLinhas: 1, overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(
                                  height: 2,
                                ),
                                DStextCaption1('${aluno!.altura != null ? aluno!.altura.toString() + 'cm' : '0 cm'} | ${aluno!.peso != null ? 
                                    aluno!.peso.toString() + ' Kg' : '0 Kg'} | ${localizedString('argument_years_old', args: [aluno!.idade.toString()])}', eHeavy: false, ePrimario: false,),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  Container(width: 45,)
                  ],
                ),
              ),
            ),
            // Seção Treino do Dia
            fichaDoDia.id != null ?
            Container(
              color: Theme.of(context).colorScheme.surface,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
                    child: Row(
                      children: [
                        DStextSubheadline('treino_dia', textAlign: TextAlign.start),
                      ],
                    ),
                  ),
                  erroFichaDoDia ?
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
                    child: itemErro(),
                  ) :
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
                    child: InkWell(
                    onTap: () {
                      DSBottomSheet().exibirAlerta(context, 'Atividades', 
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              (fichaDoDia.atividades ?? []).isEmpty ? DStextSubheadline('Sem atividades', eHeavy: false,) :
                              SizedBox(
                                height:  MediaQuery.of(context).size.height * 0.65,
                                child: ListView.separated(
                                  shrinkWrap: true,
                                  itemCount: (fichaDoDia.atividades ?? []).length,
                                  separatorBuilder: (_, __) => const Divider(),
                                  itemBuilder: (_, i){
                                    return InkWell(
                                      onTap: () {
                                        fichaDoDia.atividades?[i].aparelhos?.clear();
                                        for (final element in (programaAtual?.programaFichas ?? [])) {
                                          if(element.ficha == fichaDoDia.id) {
                                            element.atividadesFicha.forEach((element) {
                                              if(element.atividade == fichaDoDia.atividades?[i].atividade?.id) {
                                                fichaDoDia.atividades?[i].aparelhos = (element.aparelhos as List<dynamic>).cast<String>();
                                              }
                                            });
                                          }
                                        }
                                        Navigator.pop(context);
                                        Navigator.of(context).pushNamed('/telaTreinoDetalheAtividadeAcompanhar', arguments: {'dadosAtividade': fichaDoDia.atividades?[i]});
                                      },
                                      child: DScard(
                                        categoria: CategoriaCard.secundario,
                                        paddingInterno: const EdgeInsets.all(16),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            DStextSubheadline(UtilitarioApp.sentenseCase(fichaDoDia.atividades?[i].atividade?.nome ?? '')),
                                            const SizedBox(height: 8),
                                            DStextBody(UtilitarioApp.sentenseCase(fichaDoDia.atividades?[i].atividade?.tipo ?? ''), eHeavy: false, textAlign: TextAlign.start,)
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        )
                      );
                    },
                    child: DScard(
                      child:  Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ImageWidget(guardarImagemEmCache: true,
                          imageUrl: controladorProgramaTreinoAluno.getImagemPadraoFichaPelaAtividade(encontrarNomeAtividadeFicha(ficha: fichaDoDia)),
                          height: 116,
                          fit: BoxFit.cover,
                          width: 90,
                        ),
                          Padding(
                            padding: const EdgeInsets.only(left: 16, top: 16, bottom: 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    SizedBox(
                                      width: MediaQuery.of(context).size.width - 208,
                                      child: DStextSubheadline(UtilitarioApp.sentenseCaseFirst(fichaDoDia.nome ?? '')!, overflow: TextOverflow.ellipsis,)
                                    ),
                                    DSbotaoPadrao(titulo: localizedString('treinar'),
                                      tamanhoReduzido: true,
                                      tipoBotao: TipoBotao.sublinhado,
                                      onTap: () {
                                        DSalerta().exibirAlerta(context: context, 
                                        tipoAlerta: TipoAlerta.sucesso,
                                        titulo: 'iniciar_treino', subtitulo: 'Inicíar o treino ${UtilitarioApp.sentenseCaseFirst(fichaDoDia.nome ?? '')} agora?', tituloBotao: 'iniciar', tituloBotaoSecundario: 'cancel', 
                                          onTap: () {
                                            if(_controladorPrescricao.habilitarExecucaoFicha(fichaDoDia)){
                                              fichaDoDia.idPrograma = (programaMapeado?.id ?? 0).toInt();
                                            _controladorPrescricao.adicionarAlunoExecucao(
                                              maluno: aluno,
                                              fichaSelecionada: fichaDoDia,
                                              programaFicha: (programaAtual?.programaFichas ?? []).firstWhere((test) => test.ficha == fichaDoDia.id, orElse: () => ProgramaFicha()),
                                              sucesso: () async {
                                                await _controladorPrescricao.consultarAlunosEmExecucao(aluno);
                                                  if(GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_ACOMPANHAR_GERAR_ACESSO).habilitado!) {
                                                    await _controladorPrescricao.registrarAcessoAluno(maluno: aluno!, carregando: () {}, sucesso: (alunoRetorno) {
                                                      _controladorPrescricao.contabilizarAcompanharWeb(id: alunoRetorno.id ?? 0, sucesso: () {}, falha: () {});
                                                    });
                                                  } else {
                                                    _controladorPrescricao.contabilizarAcompanharWeb(id: aluno?.id ?? 0, sucesso: () {}, falha: () {});
                                                  }
                                                Navigator.popUntil(context, ModalRoute.withName('/homePageApp'));
                                                Navigator.pushNamed(context, '/telaAcompanhar', arguments: 1);
                                              },
                                              falha: () {}
                                            );
                                          }else {
                                            DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Oops!', subtitulo: 'acompanhar.ficha_sem_atividade', tituloBotao: 'continue');
                                          }
                                        }
                                      );
                                    },
                                  ),
                                  ],
                                ),
                                ValidaCondicoes(
                                  validacaoExtra: ultimaExecucao(fichaDoDia).isNotEmpty,
                                  child: Padding(
                                    padding: const EdgeInsets.fromLTRB(0, 8, 0, 0),
                                    child: DSchip(titulo: '${localizedString('ultima_execucao')} ${ultimaExecucao(fichaDoDia)}', posicaoIcone: PosicaoIcone.esquerda, tipoChip: TipoChip.secundario,),
                                  ),
                                  childFalse: Padding(
                                    padding: const EdgeInsets.fromLTRB(0, 8, 0, 0),
                                    child: DSchip(titulo: localizedString('ficha_sem_execucao'), posicaoIcone: PosicaoIcone.esquerda, tipoChip: TipoChip.secundario,),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(top: 8),
                                  child: DSchip(titulo: UtilitarioApp.sentenseCaseFirst(Metodosxecucao.obterMetodoPorNome(_controladorPrescricao.categoria(fichaDoDia.categoria?.id != null ?
                                    fichaDoDia.categoria!.id!.toInt() : 0))!)!, tipoChip: TipoChip.secundario
                                  ),
                                ),
                              ],
                            ),
                          ),  
                          //const Spacer(),
                        //const SizedBox(width: 16)         
                        ],
                      ),
                    ),
                    ),
                  ),
                ],
              ),
            ) : Container(),
            // Divisor visual entre seções
            Container(
              height: 8,
              color: Theme.of(context).dividerColor.withValues(alpha: 0.1),
            ),

            // Seção Todas as Fichas
            Expanded(
              child: Container(
                color: Theme.of(context).colorScheme.surface,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                      child: Row(
                        children: [
                          DStextSubheadline('Todas as fichas', textAlign: TextAlign.start),
                          const Spacer(),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: DStextCaption1(
                              '${programaMapeado!.fichas!.length} fichas',
                              ePrimario: true,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Observer(builder: (_) {
                          return ListView.builder(
                            physics: const NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            itemCount: programaMapeado!.fichas!.length,
                            itemBuilder: (_, index){
                              return Padding(
                                padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                                child: celulaFichas(index, context),
                              );
                            });
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget celulaFichas(int index, BuildContext context){
    return InkWell(
      onTap: () {
        DSBottomSheet().exibirAlerta(context, 'Atividades', 
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                (programaMapeado?.fichas?[index].atividades ?? []).isEmpty ? DStextSubheadline('Sem atividades', eHeavy: false,) :
                SizedBox(
                  height:  MediaQuery.of(context).size.height * 0.65,
                  child: ListView.separated(
                    shrinkWrap: true,
                    itemCount: (programaMapeado?.fichas?[index].atividades ?? []).length,
                    separatorBuilder: (_, __) => const Divider(),
                    itemBuilder: (_, i){
                      return InkWell(
                        onTap: () {
                          //programaMapeado?.fichas?[index].atividades?[i].aparelhos?.clear();
                          for (final element in (programaAtual?.programaFichas ?? [])) {
                            if(element.ficha == programaMapeado?.fichas?[index].id) {
                              element.atividadesFicha.forEach((element) {
                                if(element.atividade == programaMapeado?.fichas?[index].atividades?[i].atividade?.id) {
                                  programaMapeado?.fichas?[index].atividades?[i].aparelhos = (element.aparelhos as List<dynamic>).cast<String>();
                                }
                              });
                            }
                          }
                          Navigator.pop(context);
                          Navigator.of(context).pushNamed('/telaTreinoDetalheAtividadeAcompanhar', arguments: {'dadosAtividade': programaMapeado?.fichas?[index].atividades?[i]});
                        },
                        child: DScard(
                          categoria: CategoriaCard.secundario,
                          paddingInterno: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              DStextSubheadline(UtilitarioApp.sentenseCase(programaMapeado?.fichas?[index].atividades?[i].atividade?.nome ?? '')),
                              const SizedBox(height: 8),
                              DStextBody(UtilitarioApp.sentenseCase(programaMapeado?.fichas?[index].atividades?[i].atividade?.tipo ?? ''), eHeavy: false, textAlign: TextAlign.start,)
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          )
        );
      },
      child: DScard(
        child:  Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ImageWidget(guardarImagemEmCache: true,
            imageUrl: controladorProgramaTreinoAluno.getImagemPadraoFichaPelaAtividade(encontrarNomeAtividadeFicha(ficha: programaMapeado!.fichas![index])),
            height: 116,
            fit: BoxFit.cover,
            width: 90,
          ),
            Padding(
              padding: const EdgeInsets.only(left: 16, top: 16, bottom: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width - 208,
                        child: DStextSubheadline(UtilitarioApp.sentenseCaseFirst(programaMapeado!.fichas![index].nome ?? '')!, overflow: TextOverflow.ellipsis)
                      ),
                      DSbotaoPadrao(titulo: localizedString('treinar'),
                        tamanhoReduzido: true,
                        tipoBotao: TipoBotao.sublinhado,
                        onTap: () {
                          DSalerta().exibirAlerta(context: context, 
                          tipoAlerta: TipoAlerta.sucesso,
                          titulo: 'iniciar_treino', subtitulo: 'Inicíar o treino ${UtilitarioApp.sentenseCaseFirst(programaMapeado!.fichas![index].nome ?? '')} agora?', tituloBotao: 'iniciar', tituloBotaoSecundario: 'cancel', 
                              onTap: () {
                                if(_controladorPrescricao.habilitarExecucaoFicha(programaMapeado!.fichas![index])){
                                  var ficha = programaMapeado!.fichas![index];
                                  ficha.idPrograma = programaMapeado!.id;
                                _controladorPrescricao.adicionarAlunoExecucao(
                                  maluno: aluno,
                                  fichaSelecionada: ficha,
                                  programaFicha: (programaAtual?.programaFichas ?? []).firstWhere((test) => test.ficha == ficha.id, orElse: () => ProgramaFicha()),
                                  sucesso: () async {
                                    await _controladorPrescricao.consultarAlunosEmExecucao(aluno);
                                    if(GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_ACOMPANHAR_GERAR_ACESSO).habilitado!) {
                                      await _controladorPrescricao.registrarAcessoAluno(maluno: aluno!, carregando: () {}, sucesso: (alunoRetorno) {
                                        _controladorPrescricao.contabilizarAcompanharWeb(id: alunoRetorno.id ?? 0, sucesso: () {}, falha: () {});
                                      });
                                    } else {
                                      _controladorPrescricao.contabilizarAcompanharWeb(id: aluno?.id ?? 0, sucesso: () {}, falha: () {});
                                    }
                                    Navigator.popUntil(context, ModalRoute.withName('/homePageApp'));
                                    Navigator.pushNamed(context, '/telaAcompanhar', arguments: 1);
                                  },
                                  falha: () {}
                                );
                              }else {
                                DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Oops!', subtitulo: 'acompanhar.ficha_sem_atividade', tituloBotao: 'continue');
                              }
                            }
                          );
                        },
                      ),
                    ],
                  ),
                  ValidaCondicoes(
                    validacaoExtra: ultimaExecucao(programaMapeado!.fichas![index]).isNotEmpty,
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(0, 8, 0, 0),
                      child: Column(
                        children: [
                          DSchip(titulo: '${localizedString('ultima_execucao')} ${ultimaExecucao(programaMapeado!.fichas![index])}', posicaoIcone: PosicaoIcone.esquerda, tipoChip: TipoChip.secundario,),
                        ],
                      ),
                    ),
                    childFalse: Padding(
                      padding: const EdgeInsets.fromLTRB(0, 8, 0, 0),
                      child: Column(
                        children: [
                          DSchip(titulo: localizedString('ficha_sem_execucao'), posicaoIcone: PosicaoIcone.esquerda, tipoChip: TipoChip.secundario,),
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: DSchip(titulo: UtilitarioApp.sentenseCaseFirst(Metodosxecucao.obterMetodoPorNome(_controladorPrescricao.categoria(programaMapeado!.fichas![index].categoria!.id != null ?
                      programaMapeado!.fichas![index].categoria!.id!.toInt() : 0))!)!, tipoChip: TipoChip.secundario,
                    ),
                  ),
                ],
              ),
            ),       
            ],
          ),
        ),
    );
  }

  String encontrarNomeAtividadeFicha({required Fichas ficha}) {
   if (ficha.atividades == null) {
      return '';
   } else if (ficha.atividades!.isEmpty) {
     return '';
   } else  if (ficha.atividades!.first.atividade == null) {
     return '';
   } else {
     return ficha.atividades!.first.atividade!.nome ?? '';
   }
 }

 String ultimaExecucao(Fichas ficha) {
    String valor = '';
    if((_controladorPrescricao.mProgramaAluno?.programaFichas ?? []).isNotEmpty) {
      _controladorPrescricao.mProgramaAluno?.programaFichas?.forEach((element) {
        if(element.nomeFicha == ficha.nome) {
          valor = UtilDataHora.getDiaMes(dataMilis: element.ultimaExecucao?.toInt());
        }
     });
    }
    if(valor.contains('--')) valor = '';
    return valor;
  }

  Widget itemCarregandoFichas(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          color: Theme.of(context).colorScheme.surface,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                DSbotaoCircular(icone: TreinoIcon.angle_left, onTap: () {
                  Navigator.of(context).pop();
                },),
                SizedBox(
                  width: MediaQuery.of(context).size.width - 210,
                  child: Row(
                    children: [
                      Container(
                        height: 40,
                        width: 40,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20)
                        ),
                        child: SizedBox(
                          height: 40,
                          width: 40,
                          child: SkeletonAnimation(
                            shimmerColor:
                              Theme.of(context).dividerColor,
                            borderRadius: BorderRadius.circular(10),
                            shimmerDuration: 1000,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).canvasColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 6,
                      ),
                      Flexible(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: 16,
                              width: 160,
                              child: SkeletonAnimation(
                                shimmerColor:
                                  Theme.of(context).dividerColor,
                                borderRadius: BorderRadius.circular(10),
                                shimmerDuration: 1000,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).canvasColor,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 2,
                            ),
                            SizedBox(
                              height: 14,
                              width: 218,
                              child: SkeletonAnimation(
                                shimmerColor:
                                  Theme.of(context).dividerColor,
                                borderRadius: BorderRadius.circular(10),
                                shimmerDuration: 1000,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).canvasColor,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              Container(width: 45,)
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
          child: Row(
            children: [
              DStextSubheadline('treino_dia', textAlign: TextAlign.start),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
          child: Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
            child: SizedBox(
              height: 120,
              width: MediaQuery.of(context).size.width - 32,
              child: SkeletonAnimation(
                shimmerColor:
                  Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(10),
                shimmerDuration: 1000,
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).canvasColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
          )
        ),
        // Divisor visual entre seções
        Container(
          height: 8,
          color: Theme.of(context).dividerColor.withValues(alpha: 0.1),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
          child: Row(
            children: [
              DStextSubheadline('Todas as fichas', textAlign: TextAlign.start),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
            child: SizedBox(
              height: 120,
              width: MediaQuery.of(context).size.width - 32,
              child: SkeletonAnimation(
                shimmerColor:
                  Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(10),
                shimmerDuration: 1000,
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).canvasColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
          )
        ),
      ],
    );
  }

  Widget itemErro() {
    return DScard(
      child: SizedBox(
        height: 120,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 32,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 12),
            DStextSubheadline('Ops!', textAlign: TextAlign.center),
            const SizedBox(height: 4),
            DStextBody('Erro ao consultar a ficha do dia!', eHeavy: false, textAlign: TextAlign.center)
          ],
        ),
      ),
    );
  }

  String getTipoAtividade(num codigo) {
    switch (codigo) {
      case 0:
        return 'Aeróbico';
      case 1:
        return 'Anaeróbico';
      default:
        return '';
    }
  }
}