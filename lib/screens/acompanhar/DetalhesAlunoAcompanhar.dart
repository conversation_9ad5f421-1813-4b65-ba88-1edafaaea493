import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/skeletons/SkeletonCardHistoricoExecucoes.dart';
import 'package:app_treino/appWidgets/componentWidgets/skeletons/SkeletonCardObservacao.dart';
import 'package:app_treino/appWidgets/geral/AvatarComBorda.dart';
import 'package:app_treino/screens/prescricaoDeTreino/widgets/ListViewObservacoes.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoDeTreino.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoTreino.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/personal/Aluno.dart';
import 'package:app_treino/screens/prescricaoDeTreino/widgets/EmptyObservacoesCard.dart';
import 'package:app_treino/screens/prescricaoDeTreino/widgets/ItemProgramaDeTreinoDoAluno.dart';
import 'package:app_treino/screens/prescricaoDeTreino/widgets/ItemProgramaDeTreinoVencido.dart';
import 'package:app_treino/screens/prescricaoDeTreino/widgets/ItemSemPrograma.dart';
import 'package:app_treino/screens/prescricaoDeTreino/widgets/ListViewHistoricoAtividades.dart';
import 'package:app_treino/screens/prescricaoDeTreino/widgets/SkeletonProgramaTreino.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:get_it/get_it.dart';
import 'package:skeleton_text/skeleton_text.dart';

class DetalhesAlunoAcompanhar extends StatefulWidget {
  DetalhesAlunoAcompanhar({Key? key}) : super(key: key);

  @override
  State<DetalhesAlunoAcompanhar> createState() => _DetalhesAlunoAcompanharState();
}

class _DetalhesAlunoAcompanharState extends State<DetalhesAlunoAcompanhar> {
  Aluno? aluno;
  final _controladorPrescricao = GetIt.I.get<ControladorPrescricaoDeTreino>();
  final controladorProgramaTreino = GetIt.I.get<ControladorPrescricaoTreino>();
  ProgramaTreino? programaMapeado;
  ProgramaTreino? programaAtual;

  @override
  void initState() {
    _controladorPrescricao.consultarObservacoesAluno(codigoMatricula: _controladorPrescricao.alunoExibir!.matriculaZW!, 
    onLoading: () {
      _controladorPrescricao.statusCardObservacoesAluno = ServiceStatus.Waiting;
    },
    onSuccess: () {
      _controladorPrescricao.checkIfCanBuildCardObservacoes();
    },
    onError: (error) {});
    _controladorPrescricao.consultarAvaliacaoFisicaRecente(idAluno: _controladorPrescricao.alunoExibir!.id!, 
    onError: (error) {},
    onLoading: () {
      _controladorPrescricao.statusCardAvaliacaoFisica = ServiceStatus.Waiting;
    },
    onSuccess: () {
      _controladorPrescricao.checkIfCanBuildCardAvaliacaoFisica();
    });
    _controladorPrescricao.consultarDetalhesDoAluno(idAluno: _controladorPrescricao.alunoExibir!.id!);
    _controladorPrescricao.consultarHistoricoDeExecucoesAluno(idAluno: _controladorPrescricao.alunoExibir!.id!);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    aluno = (ModalRoute.of(context)!.settings.arguments as Aluno);
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,      
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  DSbotaoCircular(icone: TreinoIcon.angle_left, onTap: () {
                    Navigator.of(context).pop();
                  },),
                  SizedBox(
                    width: MediaQuery.of(context).size.width - 168,
                    child: Row(
                      children: [
                        Hero(
                          tag: aluno!.codigoCliente!,
                          child: AvatarComBorda(
                            radius: 17,
                            corDaBorda: Colors.transparent,
                            urlFotoUsuario: aluno!.imageUri ?? '',
                          ),
                        ),
                        const SizedBox(
                          width: 6,
                        ),
                        Flexible(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              DStextHeadline(
                                UtilitarioApp.sentenseCase(aluno!.nome),
                                maximoLinhas: 1, overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(
                                height: 2,
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Observer(builder: (_) {
                                    if(_controladorPrescricao.statusCardAvaliacaoFisica == ServiceStatus.Done || _controladorPrescricao.statusCardAvaliacaoFisica == ServiceStatus.Empty){
                                      return Row(
                                      children: [
                                        DStextCaption1(
                                          '${formatAltura(((_controladorPrescricao.avaliacaoRecenteAlunoExibir?.alunoBI?.altura ?? 0) * 100).round().toString()) + ' cm'}',
                                          eHeavy: false,
                                          ePrimario: false,
                                        ),
                                      ],
                                    );
                                    } else {
                                      return SkeletonAnimation(
                                        shimmerColor: Theme.of(context).dividerColor,
                                        borderRadius: BorderRadius.circular(50),
                                        shimmerDuration: 1000,
                                        child: Container(
                                          width: 40,
                                          height: 18,
                                          decoration: BoxDecoration(
                                            color: Theme.of(context).canvasColor,
                                            borderRadius: BorderRadius.circular(50),
                                          ),
                                        ),
                                      );
                                    }
                                    },
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(right: 4, left: 4),
                                    child: Container(
                                      height: 20,
                                      width: 1,
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).dividerColor
                                      ),
                                    ),
                                  ),
                                  Observer(builder: (_) {
                                    if(_controladorPrescricao.statusCardAvaliacaoFisica == ServiceStatus.Done || _controladorPrescricao.statusCardAvaliacaoFisica == ServiceStatus.Empty){
                                      return Row(
                                      children: [
                                        DStextCaption1(
                                          '${(_controladorPrescricao.avaliacaoRecenteAlunoExibir?.alunoBI?.peso ?? 0).toString() + ' kg'}',
                                          eHeavy: false,
                                          ePrimario: false,
                                        ),
                                      ],
                                    );
                                    } else {
                                      return SkeletonAnimation(
                                        shimmerColor: Theme.of(context).dividerColor,
                                        borderRadius: BorderRadius.circular(50),
                                        shimmerDuration: 1000,
                                        child: Container(
                                          width: 40,
                                          height: 18,
                                          decoration: BoxDecoration(
                                            color: Theme.of(context).canvasColor,
                                            borderRadius: BorderRadius.circular(50),
                                          ),
                                        ),
                                      );
                                    }
                                    },
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(right: 4, left: 4),
                                    child: Container(
                                      height: 20,
                                      width: 1,
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).dividerColor
                                      ),
                                    ),
                                  ),
                                  Row(
                                    children: [
                                      DStextCaption1(
                                        localizedString('tela_perfil.idade', args: ['${(_controladorPrescricao.alunoExibir?.idade ?? 0).toString()}']),
                                        eHeavy: false,
                                        ePrimario: false,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                Container(width: 35)
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Observer(key: const Key('TelaDetalhesDoAluno7'), builder: (_) {
                      switch (_controladorPrescricao.statusConsultaDetalhesDoAluno) {
                        case ServiceStatus.Done:
                          if (_controladorPrescricao.isProgramaMaisRecenteVencido()) {
                            return ItemProgramaDeTreinoVencido(programaDeTreinoVencido: _controladorPrescricao.programaMaisRecenteAlunoExibir(),);
                          } else {
                            return ItemProgramaDeTreinoDoAluno(programaDeTreino: _controladorPrescricao.programaAtualAlunoExibir() ?? _controladorPrescricao.programaMaisRecenteAlunoExibir(),);
                          }
                        case ServiceStatus.Empty:
                          return ItemSemPrograma();
                        case ServiceStatus.Waiting:
                          return const SkeletonProgramaTreino();
                        default:
                          return const SkeletonProgramaTreino();
                      }
                    }),
                    /* Padding(
                      padding: const EdgeInsets.fromLTRB(16, 32, 16, 0),
                      child: ItemCardProgramaResumido(programaDeTreino: _controladorPrescricao.programaAtualAlunoExibir() ?? _controladorPrescricao.programaMaisRecenteAlunoExibir(),),
                    ), */
                    Observer(key: const Key('TelaDetalhesDoAluno6'), builder: (_) {
                      switch (_controladorPrescricao.statusConsultaHistoricoAluno) {
                        case ServiceStatus.Done:
                          return ListViewHistoricoAtividades(historicoExecucoesAluno: _controladorPrescricao.historicoExecucoesAluno, veioDoAcompanhar: true, idAluno: _controladorPrescricao.alunoExibir?.matriculaZW?.toInt() ?? 0);
                        case ServiceStatus.Waiting:
                          return const SkeletonCardHistoricoExecucoes();
                        default:
                          return Container();
                      }
                    }),
                    Observer(key: const Key('TelaDetalhesDoAluno5'), builder: (_) {
                      switch (_controladorPrescricao.statusCardObservacoesAluno) {
                        case ServiceStatus.Done:
                          return ListViewObservacoes(listObservacoes: _controladorPrescricao.listObservacoes, aluno: _controladorPrescricao.alunoExibir!,);
                        case ServiceStatus.Waiting:
                          return const SkeletonCardObservacao();
                        case ServiceStatus.Empty:
                          return EmptyObservacoesCard(aluno: _controladorPrescricao.alunoExibir!,);
                        default:
                          return EmptyObservacoesCard(aluno: _controladorPrescricao.alunoExibir!,);
                      }
                    }),
                    const SizedBox(height: 32)
                  ],
                ),
              ),
            )
          ],
        ),
      ),
       bottomNavigationBar: Container(
        height: 80,
        color: Theme.of(context).colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
          child: Observer(builder: (_) {
             return DSbotaoPadrao(  
            desabilitado: _controladorPrescricao.statusConsultaDetalhesDoAluno == ServiceStatus.Empty,            
            onTap: () {
              if(_controladorPrescricao.habilitarExecucao(aluno)){
                controladorProgramaTreino.consultarProgramaTreinoAluno(
                  sucesso: (value) {
                    programaAtual = value;
                  }, 
                  falha: () {}, 
                  carregando: () {}, 
                  codigoPrograma: _controladorPrescricao.programaAtualAlunoExibir()!.id!
                );
                controladorProgramaTreino.consultarAlunoDetalhado(sucesso: (r) async {
                  Navigator.of(context).pop();
                  setState(() {
                    programaMapeado = r;
                    aluno!.programas!.add(programaMapeado!);
                  });
                  _controladorPrescricao.consultarProgramaDeTreinoDoAluno(carregando: () {}, sucesso: () {
                    if(programaAtual?.fichas?.isNotEmpty ?? false) {
                      aluno?.altura = ((_controladorPrescricao.avaliacaoRecenteAlunoExibir?.alunoBI?.altura ?? 0) * 100).round();
                      aluno?.peso = (_controladorPrescricao.avaliacaoRecenteAlunoExibir?.alunoBI?.peso ?? 0);
                      Navigator.pushNamed(context, '/telaSelecionarFicha', arguments: {'maluno': aluno, 'programaMapeado': programaAtual, 'programaAtual': _controladorPrescricao.mProgramaAluno});
                    }else {
                      DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Oops!', subtitulo: 'acompanhar.sem_ficha', tituloBotao: 'continue');
                    }
                  }, falha: (v) {});
                }, falha: () {
                  Navigator.of(context).pop();
                  setState(() {
                    programaMapeado = ProgramaTreino();
                  });
                  DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Oops!', subtitulo: 'acompanhar.aluno_sem_programa', tituloBotao: 'continue');                 
                }, semPrograma: () {
                  Navigator.of(context).pop();
                  setState(() {
                    programaMapeado = ProgramaTreino();
                  });
                  DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Oops!', subtitulo: 'acompanhar.aluno_sem_programa', tituloBotao: 'continue');
                }, carregando: () {
                  UtilitarioApp().showDialogCarregando(context);
                  setState(() {});
                }, idAluno: aluno!.id!.toInt());
              } else {
                DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Oops!', subtitulo: 'acompanhar.aluno_com_treino_execucao', tituloBotao: 'continue');
              }
            },
              titulo: localizedString('acompanhar.executar_treino'),
            );
          },
          )
          ),
      ),
    );
  }

  formatAltura(String altura) {
    switch (altura.length) {
      case 1:
        return altura;
      case 2:
        return altura.substring(0, 2);
      case 3:
        return altura.substring(0, 3);
      default:
        return altura.substring(0, 3);
    }
  }
}