// ignore_for_file: deprecated_member_use, unused_import

import 'dart:convert';
import 'dart:io';

import 'package:after_layout/after_layout.dart';
import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:app_treino/ServiceProvider/StuckTrace.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/controlladores/ControladorAgendamento.dart';
import 'package:app_treino/controlladores/ControladorAppLoading.dart';
import 'package:app_treino/controlladores/ControladorAvaliacaoFisica.dart';
import 'package:app_treino/controlladores/ControladorConfiguracao.dart';
import 'package:app_treino/controlladores/ControladorContratoUsuario.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorAulaTurma.dart';
import 'package:app_treino/controlladores/ControladorBeberAgua.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorEventBus.dart';
import 'package:app_treino/controlladores/ControladorExecucaoTreino.dart';
import 'package:app_treino/controlladores/ControladorNotificacoes.dart';
import 'package:app_treino/controlladores/ControladorSplash.dart';
import 'package:app_treino/controlladores/ControladorTreinoAluno.dart';
import 'package:app_treino/controlladores/ControladorTreinosPreDefinidos.dart';
import 'package:app_treino/controlladores/ControladorUsuarioApp.dart';
import 'package:app_treino/controlladores/ControladorWod.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/Usuario.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoWidgetExecucao.dart';
import 'package:app_treino/screens/_treino6/novo_chat/chat_controller.dart';
import 'package:app_treino/screens/configuracoes/TelaConfiguracoes.dart';
import 'package:app_treino/screens/novoCross/novo_cronometro/ControladorCronometro.dart';
import 'package:app_treino/screens/novoCross/novo_cronometro/ControladorCronometroPadrao.dart';
import 'package:app_treino/screens/novoCross/novo_cronometro/CronometroHome.dart';
import 'package:app_treino/screens/vitio/models/vitioModels.dart';
import 'package:app_treino/util/notificacao_app_aberto.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
// import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:lottie/lottie.dart';
import 'package:shake/shake.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HomePage extends StatefulWidget {
  HomePage({Key? key, this.title}) : super(key: key);

  final String? title;

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with AfterLayoutMixin<HomePage>, WidgetsBindingObserver {
  final GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey<ScaffoldState>();
  final _controladorContrato = GetIt.I.get<ControladorContratoUsuario>();
  final _mCliente = GetIt.I.get<ControladorCliente>();
  ControladorApp controllerApp = GetIt.I.get<ControladorApp>();
  final _controladorCronometro = GetIt.I.get<ControladorCronometro>();

  void Function(void Function())? setStateTale;
  BuildContext? contextScafold;

  void passouDataConsultaEmpresa(int data) async {
    SharedPreferences dataConsultarEmpresas = await SharedPreferences.getInstance();
    await dataConsultarEmpresas.setInt('data_consulta_listaEmpresa', data);
  }

  var shakeEnabled = true;

  //var _counter;

  @override
  void initState() {
    StuckTrace().isInHome = true;
    WidgetsBinding.instance.addObserver(this);
    GetIt.I.get<ControladorConfiguracao>().consultarAgiteParaReportar();
    GetIt.I.get<ControladorExecucaoTreino>().iniciarComunicacaoEntreAppleWatchEApp(context);
    GetIt.I.get<ControladorApp>().consultarConfiguracoesTreinoWeb(() {}, (x) {}, isHome: true);
    // CONSULTAS ESPECIFICAS PARA COLABORADOR
    if (GetIt.I.get<ControladorCliente>().isUsuarioColaborador) {
      GetIt.I.get<ControladorSplash>().validarTokenColaborador(sucesso: () {}, falha: () {});
      GetIt.I.get<ControladorAulaTurma>().consultarAulasTurmasProfessor(carregando: () {}, falha: (value) {}, sucesso: () {});
      // CONSULTAS ESPECIFICAS PARA ALUNO
    } else {
      GetIt.I.get<ControladorCliente>().consultarSituacaoAlunoAPP(
          carregando: () {},
          sucesso: () {
            verificarClienteParaModalDeCompra(context);
          },
          falha: (value) {});
      _controladorContrato.consultarOsContratosDoUsuario(carregando: () {}, falha: (error) {}, sucesso: () {});
      //   GetIt.I.get<ControladorVendaDePlano>().consultarConfigVendasOnline(carregando: () {}, sucesso: () {}, falha: (value) {});
      _initializeAsync();
      GetIt.I.get<ControladorAgendamento>().consultarAgendamentos(
          dia: DateTime.now(),
          disponibilidades: false,
          carregando: () {},
          sucesso: () {
            GetIt.I.get<ControladorAgendamento>().obterTiposDeAgendamento();
          },
          falha: (erro) {},
          periodo: 'MES');
    }
    GetIt.I.get<ControladorConfiguracao>().consultarAvisoSonoro();
    GetIt.I.get<ControladorConfiguracao>().consultarDescansoExercicio();
    GetIt.I.get<ControladorAulaTurma>().consultarVisualizacaoAgrupamentoHorario();
    GetIt.I.get<ControladorWod>().consultarUnidadeMedida();
    GetIt.I.get<ControladorConfiguracao>().consultarUnidadeMedidaDistancia();
    GetIt.I.get<ControladorConfiguracao>().consultarUnidadeMedidaCorporal();
    GetIt.I.get<ControladorConfiguracao>().consultarUnidadeMedidaAltura();
    GetIt.I.get<ControladorTreinosPreDefinidos>().validaSeTudoCarregou();
    GetIt.I.get<ControladorBeberAgua>().carregarBeberAgua();
    GetIt.I.get<ControladorCronometroPadrao>().consultarSomHabilitado();
    GetIt.I.get<ControladorNotificacoes>().carregouApp = true;
    // FirebaseDynamicLinksPlatform.instance.onLink.listen((dynamicLinkData) async {
    //   String uri = dynamicLinkData.link.toString();
    //   if (uri.contains('vitio')) {
    //     Navigator.of(GetIt.I.get<NavigationService>().context).pushNamed('/telaVitioGanhouConvite', arguments: 'https://checkout.vitio.com.br/product-offer/${uri.split('vitio_')[1]}');
    //   }
    // }).onError((error) {});
    shakeEnabled = GetIt.I.get<ControladorConfiguracao>().agiteParaReportarHabilitado;
    GetIt.I.get<ControladorApp>().consultarDadosRede();
    ShakeDetector detector = ShakeDetector.autoStart(
      onPhoneShake: () {
        if (shakeEnabled) {
          UtilitarioApp.vibrar();
          shakeEnabled = false;
          abrirReport(context);
        }
      },
      minimumShakeCount: 4,
      shakeSlopTimeMS: 500,
      shakeCountResetTime: 3000,
      shakeThresholdGravity: 2.7,
    );
    detector.startListening();
    super.initState();
  }

  Future<void> _initializeAsync() async {
    await GetIt.I.get<ControladorTreinoAluno>().fichaEmExecucao();
  }

  AppLifecycleState? _notification;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    setState(() {
      _notification = state;
      switch (_notification) {
        case AppLifecycleState.resumed:
          GetIt.I.get<ControladorApp>().updateEstadoApp(AppLifecycleState.resumed);
          GetIt.I.get<ControladorTreinoAluno>().mProgramaCarregado?.programa?.fichaDoDiaTemp = null;
          SharedPreferences.getInstance().then((db) async {
            if (!(db.getBool('TREINO_PAUSADO_PELO_USUARIO') ?? false)) {
              GetIt.I.get<ControladorExecucaoTreino>().iniciarCronometroTreino();
            }
          });
          break;
        case AppLifecycleState.inactive:
          GetIt.I.get<ControladorApp>().updateEstadoApp(AppLifecycleState.inactive);
          SharedPreferences.getInstance().then((db) async {
            GetIt.I.get<ControladorTreinoAluno>().mProgramaCarregado?.programa?.fichaDoDiaTemp = null;
            if (!(db.getBool('TREINO_PAUSADO_PELO_USUARIO') ?? false)) {
              GetIt.I.get<ControladorExecucaoTreino>().pausarCronometroTreino(inativou: true);
            }
          });
          break;
        default:
      }
    });
  }

  @override
  void afterFirstLayout(BuildContext context) async {
    if (GetIt.I.get<ControladorCliente>().isUsuarioColaborador) {
      validarTokenColaboradorDiario();
    }
    ChatController().escutarTopicoPush();
    registarOAcessoNoMetabase();
    consultarDadosUsuarioNoFirebase();
    verificarSeHojeEAniversarioDoUsuario();
    validarSeOColaboradorBodyFitEstaBloqueado();
    GetIt.I.get<ControladorUsuarioApp>().consultarUsuarioRespondeuAnamnese();
    GetIt.I.get<ControladorWod>().consultarNivelWod(carregando: () {}, sucesso: () {}, falha: (erro) {});
    GetIt.I.get<ControladorCliente>().registrarUsoNoTreinoWeb();
    verificarSeUsuarioRespondeuParQ();
    verificarSeUsuarioJaAceitouNovosTermos(context);
    iniciarComunicacaoComAppleWatch(context);
    GetIt.I.get<ControladorEventBus>().eventBus.on<AgiteParaSuporteEvent>().listen((event) {
      shakeEnabled = event.user;
      setState(() {});
    });
    iniciarBannersVitio(context);
  }

  void verificarSeUsuarioRespondeuParQ() {
    if (!GetIt.I.get<ControladorCliente>().isUsuarioColaborador && GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_LANCAR_PARQ).habilitado!) {
      GetIt.I.get<ControladorAvaliacaoFisica>().consultarSeAlunoRespondeuParQ(
          cod: GetIt.I.get<ControladorCliente>().mUsuarioLogado?.cod,
          onNaoRespondeu: () {
            SharedPreferences.getInstance().then((prefs) {
              if (fazMaisDeTresDiasQueNegouParQ(prefs.getInt('ultimaDataParQNegado'))) {
                GetIt.I.get<ControladorAvaliacaoFisica>().consultarPerguntasParQ(onSuccess: () {
                  showModalParq();
                });
              }
            });
          });
    }
  }

  //consultar as permissões do perfil do usuário
  Future<void> validarTokenColaboradorDiario() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int? ultimaValidacao = prefs.getInt('ultima_validacao_token_colaborador');
    int hoje = DateTime.now().millisecondsSinceEpoch;

    if (ultimaValidacao == null || hoje - ultimaValidacao >= 86400000) {
      GetIt.I.get<ControladorSplash>().validarTokenColaborador(
            sucesso: () {
              prefs.setInt('ultima_validacao_token_colaborador', hoje);
            },
            falha: () {},
          );
    } else {
      GetIt.I.get<ControladorSplash>().mPerfilUsuario = PerfilAcesso.fromJson(jsonDecode(prefs.getString('perfilUsuario') ?? '{}'));
    }
  }

  //Consultar empresa cadastrada no financeiro e registrar o events no metabase
  Future<void> registarOAcessoNoMetabase() async {
    analytic(EventosKey.abriu_app);
    await SharedPreferences.getInstance().then((value) {
      if (value.getInt('data_consulta_listaEmpresa') == null) {
        var data = DateTime.now().add(const Duration(days: 1)).millisecondsSinceEpoch;
        passouDataConsultaEmpresa(data);
        GetIt.I.get<ControladorApp>().consultarEmpresaFinanceiro(_mCliente.mUsuarioLogado?.codEmpresa.toString() ?? '1', carregando: () {}, sucesso: () {
          analytic(EventosKey.USUARIO_FEZ_LOGIN_APP_TREINO);
        }, falha: (error) {});
      } else if (value.getInt('data_consulta_listaEmpresa')! < DateTime.now().millisecondsSinceEpoch) {
        var data = DateTime.now().add(const Duration(days: 1)).millisecondsSinceEpoch;
        passouDataConsultaEmpresa(data);
        GetIt.I.get<ControladorApp>().consultarEmpresaFinanceiro(_mCliente.mUsuarioLogado?.codEmpresa.toString() ?? '1', carregando: () {}, sucesso: () {
          analytic(EventosKey.USUARIO_FEZ_LOGIN_APP_TREINO);
        }, falha: (error) {});
      }
    });
  }

  validarSeOColaboradorBodyFitEstaBloqueado() {
    if (GetIt.I.get<ControladorCliente>().isUsuarioColaborador) {
      GetIt.I.get<ControladorCliente>().validarColaboradorBodyFit(() {
        Navigator.of(context).pushNamedAndRemoveUntil('/telaAcessoNegadoBodyFit', (Route<dynamic> route) => false);
      });
    }
  }

  Future<void> iniciarComunicacaoComAppleWatch(BuildContext context) async {
    if (Platform.isIOS) {
      WidgetsBinding.instance.addPostFrameCallback((_) => initPlugin());
      await Future.delayed(const Duration(seconds: 2));
      GetIt.I.get<ControladorExecucaoTreino>().enviarDadosAlunoEURLParaAppleWatch(context);
      GetIt.I.get<ControladorExecucaoTreino>().enviarTreinoParaAppleWatch(null);
    }
  }

  Future<void> verificarSeUsuarioJaAceitouNovosTermos(BuildContext context) async {
    GetIt.I.get<ControladorCliente>().verificarSeAssinouOsTermos(
          assinou: (eColaborador) {},
          naoAssinou: (eColaborador) {
            Navigator.of(context).pushNamedAndRemoveUntil('/telaTermosPoliticas', (Route<dynamic> route) => false);
          },
        );
  }

  void consultarDadosUsuarioNoFirebase() {
    if (!GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_BLOQUEAR_ALUNO_NO_APP).habilitado!) {
      GetIt.I.get<ControladorUsuarioApp>().consultarDadosUsuarioAPP(
          responderAnamnese: () async {},
          sucesso: () {
            setState(() {});
          },
          falha: () {},
          carregando: () {});
    }
  }

  verificarSeHojeEAniversarioDoUsuario() {
    GetIt.I.get<ControladorCliente>().consultarDadosDoUsuario(
        force: false,
        sucesso: () async {
          if (!(GetIt.I.get<ControladorCliente>().mDadosDoUsuario?.dataNascimento?.isEmpty ?? true)) {
            if (UtilDataHora.getDiaMes(dateTime: UtilDataHora.parseStringToDate(GetIt.I.get<ControladorCliente>().mDadosDoUsuario?.dataNascimento!)) ==
                UtilDataHora.getDiaMes(dateTime: DateTime.now())) {
              await Future.delayed(const Duration(seconds: 1));
              setState(() {
                diaAniversario = true;
              });
            }
          }
        });
  }

  Future<void> iniciarBannersVitio(BuildContext context) async {
    if ((GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_VITIO).habilitado ?? false) && !GetIt.I.get<ControladorCliente>().isUsuarioColaborador) {
      var db = await SharedPreferences.getInstance();
      if (db.getString('URL_VITIO') != null) {
        Navigator.of(GetIt.I.get<NavigationService>().context).pushNamed('/telaVitioGanhouConvite', arguments: db.getString('URL_VITIO'));
        db.remove('URL_VITIO');
      }
    }
  }
  // showDialogNovosTermos() async {
  //   SharedPreferences db = await SharedPreferences.getInstance();
  //   showModalBottomSheet(
  //     isScrollControlled: true,
  //     isDismissible: false,
  //     enableDrag: false,
  //     context: context,
  //     builder: (BuildContext context) {
  //       return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
  //         return FractionallySizedBox(
  //           heightFactor: 0.9,
  //           child: DScard(
  //             paddingExterno: const EdgeInsets.all(16),
  //             child: Wrap(
  //               children: [
  //                 SizedBox(
  //                     height: (MediaQuery.of(context).size.height * 0.9) - 110,
  //                     child: SingleChildScrollView(
  //                         child: Padding(
  //                       padding: const EdgeInsets.fromLTRB(16, 16, 16, 64),
  //                       child: Column(
  //                         children: [
  //                           DSsvg(imagem: Imagem.cadeado),
  //                           Padding(
  //                             padding: const EdgeInsets.only(top: 32, bottom: 16),
  //                             child: DStextTitle1(
  //                               'termo_polica',
  //                               eHeavy: true,
  //                             ),
  //                           ),
  //                           DStextBody('texto_termos_politicas', eHeavy: false, ePrimario: true, textAlign: TextAlign.center),
  //                           const SizedBox(height: 32),
  //                           Row(
  //                             crossAxisAlignment: CrossAxisAlignment.center,
  //                             mainAxisAlignment: MainAxisAlignment.center,
  //                             children: [
  //                               DSbotaoPadrao(
  //                                 titulo: localizedString('terms_and_conditions'),
  //                                 tipoBotao: TipoBotao.sublinhado,
  //                                 icone: TreinoIcon.file_alt,
  //                                 posicaoIcone: PosicaoIcone.esquerda,
  //                                 alturaIcone: 18,
  //                                 onTap: () {
  //                                   launchUrl(Uri(scheme: 'https', host: 'www.sistemapacto.com.br', path: '/politica-de-privacidade/'));
  //                                 },
  //                               ),
  //                             ],
  //                           ),
  //                           Padding(
  //                             padding: const EdgeInsets.only(top: 16),
  //                             child: Row(
  //                               crossAxisAlignment: CrossAxisAlignment.center,
  //                               mainAxisAlignment: MainAxisAlignment.center,
  //                               children: [
  //                                 DSbotaoPadrao(
  //                                   titulo: localizedString('privacy_policies'),
  //                                   tipoBotao: TipoBotao.sublinhado,
  //                                   icone: TreinoIcon.file_alt,
  //                                   posicaoIcone: PosicaoIcone.esquerda,
  //                                   alturaIcone: 18,
  //                                   onTap: () {
  //                                     launchUrl(Uri(scheme: 'https', host: 'www.sistemapacto.com.br', path: '/politica-de-privacidade/'));
  //                                   },
  //                                 ),
  //                               ],
  //                             ),
  //                           ),
  //                         ],
  //                       ),
  //                     ))),
  //                 Padding(
  //                   padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
  //                   child: DSbotaoPadrao(
  //                     titulo: localizedString('aceitar'),
  //                     onTap: () {
  //                       db.setBool('aceitouNovosTermos-${GetIt.I.get<ControladorApp>().chave!}-${GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa}-${GetIt.I.get<ControladorCliente>().mUsuarioLogado!.matricula}', true);
  //                       Navigator.of(context).pop();
  //                     },
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ),
  //         );
  //       });
  //     },
  //   );
  // }

  canShowDialogRenovacao() {
    return _controladorContrato.isUsuarioFrequenteParaRenovacaoAntecipada(
            qtdeAcessosSemanaPassada: _controladorContrato.situacaoUsuarioLogado?.qtdeAcessosSemanaPassada ?? 0,
            qtdeAcessosSemana2: _controladorContrato.situacaoUsuarioLogado?.qtdeAcessosSemana2 ?? 0,
            qtdeAcessosSemana3: _controladorContrato.situacaoUsuarioLogado?.qtdeAcessosSemana3 ?? 0,
            qtdeAcessosSemana4: _controladorContrato.situacaoUsuarioLogado?.qtdeAcessosSemana4 ?? 0) &&
        isContratoVencendo(diasAteVencer(_controladorContrato.contratoVisualizar!.vigenciaAteAjustada)) &&
        !(_controladorContrato.contratoVisualizar!.renovacaoautomaticasimnao ?? false) &&
        _controladorContrato.contratoVisualizar!.permiteRenovar! &&
        !_controladorContrato.contratoVisualizar!.bolsa! &&
        GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_RENOVACAO_DE_CONTRATO).habilitado!;
  }

  fazMaisDeTresDiasQueNegouParQ(int? ultimaDataParQNegado) {
    if (ultimaDataParQNegado == null) {
      return true;
    }
    return DateTime.now().millisecondsSinceEpoch - ultimaDataParQNegado > 259200000;
  }

  showDialogRenovacao({Function()? onTapRenovar, Function()? onTapAgoraNao, String? nomePlano, int? diasAteVencer}) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (_) {
          return StatefulBuilder(builder: (context, StateSetter setState) {
            return SafeArea(
              child: DScard(
                  borderRadius: 24,
                  paddingExterno: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                            color: DSLib.theme == ThemeMode.dark ? const Color(0xff424242) : const Color(0xffDCDDDF),
                            borderRadius: const BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24))),
                        child: Column(
                          children: [
                            Align(
                              alignment: Alignment.topRight,
                              child: Padding(
                                padding: const EdgeInsets.fromLTRB(12, 12, 12, 0),
                                child: DSbotaoCircular(
                                  altura: 28,
                                  icone: TreinoIcon.times,
                                  categoria: Categoria.primario,
                                  onTap: () async {
                                    Navigator.of(context).pop();
                                    onTapAgoraNao?.call();
                                  },
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 31),
                              child: ColorFiltered(
                                colorFilter: ColorFilter.mode(Theme.of(context).primaryColor, BlendMode.color),
                                child: SvgPicture.asset(DSLib.theme == ThemeMode.dark ? 'assets/images/contract-dark.svg' : 'assets/images/contract-light.svg',
                                    colorFilter: ColorFilter.mode(
                                      DSLib.theme == ThemeMode.dark ? const Color(0xff424242) : const Color(0xffDCDDDF),
                                      DSLib.theme == ThemeMode.dark ? BlendMode.colorDodge : BlendMode.colorBurn,
                                    )),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
                        child: DStextSubheadline('seu_plano_esta_expirando', textAlign: TextAlign.center),
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
                        child: DStextBody('seu_plano_ira_expirar_args', args: [(nomePlano ?? ''), (diasAteVencer ?? 0).toString()], textAlign: TextAlign.center, ePrimario: false),
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                        child: DSbotaoPadrao(
                          titulo: 'renovar_meu_plano',
                          onTap: () {
                            onTapRenovar?.call();
                          },
                        ),
                      )
                    ],
                  )),
            );
          });
        });
  }

  showModalParq() {
    if (!mounted) return;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext modalContext) {
          return StatefulBuilder(
            builder: (modalContext, StateSetter modalSetState) {
              return SafeArea(
                child: DScard(
                  borderRadius: 24,
                  paddingExterno: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  child: SizedBox(
                    child: Wrap(
                      runSpacing: 16,
                      children: [
                        Container(
                          decoration: const BoxDecoration(color: Colors.transparent, borderRadius: BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24))),
                          child: Column(
                            children: [
                              Align(
                                alignment: Alignment.topRight,
                                child: Padding(
                                  padding: const EdgeInsets.fromLTRB(12, 12, 12, 0),
                                  child: DSbotaoCircular(
                                    altura: 34,
                                    icone: TreinoIcon.times,
                                    categoria: Categoria.secundario,
                                    onTap: () {
                                      Navigator.of(modalContext).pop();
                                    },
                                  ),
                                ),
                              ),
                              const Divider(),
                              Padding(
                                padding: const EdgeInsets.only(bottom: 16, top: 16),
                                child: DSsvg(imagem: Imagem.coracao),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                          child: Column(
                            children: [
                              DStextSubheadline(localizedString('questionario_de_prontidao'), textAlign: TextAlign.center),
                              Padding(
                                padding: const EdgeInsets.only(
                                  bottom: 24,
                                  top: 12,
                                ),
                                child: DStextBody(
                                  localizedString('este_questionario_te_dira'),
                                  eHeavy: false,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              DSbotaoPadrao(
                                  titulo: localizedString('responder_agora'),
                                  onTap: () {
                                    Navigator.of(modalContext).pop();
                                    if (mounted) {
                                      Navigator.of(context).pushNamed('/telaParQ');
                                    }
                                  })
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
      );
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<void> initPlugin() async {
    try {
      final TrackingStatus status = await AppTrackingTransparency.trackingAuthorizationStatus;
      if (status == TrackingStatus.notDetermined) {
        await AppTrackingTransparency.requestTrackingAuthorization();
      }
    } on PlatformException {}
  }

  final ControladorExecucaoTreino _controladorExecucaoTreino = GetIt.I.get<ControladorExecucaoTreino>();

  var diaAniversario = false;
  ControladorApp controladroApp = GetIt.I.get<ControladorApp>();
  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        child: Stack(
          children: [
            Positioned.fill(
              child: Scaffold(
                key: _scaffoldKey,
                resizeToAvoidBottomInset: false,
                body: Stack(
                  children: <Widget>[
                    PageView(
                      controller: GetIt.I.get<ControladorApp>().controller,
                      physics: const NeverScrollableScrollPhysics(),
                      children: GetIt.I.get<ControladorApp>().telaTabbar,
                    ),
                    showPushWhenAppIsOpenOverLay(),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Observer(builder: (context) {
                          return Visibility(
                            visible: _controladorCronometro.hasCronometroExecutando,
                            child: const CronometroHome(),
                          );
                        }),
                        Observer(builder: (context) {
                          return Visibility(
                            visible: _controladorExecucaoTreino.treinoEmExecucao?.atividades != null,
                            child: const TelaTreinoWidgetExecucao(),
                          );
                        }),
                        Container(height: 1, color: Theme.of(context).dividerColor),
                        Observer(builder: (_) {
                          return DSbottomBarBlur(
                            showSelectedLabels: true,
                            showUnselectedLabels: true,
                            backgroundColor: Theme.of(context).cardColor,
                            selectedItemColor: DSLib.theme == ThemeMode.dark ? const Color(0xffF5F5F5) : const Color(0xff3F3F3F),
                            unselectedItemColor: DSLib.theme == ThemeMode.dark ? const Color(0xffB3B3B3) : const Color(0xff838383),
                            bottomNavigationBarItems: GetIt.I.get<ControladorApp>().getItensBottomBar,
                            currentIndex: _obterIndiceSeguro(controladroApp.indexTabbar, GetIt.I.get<ControladorApp>().getItensBottomBar),
                            onIndexChange: (value) {
                              GetIt.I.get<ControladorApp>().controller.animateToPage(value, duration: const Duration(milliseconds: 1), curve: Curves.easeIn);
                              setState(() {
                                GetIt.I.get<ControladorApp>().indexTabbar = value;
                              });
                            },
                          );
                        }),
                      ],
                    )
                  ],
                ),
              ),
            ),
            diaAniversario
                ? Positioned.fill(
                    child: Lottie.asset(
                    'assets/gifs/balloons.json',
                    height: MediaQuery.of(context).size.height,
                    repeat: false,
                    onLoaded: (p0) async {
                      await Future.delayed(const Duration(seconds: 7));
                      setState(() {
                        diaAniversario = false;
                      });
                    },
                  ))
                : Container(),
          ],
        ),
        onPopInvoked: (a) {
          DSalerta().exibirAlerta(
            context: context,
            titulo: 'deseja_fechar_app',
            subtitulo: 'ao_clicar_fechar_app_dados_perdidos',
            tituloBotao: 'fechar_app',
            tituloBotaoSecundario: localizedString('cancel'),
            onTap: () {
              SystemChannels.platform.invokeMethod('SystemNavigator.pop');
            },
          );
        });
  }

  int _obterIndiceSeguro(int indice, List itens) {
    if (indice < 0) {
      return 0;
    }
    if (itens.isEmpty) {
      return 0;
    }
    if (indice >= itens.length) {
      return itens.length - 1;
    }
    return indice;
  }

  showDialogVencidoComRenovacao() {
    return novoShowDialogContrato(
      context: context,
      vaiTerBotaoFechar: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_BLOQUEAR_ALUNO_NO_APP).habilitado!,
      vaiTerBotaoSecundario: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_VENDAS_DE_PLANOS).habilitado!,
      vaiTerBotaoPrimario: true,
      titulo: localizedString('showDialog_contrato.titulo_vencido', args: [camelCase(GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first!.nomePlano)]),
      subTitulo: localizedString('showDialog_contrato.subtitulo_vencido'),
      tituloBotaoPrimario: localizedString('showDialog_contrato.renove_agora'),
      tituloBotaoSecundario: localizedString('showDialog_contrato.outro_plano'),
      acaoPrimario: () {
        GetIt.I.get<NavigationService>().goBack();
        Navigator.pushNamed(context, '/telaRenovarContrato', arguments: GetIt.I.get<ControladorCliente>()).then((contratoInativo) {
          if (contratoInativo != null) {
            if (contratoInativo is bool) {
              DSalerta().exibirAlertaSimplificado(context: context, titulo: 'plano_inativo_titulo', subtitulo: 'plano_inativo_mensagem', tituloBotao: 'Ok', tipoAlerta: TipoAlerta.alerta);
            }
          }
        });
      },
      acaoSecundario: () {
        GetIt.I.get<NavigationService>().goBack();
        Navigator.pushNamed(context, '/telaContratosAluno', arguments: 1);
      },
    );
  }

  showDialogVencimentoSemRenovacao() {
    return novoShowDialogContrato(
      context: context,
      vaiTerBotaoFechar: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_BLOQUEAR_ALUNO_NO_APP).habilitado!,
      vaiTerBotaoSecundario: false,
      vaiTerBotaoPrimario: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_VENDAS_DE_PLANOS).habilitado!,
      titulo: localizedString('showDialog_contrato.titulo_vencido', args: [camelCase(GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first!.nomePlano)]),
      subTitulo: localizedString('showDialog_contrato.subtitulo_vencido'),
      tituloBotaoPrimario: localizedString('showDialog_contrato.outro_plano'),
      acaoPrimario: () {
        GetIt.I.get<NavigationService>().goBack();
        Navigator.pushNamed(context, '/telaContratosAluno', arguments: 1);
      },
    );
  }

  showDialogAVencerComRenovacao(String dias) {
    return novoShowDialogContrato(
      context: context,
      vaiTerBotaoFechar: false,
      vaiTerBotaoSecundario:
          GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_VENDAS_DE_PLANOS).habilitado! && (_mCliente.mDadosDoUsuario?.permiteContratosConcomitante ?? false),
      vaiTerBotaoPrimario: true,
      titulo: dias == '1'
          ? localizedString('showDialog_contrato.titulo_avencer_dia', args: [camelCase(GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first!.nomePlano), dias])
          : dias == '0'
              ? localizedString('showDialog_contrato.titulo_avencer_hoje', args: [camelCase(GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first!.nomePlano)])
              : localizedString('showDialog_contrato.titulo_avencer_dias', args: [camelCase(GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first!.nomePlano), dias]),
      subTitulo: localizedString('showDialog_contrato.subtitulo_Avencer'),
      tituloBotaoPrimario: localizedString('showDialog_contrato.renove_agora'),
      tituloBotaoSecundario: localizedString('showDialog_contrato.outro_plano'),
      acaoPrimario: () {
        GetIt.I.get<NavigationService>().goBack();
        Navigator.pushNamed(context, '/telaRenovarContrato', arguments: GetIt.I.get<ControladorCliente>()).then((contratoInativo) {
          if (contratoInativo != null) {
            if (contratoInativo is bool) {
              DSalerta().exibirAlertaSimplificado(context: context, titulo: 'plano_inativo_titulo', subtitulo: 'plano_inativo_mensagem', tituloBotao: 'Ok', tipoAlerta: TipoAlerta.alerta);
            }
          }
        });
      },
      acaoSecundario: () {
        GetIt.I.get<NavigationService>().goBack();
        Navigator.pushNamed(context, '/telaContratosAluno', arguments: 1);
      },
    );
  }

  showDialogAVencerSemRenovacao(String dias) {
    return novoShowDialogContrato(
      context: context,
      vaiTerBotaoFechar: false,
      vaiTerBotaoSecundario: false,
      vaiTerBotaoPrimario:
          GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_VENDAS_DE_PLANOS).habilitado! && (_mCliente.mDadosDoUsuario?.permiteContratosConcomitante ?? false),
      titulo: dias == '1'
          ? localizedString('showDialog_contrato.titulo_avencer_dia', args: [camelCase(GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first!.nomePlano), dias])
          : dias == '0'
              ? localizedString('showDialog_contrato.titulo_avencer_hoje', args: [camelCase(GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first!.nomePlano)])
              : localizedString('showDialog_contrato.titulo_avencer_dias', args: [camelCase(GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first!.nomePlano), dias]),
      subTitulo: localizedString('showDialog_contrato.subtitulo_Avencer_naoRenovar'),
      tituloBotaoPrimario: localizedString('showDialog_contrato.outro_plano'),
      acaoPrimario: () {
        GetIt.I.get<NavigationService>().goBack();
        Navigator.pushNamed(context, '/telaContratosAluno', arguments: 1);
      },
    );
  }

  showDialogCanceladoComprarPlano() {
    return novoShowDialogContrato(
      context: context,
      vaiTerBotaoFechar: false,
      vaiTerBotaoSecundario: false,
      vaiTerBotaoPrimario: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_VENDAS_DE_PLANOS).habilitado!,
      titulo: _mCliente.isVisitante ? localizedString('tela_detalhes_contrato.titulo_compra_plano') : localizedString('showDialog_contrato.titulo_cancelado'),
      subTitulo: _mCliente.isVisitante ? localizedString('para_poder_aproveitar_tudo') : localizedString('showDialog_contrato.subtitulo_cancelado'),
      tituloBotaoPrimario: localizedString('showDialog_contrato.conferirplano'),
      acaoPrimario: () {
        Navigator.pushNamed(context, '/telaContratosAluno', arguments: 1);
      },
    );
  }

  showDialogVencidoApenasRenovacao() {
    return novoShowDialogContrato(
      context: context,
      vaiTerBotaoFechar: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_BLOQUEAR_ALUNO_NO_APP).habilitado!,
      vaiTerBotaoSecundario: true,
      vaiTerBotaoPrimario: true,
      titulo: localizedString('showDialog_contrato.titulo_vencido', args: [camelCase(GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first!.nomePlano)]),
      subTitulo: localizedString('showDialog_contrato.subtitulo_vencido'),
      tituloBotaoPrimario: localizedString('showDialog_contrato.renove_agora'),
      acaoPrimario: () {
        GetIt.I.get<NavigationService>().goBack();
        Navigator.pushNamed(context, '/telaRenovarContrato', arguments: GetIt.I.get<ControladorCliente>()).then((contratoInativo) {
          if (contratoInativo != null) {
            if (contratoInativo is bool) {
              DSalerta().exibirAlertaSimplificado(context: context, titulo: 'plano_inativo_titulo', subtitulo: 'plano_inativo_mensagem', tituloBotao: 'Ok', tipoAlerta: TipoAlerta.alerta);
            }
          }
        });
      },
      tituloBotaoSecundario: 'Acessar com outro usuário',
      acaoSecundario: () {
        analytic(EventosKey.drawer_pressionou_logout);
        DSalerta().exibirAlerta(
            context: context,
            titulo: 'menu_perfil.deseja_deslogar',
            subtitulo: 'menu_perfil.aviso_logout',
            tituloBotao: 'menu_perfil.sair',
            tituloBotaoSecundario: 'cancelar_texto',
            onTap: () {
              analytic(EventosKey.drawer_confirmou_logout);
              _mCliente.deslogarUsuario(sucesso: () {
                GetIt.I.get<ControladorApp>().limparTodosControladores();
                FocusScope.of(context).requestFocus(FocusNode());
                Navigator.of(context)
                    .pushNamedAndRemoveUntil(controllerApp.desabilitarNovoFluxoLogin ? '/buscarCadastroPorTelefone' : '/telaSelecaoMetodoLogin', (Route<dynamic> route) => false);
              });
            });
      },
    );
  }

  showDialogVencidoApenasAlerta() {
    return novoShowDialogContrato(
      context: context,
      vaiTerBotaoFechar: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_BLOQUEAR_ALUNO_NO_APP).habilitado!,
      vaiTerBotaoSecundario: false,
      vaiTerBotaoPrimario: true,
      titulo: localizedString('showDialog_contrato.titulo_vencido', args: [camelCase(GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first!.nomePlano)]),
      subTitulo: localizedString('showDialog_contrato.subtitulo_vencido_naoRenovar'),
      tituloBotaoPrimario: 'Acessar com outro usuário',
      acaoPrimario: () {
        analytic(EventosKey.drawer_pressionou_logout);
        DSalerta().exibirAlerta(
            context: context,
            titulo: 'menu_perfil.deseja_deslogar',
            subtitulo: 'menu_perfil.aviso_logout',
            tituloBotao: 'menu_perfil.sair',
            tituloBotaoSecundario: 'cancelar_texto',
            onTap: () {
              analytic(EventosKey.drawer_confirmou_logout);
              _mCliente.deslogarUsuario(sucesso: () {
                GetIt.I.get<ControladorApp>().limparTodosControladores();
                FocusScope.of(context).requestFocus(FocusNode());
                Navigator.of(context)
                    .pushNamedAndRemoveUntil(controllerApp.desabilitarNovoFluxoLogin ? '/buscarCadastroPorTelefone' : '/telaSelecaoMetodoLogin', (Route<dynamic> route) => false);
              });
            });
      },
    );
  }

  String valorDosDias(int valor) {
    if (valor > 0) {
      return (valor + 1).toString();
    } else {
      return valor.toString();
    }
  }

  void verificarClienteParaModalDeCompra(BuildContext context) {
    if (GetIt.I.get<ControladorCliente>().isUsuarioColaborador || 
        ((_mCliente.isVencido || _mCliente.isAVencer || _mCliente.isAtivo) && ((GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first?.bolsa ?? false) || (GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first?.renovacaoautomaticasimnao ?? false))) ||
        (GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.LIBERAR_VISITANTE_COM_BLOQUEIO).habilitado! && _mCliente.isVisitante) ||
        ((GetIt.I.get<ControladorCliente>().mLogadoSituacao?.tipoAcesso?.toLowerCase().contains('gympass') ?? false) ||
        (GetIt.I.get<ControladorCliente>().mLogadoSituacao?.tipoAcesso?.toLowerCase().contains('totalpass') ?? false))) {
      return;
    }
    if ((_mCliente.isVisitante || _mCliente.isVencido || _mCliente.isCancelado || _mCliente.isDesistente) &&
        GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_BLOQUEAR_ALUNO_NO_APP).habilitado!) {
      Navigator.pushNamed(context, '/telaProcessoAlunoVisitante');
    } else {
      verificarContrato(context);
    }
  }

  void verificarContrato(BuildContext context) {
    if (_mCliente.isVencido) {
      if ((GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first?.permiteRenovar ?? false)) {
        showDialogVencido(
            context, GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_VENDAS_DE_PLANOS).habilitado! ? showDialogVencidoComRenovacao : showDialogVencidoApenasRenovacao);
      } else {
        showDialogVencido(
            context, GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_VENDAS_DE_PLANOS).habilitado! ? showDialogVencimentoSemRenovacao : showDialogVencidoApenasAlerta);
      }
    } else if (_mCliente.isAVencer && GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_VENDAS_DE_PLANOS).habilitado!) {
      if ((GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first?.permiteRenovar ?? false)) {
        showDialogAVencer(context, showDialogAVencerComRenovacao);
      } else {
        showDialogAVencer(context, showDialogAVencerSemRenovacao);
      }
    } else if (_mCliente.isCancelado || _mCliente.isDesistente || _mCliente.isVisitante) {
      showDialogCanceladoComprarPlano();
    }
  }

  void showDialogVencido(BuildContext context, Function showDialogRenovacao) {
    Future.delayed(const Duration(milliseconds: 2000)).then((_) => showDialogRenovacao());
  }

  void showDialogAVencer(BuildContext context, Function showDialogRenovacao) {
    DateTime data1 = UtilDataHora.parseStringToDate(GetIt.I.get<ControladorContratoUsuario>().mListaContratos.first!.vigenciaAteAjustada)!;
    DateTime data2 = DateTime.now();
    var dias = data1.difference(data2).inDays;
    Future.delayed(const Duration(milliseconds: 2000)).then((_) => showDialogRenovacao(valorDosDias(dias)));
  }

  void showDialogCancelado(BuildContext context) {
    Future.delayed(const Duration(milliseconds: 2000)).then((_) => showDialogCanceladoComprarPlano());
  }

  showDialogCanceladoApenasAlerta() {
    return novoShowDialogContrato(
      context: context,
      vaiTerBotaoFechar: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_BLOQUEAR_ALUNO_NO_APP).habilitado!,
      vaiTerBotaoSecundario: false,
      vaiTerBotaoPrimario: true,
      titulo: localizedString('showDialog_contrato.titulo_cancelado'),
      subTitulo: localizedString('showDialog_contrato.subtitulo_cancelado_desistente'),
      tituloBotaoPrimario: 'Acessar com outro usuário',
      acaoPrimario: () {
        analytic(EventosKey.drawer_pressionou_logout);
        DSalerta().exibirAlerta(
            context: context,
            titulo: 'menu_perfil.deseja_deslogar',
            subtitulo: 'menu_perfil.aviso_logout',
            tituloBotao: 'menu_perfil.sair',
            tituloBotaoSecundario: 'cancelar_texto',
            onTap: () {
              analytic(EventosKey.drawer_confirmou_logout);
              _mCliente.deslogarUsuario(sucesso: () {
                GetIt.I.get<ControladorApp>().limparTodosControladores();
                FocusScope.of(context).requestFocus(FocusNode());
                Navigator.of(context)
                    .pushNamedAndRemoveUntil(controllerApp.desabilitarNovoFluxoLogin ? '/buscarCadastroPorTelefone' : '/telaSelecaoMetodoLogin', (Route<dynamic> route) => false);
              });
            });
      },
    );
  }

  novoShowDialogContrato(
      {required BuildContext context,
      bool? vaiTerBotaoFechar,
      bool? vaiTerBotaoSecundario,
      bool? vaiTerBotaoPrimario,
      String? titulo,
      String? subTitulo,
      String? tituloBotaoPrimario,
      String? tituloBotaoSecundario,
      Function()? acaoPrimario,
      Function()? acaoSecundario}) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: false,
      isDismissible: false,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, StateSetter setState) {
            return FractionallySizedBox(
              child: SafeArea(
                child: DScard(
                  borderRadius: 24,
                  paddingExterno: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  child: SizedBox(
                    child: Wrap(
                      runSpacing: 16,
                      children: [
                        !vaiTerBotaoFechar!
                            ? Align(
                                alignment: Alignment.topRight,
                                child: Padding(
                                  padding: const EdgeInsets.fromLTRB(12, 12, 12, 0),
                                  child: DSbotaoCircular(
                                    altura: 28,
                                    icone: TreinoIcon.times,
                                    categoria: Categoria.secundario,
                                    onTap: () async {
                                      Navigator.of(context).pop();
                                    },
                                  ),
                                ),
                              )
                            : Container(),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
                          child: Column(
                            children: [
                              DSsvg(imagem: Imagem.contrato),
                              Padding(
                                padding: const EdgeInsets.only(left: 40, right: 40, top: 8),
                                child: DStextSubheadline(titulo!, textAlign: TextAlign.center),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                  bottom: 24,
                                  top: 12,
                                ),
                                child: DStextBody(
                                  subTitulo!,
                                  eHeavy: false,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              vaiTerBotaoPrimario! ? DSbotaoPadrao(titulo: tituloBotaoPrimario!, onTap: acaoPrimario) : Container(),
                              vaiTerBotaoSecundario!
                                  ? Column(
                                      children: [const SizedBox(height: 16), DSbotaoPadrao(tipoBotao: TipoBotao.secundario, titulo: tituloBotaoSecundario!, onTap: acaoSecundario)],
                                    )
                                  : Container()
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  abrirReport(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: false,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, StateSetter setState) {
            return SafeArea(
              child: DScard(
                paddingExterno: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                child: SizedBox(
                  child: Wrap(
                    runSpacing: 16,
                    children: [
                      Padding(
                        padding: const EdgeInsets.fromLTRB(12, 12, 12, 0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: 28,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 2),
                              child: Column(
                                children: [
                                  DStextHeadline('aconteceu_algum_problema'),
                                ],
                              ),
                            ),
                            DSbotaoCircular(
                              altura: 32,
                              icone: TreinoIcon.times,
                              categoria: Categoria.secundario,
                              onTap: () {
                                setState(() {
                                  shakeEnabled = GetIt.I.get<ControladorConfiguracao>().agiteParaReportarHabilitado;
                                });
                                Navigator.of(context).pop();
                              },
                            )
                          ],
                        ),
                      ),
                      const Divider(),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(12, 0, 12, 32),
                        child: Column(
                          children: [
                            SvgPicture.asset('assets/images/alerta_treino6.svg', colorFilter: ColorFilter.mode(Theme.of(context).primaryColor, BlendMode.dst)),
                            DStextBody(
                              'seu_feedback_nos_ajuda',
                              eHeavy: false,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 16, bottom: 12),
                              child: DSbotaoPadrao(
                                titulo: localizedString('relatar_um_problema'),
                                onTap: () async {
                                  await Navigator.of(context).pushNamed('/telaReportarProblema');
                                  Navigator.of(context).pop();
                                },
                              ),
                            ),
                            const Padding(
                              padding: EdgeInsets.only(top: 16, bottom: 16),
                              child: Divider(),
                            ),
                            configuracaoItemComSwitch(
                              context: context,
                              titulo: localizedString('agitar_o_telefone_para_relatar_um_problema'),
                              subTitulo: localizedString('mova_o_switch_para_desabilitar'),
                              onChanged: (v) {
                                GetIt.I.get<ControladorConfiguracao>().agiteParaReportar(v);
                                setState(() {
                                  GetIt.I.get<ControladorConfiguracao>().consultarAgiteParaReportar();
                                });
                              },
                              habilitado: GetIt.I.get<ControladorConfiguracao>().agiteParaReportarHabilitado,
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
