// ignore_for_file: must_be_immutable

import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/componets/retorno_service_status.dart';
import 'package:app_treino/appWidgets/componentWidgets/date_picker_range.dart';
import 'package:app_treino/controlladores/ControladorAulaTurma.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/aulaTurma/AulaTurma.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/screens/contrato/emptyCases/EmptyWidget.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'dart:math' as math;

import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:skeleton_text/skeleton_text.dart';

class TelaHistoricoAulas extends StatefulWidget {
  const TelaHistoricoAulas({super.key});

  @override
  State<TelaHistoricoAulas> createState() => _TelaHistoricoAulasState();
}

class _TelaHistoricoAulasState extends State<TelaHistoricoAulas> {
  final _controllerAulasTurmas = GetIt.I.get<ControladorAulaTurma>();
  late DateTime startDate;
  late DateTime endDate;
  bool listaInvertida = false;
  bool servicoIndisponivel = false;
  int page = 0;
  @override
  void initState() {
    startDate = _controllerAulasTurmas.diaConsultarAulasTurma ?? DateTime.now();
    endDate = _controllerAulasTurmas.diaConsultarAulasTurma ?? DateTime.now();
    _refresh(startDate, endDate);
    super.initState();
  }

  void _organizarListaHistoricoAula() {
    if (listaInvertida) {
      _controllerAulasTurmas.historicoAulas.sort((a, b) => DateFormat('yyyy-MM-dd').parse(b.dia!).compareTo(DateFormat('yyyy-MM-dd').parse(a.dia!)));
    } else {
      _controllerAulasTurmas.historicoAulas.sort((a, b) => DateFormat('yyyy-MM-dd').parse(a.dia!).compareTo(DateFormat('yyyy-MM-dd').parse(b.dia!)));
    }
  }

  void _refresh(DateTime dataInicial, DateTime dataFinal, {int? pageCustom}) {
    if (pageCustom != null) page = pageCustom;
    page = 0;
    _controllerAulasTurmas.consultarHistoricoAulas(
        dataInicial: dataInicial,
        dataFinal: dataFinal,
        page: page,
        sucesso: () {
          _organizarListaHistoricoAula();
          _refreshController.refreshCompleted();
        },
        falha: (err) {
          if ((err.response?.statusCode ?? 0) == 404) {
            servicoIndisponivel = true;
          } else {
            servicoIndisponivel = false;
          }
          _refreshController.refreshFailed();
        });
  }

  void _onLoading(DateTime dataInicial, DateTime dataFinal, {int? pageCustom}) {
    if (pageCustom != null) page = pageCustom;
    page += 1;
    _controllerAulasTurmas.consultarHistoricoAulas(
        dataInicial: dataInicial,
        dataFinal: dataFinal,
        addNovaPagina: true,
        page: page,
        sucesso: () {
          _organizarListaHistoricoAula();
          _refreshController.loadComplete();
        },
        falha: (err) {
          if ((err.response?.statusCode ?? 0) == 404) {
            servicoIndisponivel = true;
          } else {
            servicoIndisponivel = false;
          }
          _refreshController.loadFailed();
        });
  }

  final _refreshController = RefreshController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: DSappBar(
        backgroundColor: Theme.of(context).colorScheme.surface,
        titulo: 'historico_de_aulas',
        actions: [
          AnimatedButton(
            onTap: () {
              setState(() {
                listaInvertida = !listaInvertida;
                _organizarListaHistoricoAula();
              });
            },
          ),
          const SizedBox(
            width: 16,
          )
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: DatePickerRange(
                startDate: startDate,
                endDate: endDate,
                onPeriodoSelecionado: (periodoSelecionado) {
                  setState(() {
                    startDate = periodoSelecionado.startDate!;
                    endDate = periodoSelecionado.endDate ?? periodoSelecionado.startDate!;
                    _refresh(startDate, endDate);
                  });
                }),
          ),
          const SizedBox(
            height: 24,
          ),
          Expanded(
            child: SmartRefresher(
              enablePullDown: true,
              enablePullUp: _controllerAulasTurmas.historicoAulas.length >= 50 * (page + 1),
              controller: _refreshController,
              onRefresh: () {
                _refresh(startDate, endDate);
              },
              onLoading: () {
                _onLoading(
                  startDate,
                  endDate,
                );
              },
              child: SingleChildScrollView(child: Observer(builder: (_) {
                return RetornoServiceStatus(
                  serviceStatus: _controllerAulasTurmas.statusConsultarHistoricoAulas,
                  falha: () {
                    return EmptyWidget(
                      titulo: servicoIndisponivel ? 'Ops! serviço indisponivel' : 'Não foi possivel consultar',
                      mensagem: 'Parece que esta funcionalidade ainda não está disponível. Tente novamente mais tarde.',
                    );
                  },
                  resultadoVazio: () {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Column(
                        children: [
                          DSsvg(imagem: Imagem.calendario),
                          DStextSubheadline('nenhuma_aula_encontrada_empty_state'),
                          const SizedBox(height: 12),
                          DStextBody(
                            'nao_foram_encontradas_aulas_no_periodo',
                            eHeavy: false,
                            textAlign: TextAlign.center,
                          )
                        ],
                      ),
                    );
                  },
                  carregando: () {
                    return SkeletonAnimation(
                        shimmerColor: Theme.of(context).dividerColor,
                        borderRadius: BorderRadius.circular(50),
                        shimmerDuration: 1000,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: DScard(
                            child: const SizedBox(),
                            corPrimaria: Colors.transparent,
                          ),
                        ));
                  },
                  sucesso: () {
                    String mesAtual = 'startingValue';
                    return Column(
                        children: List<Widget>.generate(_controllerAulasTurmas.historicoAulas.length, (index) {
                      DateTime dataObjetoLista = DateFormat('yyyy-MM-dd').parse(_controllerAulasTurmas.historicoAulas[index].dia ?? '');
                      String mesObjetoLista = DateFormat('MMMM').format(dataObjetoLista);
                      if (mesAtual != mesObjetoLista) {
                        mesAtual = mesObjetoLista;
                        return Column(
                          children: [
                            DStextHeadline(
                              mesObjetoLista + '/' + dataObjetoLista.year.toString(),
                              eHeavy: false,
                            ),
                            const Padding(
                              padding: EdgeInsets.symmetric(horizontal: 16),
                              child: Divider(),
                            ),
                            const SizedBox(
                              height: 8.5,
                            ),
                            CardItemAulaHistorico(
                              controllerAulasTurmas: _controllerAulasTurmas,
                              index: index,
                              nomeMes: mesObjetoLista,
                            )
                          ],
                        );
                      }
                      return CardItemAulaHistorico(
                        controllerAulasTurmas: _controllerAulasTurmas,
                        index: index,
                        nomeMes: mesObjetoLista,
                      );
                    }));
                  },
                );
              })),
            ),
          ),
        ],
      ),
    );
  }
}

class CardItemAulaHistorico extends StatefulWidget {
  const CardItemAulaHistorico({super.key, required ControladorAulaTurma controllerAulasTurmas, required this.index, required this.nomeMes}) : _controllerAulasTurmas = controllerAulasTurmas;
  final int index;
  final ControladorAulaTurma _controllerAulasTurmas;
  final String nomeMes;

  @override
  State<CardItemAulaHistorico> createState() => _CardItemAulaHistoricoState();
}

class _CardItemAulaHistoricoState extends State<CardItemAulaHistorico> {
  bool expanded = false;
  ServiceStatus? statusConsultarAlunosDaAulaHistorico;
  AlunoNaAulaTurma? alunoSelecionado;
  @override
  Widget build(BuildContext context) {
    // bool ultimoDoMes = false;
    // if(_controllerAulasTurmas.historicoAulas.length == index + 1){
    //     ultimoDoMes = true;
    // }
    // if (_controllerAulasTurmas.historicoAulas.length >= index + 2) {
    //   DateTime dataObjetoLista = DateFormat('dd/MM/yyyy').parse(_controllerAulasTurmas.historicoAulas[index + 1].dataAula ?? '');
    //   String mesProxObjeto = DateFormat('MMMM').format(dataObjetoLista);
    //   if (mesProxObjeto != nomeMes) {
    //     ultimoDoMes = true;
    //   }
    // }
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: DScard(
          paddingInterno: const EdgeInsets.symmetric(vertical: 8),
          child: Column(
            children: [
              Row(mainAxisAlignment: MainAxisAlignment.start, crossAxisAlignment: CrossAxisAlignment.center, children: [
                const SizedBox(
                  width: 16,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    DStextSubheadline(
                      capitalize(widget._controllerAulasTurmas.historicoAulas[widget.index].nomeAula ?? ''),
                      maximoLinhas: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    DStextBody(
                      localizedString('prof_abreviacao', args: [UtilitarioApp.sentenseCase(widget._controllerAulasTurmas.historicoAulas[widget.index].nomeProfessor ?? '')]),
                      eHeavy: false,
                      maximoLinhas: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Row(
                      children: [
                        const Icon(
                          TreinoIcon.calender,
                          size: 13,
                        ),
                        const SizedBox(
                          width: 6,
                        ),
                        DStextBody(widget._controllerAulasTurmas.historicoAulas[widget.index].horario ?? '', eHeavy: false),
                        const SizedBox(
                          width: 16,
                        ),
                        if (widget._controllerAulasTurmas.historicoAulas[widget.index].dia != null)
                          Row(
                            children: [
                              const Icon(
                                TreinoIcon.clock,
                                size: 13,
                              ),
                              const SizedBox(
                                width: 6,
                              ),
                              DStextBody(UtilDataHora.getDiaMesAno(dateTime: DateFormat('yyyy-MM-dd').parse(widget._controllerAulasTurmas.historicoAulas[widget.index].dia ?? '2023-01-01')),
                                  eHeavy: false),
                            ],
                          ),
                      ],
                    ),
                  ],
                ),
                const Spacer(),
                if (widget._controllerAulasTurmas.historicoAulas[widget.index].temIntegracaoSelfLoops ?? false)
                  DSbotaoCircular(
                    icone: expanded ? TreinoIcon.angle_up : TreinoIcon.angle_down,
                    onTap: () {
                      if (!expanded && alunoSelecionado == null) {
                        widget._controllerAulasTurmas.consultarAlunosDaAulaHistorico(
                          widget._controllerAulasTurmas.historicoAulas[widget.index],
                          sucesso: (dadosAluno) {
                            if (dadosAluno == null) {
                              statusConsultarAlunosDaAulaHistorico = ServiceStatus.Empty;
                            } else {
                              setState(() {
                                alunoSelecionado = dadosAluno;
                                statusConsultarAlunosDaAulaHistorico = ServiceStatus.Done;
                              });
                            }
                          },
                          carregando: () {
                            setState(() {
                              statusConsultarAlunosDaAulaHistorico = ServiceStatus.Waiting;
                            });
                          },
                          falha: (err) {
                            setState(() {
                              statusConsultarAlunosDaAulaHistorico = ServiceStatus.Error;
                            });
                          },
                        );
                      }
                      setState(() {
                        expanded = !expanded;
                      });
                    },
                    corBordaCustomizada: Theme.of(context).primaryColor,
                    categoria: Categoria.comBorda,
                  ),
                const SizedBox(
                  width: 16,
                ),
              ]),
              const SizedBox(
                height: 8.5,
              ),
              () {
                switch (statusConsultarAlunosDaAulaHistorico) {
                  case ServiceStatus.Waiting:
                    return const CircularProgressIndicator();
                  case ServiceStatus.Done:
                    return AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      height: expanded ? 280 : 0,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: SingleChildScrollView(
                        physics: const NeverScrollableScrollPhysics(),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surface,
                                borderRadius: BorderRadius.circular(18),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 8),
                                child: buildMetricaRow(
                                  'Ranking',
                                  '${alunoSelecionado?.cliente?.posicaoRankingAluno ?? ''}º ${localizedString('posicao')}',
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 16,
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surface,
                                borderRadius: BorderRadius.circular(18),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        Expanded(child: buildMetricaRow('tempo_de_aula', '${alunoSelecionado?.cliente?.tempoDeAula ?? '-'} min')),
                                        Container(
                                          height: 44,
                                          width: 1,
                                          color: Theme.of(context).dividerColor,
                                        ),
                                        Expanded(child: buildMetricaRow('gasto_calorico', '${alunoSelecionado?.cliente?.calories ?? '-'} kCal')),
                                      ],
                                    ),
                                    const Divider(),
                                    Row(
                                      children: [
                                        Expanded(child: buildMetricaRow('potencia_media', '${alunoSelecionado?.cliente?.averagePower ?? '-'}')),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 16,
                            ),
                            GestureDetector(
                              onTap: () {
                                Navigator.of(context).pushNamed('/telaCompartilharAulaSelfLoops',
                                    arguments: {'historicoAula': widget._controllerAulasTurmas.historicoAulas[widget.index], 'alunoSelecionado': alunoSelecionado});
                              },
                              child: const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    TreinoIcon.share_alt,
                                    size: 13,
                                  ),
                                  SizedBox(
                                    width: 4,
                                  ),
                                  DSbotaoPadrao(
                                    titulo: 'Compartilhar',
                                    tipoBotao: TipoBotao.sublinhado,
                                  ),
                                  SizedBox(
                                    width: 17,
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    );

                  case null:
                    return const SizedBox();
                  case ServiceStatus.Error:
                    return const SizedBox();
                  case ServiceStatus.Empty:
                    return const SizedBox();
                }
              }.call(),
              const SizedBox(
                height: 8.5,
              ),
            ],
          )),
    );
  }

  Widget buildMetricaRow(String titulo, String valor, {CrossAxisAlignment alignment = CrossAxisAlignment.center}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Flexible(
          child: Column(
            crossAxisAlignment: alignment,
            children: [
              DStextCaption1(
                localizedString(titulo),
                textAlign: TextAlign.center,
                maximoLinhas: 2,
                overflow: TextOverflow.ellipsis,
              ),
              DStextTitle1(
                valor,
                textAlign: TextAlign.center,
                maximoLinhas: 1,
                overflow: TextOverflow.ellipsis,
              )
            ],
          ),
        ),
      ],
    );
  }
}

class AnimatedButton extends StatefulWidget {
  Function()? onTap;
  AnimatedButton({Key? key, this.onTap}) : super(key: key);
  @override
  _AnimatedButtonState createState() => _AnimatedButtonState();
}

class _AnimatedButtonState extends State<AnimatedButton> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool rotatacaoEstaNoSentidoHorario = true;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTap() {
    widget.onTap?.call();
    rotatacaoEstaNoSentidoHorario = !rotatacaoEstaNoSentidoHorario;
    _controller.forward().then((value) => _controller.reset());
  }

  @override
  Widget build(BuildContext context) {
    return FittedBox(
      child: AnimatedBuilder(
        animation: _controller,
        builder: (BuildContext context, Widget? child) {
          return Transform.rotate(
            angle: rotatacaoEstaNoSentidoHorario ? _controller.value * math.pi : (_controller.value * -1) * math.pi,
            child: Transform(
              alignment: Alignment.center,
              transform: Matrix4.rotationZ(
                math.pi / 2,
              ),
              child: DSbotaoCircular(
                alturaIcone: 20,
                onTap: _handleTap,
                altura: 34,
                icone: TreinoIcon.exchange,
              ),
            ),
          );
        },
      ),
    );
  }
}

extension SafeSubstring on String {
  String safeSubstring(int start, [int? end]) {
    if (length >= start) {
      return end != null && length >= end ? substring(start, end) : substring(start);
    }
    return '';
  }
}
