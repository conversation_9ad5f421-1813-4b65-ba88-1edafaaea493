import 'package:after_layout/after_layout.dart';
import 'package:app_treino/appWidgets/BasicWdigetUtil.dart';
import 'package:app_treino/appWidgets/componentWidgets/TextWidgets.dart';
import 'package:app_treino/appWidgets/componentWidgets/ValidaCondicoes.dart';
import 'package:app_treino/appWidgets/componentWidgets/skeletons/SkeletonCardAula.dart';
import 'package:app_treino/screens/aulasETurmas/ItemAula.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/controlladores/ControladorExecucaoTreino.dart';
import 'package:app_treino/controlladores/controladorPlanner.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorAulaTurma.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorContratoUsuario.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/aulaTurma/AulaTurma.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/EmpresaRede.dart';
import 'package:app_treino/model/vendaplanos/TelaAcessoNegado.dart';
import 'package:app_treino/screens/aulasETurmas/TelaSelecaoContrato.dart';
import 'package:app_treino/screens/aulasETurmas/TelaSelecaoUnidade.dart';
import 'package:app_treino/screens/planner/widgets/ItemMoverEntreDias.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:collection/collection.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:ds_pacto/ds_segment.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_xlider/flutter_xlider.dart';
import 'package:get_it/get_it.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:skeleton_text/skeleton_text.dart';
import 'package:flutter/material.dart';
import 'package:badges/badges.dart' as badges;
import 'package:app_treino/Utilitario.dart';



class TelaAulasTurmas extends StatefulWidget {
  TelaAulasTurmas({Key? key}) : super(key: key);

  @override
  _TelaAulasTurmasState createState() => _TelaAulasTurmasState();
}

class _TelaAulasTurmasState extends State<TelaAulasTurmas>
    with AfterLayoutMixin<TelaAulasTurmas> {

  final ControladorPlanner _controladorPlanner = GetIt.I.get();
  List<ModalidadeSelecionada> modalidadesSelecionadas = [];
  final ExpandableController _expandableControlle = new ExpandableController(initialExpanded: true);

  @override
  void initState() {
    _onRefresh();
    restaurarFiltros();
    filtroSelecionado =  GetIt.I.get<ControladorAulaTurma>().agruparAulasPorHorario ? 'horario' : 'modalidade';
    super.initState();
  }

  @override
  void dispose() {
    _controllerAulasTurmas.textopesquisa = '';
    _controllerAulasTurmas.estaPesquisando = false;
    super.dispose();
  }

  Future<void> salvarFiltros() async{
    final prefs = await SharedPreferences.getInstance();
    prefs.setDouble('horarioFiltroInicial', _horarioFiltroInicial);
    prefs.setDouble('horarioFiltroFinal', _horarioFiltroFinal);
    prefs.setBool('mostrarAulasCheias', mostrarAulasCheias);
    prefs.setString('filtroSelecionado', filtroSelecionado);

    prefs.setStringList('modalidadesSelecionadas', modalidadesSelecionadas.map((e) => '${e.modalidade}:${e.selecionado ?? false}').toList());
  }

  Future<void> restaurarFiltros() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _horarioFiltroInicial = prefs.getDouble('horarioFiltroInicial') ?? 0;
      _horarioFiltroFinal = prefs.getDouble('horarioFiltroFinal') ?? 24;
      mostrarAulasCheias = prefs.getBool('mostrarAulasCheias') ?? true;
      filtroSelecionado = prefs.getString('filtroSelecionado') ?? 'horario';
      final modalidades = prefs.getStringList('modalidadesSelecionadas');
      if (modalidades != null) {
        modalidadesSelecionadas = modalidades.map((str) {
          final parts = str.split(':');
          return ModalidadeSelecionada(
            modalidade: parts[0],
            selecionado: parts.length > 1 ? parts[1] == 'true' : false,
          );
        }).toList();
      }
    });
  }

  final _controllerAulasTurmas = GetIt.I.get<ControladorAulaTurma>();

  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  var precisaResetarFiltros = true;
  var mostrarAulasCheiasTemp = true;
  var mostrarAulasCheias = true;

  void _onRefresh() async {
    _controllerAulasTurmas.consultarAulasTurmas(
      sucesso: () {       
        final listaSeparadaPorTipo =  groupBy(_controllerAulasTurmas.mAulasETurmas, (AulaTurma item) => item.modalidade);
        listaSeparadaPorTipo.forEach((key, value) {
          if (modalidadesSelecionadas.where((element) => element.modalidade == key).isEmpty) {
             modalidadesSelecionadas.add(ModalidadeSelecionada(modalidade: key, selecionado: true));
          }
        });
        marcarDatasCalendario();
        _refreshController.refreshCompleted();
      },
      falha: (mensagem) {
        _refreshController.refreshFailed();
        BasicWdigetUtil()
            .showDialogMensagem(context, titulo: 'Ops!', mensagem: mensagem);
      },
    );
    _controllerAulasTurmas.consultarHistoricoAulas(
      dataInicial: UtilDataHora.getPrimeiroDiaDoMes(_controllerAulasTurmas.diaConsultarAulasTurma ?? DateTime.now()),
      dataFinal: (_controllerAulasTurmas.diaConsultarAulasTurma ?? DateTime.now()).add(const Duration(days: 30)),
      page: 0,
      sucesso: () {
        marcarDatasCalendarioAulas();
         _refreshController.refreshCompleted();
      },
      falha: (err) {
        _refreshController.refreshFailed();
        BasicWdigetUtil()
            .showDialogMensagem(context, titulo: 'Ops!', mensagem: err);
      },
      carregando: () {},
    );
  }

  bool mostarMaisItens = false;
  ScrollController scrollController = ScrollController();

  final _controladorApp = GetIt.I.get<ControladorApp>();
  final _controladorCliente = GetIt.I.get<ControladorCliente>();
  final _controladorContrato = GetIt.I.get<ControladorContratoUsuario>();

  String? nomeEmpresaSelecionada(EmpresaRede? empresaSelecionada) {
    return empresaSelecionada?.nome ?? _controladorApp.empresasRede.firstWhere((element) => element.nome == _controladorCliente.mUsuarioLogado?.nomeEmpresa, orElse: () {
        return EmpresaRede();
      }).nome;
  }

  EmpresaRede? get empresaSelecionada {
    return _controllerAulasTurmas.empresaSelecionada ?? _controladorApp.empresasRede.firstWhere((element) => element.nome == _controladorCliente.mUsuarioLogado?.nomeEmpresa, orElse: () {
        return EmpresaRede();
      },);
  }

  bool calendarioRetraido = true;
  var filtroSelecionado = 'horario';
  var filtroSelecionadoTemp = 'horario';
  final TextEditingController _textController = new TextEditingController();
  

  @override
  Widget build(BuildContext context) {
    return GetIt.I.get<ControladorCliente>().bloquearNavegacao
        ? TelaAcessoNegado(tituloToolBar: GetIt.I.get<ControladorApp>().chave == '2baf2084c71d676019c4440a0e52a989' ? 'Aulas ao vivo' : 'titulolocalization.aulas_turmas', darPop: false)
        : Scaffold(
            backgroundColor: Theme.of(context).colorScheme.surface,
            resizeToAvoidBottomInset: false,
            appBar: DSappBar(
              titulo: GetIt.I.get<ControladorApp>().chave == '2baf2084c71d676019c4440a0e52a989' ? 'Aulas ao vivo' : 'titulolocalization.aulas',
              backgroundColor: Theme.of(context).colorScheme.surface,
              tipoAppbar: TipoAppBar.tituloNaEsquerda,
              actions: [
                Observer(builder: (_) {
                  return (_controllerAulasTurmas.saldoParaAulasTurmasCredito > 0)
                      ? ValidaCondicoes(
                          moduloApp: ModuloApp.MODULO_CREDITO_DE_CONTRATO,
                          child: Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: GestureDetector(
                              onTap: () {
                                abrirDetalhamentoSaldo(context, eCredito: true, saldo: _controllerAulasTurmas.saldoParaAulasTurmasCredito.toInt());
                              },
                              child: Center(
                                child: Container(
                                  width: 58,
                                  height: 34,
                                  decoration: BoxDecoration(color: Theme.of(context).cardColor, borderRadius: BorderRadius.circular(50)),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                    children: [
                                      DStextHeadline(_controllerAulasTurmas.saldoParaAulasTurmasCredito.toString()),
                                      Icon(
                                        TreinoIcon.coins,
                                        color: Theme.of(context).iconTheme.color,
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        )
                      : Container();
                }),
                Observer(builder: (context) {
                  switch (_controladorContrato.mStatusConsultaContratos) {
                    case ServiceStatus.Done:
                      int quantidadeContratos = _controladorContrato.mListaContratos.where((element) => element?.situacao == 'Ativo').length;
                      return Visibility(
                          visible: quantidadeContratos > 1,
                          child: Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: Center(
                              child: badges.Badge(
                                badgeContent: DStextCaption1(quantidadeContratos.toString(), eHeavy: true, corCustomizada: corQuandoFundoForGradiente(context)),
                                position: badges.BadgePosition.topEnd(top: -10, end: -5),
                                ignorePointer: false,
                                badgeAnimation: const badges.BadgeAnimation.slide(toAnimate: true, animationDuration: Duration(seconds: 1)),
                                badgeStyle: badges.BadgeStyle(badgeColor: Theme.of(context).primaryColor, padding: const EdgeInsets.all(6), shape: badges.BadgeShape.circle),
                                child: DSbotaoCircular(
                                    alturaIcone: 21,
                                    onTap: () {
                                      abrirModalSelecaoContrato(context, salvou: () {
                                        _onRefresh();
                                      });
                                      // Navigator.of(context).pushNamed('/telaSelecaoContrato');
                                    },
                                    icone: TreinoIcon.file_edit_alt),
                              ),
                            ),
                          ));
                    default:
                      return const SizedBox();
                  }
                }),
                Center(
                    child: Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: DSbotaoPesquisa(
                            hintText: localizedString('busque_pelo_nome'),
                            largura: tamanhoBarraPesquisa(),
                            onTextChanged: (text) {
                              _controllerAulasTurmas.textopesquisa = text;
                              setState(() {
                                _controllerAulasTurmas.estaPesquisando = text.isNotEmpty;
                              });
                            },
                            textController: _textController)))
              ],
            ),
            body: SmartRefresher(
              enablePullDown: true,
              enablePullUp: false,
              onRefresh: _onRefresh,
              physics: const BouncingScrollPhysics(),
              header: const WaterDropHeader(),
              controller: _refreshController,
              child: SingleChildScrollView(
                controller: scrollController,
                child: Column(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        itemCalendario(context),
                        itemMinhasAulas(),
                        cardHistorico(),
                        _controllerAulasTurmas.estaPesquisando
                            ? Container()
                            : !(GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_ESCONDER_QUANTIDADE_SALDO_REPOSICOES).habilitado ?? false)
                                ? itemSaldoReposicao(context)
                                : Container(),
                      ],
                    ),
                    Observer(
                        key: const Key('TelaAulaTurmas'),
                        builder: (_) {
                          switch (_controllerAulasTurmas.consultaAulaTurmas) {
                            case ServiceStatus.Waiting:
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(top: 16, bottom: 24, left: 16),
                                    child: skeletonChip(height: 32, width: 120),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                                    child: ListView.builder(
                                      itemCount: 6,
                                      shrinkWrap: true,
                                      itemBuilder: (context, index) {
                                        return Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            const SkeletonCardAula(),
                                            Padding(
                                              padding: const EdgeInsets.only(top: 24, bottom: 24),
                                              child: skeletonChip(height: 32, width: 120),
                                            ),
                                          ],
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              );
                            case ServiceStatus.Error:
                              return Padding(
                                padding: const EdgeInsets.all(32),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Image.asset(
                                      'assets/images/onboard1.png',
                                      height: 140,
                                    ),
                                    const SizedBox(
                                      height: 8,
                                    ),
                                    TextHeadLine1(
                                      'aviso_erro',
                                      textAlign: TextAlign.center,
                                    ),
                                    TextBody2(
                                      'aviso_timeout',
                                      textAlign: TextAlign.center,
                                    )
                                  ],
                                ),
                              );
                            default:
                              if (_controllerAulasTurmas.alunoEmFerias || _controllerAulasTurmas.alunoTrancado || _controllerAulasTurmas.alunoAtestado) {
                                return Padding(
                                  padding: const EdgeInsets.all(32),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SizedBox(height: 130, width: 130, child: Image.asset('assets/images/empty/empty_ferias.png')),
                                      const SizedBox(
                                        height: 20,
                                      ),
                                      TextHeader(
                                        'pausa_treino',
                                        textAlign: TextAlign.justify,
                                      ),
                                      const SizedBox(
                                        height: 24,
                                      ),
                                      TextBody1(
                                        'contrato_pausado',
                                        textAlign: TextAlign.center,
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.fromLTRB(50, 24, 50, 0),
                                        child: BotaoPrimario(
                                          onTap: () {
                                            Navigator.of(context).pushNamed('/telaContratosAluno');
                                          },
                                          value: localizedString('meu_contrato'),
                                        ),
                                      )
                                    ],
                                  ),
                                );
                              } else if (_controllerAulasTurmas.estaPesquisando) {
                                if (_controllerAulasTurmas.mAulasETurmasAgendadas.where((element) => _controllerAulasTurmas.filtrarAulas(element)).length == 0 &&
                                    _controllerAulasTurmas.mAulasETurmas.where((element) => _controllerAulasTurmas.filtrarAulas(element)).length == 0) {
                                  return Padding(
                                    padding: const EdgeInsets.fromLTRB(32, 64, 32, 0),
                                    child: Column(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.only(top: 0, bottom: 8),
                                          child: DSsvg(imagem: Imagem.busca),
                                        ),
                                        DStextSubheadline(
                                          'erro_pesquisa_aulas_titulo',
                                          textAlign: TextAlign.center,
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.only(top: 8),
                                          child: DStextBody(
                                            'erro_pesquisa_aulas_mensagem',
                                            eHeavy: false,
                                            ePrimario: false,
                                            textAlign: TextAlign.center,
                                          ),
                                        )
                                      ],
                                    ),
                                  );
                                } else {
                                  List<Widget> widgets = [];
                                  var teste = _controllerAulasTurmas.mAulasETurmas
                                      .where((element) => _controllerAulasTurmas.estaPesquisando ? _controllerAulasTurmas.filtrarAulas(element) : true)
                                      .toList();
                                  if (!mostrarAulasCheias) {
                                    teste.removeWhere((element) => (element.vagasRestantes ?? 0) <= 0);
                                  }
                                  for (final element in teste) {
                                    widgets.add(Padding(
                                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
                                        child: ItemAula(
                                          aulaTurma: element,
                                          minhasAulas: false,
                                          onError: _onRefresh,
                                          onDesmarcarPresenca: _onRefresh,
                                          onAulaAgendada: () async {
                                            _onRefresh();
                                          },
                                        )));
                                  }
                                  return Padding(padding: const EdgeInsets.only(bottom: 140), child: Wrap(children: widgets));
                                }
                              }
                              return Column(children: [
                                _controllerAulasTurmas.estaPesquisando
                                    ? Container()
                                    : Padding(
                                        padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Flexible(child: DStextHeadline('aulas_disponiveis')),
                                            Padding(
                                              padding: const EdgeInsets.only(left: 16),
                                              child: badges.Badge(
                                                badgeContent: DStextCaption1(modalidadesSelecionadas.where((element) => element.selecionado ?? true).length.toString(),
                                                    eHeavy: true, corCustomizada: corQuandoFundoForGradiente(context)),
                                                position: badges.BadgePosition.topEnd(top: -5, end: -5),
                                                showBadge: modalidadesSelecionadas.where((element) => element.selecionado ?? true).length >= 1,
                                                ignorePointer: false,
                                                badgeAnimation: const badges.BadgeAnimation.slide(toAnimate: true, animationDuration: Duration(seconds: 1)),
                                                badgeStyle: badges.BadgeStyle(
                                                    badgeColor: Theme.of(context).primaryColor,
                                                    padding: modalidadesSelecionadas.where((element) => element.selecionado ?? true).length >= 10
                                                        ? const EdgeInsets.all(4)
                                                        : const EdgeInsets.all(6),
                                                    shape: badges.BadgeShape.circle),
                                                child: DSbotaoPadrao(
                                                    titulo: 'filtrar',
                                                    onTap: () {
                                                      _horarioFiltroInicialTemp = _horarioFiltroInicial;
                                                      _horarioFiltroFinalTemp = _horarioFiltroFinal;
                                                      mostrarAulasCheiasTemp = mostrarAulasCheias;
                                                      filtroSelecionadoTemp = filtroSelecionado;
                                                      abrirFiltroAulas(context, salvou: () async {
                                                        _horarioFiltroInicial = _horarioFiltroInicialTemp;
                                                        _horarioFiltroFinal = _horarioFiltroFinalTemp;
                                                        filtroSelecionado = filtroSelecionadoTemp;
                                                        _controllerAulasTurmas.gravarVisualizacaoAgrupamento(filtroSelecionadoTemp == 'horario');
                                                        mostrarAulasCheias = mostrarAulasCheiasTemp;
                                                        await salvarFiltros();
                                                        Navigator.of(context).pop();
                                                        setState(() {});
                                                        _onRefresh();
                                                      });
                                                    },
                                                    icone: TreinoIcon.filter,
                                                    tamanhoReduzido: true,
                                                    posicaoIcone: PosicaoIcone.esquerda,
                                                    tipoBotao: TipoBotao.secundario),
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                criarAulasSeparadasPorModalidade()
                              ]);
                          }
                        }),
                    Observer(builder: (context) {
                      return GetIt.I.get<ControladorExecucaoTreino>().treinoEmExecucao?.atividades != null ? const SizedBox(height: 80) : const SizedBox(height: 0);
                    })
                  ],
                        ),
              ),
            ),
          );
  }

  double? tamanhoBarraPesquisa() {
    if (_controllerAulasTurmas.saldoParaAulasTurmasCredito > 0) {
      if (_controladorContrato.mListaContratos.where((element) => element?.situacao == 'Ativo').length > 1) {
        return MediaQuery.of(context).size.width - 140;
      } else {
        return MediaQuery.of(context).size.width - 100;
      }
    } else {
      if (_controladorContrato.mListaContratos.where((element) => element?.situacao == 'Ativo').length > 1) {
        return MediaQuery.of(context).size.width - 76;
      } else {
        return MediaQuery.of(context).size.width - 32;
      }
    }
  }

  final List<DateTime> _eventos = [];
  final List<DateTime> _eventosAulas = [];

  marcarDatasCalendario() {
    _eventos.clear();
    for (final turmaAgendada in _controllerAulasTurmas.mTurmasAgendadas) {
      final DateTime? turmaAgendadaDate = UtilDataHora.parseNunToDate(turmaAgendada.diaDate);
      if (turmaAgendadaDate != null && (turmaAgendadaDate.isAfter(DateTime.now()) || UtilDataHora.dataEigualDaDeHoje(dateTime: turmaAgendadaDate))) {
        _eventos.add(turmaAgendadaDate);
      }
    }
    setState(() {});
  }

  marcarDatasCalendarioAulas() {
    _eventosAulas.clear();
    for(final aulaAgendada in _controllerAulasTurmas.historicoAulas) {
      final DateTime? aulaAgendadaDate = UtilDataHora.parseStringToDate(aulaAgendada.dia);
      if(aulaAgendadaDate != null && !(aulaAgendada.aulaTurma ?? false)) {
        if (aulaAgendadaDate.isAfter(DateTime.now()) || UtilDataHora.dataEigualDaDeHoje(dateTime: aulaAgendadaDate)) {
          _eventosAulas.add(aulaAgendadaDate);
        }
      }
    }
    setState(() {});
  }


  Widget itemSaldoReposicao(BuildContext context) {
    return Observer(builder: (_) {
      return (_controllerAulasTurmas.saldoAulaColetiva?.disponiveis == 0 &&
              _controllerAulasTurmas.saldoAulaColetiva?.utilizadas == 0 &&
              _controllerAulasTurmas.saldoAulaColetiva?.expiradas == 0 &&
              _controllerAulasTurmas.saldoParaAulasTurmasReposicao == 0)
          ? Container()
          : ExpandablePanel(
              collapsed: DScard(
                  paddingInterno: const EdgeInsets.all(16),
                  paddingExterno: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                  onTap: () {
                    _expandableControlle.toggle();
                  },
                  child: Column(
                    children: [
                      headerItemSaldoReposicao(context, true),
                      const SizedBox(
                        height: 16,
                      ),
                      DScard(
                        categoria: CategoriaCard.secundario,
                        paddingInterno: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            DStextSubheadline('aulas_coletivas'),
                            const SizedBox(height: 8),
                            Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
                              Column(
                                children: [
                                  DStextCaption1('disponivel'),
                                  DStextTitle2('${_controllerAulasTurmas.saldoAulaColetiva?.disponiveis}'),
                                ],
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 8, right: 8),
                                child: Container(
                                  height: 50,
                                  width: 1,
                                  color: Theme.of(context).dividerColor,
                                ),
                              ),
                              Column(
                                children: [
                                  DStextCaption1('utilizados'),
                                  DStextTitle2('${_controllerAulasTurmas.saldoAulaColetiva?.utilizadas}'),
                                ],
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 8, right: 8),
                                child: Container(
                                  height: 50,
                                  width: 1,
                                  color: Theme.of(context).dividerColor,
                                ),
                              ),
                              Column(
                                children: [
                                  DStextCaption1('expirados'),
                                  DStextTitle2('${_controllerAulasTurmas.saldoAulaColetiva?.expiradas}'),
                                ],
                              ),
                            ]),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      DScard(
                        categoria: CategoriaCard.secundario,
                        paddingInterno: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            DStextSubheadline('turmas'),
                            const SizedBox(height: 8),
                            Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
                              Column(
                                children: [
                                  DStextCaption1(UtilitarioApp.sentenseCase(localizedString('reposicao_disponivel'))),
                                  DStextTitle2(_controllerAulasTurmas.saldoParaAulasTurmasReposicao.toString()),
                                  GetIt.I.get<ControladorApp>().chave == 'ac7ac10cbeba800c78f4de2922bd1e1b'
                                      ? DStextCaption1(
                                          'ate_o_final_do_seu_plano',
                                          eHeavy: false,
                                          ePrimario: false,
                                        )
                                      : Container()
                                ],
                              ),
                            ]),
                          ],
                        ),
                      )
                    ],
                  )),
              expanded: DScard(
                  paddingInterno: const EdgeInsets.all(16),
                  paddingExterno: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                  onTap: () {
                    _expandableControlle.toggle();
                  },
                  child: headerItemSaldoReposicao(context, false)),
              controller: _expandableControlle,
            );
    });
  }

  Column headerItemSaldoReposicao(BuildContext context, bool up) {
    return Column(
      children: [
        Row(
          children: [
            const DSbotaoCircular(
              categoria: Categoria.secundario,
              alturaIcone: 22,
              icone: TreinoIcon.coins,
              altura: 48,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DStextSubheadline('meus_creditos'),
                  SizedBox(
                      width: MediaQuery.of(context).size.width - 160,
                      child: DStextCaption1(
                        'meus_creditos_descricao',
                        eHeavy: false,
                        ePrimario: false,
                      )),
                ],
              ),
            ),
            const Spacer(),
            DSbotaoCircular(
              categoria: Categoria.comBorda,
              alturaIcone: 20,
              icone: (up) ? TreinoIcon.angle_up : TreinoIcon.angle_down,
              altura: 34,
            ),
          ],
        ),
      ],
    );
  }

  Widget criarAulasSeparadasPorModalidade() {
    List<Widget> grupos = [];
    var listaModalidadesNaoSelecionadas = modalidadesSelecionadas.where((element) => !(element.selecionado ?? false)).toList();
    for (final modalidade in listaModalidadesNaoSelecionadas) {
      _controllerAulasTurmas.mAulasETurmas.removeWhere((element) => element.modalidade == modalidade.modalidade);
    }
    if (!mostrarAulasCheias) {
      _controllerAulasTurmas.mAulasETurmas.removeWhere((element) => (element.vagasRestantes ?? 0) <= 0);
    }
     _controllerAulasTurmas.mAulasETurmas.removeWhere((element) {
      int horarioInicio = int.tryParse((element.inicio ?? '00:00').split(':')[0]) ?? 0;
      return _horarioFiltroInicial > horarioInicio;
    });
      _controllerAulasTurmas.mAulasETurmas.removeWhere((element) {
      int horarioFinal = int.tryParse((element.fim ?? '00:00').split(':')[0]) ?? 0;
      return _horarioFiltroFinal < horarioFinal;
    });
     final listaAulasFavoritas = _controllerAulasTurmas.mAulasETurmas.where((element) => element.favorita ?? false).toList();
     if (listaAulasFavoritas.isNotEmpty) {
       grupos.add(Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DStextSubheadline('aulas_favoritas'),
                ],
              ),
            ),
            Padding(
            padding: const EdgeInsets.only(top: 16, bottom: 24),
            child: SizedBox(
                height: 200,
                width: MediaQuery.of(context).size.width,
                child: Swiper(
                loop: false,
                viewportFraction: 0.92,
                itemCount: listaAulasFavoritas.length,
                itemBuilder: (_, i) {
                  return Padding(
                    padding: const EdgeInsets.only(left: 4, right: 4, bottom: 20),
                    child: ItemAula(aulaTurma: listaAulasFavoritas[i], minhasAulas: false,onDesmarcarPresenca: _onRefresh, 
                    onAulaAgendada: () async {
                      _onRefresh();
                    }),
                  );
                },
                pagination:  listaAulasFavoritas.length > 1 ? SwiperPagination(
                  builder: DotSwiperPaginationBuilder(
                    activeColor: Theme.of(context).primaryColor,
                    size: 6,
                    activeSize: 8,
                    color: Theme.of(context).dividerColor),
                    alignment: Alignment.bottomCenter,
                    margin: const EdgeInsets.only(top: 0)) : null,
                              
              ) 
            ),
          ),
          ],
        ));
     }
    if (filtroSelecionado == 'horario') {
        final listaSeparadaPorHorario =  groupBy(_controllerAulasTurmas.mAulasETurmas, (AulaTurma item) => item.inicio ?? '00:00');      
        listaSeparadaPorHorario.forEach((key, value) {
        grupos.add(Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: DStextSubheadline(capitalize(key.toString())),
            ),
            Padding(
            padding: const EdgeInsets.only(top: 16, bottom: 24),
            child: SizedBox(
                height: 200,
                width: MediaQuery.of(context).size.width,
                child: Swiper(
                loop: false,
                viewportFraction: 0.92,
                itemCount: value.length,
                itemBuilder: (_, i) {
                  return Padding(
                    padding: const EdgeInsets.only(left: 4, right: 4, bottom: 20),
                    child: ItemAula(aulaTurma: value[i], minhasAulas: false,onDesmarcarPresenca: (){
                      _onRefresh();
                    }, onAulaAgendada: () async {
                        _onRefresh();
                      }),
                  );
                },
                pagination:  value.length > 1 ? SwiperPagination(
                  builder: DotSwiperPaginationBuilder(
                    activeColor: Theme.of(context).primaryColor,
                    size: 6,
                    activeSize: 8,
                    color: Theme.of(context).dividerColor),
                    alignment: Alignment.bottomCenter,
                    margin: const EdgeInsets.only(top: 0)) : null,
                              
              ) 
            ),
          ),
          ],
        ));
      });
    } else {      
      final listaSeparadaPorTipo =  groupBy(_controllerAulasTurmas.mAulasETurmas, (AulaTurma item) => item.modalidade);
      listaSeparadaPorTipo.forEach((key, value) {
        grupos.add(Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: DStextSubheadline(capitalize(key.toString())),
            ),
             Padding(
            padding: const EdgeInsets.only(top: 16, bottom: 24),
            child: SizedBox(
                height: 200,
                width: MediaQuery.of(context).size.width,
                child: Swiper(
                loop: false,
                viewportFraction: 0.92,
                itemCount: value.length,
                itemBuilder: (_, i) {
                  return Padding(
                    padding: const EdgeInsets.only(left: 4, right: 4, bottom: 20),
                    child: ItemAula(aulaTurma: value[i], minhasAulas: false,onDesmarcarPresenca: _onRefresh, 
                    onAulaAgendada: () async {
                      _onRefresh();
                    }),
                  );
                },
                pagination:  value.length > 1 ? SwiperPagination(
                  builder: DotSwiperPaginationBuilder(
                    activeColor: Theme.of(context).primaryColor,
                    size: 6,
                    activeSize: 8,
                    color: Theme.of(context).dividerColor),
                    alignment: Alignment.bottomCenter,
                    margin: const EdgeInsets.only(top: 0)) : null,
              ) ))
          ],
        ));
      });
    }
     if (grupos.isEmpty) {
        grupos.add(Padding(
          padding: const EdgeInsets.fromLTRB(32, 64, 32, 0),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 0, bottom: 8),
                child: DSsvg(imagem: Imagem.calendario),
              ),
              DStextSubheadline('sem_aulas_disponiveis'),
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: DStextBody('nao_foram_encotradas_aulas', eHeavy: false, ePrimario: false, textAlign: TextAlign.center,),
              )
            ],
          ),
        ));
     }
    return Padding(
      padding: const EdgeInsets.only(bottom: 128),
      child: Wrap(children: grupos),
    );
  }

  Widget itemMinhasAulas() {
    return Observer(builder: (context) {
      if (_controllerAulasTurmas.mAulasETurmasAgendadas.isNotEmpty) {
        var minhasAulas = _controllerAulasTurmas.mAulasETurmasAgendadas.where((element) => _controllerAulasTurmas.estaPesquisando ? _controllerAulasTurmas.filtrarAulas(element) : true).toList();
        return _controllerAulasTurmas.mAulasETurmasAgendadas.isEmpty || minhasAulas.isEmpty ? Container() : DScard(
        paddingInterno: const EdgeInsets.fromLTRB(12, 16, 12, 16),
        paddingExterno: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(children: [
            const DSbotaoCircular(icone: TreinoIcon.presentation_check, altura: 29, categoria: Categoria.secundario,),
            Padding(
              padding: const EdgeInsets.only(left: 10),
              child: DStextSubheadline('aulasE_turmas.minhas_aulas_args'),
            )
          ],
        ),
         _controllerAulasTurmas.mAulasETurmasAgendadas.isEmpty  ? Padding(
           padding: const EdgeInsets.symmetric(vertical:16 ),
           child: DStextCaption1('Sem aulas agendadas para a data selecionada',eHeavy: false,),
         ) : Padding(
           padding: const EdgeInsets.only(top: 16),
           child: SizedBox(
                height: 213,
                width: MediaQuery.of(context).size.width - 64,
                child: Swiper(
                  loop: false,               
                  itemBuilder: (BuildContext context, int index) {
                    return Padding(
                      padding:  const EdgeInsets.only(bottom: 32),
                      child: ItemAula(
                        secundaria: true,
                        minhasAulas: true,
                        aulaTurma: _controllerAulasTurmas.estaPesquisando ?  minhasAulas[index]:  _controllerAulasTurmas.mAulasETurmasAgendadas[index],
                        onDesmarcarPresenca: _onRefresh,
                        onAulaAgendada: () async {
                          _onRefresh();
                        },
                      ),
                    );
                  },
                  scale: 0.9,
                  itemCount: _controllerAulasTurmas.estaPesquisando ?  minhasAulas.length:  _controllerAulasTurmas.mAulasETurmasAgendadas.length,
                  pagination: SwiperPagination(
                    margin: const EdgeInsets.only(top: 0),
                      builder: DotSwiperPaginationBuilder(
                          activeColor: Theme.of(context).primaryColor,
                          size: 6,
                          activeSize: 6,
                          color: Theme.of(context).dividerColor),
                      alignment: Alignment.bottomCenter),
                ),
              ),
            ),   
          ],
        )); 
      }else {
        return Container();
      }
    },);
  }
  
  Widget cardHistorico(){
        return DScard(
          paddingExterno: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          paddingInterno: const EdgeInsets.fromLTRB(16, 12, 16, 12),
        onTap: () {
          Navigator.pushNamed(context, '/telaHistoricoAulas');
        },
        child: Row(

          children: [
            Expanded(
              child: Row(
                children: [
                  const DSbotaoCircular(
                    categoria: Categoria.secundario,
                    alturaIcone: 22,
                    icone: TreinoIcon.calender_rendo,
                    altura: 48,
                  ),
                  Flexible(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DStextSubheadline('historico_de_aulas'),
                          DStextCaption1(
                            'veja_suas_aulas_realizadas',
                            maximoLinhas: 1,
                            overflow: TextOverflow.ellipsis,
                            eHeavy: false,
                            ePrimario: false,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const DSbotaoCircular(
              categoria: Categoria.comBorda,
              alturaIcone: 20,
              icone: TreinoIcon.angle_right,
              altura: 34,
            ),
          ],
        ));
  }
  Widget itemCalendario(BuildContext context) {
    return AnimatedContainer(
      margin: const EdgeInsets.fromLTRB(16, 6, 16, 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        color: DSLib.theme == ThemeMode.dark
        ? const Color(0xff202020)
        : const Color(0xffFFFFFF)
      ),
      duration: const Duration(milliseconds: 300),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Visibility(
            visible: (_controladorApp.getConfiguracaoApp(ModuloApp.MODULO_SELECAO_UNIDADE_AULAS).habilitado ?? false) && _controladorApp.empresasRede.length > 1,
            child: Column(
              children: [
                Semantics(
                    identifier: 'selecionar_unidade',
                  child: InkWell(
                    onTap: () {
                      abrirSelecaoUnidade(context, salvou: () {
                        setState(() {
                          precisaResetarFiltros = true;
                        });
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                      child: Container(
                        height: 34,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: DSLib.theme == ThemeMode.dark
                            ? const Color(0xff202020)
                            : const Color(0xffF0F0F0)
                        ),
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(8, 4, 2, 4),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              const Icon(TreinoIcon.map_marker),
                              Padding(
                                padding: const EdgeInsets.only(left: 4),
                                child: DStextSubheadline('unidade'),
                              ),
                              const Spacer(),
                              SizedBox(
                                width: MediaQuery.of(context).size.width - 190,
                                child: DStextCaption1(UtilitarioApp.sentenseCase(nomeEmpresaSelecionada(_controllerAulasTurmas.empresaSelecionada) ?? ''), eHeavy: false, ePrimario: false, overflow: TextOverflow.ellipsis, textAlign: TextAlign.right,)),
                              const Icon(TreinoIcon.angle_right)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                 const Padding(
                   padding: EdgeInsets.fromLTRB(16, 8, 16, 0),
                   child: Divider(),
                 ),
              ],
            ),
          ),
          calendarioRetraido ? Padding(
            padding: const EdgeInsets.only(top: 8, bottom: 8),
            child: ItemMoverEntreDias(
              callback: (data) {
                analytic(EventosKey.aulas_pressionou_dia);
                _controllerAulasTurmas.diaConsultarAulasTurma = data;
                setState(() {
                  precisaResetarFiltros = true;
                });
                _onRefresh();
                
              },
            ),
          ) : DScalendario(
            bloquearSelecionarDataPassada: true,
            dataLimitePassada: DateTime.now().subtract(const Duration(days: 1)),
            paddingInterno: 0,
            datasComAgendamentos: _eventos,
            datasComAgendamentosAulas: _eventosAulas,
            onPressed: (date, events) {     
              analytic(EventosKey.aulas_pressionou_dia);
              _controllerAulasTurmas.diaConsultarAulasTurma = date;
              _controladorPlanner.dataSelecionada = date;
              setState(() {
                precisaResetarFiltros = true;
              });
              _onRefresh();  
          }, dataSelecionada: _controllerAulasTurmas.diaConsultarAulasTurma ?? DateTime.now()),
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Row(
              children: [
                const Spacer(),
                DSbotaoPadrao(
                  icone: calendarioRetraido ?  TreinoIcon. angle_down : TreinoIcon.angle_up,
                  posicaoIcone: PosicaoIcone.direita,
                  tipoBotao: TipoBotao.sublinhado,
                  titulo: calendarioRetraido ? 'expandir_calendario' : 'retrair_calendario', onTap:  () {
                   setState(() {
                        marcarDatasCalendario();
                        marcarDatasCalendarioAulas();
                        calendarioRetraido = !calendarioRetraido;
                      });
                },),
                const Spacer()
              ],
            ),
          )         
         
        ],
    ),
    );
  }

  Widget chip({Function()? onPressed, IconData? icon, String? title}) {
    return GestureDetector(onTap: () {
        onPressed?.call();},
      child: Container(child: Center(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 2),
            child: Icon(icon, color: Colors.white, size: 14,),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 6),
            child: TextButtonApp(UtilitarioApp.sentenseCase(title ?? ''), customColor: Colors.white,),
          )
        ],),
      ), decoration: BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: const BorderRadius.all(Radius.circular(50)), boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).primaryColor.withAlpha(50),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      )
                    ],),
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 0)),
    );
  }

  Widget skeletonChip({double? height, double? width}) {
    return Container(height: height, width: width, decoration: BoxDecoration(color: Theme.of(context).cardColor, borderRadius: const BorderRadius.all(Radius.circular(50))),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(35, 12, 17, 12),
        child: SkeletonAnimation(borderRadius: const BorderRadius.all(Radius.circular(50)),
          child: Container(decoration: BoxDecoration(color: Theme.of(context).canvasColor, borderRadius: const BorderRadius.all(Radius.circular(50))))),
      ),);
  }

  var trocouUnidade = false;

  abrirSelecaoUnidade(BuildContext context, {required Function()? salvou}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: false,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, StateSetter setState) {
            return SafeArea(
              child: DScard(
                paddingExterno: const EdgeInsets.fromLTRB(16, 64, 16, 8),
                child: SizedBox(
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.fromLTRB(12, 12, 12, 0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: 28,
                            ),
                            DStextHeadline('selecionar_unidade'),
                            DSbotaoCircular(
                              altura: 28,
                              icone: TreinoIcon.times,
                              categoria: Categoria.secundario,
                              onTap: () {
                                Navigator.of(context).pop();
                                if (trocouUnidade){
                                  _onRefresh(); 
                                  trocouUnidade = false;
                                }  
                              },
                            )
                          ],
                        ),
                      ),
                      const Divider(),
                      TelaSelecaoUnidade(onTap: () {
                        salvou?.call();
                        setState(() {
                          trocouUnidade = true;
                        });
                        Navigator.of(context).pop();
                        if (trocouUnidade){
                          _onRefresh();
                          trocouUnidade = false;
                        }
                      },),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }


  abrirDetalhamentoSaldo(BuildContext context, {required bool eCredito, required int saldo}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: false,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, StateSetter setState) {
            return SafeArea(
              child: SizedBox(
                height: 560,
                child: DScard(
                  paddingExterno: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Spacer(),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(12, 12, 12, 0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: 28,
                            ),
                            DStextHeadline(eCredito ? 'meus_creditos' :'minhas_reposicoes'),
                            DSbotaoCircular(
                              altura: 28,
                              icone: TreinoIcon.times,
                              categoria: Categoria.secundario,
                              onTap: () {
                                Navigator.of(context).pop();
                                if (trocouUnidade){
                                  _onRefresh(); 
                                  trocouUnidade = false;
                                }  
                              },
                            )
                          ],
                        ),
                      ),
                      const Divider(),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                        child: Column(
                          children: [
                            DSsvg(imagem: Imagem.moedas, altura: 220, largura: 220,),
                            DStextHeadline(eCredito ? 'creditos' : 'saldo_de_reposicao'),
                            DStextBody(eCredito ? ('seu_contrato_e_a_base_de_credito') : 'saldo_proveniente_de_uma_reposicao', eHeavy: false, ePrimario: false, textAlign: TextAlign.center,),
                            const Divider(),
                             Padding(
                               padding: const EdgeInsets.only(bottom: 16, top: 8),
                               child: Row(
                                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                 children: [
                                   DStextHeadline('saldo_atual'),
                                   Container(height: 29,
                                   width: 59,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(60),
                                    color: Theme.of(context).dividerColor
                                   ), child: Padding(
                                     padding: const EdgeInsets.only(left: 12, right: 8),
                                     child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        DStextBody(saldo.toString()),
                                        const Icon(TreinoIcon.coins, size: 20,)
                                      ],
                                     ),
                                   ),),
                                 ],
                               ),
                             ),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 14),
                              child: DSbotaoPadrao(titulo: localizedString('confirm'), onTap: () => Navigator.of(context).pop(),),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }


  abrirModalSelecaoContrato(BuildContext context, {required Function()? salvou}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, StateSetter setState) {
            return SafeArea(
              child: DScard(
                paddingExterno: const EdgeInsets.fromLTRB(16, 64, 16, 8),
                child: SizedBox(
                  child: Wrap(
                    runSpacing: 16,
                    children: [
                      Padding(
                        padding: const EdgeInsets.fromLTRB(12, 12, 12, 0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: 28,
                            ),
                            Flexible(
                              child: Column(
                                children: [
                                  DStextHeadline('selecionar_contrato'),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 10),
                                    child: DStextBody('notamos_que_voce_tem_mais_de_um_contrato', eHeavy: false, textAlign: TextAlign.center, ePrimario: false,),
                                  )
                                ],
                              ),
                            ),
                            DSbotaoCircular(
                              altura: 28,
                              icone: TreinoIcon.times,
                              categoria: Categoria.secundario,
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                            )
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 0),
                        child: Container(height: 1, color: Theme.of(context).dividerColor,),
                      ),
                      TelaSelecaoContrato(onSelecionado: _onRefresh)
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  double _horarioFiltroInicial = 0;
  double _horarioFiltroFinal = 24;
  double _horarioFiltroInicialTemp = 0;
  double _horarioFiltroFinalTemp = 24;

  final ExpandableController _expandableControlleHorario = new ExpandableController(initialExpanded: true);
  final ExpandableController _expandableControlleModalidades = new ExpandableController(initialExpanded: true);
  final ExpandableController _expandableControlleVisualizacao = new ExpandableController(initialExpanded: true);

  abrirFiltroAulas(BuildContext context, {required Function()? salvou}) {
    DSBottomSheet().exibirAlerta(context,  localizedString('filters'), 
    StatefulBuilder(
        builder: (context, setState) {
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.83,
          child: Column(
            children: [
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.73,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                       Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                        child: DSCardExpansivel( context, null, categoriaCardExpansivel: CategoriaCard.secundario, 
                          body: Padding(
                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                            child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: DStextSubheadline('agrupar_por', eHeavy: false,),
                            ),
                            Center(
                              child: DSsegmentedSliding(
                              fixedWidth: (MediaQuery.of(context).size.width - 104) / 2,
                              secundario: true,
                              initialValue: filtroSelecionadoTemp,
                              children:  {'horario': DStextBody('horario_de_inicio'), 'modalidade': DStextBody('modalidade')}, onValueChanged: (value) {
                                  filtroSelecionadoTemp = value.toString();
                                },),
                            ),
                            const Padding(
                              padding: EdgeInsets.only(top: 8),
                              child: Divider(),
                            ),
                             Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                               children: [
                                 DStextSubheadline('mostar_aulas_cheias', eHeavy: false,),
                                    CupertinoSwitch(
                                    activeTrackColor: Theme.of(context).primaryColor,
                                    value: mostrarAulasCheiasTemp,
                                    onChanged: (value) {
                                       setState(() {
                                        mostrarAulasCheiasTemp = value;
                                       });
                                    },)
                               ],
                             ),
                          ],
                      ),
                        ),
                      titulo: 'visualizacao_das_aulas', expandableController: _expandableControlleVisualizacao),
                    ),
                      Padding(
                        padding: const EdgeInsets.only(top: 16, left: 16, right: 16),
                        child: DSCardExpansivel(
                                  context, DSchip(titulo: '${_horarioFiltroInicialTemp.toStringAsFixed(0)}h - ${_horarioFiltroFinalTemp.toStringAsFixed(0)}h', tipoChip: TipoChip.terciario,), categoriaCardExpansivel: CategoriaCard.secundario, 
                                    body: Column(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.only(left: 12, right: 12),
                                          child: FlutterSlider(
                                              values: [_horarioFiltroInicialTemp, _horarioFiltroFinalTemp],                                  
                                              handlerHeight: 22,
                                              handlerWidth: 44,
                                              rangeSlider: true,
                                              minimumDistance: 1,
                                              trackBar:  FlutterSliderTrackBar(
                                                inactiveTrackBar: BoxDecoration(
                                                borderRadius: BorderRadius.circular(20),
                                              ),
                                              activeTrackBar: BoxDecoration(
                                                  borderRadius: BorderRadius.circular(4),
                                                  color: Theme.of(context).primaryColor),
                                                activeTrackBarHeight: 6,
                                                inactiveTrackBarHeight: 6,
                                              ),
                                              handler: handlerSlider(context),                                    
                                              rightHandler: handlerSlider(context),
                                              max: 24,
                                              min: 0,
                                              tooltip: FlutterSliderTooltip(
                                                format: (value) {
                                                  return '${double.parse(value).toStringAsFixed(0)}h ';
                                                },
                                                boxStyle: FlutterSliderTooltipBox(
                                                  decoration: BoxDecoration(
                                                      color: Theme.of(context).colorScheme.surface,
                                                        borderRadius: BorderRadius.circular(8),
                                                        border: Border.all(width: 1, color: Theme.of(context).primaryColor))
                                                    ),
                                                  textStyle: DStextUtil.styleCaption1(eHeavy: true, ePrimario: true),
                                                ),
                                                onDragging: (handlerIndex, lowerValue, upperValue) {
                                                  _horarioFiltroInicialTemp = lowerValue;
                                                  _horarioFiltroFinalTemp = upperValue;
                                                  setState(() {});
                                                },
                                              ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                DStextCaption1('0h', eHeavy: false,),
                                                DStextCaption1('24h', eHeavy: false,)
                                              ],
                                            ),
                                          )
                                      ],
                                    ),
                                    titulo: 'horario_das_aulas', expandableController: _expandableControlleHorario)),                        
                                    modalidadesSelecionadas.isEmpty ? Container() :
                                      Padding(
                                        padding: const EdgeInsets.all(16),
                                        child: DSCardExpansivel( context, 
                                        DSchip(
                                    titulo: localizedString('de_ativas',args: [modalidadesSelecionadas.where((element) => element.selecionado ?? true).length.toString(), modalidadesSelecionadas.length.toString()]), tipoChip: TipoChip.terciario),categoriaCardExpansivel: CategoriaCard.secundario,    
                                          body: Padding(
                                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                            child: criarModalidadesSelecao(() => setState(() {  }),),
                                          ),
                                        titulo: 'modalidades', expandableController: _expandableControlleModalidades),
                                      ),     
                    ],
                  ),
                ),
              ),
              Padding(
                      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                      child: DSbotaoPadrao(
                        titulo:  localizedString('use_filter'),
                        onTap: salvou ?? () {},
                      ),
                    )
            ],
          ),
        );
      }
    ));
  }

  FlutterSliderHandler handlerSlider(BuildContext context) {
    return FlutterSliderHandler(
            decoration: const BoxDecoration(),
              child: Container(
                width: 44,
                height: 24,
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(width: 1, color: Theme.of(context).primaryColor)
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(height: 12, width: 1, color: Theme.of(context).dividerColor,),
                    Padding(
                      padding: const EdgeInsets.only(left: 3, right: 3),
                      child: Container(height: 12, width: 1, color: Theme.of(context).dividerColor,),
                    ),
                    Container(height: 12, width: 1, color: Theme.of(context).dividerColor,)
                  ],
                ),
              ));
  }

  Widget criarModalidadesSelecao(Function() onTap) {
    List<Widget> grupos = [];
    for (final key in modalidadesSelecionadas) {
      grupos.add(Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
         Padding(
           padding: const EdgeInsets.only(top: 8, bottom: 8),
           child: SizedBox(
            width: MediaQuery.of(context).size.width - 156,
            child: DStextSubheadline(capitalize(key.modalidade ?? ''), eHeavy: false,)),
         ),
          CupertinoSwitch(
            activeTrackColor: Theme.of(context).primaryColor,
            value: key.selecionado ?? false,
            onChanged: (v) {
              key.selecionado = !(key.selecionado ?? false);
              onTap();
            })
        ],
      ));
    }
    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: SizedBox(
        height: grupos.length * 42,
        child: SingleChildScrollView(child: Wrap(children: grupos))),
    );
  }

  @override
  void afterFirstLayout(BuildContext context) {
    
  }
}
  
num? duracaoAula(AulaTurma aulaTurma) {
  return UtilDataHora.minutosEntreHorarios(inicio: aulaTurma.inicio ?? '00:00', fim: aulaTurma.fim ?? '00:00');
}

class ModalidadeSelecionada {
  String? modalidade;
  bool? selecionado;

  ModalidadeSelecionada({this.modalidade, this.selecionado});
}