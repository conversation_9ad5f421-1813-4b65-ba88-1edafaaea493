import 'dart:async';

import 'package:after_layout/after_layout.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/TextWidgets.dart';
import 'package:app_treino/appWidgets/componentWidgets/ValidaCondicoes.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorExecucaoTreino.dart';
import 'package:app_treino/controlladores/ControladorWod.dart';
import 'package:app_treino/controlladores/controladorPlanner.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/screens/_treino6/novo_chat/botao_chat_com_badge.dart';
import 'package:app_treino/screens/_treino6/treinoColaborador/ItemTreinoColaborador.dart';
import 'package:app_treino/screens/novaDashboardColaborador/ItemAlunosNaAcademia.dart';
import 'package:app_treino/screens/novaDashboardColaborador/ItemSelecaoUnidadeColaborador.dart';
import 'package:app_treino/screens/novologin/widgets/UltimosUsuariosWidget.dart';
import 'package:app_treino/screens/prescricaoDeTreino/treinoIA/CarrosselTreinosAprovacao.dart';
import 'package:app_treino/screens/transparency/TelaPermissaoNotificacoes.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/config/personal_icon_icons.dart';
import 'package:app_treino/controlladores/ControladorAulaTurma.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorDashBoardPersonal.dart';
import 'package:app_treino/controlladores/ControladorNotificacoesCrm.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoDeTreino.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/screens/acompanhar/TelaAcompanhar.dart';
import 'package:app_treino/screens/novaDashboardColaborador/ItemDashPrograma.dart';
import 'package:app_treino/screens/novaDashboardColaborador/ItensCateiraDash.dart';
import 'package:app_treino/screens/novaDashboardColaborador/ItensSecundariosDash.dart';
import 'package:app_treino/screens/planner/widgets/ItemAulaHoje.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:get_it/get_it.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:skeleton_text/skeleton_text.dart';
import 'package:badges/badges.dart' as badges;

class TelaDashboard extends StatefulWidget {
  TelaDashboard({Key? key}) : super(key: key);

  @override
  State<TelaDashboard> createState() => _TelaDashboardState();
}

class _TelaDashboardState extends State<TelaDashboard> with AutomaticKeepAliveClientMixin, AfterLayoutMixin<TelaDashboard>  {
  final _controllerAulaTurma = GetIt.I.get<ControladorAulaTurma>();
  final _controller = GetIt.I.get<ControladorDashBoardPersonal>();
  ControladorCliente mControladorCliente = GetIt.I.get<ControladorCliente>();
  ControladorAulaTurma mControladorAulasTurmas = GetIt.I.get();
  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  var tentarNovamente = false;
  final _controladorPrescricao = GetIt.I.get<ControladorPrescricaoDeTreino>();
  bool estaPesquisando = false;
  bool unidadeSelecionada = false;
  bool cardExpandedRecDep = false;

  @override
  void initState() {
    if(!GetIt.I.get<ControladorCliente>().isColaboradorAtivo) {
      Navigator.of(context).pushNamedAndRemoveUntil('/telaBloqueioColaboradorInativo', (Route<dynamic> route) => false);
    }
    refresh();
    mControladorCliente.atualizarNomeEmpresa();
    super.initState();
  }

  void refresh() async{
    GetIt.I.get<ControladorCliente>().consultarSeTemUsuariosQueJaLogaram((success) {});
    _controller.carregarDashNovoTreino(
      forceUpdate: true,
      sucesso: (){
        _controller.carregarBiAvaliacaoFisica();
        _refreshController.refreshCompleted();
      }, falha: (e){
         _refreshController.refreshFailed();
         }, carregando: () {
         _controller.mStatusDashboard = ServiceStatus.Waiting;
      });
    _controllerAulaTurma.consultarAulasTurmasProfessor(carregando: () {}, falha: (e) {}, sucesso: () {});
    _controladorPrescricao.consultarAlunosColaboradorNaAcademia(
      sucesso: (aluno) {},
      erro: (e) {},
      carteira: true
    );
    GetIt.I.get<ControladorPlanner>().obterFichaDoDia(sucesso: () {
      GetIt.I.get<ControladorPlanner>().tituloErro = '';
      GetIt.I.get<ControladorPlanner>().subtituloErro = '';
      setState(() {});
      _refreshController.refreshCompleted();
    }, falha: (falha) {
      final nome = UtilitarioApp().retornarSomenteOPrimeiroNome(GetIt.I.get<ControladorCliente>().mUsuarioLogado?.nome ?? '');
      if (falha.toLowerCase().contains('aluno não possui autorização de acesso.')) {
        GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('pendencias_encontradas');
        GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('existem_parcelas_vencidas_no_seu_contrato');
        GetIt.I.get<ControladorExecucaoTreino>().finalizarTreinoEmExecucao();
      } else if (falha.toLowerCase().contains('Treino vencido. Contate seu Professor!'.toLowerCase())) {
        GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('treino_vencido');
        GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('seu_programa_de_treino_chegou_ao_fim');
      } else if (falha.toLowerCase().contains('você já executou')) {
        GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('seu_treino_chegou_ao_fim');
        GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('todos_os_treinos_ja_foram_realizados');
      } else if (falha.toLowerCase().contains('nenhum treino. contate seu professor!')) {
        GetIt.I.get<ControladorPlanner>().tituloErro = 'sem_programa_treino';
        GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('o_seu_professor_ainda_nao_gerou_seu_programa');
        GetIt.I.get<ControladorExecucaoTreino>().finalizarTreinoEmExecucao();
      } else if (falha.toString().contains('Sem conexão com a internet')) {
        GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('internet_off');
        GetIt.I.get<ControladorPlanner>().subtituloErro = localizedString('nao_foi_possivel_validar_programa_de_treino');
      } else if (falha.toString().contains('Professor está realizando a validação do seu treino')) {
        GetIt.I.get<ControladorPlanner>().tituloErro = 'Seu treino está em revisão';
        GetIt.I.get<ControladorPlanner>().subtituloErro = 'Aguarde alguns instantes e tente novamente';
      } else if (falha.toString().toLowerCase().contains('o treino está aguardando aprovação do professor.'.toLowerCase())) {
        GetIt.I.get<ControladorPlanner>().tituloErro = 'seu_treino_esta_em_revisao';
        GetIt.I.get<ControladorPlanner>().subtituloErro = 'seu_treino_estara_disponivel_quando_aprovado';
      } else {
        GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('ops_nao_foi_possivel_consultar');
        GetIt.I.get<ControladorPlanner>().subtituloErro = localizedString('nao_foi_possivel_validar_programa_de_treino');
      }
      setState(() {});
      _refreshController.refreshCompleted();
    });
    GetIt.I.get<ControladorWod>().consultarWods();
    mControladorCliente.puxarEmpresasDoCadastro();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
        appBar: DSappBar(
           mostrarSetaTitulo: true,
           iconeSetaCustom: cardExpandedRecDep ? TreinoIcon.angle_up : TreinoIcon.angle_down,
           onArrowTap: () {
            setState(() {
              cardExpandedRecDep = !cardExpandedRecDep;
            });
          },
          onTap: () {
            Navigator.of(context).pushNamed('/telaPerfilProfessor');
          },
          backgroundColor: Theme.of(context).colorScheme.surface,
          tipoAppbar: TipoAppBar.comFoto,
          urlFoto: GetIt.I.get<ControladorCliente>().mUsuarioLogado?.srcImg ?? '',
          titulo: UtilitarioApp().retornarSomenteOPrimeiroNome(
            GetIt.I.get<ControladorCliente>().mUsuarioLogado?.nome ?? ''
          ),
          subtitulo: 'hello_user',
          actions: [
            ValidaCondicoes(
              moduloApp: ModuloApp.MODULO_NOTIFICACOES,
              apenasAluno: false,
              child: StatefulBuilder(
                builder: (BuildContext context, setState){
                  GetIt.I.get<ControladorNotificacoesCrm>().setStateBell = setState;
                  return Observer(builder: (_) {
                    return Center(
                      child: Padding(
                        padding: const EdgeInsets.only(right: 22),
                        child: badges.Badge(
                          badgeContent: DStextCaption1('${GetIt.I.get<ControladorNotificacoesCrm>().quantidadeRecebidasSessao.toString()}', eHeavy: false, textoSobFundoGradiente: true),
                          position: badges.BadgePosition.topEnd(top: -10, end: -5),
                          showBadge: GetIt.I.get<ControladorNotificacoesCrm>().quantidadeRecebidasSessao >= 1,
                          ignorePointer: false,
                          badgeAnimation: const badges.BadgeAnimation.slide(
                            toAnimate: true,
                            animationDuration: Duration(seconds: 1)
                          ),
                          badgeStyle: badges.BadgeStyle(
                            badgeColor: Theme.of(context).primaryColor,
                            padding: GetIt.I.get<ControladorNotificacoesCrm>().quantidadeRecebidasSessao >= 10 ? const EdgeInsets.all(4) : const EdgeInsets.all(6),
                            shape: badges.BadgeShape.circle
                          ),
                          child: DSbotaoCircular(
                            onTap: () async {
                             TelaPermissaoNotificacoes().abrirSolicitacaoPermissao(context, fluxo: FluxoPermissaoNotificacoes.MARCOU_AULA_COLETIVA_ALUNO, fechouModal: () async {
                                Navigator.pushNamed(context, '/telaNotificacoes');
                              }); 
                            },
                            icone: TreinoIcon.bell,
                            alturaIcone: 22,
                          ),
                        )
                      ),
                    );
                  },
                  );
                }
              )
            ),
            ValidaCondicoes(
              apenasColaborador: true,
              validacaoExtra: mControladorCliente.getPermissaoAluno,
              child: Observer(builder: (_) {
                if(_controladorPrescricao.mAlunosAcompanhar.isNotEmpty){
                  return Padding(
                    padding: const EdgeInsets.fromLTRB(0, 12, 16, 12),
                    child: GestureDetector(
                      onTap: (){
                        analytic(EventosKey.acessou_acompanhamento_aluno);
                        Navigator.of(context).push(_createRoute());
                      },
                      child: Container(
                        width: 64,
                        height: 44,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.circular(20)
                        ),
                        child: Center(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Observer(builder: (_) {
                                return Padding(
                                  padding: const EdgeInsets.fromLTRB(14, 8, 4, 8),
                                  child: DStextSubheadline('${_controladorPrescricao.mAlunosAcompanhar.length}',corCustomizada: corQuandoFundoForGradiente(context),),
                                );
                                },
                              ),
                              Padding(
                                padding: const EdgeInsets.only(right: 2),
                                child: Container(
                                  width: 32,
                                  height: 32,
                                  decoration: BoxDecoration(
                                    color:  retornarCorCirculo(context, null),
                                    borderRadius: BorderRadius.circular(20)
                                  ),
                                  child: Center(
                                    child: Icon(
                                      TreinoIcon.user_check,
                                      size: 22,
                                      color: corQuandoFundoForGradiente(context)
                                    ),
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                } else {
                  return Center(
                    child: Semantics(
                        identifier: 'botao_acessar_acompanhar',
                      child: Padding(
                          padding: const EdgeInsets.only(right: 8),
                        child: DSbotaoCircular(
                          icone: TreinoIcon.user_check,
                          alturaIcone: 22,
                          onTap: () {
                             Navigator.of(context).push(_createRoute());
                          }),
                      ),
                    ),
                  );
                }
              },
              ),
            ),
            Semantics(
                identifier: 'bnt_chat',
              child: const BotaoChatComBadge()),
          ],
        ),
        body: SmartRefresher(
            enablePullDown: true,
            enablePullUp: false,
            onRefresh: refresh,
            physics: const BouncingScrollPhysics(),
            header: const WaterDropHeader(),
            controller: _refreshController,
            child: SingleChildScrollView(
              child:Observer(key: const Key('TelaNovaDashboard'), 
                  builder: (_) {
                    switch (_controller.mStatusDashboard) {
                      case ServiceStatus.Waiting:
                        return carregandoTela(context);
                      case ServiceStatus.Done:
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 16, right: 16, top: 8),
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              height: cardExpandedRecDep ? null : 0,
                              child: cardExpandedRecDep ? const UltimosUsuariosWidget(type: LayoutType.secundario, removerUsuarioAtual: true, ativarDschipLogin: true) : const SizedBox.shrink(),
                            ),
                          ),
                            const ItemAlunosNaAcademia(),
                            ItemSelecaoUnidadeColaborador(onSelected: () {
                              refresh();
                            }),
                          const CarrosselTreinosAprovacao(),
                            const ItemDashPrograma(),
                            ItensSecundariosDash(),
                            ItemCarteiraDash(),
                            const Itemtreinocolaborador(),
                            const ItemAulaHoje(),
                            const SizedBox(height: 128),
                          ],
                        );
                      case ServiceStatus.Error:
                        return DScard(
                          paddingExterno: const EdgeInsets.fromLTRB(16, 32, 16, 0),
                          paddingInterno: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              DSsvg(imagem: Imagem.graficoBarras),
                              const SizedBox(height: 24),
                              DStextHeadline('dasBoardColaborador.falha_consulta_dashboard'),
                              const SizedBox(height: 8,),
                              DStextBody('pull_down_to_try_again', eHeavy: false, ePrimario: false,)
                            ],
                          ));
                      default:
                        return Container();
                    }
                  },
                ),
            ))
        // ),
        );
  }

  acessarTelaBloqueio(bool situacao) {
    return Navigator.of(context).pushNamed('/telaBloqueioColaboradorProfessor', arguments: situacao);
  }

  Route _createRoute() {
  return PageRouteBuilder(
    pageBuilder: (context, animation, secondaryAnimation) => TelaAcompanhar(),
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      const begin = Offset(0.0, 1.0);
      const end = Offset.zero;
      const curve = Curves.ease;

      var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

      return SlideTransition(
        position: animation.drive(tween),
        child: child,
      );
    },
  );
}

  Widget carregandoTela(BuildContext context){
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DScard(
            child: SizedBox(
              height: 268,
              width: MediaQuery.of(context).size.width,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          height: 14,
                          width: 180,
                          child: SkeletonAnimation(
                            shimmerColor:
                              Theme.of(context).dividerColor,
                            borderRadius: BorderRadius.circular(10),
                            shimmerDuration: 1000,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).canvasColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 14,
                          width: 60,
                          child: SkeletonAnimation(
                            shimmerColor:
                              Theme.of(context).dividerColor,
                            borderRadius: BorderRadius.circular(10),
                            shimmerDuration: 1000,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).canvasColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Padding(
                    padding: const EdgeInsets.only(bottom: 26, top: 10),
                    child: DottedLine(
                      dashColor: Theme.of(context).dividerColor
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      CircleAvatar(
                        radius: 60,
                        child: SkeletonAnimation(
                          shimmerColor:
                            Theme.of(context).dividerColor,
                          borderRadius: BorderRadius.circular(60),
                          shimmerDuration: 1000,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).canvasColor,
                              borderRadius: BorderRadius.circular(60),
                            ),
                          ),
                        ),
                      ),
                      Container(
                        color: Theme.of(context).dividerColor,
                        width: 0.5,
                        height: 100.0,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                          height: 14,
                          width: 160,
                          child: SkeletonAnimation(
                            shimmerColor:
                              Theme.of(context).dividerColor,
                            borderRadius: BorderRadius.circular(10),
                            shimmerDuration: 1000,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).canvasColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8,),
                        SizedBox(
                          height: 14,
                          width: 160,
                          child: SkeletonAnimation(
                            shimmerColor:
                              Theme.of(context).dividerColor,
                            borderRadius: BorderRadius.circular(10),
                            shimmerDuration: 1000,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).canvasColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8,),
                        SizedBox(
                          height: 14,
                          width: 160,
                          child: SkeletonAnimation(
                            shimmerColor:
                              Theme.of(context).dividerColor,
                            borderRadius: BorderRadius.circular(10),
                            shimmerDuration: 1000,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).canvasColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                        ],
                      )
                    ],
                  ),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16, top: 26),
                    child: DottedLine(
                      dashColor: Theme.of(context).dividerColor
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        height: 14,
                        width: 120,
                        child: SkeletonAnimation(
                          shimmerColor:
                            Theme.of(context).dividerColor,
                          borderRadius: BorderRadius.circular(10),
                          shimmerDuration: 1000,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).canvasColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 14,
                        width: 120,
                        child: SkeletonAnimation(
                          shimmerColor:
                            Theme.of(context).dividerColor,
                          borderRadius: BorderRadius.circular(10),
                          shimmerDuration: 1000,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).canvasColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                  ],
                ),
              )
            ),
          ),
          const SizedBox(
            height: 32
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Expanded(
                child: DScard(
                  paddingExterno: const EdgeInsets.fromLTRB(0, 0, 8, 0),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
                        child: SizedBox(
                          height: 30,
                          width: 40,
                          child: SkeletonAnimation(
                            shimmerColor:
                              Theme.of(context).dividerColor,
                            borderRadius: BorderRadius.circular(10),
                            shimmerDuration: 1000,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).canvasColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 16,
                        width: 60,
                        child: SkeletonAnimation(
                          shimmerColor:
                            Theme.of(context).dividerColor,
                          borderRadius: BorderRadius.circular(10),
                          shimmerDuration: 1000,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).canvasColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
              ),
              Expanded(
                child: DScard(
                  paddingExterno: const EdgeInsets.fromLTRB(8, 0, 8, 0),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
                        child: SizedBox(
                          height: 30,
                          width: 40,
                          child: SkeletonAnimation(
                            shimmerColor:
                              Theme.of(context).dividerColor,
                            borderRadius: BorderRadius.circular(10),
                            shimmerDuration: 1000,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).canvasColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 16,
                        width: 60,
                        child: SkeletonAnimation(
                          shimmerColor:
                            Theme.of(context).dividerColor,
                          borderRadius: BorderRadius.circular(10),
                          shimmerDuration: 1000,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).canvasColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
              ),
              Expanded(
                child: DScard(
                  paddingExterno: const EdgeInsets.fromLTRB(8, 0, 0, 0),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
                        child: SizedBox(
                          height: 30,
                          width: 40,
                          child: SkeletonAnimation(
                            shimmerColor:
                              Theme.of(context).dividerColor,
                            borderRadius: BorderRadius.circular(10),
                            shimmerDuration: 1000,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).canvasColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 16,
                        width: 60,
                        child: SkeletonAnimation(
                          shimmerColor:
                            Theme.of(context).dividerColor,
                          borderRadius: BorderRadius.circular(10),
                          shimmerDuration: 1000,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).canvasColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
              ),
            ],
          ),
          const SizedBox(
            height: 32
          ),
          Row(
            children: [
              Expanded(
                child: DScard(
                  child: SizedBox(
                    height: 180,
                    width: (MediaQuery.of(context).size.width / 2) - 32,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        height: 12,
                                        width: 60,
                                        child: SkeletonAnimation(
                                          shimmerColor:
                                            Theme.of(context).dividerColor,
                                          borderRadius: BorderRadius.circular(10),
                                          shimmerDuration: 1000,
                                          child: Container(
                                            decoration: BoxDecoration(
                                              color: Theme.of(context).canvasColor,
                                              borderRadius: BorderRadius.circular(10),
                                            ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 8,),
                                      SizedBox(
                                        height: 12,
                                        width: 40,
                                        child: SkeletonAnimation(
                                          shimmerColor:
                                            Theme.of(context).dividerColor,
                                          borderRadius: BorderRadius.circular(10),
                                          shimmerDuration: 1000,
                                          child: Container(
                                            decoration: BoxDecoration(
                                              color: Theme.of(context).canvasColor,
                                              borderRadius: BorderRadius.circular(10),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const Icon(PersonalIcon.chevron_right, size: 24)
                                ],
                              ),
                              Padding(
                              padding: const EdgeInsets.only(bottom: 16, top: 8),
                              child: DottedLine(
                                dashColor: Theme.of(context).dividerColor,
                              ),
                            ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        height: 12,
                                        width: 60,
                                        child: SkeletonAnimation(
                                          shimmerColor:
                                            Theme.of(context).dividerColor,
                                          borderRadius: BorderRadius.circular(10),
                                          shimmerDuration: 1000,
                                          child: Container(
                                            decoration: BoxDecoration(
                                              color: Theme.of(context).canvasColor,
                                              borderRadius: BorderRadius.circular(10),
                                            ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 8,),
                                      SizedBox(
                                        height: 12,
                                        width: 40,
                                        child: SkeletonAnimation(
                                          shimmerColor:
                                            Theme.of(context).dividerColor,
                                          borderRadius: BorderRadius.circular(10),
                                          shimmerDuration: 1000,
                                          child: Container(
                                            decoration: BoxDecoration(
                                              color: Theme.of(context).canvasColor,
                                              borderRadius: BorderRadius.circular(10),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const Icon(PersonalIcon.chevron_right, size: 24,)
                                ],
                              ),
                              const SizedBox(height: 32),
                              SizedBox(
                                height: 16,
                                width: 130,
                                child: SkeletonAnimation(
                                  shimmerColor:
                                    Theme.of(context).dividerColor,
                                  borderRadius: BorderRadius.circular(10),
                                  shimmerDuration: 1000,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).canvasColor,
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                    ),
                ),
                          ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: DScard(
                  child: SizedBox(
                    height: 180,
                    width: (MediaQuery.of(context).size.width / 2) - 32,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          SizedBox(
                            height: 60,
                            width: 30,
                            child: SkeletonAnimation(
                              shimmerColor:
                                Theme.of(context).dividerColor,
                              borderRadius: BorderRadius.circular(10),
                              shimmerDuration: 1000,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Theme.of(context).canvasColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            height: 14,
                            width: 130,
                            child: SkeletonAnimation(
                              shimmerColor:
                                Theme.of(context).dividerColor,
                              borderRadius: BorderRadius.circular(10),
                              shimmerDuration: 1000,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Theme.of(context).canvasColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          DottedLine(dashColor: Theme.of(context).dividerColor),
                          const SizedBox(height: 16),
                          SizedBox(
                            height: 14,
                            width: 160,
                            child: SkeletonAnimation(
                              shimmerColor:
                                Theme.of(context).dividerColor,
                              borderRadius: BorderRadius.circular(10),
                              shimmerDuration: 1000,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Theme.of(context).canvasColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                    ),
                  ),
              )
            ],
          ),
          const SizedBox(height: 16,),
          DScard(
            child: SizedBox(
              height: 280,
              width: MediaQuery.of(context).size.width,
              child: SkeletonAnimation(
                  shimmerColor: Theme.of(context).dividerColor,
                  borderRadius: BorderRadius.circular(6),
                  shimmerDuration: 1000,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).canvasColor,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
            ),
          ),
        ],
      ),
    );
  }

  Widget contratosAvencer(){
    double porcentagem = _controller.mDashboard!.biCarteira!.aVencerZW != null || _controller.mDashboard!.biCarteira!.aVencerZW !> 0  
    ? double.parse(((_controller.mDashboard!.biCarteira!.aVencerZW! * 100) / _controller.mDashboard!.biCarteira!.ativos!).toStringAsFixed(2)) : 0;
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextHeadLine1('contratos'),
          const SizedBox(height: 8),
          SizedBox(
            height: 188,
            width: MediaQuery.of(context).size.width,
            child: GestureDetector(
              onTap: (){
                Navigator.pushNamed(context, '/telaMinhaCarteiraContratos', arguments: 2);
              },
              child: DScard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        TextDisplay1(_controller.mDashboard!.biCarteira!.aVencerZW != null || _controller.mDashboard!.biCarteira!.aVencerZW !> 0 
                        ? _controller.mDashboard!.biCarteira!.aVencerZW!.toString() : '0', corPrimaria: true,),
                        TextHeadLine3('contratos_a_vencer'),
                      ],
                    ),
                    const SizedBox(height: 8),
                    DottedLine(
                      dashColor: Theme.of(context).dividerColor,
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                      child: LinearPercentIndicator(
                        padding: EdgeInsets.zero,
                        lineHeight: 8,
                        percent: ((porcentagem / 100).isNaN || (porcentagem / 100).isInfinite) 
                        ? 0 
                        : ((porcentagem / 100) > 1) ? 1 : (porcentagem / 100),
                        backgroundColor: Theme.of(context).dividerColor,
                        progressColor: Theme.of(context).primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8,),
                    TextOverLine1('porcentagem_alunos', args: [porcentagem.isNaN || porcentagem.isInfinite ? '0' : porcentagem.floor().toString()])
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  @override
  bool get wantKeepAlive => true;

  @override
  FutureOr<void> afterFirstLayout(BuildContext context) {
    final possuiValidadeCREF = GetIt.I.get<ControladorCliente>().mUsuarioLogado?.validadeCREF?.isNotEmpty ?? false;
    final estaInativo = GetIt.I.get<ControladorCliente>().mUsuarioLogado?.status?.toLowerCase().contains('inativo') ?? false;

    if (possuiValidadeCREF && (GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.HABILITAR_VALIDACAO_CREF).habilitado ?? false)) {
      if (possuiValidadeCREF && UtilDataHora.dataMenorQueDeAgora(dateTime: UtilDataHora.parseStringToDate(GetIt.I.get<ControladorCliente>().mUsuarioLogado?.validadeCREF?? ''))) {
        acessarTelaBloqueio(false);
      }
    }
    if(estaInativo) {
      acessarTelaBloqueio(true);
    }
  }
}