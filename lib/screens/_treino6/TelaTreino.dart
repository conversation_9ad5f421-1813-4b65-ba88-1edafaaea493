import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/ValidaCondicoes.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoDeTreino.dart';
import 'package:app_treino/controlladores/controladorPlanner.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/avalicaoFisica/AvaliacaoFisicaRecente.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/screens/_treino6/ItemHistoricoDeTreinos.dart';
import 'package:app_treino/screens/historicoFitness/CardProgramaDeTreino.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/controlladores/ControladorExecucaoTreino.dart';
import 'package:app_treino/controlladores/ControladorTreinoAluno.dart';
import 'package:app_treino/model/treinoAluno/ProgramadeTreino.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoItemFicha.dart';
import 'package:app_treino/screens/_treino6/TreinosNaSemana.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:get_it/get_it.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';


class TelaTreino extends StatefulWidget {
  TelaTreino({Key? key}) : super(key: key);

  @override
  State<TelaTreino> createState() => _TelaTreinoState();
}

class _TelaTreinoState extends State<TelaTreino> with AutomaticKeepAliveClientMixin {

   final _refreshController = RefreshController(initialRefresh: false);
   final controladorProgramaTreino = GetIt.I.get<ControladorTreinoAluno>();
   final _controladorPrescricao = GetIt.I.get<ControladorPrescricaoDeTreino>();
   Ficha? fichaDoDia;
   String get _keyTreinoDia => 'treino_${controladorProgramaTreino.statusTreinoIASituacao}_${DateTime.now().millisecondsSinceEpoch}';

   @override
  void initState() {
    _controladorPrescricao.startDate = controladorProgramaTreino.mProgramaCarregado?.programa?.dataInicio ?? DateTime.now().subtract(const Duration(days: 15));
    _controladorPrescricao.endDate = controladorProgramaTreino.mProgramaCarregado?.programa?.dataTerminoPrevisto ?? DateTime.now();
    _onRefresh(validarPrograma: controladorProgramaTreino.mProgramaCarregado == null);
    super.initState();
  }

  void _onRefresh({bool validarPrograma = false}) {
   
    _controladorPrescricao.consultarLinhaDoTempoComFiltro(idAluno: num.parse(GetIt.I.get<ControladorCliente>().mUsuarioLogado?.matricula ?? '0'), idEmpresa: GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 1);
    if (!validarPrograma) {
      GetIt.I.get<ControladorPlanner>().obterFichaDoDia(sucesso: () {
        GetIt.I.get<ControladorPlanner>().tituloErro = '';
        GetIt.I.get<ControladorPlanner>().subtituloErro = '';
        fichaDoDia = controladorProgramaTreino.mProgramaCarregado!.programa!.getFichaDoDia;
        setState(() {});
        _refreshController.refreshCompleted();
      }, falha: (falha) {
        final nome = UtilitarioApp.sentenseCaseFirst(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.nome!.split(' ')[0]);
        if (falha.toLowerCase().contains('aluno não possui autorização de acesso.')) {
          GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('pendencias_encontradas');
          GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('existem_parcelas_vencidas_no_seu_contrato');
          GetIt.I.get<ControladorExecucaoTreino>().finalizarTreinoEmExecucao();
        } else if (falha.toLowerCase().contains('Treino vencido. Contate seu Professor!'.toLowerCase())) {
          GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('treino_vencido');
          GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('seu_programa_de_treino_chegou_ao_fim');
        } else if (falha.toLowerCase().contains('você já executou')) {
          GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('seu_treino_chegou_ao_fim');
          GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('todos_os_treinos_ja_foram_realizados');
        } else if (falha.toLowerCase().contains('nenhum treino. contate seu professor!')) {
          GetIt.I.get<ControladorPlanner>().tituloErro = 'sem_programa_treino';
          GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('o_seu_professor_ainda_nao_gerou_seu_programa');
          GetIt.I.get<ControladorExecucaoTreino>().finalizarTreinoEmExecucao();
        } else if (falha.toString().contains('Sem conexão com a internet')) {
          GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('internet_off');
          GetIt.I.get<ControladorPlanner>().subtituloErro = localizedString('nao_foi_possivel_validar_programa_de_treino');
        } else if (falha.toString().contains('Professor está realizando a validação do seu treino')) {
          GetIt.I.get<ControladorPlanner>().tituloErro = 'Seu treino está em revisão';
          GetIt.I.get<ControladorPlanner>().subtituloErro = 'Aguarde alguns instantes e tente novamente';
        } else if (falha.toString().toLowerCase().contains('o treino está aguardando aprovação do professor.'.toLowerCase())) {
          GetIt.I.get<ControladorPlanner>().tituloErro = 'seu_treino_esta_em_revisao';
          GetIt.I.get<ControladorPlanner>().subtituloErro = 'seu_treino_estara_disponivel_quando_aprovado';
        } else {
          GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('ops_nao_foi_possivel_consultar');
          GetIt.I.get<ControladorPlanner>().subtituloErro = localizedString('nao_foi_possivel_validar_programa_de_treino');
        }
        setState(() {});
        _refreshController.refreshCompleted();
      });
    } else {
      GetIt.I.get<ControladorPlanner>().tituloErro = '';
      if((GetIt.I.get<ControladorPlanner>().subtituloErro != 'seu_treino_estara_disponivel_quando_aprovado'))
      GetIt.I.get<ControladorPlanner>().subtituloErro = '';
      GetIt.I.get<ControladorPlanner>().statusCarregandoFichaDoDia = ServiceStatus.Empty;
      setState(() {});
    }
  }


   
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: DSappBar(
        titulo: localizedString('navibar.treino'),
        tipoAppbar: TipoAppBar.tituloNaEsquerda,
        backgroundColor: Theme.of(context).colorScheme.surface,
        actions:  [
          ValidaCondicoes(
                configuracaoDoTreino: 'PERMITIR_ALUNO_CRIAR_TREINO_IA_APP',
                validacaoExtra: (controladorProgramaTreino.mProgramaCarregado?.programa?.geradoPorIA ?? false) || controladorProgramaTreino.programaDeTreinoVencido,
      child: Observer(
        builder: (context) {
          return  Semantics(
              identifier: 'bnt_opcao_IA',
            child: Padding(
              padding: const EdgeInsets.only(right: 16),
              child: Center(child: Semantics(
                  identifier: 'bnt_opcoes_IA',
                child: DSbotaoCircular(icone: TreinoIcon.ellipsis_v, onTap: () {
                  DSBottomSheet().exibirAlerta(
                                context,
                                null,
                                Column(
                                  children: [
                                    Semantics(
                                        identifier: 'modal_renovar_criar_treino_IA',
                                      child: Padding(
                                        padding: const EdgeInsets.only(bottom: 8),
                                        child: InkWell(
                                          onTap: () async {
                                            await Navigator.of(context).pushNamed('/telaAnamneseRestricoes');
                                            Navigator.of(context).pop();
                                          },
                                          child: Padding(
                                            padding: const EdgeInsets.fromLTRB(12, 0, 12, 16),
                                            child: Row(
                                              children: [
                                                Icon(
                                                  TreinoIcon.sync,
                                                  color: Theme.of(context).iconTheme.color,
                                                ),
                                                Padding(
                                                  padding: const EdgeInsets.only(left: 8),
                                                  child: SizedBox(
                                                    width: MediaQuery.of(context).size.width - 120,
                                                    child:Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        DStextSubheadline(
                                                         controladorProgramaTreino.programaDeTreinoVencido ? 'renovar_criar_meu_programa_de_treino' : 'nao_gostei_do_meu_programa',
                                                          maximoLinhas: 2 ,
                                                          overflow: TextOverflow.ellipsis,
                                                        ),
                                                        DStextCaption1(
                                                         'gerar_novo_programa_ia',
                                                          maximoLinhas: 2,
                                                          overflow:  TextOverflow.ellipsis,
                                                          eHeavy: false,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                                const Spacer(),
                                                Icon(
                                                  TreinoIcon.angle_right,
                                                  color: Theme.of(context).iconTheme.color,
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ));
                },),
              )),
            ),
          );
        })),         
        ],
      ),
      body: SmartRefresher(
        enablePullDown: true,
        onRefresh: _onRefresh,
        physics: const BouncingScrollPhysics(),
        header: const WaterDropHeader(),
        controller: _refreshController,
        child: Observer(
        builder: (context) {
          return SingleChildScrollView(
            child:  Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const TreinosNaSemana(telaHomePage: true,),
                itemProgramaFim(context),
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 24, 16, 0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      DStextSubheadline('treino_dia'), 
                      (controladorProgramaTreino.mProgramaCarregado?.programa?.outrosTreinos().isEmpty ?? true) ? Container() : DSbotaoPadrao(titulo: 'see_more', tipoBotao: TipoBotao.sublinhado, onTap: () {
                        Navigator.of(context).pushNamed('/telaFichasVerMais');
                      },)
                    ],
                  ),
                ),
                Observer(builder: (context) {
                      controladorProgramaTreino.statusTreinoIASituacao;
                      return ItemTreinoDoDiaOuExecutando(
                          key: ValueKey(_keyTreinoDia), titulo: GetIt.I.get<ControladorPlanner>().tituloErro, subtitulo: GetIt.I.get<ControladorPlanner>().subtituloErro);
                    }),
                const ItemHistoricoDeTreinos(),
                (controladorProgramaTreino.mProgramaCarregado?.programa != null) ?
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                  child: CardProgramaDeTreino(programaTreino: controladorProgramaTreino.mProgramaCarregado!, avaliacaoAluno: GetIt.I.get<ControladorPrescricaoDeTreino>().avaliacaoRecenteAlunoExibir ?? AvaliacaoFisicaRecente(), telaTreino: true),
                ) : Container(height: 16,),
                ValidaCondicoes(
                  moduloApp: ModuloApp.MODULO_HOME_FIT,
                  child: itemHomeFit(context)),
                const SizedBox(height: 16),
                  const CardVerRecords(),
                 Observer(builder: (context) {
                  return  GetIt.I.get<ControladorExecucaoTreino>().treinoEmExecucao?.atividades != null ? const SizedBox(height: 220) : const SizedBox(height: 120);
                 })
              ],
            ),
          );
        },
      ),
    ));
  }

  Widget itemProgramaFim(BuildContext context) {
    return controladorProgramaTreino.programaDeTreinoVencido ? Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: GestureDetector(
        onTap: () {
            if (GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_AGENDA).habilitado!) {
              Navigator.pushNamed(context, '/novaTelaAgendaAluno', arguments: false);
            }
        },
        child: DScard(
          vaiTerCirculo: true,
          gradiente: true,
          child: Padding(
            padding: EdgeInsets.fromLTRB( GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_AGENDA).habilitado! ? 64 : 46, 16, 16, 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DStextSubheadline('programa_de_treino_chegou_fim!', eHeavy: true, textoSobFundoGradiente: true),
              DStextBody(GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_AGENDA).habilitado! ? 'text_fale_professor_agende_renovacao_treino' : 'text_fale_professor_solicite_renovacao_treino', eHeavy: false, textoSobFundoGradiente: true),
              GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_AGENDA).habilitado! ? const Padding(
                padding: EdgeInsets.only(top: 4),
                child: DSbotaoPadrao(titulo: 'agendar_aulas', sobFundoGradiente: true, tamanhoReduzido: true),
              ) : Container()
            ],
                            ),
          )),
      ),
    ): Container();
  }

  

  DScard itemHomeFit(BuildContext context) {
    return DScard(
            onTap: () {
              Navigator.of(context).pushNamed('/telaHomeFit');
            },
            paddingExterno: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            paddingInterno: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const DSbotaoCircular(altura: 48, icone: TreinoIcon.tv_retro, alturaIcone: 22, categoria: Categoria.secundario),
                    Padding(
                      padding: const EdgeInsets.only(left: 16, right: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width - 160,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                DStextSubheadline('treine_em_casa'),
                                Padding(
                                  padding: const EdgeInsets.only(top: 0, bottom: 8),
                                  child: DStextCaption1(
                                    'veja_mais_treinos_para_fazer',
                                    eHeavy: false,
                                    ePrimario: false,
                                  ),
                                ),
                                const DSbotaoPadrao(titulo: 'ver_treinos', icone: TreinoIcon.arrow_right, tipoBotao: TipoBotao.sublinhado,)
                              ],
                            ),
                          ),                      
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ));
  }

  @override
  bool get wantKeepAlive => true;
}

class CardVerRecords extends StatelessWidget {
  const CardVerRecords({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(24),
          child: SizedBox(
            height: 171,
            child: Stack(
              children: [
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        image: DecorationImage(
                            colorFilter: new ColorFilter.mode(Theme.of(context).primaryColor, BlendMode.hue),
                            image: const AssetImage('assets/images/ManCross.jpg'),
                            fit: BoxFit.cover,
                            alignment:const Alignment(0,-0.31 ),
                            
                            )),
                  )),
                                                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.bottomCenter,
                        end: Alignment.topCenter,
                        colors: [
                          Colors.black.withValues(alpha: 1), 
                          Colors.transparent 
                        ],
                        stops: [0.0, 0.9],
                      ),
                    ),
                  ),
                ),
                Positioned(
                  top: 44,
                  left: 12,
                  right: 12,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DStextHeadline(
                        'titulo_personal_records',
                        corCustomizada: Colors.white,
                      ),
                      DStextCaption1('Veja seus recordes de pesos',eHeavy: false, corCustomizada: Colors.white,)
                    ],
                  ),
                ),
                Positioned(
                  bottom: 12,
                  left: 12,
                  right: 12,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 8, bottom: 8),
                        child: Container(height: 1, color: const Color(0xff424242)),
                      ),
                      DSbotaoPadrao(
                        titulo: 'ver_records',
                        onTap: () {
                        Navigator.of(context).pushNamed('/telaAtividadesPersonalRecords');
                        },
                      ),
                    ],
                  ),
                ),
    
              ],
            ),
          ),
        ),
    );
  }
}
