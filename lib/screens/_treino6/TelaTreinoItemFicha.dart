import 'dart:async';

import 'package:after_layout/after_layout.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/ValidaCondicoes.dart';
import 'package:app_treino/appWidgets/componentWidgets/image_widget.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorAulaTurma.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorExecucaoTreino.dart';
import 'package:app_treino/controlladores/controladorPlanner.dart';
import 'package:app_treino/controlladores/ControladorTreinoAluno.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoWidgetExecucao.dart';
import 'package:app_treino/screens/prescricaoDeTreino/mockia/mock.ia.dart';
import 'package:app_treino/screens/prescricaoDeTreino/treinoIA/TilePrescricaoIA.dart';
import 'package:app_treino/screens/transparency/TelaPermissaoNotificacoes.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:get_it/get_it.dart';
import 'package:skeleton_text/skeleton_text.dart';

class ItemTreinoDoDiaOuExecutando extends StatefulWidget {
  final String titulo;
  final String subtitulo;
  const ItemTreinoDoDiaOuExecutando({Key? key, required this.titulo, required this.subtitulo}) : super(key: key);

  @override
  _ItemTreinoDoDiaOuExecutandoState createState() => _ItemTreinoDoDiaOuExecutandoState();
}

class _ItemTreinoDoDiaOuExecutandoState extends State<ItemTreinoDoDiaOuExecutando> with AfterLayoutMixin<ItemTreinoDoDiaOuExecutando> {
  final _controllerPlanner = GetIt.I.get<ControladorPlanner>();
  final _controlladorProgramaTreino = GetIt.I.get<ControladorTreinoAluno>();
  @override
  void initState() {
    super.initState();
  }

  @override
  FutureOr<void> afterFirstLayout(BuildContext context) async {
    _controlladorProgramaTreino.mTreinoEmExecucaoNotifier.addListener(() {
      if (mounted) setState(() {});
    });
    setState(() {});
  }

  var consultei = false;
  @override
  Widget build(BuildContext context) {
    return ValidaCondicoes(
      //apenasAluno: false,
      moduloApp: ModuloApp.MODULO_TREINO,
      child: Observer(
        key: const Key('ItemTreinoHoje'),
        builder: (_) {
          // ignore: unused_local_variable
          AppLifecycleState estadoApp = GetIt.I.get<ControladorApp>().estadoApp;
          final nome = UtilitarioApp.sentenseCaseFirst(GetIt.I.get<ControladorCliente>().mUsuarioLogado?.nome?.split(' ')[0] ?? '');

          // Verificar primeiro se há um processo de treino IA em andamento
          final statusTreinoIA = _controlladorProgramaTreino.statusTreinoIASituacao;
          if (statusTreinoIA == SituacaoValidacao.processando || statusTreinoIA == SituacaoValidacao.aguardando) {
            return ValidaCondicoes(
              configuracaoDoTreino: 'PERMITIR_ALUNO_CRIAR_TREINO_IA_APP',
              child: const TilePrescricaoIA(),
              childFalse: itemSemPrograma(homepage: true, titulo: localizedString(widget.titulo), subtitulo: localizedString(widget.subtitulo)),
            );
          }

          switch (_controllerPlanner.statusCarregandoFichaDoDia) {
            case ServiceStatus.Waiting:
              return SizedBox(
                height: 174,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: SkeletonAnimation(
                    shimmerColor: Theme.of(context).dividerColor,
                    borderRadius: BorderRadius.circular(5),
                    shimmerDuration: 1000,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).canvasColor,
                        borderRadius: BorderRadius.circular(5),
                      ),
                    ),
                  ),
                ),
              );
            case ServiceStatus.Done:
              if (widget.titulo.isEmpty && _controlladorProgramaTreino.mProgramaCarregado != null) {
                return celulaTreino(context, false);
              } else {
                return ValidaCondicoes(
                  configuracaoDoTreino: 'PERMITIR_ALUNO_CRIAR_TREINO_IA_APP',
                  child: const TilePrescricaoIA(),
                  childFalse: itemSemPrograma(homepage: true, titulo: localizedString(widget.titulo), subtitulo: localizedString(widget.subtitulo)),
                );
              }
            case ServiceStatus.Error:
              return itemSemPrograma(homepage: true, titulo: localizedString('ops_nao_foi_possivel_consultar'), subtitulo: localizedString('nao_foi_possivel_validar_programa_de_treino'));
            case ServiceStatus.Empty:
               if(widget.subtitulo == 'seu_treino_estara_disponivel_quando_aprovado'  ){
              return itemSemPrograma(homepage: true, titulo: localizedString(widget.titulo), subtitulo: '$nome, ' + localizedString(widget.subtitulo));
            }
              return itemSemPrograma(homepage: true, titulo: localizedString('sem_programa_treino'), subtitulo: '$nome,' + localizedString('o_seu_professor_ainda_nao_gerou_seu_programa'));
          }
        },
      ),
    );
  }

  SizedBox celulaTreino(BuildContext context, bool executando) {
    ControladorExecucaoTreino controlladorTreino = GetIt.I.get<ControladorExecucaoTreino>();
    return SizedBox(
      height: 264,
      child: Semantics(
        identifier: 'acessar_ficha_do_dia',
        child: DScard(
            onTap: () async {
              TelaPermissaoNotificacoes().abrirSolicitacaoPermissao(context, fluxo: FluxoPermissaoNotificacoes.INICIOU_TREINO_ALUNO, fechouModal: () async {
                if (GetIt.I.get<ControladorExecucaoTreino>().treinoEmExecucao == null) {
                  _controlladorProgramaTreino.setFichaExibir((_controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia)!);
                  if (_controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia?.atividades?.isEmpty ?? true) {
                    DSalerta().exibirAlertaSimplificado(
                        context: context,
                        titulo: 'Treino sem atividades',
                        subtitulo: 'Seu treino está sem atividades e por isso não foi possível iniciar. Informe o seu professor e solicite a revisão do treino.',
                        tituloBotao: localizedString('got_it'));
                  } else {
                    await Navigator.of(context).pushNamed('/telaTreinoDetalheFicha');
                    setState(() {});
                  }
                } else {
                  await abrirTelaExecucaoTreino(context: context);
                  setState(() {});
                }
              });
            },
            borderRadius: 24,
            paddingExterno: const EdgeInsets.all(16),
            child: Stack(
              children: [
                Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    height: 170,
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24)),
                      child: Container(
                        color: Theme.of(context).dividerColor,
                        child: ImageWidget(
                          guardarImagemEmCache: true,
                          imageUrl: (GetIt.I.get<ControladorApp>().chave ?? '').contains('ee35a19f99abdc8cc54ecdcd96c98d87')
                              ? GetIt.I.get<ControladorAulaTurma>().imagemBeachAulaTurma(treino: true, ficha: _controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia)
                              : _controlladorProgramaTreino.getImagemPadraoFicha(_controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia),
                          fit: BoxFit.cover,
                          width: double.maxFinite,
                        ),
                      ),
                    )),
                Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    height: 170,
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24)),
                      child: Container(color: Colors.black26),
                    )),
                Positioned(
                    top: 16,
                    right: 16,
                    left: 16,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        DSchip(
                            tipoChip: TipoChip.comTransparencia,
                            titulo: UtilDataHora.getDaTaMesDiaAno(dateTime: _controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia?.ultimaExecucaoDate ?? DateTime.now()) ==
                                    UtilDataHora.getDaTaMesDiaAno(dateTime: DateTime.now())
                                ? 'workout_completed'
                                : controlladorTreino.treinoEmExecucao != null
                                    ? 'em_execucao'
                                    : 'treino_dia'),
                        ValidaCondicoes(
                          apenasAluno: true,
                          child: DScalendarioMini(
                            categoria: Categoria.secundario,
                            segunda: controlladorTreino.treinoEmExecucao == null
                                ? _controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia?.diaSemana?.contains('SG')
                                : controlladorTreino.treinoEmExecucao?.diaSemana?.contains('SG'),
                            terca: controlladorTreino.treinoEmExecucao == null
                                ? _controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia?.diaSemana?.contains('TR')
                                : controlladorTreino.treinoEmExecucao?.diaSemana?.contains('TR'),
                            quarta: controlladorTreino.treinoEmExecucao == null
                                ? _controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia?.diaSemana?.contains('QA')
                                : controlladorTreino.treinoEmExecucao?.diaSemana?.contains('QA'),
                            quinta: controlladorTreino.treinoEmExecucao == null
                                ? _controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia?.diaSemana?.contains('QI')
                                : controlladorTreino.treinoEmExecucao?.diaSemana?.contains('QI'),
                            sexta: controlladorTreino.treinoEmExecucao == null
                                ? _controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia?.diaSemana?.contains('SX')
                                : controlladorTreino.treinoEmExecucao?.diaSemana?.contains('SX'),
                            sabado: controlladorTreino.treinoEmExecucao == null
                                ? _controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia?.diaSemana?.contains('SB')
                                : controlladorTreino.treinoEmExecucao?.diaSemana?.contains('SB'),
                            domingo: controlladorTreino.treinoEmExecucao == null
                                ? _controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia?.diaSemana?.contains('DM')
                                : controlladorTreino.treinoEmExecucao?.diaSemana?.contains('DM'),
                          ),
                        )
                      ],
                    )),
                Positioned(
                    bottom: 8,
                    left: 16,
                    right: 16,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              DStextSubheadline(
                                UtilitarioApp.sentenseCase(controlladorTreino.treinoEmExecucao == null
                                        ? (_controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia?.nome ?? 'Treino do dia ::')
                                        : (controlladorTreino.treinoEmExecucao?.nome ?? 'Treino do dia ::'))
                                    .split('::')[0],
                                maximoLinhas: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              DStextCaption1(
                                localizedString('prof.') +
                                    ': ${UtilitarioApp.sentenseCase(UtilitarioApp().retornarApenasOPrimeiroNome(_controlladorProgramaTreino.mProgramaCarregado?.programa?.professorMontou ?? ''))}',
                                ePrimario: false,
                                eHeavy: false,
                              ),
                            ],
                          ),
                        ),
                        (GetIt.I.get<ControladorApp>().chave ?? '').contains('ee35a19f99abdc8cc54ecdcd96c98d87')
                            ? Container()
                            : DSbotaoPadrao(
                                onTap: () async {
                                  TelaPermissaoNotificacoes().abrirSolicitacaoPermissao(context, fluxo: FluxoPermissaoNotificacoes.INICIOU_TREINO_ALUNO, fechouModal: () async {
                                    if (controlladorTreino.treinoEmExecucao == null) {
                                      if (_controlladorProgramaTreino.mFichaExibir == null) {
                                        _controlladorProgramaTreino.setFichaExibir((_controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia)!);
                                        if (_controlladorProgramaTreino.mProgramaCarregado?.programa?.getFichaDoDia?.atividades?.isEmpty ?? true) {
                                          DSalerta().exibirAlertaSimplificado(
                                              context: context,
                                              titulo: localizedString('workout_with_no_exercises'),
                                              subtitulo: 'Seu treino está sem atividades e por isso não foi possível iniciar. Informe o seu professor e solicite a revisão do treino.',
                                              tituloBotao: localizedString('got_it'));
                                        } else {
                                          await Navigator.of(context).pushNamed('/telaTreinoDetalheFicha');
                                          setState(() {});
                                        }
                                      } else {
                                        if (_controlladorProgramaTreino.mFichaExibir?.atividades?.isEmpty ?? true) {
                                          DSalerta().exibirAlertaSimplificado(
                                              context: context,
                                              titulo: localizedString('workout_with_no_exercises'),
                                              subtitulo: 'Seu treino está sem atividades e por isso não foi possível iniciar. Informe o seu professor e solicite a revisão do treino.',
                                              tituloBotao: localizedString('got_it'));
                                        } else {
                                          await Navigator.of(context).pushNamed('/telaTreinoDetalheFicha');
                                          setState(() {});
                                        }
                                      }
                                    } else {
                                      await abrirTelaExecucaoTreino(context: context);
                                      setState(() {});
                                    }
                                  });
                                },
                                titulo: controlladorTreino.treinoEmExecucao != null ? 'continue' : 'iniciar',
                                icone: controlladorTreino.treinoEmExecucao != null ? null : TreinoIcon.angle_right,
                                alturaIcone: 20,
                                tamanhoReduzido: true,
                              )
                      ],
                    ))
              ],
            )),
      ),
    );
  }

  DScard itemSemPrograma({required bool homepage, required titulo, required subtitulo}) {
    return DScard(
        paddingExterno: const EdgeInsets.fromLTRB(16, 16, 16, 12),
        paddingInterno: const EdgeInsets.all(16),
        child: Column(
          children: [
            DSsvg(
              imagem: Imagem.altere,
              altura: homepage ? 100 : null,
            ),
            Padding(
              padding: EdgeInsets.only(top: homepage ? 12 : 16, bottom: homepage ? 4 : 8),
              child: DStextHeadline(localizedString(titulo)),
            ),
            DStextBody(
              localizedString(subtitulo),
              eHeavy: false,
              ePrimario: false,
              textAlign: TextAlign.center,
            )
          ],
        ));
  }
}
