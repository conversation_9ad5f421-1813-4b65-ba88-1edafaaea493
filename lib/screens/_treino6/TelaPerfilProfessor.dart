import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorAulaTurma.dart';
import 'package:app_treino/util/profile_pic.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/screens/novaDashboardColaborador/ItemExecucaoTreino.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class TelaPerfilProfessor extends StatefulWidget {
  TelaPerfilProfessor({Key? key}) : super(key: key);

  @override
  State<TelaPerfilProfessor> createState() => _TelaPerfilProfessorState();
}

class _TelaPerfilProfessorState extends State<TelaPerfilProfessor> {
  late ScrollController _scrollController;
  final _controladorCliente = GetIt.I.get<ControladorCliente>();
  ControladorApp get controlladorApp => GetIt.I.get();

  @override
  void initState() {
    _scrollController = ScrollController()
      ..addListener(() {
        setState(() {});
      });
    super.initState();
  }

  bool get _isSliverAppBarExpanded {
    return _scrollController.hasClients &&
        _scrollController.offset > (180 - kToolbarHeight);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: NestedScrollView(
          controller: _scrollController,
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return <Widget>[
              SliverAppBar(
                elevation: 0.1,
                pinned: true,
                centerTitle: true,
                backgroundColor: DSLib.theme == ThemeMode.light ? const Color(0xffFFFFFF) : const Color(0xff202020),
                leading: Navigator.of(context).canPop() ? Center(
                    child: DSbotaoCircular(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  icone: TreinoIcon.angle_left,
                  alturaIcone: 25,
                  categoria: Categoria.secundario,
                )) : Container(),
                title: !_isSliverAppBarExpanded
                    ? Container()
                    : Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          InkWell(
                            onTap: () {
                              analytic(EventosKey.drawer_pressionou_minha_conta);
                              Navigator.pushNamed(context, '/telaPerfil');
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                DStextHeadline(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.nome!.split(' ')[0].toUpperCase()),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 4),
                                    child: CircleAvatar(
                                      radius: 8,
                                      child: Padding(
                                        padding: const EdgeInsets.only(bottom: 1),
                                        child: Icon(
                                          TreinoIcon.edit_alt,
                                          size: 8,
                                          color: corQuandoFundoForGradiente(context),
                                        ),
                                      ),
                                      backgroundColor: Theme.of(context).primaryColor,
                                    ),
                                  )
                              ],
                            ),
                          ),
                          DStextCaption1(
                            UtilitarioApp.sentenseCase(GetIt.I.get<ControladorAulaTurma>().empresaSelecionadaColaborador?.nome ?? ((GetIt.I.get<ControladorCliente>().mUsuarioLogado?.empresas ?? []).isNotEmpty ? (GetIt.I.get<ControladorCliente>().mUsuarioLogado?.empresas?.first.nomeAmigavel ?? '') : '')),
                            eHeavy: false,
                            ePrimario: false,
                          ),
                        ],
                      ),
                expandedHeight: 180,
                flexibleSpace: FlexibleSpaceBar(
                  background: Stack(
                    children: [
                      _isSliverAppBarExpanded
                          ? Container()
                          : Positioned(
                              bottom: 70,
                              height: 100,
                              right: 0,
                              left: 0,
                              child: GestureDetector(
                                onTap: () {
                                  analytic(EventosKey.drawer_pressionou_minha_conta);
                                  Navigator.pushNamed(context, '/telaPerfil');
                                },
                                child: Stack(
                                  children: [
                                  const Positioned.fill(
                                    child: ProfilePic(
                                      radius: 50,
                                      ),
                                    ),
                                    Positioned(
                                      left: MediaQuery.of(context).size.width /
                                              2 +
                                          15,
                                      top: 70,
                                      child: CircleAvatar(
                                        radius: 15,
                                        child: Center(
                                          child: Icon(
                                            TreinoIcon.edit_alt,
                                            size: 18,
                                            color: corQuandoFundoForGradiente(context),
                                          ),
                                        ),
                                        backgroundColor:
                                            Theme.of(context).primaryColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                      Positioned(
                        bottom: 20,
                        left: 0,
                        right: 0,
                        child: Column(
                          children: [
                            DStextHeadline((GetIt.I.get<ControladorCliente>().mUsuarioLogado?.nome ?? '').split(' ')[0].toUpperCase()),
                            DStextCaption1(
                              UtilitarioApp.sentenseCase((
                                GetIt.I.get<ControladorAulaTurma>().empresaSelecionadaColaborador?.nome ??
                                ((GetIt.I.get<ControladorCliente>().mUsuarioLogado?.empresas?.isNotEmpty ?? false)
                                  ? (GetIt.I.get<ControladorCliente>().mUsuarioLogado?.empresas?.first.nomeAmigavel ?? '')
                                  : '')
                              )),
                              eHeavy: false,
                              ePrimario: false,
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
                actions: [
                  Center(
                      child: Padding(
                    padding: const EdgeInsets.only(right: 12),
                    child: DSbotaoCircular(
                      onTap: () {
                        mostarMaisOpcoes(context);
                      },
                      icone: TreinoIcon.ellipsis_v,
                      alturaIcone: 23,
                      categoria: Categoria.secundario,
                    ),
                  ))
                ],
              ),
            ];
          }, body: SingleChildScrollView(
              child: Column(
                children: [
                  DScard(
                    paddingExterno: const EdgeInsets.fromLTRB(16, 24, 16, 24),
                    paddingInterno: const EdgeInsets.fromLTRB(10, 16, 10, 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Semantics(
                            identifier: 'bnt_programas',
                          child: Column(
                            children: [
                              DSbotaoCircular(icone: TreinoIcon.clipboard_notes, alturaIcone: 20, 
                                corIcone: (_controladorCliente.getPermissaoProgramaPreDefinido && _controladorCliente.getPermissaoProgramaPreDefinidoConsultar) ? DSLib.theme == ThemeMode.dark ? const Color(0xffF5F5F5) : const Color(0xff3F3F3F)
                                  : DSLib.theme == ThemeMode.dark ? const Color(0xFF424242) : const Color(0xffDCDDDF),
                                categoria: Categoria.comBorda,
                                corBordaCustomizada: (_controladorCliente.getPermissaoProgramaPreDefinido && _controladorCliente.getPermissaoProgramaPreDefinidoConsultar) ? Theme.of(context).primaryColor
                                  : DSLib.theme == ThemeMode.dark ? const Color(0xFF424242) : const Color(0xffDCDDDF),
                                onTap: (_controladorCliente.getPermissaoProgramaPreDefinido && _controladorCliente.getPermissaoProgramaPreDefinidoConsultar)  ? () {
                                  Navigator.of(context).pushNamed('/telaFichasEProgramas', arguments: 'programas');
                                } : null,
                              ),
                              const SizedBox(height: 8),
                              DStextCaption1(localizedString('programs'), eHeavy: false, 
                              corCustomizada: (_controladorCliente.getPermissaoProgramaPreDefinido && _controladorCliente.getPermissaoProgramaPreDefinidoConsultar) ?  DSLib.theme == ThemeMode.dark ? const Color(0xffF5F5F5) : const Color(0xff3F3F3F)
                                : DSLib.theme == ThemeMode.dark ? const Color(0xFF424242) : const Color(0xffDCDDDF),
                              )
                            ],
                          ),
                        ),
                        Semantics(
                            identifier: 'bnt_fichas_predefinidas',
                          child: Column(
                            children: [
                              DSbotaoCircular(icone: TreinoIcon.th, alturaIcone: 20, 
                              corIcone: (_controladorCliente.getPermissaoFichaPreDefinido && (_controladorCliente.getPermissaoFichaPreDefinidoConsultar || _controladorCliente.getPermissaoFichaPreDefinidoTotal)) ? DSLib.theme == ThemeMode.dark ? const Color(0xffF5F5F5) : const Color(0xff3F3F3F)
                                : DSLib.theme == ThemeMode.dark ? const Color(0xFF424242) : const Color(0xffDCDDDF),
                              categoria: Categoria.comBorda,
                              corBordaCustomizada: (_controladorCliente.getPermissaoFichaPreDefinido && (_controladorCliente.getPermissaoFichaPreDefinidoConsultar || _controladorCliente.getPermissaoFichaPreDefinidoTotal)) ? Theme.of(context).primaryColor
                                  : DSLib.theme == ThemeMode.dark ? const Color(0xFF424242) : const Color(0xffDCDDDF),
                              onTap: (_controladorCliente.getPermissaoFichaPreDefinido && (_controladorCliente.getPermissaoFichaPreDefinidoConsultar || _controladorCliente.getPermissaoFichaPreDefinidoTotal)) ? () {
                                Navigator.of(context).pushNamed('/telaFichasEProgramas', arguments: 'fichas');
                              } : null,
                              ),
                              const SizedBox(height: 8),
                              DStextCaption1(localizedString('sheets'), eHeavy: false,
                              corCustomizada:  (_controladorCliente.getPermissaoFichaPreDefinido && (_controladorCliente.getPermissaoFichaPreDefinidoConsultar || _controladorCliente.getPermissaoFichaPreDefinidoTotal)) ? DSLib.theme == ThemeMode.dark ? const Color(0xffF5F5F5) : const Color(0xff3F3F3F)
                                : DSLib.theme == ThemeMode.dark ? const Color(0xFF424242) : const Color(0xffDCDDDF),
                              )
                            ],
                          ),
                        ),
                        Semantics(
                            identifier: 'acessar_prescricao',
                          child: Column(
                            children: [
                              DSbotaoCircular(
                                icone: TreinoIcon.robot,
                                alturaIcone: 20,
                              categoria: (GetIt.I.get<ControladorApp>().getConfigWebTreino('PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA')) ? Categoria.comBorda : Categoria.secundario,
                                onTap: () {
                                analytic(EventosKey.abriu_treinos_ia_revisar, recursoEmpresa: true);
                                if ((GetIt.I.get<ControladorApp>().getConfigWebTreino('PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA'))) {
                                    Navigator.of(context).pushNamed( '/telaTreinosAguardandoRevisaoIA');
                                  } else {
                                    DSalerta().exibirAlertaSimplificado(context: context, titulo: localizedString('prescricao_por_ia'), subtitulo: localizedString('recurso_ia'), tituloBotao: localizedString('got_it'));
                                  }
                                },
                              ),
                              const SizedBox(height: 8),
                              DStextCaption1(
                                localizedString('prescricao_ia'),
                                eHeavy: false,
                              )
                            ],
                          ),
                        ),
                        Column(
                          children: [
                            DSbotaoCircular(icone: TreinoIcon.Kettlebell, alturaIcone: 20, categoria: Categoria.comBorda,
                              onTap: () {
                                Navigator.of(context).pushNamed('/telaListaWods');
                              },
                            ),
                            const SizedBox(height: 8),
                            DStextCaption1(
                              'Wods',
                              eHeavy: false,
                              textAlign: TextAlign.center,
                            )
                          ],
                        )
                      ].map((e) => Expanded(child: e)).toList(),
                    )),
                  ItemExecucaoTreino(),
                  SizedBox(height: Navigator.of(context).canPop() ? 24 : 80)
                ],
              ),
            ),
        ),
    );
  }

  mostarMaisOpcoes(BuildContext context, {Function()? remarcar, Function()? desmarcar}) {
    DSBottomSheet().exibirAlerta(context, null, Column(
      children: [
        InkWell(
                        onTap: () {
                          analytic(EventosKey.drawer_pressionou_configurar_app);
                          Navigator.of(context).pop();
                          Navigator.pushNamed(context, '/telaConfiguracoes');
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 16, right: 16, top: 4, bottom: 4),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  const Icon(TreinoIcon.setting),
                                  const SizedBox(width: 8),
                                  DStextHeadline('menu_perfil.configurar'),
                                ],
                              ),
                              const Padding(
                                padding: EdgeInsets.only(top: 8),
                                child: Divider(),
                              )
                            ],
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                          Navigator.pushNamed(context, '/telaFeed',
                              arguments: true);
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 16, right: 16, top: 4, bottom: 4),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  const Icon(TreinoIcon.image),
                                  const SizedBox(width: 8),
                                  DStextHeadline('menu_perfil.minhas_publicacoes'),
                                ],
                              ),
                             const Padding(
                                padding: EdgeInsets.only(top: 8),
                                child: Divider(),
                              )
                            ],
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                          Navigator.pushNamed(context, '/telaSobre');
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 16, right: 16, top: 4, bottom: 4),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  const Icon(TreinoIcon.info_circle),
                                  const SizedBox(width: 8),
                                  DStextHeadline('menu_perfil.sobre'),
                                ],
                              ),
                             const Padding(
                                padding: EdgeInsets.only(top: 8),
                                child: Divider(),
                              )
                            ],
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          analytic(EventosKey.drawer_pressionou_logout);
                          DSalerta().exibirAlerta(context: context, titulo: 'menu_perfil.deseja_deslogar', subtitulo: 'menu_perfil.aviso_logout', tituloBotao: 'menu_perfil.sair', tituloBotaoSecundario: 'cancelar_texto', onTap: () {
                            analytic(EventosKey.drawer_confirmou_logout);
                            _controladorCliente.deslogarUsuario(  sucesso: () {
                              GetIt.I.get<ControladorApp>().limparTodosControladores();
                              FocusScope.of(context).requestFocus(FocusNode());
                        Navigator.of(context)
                            .pushNamedAndRemoveUntil(controlladorApp.desabilitarNovoFluxoLogin ? '/buscarCadastroPorTelefone' : '/telaSelecaoMetodoLogin', (Route<dynamic> route) => false);
                            });
                          });
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 16, right: 16, top: 4, bottom: 4),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  const Icon(TreinoIcon.sign_out_alt),
                                  const SizedBox(width: 8),
                                  DStextHeadline('encerrar_sessao'),
                                ],
                              ),
                              const SizedBox(height: 32)
                            ],
                          ),
                        ),
                      )
      ],
    ));    
  }

}