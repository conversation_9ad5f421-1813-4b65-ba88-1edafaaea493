// ignore_for_file: unused_local_variable

import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/observer_widget.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorExecucaoTreino.dart';
import 'package:app_treino/controlladores/ControladorTreinoAluno.dart';
import 'package:app_treino/controlladores/controladorPlanner.dart';
import 'package:app_treino/screens/_treino6/CelulaTreino.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:get_it/get_it.dart';
import 'package:skeleton_text/skeleton_text.dart';

class TelaFichasVerMais extends StatefulWidget {
  const TelaFichasVerMais({super.key});

  @override
  State<TelaFichasVerMais> createState() => _TelaFichasVerMaisState();
}

class _TelaFichasVerMaisState extends State<TelaFichasVerMais> {
  final controladorProgramaTreino = GetIt.I.get<ControladorTreinoAluno>();
  bool? veioDaTelaDeTreinoExtra = false;

  @override
  void initState() {
    super.initState();
    _verificarConsistenciaFichas();
  }

  void _verificarConsistenciaFichas() {
    if (controladorProgramaTreino.mProgramaCarregado?.programa?.fichas == null) {
      return;
    }

    final Map<String, String> codigoParaNome = {};
    bool inconsistenciaEncontrada = false;

    for (final ficha in controladorProgramaTreino.mProgramaCarregado!.programa!.fichas!) {
      if (ficha == null || ficha.cod == null) continue;

      String codigoFicha = ficha.cod.toString();
      String? nomeFicha = ficha.nome;

      if (codigoParaNome.containsKey(codigoFicha)) {
        String nomeExistente = codigoParaNome[codigoFicha]!;

        if (nomeFicha != null && nomeExistente != nomeFicha) {
          inconsistenciaEncontrada = true;
          break;
        }
      } else if (nomeFicha != null) {
        codigoParaNome[codigoFicha] = nomeFicha;
      }
    }

    if (inconsistenciaEncontrada) {
      _atualizarProgramaDeTreino();
    }
  }

  void _atualizarProgramaDeTreino() {
    GetIt.I.get<ControladorPlanner>().obterFichaDoDia(sucesso: () {
      setState(() {});
      GetIt.I.get<ControladorPlanner>().tituloErro = '';
      GetIt.I.get<ControladorPlanner>().subtituloErro = '';
    }, falha: (falha) {
      final nome = UtilitarioApp.sentenseCaseFirst(GetIt.I.get<ControladorCliente>().mUsuarioLogado!.nome!.split(' ')[0]);
      if (falha.toLowerCase().contains('aluno não possui autorização de acesso.')) {
        GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('pendencias_encontradas');
        GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('existem_parcelas_vencidas_no_seu_contrato');
        GetIt.I.get<ControladorExecucaoTreino>().finalizarTreinoEmExecucao();
      } else if (falha.toLowerCase().contains('Treino vencido. Contate seu Professor!'.toLowerCase())) {
        GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('treino_vencido');
        GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('seu_programa_de_treino_chegou_ao_fim');
      } else if (falha.toLowerCase().contains('você já executou')) {
        GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('seu_treino_chegou_ao_fim');
        GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('todos_os_treinos_ja_foram_realizados');
      } else if (falha.toLowerCase().contains('nenhum treino. contate seu professor!')) {
        GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('sem_programa_treino');
        GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('o_seu_professor_ainda_nao_gerou_seu_programa');
        GetIt.I.get<ControladorExecucaoTreino>().finalizarTreinoEmExecucao();
      } else if (falha.toString().contains('Sem conexão com a internet')) {
        GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('internet_off');
        GetIt.I.get<ControladorPlanner>().subtituloErro = localizedString('nao_foi_possivel_validar_programa_de_treino');
      } else if (falha.toString().contains('Professor está realizando a validação do seu treino')) {
        GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('seu_treino_esta_em_revisao');
        GetIt.I.get<ControladorPlanner>().subtituloErro = localizedString('aguarde_alguns_instantes');
      } else if (falha.toString().toLowerCase().contains('o treino está aguardando aprovação do professor.'.toLowerCase())) {
        GetIt.I.get<ControladorPlanner>().tituloErro = 'seu_treino_esta_em_revisao';
        GetIt.I.get<ControladorPlanner>().subtituloErro = 'seu_treino_estara_disponivel_quando_aprovado';
      } else {
        GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('ops_nao_foi_possivel_consultar');
        GetIt.I.get<ControladorPlanner>().subtituloErro = localizedString('nao_foi_possivel_validar_programa_de_treino');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    veioDaTelaDeTreinoExtra = ModalRoute.of(context)!.settings.arguments as bool?;
    return Scaffold(
      appBar: DSappBar(
        titulo: 'suas_fichas',
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: ObserverWidget(
          observer: () => controladorProgramaTreino.mStatusConsultaPrograma,
          carregando: (_) => _buildSkeletonFichas(),
          erro: (_) => _buildErrorState(),
          vazio: (_) => _buildEmptyState(),
          sucesso: (context) {
            return ListView.separated(
                itemBuilder: (context, index) {
                  return Observer(builder: (context) {
                    AppLifecycleState estadoApp = GetIt.I.get<ControladorApp>().estadoApp;
                    return CelulaTreino(
                      ficha: (veioDaTelaDeTreinoExtra ?? false)
                          ? controladorProgramaTreino.mProgramaCarregado!.programa!.getListaFichasComFichaDoDiaNaPrimeiraPosicao()[index]!
                          : controladorProgramaTreino.mProgramaCarregado!.programa!.outrosTreinos()[index]!,
                      veioDaTelaDeTreinoExtra: veioDaTelaDeTreinoExtra,
                    );
                  });
                },
                separatorBuilder: (context, index) {
                  return const SizedBox(height: 0);
                },
                itemCount: (veioDaTelaDeTreinoExtra ?? false)
                    ? (controladorProgramaTreino.mProgramaCarregado?.programa?.getListaFichasComFichaDoDiaNaPrimeiraPosicao().length ?? 0)
                    : (controladorProgramaTreino.mProgramaCarregado?.programa?.outrosTreinos().length ?? 0));
          }),
    );
  }

// Skeleton para o estado de carregamento
  Widget _buildSkeletonFichas() {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: DScard(
            paddingInterno: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SkeletonAnimation(
                      shimmerColor: Theme.of(context).dividerColor,
                      borderRadius: BorderRadius.circular(50),
                      shimmerDuration: 1000,
                      child: Container(
                        width: 120,
                        height: 22,
                        decoration: BoxDecoration(
                          color: Theme.of(context).canvasColor,
                          borderRadius: BorderRadius.circular(50),
                        ),
                      ),
                    ),
                    const Spacer(),
                    SkeletonAnimation(
                      shimmerColor: Theme.of(context).dividerColor,
                      borderRadius: BorderRadius.circular(50),
                      shimmerDuration: 1000,
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Theme.of(context).canvasColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                SkeletonAnimation(
                  shimmerColor: Theme.of(context).dividerColor,
                  borderRadius: BorderRadius.circular(50),
                  shimmerDuration: 1000,
                  child: Container(
                    width: double.infinity,
                    height: 14,
                    decoration: BoxDecoration(
                      color: Theme.of(context).canvasColor,
                      borderRadius: BorderRadius.circular(50),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                SkeletonAnimation(
                  shimmerColor: Theme.of(context).dividerColor,
                  borderRadius: BorderRadius.circular(50),
                  shimmerDuration: 1000,
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.7,
                    height: 14,
                    decoration: BoxDecoration(
                      color: Theme.of(context).canvasColor,
                      borderRadius: BorderRadius.circular(50),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SkeletonAnimation(
                      shimmerColor: Theme.of(context).dividerColor,
                      borderRadius: BorderRadius.circular(8),
                      shimmerDuration: 1000,
                      child: Container(
                        width: 80,
                        height: 30,
                        decoration: BoxDecoration(
                          color: Theme.of(context).canvasColor,
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                    SkeletonAnimation(
                      shimmerColor: Theme.of(context).dividerColor,
                      borderRadius: BorderRadius.circular(50),
                      shimmerDuration: 1000,
                      child: Container(
                        width: 120,
                        height: 16,
                        decoration: BoxDecoration(
                          color: Theme.of(context).canvasColor,
                          borderRadius: BorderRadius.circular(50),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

// Estado de erro
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          DSsvg(
            imagem: Imagem.altere,
            altura: 150,
            largura: 150,
          ),
          const SizedBox(height: 16),
          DStextHeadline(
            GetIt.I.get<ControladorPlanner>().tituloErro.isEmpty ? 'ops_nao_foi_possivel_consultar' : GetIt.I.get<ControladorPlanner>().tituloErro,
            textAlign: TextAlign.center,
            eHeavy: true,
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: DStextBody(
              GetIt.I.get<ControladorPlanner>().subtituloErro.isEmpty ? 'nao_foi_possivel_validar_programa_de_treino' : GetIt.I.get<ControladorPlanner>().subtituloErro,
              textAlign: TextAlign.center,
              ePrimario: false,
            ),
          ),
          const SizedBox(height: 24),
          DSbotaoPadrao(
            titulo: 'tentar_novamente',
            onTap: () => _atualizarProgramaDeTreino(),
          ),
        ],
      ),
    );
  }

// Estado vazio
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          DSsvg(
            imagem: Imagem.busca,
            altura: 150,
            largura: 150,
          ),
          const SizedBox(height: 16),
          DStextHeadline(
            'nenhuma_ficha_encontrada',
            textAlign: TextAlign.center,
            eHeavy: true,
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: DStextBody(
              'professor_ainda_nao_criou_fichas',
              textAlign: TextAlign.center,
              ePrimario: false,
            ),
          ),
        ],
      ),
    );
  }
}
