import 'package:activity_ring/activity_ring.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/config/personal_icon_icons.dart';
import 'package:app_treino/controlladores/ControladorAvaliacaoFisica.dart';
import 'package:app_treino/model/DadosParaBioempedancia.dart';
import 'package:ds_pacto/custom_slider.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'dart:math';

class TelaResultadoBioempedancia extends StatefulWidget {
  const TelaResultadoBioempedancia({super.key});

  @override
  State<TelaResultadoBioempedancia> createState() => _TelaResultadoBioempedanciaState();
}

class _TelaResultadoBioempedanciaState extends State<TelaResultadoBioempedancia> {
  final _controladorAvaliacaoFisica = GetIt.I.get<ControladorAvaliacaoFisica>();
  int indexAanaliseSegmentar = 0;
  @override
  Widget build(BuildContext context) {
    DadosParaBiompendancia args = ModalRoute.of(context)!.settings.arguments as DadosParaBiompendancia;
    return Scaffold(
      appBar: DSappBar(
        backgroundColor: Theme.of(context).colorScheme.surface,
        titulo: 'conectar_balanca_22',
      ),
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SingleChildScrollView(
          child: Column(
        children: [
          DScard(
            paddingExterno: const EdgeInsets.all(16),
            paddingInterno: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Container(
                      height: 30,
                      width: 30,
                      decoration: BoxDecoration(borderRadius: BorderRadius.circular(36), color: DSLib.theme == ThemeMode.dark ? Colors.black : const Color(0xffDFDFDF)),
                      child: Center(
                        child: SizedBox(
                          height: 15,
                          width: 15,
                          child: SvgPicture.asset('assets/images/body.svg',
                              colorFilter: ColorFilter.mode(DSLib.theme == ThemeMode.dark ? const Color(0xffF5F5F5) : const Color(0xff3F3F3F), BlendMode.srcIn)),
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    DStextSubheadline(
                      'Peso',
                    )
                  ],
                ),
                graficoSegmentadoDonut(context,
                    valorNormal: 60,
                    maiorValor: 200,
                    valorBom: 80,
                    valorReferencia: (_controladorAvaliacaoFisica.resultadoBioimpedancia?.weightScaleData.weight ?? 1).toDouble(),
                    titulo: 'kg'),
              ],
            ),
          ),
          DScard(
            paddingExterno: const EdgeInsets.all(16),
            paddingInterno: const EdgeInsets.fromLTRB(16, 16, 16, 32),
            child: Column(
              children: [
                Row(
                  children: [
                    ringBuilder(context,
                        percent: ((_controladorAvaliacaoFisica.resultadoBioimpedancia?.bodyFatData.visceralFatLevel ?? 0.0) / 30) * 100,
                        value: (_controladorAvaliacaoFisica.resultadoBioimpedancia?.bodyFatData.visceralFatLevel ?? 0.0),
                        subtitle: '%',
                        title: 'conectar_balanca_45'),
                    const Spacer(),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(0, 0, 46, 0),
                      child: ringBuilder(context,
                          percent: ((_controladorAvaliacaoFisica.resultadoBioimpedancia?.massaMuscular ?? 0.0) /
                                  (_controladorAvaliacaoFisica.resultadoBioimpedancia?.weightScaleData.weight ?? 1).toDouble()) *
                              100,
                          value: (_controladorAvaliacaoFisica.resultadoBioimpedancia?.massaMuscular ?? 0.0),
                          subtitle: 'KG',
                          title: 'conectar_balanca_46'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          DScard(
            paddingExterno: const EdgeInsets.all(16),
            paddingInterno: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Container(
                      height: 30,
                      width: 30,
                      decoration: BoxDecoration(borderRadius: BorderRadius.circular(36), color: DSLib.theme == ThemeMode.dark ? Colors.black : const Color(0xffDFDFDF)),
                      child: const Center(
                        child: Icon(
                          TreinoIcon.pizza_slice,
                          size: 13,
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    DStextSubheadline(
                      'saudeanalisesegmentar',
                    )
                  ],
                ),
                () {
                  if (indexAanaliseSegmentar == 0) {
                    return graficoSegmentadoDonut(context,
                        valorNormal: _controladorAvaliacaoFisica.mListaCorporal[0].nivel?.bom ?? 0,
                        maiorValor: (_controladorAvaliacaoFisica.mListaCorporal[1].nivel?.muitoBom ?? 0) + 50,
                        valorBom: _controladorAvaliacaoFisica.mListaCorporal[1].nivel?.muitoBom ?? 0,
                        valorReferencia: (_controladorAvaliacaoFisica.resultadoBioimpedancia?.bodyCompositionData.softLeanMass ?? 1).toDouble(),
                        titulo: '%');
                  } else {
                    return graficoSegmentadoDonut(context,
                        valorNormal: _controladorAvaliacaoFisica.mListaCorporal[1].nivel?.bom ?? 0,
                        maiorValor: (_controladorAvaliacaoFisica.mListaCorporal[1].nivel?.muitoBom ?? 0) + 50,
                        valorBom: _controladorAvaliacaoFisica.mListaCorporal[1].nivel?.muitoBom ?? 0,
                        valorReferencia: (_controladorAvaliacaoFisica.resultadoBioimpedancia?.bodyCompositionData.bodyFat ?? 1).toDouble(),
                        titulo: '%');
                  }
                }.call(),
                DSSliderSegmentado<int>(
                  height: 30,
                  onValueChanged: (indexSelecionado) {
                    setState(() {
                      indexAanaliseSegmentar = indexSelecionado;
                    });
                  },
                  children: {
                    0: 'saudemassamagra',
                    1: 'gordura_simples',
                  },
                )
              ],
            ),
          ),
          DScard(
              paddingExterno: const EdgeInsets.all(16),
              paddingInterno: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        height: 30,
                        width: 30,
                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(36), color: DSLib.theme == ThemeMode.dark ? Colors.black : const Color(0xffDFDFDF)),
                        child: Center(
                          child: SizedBox(
                            height: 15,
                            width: 15,
                            child: SvgPicture.asset('assets/images/body.svg',
                                colorFilter: ColorFilter.mode(DSLib.theme == ThemeMode.dark ? const Color(0xffF5F5F5) : const Color(0xff3F3F3F), BlendMode.srcIn)),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      DStextSubheadline('conectar_balanca_25')
                    ],
                  ),
                  itemGraficoPosicional(false, 20,
                      menorValor: 18.5,
                      maiorValor: 25,
                      medioValor: 30,
                      valorReferencia: (_controladorAvaliacaoFisica.resultadoBioimpedancia?.weightScaleData.weight ?? 1).toDouble() /
                          pow((_controladorAvaliacaoFisica.dadosBasicos.altura ?? 1) / 100, 2)),
                  const SizedBox(
                    height: 16,
                  ),
                  DStextBody(
                    () {
                      double imc =
                          (_controladorAvaliacaoFisica.resultadoBioimpedancia?.weightScaleData.weight ?? 1).toDouble() / pow((_controladorAvaliacaoFisica.dadosBasicos.altura ?? 1) / 100, 2);

                      if (imc < 18.5) {
                        return 'Seu percentual de massa corporal\n encontra-se em um nível baixo';
                      } else if (imc >= 18.5 && imc < 25) {
                        return 'Seu percentual de massa corporal\n encontra-se em um nível normal';
                      } else if (imc >= 25 && imc < 30) {
                        return 'Seu percentual de massa corporal\n encontra-se em um nível de sobrepeso';
                      } else {
                        return 'Seu percentual de massa corporal\n encontra-se em um nível de obesidade';
                      }
                    }(),
                    eHeavy: false,
                    ePrimario: false,
                  ),
                  const SizedBox(
                    height: 8,
                  ),
                  buildNiveisPesoIMC(context, titulo: 'Baixo peso', valor: '<18.5'),
                  buildNiveisPesoIMC(context, titulo: 'Peso normal', valor: '<18.5 - 24.9'),
                  buildNiveisPesoIMC(context, titulo: 'Sobrepeso', valor: '25 - 29.9'),
                  buildNiveisPesoIMC(context, titulo: 'Obesidade', valor: '>30', ocultarDivider: true),
                ],
              )),
          cardDadosBasicos(icone: TreinoIcon.ruler, label: 'conectar_balanca_24', valor: ((_controladorAvaliacaoFisica.dadosBasicos.altura ?? 1) / 100).toStringAsFixed(2)),
          cardDadosBasicos(icone: TreinoIcon.water_glass, label: 'conectar_balanca_28', valor: _controladorAvaliacaoFisica.valorPercentualAgua.toStringAsFixed(0), subtitle: '%'),
          cardDadosBasicos(icone: TreinoIcon.arrow_growth, label: 'conectar_balanca_29', valor: ((_controladorAvaliacaoFisica.obterTMBPorDadosBasicos())).toStringAsFixed(0), subtitle: ''),
          const SizedBox(
            height: 100,
          ),
        ],
      )),
      bottomSheet: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SizedBox(
            height: 44,
            child: DSbotaoPadrao(
              titulo: 'conectar_balanca_30',
              onTap: () {
                GetIt.I.get<ControladorAvaliacaoFisica>().cadastrarAvaliacaoBioimpedancia(
                      aluno: args.aluno,
                      carregando: () {
                        UtilitarioApp().showDialogCarregando(context);
                      },
                      sucesso: () async {
                        Navigator.of(context).popUntil(ModalRoute.withName('/telaDetalhesDoAluno'));
                        await DSalertaSucesso().exibirAlerta(context: context);
                      },
                      falha: (erro) {
                        Navigator.of(context).popUntil(ModalRoute.withName('/telaDetalhesDoAluno'));
                        DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Ops!', subtitulo: 'conectar_balanca_31', tituloBotao: 'go_back');
                      },
                    );
              },
            )),
      ),
    );
  }

  SizedBox graficoSegmentadoDonut(BuildContext context,
      {required double valorNormal, required double maiorValor, required double valorReferencia, required double valorBom, String titulo = ''}) {
    double porcentagemMedioValor = valorNormal - valorReferencia;
    porcentagemMedioValor = porcentagemMedioValor < 0 ? 0 : porcentagemMedioValor;
    double porcentagemValorBom = maiorValor - (porcentagemMedioValor + valorReferencia);
    porcentagemValorBom = porcentagemValorBom < 0 ? 0 : porcentagemValorBom;
    return SizedBox(
      height: 160,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned(
            top: 50,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  height: 160,
                  child: SfCircularChart(palette: [
                    0 == 0
                        ? HSLColor.fromColor(Theme.of(context).primaryColor).withLightness(0.50).withSaturation(1).toColor()
                        : HSLColor.fromColor(Theme.of(context).primaryColor).withLightness(0.90).withSaturation(0.90).toColor(),
                    0 == 1
                        ? HSLColor.fromColor(Theme.of(context).primaryColor).withLightness(0.85).withSaturation(0.80).toColor()
                        : HSLColor.fromColor(Theme.of(context).primaryColor).withLightness(0.95).withSaturation(0.5).toColor(),
                    0 == 2
                        ? HSLColor.fromColor(Theme.of(context).primaryColor).withLightness(0.40).withSaturation(0.75).toColor()
                        : HSLColor.fromColor(Theme.of(context).primaryColor).withLightness(0.80).withSaturation(0.30).toColor(),
                    0 == 3
                        ? HSLColor.fromColor(Theme.of(context).primaryColor).withLightness(0.25).withSaturation(0.7).toColor()
                        : HSLColor.fromColor(Theme.of(context).primaryColor).withLightness(0.85).withSaturation(0.15).toColor()
                  ], series: <CircularSeries>[
                    DoughnutSeries<ChartData, String>(dataSource: [
                      ChartData(localizedString('muscles'), valorReferencia),
                      ChartData(localizedString('fat'), porcentagemMedioValor),
                      ChartData(localizedString('residue'), porcentagemValorBom),
                    ], xValueMapper: (ChartData data, _) => data.x, yValueMapper: (ChartData data, _) => data.y, startAngle: 270, endAngle: 90, radius: '126', innerRadius: '102')
                  ]),
                ),
              ],
            ),
          ),
          const Positioned(
            top: 40,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  width: 4,
                ),
              ],
            ),
          ),
          Positioned(
            top: 70,
            left: 0,
            right: 0,
            child: Center(
                child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                DStextDisplay2('${valorReferencia}', eCorPadrao: true),
                const SizedBox(
                  width: 4,
                ),
                DStextCaption1(
                  titulo,
                  eHeavy: false,
                  eCorPadrao: true,
                )
              ],
            )),
          ),
        ],
      ),
    );
  }

  Column buildNiveisPesoIMC(BuildContext context, {String titulo = '', String valor = '', bool ocultarDivider = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 8,
        ),
        Row(
          children: [
            Container(
              height: 8,
              width: 8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(
              width: 8,
            ),
            DStextBody(
              titulo,
              eHeavy: false,
            )
          ],
        ),
        Padding(
          padding: const EdgeInsets.only(left: 16),
          child: DStextCaption1(
            '${valor}',
            eHeavy: false,
            ePrimario: false,
          ),
        ),
        const SizedBox(height: 4),
        if (!ocultarDivider) const Divider()
      ],
    );
  }

  DScard cardDadosBasicos({IconData? icone, String? label, String? valor, String? subtitle}) {
    return DScard(
        paddingExterno: const EdgeInsets.all(16),
        paddingInterno: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
                height: 48,
                width: 48,
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(36), color: DSLib.theme == ThemeMode.dark ? Colors.black : const Color(0xffDFDFDF)),
                child: Icon(icone ?? TreinoIcon.Kettlebell)),
            const SizedBox(
              width: 16,
            ),
            DStextSubheadline(label ?? ''),
            const Spacer(),
            DStextDisplay3(valor ?? ''),
            const SizedBox(
              width: 8,
            ),
            DStextCaption1(
              subtitle ?? 'm',
              eHeavy: false,
            )
          ],
        ));
  }

  SizedBox ringBuilder(BuildContext context, {required double percent, required double value, String title = '', String? subtitle}) {
    return SizedBox(
      height: 160,
      child: Column(
        children: [
          DStextSubheadline(title),
          const SizedBox(
            height: 30,
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(46, 32, 0, 0),
            child: SizedBox(
              height: ((MediaQuery.of(context).size.width * 0.5) - 48) / 4,
              child: FittedBox(
                child: Ring(
                  percent: percent,
                  color: RingColorScheme(ringGradient: [
                    Theme.of(context).primaryColor,
                    Theme.of(context).primaryColor,
                    escurecerCor(Theme.of(context).primaryColor, 0.1),
                    escurecerCor(Theme.of(context).primaryColor, 0.2),
                  ], backgroundColor: Theme.of(context).dividerColor, ringColor: Theme.of(context).primaryColor),
                  radius: 60,
                  width: 20,
                  animate: true,
                  child: Center(
                      child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          DStextDisplay3(
                            '${value.toStringAsFixed(0)}',
                          ),
                          if (subtitle != null)
                            DStextSubheadline(
                              subtitle,
                              eHeavy: false,
                            )
                        ],
                      ),
                    ],
                  )),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Padding itemGraficoPosicional(bool imc, double medioValorIMC, {required double menorValor, required double maiorValor, required double medioValor, required double valorReferencia}) {
    return Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (valorReferencia <= menorValor)
                    buildCardValorImc(valorReferencia)
                  else
                    const SizedBox(
                      height: 28,
                    ),
                  valorReferencia <= menorValor
                      ? Container(
                          height: 25,
                          decoration: BoxDecoration(color: Theme.of(context).primaryColor /*  DSLib.theme == ThemeMode.dark ? Color(0xff202020) : Color(0xFFDCDDDF) */
                              //borderRadius: BorderRadius.only(bottomLeft: Radius.circular(20), topLeft: Radius.circular(20))
                              ),
                          child: Center(child: DStextCaption1('niveis_corporal.baixo', eHeavy: false, ePrimario: true, corCustomizada: corQuandoFundoForGradiente(context))),
                        )
                      : Container(
                          height: 4,
                          decoration: BoxDecoration(color: DSLib.theme == ThemeMode.dark ? const Color(0xff424242) : const Color(0xFFDCDDDF)),
                        ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 24),
              child: Container(
                height: 25,
                width: 1.5,
                decoration: BoxDecoration(color: DSLib.theme == ThemeMode.dark ? const Color(0xffB3B3B3) : const Color(0xFF838383)),
              ),
            ),
            Expanded(
              child: Column(
                children: [
                  if (valorReferencia > menorValor && valorReferencia < maiorValor)
                    buildCardValorImc(valorReferencia)
                  else
                    const SizedBox(
                      height: 28,
                    ),
                  valorReferencia > menorValor && valorReferencia < maiorValor
                      ? Container(
                          height: 25,
                          color: Theme.of(context).primaryColor,
                          //DSLib.theme == ThemeMode.dark ? Color(0xff202020) : Color(0xFFDCDDDF),
                          child: Center(child: DStextCaption1('niveis_corporal.normal', eHeavy: false, ePrimario: true, corCustomizada: corQuandoFundoForGradiente(context))),
                        )
                      : Container(
                          height: 4,
                          decoration: BoxDecoration(color: DSLib.theme == ThemeMode.dark ? const Color(0xff424242) : const Color(0xFFDCDDDF)),
                        ),
                ],
              ),
            ),
            imc
                ? Padding(
                    padding: const EdgeInsets.only(top: 24),
                    child: Container(
                      height: 25,
                      width: 1.5,
                      decoration: BoxDecoration(color: DSLib.theme == ThemeMode.dark ? const Color(0xffB3B3B3) : const Color(0xFF838383)),
                    ),
                  )
                : Container(),
            imc
                ? Expanded(
                    child: Column(
                      children: [
                        if (valorReferencia > menorValor && valorReferencia > medioValorIMC && valorReferencia < maiorValor)
                          buildCardValorImc(valorReferencia)
                        else
                          const SizedBox(
                            height: 28,
                          ),
                        valorReferencia > menorValor && valorReferencia > medioValorIMC && valorReferencia < maiorValor
                            ? Container(
                                height: 25,
                                color: Theme.of(context).primaryColor,
                                //DSLib.theme == ThemeMode.dark ? Color(0xff202020) : Color(0xFFDCDDDF),
                                child: Center(child: DStextCaption1('niveis_corporal.sobrepeso', eHeavy: false, ePrimario: true, corCustomizada: corQuandoFundoForGradiente(context))),
                              )
                            : Container(
                                height: 4,
                                decoration: BoxDecoration(color: DSLib.theme == ThemeMode.dark ? const Color(0xff424242) : const Color(0xFFDCDDDF)),
                              ),
                      ],
                    ),
                  )
                : Container(),
            Padding(
              padding: const EdgeInsets.only(top: 24),
              child: Container(
                height: 25,
                width: 1.5,
                decoration: BoxDecoration(color: DSLib.theme == ThemeMode.dark ? const Color(0xffB3B3B3) : const Color(0xFF838383)),
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (valorReferencia > maiorValor)
                    buildCardValorImc(valorReferencia)
                  else
                    const SizedBox(
                      height: 28,
                    ),
                  valorReferencia > maiorValor
                      ? Container(
                          height: 25,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            //DSLib.theme == ThemeMode.dark ? Color(0xff202020) : Color(0xFFDCDDDF),
                            //borderRadius: BorderRadius.only(bottomRight: Radius.circular(20), topRight: Radius.circular(20))
                          ),
                          child: Center(
                              child: DStextCaption1(
                            imc ? 'niveis_corporal.obesidade' : 'niveis_corporal.elevado',
                            eHeavy: false,
                            ePrimario: true,
                            corCustomizada: corQuandoFundoForGradiente(context),
                          )),
                        )
                      : Container(
                          height: 4,
                          decoration: BoxDecoration(color: DSLib.theme == ThemeMode.dark ? const Color(0xff424242) : const Color(0xFFDCDDDF)),
                        ),
                ],
              ),
            )
          ],
        ));
  }

  Stack buildCardValorImc(double valorReferencia) {
    return Stack(
      alignment: Alignment.center,
      children: [
        const SizedBox(
          height: 28,
        ),
        Positioned(
          bottom: 9,
          child: Container(
            decoration: BoxDecoration(color: HSLColor.fromColor(Theme.of(context).primaryColor).withLightness(0.70).withSaturation(0.70).toColor(), borderRadius: BorderRadius.circular(10)),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: DStextCaption1(valorReferencia.toStringAsFixed(2)),
            ),
          ),
        ),
        Center(
            child: Icon(
          PersonalIcon.caret_down,
          color: HSLColor.fromColor(Theme.of(context).primaryColor).withLightness(0.70).withSaturation(0.70).toColor(),
        )),
      ],
    );
  }
}
