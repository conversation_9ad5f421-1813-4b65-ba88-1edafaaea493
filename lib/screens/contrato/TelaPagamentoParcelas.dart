import 'dart:convert';
import 'dart:math' as math;
import 'package:after_layout/after_layout.dart';
import 'package:app_treino/ServiceProvider/ContratoUsuarioService.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/TextWidgets.dart';
import 'package:app_treino/appWidgets/componentWidgets/boleto/boletoWebView.dart';
import 'package:app_treino/appWidgets/componentWidgets/image_widget.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorVendaDePlano.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/screens/contrato/emptyCases/EmptyWidget.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:ds_pacto/ds_pacto.dart' as masked;
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:get_it/get_it.dart';
import 'package:skeleton_text/skeleton_text.dart';

class TelaPagamentoParcelas extends StatefulWidget {
  const TelaPagamentoParcelas({super.key});

  @override
  State<TelaPagamentoParcelas> createState() => _TelaPagamentoParcelasState();
}

class _TelaPagamentoParcelasState extends State<TelaPagamentoParcelas> with AfterLayoutMixin<TelaPagamentoParcelas> {
  final _mControladorVendasDePlano = GetIt.I.get<ControladorVendaDePlano>();
  final TextEditingController _controllerCartaoCredito = TextEditingController();
  final TextEditingController _controladorCartaoCPF = TextEditingController();
  final FocusNode _numberFocus = FocusNode();
  final FocusNode _nomeFocus = FocusNode();
  final FocusNode _cpfFocus = FocusNode();
  final FocusNode _validadeFocus = FocusNode();
  final FocusNode _cvvFocus = FocusNode();


  int indexPage = 0;
  bool cartaoSelecionado = true;
  bool pixSelecionado = false;
  bool boletoSelecionado = false;
  bool carregando = false;
  bool deuFalha = false;
  Widget get widgetFalha {
    return EmptyWidget(
      titulo: 'unable_to_load_data',
      mensagem: 'ia.tentenovamente',
      margim: const EdgeInsets.all(16),
      imagem: EmptyImage.PRANCHETA,
      child: BotaoPrimario(
        onTap: () {},
        value: localizedString('tentar_novamente'),
      ),
    );
  }

  void carregarDados() {
    cartaoSelecionado = _mControladorVendasDePlano.isPagamentoCartaoHabilitado;
    pixSelecionado = _definirPixSelecionado();
    boletoSelecionado = _definirBoletoSelecionado();
    if(_mControladorVendasDePlano.isPagamentoPixHabilitado) {
      _mControladorVendasDePlano.cobrarParcelasFuturasPix(
      carregando: () {},
      falha: (error) {
        setState(() {
          deuFalha = true;
        });
      },
      sucesso: () {
        setState(() {
          deuFalha = false;
        });
      },
    );
    }
  }

  bool _definirPixSelecionado() {
    if (cartaoSelecionado) return false;
    return _mControladorVendasDePlano.isPagamentoPixHabilitado;
  }

  bool _definirBoletoSelecionado() {
    if (cartaoSelecionado || pixSelecionado) return false;
    return _mControladorVendasDePlano.isPagamentoBoletoHabilitado;
  }

  Widget _buildBotaoPagamento() {
    if (deuFalha || carregando) {
      return Container();
    }

    return DSbotaoPadrao(
      desabilitado: _desabilitarBotao(),
      titulo: _obterTituloBotao(),
      onTap: _obterAcaoBotao(),
    );
  }

  bool _desabilitarBotao() {
    if (cartaoSelecionado) {
      return !_mControladorVendasDePlano.isDAdosCartaoValidoPagamentoParcelaFuturas;
    }
    if (pixSelecionado) {
    final prontoPix = _mControladorVendasDePlano.statusConsultarParcelasAtrasadasPix == ServiceStatus.Done
      && (_mControladorVendasDePlano.dadosPixPagamento.urlQRcode?.isNotEmpty ?? false);
    return !prontoPix;
    }
    if (boletoSelecionado) {
      final existeOuGerado = existeBoletoGerado.isNotEmpty || (_mControladorVendasDePlano.boletoResponse?.sucesso ?? false);
      return !existeOuGerado;
    }
    return true; // nenhuma opção válida selecionada
  }

  String _obterTituloBotao() {
    if (pixSelecionado || boletoSelecionado) {
      return localizedString('finish');
    }
    return localizedString('pasta_contrato.pagar_agora');
  }

  VoidCallback _obterAcaoBotao() {
    if (pixSelecionado) {
      return _processarPagamentoPix;
    }
    if (boletoSelecionado) {
      return _processarPagamentoBoleto;
    }
    return _processarPagamentoCartao;
  }

  Future<void> _processarPagamentoPix() async {
    await DSalertaSucesso().exibirAlerta(context: context);
    if (!mounted) return;
    Navigator.of(context).pushNamedAndRemoveUntil('/telaContratosAluno', ModalRoute.withName('/homePageApp'), arguments: 0);
  }

  Future<void> _processarPagamentoBoleto() async {
    _mControladorVendasDePlano.parcelasContratoSelecionados = [];
    await DSalertaSucesso().exibirAlerta(context: context);
    if (!mounted) return;
    Navigator.of(context).pushNamedAndRemoveUntil('/telaContratosAluno', ModalRoute.withName('/homePageApp'), arguments: 0);
  }

  void _processarPagamentoCartao() {
    _mControladorVendasDePlano.cobrarParcelasFuturasCartao(
      carregando: () {
        if (!mounted) return;
        setState(() => carregando = true);
        UtilitarioApp().showDialogCarregando(context);
      },
      falha: (error) {
        if (!mounted) return;
        if (Navigator.of(context).canPop()) Navigator.of(context).pop(); // fecha o loading
        setState(() => carregando = false);
        _exibirAlertaPagamentoRecusado();
      },
      sucesso: () async {
        if (!mounted) return;
        if (Navigator.of(context).canPop()) Navigator.of(context).pop(); // fecha o loading
        setState(() => carregando = false);
        if ((_mControladorVendasDePlano.retornoCobranca ?? '') == 'APROVADA') {
          await DSalertaSucesso().exibirAlerta(context: context);
          if (!mounted) return;
          Navigator.of(context).pushNamedAndRemoveUntil('/telaContratosAluno', ModalRoute.withName('/homePageApp'), arguments: 0);
        } else {
          _exibirAlertaPagamentoRecusado();
        }
      }
    );
  }

  void _exibirAlertaPagamentoRecusado() {
    DSalerta().exibirAlertaSimplificado(
      context: context,
      titulo: 'pasta_contrato.pagamento_recusado_tituloMensagem',
      subtitulo: 'pasta_contrato.pagamento_recusado_subtituloMensagem',
      tituloBotao: 'continue'
    );
  }

  Widget _buildCardPagamento() {
    if (cartaoSelecionado) {
      return cardCartao();
    }
    if (pixSelecionado) {
      return cardPix();
    }
    return cardBoleto();
  }

  double _calcularTamanhoBotoes(bool cartaoHabilitado, bool boletoHabilitado, bool pixHabilitado) {
    final totalHabilitados = _contarOpcoesHabilitadas(cartaoHabilitado, boletoHabilitado, pixHabilitado);
    final margemLateral = _mControladorVendasDePlano.indexDefaultTabController == 1 ? 40 : 56;
    if (totalHabilitados <= 0) {
      return MediaQuery.of(context).size.width - margemLateral;
    }
    return (MediaQuery.of(context).size.width - margemLateral) / totalHabilitados;

  }

  int _contarOpcoesHabilitadas(bool cartaoHabilitado, bool boletoHabilitado, bool pixHabilitado) {
    int contador = 0;
    if (cartaoHabilitado) contador++;
    if (boletoHabilitado) contador++;
    if (pixHabilitado) contador++;
    return contador;
  }


@override
Future<void> afterFirstLayout(BuildContext context) async {
   consultarSeExisteBoletoGerado();
}

 List<Map<String, dynamic>> existeBoletoGerado = [];
 
  consultarSeExisteBoletoGerado() {
    final _mCliente = GetIt.I.get<ControladorCliente>();
    UtilitarioApp().showDialogCarregando(context);
    List<Future<dynamic>> consultas = [];
    _mControladorVendasDePlano.boletoResponse = null;
    for (final element in _mControladorVendasDePlano.parcelasSelecionadasParaBoleto) {
      consultas.add(GetIt.I.get<ContratoUsuarioService>().gerarBoletoParcelaEspecifica(_mCliente.mUsuarioLogado!.codEmpresa!, num.parse(_mCliente.mUsuarioLogado!.matricula!), element));
    }
    Future.wait(consultas).then((value) {
      Navigator.of(context).pop();
      final boletosGerados = value.where((element) => element.toString().toLowerCase().contains('vencimento')).toList();
      for (final element in boletosGerados) {
        Map<String, dynamic> jsonBoletoGerado = json.decode(element.toString().replaceAll('[', '').replaceAll(']', ''));
         if (!UtilDataHora.dataJaPassouDeHoje(dateTime: UtilDataHora.parseStringToDate(jsonBoletoGerado['dataVencimento']), ateUltimoMinutoDoDia: true)) {
           if (existeBoletoGerado.where((element) => element['linhaDigitavel'] == jsonBoletoGerado['linhaDigitavel']).isEmpty) {
            existeBoletoGerado.add(jsonBoletoGerado);
           }
         }
      }
      setState(() {});
    }).catchError((onError) {
      onError?.call();
    });
  }

  double get valorTotalParcelas {
    double valor = 0.0;
    for (final item in _mControladorVendasDePlano.parcelasContratoSelecionados) {
      valor += (item.valor ?? 0.0);
    }
    return valor;
  }

  @override
  void initState() {
    this.carregarDados();
    super.initState();
  }

  void _fieldFocusChange(BuildContext context, FocusNode currentFocus, FocusNode nextFocus) {
    currentFocus.unfocus();
    FocusScope.of(context).requestFocus(nextFocus);
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: DSappBar(
        backgroundColor: Theme.of(context).colorScheme.surface,
        titulo: 'Checkout',
      ),
      bottomNavigationBar: SizedBox(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
              child: Center(
                child: Observer(
                    builder: (_) {
                      return _buildBotaoPagamento();
                    }),
              ),
            ),
          ],
        ),
      ),
      body: _buildCorpoTela(),
    );
  }

  Widget _buildCorpoTela() {
    if (deuFalha) {
      return widgetFalha;
    }
    if (carregando) {
      return skeletonPagaMentoParcelas();
    }
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 24, 16, 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
                        DScard(
                          paddingInterno: const EdgeInsets.all(16.0),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  const DSbotaoCircular(categoria: Categoria.secundario, icone: TreinoIcon.bill),
                                  const SizedBox(width: 10),
                                  DStextSubheadline('parcelas_selecionadas', eHeavy: true)
                                ],
                              ),
                              const SizedBox(height: 16),
                              DScard(
                                categoria: CategoriaCard.secundario,
                                paddingInterno: const EdgeInsets.fromLTRB(16, 16, 16, 12),
                                child: ListView.builder(
                                    shrinkWrap: true,
                                    physics: const NeverScrollableScrollPhysics(),
                                    itemCount: _mControladorVendasDePlano.parcelasContratoSelecionados.length,
                                    itemBuilder: (_, index) {
                                      return mapBuild(
                                          _mControladorVendasDePlano.parcelasContratoSelecionados[index].descricao!.contains('PARCELA')
                                              ? localizedString('tela_detalhes_contrato.parcela_args', args: [_mControladorVendasDePlano.parcelasContratoSelecionados[index].descricao!.replaceAll(RegExp(r'[^0-9]'), '')])
                                              : _mControladorVendasDePlano.parcelasContratoSelecionados[index].descricao!,
                                          'R\$\ ${_mControladorVendasDePlano.parcelasContratoSelecionados[index].valor.toString()}');
                                    }),
                              ),
                              const SizedBox(height: 16),
                              Container(
                                width: MediaQuery.of(context).size.width - 64,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    color: Theme.of(context).colorScheme.surface /* HSLColor.fromColor(Theme.of(context).primaryColor).withLightness(0.40).toColor().withValues(alpha:0.7) */
                                    ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
                                      child: DStextBody(localizedString('pasta_contrato.total'), eHeavy: true),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: DStextSubheadline(localizedString('pasta_contrato.valor_dolar_real', args: [(valorTotalParcelas).toStringAsFixed(2)]),
                                          eHeavy: true),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                        DefaultTabController(
                          initialIndex: indexPage,
                          length: math.max(1, _mControladorVendasDePlano.indexDefaultTabController),
                          child: Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.fromLTRB(0, 24, 0, 16),
                                child: Container(
                                  height: 40,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8), color: DSLib.theme == ThemeMode.dark ? const Color(0x30767680) : const Color(0x12767680)),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: itensDoDefaultTabController(),
                                  ),
                                ),
                              ),
                              _buildCardPagamento(),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                );
  }

  List<Widget> itensDoDefaultTabController() {
    List<Widget> aux = [];
    final cartaoHabilitado = _mControladorVendasDePlano.isPagamentoCartaoHabilitado;
    final boletoHabilitado = _mControladorVendasDePlano.isPagamentoBoletoHabilitado;
    final pixHabilitado = _mControladorVendasDePlano.isPagamentoPixHabilitado;
    final tamanhoBotoes = _calcularTamanhoBotoes(cartaoHabilitado, boletoHabilitado, pixHabilitado);
    if(cartaoHabilitado) {
      aux.add(Padding(
        padding: const EdgeInsets.all(4.0),
        child: InkWell(
          onTap: () {
            setState(() {
              cartaoSelecionado = true;
              pixSelecionado = false;
              boletoSelecionado = false;
            });
          },
          child: Container(
            height: 32,
            width: tamanhoBotoes,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: cartaoSelecionado
                    ? DSLib.theme == ThemeMode.dark
                        ? const Color(0xff202020)
                        : Colors.white
                    : Colors.transparent),
            child: Center(child: DStextSubheadline('credito', eHeavy: true, textAlign: TextAlign.center, corCustomizada: !cartaoSelecionado ? const Color(0xFFA0A0A0) : null)),
          ),
        ),
      ));
    }
    if(pixHabilitado) {
      aux.add(Padding(
        padding: const EdgeInsets.all(4.0),
        child: InkWell(
          onTap: () {
            setState(() {
              cartaoSelecionado = false;
              pixSelecionado = true;
              boletoSelecionado = false;
            });
          },
          child: Container(
            height: 32,
            width: tamanhoBotoes,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: pixSelecionado
                    ? DSLib.theme == ThemeMode.dark
                        ? const Color(0xff202020)
                        : Colors.white
                    : Colors.transparent),
            child: Center(child: DStextSubheadline('Pix', eHeavy: true, textAlign: TextAlign.center, corCustomizada: !pixSelecionado ? const Color(0xFFA0A0A0) : null)),
          ),
        ),
      ));
    }
    if(boletoHabilitado) {
      aux.add(Padding(
        padding: const EdgeInsets.all(4.0),
        child: InkWell(
          onTap: () {
            setState(() {
              cartaoSelecionado = false;
              pixSelecionado = false;
              boletoSelecionado = true;
            });
          },
          child: Container(
            height: 32,
            width: tamanhoBotoes,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: boletoSelecionado
                    ? DSLib.theme == ThemeMode.dark
                        ? const Color(0xff202020)
                        : Colors.white
                    : Colors.transparent),
            child: Center(child: DStextSubheadline('Boleto', textAlign: TextAlign.center, eHeavy: true, corCustomizada: !boletoSelecionado ? const Color(0xFFA0A0A0) : null)),
          ),
        ),
      ));
    }
    return aux;
  }

  Widget cardCartao() {
    return DScard(
        paddingInterno: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                const DSbotaoCircular(icone: TreinoIcon.credit_card, categoria: Categoria.secundario),
                const SizedBox(width: 10),
                DStextSubheadline(
                  'dados_do_cartao',
                  eHeavy: true,
                )
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                DStextfield(
                  maxLines: 1,
                  inputType: TextInputType.number,
                  hintText: localizedString('numero'),
                  maxLength: 16,
                  focusNode: _numberFocus,
                  controlller: _controllerCartaoCredito,
                  textInputAction: TextInputAction.next,
                  valorNoCampo: _mControladorVendasDePlano.mParcelasPagamento.numeroCartao,
                  onChanged: (text) async {
                    setState(() {
                      _mControladorVendasDePlano.mParcelasPagamento.numeroCartao = _controllerCartaoCredito.text;
                    });
                  },
                  onEditingComplete: () {
                    _fieldFocusChange(context, _numberFocus, _validadeFocus);
                  },
                  prefixIcon: Observer(
                    builder: (_) {
                      return _mControladorVendasDePlano.mParcelasPagamento.numeroCartao != null
                          ? Padding(
                              padding: const EdgeInsets.fromLTRB(8, 0, 8, 8),
                              child: _mControladorVendasDePlano.getCardIcon(
                                  cardNumber: _mControladorVendasDePlano.mParcelasPagamento.numeroCartao != null ? _mControladorVendasDePlano.mParcelasPagamento.numeroCartao : ''),
                            )
                          : Padding(
                              padding: const EdgeInsets.fromLTRB(8, 0, 8, 8),
                              child: _mControladorVendasDePlano.getCardIcon(
                                  cardNumber: _mControladorVendasDePlano.mParcelasPagamento.numeroCartao != null ? _mControladorVendasDePlano.mParcelasPagamento.numeroCartao : ''),
                            );
                    },
                  ),
                ),
                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DStextfield(
                            hintText: 'MM/AAAA',
                            inputType: TextInputType.number,
                            maxLength: 7,
                            focusNode: _validadeFocus,
                            textInputAction: TextInputAction.next,
                            onChanged: (text) {
                              setState(() {
                                _mControladorVendasDePlano.mParcelasPagamento.validade = text;
                              });
                            },
                            mask: masked.MaskedTextController(mask: '00/0000', text: _mControladorVendasDePlano.mParcelasPagamento.validade),
                            onEditingComplete: () {
                              _fieldFocusChange(context, _validadeFocus, _cvvFocus);
                            },
                          ),
                          Observer(
                              builder: (_) {
                                return Visibility(
                                    visible: (_mControladorVendasDePlano.mParcelasPagamento.validade ?? '').isNotEmpty ? (!_mControladorVendasDePlano.validarDataCartao(_mControladorVendasDePlano.mParcelasPagamento.validade ?? '')) : false,
                                    child: TextOverLine1(
                                      'pasta_contrato.validade_invalida',
                                      customColor: Colors.red,
                                    ));
                              })
                        ],
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      flex: 2,
                      child: DStextfield(
                        inputType: TextInputType.number,
                        maxLength: 5,
                        focusNode: _cvvFocus,
                        textInputAction: TextInputAction.next,
                        valorNoCampo: _mControladorVendasDePlano.mParcelasPagamento.cvv != null ? _mControladorVendasDePlano.mParcelasPagamento.cvv.toString() : null,
                        onChanged: (text) {
                          _mControladorVendasDePlano.mParcelasPagamento.cvv = text;
                        },
                        hintText: localizedString('cvc').toUpperCase(),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp('[0-9 {3, 4}]')),
                        ],
                        onEditingComplete: () {
                          _fieldFocusChange(context, _cvvFocus, _nomeFocus);
                        },
                      ),
                    ),
                  ],
                ),
                DStextfield(
                  maxLength: 100,
                  focusNode: _nomeFocus,
                  textInputAction: TextInputAction.next,
                  valorNoCampo: _mControladorVendasDePlano.mParcelasPagamento.nomeCartao,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp('[a-z A-Z á-ú Á-Ú 0-9]')),
                  ],
                  inputType: TextInputType.name,
                  hintText: localizedString('nome_cartao'),
                  onChanged: (text) => _mControladorVendasDePlano.mParcelasPagamento.nomeCartao = text,
                  onEditingComplete: () {
                    _fieldFocusChange(context, _nomeFocus, _cpfFocus);
                  },
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DStextfield(
                      hintText: 'CPF',
                      maxLength: 14,
                      focusNode: _cpfFocus,
                      textInputAction: TextInputAction.next,
                      controlller: _controladorCartaoCPF,
                      onChanged: (text) {
                        setState(() {
                          _mControladorVendasDePlano.mParcelasPagamento.cpftitularcard = _controladorCartaoCPF.text;
                        });
                      },
                    ),
                    Observer(
                      builder: (_) {
                        return Visibility(
                          visible: (_mControladorVendasDePlano.mParcelasPagamento.cpftitularcard ?? '').isNotEmpty ? (!_mControladorVendasDePlano.isCPFCartaoValidoPagamentoParcelaFuturas) : false,
                          child: TextOverLine1(
                            'pasta_contrato.cpf_invalido',
                            customColor: Colors.red,
                          )
                        );
                      }
                    )
                  ],
                ),
              ],
            ),
          ],
        ));
  }

  Widget cardPix() {
    return Observer(
      builder: (_) {
        switch (_mControladorVendasDePlano.statusConsultarParcelasAtrasadasPix) {
          case ServiceStatus.Done:
            return (_mControladorVendasDePlano.dadosPixPagamento.urlQRcode?.isNotEmpty ?? false)
                ? DScard(
                    paddingInterno: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            const DSbotaoCircular(icone: TreinoIcon.qrcode_scan, categoria: Categoria.secundario),
                            const SizedBox(width: 10),
                            DStextSubheadline(
                              'escanei_este_codigo_para_pagar',
                              eHeavy: true,
                            )
                          ],
                        ),
                        ImageWidget(imageUrl: _mControladorVendasDePlano.dadosPixPagamento.urlQRcode ?? ''),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(0, 16, 0, 16),
                          child: DStextBody(
                            'pague_e_sera_creditado_na_hora',
                            eHeavy: false,
                          ),
                        ),
                        DScard(
                            categoria: CategoriaCard.secundario,
                            paddingInterno: const EdgeInsets.all(16.0),
                            child: Wrap(
                              runSpacing: 12,
                              children: [
                                dotsText('Acesse seu internet banking'),
                                dotsText('Escolha pagar via pix'),
                                dotsText('Escolha a opção QR code'),
                                dotsText('Escaneie o código acima')
                              ],
                            )),
                        const Padding(
                          padding: EdgeInsets.fromLTRB(0, 16, 0, 16),
                          child: Divider(),
                        ),
                        DStextSubheadline(
                          'ou_copie_este_codigo',
                          eHeavy: true,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        DStextBody('escolha_pagar_via_pix', eHeavy: false, ePrimario: false, textAlign: TextAlign.center),
                        const SizedBox(height: 16),
                        Container(
                          margin: EdgeInsets.zero,
                          width: MediaQuery.of(context).size.width,
                          height: 48,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadiusDirectional.circular(10),
                              border: Border.all(width: 1, color: DSLib.theme == ThemeMode.dark ? const Color(0xff424242) : const Color(0xffDCDDDF))),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: DStextBody(_mControladorVendasDePlano.dadosPixPagamento.qrtext ?? '', eHeavy: false, overflow: TextOverflow.fade, maximoLinhas: 1),
                                ),
                              ),
                              InkWell(
                                  onTap: () {
                                    Clipboard.setData(ClipboardData(text: _mControladorVendasDePlano.dadosPixPagamento.qrtext ?? ''));
                                    DSalerta().exibirToast(context, 'codigo_copiado_sucesso');
                                    // Fluttertoast.showToast(
                                    //   msg: localizedString('codigo_copiado_sucesso'),
                                    //   toastLength: Toast.LENGTH_SHORT,
                                    //   gravity: ToastGravity.TOP,
                                    //   timeInSecForIosWeb: 3,
                                    //   backgroundColor: HexColor.fromHex('#51555A')?.withValues(alpha: 0.8),
                                    //   textColor: Colors.white,
                                    //   fontSize: 16.0
                                    // );
                                  },
                                  child: Container(
                                    height: 48,
                                    margin: EdgeInsets.zero,
                                    decoration: BoxDecoration(
                                        color: Theme.of(context).primaryColor, borderRadius: const BorderRadius.only(topRight: Radius.circular(8), bottomRight: Radius.circular(8))),
                                    child: Padding(
                                      padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
                                      child: Row(
                                        children: [
                                          Icon(TreinoIcon.copy, size: 14, color: corQuandoFundoForGradiente(context)),
                                          const SizedBox(
                                            width: 4,
                                          ),
                                          DStextBody('Copiar', textoSobFundoGradiente: true),
                                        ],
                                      ),
                                    ),
                                  ))
                            ],
                          ),
                        ),
                      ],
                    ))
                : DScard(
                    paddingInterno: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            const DSbotaoCircular(icone: TreinoIcon.qrcode_scan, categoria: Categoria.secundario),
                            const SizedBox(width: 10),
                            DStextSubheadline(
                              'Escaneie este código para pagar',
                              eHeavy: true,
                            )
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(0, 16, 0, 0),
                          child: Container(
                            width: MediaQuery.of(context).size.width - 32,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              color: DSLib.theme == ThemeMode.dark ? const Color(0xff000000) : const Color(0xffF0F0F0),
                            ),
                            child: Center(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      TreinoIcon.exclamation_triangle,
                                      color: Color(0xffAF0404),
                                      size: 14,
                                    ),
                                    const SizedBox(
                                      width: 4,
                                    ),
                                    DStextCaption1(
                                      'Erro ao carregar QR Code',
                                      eHeavy: false,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
          case ServiceStatus.Waiting:
            return DScard(
                paddingInterno: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      children: [
                        SkeletonAnimation(
                          shimmerColor: Theme.of(context).dividerColor,
                          borderRadius: BorderRadius.circular(50),
                          shimmerDuration: 1000,
                          child: Container(
                            width: 36,
                            height: 36,
                            decoration: BoxDecoration(
                              color: Theme.of(context).canvasColor,
                              borderRadius: BorderRadius.circular(50),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        SkeletonAnimation(
                          shimmerColor: Theme.of(context).dividerColor,
                          borderRadius: BorderRadius.circular(50),
                          shimmerDuration: 1000,
                          child: Container(
                            width: 140,
                            height: 14,
                            decoration: BoxDecoration(
                              color: Theme.of(context).canvasColor,
                              borderRadius: BorderRadius.circular(50),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(0, 16, 0, 16),
                      child: SkeletonAnimation(
                        shimmerColor: Theme.of(context).dividerColor,
                        borderRadius: BorderRadius.circular(50),
                        shimmerDuration: 1000,
                        child: Container(
                          width: 180,
                          height: 180,
                          decoration: BoxDecoration(
                            color: Theme.of(context).canvasColor,
                            borderRadius: BorderRadius.circular(50),
                          ),
                        ),
                      ),
                    ),
                    SkeletonAnimation(
                      shimmerColor: Theme.of(context).dividerColor,
                      borderRadius: BorderRadius.circular(50),
                      shimmerDuration: 1000,
                      child: Container(
                        width: 180,
                        height: 14,
                        decoration: BoxDecoration(
                          color: Theme.of(context).canvasColor,
                          borderRadius: BorderRadius.circular(50),
                        ),
                      ),
                    ),
                  ],
                ));
          case ServiceStatus.Error:
            return Container();
          default:
            return Container();
        }
      },
    );
  }

  Widget cardBoleto() {
    return Observer(
      builder: (_) {
        if (existeBoletoGerado.isNotEmpty) {
          return DScard(
            paddingInterno: const EdgeInsets.all(16),
            child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              DStextSubheadline('boleto_existente_titulo', textAlign: TextAlign.center,),
              Padding(
                padding: const EdgeInsets.only(top: 4, bottom: 16),
                child: DStextCaption1('boleto_existente_descricao', eHeavy: false, ePrimario: false, textAlign: TextAlign.center,),
              ),
              Column(
                    children: existeBoletoGerado
                        .map(
                          (parcela) => DScard(
                              paddingInterno: const EdgeInsets.all(16),
                              categoria: CategoriaCard.secundario,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  DStextSubheadline('dados_do_boleto'),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 16, bottom: 16),
                                    child: DScard(
                                      paddingInterno: const EdgeInsets.all(16),
                                      child: Column(
                                        children: [
                                          DStextBody(
                                            parcela['linhaDigitavel'],
                                            textAlign: TextAlign.center,
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.only(top: 8),
                                            child: DSbotaoPadrao(
                                              tamanhoReduzido: true,
                                              tipoBotao: TipoBotao.secundario,
                                              icone: TreinoIcon.copy,
                                              posicaoIcone: PosicaoIcone.esquerda,
                                              titulo: 'copiar_codigo', onTap: () {
                                                Clipboard.setData(ClipboardData(text: parcela['linhaDigitavel']));
                                                DSalerta().exibirToast(context, 'codigo_copiado_sucesso');
                                            },),
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                  DStextBody(
                                    'Você tem até ${UtilDataHora.getDiaDaSemanaEmesPorExtenso(dateTime: UtilDataHora.parseStringToDate(parcela['dataVencimento'].toString()))} de ${UtilDataHora.getMesAno(dateString: parcela['dataVencimento'].toString()).split('/').last} para pagar.',
                                    textAlign: TextAlign.center,
                                  )
                                ],
                              )),
                        )
                        .toList()),
              ]));
        } else {
           if (_mControladorVendasDePlano.boletoResponse?.sucesso ?? false) {
          return DScard(
            paddingInterno: const EdgeInsets.all(16),
            child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              DScard(
                  paddingInterno: const EdgeInsets.all(16),
                  categoria: CategoriaCard.secundario,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [DStextSubheadline('boleto_gerado_sucesso'), const SizedBox(height: 24), DStextBody(_mControladorVendasDePlano.boletoResponse?.boleto_linha_digitavel ?? '', textAlign: TextAlign.center,),
                    DSbotaoPadrao(titulo: 'copiar_codigo', icone: TreinoIcon.copy, posicaoIcone: PosicaoIcone.esquerda, tamanhoReduzido: true, tipoBotao: TipoBotao.secundario, onTap: () {
                      Clipboard.setData(ClipboardData(text: _mControladorVendasDePlano.boletoResponse?.boleto_linha_digitavel ?? ''));
                      DSalerta().exibirToast(context, 'codigo_copiado_sucesso');
                    },),
                    ],
                  )),
              Padding(
                padding: const EdgeInsets.fromLTRB(0, 24, 0, 24),
                child: DStextBody(
                  'Você tem até ${UtilDataHora.getDiaDaSemanaEmesPorExtenso(dateTime: UtilDataHora.parseStringToDate(_mControladorVendasDePlano.boletoResponse?.boleto_vencimento ?? ''))} de ${UtilDataHora.getMesAno(dateTime: UtilDataHora.parseStringToDate(_mControladorVendasDePlano.boletoResponse?.boleto_vencimento ?? '')).split('/').last} para pagar.',
                  textAlign: TextAlign.center,
                ),
              ),
              Semantics(
                  identifier: 'bnt_visualizar_boleto',
                child: DSbotaoPadrao(
                  tipoBotao: TipoBotao.secundario,
                  titulo: 'visualizar_boleto',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => BoletoWebView(
                          urlBoleto: _mControladorVendasDePlano.boletoResponse?.boleto_url ?? '',
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    TreinoIcon.info_circle,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Flexible(
                      child: DStextCaption1(
                    'boleto_compensacao_aviso',
                    ePrimario: false,
                    eHeavy: false,
                  ))
                ],
              )
            ],
          ));
        } else {
          return DScard(
            paddingInterno: const EdgeInsets.all(16),
            child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: !(GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.HABILITAR_GERACAO_BOLETO_PELO_ALUNO).habilitado ?? true) ? [
              DStextSubheadline('titulo_boleto_indisponivel'),
              const SizedBox(height: 16),
              DStextBody('mensagem_boleto_indisponivel', eHeavy: false, ePrimario: false, textAlign: TextAlign.center,),
            ] :   [
              const SizedBox(height: 16),
              DStextSubheadline('clique_gerar_boleto', textAlign: TextAlign.center,),
              Padding(
                padding: const EdgeInsets.fromLTRB(0, 24, 0, 24),
                child: Semantics(
                    identifier: 'bnt_gerar_boleto',
                  child: DSbotaoPadrao(
                    titulo: 'gerar_boleto',
                    icone: TreinoIcon.bill,
                    posicaoIcone: PosicaoIcone.esquerda,
                    tipoBotao: TipoBotao.secundario,
                    onTap: () async {
                      _mControladorVendasDePlano.cobrarParcelasFuturasBoleto(
                        carregando: () {
                          UtilitarioApp().showDialogCarregando(context);
                        },
                        sucesso: () {
                          Navigator.of(context).pop();
                          setState(() {});
                        },
                        falha: (erro) {
                          Navigator.of(context).pop();
                          DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Ops!', subtitulo: erro, tituloBotao: 'Confirmar');
                        },
                      );
                    },
                  ),
                ),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    TreinoIcon.info_circle,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Flexible(
                      child: DStextCaption1(
                    'boleto_compensacao_aviso',
                    ePrimario: false,
                    eHeavy: false,
                  ))
                ],
              )
            ],
          ));
        }
        }
       
      },
    );
  }

  Widget mapBuild(String nome, String? value) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 3, 0, 3),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: DStextBody(camelCase('$nome'), eHeavy: true),
          ),
          DStextBody(
            '$value',
            eHeavy: false,
          )
        ],
      ),
    );
  }

  Widget dotsText(String nome) {
    return Row(
      children: [
        Container(
          height: 8,
          width: 8,
          decoration: BoxDecoration(borderRadius: BorderRadiusDirectional.circular(30), color: Theme.of(context).primaryColor),
        ),
        const SizedBox(width: 12),
        DStextBody(
          nome,
          eHeavy: false,
        )
      ],
    );
  }

  skeletonPagaMentoParcelas() {
    var widgetLinha = SizedBox(
      height: 50,
      child: SkeletonAnimation(
        shimmerColor: Theme.of(context).dividerColor,
        borderRadius: BorderRadius.circular(0),
        shimmerDuration: 1000,
        child: Container(
          width: double.maxFinite,
          decoration: BoxDecoration(
            color: Theme.of(context).canvasColor,
            borderRadius: BorderRadius.circular(0),
          ),
        ),
      ),
    );
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          DScard(
            child: Container(
              padding: const EdgeInsets.all(16),
              height: 150,
              child: Wrap(
                runSpacing: 16,
                children: [widgetLinha, widgetLinha],
              ),
            ),
          ),
          const SizedBox(height: 16),
          DScard(
            child: Container(
              height: 300,
              padding: const EdgeInsets.all(16),
              child: Wrap(
                runSpacing: 16,
                children: [
                  widgetLinha,
                  widgetLinha,
                  widgetLinha,
                  widgetLinha,
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}