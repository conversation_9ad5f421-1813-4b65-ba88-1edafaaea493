import 'dart:convert';
import 'package:after_layout/after_layout.dart';
import 'package:app_treino/appWidgets/componentWidgets/ValidaCondicoes.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorAgendamento.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorAulaTurma.dart';
import 'package:app_treino/controlladores/ControladorAvaliarProfessor.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorContratoUsuario.dart';
import 'package:app_treino/controlladores/ControladorExecucaoTreino.dart';
import 'package:app_treino/controlladores/ControladorFeed.dart';
import 'package:app_treino/controlladores/ControladorNotificacoesCrm.dart';
import 'package:app_treino/controlladores/ControladorNutricao.dart';
import 'package:app_treino/controlladores/ControladorTreinoAluno.dart';
import 'package:app_treino/controlladores/ControladorUsuarioApp.dart';
import 'package:app_treino/controlladores/ControladorWod.dart';
import 'package:app_treino/controlladores/controladorPlanner.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/screens/_treino6/novo_chat/botao_chat_com_badge.dart';
import 'package:app_treino/screens/avaliacao_professor/widget_avaliacao_professor.dart';
import 'package:app_treino/screens/meuDia/ItemAcessoRapido.dart';
import 'package:app_treino/screens/meuDia/card_resumo_aulas_realizadas.dart';
import 'package:app_treino/screens/novologin/widgets/UltimosUsuariosWidget.dart';
import 'package:app_treino/screens/transparency/TelaPermissaoNotificacoes.dart';
import 'package:app_treino/model/contrato/ContratoUsuario.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/util/UtilColor.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoItemFicha.dart';
import 'package:app_treino/screens/agenda_treino6/NovaTelaAgendaAluno.dart';
import 'package:app_treino/screens/meuDia/ItemConvidarAmigo.dart';
import 'package:app_treino/screens/novoCross/NovoSwiperCross.dart';
import 'package:app_treino/screens/planner/widgets/ItemAulaHoje.dart';
import 'package:app_treino/util/debug_utils.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:get_it/get_it.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:badges/badges.dart' as badges;
import 'package:app_treino/Utilitario.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TelaInicioAluno extends StatefulWidget {
  TelaInicioAluno({Key? key}) : super(key: key);

  @override
  _TelaInicioAlunoState createState() => _TelaInicioAlunoState();
}

class _TelaInicioAlunoState extends State<TelaInicioAluno> with AutomaticKeepAliveClientMixin, TickerProviderStateMixin, AfterLayoutMixin<TelaInicioAluno> {
  ControladorCliente mControladorCliente = GetIt.I.get();
  ControladorNutricao controladorNutricao = GetIt.I.get();
  ControladorFeed controladorFeed = GetIt.I.get();
  bool get wantKeepAlive => true;
  var diaSelecionadoCalendario = DateTime.now();
  final _refreshController = RefreshController();
  ControladorUsuarioApp controladorUsuarioApp = GetIt.I.get();
  final ControladorAgendamento mControladorAgendamento = GetIt.I.get<ControladorAgendamento>();
  final _controladorWod = GetIt.I.get<ControladorWod>();
  final _controladorApp = GetIt.I.get<ControladorApp>();
  bool cardExpandedRecDep = false;
  String get _keyTreinoDia => 'treino_${GetIt.I.get<ControladorTreinoAluno>().statusTreinoIASituacao}_${DateTime.now().millisecondsSinceEpoch}';

  void refresh() async {
    // await GetIt.I.get<ControladorApp>().carregarDadosClienteApp();
    GetIt.I.get<ControladorAvaliarProfessor>().consultarAvaliacoesPorCliente();
    mControladorCliente.consultarHistoricoPresenca();
    GetIt.I.get<ControladorCliente>().consultarSeTemUsuariosQueJaLogaram((success) {});
    //GetIt.I.get<ControladorExecucaoTreino>().consultarTreinosConsecutivos();
    controladorUsuarioApp.consultarAulasFavoritas();
    GetIt.I.get<ControladorAulaTurma>().mAulasETurmasAgendadasDoDia.clear();
    GetIt.I.get<ControladorAulaTurma>().consultarAulasTurmas();
    GetIt.I.get<ControladorPlanner>().obterFichaDoDia(
      sucesso: () {
        GetIt.I.get<ControladorPlanner>().tituloErro = '';
        GetIt.I.get<ControladorPlanner>().subtituloErro = '';
        GetIt.I.get<ControladorExecucaoTreino>().getTreinoEmExecucao();
        setState(() {});
      },
      falha: (falha) {
        final nome = UtilitarioApp.sentenseCaseFirst(GetIt.I.get<ControladorCliente>().mUsuarioLogado?.nome?.split(' ')[0]);
        if (falha.toLowerCase().contains('aluno não possui autorização de acesso.')) {
          GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('pendencias_encontradas');
          GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('existem_parcelas_vencidas_no_seu_contrato');
          GetIt.I.get<ControladorExecucaoTreino>().finalizarTreinoEmExecucao();
        } else if (falha.toLowerCase().contains('Treino vencido. Contate seu Professor!'.toLowerCase())) {
          GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('treino_vencido');
          GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('seu_programa_de_treino_chegou_ao_fim');
        } else if (falha.toLowerCase().contains('você já executou')) {
          GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('seu_treino_chegou_ao_fim');
          GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('todos_os_treinos_ja_foram_realizados');
        } else if (falha.toLowerCase().contains('nenhum treino. contate seu professor!')) {
          GetIt.I.get<ControladorPlanner>().tituloErro = 'sem_programa_treino';
          GetIt.I.get<ControladorPlanner>().subtituloErro = '$nome, ' + localizedString('o_seu_professor_ainda_nao_gerou_seu_programa');
          GetIt.I.get<ControladorExecucaoTreino>().finalizarTreinoEmExecucao();
        } else if (falha.toString().contains('Sem conexão com a internet')) {
          GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('internet_off');
          GetIt.I.get<ControladorPlanner>().subtituloErro = localizedString('nao_foi_possivel_validar_programa_de_treino');
        } else if (falha.toString().contains('Professor está realizando a validação do seu treino')) {
          GetIt.I.get<ControladorPlanner>().tituloErro = 'Seu treino está em revisão';
          GetIt.I.get<ControladorPlanner>().subtituloErro = 'Aguarde alguns instantes e tente novamente';
        } else if (falha.toString().toLowerCase().contains('o treino está aguardando aprovação do professor.'.toLowerCase())) {
          GetIt.I.get<ControladorPlanner>().tituloErro = 'seu_treino_esta_em_revisao';
          GetIt.I.get<ControladorPlanner>().subtituloErro = 'seu_treino_estara_disponivel_quando_aprovado';
        } else {
          GetIt.I.get<ControladorPlanner>().tituloErro = localizedString('ops_nao_foi_possivel_consultar');
          GetIt.I.get<ControladorPlanner>().subtituloErro = localizedString('nao_foi_possivel_validar_programa_de_treino');
        }
        setState(() {});
      },
    );
    GetIt.I.get<ControladorNotificacoesCrm>().consultarNotificacoesCrm(
          sucesso: () {},
          carregando: () {},
          falha: (mensagem) {},
        );
    _controladorWod.consultarWods(
      sucesso: () {
        setState(() {
          _refreshController.refreshCompleted();
        });
      },
      falha: (mensagem) {
        setState(() {
          _refreshController.refreshFailed();
        });
      },
    );
  }

  bool existeVersaoDisponivel = false;

  bool shakeEnabled = true;

  @override
  void initState() {
    refresh();

    super.initState();
  }

  void passouDataConsultaContrato(int data) async {
    SharedPreferences dataConsultarContrato = await SharedPreferences.getInstance();
    await dataConsultarContrato.setInt('data_consulta_contrato', data);
  }

  @override
  void afterFirstLayout(BuildContext context) async {
    verificarSeExistemDadosDoUsuario();
    //GetIt.I.get<ControladorExecucaoTreino>().consultarTreinosConsecutivos();
  }

  /// Verifica se existem dados do usuário.
  ///
  /// Este método verifica se há dados do usuário disponíveis já preenchidos.
  /// Tenta a cada 2 segundos por 5 vezes verificar se os dados do usuário já foram carregados.
  /// Se foi carregado, verifica se o contrato foi assinado, se não foi, abre a tela de contrato.
  ///
  void verificarSeExistemDadosDoUsuario() async {
    int tentativas = 0;
    const int maxTentativas = 5;
    const Duration intervalo = Duration(seconds: 2);
    while (GetIt.I.get<ControladorCliente>().mDadosDoUsuario == null && tentativas < maxTentativas) {
      await Future.delayed(intervalo);
      tentativas++;
    }
    if (GetIt.I.get<ControladorCliente>().mDadosDoUsuario == null) {
      validarContratoParaAssinatura();
    } else {
      validarContratoParaAssinatura();
    }
  }

  Future<void> validarContratoParaAssinatura() async {
//   if (!GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.HABILITAR_ASSINATURA_CONTRATO_APP).habilitado!) {
//     return;
//   }
    await Future.delayed(const Duration(seconds: 1));
    GetIt.I.get<ControladorContratoUsuario>().consultarContratosAluno(
    somenteNaoAssinados: true, 
        retornarTodos: false,
    carregando: () {},
    sucesso: (contrato) {
          if (contrato.assinado == true || !contrato.moduloAssinaturaDeContratoHabilitado) {
        return;
          }
          SharedPreferences.getInstance().then((prefs) {
            prefs.setString('contrato_assinado', jsonEncode(contrato));
            final data = DateTime.now().add(const Duration(days: 3)).millisecondsSinceEpoch;
            prefs.setInt('data_consulta_contrato', data);
            if (!mounted) return;
            Navigator.of(context).pushNamed(
              '/telaContratoBloqueioAssinatura',
              arguments: {'dadosContrato': contrato, 'foto': false},
            );
          });

    },
    falha: (erro) {
          DebugUtils.debugLog('Erro ao consultar contrato: $erro');
    });
  }

  void abrirTelaContrato(ContratoAssinatura contrato) {
    if (contrato.assinado == true || !contrato.moduloAssinaturaDeContratoHabilitado) {
      return;
    }
    Navigator.of(context).pushNamed('/telaContratoBloqueioAssinatura', arguments: {'dadosContrato': contrato, 'foto': false});
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var corPrimaria = Theme.of(context).primaryColor.toHex(leadingHashSign: true).replaceRange(0, 3, '#').toUpperCase();
    var corPrimariaDark = escurecerCor(Theme.of(context).primaryColor, DSLib.theme == ThemeMode.dark ? 0.25 : 0.35).toHex(leadingHashSign: true).replaceAll('ff', '').toUpperCase();
    super.build(context);
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: DSappBar(
        mostrarSetaTitulo: true,
        iconeSetaCustom: cardExpandedRecDep ? TreinoIcon.angle_up : TreinoIcon.angle_down,
        onArrowTap: () {
          setState(() {
            cardExpandedRecDep = !cardExpandedRecDep;
          });
        },
        onTap: GetIt.I.get<ControladorApp>().itensBottomBar.length >= 3
            ? () {
                Navigator.of(context).pushNamed('/telaPerfilAluno').then((value) {
                  setState(() {});
                });
              }
            : null,
        backgroundColor: Theme.of(context).colorScheme.surface,
        tipoAppbar: TipoAppBar.comFoto,
        urlFoto: GetIt.I.get<ControladorCliente>().mUsuarioLogado?.srcImg ?? '',
        titulo: UtilitarioApp().retornarSomenteOPrimeiroNome(
          GetIt.I.get<ControladorCliente>().mUsuarioLogado?.nome ?? ''
          ),
        actions: [
          ValidaCondicoes(
              moduloApp: ModuloApp.MODULO_NOTIFICACOES,
              apenasAluno: true,
              child: StatefulBuilder(builder: (BuildContext context, setState) {
                GetIt.I.get<ControladorNotificacoesCrm>().setStateBell = setState;
                return Observer(
                  builder: (_) {
                    return Center(
                      child: Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: badges.Badge(
                            badgeContent: DStextCaption1('${GetIt.I.get<ControladorNotificacoesCrm>().quantidadeRecebidasSessao.toString()}', eHeavy: true, textoSobFundoGradiente: true),
                            position: badges.BadgePosition.topEnd(top: -10, end: -5),
                            showBadge: GetIt.I.get<ControladorNotificacoesCrm>().quantidadeRecebidasSessao >= 1,
                            ignorePointer: false,
                            badgeAnimation: const badges.BadgeAnimation.slide(toAnimate: true, animationDuration: Duration(seconds: 1)),
                            badgeStyle: badges.BadgeStyle(
                                badgeColor: Theme.of(context).primaryColor,
                                padding: GetIt.I.get<ControladorNotificacoesCrm>().quantidadeRecebidasSessao >= 10 ? const EdgeInsets.all(4) : const EdgeInsets.all(6),
                                shape: badges.BadgeShape.circle),
                            child: DSbotaoCircular(
                              onTap: () async {
                                analytic(EventosKey.inicio_pressionou_notificacoes);
                                TelaPermissaoNotificacoes().abrirSolicitacaoPermissao(context, fluxo: FluxoPermissaoNotificacoes.MARCOU_AULA_COLETIVA_ALUNO, fechouModal: () async {
                                  Navigator.pushNamed(context, '/telaNotificacoes');
                                });
                              },
                              icone: TreinoIcon.bell,
                              alturaIcone: 25,
                            ),
                          )),
                    );
                  },
                );
              })),
          Semantics(identifier: 'bnt_chat', child: const BotaoChatComBadge()),
        ],
      ),
      body: SmartRefresher(
        controller: _refreshController,
        onRefresh: refresh,
        physics: const BouncingScrollPhysics(),
        header: const WaterDropHeader(),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 8),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  height: cardExpandedRecDep ? null : 0,
                  child: cardExpandedRecDep
                      ? const UltimosUsuariosWidget(
                          type: LayoutType.secundario,
                          removerUsuarioAtual: true,
                          ativarDschipLogin: true,
                        )
                      : const SizedBox.shrink(),
                ),
              ),
              existeVersaoDisponivel ? atualizacaoDisponivel(context) : Container(),
              GetIt.I.get<ControladorApp>().chave == '2baf2084c71d676019c4440a0e52a989' ? itemAulasFitStream(context) : Container(),
              GetIt.I.get<ControladorApp>().chave == '2baf2084c71d676019c4440a0e52a989'
                  ? Container()
                  : F.appFlavor == Flavor.BOX
                      ? itemTreinoECrossfitSwiper(context)
                      : Observer(builder: (context) {
                          GetIt.I.get<ControladorTreinoAluno>().statusTreinoIASituacao;
                          GetIt.I.get<ControladorPlanner>().statusCarregandoFichaDoDia;
                          return ItemTreinoDoDiaOuExecutando(
                              key: ValueKey(_keyTreinoDia), titulo: GetIt.I.get<ControladorPlanner>().tituloErro, subtitulo: GetIt.I.get<ControladorPlanner>().subtituloErro);
                        }),
              GetIt.I.get<ControladorApp>().chave == '2baf2084c71d676019c4440a0e52a989'
                  ? Container()
                  : F.appFlavor == Flavor.BOX
                      ? ItemTreinoDoDiaOuExecutando(titulo: GetIt.I.get<ControladorPlanner>().tituloErro, subtitulo: GetIt.I.get<ControladorPlanner>().subtituloErro)
                      : itemTreinoECrossfitSwiper(context),
              ItemAcessoRapido(key: ValueKey('acesso_rapido_${DateTime.now().millisecondsSinceEpoch}'), corPrimaria: corPrimaria, corPrimariaDark: corPrimariaDark),
              Observer(builder: (context) {
                switch (mControladorCliente.mStatusHisoricoPresenca) {
                  case ServiceStatus.Waiting:
                    return const CardResumoAulasRealizadasSkeleton();
                  case ServiceStatus.Done:
                    return CardResumoAulasRealizadas(
                      aulasRealizadas: mControladorCliente.mHistoricoPresenca!.totalAulasRealizadas!,
                      aulasMes: mControladorCliente.mHistoricoPresenca!.aulasMesAtual!,
                      semanaConsecutivasPresenca: mControladorCliente.mHistoricoPresenca!.semanasConsecutivas!,
                    );
                  case ServiceStatus.Error:
                    return Container();
                  case ServiceStatus.Empty:
                    return const CardResumoAulasRealizadas(
                      aulasRealizadas: 0,
                      aulasMes: 0,
                      semanaConsecutivasPresenca: 0,
                    );
                }
              }),
              Observer(builder: (_) {
                switch (mControladorAgendamento.statusConsultaAgendamentos) {
                  case ServiceStatus.Done:
                    return Column(
                      children: [
                        Observer(
                          builder: (_) {
                            if (mControladorAgendamento.temAgendamentoHoje) {
                              return SizedBox(
                                height: 174,
                                width: MediaQuery.of(context).size.width,
                                child: Swiper(
                                  loop: false,
                                  itemBuilder: (BuildContext context, int index) {
                                    return CardAgendamentosHoje(agendamento: mControladorAgendamento.agendamentosDeHoje[index]);
                                  },
                                  itemCount: mControladorAgendamento.agendamentosDeHoje.length,
                                  pagination: new SwiperPagination(
                                      builder: new DotSwiperPaginationBuilder(activeColor: Theme.of(context).primaryColor, size: 6, activeSize: 6, color: Theme.of(context).dividerColor),
                                      alignment: Alignment.bottomCenter,
                                      margin: const EdgeInsets.only(top: 0)),
                                ),
                              );
                            } else {
                              return Container();
                            }
                          },
                        ),
                        ValueListenableBuilder(
                            valueListenable: mControladorAgendamento.listeneragendamentosFuturos,
                            builder: (_, value, __) {
                              return ValidaCondicoes(
                                validacaoExtra: value.isNotEmpty,
                                child: SizedBox(
                                  height: 230,
                                  width: MediaQuery.of(context).size.width,
                                  child: Swiper(
                                    loop: false,
                                    itemBuilder: (BuildContext context, int index) {
                                      return CardAgendamento(
                                        agendamento: value[index],
                                        telaInicial: true,
                                      );
                                    },
                                    itemCount: value.length,
                                    pagination: new SwiperPagination(
                                        builder: new DotSwiperPaginationBuilder(activeColor: Theme.of(context).primaryColor, size: 6, activeSize: 6, color: Theme.of(context).dividerColor),
                                        alignment: Alignment.bottomCenter,
                                        margin: const EdgeInsets.only(top: 0)),
                                  ),
                                ),
                              );
                            }),
                      ],
                    );
                  default:
                    return Container();
                }
              }),
              GetIt.I.get<ControladorApp>().chave == '2baf2084c71d676019c4440a0e52a989' ? Container() : const ItemAulaHoje(),
              Visibility(visible: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.PERMITIR_AVALIAR_PROFESSOR).habilitado ?? false, child: const WidgetAvaliacaoProfessor()),
              Visibility(visible: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_CONVIDAR_AMIGO).habilitado!, child: const ItemConvidarAmigo()),
              Observer(builder: (context) {
                return GetIt.I.get<ControladorExecucaoTreino>().treinoEmExecucao?.atividades != null ? const SizedBox(height: 200) : const SizedBox(height: 100);
              }),
            ],
          ),
        ),
      ),
    );
  }

  DScard itemAulasFitStream(BuildContext context) {
    return DScard(
        onTap: () {
          GetIt.I.get<ControladorApp>().controller.animateToPage(1, duration: const Duration(milliseconds: 1), curve: Curves.easeIn);
          setState(() {
            GetIt.I.get<ControladorApp>().indexTabbar = 1;
          });
        },
        paddingExterno: const EdgeInsets.fromLTRB(16, 16, 16, 0),
        child: SizedBox(
          width: MediaQuery.of(context).size.width - 32,
          height: 150,
          child: Stack(
            children: [
              Positioned(
                left: 16,
                top: 16,
                bottom: 8,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        DStextSubheadline('Aulas ao vivo'),
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: DStextCaption1(
                            'Acesse as aulas disponíveis que \nestão acontecendo agora!',
                            eHeavy: false,
                          ),
                        ),
                      ],
                    ),
                    const DSbotaoPadrao(titulo: 'Ver todas as aulas', tamanhoReduzido: true, icone: TreinoIcon.angle_right, posicaoIcone: PosicaoIcone.direita),
                  ],
                ),
              ),
              Positioned(
                  right: 0,
                  top: 0,
                  child: Image.asset(
                    'assets/images/aulas_coletivas_fitstream.png',
                    height: 150,
                    width: 197,
                  )),
            ],
          ),
        ));
  }

  Widget itemTreinoECrossfitSwiper(BuildContext context) {
    return ValidaCondicoes(
      validacaoExtra: GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_CROSS).habilitado!,
      child: _controladorWod.wodsDeHoje.isEmpty
          ? Container()
          : SizedBox(
              height: 280,
              width: MediaQuery.of(context).size.width,
              child: Swiper(
                loop: false,
                pagination: SwiperPagination(
                    builder: DotSwiperPaginationBuilder(activeColor: Theme.of(context).primaryColor, size: 6, activeSize: 6, color: Theme.of(context).dividerColor),
                    alignment: Alignment.bottomCenter,
                    margin: const EdgeInsets.only(top: 0)),
                viewportFraction: 1,
                itemCount: _controladorWod.wodsDeHoje.length == 0 ? 1 : _controladorWod.wodsDeHoje.length,
                itemBuilder: (_, i) {
                  if (_controladorWod.wodsDeHoje.isEmpty) {
                    return ItemSemWod(context);
                  } else {
                    return celulaCrossfit(_controladorWod.wodsDeHoje[i], context, i, _controladorWod, _controladorApp);
                  }
                },
              )),
    );
  }

  int processarQuantosIndexExibir() {
    var quantidadeWods = 0;
    if (GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_CROSS).habilitado!) {
      quantidadeWods = _controladorWod.wodsDeHoje.length;
    }
    if (GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_TREINO).habilitado!) {
      quantidadeWods += 1;
    }
    return quantidadeWods;
  }

  Widget atualizacaoDisponivel(BuildContext context) {
    return DScard(
        paddingExterno: const EdgeInsets.only(left: 16, right: 16, top: 24, bottom: 16),
        paddingInterno: const EdgeInsets.all(16),
        onTap: () async {
          try {
            UtilitarioApp().showDialogCarregando(context);
            //await LaunchReview.launch(writeReview: false, androidAppId: 'com.pacto', iOSAppId: '862662527');
            Navigator.of(context).pop();
            setState(() {
              existeVersaoDisponivel = false;
            });
          } catch (e) {
            Navigator.of(context).pop();
            setState(() {
              existeVersaoDisponivel = false;
            });
          }
        },
        gradiente: true,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DStextHeadline(
              localizedString('atualizacao_necessaria'),
              textoSobFundoGradiente: true,
            ),
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: DStextCaption1(localizedString('atualize_seu_app'), eHeavy: false, textoSobFundoGradiente: true),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 12, bottom: 12),
              child: Container(height: 1, color: corQuandoFundoForGradiente(context).withAlpha(50)),
            ),
            const DSbotaoPadrao(
              titulo: 'atualizar_app',
              tamanhoReduzido: true,
              tipoBotao: TipoBotao.secundario,
              sobFundoGradiente: true,
              icone: TreinoIcon.angle_right,
            )
          ],
        ));
  }
}

class ItemRsetarAluno extends StatefulWidget {
  const ItemRsetarAluno({super.key});

  @override
  State<ItemRsetarAluno> createState() => _ItemRsetarAlunoState();
}

class _ItemRsetarAlunoState extends State<ItemRsetarAluno> {
  bool get isBancoTestes => ['aa5abb60d28e4583f5438c7d4a1cb376', 'aca438e8c9e947e64db2236bb2f1f7a9'].any((c) => GetIt.I.get<ControladorApp>().chave == c);
  ControladorTreinoAluno get controladorTreinoAluno => GetIt.I.get<ControladorTreinoAluno>();
  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: false,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: DSbotaoPadrao(
          titulo: 'resetar_aluno',
          onTap: () {
            GetIt.I.get<ControladorTreinoAluno>().resetAluno();
          },
        ),
      ),
    );
  }
}
