import 'dart:async';

import 'package:after_layout/after_layout.dart';
import 'package:app_treino/appWidgets/componentWidgets/ScanAnimation.dart';
import 'package:app_treino/controlladores/ControladorAvaliacaoFisica.dart';
import 'package:app_treino/model/DadosParaBioempedancia.dart';
import 'package:app_treino/model/util/UtilColor.dart' hide retornarCorGradientPadrao;
import 'package:app_treino/screens/splash/EspereUmPouco.dart';
import 'package:app_treino/screens/vitio/avaliacaoBioimpedancia/Tela_solucao_poblemas_bluetooth.dart';

import 'package:ds_pacto/ds_pacto.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';

class TelaEtapasBalanca extends StatefulWidget {
  const TelaEtapasBalanca({super.key});

  @override
  State<TelaEtapasBalanca> createState() => _TelaEtapasBalancaState();
}

class _TelaEtapasBalancaState extends State<TelaEtapasBalanca> with AfterLayoutMixin<TelaEtapasBalanca> {
  int indexAtual = 0;
  bool carregando = true;
  List<String> etapasTitulo = [
    'solucao_blue_08',
    'solucao_blue_09',
    'solucao_blue_10',
  ];
  List<String> etapasContent = [
    'solucao_blue_11',
    '',
    'solucao_blue_12',
  ];
  PageController controladorPagina = PageController(initialPage: 0);
  late final DadosParaBiompendancia args;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controladorPagina.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: carregando
          ? Container()
          : Stack(
              children: [
                AnimatedGradientWidget(
                  colors: retornarCorGradientPadrao(context, null),
                ),
                Column(
                  children: [
                    SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Center(
                              child: DSbotaoCircular(
                                alturaIcone: 25,
                                onTap: () {
                                  Navigator.of(context).pop(false);
                                },
                                icone: TreinoIcon.angle_left,
                              ),
                            ),
                            DStextHeadline(
                              'solucao_blue_13',
                              corCustomizada: const Color(0xffF5F5F5),
                            ),
                            const SizedBox(width: 40),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: PageView(
                        physics: const NeverScrollableScrollPhysics(),
                        controller: controladorPagina,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Center(
                                child: SizedBox(width: MediaQuery.of(context).size.width * 0.7, child: SvgPicture.asset('assets/images/balanca_etapa.svg')),
                              ),
                            ],
                          ),
                          Center(
                            child: SizedBox(
                              height: MediaQuery.of(context).size.height * 0.7,  
                              child: const ScanAnimation(),
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Center(
                                child: SizedBox(width: MediaQuery.of(context).size.width * 0.8, child: SvgPicture.asset('assets/images/balanca_etapa.svg')),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Column(
                        children: [
                          DStextHeadline(etapasTitulo[indexAtual], corCustomizada: const Color(0xffF5F5F5)),
                          const SizedBox(
                            height: 16,
                          ),
                          DStextBody(
                            etapasContent[indexAtual],
                            eHeavy: false,
                            corCustomizada: const Color(0xffF5F5F5),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 51,
                    ),
                    if (indexAtual == 2)
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: DSbotaoPadrao(
                            titulo: 'solucao_blue_14',
                            onTap: () {
                              Navigator.of(context).pushNamed('/telaResultadoBioempedancia', arguments: args);
                            }),
                      ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ...List.generate(
                            3,
                            (index) => Row(
                                  children: [
                                    Container(
                                      width: 6,
                                      height: 6,
                                      decoration: BoxDecoration(
                                        color: () {
                                          if (indexAtual == index) {
                                            return lighten(Theme.of(context).primaryColor, 0.3);
                                          } else if (indexAtual > index) {
                                            return Theme.of(context).primaryColor;
                                          } else {
                                            return const Color(0xffCFCFCF);
                                          }
                                        }.call(),
                                        shape: BoxShape.circle,
                                      ),
                                    ),
                                    if (index != 2) const SizedBox(width: 4),
                                  ],
                                )),
                      ],
                    ),
                    const SizedBox(height: 56),
                  ],
                ),
              ],
            ),
    );
  }



  @override
  FutureOr<void> afterFirstLayout(BuildContext context) {
    args = ModalRoute.of(context)!.settings.arguments as DadosParaBiompendancia;
    etapasContent[1] = args.balanca.model?.userGuide ?? '';
    setState(() {
      carregando = false;
    });
    Future.delayed(const Duration(milliseconds: 300)).then((x) {
      GetIt.I.get<ControladorAvaliacaoFisica>().setUserBalanca(
        args.balanca,
        args.aluno,
        balancaOn: () {
          setState(() {
            indexAtual = 1;
            controladorPagina.jumpToPage(1);
          });
        },
        sucesso: () {
          setState(() {
            indexAtual = 2;
            controladorPagina.jumpToPage(2);
          });
        },
        falha: (error) {
          Navigator.of(context)
              .push(
            MaterialPageRoute(
              builder: (context) => DeviceReconnectScreen(deviceBrand: args.balanca.brand!, deviceModel: args.balanca.model, errorDevice: error),
            ),
          )
              .then((x) {
            Navigator.pop(context, false);
          });
        },
      );
    });
  }
}
