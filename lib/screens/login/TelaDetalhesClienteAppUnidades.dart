import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/CelulaResumoClienteApp.dart';
import 'package:app_treino/flavors.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
// import 'package:smartlook/smartlook.dart';
import 'package:get_it/get_it.dart';



class TelaDetalhesClienteAppUnidades extends StatefulWidget {
  const TelaDetalhesClienteAppUnidades({Key? key}) : super(key: key);

  @override
  State<TelaDetalhesClienteAppUnidades> createState() => _TelaDetalhesClienteAppUnidadesState();
}

class _TelaDetalhesClienteAppUnidadesState extends State<TelaDetalhesClienteAppUnidades> {
  final _controladorClienteApp = GetIt.I.get<ControladorApp>();
  initState() {
    super.initState();
    _controladorClienteApp.statusCarregandoAcademias = ServiceStatus.Done;
  }
  @override
  Widget build(BuildContext context) {
    //String termoPesquisa = '';
    return Scaffold(
      appBar: DSappBar(
        titulo: localizedString('select_your_unit'),
        ocultarBotaoVoltar: F.isPersonalizado,
      ),
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            Column(
              children: [
                DScard(
                  paddingExterno: const EdgeInsets.all(16),
                  child: CelulaResumoClienteApp(
                    clienteApp: _controladorClienteApp.mClienteAppSelecionado,
                    controladorClienteApp: _controladorClienteApp,
                  ),
                ),
                (_controladorClienteApp.mClienteAppSelecionado?.empresaApps?.length ?? 0) > 5
                    ? Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                        child: DStextfield(
                          suffixIcon: const Icon(TreinoIcon.search, size: 18,),
                          hintText: localizedString('pesquisar_por_nome'),
                          onChanged: (v) {
                            _controladorClienteApp.textopesquisa = v;
                            _controladorClienteApp.statusCarregandoAcademias = ServiceStatus.Waiting;
                            Future.delayed(const Duration(milliseconds: 500)).then((value) {_controladorClienteApp.statusCarregandoAcademias = ServiceStatus.Done;
                            });
                          /* termoPesquisa = v;
                          _controladorClienteApp.pesquisarEmpresaPorNome(termoPesquisa); */
                          
                        }),
                      )
                    : Container(),
              ],
            ),
            const SizedBox(
              height: 16,
            ),
            Observer(key: const Key('TelaDetalhesClienteAppUnidades'), builder: (_) {
              switch (_controladorClienteApp.statusCarregandoAcademias) {
                case ServiceStatus.Empty:
                  return Padding(
                    padding: const EdgeInsets.all(32),
                    child: DStextBody('your_search_did_not_match_any_results',
                      textAlign: TextAlign.center,
                    ),
                  );
                case ServiceStatus.Done:
                  final clienteApp = _controladorClienteApp.mClienteAppSelecionado;
                  final empresaApps = clienteApp?.empresaApps;

                  if (clienteApp == null || empresaApps == null) {
                    return Padding(
                      padding: const EdgeInsets.all(32),
                      child: DStextBody('no_units_available',
                        textAlign: TextAlign.center,
                      ),
                    );
                  }

                  var lista = empresaApps.where((element) => _controladorClienteApp.filtrarUnidades(element));
                  //termoPesquisa.isEmpty ? _controladorClienteApp.mClienteAppSelecionado!.empresaApps! : _controladorClienteApp.mEmpresasAppPequisa;
                  return Column(
                    children: lista.map((e) => Column(
                      children: <Widget>[
                        ListTile(
                          onTap: () {
                              UtilitarioApp().showDialogCarregando(context);
                            _controladorClienteApp.setEmpresaSelecionada(e, () {
                              Navigator.pop(context);
                                          Navigator.of(context)
                                              .pushReplacementNamed(_controladorClienteApp.desabilitarNovoFluxoLogin ? '/buscarCadastroPorTelefone' : '/telaSelecaoMetodoLogin');
                            }, (x) {
                              Navigator.pop(context);
                              DSalerta().exibirAlertaSimplificado(context: context, titulo: 'attention', subtitulo: x ?? 'unknown_error', tituloBotao: 'got_it');
                            });
                          },
                          dense: false,
                          trailing: Icon(TreinoIcon.angle_right,
                            color: DSLib.theme ==  ThemeMode.dark
                            ? const Color(0xffFFFFFF)
                            : const Color(0xff202020),
                          ),
                          title: DStextSubheadline(UtilitarioApp.sentenseCase(e.nome ?? '')),
                        ),
                        const Padding(
                          padding: EdgeInsets.fromLTRB(16, 0, 16, 0),
                          child: Divider(),
                        )
                      ],
                    )).toList(),
                  );
                default:
                  return Container();
              }
            })
          ],
        ),
      ),
    );
  }
}