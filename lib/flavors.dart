import 'dart:io';

import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/main-Treino.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:diacritic/diacritic.dart';
import 'package:firebase_core_platform_interface/firebase_core_platform_interface.dart';

import 'package:flutter/material.dart';
// import 'package:native_admob_flutter/native_admob_flutter.dart';

enum Flavor { TREINO, BOX, REVISAO, FABRICADEMONTROS, LIVEACADEMIA, MAYBOOTCAMP, INTENSEFIT, FITSTREAM, ENGENHARIA_DO_CORPO, FLYNOW, APPPERSONALIZADO }

enum BannerType { FEED, PAUSE, PERFIL, EXECUCAO }

class F {
  static Flavor? appFlavor;

  static String get colorDefault {
    switch (appFlavor) {
      case Flavor.FABRICADEMONTROS:
        return '#FF3D00';
      case Flavor.LIVEACADEMIA:
        return '#1dc3fa';
      case Flavor.MAYBOOTCAMP:
        return '#FF3D00';
      case Flavor.INTENSEFIT:
        return '#FF3D00';
      case Flavor.FITSTREAM:
        return '#FF3D00';
      case Flavor.ENGENHARIA_DO_CORPO:
        return '#C60395';
      case Flavor.FLYNOW:
        return '#ffd800';
      default:
        return '#FF3D00';
    }
  }

  static FirebaseOptions? get firebaseInitConfig {
    switch (appFlavor) {
      case Flavor.APPPERSONALIZADO:
        return FirebaseOptions(
          apiKey: 'AIzaSyBIZfILpMHJNbD49wmX-3aZtm6q5c4d_Zw',
          appId: Platform.isIOS ? '1:458760368028:android:da01372df593944a' : '1:458760368028:android:da01372df593944a',
          messagingSenderId: '458760368028',
          projectId: 'appgestor-3f25c',
          databaseURL: 'https://appgestor-3f25c.firebaseio.com',
          storageBucket: 'appgestor-3f25c.appspot.com',
          measurementId: 'G-MEASUREMENT_ID',
          iosBundleId: Platform.isAndroid ? null : 'br.com.pactosolucoes.thesimplegym',
        );
      case Flavor.FLYNOW:
        return FirebaseOptions(
          apiKey: 'AIzaSyBlmcsWvOkQHfRExGeKKuQyEgDed_0TwIY',
          appId: Platform.isIOS ? '1:977449544580:ios:f972e7ee26d1d33419cbd1' : '1:977449544580:android:bf718655954f742a19cbd1',
          messagingSenderId: '977449544580',
          projectId: 'app-do-aluno-unificado',
          databaseURL: 'https://app-do-aluno-unificado.firebaseio.com',
          storageBucket: 'app-do-aluno-unificado.appspot.com',
          measurementId: 'G-QXKFWTTV5F',
          iosBundleId: Platform.isAndroid ? null : 'br.com.sistemapacto.flaynowacademia',
        );
      case Flavor.ENGENHARIA_DO_CORPO:
        return FirebaseOptions(
          apiKey: 'AIzaSyBlmcsWvOkQHfRExGeKKuQyEgDed_0TwIY',
          appId: Platform.isIOS ? '1:977449544580:ios:88ea22341bf9fd0e19cbd1' : '1:977449544580:android:67fb8fd588c3dd4119cbd1',
          messagingSenderId: '977449544580',
          projectId: 'app-do-aluno-unificado',
          databaseURL: 'https://app-do-aluno-unificado.firebaseio.com',
          storageBucket: 'app-do-aluno-unificado.appspot.com',
          iosBundleId: Platform.isAndroid ? null : 'br.com.pactosolucoes.engenhariadocorpo',
        );
      case Flavor.FABRICADEMONTROS:
        return FirebaseOptions(
          apiKey: 'AIzaSyBWJMTPYpioywOHK37Wx7_Fn_wMxg3dCJA',
          appId: Platform.isIOS ? '1:458760368028:ios:e612595036e71295f9a259' : '1:458760368028:web:b693c3466a5fb066f9a259',
          messagingSenderId: '458760368028',
          projectId: 'appgestor-3f25c',
          databaseURL: 'https://appgestor-3f25c.firebaseio.com',
          storageBucket: 'appgestor-3f25c.firebaseio.com',
          //   iosClientId: Platform.isAndroid ? null : '',
          iosBundleId: Platform.isAndroid ? null : 'br.com.pactosolucoes.fabricademonstrosct',
        );
      case Flavor.LIVEACADEMIA:
        return FirebaseOptions(
          apiKey: 'AIzaSyBWJMTPYpioywOHK37Wx7_Fn_wMxg3dCJA',
          appId: Platform.isIOS ? '1:458760368028:ios:24b132af7f6ea36ef9a259' : '1:458760368028:android:66e6e6bbfc34c89af9a259',
          messagingSenderId: '458760368028',
          projectId: 'appgestor-3f25c',
          databaseURL: 'https://appgestor-3f25c.firebaseio.com',
          storageBucket: 'appgestor-3f25c.firebaseio.com',
          //   iosClientId: Platform.isAndroid ? null : '',
          iosBundleId: Platform.isAndroid ? null : 'br.com.pactosolucoes.liveacademia',
        );
      case Flavor.MAYBOOTCAMP:
        return FirebaseOptions(
          apiKey: 'AIzaSyBWJMTPYpioywOHK37Wx7_Fn_wMxg3dCJA',
          appId: Platform.isIOS ? '1:458760368028:ios:021b2c9f9992d157f9a259' : '1:458760368028:android:754f52e87fa0d676f9a259',
          messagingSenderId: '458760368028',
          projectId: 'appgestor-3f25c',
          databaseURL: 'https://appgestor-3f25c.firebaseio.com',
          storageBucket: 'appgestor-3f25c.firebaseio.com',
          //   iosClientId: Platform.isAndroid ? null : '',
          iosBundleId: Platform.isAndroid ? null : 'br.com.pactosolucoes.maybootcamp',
        );
      case Flavor.INTENSEFIT:
        return FirebaseOptions(
          apiKey: 'AIzaSyBWJMTPYpioywOHK37Wx7_Fn_wMxg3dCJA',
          appId: Platform.isIOS ? '1:458760368028:ios:e21d6d64ffe42aebf9a259' : '1:458760368028:android:04d3873c3011f1c9f9a259',
          messagingSenderId: '458760368028',
          projectId: 'appgestor-3f25c',
          databaseURL: 'https://appgestor-3f25c.firebaseio.com',
          storageBucket: 'appgestor-3f25c.firebaseio.com',
          //   iosClientId: Platform.isAndroid ? null : '',
          iosBundleId: Platform.isAndroid ? null : 'br.com.sistemapacto.intensefit',
        );
      case Flavor.FITSTREAM:
        return FirebaseOptions(
          apiKey: 'AIzaSyBWJMTPYpioywOHK37Wx7_Fn_wMxg3dCJA',
          appId: Platform.isIOS ? '1:458760368028:ios:2462eb6abede2f52f9a259' : '1:458760368028:android:f369e5d863c7e63ff9a259',
          messagingSenderId: '458760368028',
          projectId: 'appgestor-3f25c',
          databaseURL: 'https://appgestor-3f25c.firebaseio.com',
          storageBucket: 'appgestor-3f25c.firebaseio.com',
          //   iosClientId: Platform.isAndroid ? null : '',
          iosBundleId: Platform.isAndroid ? null : 'br.com.pactosolucoes.fitstream',
        );
      default:
        return null;
    }
  }

  static String get revenueKey {
    switch (appFlavor ?? '') {
      case Flavor.BOX:
        return Platform.isAndroid ? 'goog_snNAgWYNbivERQVtkLQqnKRBkFk' : 'appl_AhLXWnAOBUJIDbjPjvAgLUHEvXl';
      case Flavor.TREINO:
        return Platform.isAndroid ? 'goog_ByIZhhPLqsOabpfKVxUkcaeGNMB' : 'appl_ZgGClnTfKwRsdplfOLCCWWQHZcK';
      default:
        return Platform.isAndroid ? 'goog_ByIZhhPLqsOabpfKVxUkcaeGNMB' : 'appl_ZgGClnTfKwRsdplfOLCCWWQHZcK';
    }
  }

  static String get app {
    return removeDiacritics(nomeApp.replaceAll(' ', '_').toUpperCase());
  }

  static String get logoAppPersonalizado {
    return getIt.get<ControladorApp>().mClienteAppSelecionado?.urlLogoPrincipal ?? '';
  }

  static bool get isPersonalizado {
    return firebaseClienteAppRef.isNotEmpty;
  }

  static String get firebaseClienteAppRef {
    switch (appFlavor ?? '') {
      case Flavor.REVISAO:
        return 'qvD3JGjyQfNpRsymVTVi';
      case Flavor.FABRICADEMONTROS:
        return 'w1LwO32wvhfVjLjrcQTd';
      case Flavor.LIVEACADEMIA:
        return 'IIwFpJXlG4bZV0RVCEiB';
      case Flavor.MAYBOOTCAMP:
        return 'qzouWgT8QVHiWaunG108';
      case Flavor.INTENSEFIT:
        return 'O6TTkp8jaqi8ctVZTKrR';
      case Flavor.FITSTREAM:
        return '90gtxcS7mfqkfJ5Qdl5j';
      case Flavor.ENGENHARIA_DO_CORPO:
        return 'fcnGqLt0PddDXLjojN1X';
      case Flavor.FLYNOW:
        return 'Ef4qBSTlDDYStYgf9eQd';
      case Flavor.APPPERSONALIZADO:
        return 'AueZMA6GQEXGXhRKGYZ5';
      default:
        return '';
    }
  }

  static String get nomeApp {
    switch (appFlavor ?? '') {
      case Flavor.BOX:
        return 'MEU BOX';
      case Flavor.TREINO:
        return 'TREINO';
      case Flavor.REVISAO:
        return 'REVISÃO';
      case Flavor.FABRICADEMONTROS:
        return 'FÁBRICA DE MONSTROS';
      case Flavor.LIVEACADEMIA:
        return 'LIVE ACADEMIA';
      case Flavor.MAYBOOTCAMP:
        return 'MAY BOOTCAMP';
      case Flavor.INTENSEFIT:
        return 'INTENSEFIT';
      case Flavor.FITSTREAM:
        return 'FITSTREAM';
      case Flavor.ENGENHARIA_DO_CORPO:
        return 'ENGENHARIA DO CORPO';
      case Flavor.FLYNOW:
        return 'FLY NOW';
      default:
        return 'TREINO';
    }
  }

  static List<Color> get degradeSplash {
    switch (appFlavor ?? '') {
      case Flavor.TREINO:
        return [const Color(0xfffe4530), const Color(0xfffe3c5c)];
      case Flavor.BOX:
        return [Colors.grey[900]!, Colors.grey[900]!];
      case Flavor.ENGENHARIA_DO_CORPO:
        return [Colors.grey[900]!, Colors.grey[900]!];
      default:
        return [];
    }
  }

  static String? get logoSquare {
    switch (appFlavor ?? '') {
      case Flavor.TREINO:
        return 'assets/applogo/android_logo.png';
      case Flavor.BOX:
        return 'assets/applogo/android_log_box.png';
      case Flavor.FABRICADEMONTROS:
        return 'assets/applogo/android_logo_fdm.png';
      case Flavor.LIVEACADEMIA:
        return 'assets/applogo/android_logo_liveacademia.png';
      case Flavor.MAYBOOTCAMP:
        return 'assets/applogo/android_logo_maybootcamp.png';
      case Flavor.INTENSEFIT:
        return 'assets/applogo/android_logo_intensefit.png';
      case Flavor.FITSTREAM:
        return 'assets/applogo/android_logo_fitstream.png';
      default:
        return null;
    }
  }

  static String? get logo {
    switch (appFlavor ?? '') {
      case Flavor.TREINO:
        return 'assets/applogo/treino_logo.svg';
      case Flavor.BOX:
        return 'assets/applogo/meubox_logo.svg';
      default:
        return null;
    }
  }

  static String get title {
    return camelCase(nomeApp);
  }
}
