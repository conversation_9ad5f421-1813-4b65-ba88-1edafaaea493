import 'dart:io';

import 'package:app_treino/controlladores/ControladorDBExtend.dart';
import 'package:sembast/sembast.dart';
import 'package:shared_preferences/shared_preferences.dart';

class Fix6311Bellow extends UtilDataBase {
  initfix() async {
    var dbShared = await SharedPreferences.getInstance();

    if (!(dbShared.getBool('6310') ?? false) && Platform.isAndroid) {
      dbShared.setBool('6310', true);
      StoreRef<int, Map<String, dynamic>> store = intMapStoreFactory.store('usuariosKeep');
      await store.delete(getDb);
    }
  }
}
