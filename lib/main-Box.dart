import 'dart:io';

import 'package:app_treino/fabricaGetIt.dart';
import 'package:app_treino/util/HttpOverride.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get_it/get_it.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/main.dart';
import 'package:mobx/mobx.dart';

GetIt getIt = GetIt.instance;
FlutterLocalNotificationsPlugin? flutterLocalNotificationsPlugin;
Future<void> main() async {
  F.appFlavor = Flavor.BOX;
  HttpOverrides.global = MyHttpOverrides();
  mainContext.config = ReactiveConfig.main.clone(
      writePolicy: ReactiveWritePolicy.never, // Aumenta o rastreamento de mutações incorretas
      disableErrorBoundaries: false, // Deixa os erros "crasharem" a aplicação
      isSpyEnabled: false);
  mainContext.spy((event) {
    if (kDebugMode) {
        print(event);
      }
   });
 
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  await initDepedencies(getIt, (v) {
    flutterLocalNotificationsPlugin = v;
  });
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]).then((value) => runApp(EasyLocalization(
        child: MyApp(
          notificationsPlugin: flutterLocalNotificationsPlugin,
        ),
        supportedLocales: [
           const Locale('en', 'US'), //Inglês
          const Locale('pt', 'BR'), //Português(Brasil)
          const Locale('es'), //Espanhol
          const Locale('de', 'DE'), // Alemão
          const Locale('fr'), //Francês
          const Locale('nl', 'NL'), // Holandês
          const Locale('pt', 'PT'), //Português(Portugal)
        ],
        path: 'assets/langs',
        
      )));  
}

