import 'dart:convert';

import 'package:app_treino/appWidgets/componentWidgets/componets/CardPadrao.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/model/novo_chat/models_chats.dart';
import 'package:app_treino/screens/_treino6/novo_chat/chat_controller.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart' hide CardPadrao;
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:url_launcher/url_launcher_string.dart';

late void Function(void Function()) statefulBuilderPush;

ElevatedButton showPushTest() {
  return ElevatedButton(
      onPressed: () {
        showPushWhenAppIsOpen(const RemoteMessage(notification: RemoteNotification(title: 'teste', body: 'teste body')));
      },
      child: const Text('PUSH'));
}

RemoteMessage? messageShow;
showPushWhenAppIsOpenOverLay() {
  return StatefulBuilder(builder: (context, statefulBuilder) {
    statefulBuilderPush = statefulBuilder;
    if (messageShow == null) {
      return Container();
    }
    return AnimatedContainer(
      duration: const Duration(milliseconds: 400),
      curve: Curves.bounceIn,
      child: LocalPushShowWidget(
        message: RemoteMessage(notification: messageShow!.notification, data: messageShow!.data),
      ),
    );
  });
}

showPushWhenAppIsOpen(RemoteMessage message) {
  if (message.data['chat'] != null) {
    ChatModel chat = ChatModel.fromJson(const JsonCodec().decode(message.data['chat']));
    if (!ChatController().podeExibirPush(chat)) {
      return;
    }
  }
  statefulBuilderPush(() {
    messageShow = message;
  });
  Future.delayed(const Duration(seconds: 6)).then((_) {
    statefulBuilderPush(() {
      messageShow = null;
    });
  });
}

class LocalPushShowWidget extends StatefulWidget {
  final RemoteMessage message;
  const LocalPushShowWidget({super.key, required this.message});

  @override
  State<LocalPushShowWidget> createState() => _LocalPushShowWidgetState();
}

class _LocalPushShowWidgetState extends State<LocalPushShowWidget> {
  @override
  Widget build(BuildContext context) {
    bool temLink = widget.message.data['link']?.toString().isNotEmpty ?? false;
    bool temTela = widget.message.data['telaAbrir']?.toString().isNotEmpty ?? false;
    bool isDarkMode = GetIt.I.get<ControladorApp>().themeMode == ThemeMode.dark;
    bool isChat = widget.message.data['chat'] != null;
    ChatModel? chatShow = isChat ? ChatModel.fromJson(const JsonCodec().decode(widget.message.data['chat'])) : null;
    return SafeArea(
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              statefulBuilderPush(() {
                messageShow = null;
              });
              if (isChat) {
                ChatController().abrirTelaChatSeNecessario(chatShow!);
                return;
              }
              //   Navigator.pop(context);
              if (temLink) {
                DSalerta().exibirAlertaSimplificado(
                  context: context,
                  titulo: localizedString('push.link.titulo'),
                  subtitulo: localizedString('push.link.disclaimer'),
                  //   tituloBotaoSecundario: localizedString('push.link.voltar'),
                  tituloBotao: localizedString('push.link.acessar'),
                  onTap: () {
                    launchUrlString(widget.message.data['link']);
                  },
                );
              } else if (temTela) {
                Navigator.pushNamed(context, widget.message.data['telaAbrir']!.toString());
              } else {
                Navigator.pushNamed(context, '/telaNotificacoes');
              }
            },
            behavior: HitTestBehavior.translucent,
            child: IntrinsicHeight(
              child: Container(
                decoration: BoxDecoration(boxShadow: [BoxShadow(color: isDarkMode ? Colors.white12 : Colors.black26, blurRadius: 30)]),
                child: CardPadrao(
                  radius: 16,
                  elevation: 20,
                  color: isDarkMode ? const Color(0xff010819) : Colors.white,
                  margin: const EdgeInsets.all(12),
                  padding: const EdgeInsets.all(12),
                  child: Row(children: [
                    if (isChat)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: CircleAvatar(
                          radius: 23,
                          backgroundImage: NetworkImage(chatShow!.otherUser.foto),
                        ),
                      )
                    else
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Icon(
                        TreinoIcon.App_Treino_6,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DStextBody(
                            widget.message.notification?.title ?? '',
                          ),
                          DStextBody(
                            widget.message.notification?.body ?? '',
                            eHeavy: false,
                          )
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Icon(
                        temLink ? TreinoIcon.external_link_alt : TreinoIcon.arrow_right,
                      ),
                    )
                  ]),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
