import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:health/health.dart';

class HealthPermissionsUtil {
  final Health _health = Health();

  // Tipos de dados que queremos acessar
  final List<HealthDataType> _types = [
    HealthDataType.STEPS,
    HealthDataType.SLEEP_IN_BED,
    HealthDataType.SLEEP_ASLEEP,
    HealthDataType.SLEEP_AWAKE,
    HealthDataType.ACTIVE_ENERGY_BURNED,
    HealthDataType.HEART_RATE,
    if (Platform.isIOS) HealthDataType.DISTANCE_WALKING_RUNNING,
    if (Platform.isAndroid) HealthDataType.DISTANCE_DELTA
  ];

  // Função para solicitar permissões
  Future<bool> requestPermissions() async {
    List<HealthDataType> remainingTypes = List.from(_types);
    bool granted = false;

    for (final type in List.from(_types)) {
      try {
        // Solicita permissão para o tipo atual
        granted = await _health.requestAuthorization([type]);

        if (!granted) {
          // Caso a permissão não seja concedida, remove o tipo da lista
          remainingTypes.remove(type);
          if (kDebugMode) {
            print('Permissão negada para o tipo: $type');
          }
        }
      } catch (e) {
        // Captura exceção e remove o tipo da lista
        remainingTypes.remove(type);
        if (kDebugMode) {
          print('Erro ao solicitar permissão para $type: $e');
        }
      }
    }

    // Tenta novamente com os tipos de dados restantes
    if (remainingTypes.isNotEmpty) {
      try {
        granted = await _health.requestAuthorization(remainingTypes);
      } catch (e) {
        if (kDebugMode) {
          print('Erro ao solicitar permissões com a lista final: $e');
        }
      }
    }

    return granted;
  }
}
