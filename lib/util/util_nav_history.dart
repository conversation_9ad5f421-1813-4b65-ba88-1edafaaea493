import 'package:flutter/material.dart';

class UserActivityTracker {
  static final UserActivityTracker _instance = UserActivityTracker._internal();

  factory UserActivityTracker() {
    return _instance;
  }

  UserActivityTracker._internal();

  final List<Map<String, dynamic>> _activityHistory = [];
    
  // Variável global para armazenar a última tela
  static String _lastValidScreenName = '';
  static String get lastValidScreenName => _lastValidScreenName;

  void logNavigation(String screenName) {
    _activityHistory.add({
      'type': 'navigation',
      'screen': screenName,
    });
        
    // Atualiza a última tela aberta
    if (screenName.contains('/')) {
      _lastValidScreenName = screenName;
    }
  }

  void logClick(String buttonName) {
    _activityHistory.add({
      'type': 'click',
      'button': buttonName,
    });
  }

  List<String> getFormattedActivityHistory() {
    Map<String, int> activityCount = {};
    for (final activity in _activityHistory) {
      String key;
      if (activity['type'] == 'navigation') {
        key = "Navigated to '${activity['screen']}'";
      } else if (activity['type'] == 'click') {
        key = "Clicked on '${activity['button']}'";
      } else {
        continue;
      }

      if (activityCount.containsKey(key)) {
        activityCount[key] = activityCount[key]! + 1;
      } else {
        activityCount[key] = 1;
      }
    }

    return activityCount.entries.map((entry) {
      if (entry.value > 1) {
        return '${entry.key} ${entry.value}X\n';
      } else {
        return '${entry.key}\n';
      }
    }).toList();
  }
}

// NavigatorObserver personalizado para registrar navegações
class UserNavigationObserver extends NavigatorObserver {
  final UserActivityTracker activityTracker;

  UserNavigationObserver(this.activityTracker);

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    // Registra a navegação automaticamente
    String screenName = route.settings.name ?? 'Unknown Screen';
    if (route is DialogRoute) {
      screenName = 'Dialog ${route.debugLabel}';
    }
    if (screenName == 'Unknown Screen' && route is ModalBottomSheetRoute) {
      screenName = 'Modal bottom Sheet ${route.debugLabel}';
    }
    activityTracker.logNavigation(screenName);
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    // Registra a navegação automaticamente
    String screenName = newRoute?.settings.name ?? 'Unknown Screen';
    if (newRoute is DialogRoute) {
      screenName = 'Dialog ${newRoute.debugLabel}';
    }
    if (screenName == 'Unknown Screen' && newRoute is ModalBottomSheetRoute) {
      screenName = 'Modal bottom Sheet ${newRoute.debugLabel}';
    }
    activityTracker.logNavigation(screenName);
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
  }
}
