
import 'package:app_treino/appWidgets/geral/AvatarComBorda.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class ProfilePicController {
  static final ProfilePicController _instance = ProfilePicController._internal();

  factory ProfilePicController() {
    return _instance;
  }
  final Map<String, Function()> _listeners = {};
  void setListener(String name, Function() function) {
    _listeners[name] = function;
  }

  void notify() {
    for (final f in _listeners.values) {
      f.call();
    }
  }

  ProfilePicController._internal();

  void removeListener(String instanceName) {
    _listeners.remove(instanceName);
  }
}

class ProfilePic extends StatefulWidget {
  final String? urlFotoUsuario;
  final double? radius;
  final double? tamanhoBorda;
  final Color? corDaBorda;
  final String? fotoDefault;
  const ProfilePic({super.key, this.urlFotoUsuario, this.radius, this.tamanhoBorda, this.corDaBorda, this.fotoDefault});

  @override
  State<ProfilePic> createState() => _ProfilePicState();
}

class _ProfilePicState extends State<ProfilePic> {
  late String instanceName = '';
  @override
  void initState() {
    instanceName = DateTime.now().toIso8601String();
    ProfilePicController().setListener(instanceName, () {
      setState(() {
      });
    });
    super.initState();
  }

  @override
  void dispose() {
    ProfilePicController().removeListener(instanceName);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AvatarComBorda(
      radius: widget.radius,
      fotoDefault: widget.fotoDefault,
      tamanhoBorda: widget.tamanhoBorda ?? 0,
      corDaBorda: widget.corDaBorda,
      urlFotoUsuario: widget.urlFotoUsuario ?? GetIt.I.get<ControladorCliente>().mUsuarioLogado!.srcImg ?? '',
    );
  }
}
