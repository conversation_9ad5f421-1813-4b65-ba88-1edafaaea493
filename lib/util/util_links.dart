import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:get_it/get_it.dart';

class UtilLinks {
  static String _substituirArgumentosUrl(String url, Map<String, dynamic> substituicoes) {
    // Expressão regular para localizar argumentos na URL no formato {{argumento}}
    RegExp padrao = RegExp(r'{{(.*?)}}');
    // Encontrar todos os argumentos na URL
    Iterable<RegExpMatch> matches = padrao.allMatches(url);
    // Iterar sobre os argumentos encontrados na URL
    for (final RegExpMatch match in matches) {
      // Capturar o argumento sem as chaves {{}}
      String argumentoCompleto = match.group(0)!.toLowerCase();
      String nomeArgumento = match.group(1)!.toLowerCase();
      // Verificar se o argumento está presente no mapa de substituições
      if (substituicoes.containsKey(nomeArgumento.replaceAll('app.', '').toLowerCase())) {
        // Substituir o argumento na URL pelo valor correspondente no mapa
        url = url.replaceAll(argumentoCompleto, substituicoes[nomeArgumento.replaceAll('app.', '').toLowerCase()].toString());
      }
    }

    return url;
  }

  static String montarUrlComDados(String urlOriginal) {
    // Exemplo de uso
    // String urlOriginal = 'https://meulink.com?cpf={{app.cpf}}&nome={{app.nome}}';
    final ControladorCliente mCliente = GetIt.I.get();
    // Mapa de substituições
    Map<String, dynamic> mapaReplacesOriginal = {}, mapaReplaces = {};
    if (mCliente.mDadosDoUsuario != null) {
      mapaReplacesOriginal.addAll(mCliente.mDadosDoUsuario!.toJson());
    }
    if (mCliente.mUsuarioLogado != null) {
      mapaReplacesOriginal.addAll(mCliente.mUsuarioLogado!.toJson());
    }
    if (mCliente.mUsuarioAuth != null) {
      mapaReplacesOriginal.addAll(mCliente.mUsuarioAuth!.toJson());
    }

    mapaReplacesOriginal.forEach((chave, valor) {
      mapaReplaces[chave.toLowerCase()] = valor;
    });

    mapaReplacesOriginal = {};
    return _substituirArgumentosUrl(urlOriginal, mapaReplaces);
  }
}
