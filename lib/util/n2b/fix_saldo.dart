import 'dart:convert';

import 'package:app_treino/ServiceProvider/n2bservices.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/screens/vitio/models/vitioModels.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FixSaldosN2b {
  static final FixSaldosN2b _instance = FixSaldosN2b._internal();

  factory FixSaldosN2b() {
    return _instance;
  }
  N2BService get _n2bService => GetIt.I.get<N2BService>();
  String keyAPPN2B = '';
  String mainAuthorization = '';
  String get tokenPaciente => currentPatient?.jwt ?? '';
  String cpfColaboradorN2B = '';
  bool get isSaldoIlimited => _controladorApp.getConfiguracaoApp(ModuloApp.UNLIMTED_BIOS).habilitado ?? false;
  int saldoAtualBios = 0;
  String namePackage = '';
  ValueNotifier<StatusPacienteVitio> statusTelaPrincipal = ValueNotifier<StatusPacienteVitio>(StatusPacienteVitio.carregando);
  updateStatusTelaPrincipal(StatusPacienteVitio status) {
    statusTelaPrincipal.value = status;
  }

  void resetVariables() {
    currentPatient = null;
    cpfVitio = '';
    biosrealizadas.clear();
    keyAPPN2B = '';
    mainAuthorization = '';
  }

  FixSaldosN2b._internal();
  bool isErrorOnCreatePartner = false;
  String errorMessage = '';
  late Function() onErrorPartner;
  static ControladorCliente get _controllerCliente => GetIt.I.get<ControladorCliente>();
  static ControladorApp get _controladorApp => GetIt.I.get<ControladorApp>();

  String get userToken => currentPatient?.jwt ?? '';
  PatientN2B? currentPatient;
  String cpfVitio = '';
//   String defaultToken = 'eba26daf5aa85616ad56a768a7e24238ca0f98ca055114a993c05d53bd0cbcc92100e9e2';
  List<AvaliacaoBioimpedancia> biosrealizadas = [];
  bool isLoaded = false;

  loadPatient({Function(PatientN2B)? callback, String? cpfColaborador, Function()? falha}) async {
    cpfColaboradorN2B = cpfColaborador?.replaceAll(RegExp(r'[^0-9]'), '') ?? '';
    if (_controladorApp.getConfiguracaoApp(ModuloApp.MODULO_VITIO_BIOIMPEDANCIA).habilitado == false) {
      return;
    }
    updateStatusTelaPrincipal(StatusPacienteVitio.carregando);
    PackageInfo.fromPlatform().then((PackageInfo packageInfo) {
      namePackage = packageInfo.packageName;
      return FirebaseRemoteConfig.instance.fetchAndActivate();
    }).then((onValue) {
      keyAPPN2B = FirebaseRemoteConfig.instance.getString('APP_SECRET_KEY_N2B');
      if (keyAPPN2B.isEmpty) {
        keyAPPN2B = 'S2Me+GPzrVi3BZb8nBGAo+FCAwvmuh31lPeEiUC/yuI=';
      }
      mainAuthorization = FirebaseRemoteConfig.instance.getString('APP_TOKEN_KEY_N2B');
      mainAuthorization = 'eba26daf5aa85616ad56a768a7e24238ca0f98ca055114a993c05d53bd0cbcc92100e9e2';
      if (keyAPPN2B.isEmpty || mainAuthorization.isEmpty) {
        throw Exception('APP_SECRET_KEY_N2B or APP_TOKEN_KEY_N2B not found');
      }
      return _consultarDadosUsuario();
    }).then((cpf) {
      if (cpf == 'ASK_FOR_CPF' && cpfColaborador == null) {
        throw Exception('ASK_FOR_CPF');
      } else if (cpfColaborador?.isNotEmpty ?? false) {
        cpfColaborador = cpfColaborador?.replaceAll(RegExp(r'[^0-9]'), '');
        cpf = cpfColaborador;
      }
      return _n2bService.findPatientsByEmailOrCpf(cpf ?? _controllerCliente.mUsuarioLogado?.emailN2b, 'Bearer $mainAuthorization');
    }).then((patients) async {
      if ((cpfColaborador?.isNotEmpty ?? false) && patients.isNotEmpty) {
        SharedPreferences dbShared = await SharedPreferences.getInstance();
        dbShared.setString('cpf_colaborador_n2b', cpfColaborador!);
      }
      for (var i = 0; i < patients.length; i++) {
        patients[i].patient.jwt = patients[i].jwt;
      }
      bool temPacienteCadastrado = patients.any((element) {
        return element.patient.partner?.id == _controladorApp.n2bExternalPartnerIdOrigemUsuario;
      });
      if (temPacienteCadastrado) {
        // Se já tiver paciente cadastrado, pega o primeiro
        currentPatient = patients.firstWhere((element) {
          return element.patient.partner?.id == _controladorApp.n2bExternalPartnerIdOrigemUsuario;
        }).patient;
        return currentPatient;
      } else {
        insertN2bPatient().then((onValue) {
          if (onValue) {
            loadPatient(callback: callback, cpfColaborador: cpfColaboradorN2B, falha: falha);
          }
        });
      }
    }).then((patient) {
      currentPatient = patient;
      consultaSaldoAcademia(
        callback: () {
          isLoaded = true;
          updateStatusTelaPrincipal(StatusPacienteVitio.planoAtivo);
          callback?.call(currentPatient!);
        },
      );
    }).catchError((onError) {
      Future.delayed(const Duration(seconds: 2)).then((onValue) {
        if (onError.message == 'ASK_FOR_CPF') {
          updateStatusTelaPrincipal(StatusPacienteVitio.cpfInvalido);
        } else if (onError.message == 'APP_SECRET_KEY_N2B or APP_TOKEN_KEY_N2B not found') {
          updateStatusTelaPrincipal(StatusPacienteVitio.erro);
        }
      });
    });
  }

  consultaSaldoAcademia({required Function() callback}) {
    // Consulta bios realizadas pelo paciente
    List<Future<List<AvaliacaoBioimpedancia>>> mChamadas = [];
    // FixSaldosN2b().currentToken?.otherTokens?.forEach((element) {
    //   mChamadas.add(_n2bService.get_bioimpedances_bytoken(token: 'Bearer ${element}'));
    // });
    mChamadas.add(_n2bService.get_bioimpedances_bytoken(token: 'Bearer ${tokenPaciente}'));
    Future.wait(mChamadas).then((bios) {
      biosrealizadas = bios.expand((e) => e).toList();
      // Consulta saldo da academia
      return _n2bService.validar_produto_bio_totem(
        _controladorApp.chave ?? '',
        _controllerCliente.mUsuarioLogado?.codEmpresa ?? 0,
        _controllerCliente.mUsuarioLogado?.codigoCliente ?? 0,
      );
    }).then((e) {
      fixIfNecessary(patient: currentPatient!, produtosBio: e, callback: callback);
    }).catchError((onError) {
      callback();
    });
  }

  Future<bool> insertN2bPatient() {
    String birthDate = (_controllerCliente.mDadosDoUsuario?.dataNascimento ?? '');
    var bodyCriacao = {
      'name': _controllerCliente.mUsuarioLogado?.nome ?? '',
      'document_number': cpfVitio,
      'email': _controllerCliente.mUsuarioLogado?.emailN2b,
      'goal_id': '25fedad5-6813-4ac4-8cb5-57f8fad55e12',
      'restriction_ids': [],
      'other_restriction': '',
      'partner_id': _controladorApp.n2bPartnerId,
      'partner_external_id': '${GetIt.I.get<ControladorApp>().n2bExternalPartnerIdOrigemUsuario}',
      'cell_phone': _controllerCliente.mUsuarioLogado?.telefone ?? (_controllerCliente.mDadosDoUsuario?.telCelular ?? ''),
      'birth_date': birthDate.isEmpty ? '' : '${birthDate.split('/')[2]}-${birthDate.split('/')[1]}-${birthDate.split('/')[0]}'
    };
    if ((_controladorApp.n2bPartnerId ?? '').isNotEmpty) {
      return _n2bService.patients(bodyCriacao, 'Bearer $mainAuthorization').then((value) {
        return true;
      }).catchError((onError) {
        try {
          Map<String, dynamic> jsonErros = const JsonCodec().decode(onError.response.toString());
          if (jsonErros['message'] != null) {
            String mensagem = jsonErros['message'].toString();
            if (mensagem.contains('document_number should not be empty')) {
              errorMessage = 'erro_saude_cpf';
              updateStatusTelaPrincipal(StatusPacienteVitio.cpfInvalido);
            } else if (mensagem.contains('email should not be empty')) {
              updateStatusTelaPrincipal(StatusPacienteVitio.emailInvalido);
              errorMessage = 'erro_saude_email';
            } else if (mensagem.contains('email has already been taken')) {
              updateStatusTelaPrincipal(StatusPacienteVitio.emailInvalido);
            } else if (mensagem.contains('document_number has already been taken')) {
              updateStatusTelaPrincipal(StatusPacienteVitio.jaExistePacienteComCpf);
            } else {
              errorMessage = 'Erro ao criar usuário';
              updateStatusTelaPrincipal(StatusPacienteVitio.erro);
            }

            isErrorOnCreatePartner = true;
          }
          // onErrorPartner();
        } catch (e) {}
        return false;
      });
    } else {
      isErrorOnCreatePartner = true;
      updateStatusTelaPrincipal(StatusPacienteVitio.erro);
      return Future.value(false);
    }
  }

  fixIfNecessary({required PatientN2B patient, RetornoSaldoProdutoBioTotem? produtosBio, required Function() callback}) async {
    int creditoExtra = 0;
    if (_controladorApp.getConfiguracaoApp(ModuloApp.CREDITO_BOAS_VINDAS_N2B).habilitado ?? false) {
      creditoExtra = 1;
    }
    produtosBio?.quantidadeProdutoBioTotemTotal = (produtosBio.quantidadeProdutoBioTotemTotal ?? 0) + 1 + creditoExtra;
    if (isSaldoIlimited && (patient.bioimpedance_credit ?? 0) <= 0) {
      saldoAtualBios = currentPatient?.bioimpedance_credit ?? 0;
      _n2bService
          .add_credit_n2b(patient.id!, RetornoSaldoDebito(type: 'bioimpedance', value: ((currentPatient!.bioimpedance_pending ?? 0) + 1)), 'Bearer $mainAuthorization')
          .then((onValue) {
        currentPatient?.bioimpedance_credit ??= 1;
        saldoAtualBios = currentPatient?.bioimpedance_credit ?? 0;
        callback();
      }).catchError((onError) {
        callback();
      });
      return;
    }
    if ((patient.bioimpedance_credit ?? 0) <= 1 && (produtosBio?.quantidadeProdutoBioTotemTotal ?? 0) == 0) {
      saldoAtualBios = currentPatient?.bioimpedance_credit ?? 0;
      callback();
      return;
    }
    // Valida se bios realizadas + saldo é igual ao saldo da academia
    if ((patient.bioimpedance_credit ?? 0) + biosrealizadas.length == (produtosBio?.quantidadeProdutoBioTotemTotal ?? 0)) {
      saldoAtualBios = currentPatient?.bioimpedance_credit ?? 0;
      callback();
      return;
    }

    // Se o saldo da academia for diferente do saldo do paciente reliza ajuste
    if ((patient.bioimpedance_credit ?? 0) > (((produtosBio?.quantidadeProdutoBioTotemTotal ?? 0)) - biosrealizadas.length)) {
      int decrementar = ((patient.bioimpedance_credit ?? 0) - ((produtosBio?.quantidadeProdutoBioTotemTotal ?? 0) - biosrealizadas.length));
      // Ajusta o saldo do paciente
      //   _controladorSaude.updateSaldoBioimpedancia(patient.id, biosAcademia);
      _n2bService
          .get_debit_credit(
        currentPatient!.id!,
        RetornoSaldoDebito(type: 'bioimpedance', value: decrementar),
        'Bearer $mainAuthorization',
      )
          .then((onValue) {
        // Atualiza o saldo do paciente
        // currentPatient!.bioimpedance_credit = ((patient.bioimpedance_credit ?? 0) - (produtosBio?.quantidadeProdutoBioTotemTotal ?? 0)) - biosrealizadas.length;
        currentPatient!.bioimpedance_pending = (patient.bioimpedance_credit ?? 0) - decrementar;
        saldoAtualBios = currentPatient?.bioimpedance_credit ?? 0;
        callback();
      }).catchError((onError) {
        callback();
      });
    } else if (((patient.bioimpedance_credit ?? 0) + biosrealizadas.length) < (produtosBio?.quantidadeProdutoBioTotemTotal ?? 0)) {
      int diferenca = (produtosBio?.quantidadeProdutoBioTotemTotal ?? 0) - (patient.bioimpedance_credit ?? 0) - biosrealizadas.length;
      // Se o saldo for diferente então adiciona o saldo para o paciente
      _n2bService.add_credit_n2b(patient.id!, RetornoSaldoDebito(type: 'bioimpedance', value: diferenca), 'Bearer $mainAuthorization').then((onValue) {
        currentPatient?.bioimpedance_credit = diferenca + (patient.bioimpedance_credit ?? 0);
        saldoAtualBios = currentPatient?.bioimpedance_credit ?? 0;
        callback();
      }).catchError((onError) {
        callback();
      });
    } else {
      callback();
    }
  }

// consulta dados e salva na shared prefs
  Future<String?> _consultarDadosUsuario() async {
    final db = await SharedPreferences.getInstance();
    cpfVitio = (_controllerCliente.mDadosDoUsuario?.cpf ?? db.getString('CPFVITIO')) ?? '';
    if (_controllerCliente.isUsuarioColaborador) {
      if (cpfColaboradorN2B.isNotEmpty) {
        cpfColaboradorN2B = cpfColaboradorN2B.replaceAll(RegExp(r'[^0-9]'), '');
        db.setString('cpf_colaborador_n2b', cpfColaboradorN2B);
      } else {
        cpfColaboradorN2B = (db.getString('cpf_colaborador_n2b') ?? '');
      }
      cpfVitio = cpfColaboradorN2B;
      return cpfColaboradorN2B.isEmpty ? 'ASK_FOR_CPF' : cpfColaboradorN2B.replaceAll(RegExp(r'[^0-9]'), '');
    }
    if (cpfVitio.isNotEmpty) {
      return cpfVitio.replaceAll(RegExp(r'[^0-9]'), '');
    } else {
      await _controllerCliente.consultarDadosDoUsuario(
        sucesso: () {
          cpfVitio = _controllerCliente.mDadosDoUsuario?.cpf ?? '';
          db.setString('CPFVITIO', cpfVitio);
        },
        falha: (mensagem) {
          cpfVitio = '';
        },
      );
      return cpfVitio.replaceAll(RegExp(r'[^0-9]'), '');
    }
  }
}
