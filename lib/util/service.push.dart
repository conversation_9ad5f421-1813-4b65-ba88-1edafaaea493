import 'dart:async';
import 'dart:developer';

import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorNotificacoes.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoDeTreino.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/controlladores/controladorPlanner.dart';
import 'package:app_treino/model/util/string_extension.dart';
import 'package:app_treino/util/notificacao_app_aberto.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher_string.dart'; // Importar a biblioteca de permissões

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  BuildContext context = GetIt.I.get<NavigationService>().context;
  GetIt.I.get<ControladorNotificacoes>().validarPushEmBackground(message.data);
  if (message.data['link']?.toString().isNotEmpty ?? false) {
    PushNotificationService().mostrarAlertaLink(message);
  } else if (message.data['telaAbrir']?.toString().isNotEmpty ?? false) {
    Navigator.pushNamed(context, message.data['telaAbrir']!.toString());
  } else {
    Navigator.pushNamed(context, '/telaNotificacoes');
  }
}
class PushNotificationService {
//   FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  static final PushNotificationService _singleton = PushNotificationService._internal();

  factory PushNotificationService() {
    return _singleton;
  }
  BuildContext get context => GetIt.I.get<NavigationService>().context;
  PushNotificationService._internal();

  void mostrarAlertaLink(RemoteMessage message) {
    if (message.data['link']?.toString().isNotEmpty ?? false) {
      DSalerta().exibirAlertaSimplificado(
        context: context,
        titulo: localizedString('push.link.titulo'),
        subtitulo: localizedString('push.link.disclaimer'),
        // tituloBotaoSecundario: localizedString('push.link.voltar'),
        tituloBotao: localizedString('push.link.acessar'),
        onTap: () {
          launchUrlString(message.data['link']);
        },
      );
    } else if (message.data['telaAbrir']?.toString().isNotEmpty ?? false) {
      Navigator.pushNamed(context, message.data['telaAbrir']!.toString());
    } else {
      Navigator.pushNamed(context, '/telaNotificacoes');
    }
  }

  Future<void> initialize() async {
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
    FirebaseMessaging.onMessage.listen(showFlutterNotification);
    FirebaseMessaging.instance.getInitialMessage().then((message) {
      if (message == null) {
        return;
      }
      var loadMessage = () {
        if (GetIt.I.get<ControladorNotificacoes>().carregouApp) {
          mostrarAlertaLink(message);
        }
      };
      Timer.periodic(const Duration(seconds: 1), (timer) {
        if (GetIt.I.get<ControladorNotificacoes>().carregouApp) {
          loadMessage();
          timer.cancel();
        }
      });
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      var loadMessage = () {
        if (GetIt.I.get<ControladorNotificacoes>().carregouApp) {
          mostrarAlertaLink(message);
        }
      };
      Timer.periodic(const Duration(seconds: 1), (timer) {
        if (GetIt.I.get<ControladorNotificacoes>().carregouApp) {
          loadMessage();
          timer.cancel();
        }
      });
    }).onError((xx) {});
  }

  void showFlutterNotification(RemoteMessage message) {
    RemoteNotification? notification = message.notification;
    // AndroidNotification? android = message.notification?.android;
    if (notification != null) {
      showNotification(message);
    }
    if (notification?.title?.contains('Seu treino está disponível') ?? false) {
      GetIt.I.get<ControladorPrescricaoDeTreino>().consultarLinhaDoTempoComFiltro(idAluno: num.parse(GetIt.I.get<ControladorCliente>().mUsuarioLogado?.matricula ?? '0'), idEmpresa: GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 1);
      GetIt.I.get<ControladorPlanner>().obterFichaDoDia();
    }
  }

  static Future<bool> checkNotificationPermissions() async {
    try {
      if (await Permission.notification.status.isGranted) {
        // Permissão concedida para notificações
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  static Future<void> requestNotificationPermissions() async {
    final PermissionStatus status = await Permission.notification.request();
    if (status.isGranted) {
      log('// Permissão concedida para notificações');
    }
    return Future(() => null);
  }

  Future<void> showNotification(RemoteMessage message) async {
    showPushWhenAppIsOpen(message);
    return;
  }

  Future<void> onSelectNotification(String? payload) async {
    log('// Handle notification tap');
  }

  Future<void> onDidReceiveLocalNotification(int id, String? title, String? body, String? payload) async {
    log('// Handle received notification');
  }
}
