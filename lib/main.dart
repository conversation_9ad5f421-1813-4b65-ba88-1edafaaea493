import 'dart:async';

import 'package:after_layout/after_layout.dart';
import 'package:app_treino/ServiceProvider/StuckTrace.dart';
import 'package:app_treino/appWidgets/componentWidgets/camera/CameraCrop.dart';
import 'package:app_treino/appWidgets/componentWidgets/camera/CameraPacto.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorNutricao.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/fabricaGetIt.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/model/geral/TelaAcessoNegadoBodyFit.dart';
import 'package:app_treino/model/util/UtilColor.dart';
import 'package:app_treino/screens/HomePage.dart';
import 'package:app_treino/screens/TelaResultadoBioempedancia.dart';
import 'package:app_treino/screens/_treino6/TelaAuntenticarAssinaturaContratoAditivo.dart';
import 'package:app_treino/screens/_treino6/TelaCodigoAutenticacao.dart';
import 'package:app_treino/screens/_treino6/TelaFichasVerMais.dart';
import 'package:app_treino/screens/_treino6/TelaMeuLink.dart';
import 'package:app_treino/screens/_treino6/TelaMeuLinkWebView.dart';
import 'package:app_treino/screens/_treino6/anamnese_ia/TelaAnamenseOnboardGenero.dart';
import 'package:app_treino/screens/_treino6/anamnese_ia/TelaAnamnese.dart';
import 'package:app_treino/screens/_treino6/anamnese_ia/TelaAnamneseObjetivo.dart';
import 'package:app_treino/screens/_treino6/anamnese_ia/TelaAnamneseOnboardAltura.dart';
import 'package:app_treino/screens/_treino6/anamnese_ia/TelaAnamneseOnboardAtividade.dart';
import 'package:app_treino/screens/_treino6/anamnese_ia/TelaAnamneseOnboardExperienciaTreino.dart';
import 'package:app_treino/screens/_treino6/anamnese_ia/TelaAnamneseOnboardIdade.dart';
import 'package:app_treino/screens/_treino6/anamnese_ia/TelaAnamneseOnboardNivel.dart';
import 'package:app_treino/screens/_treino6/anamnese_ia/TelaAnamneseOnboardPeso.dart';
import 'package:app_treino/screens/_treino6/anamnese_ia/TelaAnamneseOnboardTempoPorDia.dart';
import 'package:app_treino/screens/_treino6/anamnese_ia/TelaAnamneseRestricaoBloqueio.dart';
import 'package:app_treino/screens/_treino6/anamnese_ia/TelaAnamneseRestricoes.dart';
import 'package:app_treino/screens/_treino6/TelaFinalizacaoCadastro.dart';
import 'package:app_treino/screens/_treino6/TelaFinalizacaoCadastroReconhecimentoFacial.dart';
import 'package:app_treino/screens/_treino6/TelaPerfilAluno.dart';
import 'package:app_treino/screens/_treino6/TelaPerfilProfessor.dart';
import 'package:app_treino/screens/_treino6/TelaPreLogin.dart';
import 'package:app_treino/screens/_treino6/TelaReportarProblema.dart';
import 'package:app_treino/screens/_treino6/TelaTermosPoliticas.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoConcluido.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoConcluidoCompartilhamento.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoConcluidoCompartilhamentoFoto.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoConcluidoPercentual.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoConfiguracoes.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoDetalheAtividade.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoDetalheAtividadeAcompanhamento.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoDetalheFicha.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoExecucao.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoPausa.dart';
import 'package:app_treino/screens/_treino6/historico_de_treinos/TelaHistoricoDeTreinos.dart';
import 'package:app_treino/screens/_treino6/historico_de_treinos/TelaHistoricoDeTreinosAdicionarNovo.dart';
import 'package:app_treino/screens/_treino6/historico_de_treinos/TelaHistoricoDeTreinosAdicionarNovoSelecionarExercicio.dart';
import 'package:app_treino/screens/_treino6/locacao/TelaLocacao.dart';
import 'package:app_treino/screens/_treino6/locacao/TelaLocacaoDetalhes.dart';
import 'package:app_treino/screens/_treino6/modulo_nutri_fitstream/TelaNutricaoCriacaoPlanoFitstream.dart';
import 'package:app_treino/screens/_treino6/modulo_nutri_fitstream/TelaNutricaoDetalhesFitstream.dart';
import 'package:app_treino/screens/_treino6/modulo_nutri_fitstream/TelaNutricaoVideoDetalheFitstream.dart';
import 'package:app_treino/screens/_treino6/novo_chat/active_chats_screen.dart';
import 'package:app_treino/screens/_treino6/novo_chat/chat_adicionar_foto.dart';
import 'package:app_treino/screens/_treino6/novo_chat/chat_mididas_envidas.dart';
import 'package:app_treino/screens/_treino6/novo_chat/chat_screen.dart';
import 'package:app_treino/screens/about/TelaSobre.dart';
import 'package:app_treino/screens/acompanhar/DetalhesAlunoAcompanhar.dart';
import 'package:app_treino/screens/acompanhar/TelaAcompanhar.dart';
import 'package:app_treino/screens/acompanhar/TelaAdicionarAlunoAcompanhar.dart';
import 'package:app_treino/screens/acompanhar/TelaSelecionarFicha.dart';
import 'package:app_treino/screens/agenda_treino6/NovaTelaAgendaAluno.dart';
import 'package:app_treino/screens/agenda_treino6/TelaReagendar.dart';
import 'package:app_treino/screens/aulasETurmas/TelaAdicionarAlunoAula.dart';
import 'package:app_treino/screens/aulasETurmas/TelaAulaTurmasProfessor.dart';
import 'package:app_treino/screens/aulasETurmas/TelaAulasTurmas.dart';
import 'package:app_treino/screens/aulasETurmas/TelaAulasTurmasCalendario.dart';
import 'package:app_treino/screens/aulasETurmas/TelaAulasTurmasProfessorFinalizadas.dart';
import 'package:app_treino/screens/aulasETurmas/TelaConfirmarPresencaAluno.dart';
import 'package:app_treino/screens/aulasETurmas/TelaDetalheDaAulaTurmaConfirmarPresenca.dart';
import 'package:app_treino/screens/aulasETurmas/TelaDetalhesDaAulaTurma.dart';
import 'package:app_treino/screens/aulasETurmas/TelaNovoQRCode.dart';
import 'package:app_treino/screens/aulasETurmas/TelaSelecaoContrato.dart';
import 'package:app_treino/screens/aulasETurmas/TelaSelecaoUnidade.dart';
import 'package:app_treino/screens/avaliacao_professor/Tela_avaliacao_profesores_lista.dart';
import 'package:app_treino/screens/chat/TelaChat.dart';
import 'package:app_treino/screens/chat/TelaContatosChat.dart';
import 'package:app_treino/screens/chat/TelaConversaChat.dart';
import 'package:app_treino/screens/configuracoes/TelaConfiguracoes.dart';
import 'package:app_treino/screens/configuracoes/notificacoes/TelaConfiguracoesPushProfIA.dart';
import 'package:app_treino/screens/contrato/TelaContratoDoPlano.dart';
import 'package:app_treino/screens/contrato/TelaOpcoesDeCompraNovoContrato.dart';
import 'package:app_treino/screens/contrato/TelaPagamentoParcelas.dart';
import 'package:app_treino/screens/contrato/TelaRenovacaoContrato.dart';
import 'package:app_treino/screens/contrato/TelaVisualizarDetalhesDeNovoContrato.dart';
import 'package:app_treino/screens/contrato/compra/FormDadosDeCartao.dart';
import 'package:app_treino/screens/contrato/compra/formResumo.dart';
import 'package:app_treino/screens/editarColaborador/TelaCadastrarEditarCartao.dart';
import 'package:app_treino/screens/editarColaborador/TelaCancelarContrato.dart';
import 'package:app_treino/screens/editarColaborador/TelaConfirmarFerias.dart';
import 'package:app_treino/screens/editarColaborador/TelaConfirmarTrancamento.dart';
import 'package:app_treino/screens/editarColaborador/TelaContratoAditivos.dart';
import 'package:app_treino/screens/editarColaborador/TelaContratoAssinatura.dart';
import 'package:app_treino/screens/editarColaborador/TelaContratoBloqueioAssinatura.dart';
import 'package:app_treino/screens/editarColaborador/TelaContratoFerias.dart';
import 'package:app_treino/screens/editarColaborador/TelaContratoTermos.dart';
import 'package:app_treino/screens/editarColaborador/TelaContratosAluno.dart';
import 'package:app_treino/screens/editarColaborador/TelaCreditosContrato.dart';
import 'package:app_treino/screens/editarColaborador/TelaHistoricoDeParcela.dart';
import 'package:app_treino/screens/editarColaborador/TelaManterCartaoCredito.dart';
import 'package:app_treino/screens/editarColaborador/TelaManterDadosContato.dart';
import 'package:app_treino/screens/editarColaborador/TelaManterSenhaUsuario.dart';
import 'package:app_treino/screens/editarColaborador/TelaMinhaConta.dart';
import 'package:app_treino/screens/editarColaborador/TelaPerfil.dart';
import 'package:app_treino/screens/editarColaborador/TelaProgressoGraduacao.dart';
import 'package:app_treino/screens/editarColaborador/TelaRenovarContrato.dart';
import 'package:app_treino/screens/editarColaborador/TelaTrancamentoDeContrato.dart';
import 'package:app_treino/screens/editarColaborador/TelaTranferirCredTreino.dart';
import 'package:app_treino/screens/exercicios/TelaVerVideoExercicio.dart';
import 'package:app_treino/screens/feed/TelaComentariosDoFeed.dart';
import 'package:app_treino/screens/feed/TelaDenunciaPost.dart';
import 'package:app_treino/screens/feed/TelaDetalhesDaPublicao.dart';
import 'package:app_treino/screens/feed/TelaFeed.dart';
import 'package:app_treino/screens/historicoFitness/tela_cluble_vantagens.dart';
import 'package:app_treino/screens/homefit/TelaTabHomeFit.dart';
import 'package:app_treino/screens/homefit/detalheTreinoViewController.dart';
import 'package:app_treino/screens/homefit/live/CadastroLivePassoUm.dart';
import 'package:app_treino/screens/homefit/live/DetalhesLiveView.dart';
import 'package:app_treino/screens/homefit/online/tela_manter_treino_online.dart';
import 'package:app_treino/screens/homefit/telaRankingUsuarios.dart';
import 'package:app_treino/screens/homefit/todos_treino_em_casa.dart';
import 'package:app_treino/screens/login/TelaBuscarCadastroPorCPF.dart';
import 'package:app_treino/screens/login/TelaBuscarCadastroPorTelefone.dart';
import 'package:app_treino/screens/login/TelaBuscarClienteApp.dart';
import 'package:app_treino/screens/login/TelaConfigHomologacao.dart';
import 'package:app_treino/screens/login/TelaDetalhesClienteAppUnidades.dart';
import 'package:app_treino/screens/login/TelaLoginEsqueceuSenha.dart';
import 'package:app_treino/screens/login/TelaLoginUsuarioSenha.dart';
import 'package:app_treino/screens/login/TelaResultadoBuscarUsuarioCPF.dart';
import 'package:app_treino/screens/login/SelecionarUsuarioTelefone.dart';
import 'package:app_treino/screens/login/TelaUsuariosQueJaLogaram.dart';
import 'package:app_treino/screens/login/TelaValidarSMS.dart';
import 'package:app_treino/screens/notificacao/TelaDetalheNovidades.dart';
import 'package:app_treino/screens/notificacao/TelaNotificacoes.dart';
import 'package:app_treino/screens/novaAvaliacaoFisica/TelaInfoIMC.dart';
import 'package:app_treino/screens/novaAvaliacaoFisica/cameraComparativo/ResumoFotosAvaliacao.dart';
import 'package:app_treino/screens/novaAvaliacaoFisica/itemCompararFotos/ItemPrimeiroAcessoPostural.dart';
import 'package:app_treino/screens/novaAvaliacaoFisica/itemCompararFotos/TelaSubstituirFoto.dart';
import 'package:app_treino/screens/novaAvaliacaoFisica/itemCompararFotos/VisualizarTodasFotos.dart';
import 'package:app_treino/screens/novaDashboardColaborador/TelaMInhaCarteiraContratos.dart';
import 'package:app_treino/screens/novaDashboardColaborador/TelaMinhaCarteira.dart';
import 'package:app_treino/screens/novaDashboardColaborador/TelaNovaDashboard.dart';
import 'package:app_treino/screens/_treino6/TelaNewCross.dart';
import 'package:app_treino/screens/novaDashboardColaborador/telaBloqueioColaborador.dart/TelaBloqueioColaboradorProfessor.dart';
import 'package:app_treino/screens/novoCross/TelaWodsPorDia.dart';
import 'package:app_treino/screens/novoCross/calendario/SubTelaCalendarioWod.dart';
import 'package:app_treino/screens/novoCross/modalRankingEPostarResultado/CompartilhamentoResultados.dart';
import 'package:app_treino/screens/novoCross/modalRankingEPostarResultado/CompartilhamentoResultadosScreenShot.dart';
import 'package:app_treino/screens/novoCross/novas_telas/NovaTelaAtividadesCross.dart';
import 'package:app_treino/screens/novoCross/novas_telas/NovaTelaDetalhesAtividadeCross.dart';
import 'package:app_treino/screens/novoCross/novas_telas/TelaAtividadesPersonalRecords.dart';
import 'package:app_treino/screens/novoCross/novas_telas/TelaCronometro.dart';
import 'package:app_treino/screens/novoCross/novas_telas/TelaDetalhesDoWodNova.dart';
import 'package:app_treino/screens/novoCross/novas_telas/TelaDetalhesPR.dart';
import 'package:app_treino/screens/novoCross/novas_telas/TelaEmom.dart';
import 'package:app_treino/screens/novoCross/novas_telas/TelaNovoRecord.dart';
import 'package:app_treino/screens/novoCross/novas_telas/TelaTabata.dart';
import 'package:app_treino/screens/novoCross/resultadoWod/PostarResultadoWod.dart';
import 'package:app_treino/screens/novoCrossColaborador/TelaCadastroWod.dart';
import 'package:app_treino/screens/novoCrossColaborador/TelaCadastroWodAvancado.dart';
import 'package:app_treino/screens/novoCrossColaborador/TelaListaAparelhosWod.dart';
import 'package:app_treino/screens/novoCrossColaborador/TelaListaWods.dart';
import 'package:app_treino/screens/novologin/TelaValidarLoginTelefone.dart';
import 'package:app_treino/screens/novologin/TelaValidarLoginUsuario.dart';
import 'package:app_treino/screens/novologin/TelaVerCentralDeAjuda.dart';
import 'package:app_treino/screens/novologin/TelaVerUsuariosAssociados.dart';
import 'package:app_treino/screens/novologin/TelaVincularEmailApple.dart';
import 'package:app_treino/screens/novologin/telaVisualizarUsuariosRecentes.dart';
import 'package:app_treino/screens/novologin/tela_login_usuario_telefone.dart';
import 'package:app_treino/screens/novologin/tela_selecao_metodo_login.dart';
import 'package:app_treino/screens/nutricao/TelaConfiguracaoConsumoAgua.dart';
import 'package:app_treino/screens/nutricao/TelaRespirar.dart';
import 'package:app_treino/screens/parq/TelaParq.dart';
import 'package:app_treino/screens/prescricao/TelaAdicionarAtividades.dart';
import 'package:app_treino/screens/prescricao/TelaAdicionarFichas.dart';
import 'package:app_treino/screens/prescricao/TelaAdicionarProgramaFichaNoAluno.dart';
import 'package:app_treino/screens/prescricao/TelaAdicionarProgramas.dart';
import 'package:app_treino/screens/prescricao/TelaAtividadesSelecionadasParaAdicao.dart';
import 'package:app_treino/screens/prescricao/TelaFicha.dart';
import 'package:app_treino/screens/prescricao/TelaFichaDetalhe.dart';
import 'package:app_treino/screens/prescricao/TelaFichasEProgramas.dart';
import 'package:app_treino/screens/prescricao/TelaPrograma.dart';
import 'package:app_treino/screens/prescricao/TelaProgramaDetalhe.dart';
import 'package:app_treino/screens/prescricaoDeTreino/treinoIA/TelaRevisaoProgramasIA.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaAvaliacaoFisicaPerfilAluno.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaDetalhesDoAluno.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaFiltroTreinosModelo.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaFiltrosAluno.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaFiltrosTimeLine.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaFotosRegistroFacial.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaHistoricoAluno.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaHistoricoExecsAluno.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaListaAlunos.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaManterTreino.dart';
import 'package:app_treino/screens/prescricaoDeTreino/TelaRegistroFacial.dart';
import 'package:app_treino/screens/prescricaoDeTreino/treinoIA/TelaRevisaoFichaIA.dart';
import 'package:app_treino/screens/prescricaoDeTreino/treinoIA/TelaTreinosAguardandoRevisaoIA.dart';
import 'package:app_treino/screens/splash/NovaSplashScreen.dart';
import 'package:app_treino/screens/splash/TelaClienteAppDesativado.dart';
import 'package:app_treino/screens/tela_etapas_balanca.dart';
import 'package:app_treino/screens/tela_historico_aulas/TelaCompartilharAulaSelfLoops.dart';
import 'package:app_treino/screens/transparency/TelaBloqueioColaboradorInativo.dart';
import 'package:app_treino/screens/tela_historico_aulas.dart';
import 'package:app_treino/screens/transparency/TelaProcessoAlunoVisitante.dart';
import 'package:app_treino/screens/treino/TelaConcluirTreino.dart';
import 'package:app_treino/screens/treino/TelaDetalheDaFicha.dart';
import 'package:app_treino/screens/treino/TelaDetalhesAtividade.dart';
import 'package:app_treino/screens/treino/TelaHistoricoExecucao.dart';
import 'package:app_treino/screens/treino/TelaListaFichas.dart';
import 'package:app_treino/screens/treino/TelaPrepararTreino.dart';
import 'package:app_treino/screens/treino/TelaVerMaisProgramaTreino.dart';
import 'package:app_treino/screens/vitio/avaliacaoBioimpedancia/TelaConectarBalanca.dart';
import 'package:app_treino/screens/vitio/avaliacaoBioimpedancia/TelaNovaBioimpedancia.dart';
import 'package:app_treino/util/util_nav_history.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
// import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:get_it/get_it.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:app_treino/controlladores/ControladorNotificacoes.dart';
import 'package:app_treino/model/vendaplanos/TelaAcessoNegado.dart';
import 'package:app_treino/screens/novaAvaliacaoFisica/ItemMinhaEvolucao.dart';
import 'package:app_treino/screens/novaAvaliacaoFisica/itemCompararFotos/ItemCompararFotos.dart';
import 'package:screenshot/screenshot.dart';



class MyApp extends StatefulWidget {
  MyApp({this.notificationsPlugin});
  final FlutterLocalNotificationsPlugin? notificationsPlugin;
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with AfterLayoutMixin<MyApp> {
  final _controller = GetIt.I.get<ControladorApp>();
  final window = WidgetsBinding.instance.platformDispatcher;
  FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  //var _platformLocalNotification = MethodChannel('crossingthestreams.io/resourceResolver');

  @override
  void initState() {
    super.initState();
    EasyLocalization.logger.enableBuildModes = [];
    var initializationSettingsAndroid = const AndroidInitializationSettings('@mipmap/ic_launcher');
    var initializationSettingsIOS = DarwinInitializationSettings(
        requestAlertPermission: false,
        requestBadgePermission: false,
        requestSoundPermission: false,
        onDidReceiveLocalNotification: GetIt.I.get<ControladorNotificacoes>().onDidReceiveLocalNotification);
    var initializationSettings = InitializationSettings(iOS: initializationSettingsIOS, android: initializationSettingsAndroid);
    widget.notificationsPlugin!.initialize(initializationSettings);
    GetIt.I.get<ControladorNotificacoes>().flutterNotificationLocalPlugin = widget.notificationsPlugin;
    GetIt.I.get<ControladorNutricao>().flutterNotificationLocalPlugin = widget.notificationsPlugin;
    GetIt.I.get<ControladorNotificacoes>().verificaSeAbriuAppApartirDePush();

    // FirebaseDynamicLinksPlatform.instance.onLink.listen((dynamicLinkData) async {
    //   String uri = dynamicLinkData.link.toString();
    //   if (uri.contains('vitio')) {
    //     var db = await SharedPreferences.getInstance();
    //     db.setString('URL_VITIO', 'https://checkout.vitio.com.br/product-offer/${uri.split('vitio_')[1]}');
    //   }
    // }).onError((error) {});
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    if (!kDebugMode && !kIsWeb) {
      //   Smartlook.enableCrashlytics(true);
      //   Smartlook.setupAndStartRecording(SetupOptionsBuilder('39a7510f1be4bc9ff323585eec7c9ed0977a55d8').build());
    }
    var corCinzaGustavo = HexColor.fromHex('#1a1a1a');
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(new FocusNode());
      },
      child: Screenshot(
        controller: GetIt.I.get<ControladorApp>().mainScreenShotController,
        child: Observer(
            key: const Key('main'),
            builder: (_) {
              return MaterialApp(
                  themeMode: _controller.themeMode != null ? (_controller.themeMode ?? ThemeMode.light) : ThemeMode.light,
                  debugShowCheckedModeBanner: false,
                  title: F.title,
                  localizationsDelegates: context.localizationDelegates,
                  supportedLocales: context.supportedLocales,
                  locale: context.locale,
                  darkTheme: ThemeData(
                      //Cor Primaria
                      useMaterial3: true,
                      scaffoldBackgroundColor: const Color(0xff000000),
                      cardColor: const Color(0xff1C1C1E),
                      canvasColor: const Color(0xff2E3133),
                      dividerColor: const Color(0xff424242),
                      dividerTheme: const DividerThemeData(color: Color(0xff424242), thickness: 1),
                      shadowColor: Colors.transparent,
                      bottomSheetTheme: const BottomSheetThemeData(backgroundColor: Colors.transparent),
                      bottomAppBarTheme: BottomAppBarThemeData(color: Colors.grey[600]),
                      dialogTheme: DialogThemeData(
                          shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      )),
                      splashColor: Colors.black,
                      primaryColor: _controller.defaultColor ?? Colors.black,
                      iconTheme: const IconThemeData(
                        color: Color(0xffB4B7BB),
                      ),
                      appBarTheme: AppBarTheme(
                        color: corCinzaGustavo,
                        toolbarTextStyle: GoogleFonts.nunitoSans(fontSize: 20, fontWeight: FontWeight.w500, letterSpacing: 0.15, color: Colors.white),
                        titleTextStyle: GoogleFonts.nunitoSans(fontSize: 12, fontWeight: FontWeight.w400, letterSpacing: 0.4, color: Colors.white),
                      ),
                      colorScheme: ColorScheme.fromSwatch(primarySwatch: Colors.grey).copyWith(
                        surface: const Color(0xff000000),
                      )),
                  theme: ThemeData(
                      useMaterial3: true,
                      scaffoldBackgroundColor: const Color(0xffFFFFFF),
                      cardColor: Colors.white,
                      canvasColor: const Color(0xffF3F3F4),
                      bottomSheetTheme: const BottomSheetThemeData(backgroundColor: Colors.transparent),
                      shadowColor: const Color(0xff90949A).withValues(alpha: 0.25),
                      dialogTheme: DialogThemeData(
                          shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      )),
                      dividerColor: const Color(0xffCFCFCF),
                      dividerTheme: const DividerThemeData(color: Color(0xffDCDDDF), thickness: 1),
                      splashColor: Colors.white,
                      iconTheme: const IconThemeData(
                        color: Color(0xff51555A),
                      ),
                      primaryColor: _controller.defaultColor ?? Colors.black,
                      appBarTheme: AppBarTheme(
                        color: corCinzaGustavo,
                        toolbarTextStyle: GoogleFonts.nunitoSans(fontSize: 20, fontWeight: FontWeight.w500, letterSpacing: 0.15, color: Colors.white),
                        titleTextStyle: GoogleFonts.nunitoSans(fontSize: 12, fontWeight: FontWeight.w400, letterSpacing: 0.4, color: Colors.white),
                      ),
                      colorScheme: ColorScheme.fromSwatch(primarySwatch: Colors.grey).copyWith(surface: const Color(0xffF0F0F0))),
                  navigatorKey: GetIt.I.get<NavigationService>().navigatorKey,
                  navigatorObservers: [
                    FirebaseAnalyticsObserver(analytics: analytics),
                    StuckTrace().navigationObserver,
                    UserNavigationObserver(activityTracker),
                  ],
                  initialRoute: '/NovaSplash',
                  routes: {
                    // '/': (context) => const TelaSplashScreen(),
                    '/NovaSplash': (context) => const NovaTelaSplashScreen(),
                    '/telaConfiguracaoConsumoAgua': (context) => TelaConfiguracaoConsumoAgua(),
                    '/homePageApp': (context) => HomePage(),
                    '/telaVerVideoExercicio': (context) => TelaVerVideoExercicio(),
                    '/buscarClienteApp': (context) => const TelaBuscarClienteApp(),
                    '/detalhesClienteAppUnidades': (context) => const TelaDetalhesClienteAppUnidades(),
                    '/buscarCadastroPorTelefone': (context) => TelaBuscarCadastroPorTelefone(),
                    '/telaUsuariosQueJaLogaram': (context) => TelaUsuariosQueJaLogaram(),
                    '/selecionarUsuarioTelefone': (context) => TelaSelecionarUsuarioTelefone(),
                    '/buscarCadastroPorCPF': (context) => TelaBuscarCadastroPorCPF(), //
                    '/validarSMS': (context) => TelaValidarSMS(),
                    '/loginUsuarioSenha': (context) => TelaLoginUsuarioSenha(),
                    '/resultadoBuscarUsuarioCPF': (context) => TelaResultadoBuscarUsuarioCPF(),
                    '/telaFeed': (context) => TelaFeed(),
                    '/telaDenunciaPost': (context) => TelaDenunciaPost(),
                    '/telaComentariosDoFeed': (context) => TelaComentarioDoFeed(),
                    '/telaDetalhesDaPubliccaao': (context) => const TelaDetalhesDaPublicacao(),
                    '/telaDetalhesDaFicha': (context) => TelaDetalhesDaFicha(),
                    '/telaDetalhesAtividade': (context) => TelaDetalhesAtividade(),
                    '/telaRankingUsuarios': (context) => TelaRankingUsuarios(),
                    '/telaListaAlunos': (context) => TelaListaAlunos(),
                    '/telaConfiguracoes': (context) => TelaConfiguracoes(),
                    '/telaHistoricoExecsAluno': (context) => TelaHistoricoExecsAluno(),
                    '/telaFiltroTreinosModelo': (context) => TelaFiltroTreinosModelo(),
                    '/telaManterTReino': (context) => TelaManterTReino(),
                    '/telaPerfil': (context) => TelaPerfil(),
                    '/telaCadastrarEditarCartao': (context) => TelaCadastrarEditarCartao(),
                    '/telaSobre': (context) => TelaSobre(),
                    '/telaHistoricoExecucao': (context) => TelaHistoricoExecucao(),
                    '/telaConcluirTreino': (context) => TelaConcluirTreino(),
                    '/telaPrepararTreino': (context) => TelaPrepararTreino(),
                    '/telaDetalhesAulaTurma': (context) => TelaDetalhesAulaTurma(),
                    '/telaAulaTurmasProfessor': (context) => TelaAulaTurmasProfessor(),
                    '/telaAulasTurmasProfessorFinalizadas': (context) => const TelaAulasTurmasProfessorFinalizadas(),
                    '/telaAulasTurmas': (context) => TelaAulasTurmas(),
                    '/telaAdicionarAlunoAula': (context) => TelaAdicionarAlunoAula(),
                    '/telaReagendar': (context) => TelaReagendar(),
                    '/telaMinhaConta': (context) => TelaMinhaConta(),
                    '/telaManterDadosContato': (context) => TelaManterDadosContato(),
                    '/telaManterSenhaUsuario': (context) => TelaManterSenhaUsuario(),
                    '/telaContratosAluno': (context) => TelaContratosAluno(),
                    '/telaRenovarContrato': (context) => TelaRenovarContrato(),
                    '/telaContratoFerias': (context) => TelaContratoFerias(),
                    '/telaTrancamentoDeContrato': (context) => TelaTrancamentoDeContrato(),
                    '/telaCreditosContrato': (context) => TelaCreditosContrato(),
                    '/telaConfirmarTrancamento': (context) => TelaConfirmarTrancamento(),
                    '/telaManterCartaoCredito': (context) => TelaManterCartaoCredito(),
                    '/telaChat': (context) => TelaChat(),
                    '/telaConversaChat': (context) => TelaConversaChat(),
                    '/telaContatosChat': (context) => TelaContatosChat(),
                    '/detalhesLive': (context) => DetalhesDaLiveView(),
                    '/DetalheTreinoViewController': (context) => const DetalheTreinoViewController(),
                    '/camera': (context) => CameraPacto(),
                    '/cameraCrop': (context) => const CameraCrop(),
                    '/telaProgressoGraduacaoB': (context) => TelaProgressoGraduacao(),
                    '/telaRegistroFacial': (context) => TelaRegistroFacial(),
                    '/telaHomeFit': (context) => TelaTabHomeFit(),
                    '/telaListaFichas': (context) => TelaListaFichas(),
                    '/telaConfigHomologacao': (context) => TelaConfigHomologacao(),
                    '/telaRespirar': (context) => TelaRespirar(),
                    '/telaFotosRegistroFacial': (_) => TelaFotosRegistroFacial(),
                    '/telaOpcoesDeCompraNovoContrato': (_) => TelaOpcoesDeCompraNovoContrato(),
                    '/telaVisualizarDetalhesDeNovoContrato': (_) => TelaVisualizarDetalhesDeNovoContrato(),
                    '/telaAcessoNegado': (_) => TelaAcessoNegado(),
                    '/telaContratoDoPlano': (_) => TelaContratoDoPlano(),
                    '/telaVerMaisProgramaTreino': (_) => TelaVerMaisProgramaTreino(),
                    '/telaNewCross': (context) => TelaNewCross(),
                    '/postarResultadoWod': (context) => PostarResultadoWod(),
                    '/subTelaCalendarioWod': (context) => const SubTelaCalendarioWod(),
                    '/telaAulasTurmasCalendario': (context) => TelaAulasTurmasCalendario(),
                    '/telaLoginEsqueceuSenha': (context) => TelaLoginEsqueceuSenha(),
                    '/telaConfirmarPresencaAluno': (context) => TelaConfirmarPresencaAluno(),
                    '/telaCadastroWod': (context) => TelaCadastroWod(),
                    '/telaCadastroWodAvancado': (context) => TelaCadastroWodAvancado(),
                    '/telaListaAparelhosWod': (context) => TelaListaAparelhosWod(),
                    '/telaListaWods': (context) => TelaListaWods(),
                    '/telaNotificacoes': (context) => TelaNotificacoes(),
                    '/telaDetalheNovidades': (context) => TelaDetalheNovidades(),
                    '/telaAvaliacaoFisicaPerfilAluno': (context) => TelaAvaliacaoFisicaPerfilAluno(),
                    '/telaAcessoNegadoBodyFit': (_) => TelaAcessoNegadoBodyFit(),
                    '/telaInfoIMC': (_) => TelaInfoIMC(),
                    '/telaFichasEProgramas': (_) => TelaFichasEProgramas(),
                    '/telaFicha': (_) => TelaFicha(),
                    '/telaAdicionarAtividades': (_) => TelaAdicionarAtividades(),
                    '/telaFichaDetalhe': (_) => TelaFichaDetalhe(),
                    '/telaHistoricoAluno': (_) => const TelaHistoricoAluno(),
                    '/telaFiltrosAluno': (_) => const TelaFiltrosAluno(),
                    '/telaDetalhesDoAluno': (_) => const TelaDetalhesDoAluno(),
                    '/telaFiltrosTimeLine': (_) => const TelaFiltrosTimeLine(),
                    '/telaProgramaDetalhe': (_) => TelaProgramaDetalhe(),
                    '/telaPrograma': (_) => TelaPrograma(),
                    '/telaAdicionarFichas': (_) => TelaAdicionarFichas(),
                    '/telaAdicionarProgramaFichaNoAluno': (_) => TelaAdicionarProgramaFichaNoAluno(),
                    '/telaMinhaCarteira': (context) => TelaMinhaCarteira(),
                    '/telaAtividadesSelecionadasParaAdicao': (_) => TelaAtividadesSelecionadasParaAdicao(),
                    '/telaAdicionarProgramas': (_) => TelaAdicionarProgramas(),
                    '/telaHistoricoDeParcela': (_) => TelaHistoricoDeParcela(),
                    '/telaProcessoAlunoVisitante': (_) => TelaProcessoAlunoVisitante(),
                    '/alunoVisitanteSemPremissao': (_) => AlunoVisitanteSemPremissao(),
                    '/telaMinhaCarteiraContratos': (_) => TelaMinhaCarteiraContratos(),
                    '/telaParQ': (_) => const TelaParq(),
                    '/telaRenovacaoContrato': (_) => const TelaRenovacaoContrato(),
                    '/telaAcompanhar': (_) => TelaAcompanhar(),
                    '/telaAdicionarAlunoAcompanhar': (_) => TelaAdicionarAlunoAcompanhar(),
                    '/detalhesAlunoAcompanhar': (_) => DetalhesAlunoAcompanhar(),
                    '/telaSelecionarFicha': (_) => TelaSelecionarFicha(),
                    '/telaDashboard': (_) => TelaDashboard(),
                    '/telaSelecaoUnidade': (_) => const TelaSelecaoUnidade(),
                    '/telaSelecaoContrato': (_) => const TelaSelecaoContrato(),
                    '/telaPreLogin': (_) => TelaPreLogin(),
                    '/telaAnamneseOnboardGenero': (_) => const TelaAnamneseOnboardGenero(),
                    '/telaAnamneseOnboardNivel': (_) => const TelaAnamneseOnboardNivel(),
                    '/telaAnamneseOnboardAltura': (_) => const TelaAnamneseOnboardAltura(),
                    '/telaAnamneseOnboardIdade': (_) => const TelaAnamneseOnboardIdade(),
                    '/telaAnamneseOnboardPeso': (_) => const TelaAnamneseOnboardPeso(),
                    '/telaAnamneseOnboardAtividade': (_) => const TelaAnamneseOnboardAtividade(),
                    '/telaAnamneseOnboardTempoPorDia': (_) => const TelaAnamneseOnboardTempoPorDia(),
                    '/telaAnamneseOnboardObjetivo': (_) => const TelaAnamneseObjetivo(),
                    '/telaAnamneseOnboardExperienciaTreino': (context) => const TelaAnamneseOnboardExperienciaTreino(),
                    '/telaAnamneseInicio': (_) => const TelaAnamnese(),
                    '/telaAnamneseRestricoes': (_) => const TelaAnamneseRestricoes(),
                    '/telaAnamneseRestricaoBloqueio': (_) => const TelaAnamneseRestricaoBloqueio(),
                    '/telaMeusContratos': (_) => TelaMeusContratos(),
                    '/telaPerfilAluno': (_) => TelaPerfilAluno(),
                    '/formDadosDeCartao': (_) => FormDadosDeCartao(),
                    '/formResumo': (_) => FormResumo(),
                    '/novaTelaAgendaAluno': (_) => NovaTelaAgendaAluno(),
                    '/telaTodosTreinoEmCasa': (_) => TelaTodosTreinoEmCasa(),
                    '/telaDetalhesDoWodNova': (_) => TelaDetalhesDoWodNova(),
                    '/novoHistoricoCross': (_) => const TelaWodsPorDia(),
                    '/novaTelaPR': (_) => const NovaTelaAtividadesCross(),
                    '/novaTelaDetalhesAtividadeCross': (_) => const NovaTelaDetalhesAtividadeCross(),
                    '/telaCronometro': (_) => const TelaCronometro(),
                    '/telaEmom': (_) => const TelaEmom(),
                    '/telaTabata': (_) => const TelaTabata(),
                    '/telaDSCircularIndicator': (_) => const TelaTreinoConcluidoPercentual(),
                    '/tirarFotoComparacao': (_) => TirarFotoComparacao(),
                    '/resumoFotosAvaliacao': (_) => ResumoFotosAvaliacao(),
                    '/itemCompararFotos': (_) => ItemCompararFotos(),
                    '/visualizarTodasFotos': (_) => VisualizarTodasFotos(),
                    '/telaCompartilhamentoResultados': (_) => const TelaCompartilhamentoResultados(),
                    '/telaCompartilhamentoResultadosScreen': (_) => const TelaCompartilhamentoResultadosSreenShot(),
                    '/telaContratoTermos': (_) => const TelaContratoTermos(),
                    '/telaContratoBloqueioAssinatura': (_) => const TelaContratoBloqueioAssinatura(),
                    '/telaContratoAssinatura': (_) => const TelaContratoAssinatura(),
                    '/telaTreinoDetalheFicha': (_) => const TelaTreinoDetalheFicha(),
                    '/telaTreinoExecucao': (_) => const TelaTreinoExecucao(),
                    '/telaTreinoPausa': (_) => const TelaTreinoPausa(),
                    '/telaTreinoConfiguracoes': (_) => const TelaTreinoConfiguracoes(),
                    '/telaTreinoConcluido': (_) => const TelaTreinoConcluido(),
                    '/telaTreinoConcluidoCompartilhamento': (_) => const TelaTreinoConcluidoCompartilhamento(),
                    '/telaTreinoConcluidoCompartilhamentoFoto': (_) => const TelaTreinoConcluidoCompartilhamentoFoto(),
                    '/telaTreinoDetalheAtividade': (_) => const TelaTreinoDetalheAtividade(),
                    '/telaTreinoDetalheAtividadeAcompanhar': (_) => const TelaTreinoDetalheAtividadeAcompanhamento(),
                    '/telaReportarProblema': (_) => const TelaReportarProblema(),
                    '/telaTermosPoliticas': (_) => TelaTermosPoliticas(),
                    '/itemPrimeiroAcessoPostural': (context) => ItemPrimeiroAcessoPostural(),
                    '/telaPerfilProfessor': (_) => TelaPerfilProfessor(),
                    '/telaNovoQRCode': (context) => const TelaNovoQRCode(),
                    '/telaConfirmarFerias': (context) => TelaConfirmarFerias(),
                    '/telaSubstituirFoto': (context) => TelaSubstituirFoto(),
                    '/telaRevisaoProgramasIA': (context) => const TelaRevisaoProgramasIA(),
                    '/telaTreinosAguardandoRevisaoIA': (context) => TelaTreinosAguardandoRevisaoIA(),
                    '/telaRevisaoFichaIA': (context) => const TelaRevisaoFichaIA(),
                    '/telaNovoRecord': (context) => const TelaNovoRecord(),
                    '/telaFinalizacaoCadastro': (context) => const TelaFinalizacaoCadastro(),
                    '/telaFinalizacaoCadastroReconhecimentoFacial': (context) => const TelaFinalizacaoCadastroReconhecimentoFacial(),
                    '/telaSelecaoMetodoLogin': (context) => const TelaSelecaoMetodoLogin(),
                    '/telaLoginUsuarioTelefone': (context) => const TelaLoginUsuarioTelefone(),
                    '/telaVisualizarUsuariosRecentes': (context) => const TelaVisualizarUsuariosRecentes(),
                    '/telaValidarLoginUsuario': (_) => const TelaValidarLoginUsuario(),
                    '/telaValidarLoginTelefone': (_) => const TelaValidarLoginTelefone(),
                    '/telaVerUsuariosAssociados': (_) => const TelaVerUsuariosAssociados(),
                    '/telaVincularEmailApple': (_) => const TelaVincularEmailApple(),
                    '/telaVerCentralDeAjuda': (_) => const TelaVerCentralDeAjuda(),
                    '/telaBloqueioColaboradorProfessor': (context) => const TelaBloqueioColaboradorProfessor(),
                    '/telaConfiguracoesPushProfIa': (_) => const TelaConfiguracoesPushProfIa(),
                    '/telaNovaBioimpedancia': (context) => const TelaNovaBioimpedancia(),
                    '/telaConectarBalanca': (context) => const TelaConectarBalanca(),
                    '/telaClienteAppDesativado': (_) => const TelaClienteAppDesativado(),
                    '/telaHistoricoAulas': (context) => const TelaHistoricoAulas(),
                    '/telaNutricaoDetalhesFitstream': (context) => TelaNutricaoDetalhesFitstream(),
                    '/telaNutricaoCriacaoPlanoFitstream': (context) => const TelaNutricaoCriacaoPlanoFitstream(),
                    '/telaNutricaoVideoDetalheFitstream': (context) => const TelaNutricaoVideoDetalheFitstream(),
                    '/ActiveChatsScreen': (context) => ActiveChatsScreen(),
                    '/chatScreen': (context) => ChatScreen(),
                    '/ChatAdicionarFoto': (context) => const ChatAdicionarFoto(),
                    '/ChatMidiasEnvidas': (context) => const ChatMidiasEnvidas(),
                    '/telaPagamentoParcelas': (context) => const TelaPagamentoParcelas(),
                    '/telaClubeVantagens': (context) => const TelaClubeVantagens(),
                    '/telaFichasVerMais': (context) => const TelaFichasVerMais(),
                    '/telaHistoricoDeTreinos': (context) => const TelaHistoricoDeTreinos(),
                    '/telaHistoricoDeTreinosAdicionarNovo': (context) => const TelaHistoricoDeTreinosAdicionarNovo(),
                    '/telaHistoricoDeTreinosAdicionarNovoSelecionarExercicio': (context) => const TelaHistoricoDeTreinosAdicionarNovoSelecionarExercicio(),
                    '/telaBloqueioColaboradorInativo': (context) => const TelaBloqueioColaboradorInativo(),
                    '/telaManterTreinoOnline':(context) => TelaManterTreinoOnline(),
                    '/telaManterTreinoAoVivo':(context) => CadastroLivePassoUm(),
                    '/telaEtapasBalanca':(context) => const TelaEtapasBalanca(),
                    '/telaResultadoBioempedancia':(context) => const TelaResultadoBioempedancia(),    
                    '/telaAvaliacaoProfesoresLista':(context) => const TelaAvaliacaoProfesoresLista(),    
                    '/telaDetalheDaAulaTurmaConfirmarPresenca':(context) => const TelaDetalheDaAulaTurmaConfirmarPresenca(),     
                    '/telaLocacao':(context) => const TelaLocacao(),     
                    '/telaLocacaoDetalhes':(context) => const TelaLocacaoDetalhes(),   
                    '/telaMeuLink':(context) => const TelaMeuLink(),     
                    '/telaMeuLinkWebView':(context) => const TelaMeuLinkWebView(), 
                    '/telaContratoAditivos':(context) => const TelaContratoAditivos(), 
                    '/telaAuntenticarAssinaturaContratoAditivo' : (context) => const TelaAuntenticarAssinaturaContratoAditivo(),
                    '/telaCodigoAutenticacao': (context) => const TelaCodigoAutenticacao(),
                    '/telaTransferirCredito':(context) => const TelaTransferirCreditotreino(),
                    '/telaAtividadesPersonalRecords':(context) => const TelaAtividadesPersonalRecords(),
                    '/telaDetalhesPR':(context) => const TelaDetalhesPR(),
                    '/telaCompartilharAulaSelfLoops':(context) => const TelaCompartilharAulaSelfLoops(),
                    '/telaCancelarContrato':(context) => const TelaCancelarContrato()
                  });
            }),
      ),
    );
  }

  @override
  FutureOr<void> afterFirstLayout(BuildContext context) {
    View.of(context).platformDispatcher.onPlatformBrightnessChanged = () {
      _controller.setThemeModeSystem();
    };
  }
}
