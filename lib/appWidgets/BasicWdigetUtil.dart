import 'dart:io';
import 'dart:typed_data';
import 'dart:ui';
import 'package:app_treino/appWidgets/formWidgets/TextFieldPadrao.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/config/TextUtil.dart';
import 'package:app_treino/config/personal_icon_icons.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/model/util/UtilColor.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flare_flutter/flare_actor.dart';
import 'package:get_it/get_it.dart';
import 'package:image/image.dart' as imageConverter;
import 'package:app_treino/Utilitario.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:app_treino/appWidgets/componentWidgets/TextWidgets.dart';

class BasicWdigetUtil {
  static void showShareNormalCom(BuildContext context, {required String titulo, String textoShare = '', String? telefone, bool whatsapp = false, bool instagram = false}) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (_) => CardPadrao(
              margin: const EdgeInsets.all(16),
              elevation: 10,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                child: Wrap(
                  children: <Widget>[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        TextBody2(
                          '$titulo',
                          textAlign: TextAlign.start,
                        ),
                        IconButton(
                            icon: Icon(
                              Icons.cancel,
                              color: Theme.of(context).iconTheme.color,
                            ),
                            onPressed: () => Navigator.pop(context))
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: <Widget>[
                        whatsapp
                            ? Column(
                                children: <Widget>[
                                  IconButton(
                                      icon: const FaIcon(
                                        FontAwesomeIcons.whatsapp,
                                        color: Colors.green,
                                      ),
                                      onPressed: () async {
                                        UtilitarioApp()
                                            .launchWhatsApp(phone: '55$telefone'.replaceAll(' ', '').replaceAll('(', '').replaceAll(')', '').replaceAll('-', ''), message: textoShare);
                                        Navigator.pop(context);
                                      }),
                                  TextOverLine2(
                                    'Whatsapp',
                                  )
                                ],
                              )
                            : Container(),
                        instagram
                            ? Column(
                                children: <Widget>[
                                  IconButton(
                                      icon: const FaIcon(
                                        FontAwesomeIcons.instagram,
                                        color: Colors.pink,
                                      ),
                                      onPressed: () async {
                                        Navigator.pop(context);
                                      }),
                                  TextOverLine2('Instagram')
                                ],
                              )
                            : Container(),
                        Column(
                          children: <Widget>[
                            IconButton(
                                icon: const FaIcon(FontAwesomeIcons.plus),
                                onPressed: () async {
                                  Share.share(textoShare);
                                  Navigator.pop(context);
                                }),
                            TextOverLine2('Outros')
                          ],
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ));
  }
  
 Future<void> escolherFotoDiretoDaGaleria(BuildContext context,
      {required Function(Uint8List imageByes) sucesso, Function()? falha, cropCircle = false, cropSquare = false, double? widthResize, double? heightResize}) async {
    var skdMaiorQue33 = false;
    if (Platform.isAndroid) {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      AndroidDeviceInfo androidInfo;
      androidInfo = await deviceInfo.androidInfo;
      skdMaiorQue33 = androidInfo.version.sdkInt >= 33;
    }
    await UtilitarioApp.validarPermissoes(permissionsRequested: skdMaiorQue33 && Platform.isAndroid ?  
    [Permission.camera, Permission.microphone, Permission.photos, Permission.videos] : 
    [Permission.camera, Permission.storage, Permission.microphone],
      onPermissoesLiberadas: () async {
        final picker = ImagePicker();
        var pickedFile = await picker.pickImage(source: ImageSource.gallery);
        
        if (pickedFile == null) {
          falha?.call();
          return;
        }
        var file = File(pickedFile.path);
        var image = imageConverter.decodeImage(file.readAsBytesSync());
        if (image!.width > (widthResize ?? 1200) && image.height > (heightResize ?? 736)) {
          Directory tempDir = await getApplicationDocumentsDirectory();
          String tempPath = tempDir.path;
          file = await file.copy('$tempPath/temp.png');
          file.writeAsBytesSync(imageConverter
              .encodePng(imageConverter.copyResize(image, width: (widthResize ?? image.width ~/ 4 as double?)!.toInt(), height: (heightResize ?? image.height ~/ 4 as double?)!.toInt())));
          // image = null;
        }
        if (cropCircle || cropSquare) {
          final cropped = await Navigator.pushNamed(context, '/cameraCrop', arguments: {'foto': file.path, 'vaiTerFiltro': false});
          /* UtilitarioApp().cropImage(file, cropCircle, context); */
          sucesso(imageConverter.encodePng(imageConverter.decodeImage(File(cropped as String).readAsBytesSync())!));
        } else {
          sucesso(imageConverter.encodePng(imageConverter.decodeImage(file.readAsBytesSync())!));
        }
      },
      onPermissoesNegadas: () {
        DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Ops!', subtitulo: 'acesso_camera_microfone', tituloBotao: 'Ok');       
      },
      onPermissoesPermanentementeNegadas: () {
        DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Ops!', subtitulo: 'acesso_camera_microfone', tituloBotao: 'Ok');     
      },
    );
  }

  void showDialogComCloseAposAcao(BuildContext context,
      {String? titulo, String? mensagem, String? imagePath, String? textoAcaoPrincipal, Size? imageSize, Function()? onTapMain, Function()? onTapClose}) {
    bool podeMostrarBotaoClose = false;
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => StatefulBuilder(
              builder: (context, setState) => SizedBox(
                width: double.maxFinite,
                height: double.maxFinite,
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 6.0, sigmaY: 6.0),
                  child: PopScope(
                    canPop: false,
                    child: AlertDialog(
                      content: Wrap(
                        children: <Widget>[
                          podeMostrarBotaoClose
                              ? IconButton(
                                  icon: const Icon(Icons.close),
                                  onPressed: () {
                                    Navigator.pop(context);
                                    onTapClose!();
                                  })
                              : Container(),
                          Center(
                            child: titulo != null
                                ? TextHeadLine2(
                                    titulo,
                                    textAlign: TextAlign.center,
                                  )
                                : Container(),
                          ),
                          Center(
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Image.asset(
                                imagePath!,
                                height: imageSize != null ? imageSize.height : null,
                                width: imageSize != null ? imageSize.width : null,
                              ),
                            ),
                          ),
                          mensagem != null && mensagem.isNotEmpty
                              ? TextBody1(
                                  mensagem,
                                  textAlign: TextAlign.center,
                                )
                              : Container(
                                  height: 1,
                                ),
                          const SizedBox(
                            height: 16,
                          ),
                        ],
                      ),
                      actions: <Widget>[
                        BotaoPrimario(
                          textStyle: TextUtil.styleBotaoPrimario(),
                          onTap: () {
                            setState(() {
                              podeMostrarBotaoClose = true;
                              onTapMain!();
                            });
                          },
                          value: textoAcaoPrincipal,
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ));
  }

  void showDialogInput(BuildContext context,
      {String? titulo, Function(bool postive, String text)? callback, String? textoAcaoPrincipal, String? hintText, TextInputType? inputType, Color? corBotaoPrincipal}) {
    TextEditingController _c = TextEditingController();
    showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext _) {
          return SizedBox(
            width: double.maxFinite,
            height: double.maxFinite,
            child: AlertDialog(
              title: TextHeader(
                titulo ?? '',
                textAlign: TextAlign.center,
              ),
              content: Wrap(
                children: <Widget>[
                  TextFieldPadrao(
                    controlller: _c,
                    hintText: hintText,
                    inputType: inputType,
                  ),
                ],
              ),
              actions: [
                SizedBox(
                  width: double.maxFinite,
                  child: Row(
                    children: [
                      Expanded(
                        child: BotaoPrimario(
                            textStyle: TextUtil.styleBotaoPrimario(),
                            corDefault: corBotaoPrincipal,
                            value: textoAcaoPrincipal ?? localizedString('continue'),
                            onTap: () {
                              Navigator.pop(context);
                              callback!(true, _c.text);
                            }),
                      ),
                      const SizedBox(
                        width: 8,
                      ),
                      Expanded(
                        child: BotaoSecundario(
                            value: localizedString('cancel'),
                            onTap: () {
                              Navigator.pop(context);
                              callback!(false, '');
                            }),
                      )
                    ],
                  ),
                )
              ],
            ),
          );
        });
  }

  void showDialogMensagem(BuildContext context,
      {String? titulo,
      String? mensagem,
      String? imgAsset,
      Function(bool postive)? callback,
      String? textoAcaoPrincipal,
      String? textoAcaoSecundaria,
      bool? apenasAcaoPrimaria,
      bool permitirPop = true,
      List<Widget>? acoes}) {
    apenasAcaoPrimaria = apenasAcaoPrimaria != null ? apenasAcaoPrimaria : false;
    showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext _) {
          return PopScope(
            canPop: permitirPop,
            child: SizedBox(
              width: double.maxFinite,
              height: double.maxFinite,
              child: ClipRect(
                // <-- clips to the 200x200 [Container] below
                child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 6.0, sigmaY: 6.0),
                    child: AlertDialog(
                      title: (titulo?.isNotEmpty ?? false)
                          ? DStextTitle2(
                              titulo ?? '',
                              textAlign: TextAlign.center,
                            )
                          : Container(),
                      content: Wrap(
                        children: <Widget>[
                          imgAsset != null && imgAsset.isNotEmpty ? Center(child: Image.asset(imgAsset)) : Container(),
                          mensagem != null && mensagem.isNotEmpty
                              ? Center(
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: DStextBody(
                                      mensagem,
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                )
                              : Container(
                                  height: 1,
                                )
                        ],
                      ),
                      actions: [
                        acoes != null
                            ? Column(
                                children: acoes
                                    .map((e) => Padding(
                                          padding: const EdgeInsets.only(bottom: 16),
                                          child: e,
                                        ))
                                    .toList(),
                              )
                            : callback != null
                                ? SizedBox(
                                    width: double.maxFinite,
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: DSbotaoPadrao(
                                              titulo: textoAcaoPrincipal ?? localizedString('continue'),
                                              onTap: () {
                                                try {
                                                  Navigator.pop(context);
                                                } catch (e) {
                                                  GetIt.I.get<NavigationService>().goBack();
                                                }
                                                callback.call(true);
                                              }),
                                        ),
                                        const SizedBox(
                                          width: 8,
                                        ),
                                        !apenasAcaoPrimaria!
                                            ? Expanded(
                                                child: DSbotaoPadrao(
                                                    tipoBotao: TipoBotao.secundario,
                                                    titulo: textoAcaoSecundaria ?? localizedString('cancel'),
                                                    onTap: () {
                                                      try {
                                                        Navigator.pop(context);
                                                      } catch (e) {
                                                        GetIt.I.get<NavigationService>().goBack();
                                                      }
                                                      callback(false);
                                                    }),
                                              )
                                            : const SizedBox(),
                                      ],
                                    ),
                                  )
                                : DSbotaoPadrao(
                                    onTap: () {
                                      try {
                                        Navigator.pop(context);
                                      } catch (e) {
                                        GetIt.I.get<NavigationService>().goBack();
                                      }
                                    },
                                    titulo: 'OK',
                                  ),
                      ],
                    )),
              ),
            ),
          );
        });
  }

  void showDialogMensagemDS(BuildContext context,
      {String? titulo,
      String? mensagem,
      String? imgAsset,
      Function(bool postive)? callback,
      String? textoAcaoPrincipal,
      String? textoAcaoSecundaria,
      bool? apenasAcaoPrimaria,
      bool permitirPop = true,
      List<Widget>? acoes}) {
    apenasAcaoPrimaria = apenasAcaoPrimaria != null ? apenasAcaoPrimaria : false;
    showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext _) {
          return PopScope(
            canPop: permitirPop,
            child: SizedBox(
              width: double.maxFinite,
              height: double.maxFinite,
              child: ClipRect(
                // <-- clips to the 200x200 [Container] below
                child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 6.0, sigmaY: 6.0),
                    child: AlertDialog(
                      title: (titulo?.isNotEmpty ?? false)
                          ? DStextHeadline(
                              titulo ?? '',
                              eHeavy: true,
                              textAlign: TextAlign.center,
                            )
                          : Container(),
                      content: Wrap(
                        children: <Widget>[
                          imgAsset != null && imgAsset.isNotEmpty ? Center(child: Image.asset(imgAsset)) : Container(),
                          mensagem != null && mensagem.isNotEmpty
                              ? Center(
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: DStextBody(
                                      mensagem,
                                      eHeavy: false,
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                )
                              : Container(
                                  height: 1,
                                )
                        ],
                      ),
                      actions: [
                        acoes != null
                            ? Column(
                                children: acoes
                                    .map((e) => Padding(
                                          padding: const EdgeInsets.only(bottom: 16),
                                          child: e,
                                        ))
                                    .toList(),
                              )
                            : callback != null
                                ? SizedBox(
                                    width: double.maxFinite,
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: DSbotaoPadrao(
                                              titulo: textoAcaoPrincipal ?? localizedString('continue'),
                                              onTap: () {
                                                try {
                                                  Navigator.pop(context);
                                                } catch (e) {
                                                  GetIt.I.get<NavigationService>().goBack();
                                                }
                                                callback.call(true);
                                              }),
                                        ),
                                        const SizedBox(
                                          width: 8,
                                        ),
                                        !apenasAcaoPrimaria!
                                            ? Expanded(
                                                child: DSbotaoPadrao(
                                                    tipoBotao: TipoBotao.secundario,
                                                    titulo: textoAcaoSecundaria ?? localizedString('cancel'),
                                                    onTap: () {
                                                      try {
                                                        Navigator.pop(context);
                                                      } catch (e) {
                                                        GetIt.I.get<NavigationService>().goBack();
                                                      }
                                                      callback(false);
                                                    }),
                                              )
                                            : const SizedBox(
                                                height: 1,
                                                width: 1,
                                              ),
                                      ],
                                    ),
                                  )
                                : BotaoPrimario(
                                    onTap: () {
                                      try {
                                        Navigator.pop(context);
                                      } catch (e) {
                                        GetIt.I.get<NavigationService>().goBack();
                                      }
                                    },
                                    value: 'OK',
                                  ),
                      ],
                    )),
              ),
            ),
          );
        });
  }

  void showDialogErroCompraPlano() {
    var context = GetIt.I.get<NavigationService>().context;
    showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext _) {
          return PopScope(
           canPop: false,
            child: SizedBox(
              width: double.maxFinite,
              height: double.maxFinite,
              child: ClipRect(
                // <-- clips to the 200x200 [Container] below
                child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 6.0, sigmaY: 6.0),
                    child: AlertDialog(
                      titlePadding: const EdgeInsets.all(16),
                      contentPadding: const EdgeInsets.all(16),
                      title: Align(
                        alignment: Alignment.topRight,
                        child: GestureDetector(
                          child: const Icon(
                            Icons.close,
                            size: 20,
                          ),
                          onTap: () {
                            Navigator.pop(context);
                          },
                        ),
                      ),
                      content: Wrap(
                        children: <Widget>[
                          Center(
                              child: Image.asset(
                            'assets/images/empty/empty_erro.png',
                            height: 144,
                            width: 144,
                          )),
                          const SizedBox(
                            height: 24,
                            width: double.maxFinite,
                          ),
                          Center(
                              child: TextHeadLine1(
                            'ops_there_has_been_a_problem',
                            textAlign: TextAlign.center,
                          )),
                          const SizedBox(
                            height: 24,
                            width: double.maxFinite,
                          ),
                          TextBody1('your_contract_has_not_been_activated', textAlign: TextAlign.center),
                          const SizedBox(
                            height: 32,
                            width: double.maxFinite,
                          ),
                          Center(
                            child: BotaoPrimario(
                              textStyle: TextUtil.styleBotaoPrimario(),
                              onTap: () {
                                Navigator.pop(context);
                              },
                              value: localizedString('go_back'),
                              fullWidth: false,
                            ),
                          ),
                          const SizedBox(
                            height: 39,
                            width: double.maxFinite,
                          ),
                        ],
                      ),
                    )),
              ),
            ),
          );
        });
  }

  void showSucessoCompraPlano() {
    var context = GetIt.I.get<NavigationService>().context;
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        Future.delayed(const Duration(seconds: 2)).then((value) => Navigator.pop(context));
        return Container(
          alignment: Alignment.topCenter,
          child: CardPadrao(
            margin: const EdgeInsets.fromLTRB(16, 32, 16, 16),
            color: HexColor.fromHex('51555A'),
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(
                  PersonalIcon.check_circle,
                  color: Colors.white,
                ),
                const SizedBox(
                  width: 16,
                ),
                TextBody1(
                  'plan_activated_successfully',
                  corLighMode: Colors.white,
                )
              ],
            ),
          ),
        );
      },
    );
  }

  void showSucessoAdicionarAluno(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        Future.delayed(const Duration(milliseconds: 2500)).then((value) => Navigator.pop(context));
        return Container(
          alignment: Alignment.topCenter,
          child: Container(
            margin: const EdgeInsets.fromLTRB(16, 32, 16, 16),
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              color: DSLib.theme ==  ThemeMode.dark
                ? const Color(0xff202020)
                : const Color(0xffFFFFFF),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xff34C759), width: 1.5)
            ),
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(
                  TreinoIcon.check_circle,
                  color: Color(0xff34C759),
                ),
                const SizedBox(
                  width: 16,
                ),
                DStextCaption1(localizedString('acompanhar.aluno_adicionado_sucesso'), eHeavy: true,)
              ],
            ),
          ),
        );
      },
    );
  }

  void showSucessoExcluirAluno() {
    var context = GetIt.I.get<NavigationService>().context;
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        Future.delayed(const Duration(milliseconds: 2500)).then((value) => Navigator.pop(context));
        return Container(
          alignment: Alignment.topCenter,
          child: Container(
            margin: const EdgeInsets.fromLTRB(16, 32, 16, 16),
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              color: DSLib.theme ==  ThemeMode.dark
                ? const Color(0xff202020)
                : const Color(0xffFFFFFF),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xff34C759), width: 1.5)
            ),
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(
                  TreinoIcon.check_circle,
                  color: Color(0xff34C759),
                ),
                const SizedBox(
                  width: 16,
                ),
                DStextCaption1('acompanhar.aluno_excluido_sucesso', eHeavy: true,)
              ],
            ),
          ),
        );
      },
    );
  }

  static showModalComImagemEBorderedButton(BuildContext context,
      {required String assetImagePath,
      double? imageHeight,
      required String headerText,
      required String bodyText,
      String? subtitleText,
      Function()? onConfirm,
      Function()? onPop,
      String? confirmText,
      String? popText}) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext) {
          return StatefulBuilder(builder: (_, setState) {
            return Dialog(
              child: CardPadrao(
                  margin: EdgeInsets.zero,
                  radius: 10,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 32.0),
                          child: SizedBox(height: imageHeight ?? 120, child: Image.asset(assetImagePath)),
                        ),
                        TextHeader(
                          headerText,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        TextBody1(
                          bodyText,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(
                          height: 24.0,
                        ),
                        Visibility(
                          visible: subtitleText?.isNotEmpty ?? false,
                          child: Column(
                            children: [
                              TextOverLine1(
                                subtitleText,
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(
                                height: 32,
                              ),
                            ],
                          ),
                        ),
                        BotaoPrimario(
                          onTap: () {
                            onConfirm?.call();
                          },
                          value: confirmText ?? localizedString('confirm'),
                        ),
                        const SizedBox(
                          height: 16,
                        ),
                        BotaoPrimario(
                            onTap: () {
                              onPop?.call();
                            },
                            value: popText ?? localizedString('cancel'),
                            bordered: true,
                            textColor: Theme.of(context).primaryColor,
                            corDefault: Theme.of(context).cardColor),
                        const SizedBox(
                          height: 16,
                        )
                      ],
                    ),
                  )),
            );
          });
        });
  }

  static showModalComIconEAcoes(BuildContext context,
      {required TipoAlerta tipoAlerta, required String headerText, required String bodyText, Function()? onConfirm, Function()? onCancel, String? confirmText, String? popText}) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext) {
          return StatefulBuilder(builder: (_, setState) {
            return Dialog(
              child: CardPadrao(
                  margin: EdgeInsets.zero,
                  radius: 10,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(
                          height: 16,
                        ),
                        SizedBox(
                            width: 150,
                            height: 150,
                            child: FlareActor(
                                tipoAlerta == TipoAlerta.alerta
                                    ? 'assets/gifs/pct-alert.flr'
                                    : tipoAlerta == TipoAlerta.sucesso
                                        ? 'assets/gifs/pct-sucess.flr'
                                        : 'assets/gifs/pct-error.flr',
                                alignment: Alignment.center,
                                fit: BoxFit.cover,
                                animation: 'start')),
                        const SizedBox(
                          height: 32,
                        ),
                        TextHeader(
                          headerText,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        TextBody1(
                          bodyText,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(
                          height: 32.0,
                        ),
                        SizedBox(
                          width: MediaQuery.of(context).size.width * 0.32,
                          child: BotaoPrimario(
                            onTap: () {
                              onConfirm?.call();
                            },
                            value: confirmText ?? localizedString('confirm'),
                          ),
                        ),
                        const SizedBox(
                          height: 16,
                        ),
                        SizedBox(
                          width: MediaQuery.of(context).size.width * 0.32,
                          child: BotaoPrimario(
                              onTap: () {
                                onCancel?.call();
                              },
                              value: popText ?? localizedString('cancel'),
                              bordered: true,
                              textColor: Theme.of(context).primaryColor,
                              corDefault: Theme.of(context).cardColor),
                        ),
                        const SizedBox(
                          height: 16,
                        )
                      ],
                    ),
                  )),
            );
          });
        });
  }

  static showModalComIconEAcao(BuildContext context, {TipoAlerta? tipoAlerta, required String headerText, Function()? onConfirm, String? confirmText}) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext) {
          return StatefulBuilder(builder: (_, setState) {
            return Dialog(
              child: CardPadrao(
                  margin: EdgeInsets.zero,
                  radius: 10,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(
                          height: 16,
                        ),
                        SizedBox(
                            width: 150,
                            height: 150,
                            child: FlareActor(
                                tipoAlerta == TipoAlerta.alerta
                                    ? 'assets/gifs/pct-alert.flr'
                                    : tipoAlerta == TipoAlerta.sucesso
                                        ? 'assets/gifs/pct-sucess.flr'
                                        : 'assets/gifs/pct-error.flr',
                                alignment: Alignment.center,
                                fit: BoxFit.cover,
                                animation: 'start')),
                        const SizedBox(
                          height: 32,
                        ),
                        DStextHeadline(
                          headerText,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(
                          height: 32,
                        ),
                        SizedBox(
                          width: MediaQuery.of(context).size.width * 0.32,
                          child: DSbotaoPadrao(
                              onTap: () {
                                onConfirm?.call();
                              },
                              titulo: confirmText ?? 'Ok'),
                        ),
                        const SizedBox(
                          height: 16,
                        )
                      ],
                    ),
                  )),
            );
          });
        });
  }

  showModalAddObservacao(BuildContext context, {Function(String observacao)? onAdd, String? valueOnTextfield}) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext) {
          // ignore: unused_local_variable
          String? observacao;
          return StatefulBuilder(builder: (_, setState) {
            return Dialog(
                backgroundColor: Colors.transparent,
                insetPadding: const EdgeInsets.all(10),
                child: SizedBox(
                    child: CardPadrao(
                  radius: 8,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          GestureDetector(
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                            child: const Padding(
                              padding: EdgeInsets.only(right: 16.0, top: 16.0),
                              child: Icon(
                                PersonalIcon.x,
                                size: 20,
                              ),
                            ),
                          )
                        ],
                      ),
                      const SizedBox(
                        height: 4,
                      ),
                      TextHeadLine1('remark'),
                      const SizedBox(
                        height: 16,
                      ),
                      Divider(height: 0, color: Theme.of(context).dividerColor),
                      const SizedBox(
                        height: 20,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: TextFieldPadrao(
                          valorNoCampo: valueOnTextfield,
                          onChanged: (text) {
                            observacao = text;
                          },
                          hintText: localizedString('escreva_sua_obs'),
                          maxLines: 3,
                          minLines: 3,
                          contentPaddingTop: 10,
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      const SizedBox(
                        height: 24,
                      ),
                      Divider(
                        color: Theme.of(context).dividerColor,
                        height: 0,
                      ),
                      const SizedBox(
                        height: 24,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          GestureDetector(
                              onTap: () {
                                onAdd?.call(observacao ?? valueOnTextfield ?? '');
                              },
                              child: TextButtonApp(
                                'add',
                                customColor: Theme.of(context).primaryColor,
                              ))
                        ],
                      ),
                      const SizedBox(
                        height: 24,
                      )
                    ],
                  ),
                )));
          });
        });
  }

  showModalAddObservacaoCardBottomUp(
    BuildContext context, {
    Widget? widgetStart,
    Function(String observacao)? onAdd,
    String? valueOnTextfield,
  }) {
    showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        builder: (dialogContext) {
          String? observacao;
          return StatefulBuilder(builder: (_, setState) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SingleChildScrollView(
                  child: Dialog(
                      backgroundColor: Colors.transparent,
                      insetPadding: const EdgeInsets.all(16),
                      child: SizedBox(
                          child: DScard(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 8, bottom: 8.0, left: 16, right: 16),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(child: widgetStart ?? Container()),
                                  TextHeadLine1('remark'),
                                  Container(
                                    alignment: Alignment.centerRight,
                                    child: InkWell(
                                      onTap: () {
                                        Navigator.of(context).pop();
                                      },
                                      child: Container(
                                        height: 28,
                                        width: 28,
                                        decoration: BoxDecoration(shape: BoxShape.circle, color: Theme.of(context).dividerColor),
                                        child: const Icon(
                                          TreinoIcon.times,
                                          size: 18,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Divider(height: 0, color: Theme.of(context).dividerColor),
                            const SizedBox(
                              height: 23.5,
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16.0),
                              child: TextFieldPadrao(
                                valorNoCampo: valueOnTextfield,
                                onChanged: (text) {
                                  observacao = text;
                                },
                                hintText: localizedString('escreva_sua_obs'),
                                maxLines: 8,
                                minLines: 8,
                                contentPaddingTop: 10,
                                fillColor: Theme.of(context).colorScheme.surface,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 24, right: 16.0, left: 16.0, top: 24),
                              child: BotaoPrimario(
                                radius: 14,
                                height: 50,
                                onTap: () {
                                  onAdd?.call(observacao ?? valueOnTextfield ?? '');
                                },
                                value: localizedString('save'),
                              ),
                            ),
                          ],
                        ),
                      ))),
                ),
              ],
            );
          });
        });
  }
}

void showDialogCarregandoRenovacao(BuildContext context) {
  showDialog(
      context: context,
      barrierDismissible: true,
      builder: (_) => BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 6.0, sigmaY: 6.0),
            child: Center(
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Padding(
                  padding: const EdgeInsets.only(left: 16.0, right: 16.0),
                  child: CardPadrao(
                    borderRadius: BorderRadius.circular(12),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        const SizedBox(
                          height: 71,
                        ),
                        SizedBox(
                          height: 81,
                          width: 81,
                          child: CircularProgressIndicator(
                            strokeWidth: 6.5,
                            color: Theme.of(context).primaryColor,
                            backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                          ),
                        ),
                        const SizedBox(
                          height: 40,
                        ),
                        TextHeader('renovando_contrato'),
                        const SizedBox(
                          height: 12,
                        ),
                        TextBody1(
                          'so_um_momento',
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 104)
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ));
}

void showDialogContratoRenovado(BuildContext context) {
  showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 6.0, sigmaY: 6.0),
            child: Center(
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Padding(
                  padding: const EdgeInsets.only(left: 16.0, right: 16.0),
                  child: CardPadrao(
                    borderRadius: BorderRadius.circular(12),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        const SizedBox(
                          height: 24,
                        ),
                        const Icon(
                          PersonalIcon.check_circle,
                          color: Color(0xff2EC750),
                          size: 144,
                        ),
                        const SizedBox(
                          height: 24,
                        ),
                        TextHeader('plano_renovado_com_sucesso'),
                        const SizedBox(
                          height: 12,
                        ),
                        TextBody1(
                          'continue_aproveitando_o_melhor_da_academia',
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 32),
                        BotaoPrimario(
                          onTap: () {
                            Navigator.of(context).popUntil(ModalRoute.withName('/homePageApp'));
                          },
                          value: localizedString('continue'),
                          fullWidth: false,
                        ),
                        const SizedBox(
                          height: 24,
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ));
}
