import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/image_widget.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
class CelulaResumoClienteApp extends StatelessWidget {
  const CelulaResumoClienteApp({
    Key? key,
    required ControladorApp controladorClienteApp,
    required this.clienteApp,
    this.clicavel,
  })  : _controladorClienteApp = controladorClienteApp,
        super(key: key);

  final ControladorApp _controladorClienteApp;
  final ClienteApp? clienteApp;
  final bool? clicavel;

  @override
  Widget build(BuildContext contextx) {
    BuildContext context = GetIt.I.get<NavigationService>().context;
    final bool isClicavel = clicavel ?? false;
    final String nomeApp = clienteApp?.nomeDoApp ?? '';
    final String? urlLogo = clienteApp?.urlLogoPrincipal;
    return InkWell(
      onTap: () {
        analytic(EventosKey.selecione_academia_selecionou_academia);
        if (isClicavel && clienteApp != null) {
          UtilitarioApp().showDialogCarregando(context);
          _controladorClienteApp.consultarClienteAppCompleto(clienteApp!, sucesso: () {
            final selecionado = _controladorClienteApp.mClienteAppSelecionado;
            final empresas = selecionado?.empresaApps;
            if ((empresas?.length ?? 0) > 1) {
              Navigator.of(context).popAndPushNamed('/detalhesClienteAppUnidades');
            } else if ((empresas?.isNotEmpty ?? false)) {
              _controladorClienteApp.setEmpresaSelecionada(empresas!.first, () {
                Navigator.of(context).pop();
                Navigator.of(context).popAndPushNamed(_controladorClienteApp.desabilitarNovoFluxoLogin ? '/buscarCadastroPorTelefone' : '/telaSelecaoMetodoLogin');
              }, (x) {
                Navigator.of(context).pop();
                DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Ops!', subtitulo: x ?? '', tituloBotao: 'got_it');
              });
            } else {
              Navigator.of(context).pop();
              DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Ops!', subtitulo: 'Nenhuma empresa encontrada.', tituloBotao: 'got_it');
            }
          }, falha: () {
            Navigator.of(context).pop();
          });
        }
      },
      child: ListTile(
        contentPadding: const EdgeInsets.only(left: 20, right: 20, top: 6),
        dense: true,
        title: DStextSubheadline(
          UtilitarioApp.sentenseCase(nomeApp),
        ),
        trailing: isClicavel
            ? Icon(TreinoIcon.angle_right,
                color: DSLib.theme == ThemeMode.dark
                    ? const Color(0xffFFFFFF)
                    : const Color(0xff202020))
            : null,
        leading: SizedBox(
          width: 45,
          height: 45,
          child: Stack(
            children: [
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(100),
                    color: DSLib.theme == ThemeMode.dark
                        ? const Color(0xffFFFFFF)
                        : const Color(0xff202020),
                  ),
                ),
              ),
              (urlLogo != null && urlLogo.isNotEmpty)
                  ? Positioned.fill(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(100),
                        child: ImageWidget(
                          imageUrl: urlLogo,
                          fit: BoxFit.fill,
                        ),
                      ),
                    )
                  : Positioned.fill(
                      child: ClipRRect(
                          borderRadius: BorderRadius.circular(100),
                          child: Image.asset(F.logoSquare ?? 'assets/images/default_logo.png')),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
