
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/ValidaCondicoes.dart';
import 'package:app_treino/controlladores/ControladorAulaTurma.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/model/aulaTurma/AulaTurma.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';

class ModalSelecionarEquipamentos {
    void exibirModalSelecaoEquipamento(BuildContext context,AulaTurma  aulaTurma,{Function(String equipamentoSelecionado)? onSelected,Function()? onDone,Function()? onErro}) {
    final _controladorAulaTurma = GetIt.I.get<ControladorAulaTurma>();  
    final ScrollController _controllerScrool = ScrollController();
    String groupValue = '';
      Widget itemPosicaoEquipamento(Function() atualizar) {
    
    Map<String, List<Widget>> posicoes = {
      'A': [],
      'B': [],
      'C': [],
      'D': [],
      'E': [],
      'F': [],
      'G': [],
      'H': [],
    };

    for (final element in _controladorAulaTurma.mListaEquipamentosReservados) {
      String posicao = element[0];
      posicoes[posicao]?.add(
        Semantics(
            identifier: 'selecionar_equipamento',
          child: Padding(
            padding: const EdgeInsets.only(bottom: 16, left: 4, right: 4),
            child: itemEquipamento(
              context: context,
              label: (aulaTurma.apresentarEquipamentoPorNumeracao ?? false) ? _controladorAulaTurma.getNumeradorEquipamento(aulaTurma: aulaTurma, posicao: element) : element,
              sigla: _controladorAulaTurma.getSiglaEquipamento(aulaTurma: aulaTurma, posicao: element),
              icone: _controladorAulaTurma.getIconeEquipamento(aulaTurma: aulaTurma, posicao: element),
              selecionado: groupValue == element,
              reservado: _controladorAulaTurma.aparelhoEstaReservado(element),
              groupValue: groupValue,
              value: element,
              onTap: (value) {
                if(groupValue.isEmpty) {
                  groupValue = value;
                  atualizar.call();
                } else {
                  if(groupValue == value) {
                    groupValue = '';
                    atualizar.call();
                  } else {
                    groupValue = value;
                    atualizar.call();
                  }
                }
              },
            ),
          ),
        ),
      );
    }
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          children: posicoes.entries.map((entry) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: entry.value,
            );
          }).toList(),
        ),
      ),
    );
  }
   ( DSBottomSheet().exibirAlerta(context, _controladorAulaTurma.alunoTemEquipamentoReservado(GetIt.I.get<ControladorCliente>().mDadosDoUsuario?.nome ?? '') ? 'editar_reserva' : 'selecionar_equipamento', StatefulBuilder(builder: (context, setState) {
      return SizedBox(
        height: MediaQuery.of(context).size.height * 0.74,
        child: Scrollbar(
          controller: _controllerScrool,
          thickness: 4, 
          radius: const Radius.circular(20), 
          child: SingleChildScrollView(
            controller: _controllerScrool,
            physics: const BouncingScrollPhysics(),
            child: Column(
              children: [
                SizedBox(
                  width: MediaQuery.of(context).size.width - 64,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(
                        TreinoIcon.info_circle,
                        size: 12.5,
                      ),
                      const SizedBox(
                        width: 4,
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width - 81,
                        child: DStextCaption1(
                          'reserva_aviso',
                          eHeavy: false,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 24,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Theme.of(context).dividerColor),
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              Expanded(
                                  child: Container(
                                height: 1,
                                color: Theme.of(context).dividerColor,
                              )),
                              Container(
                                decoration: BoxDecoration(
                                  color: Theme.of(context).colorScheme.surface,
                                  borderRadius: BorderRadius.circular(24),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  child: DStextCaption1('personal_trainer'),
                                ),
                              ),
                              Expanded(
                                  child: Container(
                                height: 1,
                                color: Theme.of(context).dividerColor,
                              )),
                            ],
                          ),
                        ),
                        itemPosicaoEquipamento(() {
                          setState(() {});
                        }),
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  height: 16,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            radius: 4,
                            backgroundColor: Theme.of(context).primaryColor,
                            child: CircleAvatar(
                              backgroundColor: Theme.of(context).cardColor,
                              radius: 3,
                            ),
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          DStextBody(
                            'disponivel',
                            eHeavy: false,
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: Theme.of(context).primaryColor,
                            radius: 4,
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          DStextBody(
                            'selecionado',
                            eHeavy: false,
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          const CircleAvatar(
                            backgroundColor: Color(0xffD20236),
                            radius: 4,
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          DStextBody(
                            'reservador',
                            eHeavy: false,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 32,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      DStextHeadline('equipamento_selecionado'),
                      Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(24),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                          child: Row(
                            children: [
                              const Icon(
                                TreinoIcon.Kettlebell,
                                size: 16,
                              ),
                              DStextBody(groupValue.isEmpty ? '-' : ((aulaTurma.apresentarEquipamentoPorNumeracao ?? false) ? _controladorAulaTurma.getNumeradorEquipamento(aulaTurma: aulaTurma, posicao: groupValue) : groupValue))
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                const SizedBox(
                  height: 16,
                ),
                ValidaCondicoes(
                  validacaoExtra: _controladorAulaTurma.alunoTemEquipamentoReservado(GetIt.I.get<ControladorCliente>().mDadosDoUsuario?.nome ?? ''),
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                    child: DSbotaoPadrao(
                      //desabilitado: groupValue.isEmpty,
                      titulo: 'cancelar_reserva',
                      tipoBotao: TipoBotao.secundario,
                      onTap: () {
                        _controladorAulaTurma.removerReservaEquipamento(
                          aulaTurma: aulaTurma,
                          equipamento: _controladorAulaTurma.getEquipamentoReservado(GetIt.I.get<ControladorCliente>().mDadosDoUsuario?.nome ?? ''),
                          onLoading: () {
                            UtilitarioApp().showDialogCarregando(context);
                          },
                          onSuccess: () async {
                            Navigator.of(context).pop();
                            Navigator.of(context).pop();
                            Navigator.of(context).pop();                          
                            //wait DSalertaSucesso().exibirAlerta(context: context);
                          },
                          onError: (error) {
                            Navigator.of(context).pop();
                            DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Ops!', subtitulo: error.toString(), tituloBotao: 'got_it');
                          },
                        );
                      },
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: DSbotaoPadrao(
                    desabilitado: groupValue.isEmpty,
                    titulo: _controladorAulaTurma.alunoTemEquipamentoReservado(GetIt.I.get<ControladorCliente>().mDadosDoUsuario?.nome ?? '') ? 'salvar_edicao'  : 'confirmar_reserva',
                    onTap: () {
                        onSelected?.call(groupValue);
                    },
                  ),
                ),
                const SizedBox(
                  height: 24,
                )
              ],
            ),
          ),
        ),
      );
    })) as Future<dynamic>).then((v){
     onDone?.call();
    });
    
  }

    static void reservarEquipamento(String groupValue, BuildContext context, AulaTurma aulaTurma, {Function()? onErro, Function()? onSucesso}) {
    final _controladorAulaTurma = GetIt.I.get<ControladorAulaTurma>();
    if (groupValue.isEmpty) {
      DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Ops!', subtitulo: 'reserva_text', tituloBotao: 'got_it');
    } else {
      _controladorAulaTurma.reservarEquipamento(
        aulaTurma: aulaTurma,
        equipamento: groupValue,
        onLoading: () {
          UtilitarioApp().showDialogCarregando(context);
        },
        onSuccess: () async {
          Navigator.of(context).pop();
          Navigator.of(context).pop();
          onSucesso?.call();
          //await DSalertaSucesso().exibirAlerta(context: context);
        },
        onError: (error) {
          Navigator.of(context).pop();
          onErro?.call();
          DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Ops!', subtitulo: error.toString(), tituloBotao: 'got_it');
        },
      );
    }
  }
 

    Widget itemEquipamento({required BuildContext context, required String label, required String sigla, required String icone, required bool selecionado, required bool reservado, required String groupValue, required String value, Function(String value)? onTap}) {
    return Semantics(
        identifier: 'substituir_equipamento',
      child: InkWell(
        onTap: reservado
            ? null
            : () {
                onTap?.call(value);
              },
        child: Container(
          height: 68,
          width: 68,
          decoration: reservado
              ? BoxDecoration(color: const Color(0xffD20236), border: Border.all(color: const Color(0xffD20236)), shape: BoxShape.circle)
              : BoxDecoration(color: selecionado ? Theme.of(context).primaryColor : Colors.transparent, border: Border.all(color: Theme.of(context).primaryColor), shape: BoxShape.circle),
          child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ValidaCondicoes(
                    validacaoExtra: icone.isNotEmpty,
                    child: SvgPicture.asset('assets/images/icones/reservaEquipamentos/${icone}.svg', colorFilter: ColorFilter.mode(selecionado || reservado ? Colors.white : Theme.of(context).primaryColor, BlendMode.srcATop), height: 24)),
                  DStextCaption1(
                    label,
                    corCustomizada: selecionado || reservado ? Colors.white : Theme.of(context).primaryColor,
                  ),                
                  ValidaCondicoes(
                    validacaoExtra: sigla.isNotEmpty,
                    child: DStextCaption1(
                      sigla,
                      eHeavy: false,
                      corCustomizada: selecionado || reservado ? Colors.white : Theme.of(context).primaryColor,
                    ),
                  ),
                  ],
                )
              ),
          ),
        ),
      ),
    );
  }
}