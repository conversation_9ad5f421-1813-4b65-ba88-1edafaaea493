# Script para iniciar emulador Android de forma segura
Write-Host "🤖 INICIANDO EMULADOR ANDROID SEGURO" -ForegroundColor Green
Write-Host "=" * 50

# Verificar se ADB está funcionando
Write-Host "📱 Verificando ADB..." -ForegroundColor Yellow
$adbPath = "$env:ANDROID_HOME\platform-tools\adb.exe"
if (Test-Path $adbPath) {
    & $adbPath devices
    Write-Host "✅ ADB funcionando" -ForegroundColor Green
} else {
    Write-Host "❌ ADB não encontrado" -ForegroundColor Red
    exit 1
}

# Listar emuladores disponíveis
Write-Host "`n📋 Emuladores disponíveis:" -ForegroundColor Yellow
$emulatorPath = "$env:ANDROID_HOME\emulator\emulator.exe"
if (Test-Path $emulatorPath) {
    $avds = & $emulatorPath -list-avds
    if ($avds) {
        $avds | ForEach-Object { Write-Host "  📱 $_" -ForegroundColor Cyan }
        
        # Usar o Pixel_9_Pro que é mais estável
        $targetAvd = "Pixel_9_Pro"
        if ($avds -contains $targetAvd) {
            Write-Host "`n🚀 Iniciando emulador: $targetAvd" -ForegroundColor Yellow
            
            # Iniciar com configurações seguras
            $arguments = @(
                "-avd", $targetAvd,
                "-gpu", "swiftshader_indirect",
                "-memory", "2048",
                "-no-snapshot-save",
                "-no-audio"
            )
            
            Start-Process -FilePath $emulatorPath -ArgumentList $arguments
            Write-Host "✅ Emulador iniciado!" -ForegroundColor Green
            Write-Host "⏳ Aguarde 2-3 minutos para o boot completo..." -ForegroundColor Yellow
            
        } else {
            Write-Host "❌ Emulador Pixel_9_Pro não encontrado" -ForegroundColor Red
            Write-Host "💡 Usando o primeiro emulador disponível..." -ForegroundColor Yellow
            
            $firstAvd = $avds[0]
            $arguments = @(
                "-avd", $firstAvd,
                "-gpu", "swiftshader_indirect",
                "-memory", "2048",
                "-no-snapshot-save",
                "-no-audio"
            )
            
            Start-Process -FilePath $emulatorPath -ArgumentList $arguments
            Write-Host "✅ Emulador $firstAvd iniciado!" -ForegroundColor Green
        }
        
    } else {
        Write-Host "❌ Nenhum emulador encontrado" -ForegroundColor Red
        Write-Host "💡 Crie um emulador no Android Studio primeiro" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Emulador não encontrado" -ForegroundColor Red
}

Write-Host "`n🎯 Próximos passos:" -ForegroundColor Cyan
Write-Host "1. Aguarde o emulador carregar completamente"
Write-Host "2. Verifique se aparece na tela inicial do Android"
Write-Host "3. Execute: adb devices (deve mostrar o emulador)"
Write-Host "4. Use com Appium Inspector ou testes"

Write-Host "`n✅ Script concluído!" -ForegroundColor Green
