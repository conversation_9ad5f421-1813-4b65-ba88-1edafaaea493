*** Settings ***
Documentation    Localizadores específicos para Android

*** Variables ***
# ========================================
# LOCALIZADORES ANDROID - TELA INICIAL
# ========================================
${ANDROID_BTN_CONTINUE}                 id=br.com.pactosolucoes.treino:id/btn_continue
${ANDROID_BTN_LOOK_FOR_GYM}            id=br.com.pactosolucoes.treino:id/btn_look_gym
${ANDROID_INPUT_SEARCH_GYM}            id=br.com.pactosolucoes.treino:id/edit_search_gym
${ANDROID_BTN_ACADEMIA_TEMPLATE}       xpath=//android.widget.TextView[@text='{NOME}']

# ========================================
# LOCALIZADORES ANDROID - LOGIN
# ========================================
${ANDROID_BTN_LOGIN_WITH_USER}         id=br.com.pactosolucoes.treino:id/btn_login_user
${ANDROID_INPUT_EMAIL_USERNAME}        id=br.com.pactosolucoes.treino:id/edit_email
${ANDROID_BTN_CONTINUE_EMAIL}          id=br.com.pactosolucoes.treino:id/btn_continue_email
${ANDROID_BTN_ENTER_PASSWORD}          id=br.com.pactosolucoes.treino:id/btn_enter_password
${ANDROID_INPUT_PASSWORD}              id=br.com.pactosolucoes.treino:id/edit_password
${ANDROID_BTN_CONFIRM_PASSWORD}        id=br.com.pactosolucoes.treino:id/btn_confirm
${ANDROID_BTN_ACCEPT_TERMS}            id=br.com.pactosolucoes.treino:id/btn_accept
${ANDROID_ERROR_MESSAGE}               id=br.com.pactosolucoes.treino:id/text_error

# ========================================
# LOCALIZADORES ANDROID - HOME
# ========================================
${ANDROID_HOME_SCREEN_INDICATOR}       id=br.com.pactosolucoes.treino:id/layout_home
${ANDROID_MENU_BUTTON}                 id=br.com.pactosolucoes.treino:id/btn_menu
${ANDROID_USER_PROFILE_AREA}           id=br.com.pactosolucoes.treino:id/layout_profile
${ANDROID_BTN_LOGOUT}                  id=br.com.pactosolucoes.treino:id/btn_logout

# ========================================
# LOCALIZADORES ANDROID - TROCA ACADEMIA
# ========================================
${ANDROID_BTN_TROCAR_ACADEMIA}         id=br.com.pactosolucoes.treino:id/bnt_trocar_academia
${ANDROID_BTN_SWAP_GYM}                xpath=//android.widget.Button[@text='Swap gym']
${ANDROID_BTN_SELECT_GYM}              xpath=//android.widget.Button[@text='Select your gym']
${ANDROID_INPUT_INSERIR_ACADEMIA}      id=inserir_nome_academia

# ========================================
# LOCALIZADORES ANDROID - LOADING
# ========================================
${ANDROID_LOADING_SPINNER}             id=br.com.pactosolucoes.treino:id/progress_bar
${ANDROID_LOADING_TEXT}                xpath=//android.widget.TextView[contains(@text, 'Carregando')]

*** Keywords ***
Get Android Locator
    [Documentation]    Retorna localizador específico do Android
    [Arguments]    ${element_name}
    
    ${locator}=    Run Keyword If    '${element_name}' == 'BTN_CONTINUE'
    ...    Set Variable    ${ANDROID_BTN_CONTINUE}
    ...    ELSE IF    '${element_name}' == 'BTN_LOOK_FOR_GYM'
    ...    Set Variable    ${ANDROID_BTN_LOOK_FOR_GYM}
    ...    ELSE IF    '${element_name}' == 'INPUT_SEARCH_GYM'
    ...    Set Variable    ${ANDROID_INPUT_SEARCH_GYM}
    ...    ELSE IF    '${element_name}' == 'BTN_LOGIN_WITH_USER'
    ...    Set Variable    ${ANDROID_BTN_LOGIN_WITH_USER}
    ...    ELSE IF    '${element_name}' == 'INPUT_EMAIL_USERNAME'
    ...    Set Variable    ${ANDROID_INPUT_EMAIL_USERNAME}
    ...    ELSE IF    '${element_name}' == 'BTN_CONTINUE_EMAIL'
    ...    Set Variable    ${ANDROID_BTN_CONTINUE_EMAIL}
    ...    ELSE IF    '${element_name}' == 'INPUT_PASSWORD'
    ...    Set Variable    ${ANDROID_INPUT_PASSWORD}
    ...    ELSE IF    '${element_name}' == 'BTN_CONFIRM_PASSWORD'
    ...    Set Variable    ${ANDROID_BTN_CONFIRM_PASSWORD}
    ...    ELSE IF    '${element_name}' == 'BTN_ACCEPT_TERMS'
    ...    Set Variable    ${ANDROID_BTN_ACCEPT_TERMS}
    ...    ELSE IF    '${element_name}' == 'HOME_SCREEN_INDICATOR'
    ...    Set Variable    ${ANDROID_HOME_SCREEN_INDICATOR}
    ...    ELSE IF    '${element_name}' == 'MENU_BUTTON'
    ...    Set Variable    ${ANDROID_MENU_BUTTON}
    ...    ELSE IF    '${element_name}' == 'ERROR_MESSAGE'
    ...    Set Variable    ${ANDROID_ERROR_MESSAGE}
    ...    ELSE
    ...    Fail    Localizador Android não encontrado: ${element_name}
    
    [Return]    ${locator}
