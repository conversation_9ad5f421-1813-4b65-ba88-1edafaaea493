*** Settings ***
Documentation    Localizadores de elementos da interface

*** Variables ***
# ========================================
# LOCALIZADORES GERAIS
# ========================================
${LOADING_SPINNER}              accessibility_id=loading_spinner
${LOADING_TEXT}                 xpath=//*[contains(@text, 'Carregando')]
${PROGRESS_BAR}                 accessibility_id=progress_bar

# ========================================
# TELA INICIAL / ONBOARDING
# ========================================
${BTN_CONTINUE}                 accessibility_id=Continue
${BTN_LOOK_FOR_GYM}            accessibility_id=Look for your gym
${INPUT_SEARCH_GYM}            accessibility_id=search_gym_input
${BTN_ACADEMIA_TEMPLATE}       xpath=//*[@text='{NOME}' or @label='{NOME}']

# ========================================
# TELA DE LOGIN
# ========================================
${BTN_LOGIN_WITH_USER}         accessibility_id=Login with user
${BTN_LOGIN_COLABORADOR}       accessibility_id=Login colaborador
${INPUT_EMAIL_USERNAME}        accessibility_id=Enter your email or username
${BTN_CONTINUE_EMAIL}          accessibility_id=Continue
${BTN_ENTER_PASSWORD}          accessibility_id=Enter password
${INPUT_PASSWORD}              accessibility_id=Password
${BTN_CONFIRM_PASSWORD}        accessibility_id=Confirm
${BTN_ACCEPT_TERMS}            accessibility_id=Accept
${ERROR_MESSAGE}               accessibility_id=error_message

# ========================================
# TELA PRINCIPAL / HOME
# ========================================
${HOME_SCREEN_INDICATOR}       accessibility_id=home_screen
${MENU_BUTTON}                 accessibility_id=menu_button
${USER_PROFILE_AREA}           accessibility_id=user_profile
${BTN_LOGOUT}                  accessibility_id=logout
${BTN_CONFIRM_LOGOUT}          accessibility_id=confirm_logout

# ========================================
# NAVEGAÇÃO E MENU
# ========================================
${MENU_AULAS}                  accessibility_id=menu_aulas
${MENU_TREINO}                 accessibility_id=menu_treino
${MENU_FEED}                   accessibility_id=menu_feed
${MENU_PERFIL}                 accessibility_id=menu_perfil
${MENU_CONFIGURACOES}          accessibility_id=menu_configuracoes

# ========================================
# MÓDULO AULAS
# ========================================
${BTN_MARCAR_AULA}             accessibility_id=marcar_aula
${BTN_DESMARCAR_AULA}          accessibility_id=desmarcar_aula
${BTN_FILA_ESPERA}             accessibility_id=fila_espera
${BTN_RESERVAR_EQUIPAMENTO}    accessibility_id=reservar_equipamento
${LIST_AULAS}                  accessibility_id=lista_aulas
${CARD_AULA_TEMPLATE}          xpath=//*[contains(@text, '{NOME_AULA}')]

# ========================================
# MÓDULO TREINO
# ========================================
${BTN_INICIAR_TREINO}          accessibility_id=iniciar_treino
${BTN_PAUSAR_TREINO}           accessibility_id=pausar_treino
${BTN_FINALIZAR_TREINO}        accessibility_id=finalizar_treino
${BTN_TROCAR_ATIVIDADE}        accessibility_id=trocar_atividade
${INPUT_CARGA}                 accessibility_id=input_carga
${INPUT_REPETICOES}            accessibility_id=input_repeticoes
${BTN_PROXIMA_SERIE}           accessibility_id=proxima_serie

# ========================================
# MÓDULO FEED
# ========================================
${BTN_NOVA_PUBLICACAO}         accessibility_id=nova_publicacao
${INPUT_TEXTO_POST}            accessibility_id=texto_post
${BTN_ADICIONAR_FOTO}          accessibility_id=adicionar_foto
${BTN_PUBLICAR}                accessibility_id=publicar
${BTN_CURTIR}                  accessibility_id=curtir
${BTN_COMENTAR}                accessibility_id=comentar
${INPUT_COMENTARIO}            accessibility_id=input_comentario

# ========================================
# MÓDULO PROFESSOR
# ========================================
${BTN_CRIAR_WOD}               accessibility_id=criar_wod
${BTN_CADASTRAR_ALUNO}         accessibility_id=cadastrar_aluno
${BTN_VER_ALUNOS}              accessibility_id=ver_alunos
${BTN_CRIAR_PROGRAMA}          accessibility_id=criar_programa
${INPUT_NOME_WOD}              accessibility_id=nome_wod
${INPUT_DESCRICAO_WOD}         accessibility_id=descricao_wod

# ========================================
# FORMULÁRIOS E VALIDAÇÕES
# ========================================
${MSG_SUCESSO}                 accessibility_id=mensagem_sucesso
${MSG_ERRO}                    accessibility_id=mensagem_erro
${BTN_OK}                      accessibility_id=OK
${BTN_CANCELAR}                accessibility_id=Cancelar
${BTN_CONFIRMAR}               accessibility_id=Confirmar

# ========================================
# LOCALIZADORES ESPECÍFICOS iOS
# ========================================
# Usar quando ${PLATFORM} == 'iOS'
${IOS_BACK_BUTTON}             accessibility_id=Back
${IOS_DONE_BUTTON}             accessibility_id=Done
${IOS_CANCEL_BUTTON}           accessibility_id=Cancel

# ========================================
# LOCALIZADORES ESPECÍFICOS ANDROID
# ========================================
# Usar quando ${PLATFORM} == 'Android'
${ANDROID_BACK_BUTTON}         xpath=//android.widget.ImageButton[@content-desc="Navigate up"]
${ANDROID_MENU_BUTTON}         accessibility_id=More options
${ANDROID_SEARCH_BUTTON}       accessibility_id=Search

# ========================================
# LOCALIZADORES DINÂMICOS (TEMPLATES)
# ========================================
# Use Replace String para substituir {PLACEHOLDER}
${ELEMENT_BY_TEXT_TEMPLATE}    xpath=//*[@text='{TEXT}' or @label='{TEXT}']
${BUTTON_BY_TEXT_TEMPLATE}     xpath=//android.widget.Button[@text='{TEXT}'] | //XCUIElementTypeButton[@label='{TEXT}']
${INPUT_BY_PLACEHOLDER}        xpath=//*[@placeholder='{PLACEHOLDER}' or @hint='{PLACEHOLDER}']

*** Keywords ***
Get Platform Specific Locator
    [Documentation]    Retorna localizador específico da plataforma
    [Arguments]    ${ios_locator}    ${android_locator}
    
    ${locator}=    Run Keyword If    '${PLATFORM}' == 'iOS'
    ...    Set Variable    ${ios_locator}
    ...    ELSE
    ...    Set Variable    ${android_locator}
    
    [Return]    ${locator}

Get Element By Text
    [Documentation]    Retorna localizador de elemento por texto
    [Arguments]    ${text}
    
    ${locator}=    Replace String    ${ELEMENT_BY_TEXT_TEMPLATE}    {TEXT}    ${text}
    [Return]    ${locator}

Get Button By Text
    [Documentation]    Retorna localizador de botão por texto
    [Arguments]    ${text}
    
    ${locator}=    Replace String    ${BUTTON_BY_TEXT_TEMPLATE}    {TEXT}    ${text}
    [Return]    ${locator}
