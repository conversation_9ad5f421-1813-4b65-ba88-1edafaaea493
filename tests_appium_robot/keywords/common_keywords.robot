*** Settings ***
Documentation    Keywords comuns para todos os testes
Library          AppiumLibrary
Library          BuiltIn
Library          Collections
Library          String
Resource         ../config/capabilities.robot
Resource         ../resources/locators.robot

*** Variables ***
${PLATFORM}              iOS    # iOS ou Android
${SCREENSHOT_DIR}         ${CURDIR}/../results/screenshots
${DEFAULT_TIMEOUT}        30s
${SHORT_TIMEOUT}          5s
${LONG_TIMEOUT}           60s

*** Keywords ***
Setup Test Environment
    [Documentation]    Configura o ambiente de teste
    [Arguments]    ${platform}=iOS
    
    Set Global Variable    ${PLATFORM}    ${platform}
    Create Directory    ${SCREENSHOT_DIR}
    
    Run Keyword If    '${platform}' == 'iOS'
    ...    Open Application    ${APPIUM_SERVER}    &{IOS_CAPABILITIES}
    ...    ELSE
    ...    Open Application    ${APPIUM_SERVER}    &{ANDROID_CAPABILITIES}
    
    Set Appium Timeout    ${DEFAULT_TIMEOUT}
    Log    Aplicação iniciada com sucesso na plataforma: ${platform}

Teardown Test Environment
    [Documentation]    Finaliza o ambiente de teste
    
    Run Keyword If Test Failed    Capture Screenshot On Failure
    Close Application
    Log    Aplicação fechada com sucesso

Capture Screenshot On Failure
    [Documentation]    Captura screenshot em caso de falha
    
    ${timestamp}=    Get Current Date    result_format=%Y%m%d_%H%M%S
    ${screenshot_name}=    Set Variable    failure_${timestamp}.png
    ${screenshot_path}=    Set Variable    ${SCREENSHOT_DIR}/${screenshot_name}
    
    Capture Page Screenshot    ${screenshot_path}
    Log    Screenshot capturado: ${screenshot_path}

Wait And Click Element
    [Documentation]    Aguarda elemento aparecer e clica
    [Arguments]    ${locator}    ${timeout}=${DEFAULT_TIMEOUT}
    
    Wait Until Element Is Visible    ${locator}    ${timeout}
    Click Element    ${locator}
    Log    Clicou no elemento: ${locator}

Wait And Input Text
    [Documentation]    Aguarda elemento aparecer e insere texto
    [Arguments]    ${locator}    ${text}    ${timeout}=${DEFAULT_TIMEOUT}
    
    Wait Until Element Is Visible    ${locator}    ${timeout}
    Clear Text    ${locator}
    Input Text    ${locator}    ${text}
    Log    Inseriu texto '${text}' no elemento: ${locator}

Wait For Element To Disappear
    [Documentation]    Aguarda elemento desaparecer
    [Arguments]    ${locator}    ${timeout}=${DEFAULT_TIMEOUT}
    
    Wait Until Element Is Not Visible    ${locator}    ${timeout}
    Log    Elemento desapareceu: ${locator}

Scroll To Element
    [Documentation]    Rola a tela até encontrar o elemento
    [Arguments]    ${locator}    ${direction}=down    ${max_scrolls}=10
    
    FOR    ${i}    IN RANGE    ${max_scrolls}
        ${element_visible}=    Run Keyword And Return Status
        ...    Element Should Be Visible    ${locator}
        
        Exit For Loop If    ${element_visible}
        
        Run Keyword If    '${direction}' == 'down'
        ...    Swipe    500    800    500    200    1000
        ...    ELSE IF    '${direction}' == 'up'
        ...    Swipe    500    200    500    800    1000
        ...    ELSE IF    '${direction}' == 'left'
        ...    Swipe    800    500    200    500    1000
        ...    ELSE IF    '${direction}' == 'right'
        ...    Swipe    200    500    800    500    1000
        
        Sleep    1s
    END
    
    Element Should Be Visible    ${locator}
    Log    Elemento encontrado após scroll: ${locator}

Wait For Loading To Complete
    [Documentation]    Aguarda carregamento completar
    [Arguments]    ${timeout}=${LONG_TIMEOUT}
    
    # Aguarda indicadores de loading desaparecerem
    ${loading_indicators}=    Create List
    ...    ${LOADING_SPINNER}
    ...    ${LOADING_TEXT}
    ...    ${PROGRESS_BAR}
    
    FOR    ${indicator}    IN    @{loading_indicators}
        ${is_present}=    Run Keyword And Return Status
        ...    Element Should Be Visible    ${indicator}
        
        Run Keyword If    ${is_present}
        ...    Wait For Element To Disappear    ${indicator}    ${timeout}
    END
    
    Sleep    2s    # Aguarda estabilizar
    Log    Carregamento completado

Validate Element Text
    [Documentation]    Valida texto de um elemento
    [Arguments]    ${locator}    ${expected_text}    ${timeout}=${DEFAULT_TIMEOUT}
    
    Wait Until Element Is Visible    ${locator}    ${timeout}
    ${actual_text}=    Get Text    ${locator}
    Should Be Equal    ${actual_text}    ${expected_text}
    Log    Texto validado: '${actual_text}' == '${expected_text}'

Validate Element Contains Text
    [Documentation]    Valida que elemento contém texto
    [Arguments]    ${locator}    ${expected_text}    ${timeout}=${DEFAULT_TIMEOUT}
    
    Wait Until Element Is Visible    ${locator}    ${timeout}
    ${actual_text}=    Get Text    ${locator}
    Should Contain    ${actual_text}    ${expected_text}
    Log    Texto contém: '${actual_text}' contém '${expected_text}'

Take Screenshot With Name
    [Documentation]    Captura screenshot com nome específico
    [Arguments]    ${name}
    
    ${timestamp}=    Get Current Date    result_format=%Y%m%d_%H%M%S
    ${screenshot_name}=    Set Variable    ${name}_${timestamp}.png
    ${screenshot_path}=    Set Variable    ${SCREENSHOT_DIR}/${screenshot_name}
    
    Capture Page Screenshot    ${screenshot_path}
    Log    Screenshot capturado: ${screenshot_path}
    [Return]    ${screenshot_path}
