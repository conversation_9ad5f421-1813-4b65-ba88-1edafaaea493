*** Settings ***
Documentation    Keywords específicas para funcionalidades de login
Resource         common_keywords.robot
Resource         ../resources/locators.robot

*** Keywords ***
Abrir App E Aceitar Termos
    [Documentation]    Abre o app e aceita os termos iniciais
    
    # Aguarda app carregar completamente
    Wait For Loading To Complete
    
    # Clica em Continue (equivalente ao tapOn: Continue do Maestro)
    Wait And Click Element    ${BTN_CONTINUE}
    
    Log    App aberto e termos aceitos

Buscar E Selecionar Academia
    [Documentation]    Busca e seleciona uma academia
    [Arguments]    ${nome_academia}
    
    # Clica em "Look for your gym" (equivalente ao tapOn: Look for your gym)
    Wait And Click Element    ${BTN_LOOK_FOR_GYM}
    
    # Insere nome da academia (equivalente ao inputText: Revisao)
    Wait And Input Text    ${INPUT_SEARCH_GYM}    ${nome_academia}
    
    # Seleciona a academia encontrada (equivalente ao tapOn: Revisão)
    ${academia_locator}=    Replace String    ${BTN_ACADEMIA_TEMPLATE}    {NOME}    ${nome_academia}
    Wait And Click Element    ${academia_locator}
    
    Log    Academia selecionada: ${nome_academia}

Iniciar Login Com Usuario
    [Documentation]    Inicia o processo de login com usuário
    
    # Clica em "Login with user" (equivalente ao tapOn: Login with user)
    Wait And Click Element    ${BTN_LOGIN_WITH_USER}
    
    Log    Processo de login iniciado

Inserir Email
    [Documentation]    Insere email no campo apropriado
    [Arguments]    ${email}
    
    # Clica no campo de email (equivalente ao tapOn: Enter your email or username)
    Wait And Click Element    ${INPUT_EMAIL_USERNAME}
    
    # Insere o email (equivalente ao inputText: <EMAIL>)
    Wait And Input Text    ${INPUT_EMAIL_USERNAME}    ${email}
    
    # Clica em Continue
    Wait And Click Element    ${BTN_CONTINUE_EMAIL}
    
    Log    Email inserido: ${email}

Inserir Senha
    [Documentation]    Insere senha no campo apropriado
    [Arguments]    ${senha}
    
    # Clica no campo de senha (equivalente ao tapOn: Enter password)
    Wait And Click Element    ${BTN_ENTER_PASSWORD}
    
    # Clica especificamente no campo Password
    Wait And Click Element    ${INPUT_PASSWORD}
    
    # Insere a senha (equivalente ao inputText: 123)
    Wait And Input Text    ${INPUT_PASSWORD}    ${senha}
    
    # Clica em Confirm (equivalente ao tapOn: Confirm)
    Wait And Click Element    ${BTN_CONFIRM_PASSWORD}
    
    Log    Senha inserida

Aguardar E Aceitar Termos Finais
    [Documentation]    Aguarda animação e aceita termos finais
    
    # Aguarda animação terminar (equivalente ao waitForAnimationToEnd: timeout: 5000)
    Wait For Loading To Complete    timeout=5s
    
    # Clica em Accept (equivalente ao tapOn: Accept)
    Wait And Click Element    ${BTN_ACCEPT_TERMS}
    
    Log    Termos finais aceitos

Fazer Login Completo Como Aluno
    [Documentation]    Executa o fluxo completo de login como aluno
    [Arguments]    ${academia}    ${email}    ${senha}
    
    Abrir App E Aceitar Termos
    Buscar E Selecionar Academia    ${academia}
    Iniciar Login Com Usuario
    Inserir Email    ${email}
    Inserir Senha    ${senha}
    Aguardar E Aceitar Termos Finais
    
    Log    Login completo realizado para: ${email}

Validar Login Realizado
    [Documentation]    Valida que o login foi realizado com sucesso
    
    # Aguarda tela principal aparecer
    Wait Until Element Is Visible    ${HOME_SCREEN_INDICATOR}    ${LONG_TIMEOUT}
    
    # Valida elementos da tela principal
    Element Should Be Visible    ${MENU_BUTTON}
    Element Should Be Visible    ${USER_PROFILE_AREA}
    
    Take Screenshot With Name    login_success
    Log    Login validado com sucesso

Fazer Logout
    [Documentation]    Realiza logout do aplicativo
    
    # Acessa menu
    Wait And Click Element    ${MENU_BUTTON}
    
    # Clica em logout
    Wait And Click Element    ${BTN_LOGOUT}
    
    # Confirma logout se necessário
    ${confirm_visible}=    Run Keyword And Return Status
    ...    Element Should Be Visible    ${BTN_CONFIRM_LOGOUT}
    
    Run Keyword If    ${confirm_visible}
    ...    Wait And Click Element    ${BTN_CONFIRM_LOGOUT}
    
    # Valida que voltou para tela de login
    Wait Until Element Is Visible    ${BTN_CONTINUE}    ${DEFAULT_TIMEOUT}
    
    Log    Logout realizado com sucesso

Login Como Colaborador
    [Documentation]    Realiza login como colaborador/professor
    [Arguments]    ${academia}    ${email}    ${senha}
    
    Abrir App E Aceitar Termos
    Buscar E Selecionar Academia    ${academia}
    
    # Para colaborador, pode ter fluxo diferente
    Wait And Click Element    ${BTN_LOGIN_COLABORADOR}
    Inserir Email    ${email}
    Inserir Senha    ${senha}
    Aguardar E Aceitar Termos Finais
    
    Log    Login como colaborador realizado para: ${email}

Validar Erro De Login
    [Documentation]    Valida que erro de login foi exibido
    [Arguments]    ${mensagem_esperada}
    
    Wait Until Element Is Visible    ${ERROR_MESSAGE}    ${DEFAULT_TIMEOUT}
    ${mensagem_atual}=    Get Text    ${ERROR_MESSAGE}
    Should Contain    ${mensagem_atual}    ${mensagem_esperada}
    
    Take Screenshot With Name    login_error
    Log    Erro de login validado: ${mensagem_atual}
