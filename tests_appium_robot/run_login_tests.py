#!/usr/bin/env python3
"""
Script para executar testes de login migrados do Maestro
Equivalente aos testes em .teste_auto/login/
"""

import os
import sys
import subprocess
from datetime import datetime

def run_command(command, description):
    """Executa comando e exibe resultado"""
    print(f"\n🔄 {description}")
    print(f"Comando: {command}")
    
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        print(f"✅ {description} - Sucesso")
        if result.stdout:
            print(f"Output: {result.stdout}")
    else:
        print(f"❌ {description} - Falha")
        if result.stderr:
            print(f"Erro: {result.stderr}")
        if result.stdout:
            print(f"Output: {result.stdout}")
    
    return result.returncode == 0

def main():
    """Função principal"""
    print("🚀 Executando Testes de Login - Migração Maestro → Appium + Robot")
    print("=" * 70)
    
    # Verificar se Appium está rodando
    print("\n📱 Verificando Appium Server...")
    appium_check = subprocess.run(
        "curl -s http://localhost:4723/wd/hub/status", 
        shell=True, 
        capture_output=True
    )
    
    if appium_check.returncode != 0:
        print("❌ Appium Server não está rodando!")
        print("💡 Inicie o Appium com: appium --port 4723")
        return False
    
    print("✅ Appium Server está rodando")
    
    # Criar diretório de resultados
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = f"results/login_tests_{timestamp}"
    os.makedirs(results_dir, exist_ok=True)
    
    # Comandos de teste
    test_commands = [
        {
            "command": f"robot --outputdir {results_dir} --name 'Login Aluno' test_suites/login/login_aluno.robot",
            "description": "Executando testes de login do aluno"
        },
        {
            "command": f"robot --outputdir {results_dir} --name 'Troca Academia' test_suites/login/troca_academia.robot",
            "description": "Executando testes de troca de academia"
        },
        {
            "command": f"robot --outputdir {results_dir} --name 'Login Suite Completa' test_suites/login/",
            "description": "Executando suite completa de login"
        }
    ]
    
    # Executar testes
    success_count = 0
    total_tests = len(test_commands)
    
    for test in test_commands:
        if run_command(test["command"], test["description"]):
            success_count += 1
    
    # Relatório final
    print("\n" + "=" * 70)
    print("📊 RELATÓRIO FINAL")
    print("=" * 70)
    print(f"✅ Testes executados com sucesso: {success_count}/{total_tests}")
    print(f"📁 Resultados salvos em: {results_dir}")
    print(f"📄 Relatório HTML: {results_dir}/report.html")
    print(f"📋 Log detalhado: {results_dir}/log.html")
    
    if success_count == total_tests:
        print("\n🎉 Todos os testes foram executados com sucesso!")
        print("✨ Migração do Maestro para Appium + Robot Framework concluída!")
    else:
        print(f"\n⚠️  {total_tests - success_count} teste(s) falharam")
        print("🔍 Verifique os logs para mais detalhes")
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
