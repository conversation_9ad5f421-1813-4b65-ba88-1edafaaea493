*** Settings ***
Documentation    Configurações de capabilities para Appium

*** Variables ***
# Servidor Appium
${APPIUM_SERVER}    http://localhost:4723/wd/hub
${APPIUM_TIMEOUT}   30

# Capabilities iOS
&{IOS_CAPABILITIES}
...    platformName=iOS
...    platformVersion=16.0
...    deviceName=iPhone 14
...    automationName=XCUITest
...    bundleId=br.com.pactosolucoes.treino.ios
...    newCommandTimeout=300
...    wdaLaunchTimeout=300000
...    wdaConnectionTimeout=300000
...    autoAcceptAlerts=true
...    autoDismissAlerts=false

# Capabilities Android
&{ANDROID_CAPABILITIES}
...    platformName=Android
...    platformVersion=13.0
...    deviceName=Android Emulator
...    automationName=UiAutomator2
...    appPackage=br.com.pactosolucoes.treino
...    appActivity=.MainActivity
...    newCommandTimeout=300
...    autoGrantPermissions=true
...    noReset=false
...    fullReset=false
...    unicodeKeyboard=true
...    resetKeyboard=true
...    systemPort=8201
...    chromeDriverPort=9516

# Capabilities para dispositivos reais iOS
&{IOS_REAL_DEVICE}
...    platformName=iOS
...    platformVersion=16.0
...    deviceName=iPhone de Teste
...    automationName=XCUITest
...    bundleId=br.com.pactosolucoes.treino.ios
...    udid=YOUR_DEVICE_UDID
...    xcodeOrgId=YOUR_TEAM_ID
...    xcodeSigningId=iPhone Developer
...    newCommandTimeout=300
...    wdaLaunchTimeout=300000
...    wdaConnectionTimeout=300000

# Capabilities para dispositivos reais Android
&{ANDROID_REAL_DEVICE}
...    platformName=Android
...    platformVersion=13.0
...    deviceName=Samsung Galaxy S23
...    automationName=UiAutomator2
...    appPackage=br.com.pactosolucoes.treino
...    appActivity=.MainActivity
...    udid=YOUR_DEVICE_UDID
...    newCommandTimeout=300
...    autoGrantPermissions=true

*** Keywords ***
Get iOS Capabilities
    [Documentation]    Retorna capabilities para iOS
    [Return]    &{IOS_CAPABILITIES}

Get Android Capabilities
    [Documentation]    Retorna capabilities para Android
    [Return]    &{ANDROID_CAPABILITIES}

Get iOS Real Device Capabilities
    [Documentation]    Retorna capabilities para dispositivo iOS real
    [Return]    &{IOS_REAL_DEVICE}

Get Android Real Device Capabilities
    [Documentation]    Retorna capabilities para dispositivo Android real
    [Return]    &{ANDROID_REAL_DEVICE}
