#!/usr/bin/env python3
"""
Script de diagnóstico para identificar problemas de configuração
"""

import subprocess
import sys
import json
import requests
from pathlib import Path

def check_python_packages():
    """Verifica se os pacotes Python estão instalados"""
    print("🐍 Verificando pacotes Python...")
    
    required_packages = [
        'robotframework',
        'robotframework-appiumlibrary',
        'Appium-Python-Client'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_').lower())
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n💡 Instale os pacotes faltantes:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_appium_server():
    """Verifica se o Appium Server está rodando"""
    print("\n📱 Verificando Appium Server...")
    
    try:
        response = requests.get("http://localhost:4723/wd/hub/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Appium Server rodando")
            print(f"  📋 Versão: {data.get('value', {}).get('build', {}).get('version', 'N/A')}")
            return True
        else:
            print(f"  ❌ Appium Server respondeu com status: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"  ❌ Appium Server não está rodando")
        print(f"  💡 Inicie com: appium --port 4723")
        return False

def check_android_setup():
    """Verifica configuração do Android"""
    print("\n🤖 Verificando configuração Android...")
    
    # Verificar ADB
    try:
        result = subprocess.run(['adb', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"  ✅ ADB instalado")
            
            # Verificar dispositivos conectados
            devices_result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
            devices_lines = devices_result.stdout.strip().split('\n')[1:]  # Pular header
            connected_devices = [line for line in devices_lines if line.strip() and 'device' in line]
            
            if connected_devices:
                print(f"  ✅ {len(connected_devices)} dispositivo(s) conectado(s):")
                for device in connected_devices:
                    print(f"    📱 {device}")
                return True
            else:
                print(f"  ⚠️  Nenhum dispositivo Android conectado")
                print(f"  💡 Conecte um dispositivo ou inicie um emulador")
                return False
        else:
            print(f"  ❌ ADB não encontrado")
            return False
    except FileNotFoundError:
        print(f"  ❌ ADB não está no PATH")
        print(f"  💡 Instale Android SDK e configure o PATH")
        return False

def check_ios_setup():
    """Verifica configuração do iOS (apenas no macOS)"""
    print("\n🍎 Verificando configuração iOS...")
    
    if sys.platform != 'darwin':
        print("  ⚠️  iOS só é suportado no macOS")
        return False
    
    # Verificar Xcode Command Line Tools
    try:
        result = subprocess.run(['xcode-select', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"  ✅ Xcode Command Line Tools instalado")
            
            # Verificar simuladores
            sim_result = subprocess.run(['xcrun', 'simctl', 'list', 'devices'], capture_output=True, text=True)
            if 'iPhone' in sim_result.stdout:
                print(f"  ✅ Simuladores iOS disponíveis")
                return True
            else:
                print(f"  ⚠️  Nenhum simulador iOS encontrado")
                return False
        else:
            print(f"  ❌ Xcode Command Line Tools não encontrado")
            return False
    except FileNotFoundError:
        print(f"  ❌ Xcode não está instalado")
        return False

def check_project_structure():
    """Verifica estrutura do projeto"""
    print("\n📁 Verificando estrutura do projeto...")
    
    required_files = [
        'requirements.txt',
        'config/capabilities.robot',
        'keywords/common_keywords.robot',
        'keywords/login_keywords.robot',
        'test_suites/login/login_aluno.robot',
        'test_suites/login/troca_academia.robot'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n💡 Arquivos faltantes encontrados")
        return False
    
    return True

def suggest_fixes():
    """Sugere correções para problemas comuns"""
    print("\n🔧 SOLUÇÕES PARA PROBLEMAS COMUNS:")
    print("=" * 50)
    
    print("\n1. 📦 Instalar dependências:")
    print("   pip install -r requirements.txt")
    
    print("\n2. 🚀 Iniciar Appium Server:")
    print("   appium --port 4723 --relaxed-security")
    
    print("\n3. 🤖 Configurar Android:")
    print("   - Instalar Android Studio")
    print("   - Configurar ANDROID_HOME")
    print("   - Adicionar ADB ao PATH")
    print("   - Conectar dispositivo ou iniciar emulador")
    
    print("\n4. 🍎 Configurar iOS (macOS):")
    print("   - Instalar Xcode")
    print("   - xcode-select --install")
    print("   - Abrir simulador iOS")
    
    print("\n5. 🔍 Debug de localizadores:")
    print("   - Usar Appium Inspector")
    print("   - Verificar IDs dos elementos")
    print("   - Atualizar localizadores conforme necessário")

def main():
    """Função principal"""
    print("🔍 DIAGNÓSTICO DE CONFIGURAÇÃO - Appium + Robot Framework")
    print("=" * 60)
    
    checks = [
        ("Pacotes Python", check_python_packages),
        ("Appium Server", check_appium_server),
        ("Android Setup", check_android_setup),
        ("Estrutura do Projeto", check_project_structure)
    ]
    
    # Adicionar verificação iOS apenas no macOS
    if sys.platform == 'darwin':
        checks.insert(-1, ("iOS Setup", check_ios_setup))
    
    results = []
    
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"  ❌ Erro durante verificação: {e}")
            results.append((name, False))
    
    # Relatório final
    print("\n" + "=" * 60)
    print("📊 RELATÓRIO FINAL")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {name}")
    
    print(f"\n🎯 Resultado: {passed}/{total} verificações passaram")
    
    if passed == total:
        print("\n🎉 Configuração está OK! Você pode executar os testes.")
    else:
        print(f"\n⚠️  {total - passed} problema(s) encontrado(s)")
        suggest_fixes()
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
