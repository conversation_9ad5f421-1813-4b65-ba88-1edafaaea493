*** Settings ***
Documentation    Configurações específicas para testes de login
...              Migrado de: .teste_auto/login/troca_de_academia/config.yaml

*** Variables ***
# Dados de teste para login
${ACADEMIA_PADRAO}       Revisao
${ACADEMIA_ALTERNATIVA}  Academia Teste
${EMAIL_ALUNO_VALIDO}    <EMAIL>
${EMAIL_ALUNO_INVALIDO}  <EMAIL>
${SENHA_VALIDA}          123
${SENHA_INVALIDA}        senha_errada

# Timeouts específicos para login
${LOGIN_TIMEOUT}         30s
${SEARCH_TIMEOUT}        10s
${ANIMATION_TIMEOUT}     5s

# Mensagens de erro esperadas
${MSG_USUARIO_NAO_ENCONTRADO}    usuário não encontrado
${MSG_SENHA_INCORRETA}           senha incorreta
${MSG_ACADEMIA_NAO_ENCONTRADA}   nenhuma academia encontrada

*** Keywords ***
Setup Login Test
    [Documentation]    Setup específico para testes de login
    
    Setup Test Environment    iOS
    Set Test Variable    ${ACADEMIA_TESTE}    ${ACADEMIA_PADRAO}
    Set Test Variable    ${EMAIL_TESTE}       ${EMAIL_ALUNO_VALIDO}
    Set Test Variable    ${SENHA_TESTE}       ${SENHA_VALIDA}

Teardown Login Test
    [Documentation]    Teardown específico para testes de login
    
    # Fazer logout se estiver logado
    ${is_logged_in}=    Run Keyword And Return Status
    ...    Element Should Be Visible    accessibility_id=home_screen
    
    Run Keyword If    ${is_logged_in}    Fazer Logout
    
    Teardown Test Environment
