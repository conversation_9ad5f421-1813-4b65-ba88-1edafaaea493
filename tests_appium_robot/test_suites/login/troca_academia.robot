*** Settings ***
Documentation    Testes de troca de academia
...              Migrado de: .teste_auto/login/troca_de_academia/

Library          AppiumLibrary
Resource         ../../keywords/common_keywords.robot
Resource         ../../keywords/login_keywords.robot
Resource         ../../config/capabilities.robot

Suite Setup      Setup Test Environment    iOS
Suite Teardown   Teardown Test Environment

*** Variables ***
${ACADEMIA_INICIAL}    Revisao
${ACADEMIA_NOVA}       Academia Teste
${EMAIL_ALUNO}         <EMAIL>
${SENHA_ALUNO}         123

*** Test Cases ***
Selecionar Academia Na Primeira Vez
    [Documentation]    Seleciona academia na primeira vez que abre o app
    ...                Migrado de: selecionar_academia.yaml
    [Tags]    login    academia    first_time
    
    # Migração do fluxo original:
    # - tapOn: id: "inserir_nome_academia"
    # - inputText: "Revisao"
    # - tapOn: point: "50%,32%"
    # - tapOn: "Your fitness journey begins now!"
    
    [Setup]    Take Screenshot With Name    selecionar_academia_inicio
    
    # Passo 1: Clicar no campo de inserir nome da academia
    Wait And Click Element    id=inserir_nome_academia
    
    # Passo 2: Inserir nome da academia
    Wait And Input Text    id=inserir_nome_academia    ${ACADEMIA_INICIAL}
    
    # Passo 3: Clicar na posição específica (50%, 32%)
    # Convertendo coordenada percentual para pixel
    ${screen_size}=    Get Window Size
    ${x}=    Evaluate    ${screen_size[0]} * 0.5
    ${y}=    Evaluate    ${screen_size[1]} * 0.32
    Click A Point    ${x}    ${y}
    
    # Passo 4: Clicar em "Your fitness journey begins now!"
    Wait And Click Element    xpath=//*[@label='Your fitness journey begins now!']
    
    # Validação: Verificar se academia foi selecionada
    Wait Until Element Is Visible    accessibility_id=academia_selecionada    10s
    
    [Teardown]    Take Screenshot With Name    selecionar_academia_sucesso

Trocar Academia Após Login
    [Documentation]    Troca de academia após estar logado
    ...                Migrado de: trocar_academia.yaml
    [Tags]    login    academia    change
    
    # Migração do fluxo original:
    # - tapOn: id: "bnt_trocar_academia"
    # - tapOn: "Swap gym"
    # - tapOn: "Select your gym"
    
    [Setup]    Fazer Login Completo Como Aluno    ${ACADEMIA_INICIAL}    ${EMAIL_ALUNO}    ${SENHA_ALUNO}
    
    # Passo 1: Clicar no botão de trocar academia
    Wait And Click Element    id=bnt_trocar_academia
    
    # Passo 2: Clicar em "Swap gym"
    Wait And Click Element    xpath=//*[@label='Swap gym']
    
    # Passo 3: Clicar em "Select your gym"
    Wait And Click Element    xpath=//*[@label='Select your gym']
    
    # Passo 4: Selecionar nova academia
    Wait And Input Text    accessibility_id=search_gym    ${ACADEMIA_NOVA}
    Wait And Click Element    xpath=//*[@label='${ACADEMIA_NOVA}']
    
    # Validação: Verificar se academia foi trocada
    Wait Until Element Is Visible    accessibility_id=academia_alterada    10s
    ${academia_atual}=    Get Text    accessibility_id=nome_academia_atual
    Should Be Equal    ${academia_atual}    ${ACADEMIA_NOVA}
    
    [Teardown]    Take Screenshot With Name    trocar_academia_sucesso

Buscar Academia Inexistente
    [Documentation]    Tenta buscar uma academia que não existe
    [Tags]    login    academia    negative
    
    Wait And Click Element    accessibility_id=Continue
    Wait And Click Element    accessibility_id=Look for your gym
    Wait And Input Text       accessibility_id=search_gym    Academia Inexistente XYZ
    
    # Aguardar um tempo para busca
    Sleep    3s
    
    # Validar que nenhuma academia foi encontrada
    Element Should Not Be Visible    xpath=//*[@label='Academia Inexistente XYZ']
    
    # Verificar se há mensagem de "nenhuma academia encontrada"
    ${no_results}=    Run Keyword And Return Status
    ...    Element Should Be Visible    accessibility_id=no_gym_found
    
    Run Keyword If    ${no_results}
    ...    Log    Mensagem de "nenhuma academia encontrada" exibida corretamente
    ...    ELSE
    ...    Log    Lista vazia exibida corretamente
    
    Take Screenshot With Name    academia_inexistente

Cancelar Troca De Academia
    [Documentation]    Inicia troca de academia mas cancela o processo
    [Tags]    login    academia    cancel
    
    [Setup]    Fazer Login Completo Como Aluno    ${ACADEMIA_INICIAL}    ${EMAIL_ALUNO}    ${SENHA_ALUNO}
    
    # Iniciar processo de troca
    Wait And Click Element    id=bnt_trocar_academia
    Wait And Click Element    xpath=//*[@label='Swap gym']
    
    # Cancelar processo
    ${cancel_button}=    Run Keyword And Return Status
    ...    Element Should Be Visible    accessibility_id=Cancel
    
    Run Keyword If    ${cancel_button}
    ...    Wait And Click Element    accessibility_id=Cancel
    ...    ELSE
    ...    # Se não há botão Cancel, usar botão de voltar
    ...    Wait And Click Element    accessibility_id=Back
    
    # Validar que voltou para tela anterior
    Wait Until Element Is Visible    accessibility_id=home_screen    10s
    
    # Verificar que academia não foi alterada
    ${academia_atual}=    Get Text    accessibility_id=nome_academia_atual
    Should Be Equal    ${academia_atual}    ${ACADEMIA_INICIAL}
    
    [Teardown]    Take Screenshot With Name    cancelar_troca_academia
