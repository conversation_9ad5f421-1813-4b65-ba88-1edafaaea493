*** Settings ***
Documentation    Testes de login como aluno
...              Migrado de: .teste_auto/login/login_only_aluno.yaml

Library          AppiumLibrary
Resource         ../../keywords/common_keywords.robot
Resource         ../../keywords/login_keywords.robot
Resource         ../../config/capabilities.robot

Suite Setup      Setup Test Environment    iOS
Suite Teardown   Teardown Test Environment

*** Variables ***
${ACADEMIA_TESTE}    Revisao
${EMAIL_ALUNO}       <EMAIL>
${SENHA_ALUNO}       123

*** Test Cases ***
Login Aluno Com Sucesso
    [Documentation]    Realiza login como aluno no app
    ...                Equivalente ao teste: login_only_aluno.yaml
    [Tags]    login    aluno    smoke    critical
    
    # Migração do fluxo original do Maestro:
    # - tapOn: Continue
    # - tapOn: Look for your gym
    # - inputText: Revisao
    # - tapOn: Revisão
    # - tapOn: Login with user
    # - tapOn: Enter your email or username
    # - inputText: <EMAIL>
    # - tapOn: Continue
    # - tapOn: Enter password
    # - tapOn: Password
    # - inputText: 123
    # - tapOn: Confirm
    # - waitForAnimationToEnd: timeout: 5000
    # - tapOn: Accept
    
    [Setup]    Take Screenshot With Name    login_inicio
    
    # Passo 1: Aceitar termos iniciais
    Wait And Click Element    accessibility_id=Continue
    
    # Passo 2: Buscar academia
    Wait And Click Element    accessibility_id=Look for your gym
    Wait And Input Text       accessibility_id=search_gym    ${ACADEMIA_TESTE}
    Wait And Click Element    xpath=//*[@label='${ACADEMIA_TESTE}']
    
    # Passo 3: Iniciar login com usuário
    Wait And Click Element    accessibility_id=Login with user
    
    # Passo 4: Inserir email
    Wait And Click Element    accessibility_id=Enter your email or username
    Wait And Input Text       accessibility_id=email_input    ${EMAIL_ALUNO}
    Wait And Click Element    accessibility_id=Continue
    
    # Passo 5: Inserir senha
    Wait And Click Element    accessibility_id=Enter password
    Wait And Click Element    accessibility_id=Password
    Wait And Input Text       accessibility_id=password_input    ${SENHA_ALUNO}
    Wait And Click Element    accessibility_id=Confirm
    
    # Passo 6: Aguardar carregamento e aceitar termos
    Wait For Loading To Complete    timeout=5s
    Wait And Click Element    accessibility_id=Accept
    
    # Validação: Verificar se login foi bem-sucedido
    Wait Until Element Is Visible    accessibility_id=home_screen    30s
    Element Should Be Visible        accessibility_id=menu_button
    
    [Teardown]    Take Screenshot With Name    login_sucesso

Login Aluno Com Email Inválido
    [Documentation]    Testa login com email inválido
    [Tags]    login    aluno    negative
    
    Wait And Click Element    accessibility_id=Continue
    Wait And Click Element    accessibility_id=Look for your gym
    Wait And Input Text       accessibility_id=search_gym    ${ACADEMIA_TESTE}
    Wait And Click Element    xpath=//*[@label='${ACADEMIA_TESTE}']
    Wait And Click Element    accessibility_id=Login with user
    Wait And Click Element    accessibility_id=Enter your email or username
    Wait And Input Text       accessibility_id=email_input    <EMAIL>
    Wait And Click Element    accessibility_id=Continue
    
    # Validar mensagem de erro
    Wait Until Element Is Visible    accessibility_id=error_message    10s
    ${error_text}=    Get Text    accessibility_id=error_message
    Should Contain    ${error_text}    usuário não encontrado
    
    Take Screenshot With Name    login_email_invalido

Login Aluno Com Senha Incorreta
    [Documentation]    Testa login com senha incorreta
    [Tags]    login    aluno    negative
    
    Wait And Click Element    accessibility_id=Continue
    Wait And Click Element    accessibility_id=Look for your gym
    Wait And Input Text       accessibility_id=search_gym    ${ACADEMIA_TESTE}
    Wait And Click Element    xpath=//*[@label='${ACADEMIA_TESTE}']
    Wait And Click Element    accessibility_id=Login with user
    Wait And Click Element    accessibility_id=Enter your email or username
    Wait And Input Text       accessibility_id=email_input    ${EMAIL_ALUNO}
    Wait And Click Element    accessibility_id=Continue
    Wait And Click Element    accessibility_id=Enter password
    Wait And Click Element    accessibility_id=Password
    Wait And Input Text       accessibility_id=password_input    senha_errada
    Wait And Click Element    accessibility_id=Confirm
    
    # Validar mensagem de erro
    Wait Until Element Is Visible    accessibility_id=error_message    10s
    ${error_text}=    Get Text    accessibility_id=error_message
    Should Contain    ${error_text}    senha incorreta
    
    Take Screenshot With Name    login_senha_incorreta
