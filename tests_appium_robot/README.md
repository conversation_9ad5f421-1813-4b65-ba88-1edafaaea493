# Testes Automatizados - Appium + Robot Framework

Este diretório contém a migração dos testes do Maestro para Appium com Robot Framework.

## Estrutura do Projeto

```
tests_appium_robot/
├── README.md                    # Este arquivo
├── requirements.txt             # Dependências Python
├── config/                      # Configurações
│   ├── capabilities.robot       # Capabilities do Appium
│   ├── environments.robot       # Ambientes (dev, staging, prod)
│   └── test_data.robot         # Dados de teste
├── keywords/                    # Keywords reutilizáveis
│   ├── common_keywords.robot    # Keywords comuns
│   ├── login_keywords.robot     # Keywords de login
│   ├── navigation_keywords.robot # Keywords de navegação
│   └── assertion_keywords.robot # Keywords de validação
├── page_objects/               # Page Objects
│   ├── base_page.robot         # Página base
│   ├── login_page.robot        # Página de login
│   ├── home_page.robot         # Página inicial
│   └── menu_page.robot         # Página de menu
├── test_suites/                # Suites de teste organizadas por módulo
│   ├── login/                  # Testes de login
│   ├── aulas/                  # Testes de aulas
│   ├── treino/                 # Testes de treino
│   ├── feed/                   # Testes de feed
│   └── professor/              # Testes do professor
├── resources/                  # Recursos auxiliares
│   ├── locators.robot          # Localizadores de elementos
│   ├── test_data.json          # Dados de teste em JSON
│   └── utils.py                # Utilitários Python
└── results/                    # Resultados dos testes
    ├── reports/                # Relatórios HTML
    ├── logs/                   # Logs detalhados
    └── screenshots/            # Screenshots de falhas
```

## Vantagens da Migração

### **Maestro vs Appium + Robot Framework:**

| Aspecto | Maestro | Appium + Robot | Vantagem |
|---------|---------|----------------|----------|
| **Linguagem** | YAML simples | Robot Framework (mais expressivo) | Robot |
| **Flexibilidade** | Limitada | Alta (Python + bibliotecas) | Robot |
| **Debugging** | Básico | Avançado (logs, screenshots) | Robot |
| **Integração CI/CD** | Boa | Excelente | Robot |
| **Relatórios** | Simples | Detalhados (HTML, XML) | Robot |
| **Reutilização** | Limitada | Alta (keywords, page objects) | Robot |
| **Manutenção** | Difícil em projetos grandes | Estruturada | Robot |

## Como Usar

1. **Instalar dependências:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configurar Appium Server:**
   ```bash
   appium --port 4723
   ```

3. **Executar testes:**
   ```bash
   # Todos os testes
   robot test_suites/
   
   # Suite específica
   robot test_suites/login/
   
   # Teste específico
   robot test_suites/login/login_aluno.robot
   ```

4. **Ver relatórios:**
   - Abrir `results/reports/report.html`

## Migração dos Testes Existentes

Cada teste do Maestro será convertido seguindo este padrão:

### **Antes (Maestro):**
```yaml
- tapOn: Continue
- inputText: <EMAIL>
- tapOn: Confirm
```

### **Depois (Robot Framework):**
```robot
*** Test Cases ***
Login Como Aluno
    [Documentation]    Realiza login como aluno no app
    [Tags]    login    aluno    smoke
    
    Abrir App
    Aceitar Termos
    Buscar Academia    Revisao
    Selecionar Academia    Revisão
    Fazer Login Com Email    <EMAIL>    123
    Validar Login Realizado
```
