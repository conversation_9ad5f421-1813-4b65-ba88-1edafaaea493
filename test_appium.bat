@echo off
echo ========================================
echo TESTE DE CONFIGURACAO APPIUM
echo ========================================

echo.
echo 1. Testando comando appium...
appium --version
if %errorlevel% neq 0 (
    echo ERRO: Comando appium nao funciona
    echo Execute install_appium_windows.bat primeiro
    pause
    exit /b 1
)

echo.
echo 2. Testando drivers...
appium driver list

echo.
echo 3. Iniciando Appium Server (pressione Ctrl+C para parar)...
echo URL: http://localhost:4723
echo.
appium --port 4723 --relaxed-security --allow-insecure chromedriver_autodownload
