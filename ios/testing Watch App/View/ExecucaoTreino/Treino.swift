//
//  Treino.swift
//  testing Watch App
//
//  Created by <PERSON><PERSON> on 09/05/23.
//

import Foundation
import SwiftUI
import HealthKit

struct TreinoView: View {
    
    @EnvironmentObject var watchViewModel: WatchViewModel
    
    var body: some View {
        List {
            ForEach((watchViewModel.ficha.atividades ?? [Atividade()]).indices) { index in
                NavigationLink(destination: DetalhesSerieView(serie: self.watchViewModel.ficha.atividades?[index].series ?? [Series()], urlImagem: self.watchViewModel.ficha.atividades?[index].urlFotoAtividade ?? "" )){
                    VStack(alignment: .leading) {
                        Text(tipoExecucao(metodoExecucao:self.watchViewModel.ficha.atividades?[index].nomeMetodoExecucao ?? "Não atribuído")).font(.custom("Poppins-Regular", size: 14)).foregroundColor(validarCor())
                        HStack {
                            Text((self.watchViewModel.ficha.atividades?[index].nomeAtividade ?? "Atividade").capitalized).font(.custom("Poppins-SemiBold", size: 16))
                            Spacer()
                            Image(systemName: (todasSeriesConcluidas(series: self.watchViewModel.ficha.atividades?[index].series ?? [])) ? "checkmark.circle.fill" : "circle")
                                .font(.system(size: 20))
                                .foregroundColor(validarCor()).padding(EdgeInsets(top: 0, leading: 6, bottom: 0, trailing: 0))
                        }
                        Text("\(quantidadeSeriesConcluidas(series: self.watchViewModel.ficha.atividades?[index].series ?? [])) de \(self.watchViewModel.ficha.atividades?[index].series?.count ?? 0)" + ((self.watchViewModel.ficha.atividades?[index].series?.count ?? 0 > 1 ) ? " séries concluídas": " série concluída") ).font(.custom("Poppins-Regular", size: 14))
                        
                    }.padding(EdgeInsets(top: 12, leading: 4, bottom: 12, trailing: 4))
                }
            }
        }.listStyle(.carousel)
    }
    
    func tipoExecucao(metodoExecucao:String) -> String {
        if (metodoExecucao == "Não atribuído") {
            return "Execução normal"
        } else if (metodoExecucao.isEmpty) {
            return "Execução normal"
        } else {
            return metodoExecucao
        }
    }
    
    func quantidadeSeriesConcluidas(series: [Series]) -> String {
        let quantidadeSeriesConcluidas = series.filter { Series in
            Series.concluida == true
        }.count
        return String(quantidadeSeriesConcluidas)
    }
    
    func todasSeriesConcluidas(series: [Series]) -> Bool {
        let quantidadeSeriesConcluidas = series.filter { Series in
            Series.concluida == true
        }.count
        return quantidadeSeriesConcluidas == series.count
    }
    
}



struct DetalhesSerieView: View {
    var serie : [Series]
    var urlImagem: String
    
    @EnvironmentObject var watchViewModel: WatchViewModel
    
    @Environment(\.presentationMode) var presentationMode
    
    let timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    
    @State private var remainingTime: Int = 0
    @State private var descansoTotal: CGFloat = 0
    
    @State private var animationProgress: CGFloat = 0
    @State private var exibirContador : Bool = false;
    
    
    private let gradientBg = RadialGradient(colors: [validarCor().adjust(brightness: -1), validarCor().adjust(brightness: 1)], center: .center, startRadius: 0, endRadius: 360)
    
    private let gradient = RadialGradient(colors: [validarCor().adjust(brightness: 0), validarCor().adjust(brightness: 0)], center: .center, startRadius: 0, endRadius: 360)
    
    var body: some View {
        AtividadesView
    }
    
  
    
    var AtividadesView: some View {
        ZStack {
            TabView {
                ForEach(serie, id: \.codSerie) { dadosSerie in
                    ScrollView {
                        VStack(alignment: .leading) {
                            ZStack {
                                NetworkImage(url: URL(string: urlImagem))
                                    .scaledToFill()
                                    .frame(width: SGConvenience.deviceWidth - 32, height: 80, alignment: .center)
                                    .cornerRadius(8)
                                NetworkImage(url: URL(string: urlImagem))
                                    .scaledToFit()
                                    .frame(width: SGConvenience.deviceWidth - 32, height: 80, alignment: .center)
                                    .cornerRadius(8)
                            }
                            HStack {
                                Text("Serie " + String(dadosSerie.ordem ?? 1)).font(.custom("Poppins-SemiBold", size: 16))
                                Spacer()
                                HStack {
                                    Image(systemName: "clock").font(.system(size: 14))
                                    Text(String(dadosSerie.descanso ?? "0") + "”s").font(.custom("Poppins-Regular", size: 14))
                                }
                                .padding(EdgeInsets(top: 2, leading: 4, bottom: 2, trailing: 4))
                                .background(Color(Color(hex: 0xff1C1C1E)))
                                .cornerRadius(6)
                            }.padding(EdgeInsets(top: 8, leading: 0, bottom: 0, trailing: 0))
                            VStack(alignment: .leading) {
                                DetalhesSerieComponent(imagem: "repeticoes_execucoes", valorNoCampo: String(dadosSerie.repeticaoApp ?? "0"), subtitulo: "Repetições")
                                DetalhesSerieComponent(imagem: "carga_execucoes", valorNoCampo: String(dadosSerie.cargaApp ?? "0"), subtitulo: "Carga")
                                DetalhesSerieComponent(imagem: "cadencia_execucoes", valorNoCampo: String(dadosSerie.cadencia ?? "0"), subtitulo: "Cadência")
                            }.padding(EdgeInsets(top: 4, leading: 0, bottom: 16, trailing: 0))
                            Button(action: {
                                DispatchQueue.main.async{
                                    if !(dadosSerie.concluida ?? false) {
                                        for atividades in watchViewModel.ficha.atividades!.indices {
                                            for index in watchViewModel.ficha.atividades![atividades].series!.indices {
                                                if (watchViewModel.ficha.atividades![atividades].series![index].codSerie == dadosSerie.codSerie) {
                                                    watchViewModel.ficha.atividades![atividades].series![index].concluida = true
                                                    self.remainingTime = converterDescansoStringParaInt(descanso: watchViewModel.ficha.atividades![atividades].series![index].descanso ?? "0")
                                                    self.descansoTotal = converterDescansoStringParaCGFloat(descanso: watchViewModel.ficha.atividades![atividades].series![index].descanso ?? "0")
                                                    watchViewModel.ficha.indexSelecionado = atividades;
                                                    watchViewModel.ficha.indexExercicioEmExecucao = atividades;
                                                    watchViewModel.ficha.indexSerieExercicioEmExecucao = index;
                                                    break
                                                }
                                            }
                                        }
                                        exibirContador = true;
                                        watchViewModel.sendDataMessage(for: .receberFichaEmExecucaoWatch)
                                    } else {
                                        exibirContador = false;
                                        watchViewModel.sendDataMessage(for: .receberFichaEmExecucaoWatch)
                                    }
                                }
                                
                            }, label: {
                                if (dadosSerie.concluida ?? false) {
                                    HStack {
                                        Image(systemName: "checkmark.circle.fill").font(.system(size: 16)).foregroundColor(validarCor())
                                        Text("Série concluída").font(.custom("Poppins-Medium", size: 16)).foregroundColor(validarCor())
                                    }
                                } else {
                                    Text("Concluir série").font(.custom("Poppins-Medium", size: 16))
                                }
                                
                            })
                        }._automaticPadding()
                    }
                }.containerBackground(Color.accentColor.gradient, for: .tabView)
            }.tabViewStyle(.verticalPage)
            if (exibirContador) {
                contadorDescanso
            } else {
                Text("")
            }
        }
    }
    
    func converterDescansoStringParaCGFloat(descanso: String) -> CGFloat {
        return CGFloat(integerLiteral: Int(descanso)!)
    }
    
    func converterDescansoStringParaInt(descanso: String) -> Int {
        return Int(descanso)!
    }
    
    var contadorDescanso : some View {
        return ZStack {
            Circle()
                .stroke(gradientBg, style: StrokeStyle(lineWidth: 16, lineCap: .round, lineJoin: .round))
                .foregroundColor(.gray)
            Circle()
                .trim(from: 0, to: animationProgress)
                .stroke(gradient, style: StrokeStyle(lineWidth: 20, lineCap: .round, lineJoin: .round))
                .rotationEffect(.degrees(-90))
                .animation(Animation.easeInOut(duration: 1.5))
            
            VStack {
                Text("\(remainingTime)")
                    .font(.custom("Poppins-SemiBold", size: 50))
                    .foregroundColor(.white)
                    .animation(Animation.easeInOut(duration: 0))
                Text("Descanso").font(.custom("Poppins-Regular", size: 12)).padding(EdgeInsets(top: -26, leading: 0, bottom: 0, trailing: 0))
            }
        }
        .navigationBarHidden(true)
        .navigationViewStyle(StackNavigationViewStyle())
        ._automaticPadding()
        .navigationBarBackButtonHidden(true)
        .frame(maxWidth: .infinity)
        .background(Color(Color.black))
        .onReceive(timer) { _ in
            withAnimation {
                if remainingTime > 0 {
                    remainingTime -= 1
                }
                if self.animationProgress >= 1 {
                    WKInterfaceDevice.current().play(.success)
                    timer.upstream.connect().cancel()
                    self.exibirContador = false;
                    self.animationProgress = 0;
                } else {
                    self.animationProgress += 1/descansoTotal
                }
            }
        }
    }
}

//
//struct TelaTreino_Previews: PreviewProvider {
//    static var previews: some View {
//        DetalhesSerieView(serie: [Series(), Series(), Series()], urlImagem: "")
//    }
//}




class SGConvenience{
#if os(watchOS)
    static var deviceWidth:CGFloat = WKInterfaceDevice.current().screenBounds.size.width
    static var deviceHeight:CGFloat = WKInterfaceDevice.current().screenBounds.size.height
#elseif os(iOS)
    static var deviceWidth:CGFloat = UIScreen.main.bounds.size.width
#elseif os(macOS)
    static var deviceWidth:CGFloat? = NSScreen.main?.visibleFrame.size.width // You could implement this to force a CGFloat and get the full device screen size width regardless of the window size with .frame.size.width
#endif
}
