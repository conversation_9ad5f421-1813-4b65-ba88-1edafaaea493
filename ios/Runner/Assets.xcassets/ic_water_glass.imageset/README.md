# Ícones iOS - Copo d'Água

Para que os ícones funcionem corretamente no iOS, você precisa adicionar as seguintes imagens PNG nesta pasta:

## Arquivos necessários:

1. **ic_water_glass.png** (35x35 pixels)
2. **<EMAIL>** (70x70 pixels)
3. **<EMAIL>** (105x105 pixels)

## Como criar os ícones:

1. Use um editor de imagens (Photoshop, Figma, Sketch, etc.)
2. Crie um ícone de copo com água azul (#2196F3)
3. Fundo transparente
4. Exporte nas 3 resoluções mencionadas acima
5. Substitua este arquivo README.md pelos arquivos PNG

## Exemplo de design:
- Copo estilizado com água
- Cor azul (#2196F3)
- Contorno suave
- Fundo transparente

## Alternativa rápida:
Você pode usar ícones do SF Symbols (sistema do iOS):
- No código Swift, use: `UIApplicationShortcutIcon(systemImageName: "cup.and.saucer.fill")`
- Isso eliminaria a necessidade de criar imagens personalizadas
