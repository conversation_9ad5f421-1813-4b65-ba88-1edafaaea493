# Ícones iOS - Gota d'Água

Para que os ícones funcionem corretamente no iOS, você precisa adicionar as seguintes imagens PNG nesta pasta:

## Arquivos necessários:

1. **ic_water_drop.png** (35x35 pixels)
2. **<EMAIL>** (70x70 pixels) 
3. **<EMAIL>** (105x105 pixels)

## Como criar os ícones:

1. Use um editor de imagens (Photoshop, Figma, Sketch, etc.)
2. Crie um ícone de gota d'água azul (#2196F3)
3. Fundo transparente
4. Exporte nas 3 resoluções mencionadas acima
5. Substitua este arquivo README.md pelos arquivos PNG

## Exemplo de design:
- Gota d'água estilizada
- Cor azul (#2196F3)
- Contorno suave
- Fundo transparente

## Alternativa rápida:
Você pode usar ícones do SF Symbols (sistema do iOS):
- No código Swift, use: `UIApplicationShortcutIcon(systemImageName: "drop.fill")`
- Isso eliminaria a necessidade de criar imagens personalizadas
