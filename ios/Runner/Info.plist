<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>$(DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.977449544580-20enbao79q2lhks3j0rlqakf04cf9km0</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb1878103395822612</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FLTEnableImpeller</key>
	<true/>
	<key>FacebookAdvertiserIDCollectionEnabled</key>
	<string>TRUE</string>
	<key>FacebookAppID</key>
	<string>1878103395822612</string>
	<key>FacebookAutoLogAppEvents</key>
	<string>TRUE</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>Treino</string>
	<key>FirebaseDeepLinkPasteboardRetrievalEnabled</key>
	<false/>
	<key>GADApplicationIdentifier</key>
	<string>$(ADMOB_ID)</string>
	<key>GIDClientID</key>
	<string>977449544580-20enbao79q2lhks3j0rlqakf04cf9km0.apps.googleusercontent.com</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
		<string>whatsapp</string>
		<string>sensorialmoove</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>pactosolucoes.com.br</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSCalendarsUsageDescription</key>
	<string>Nós precisamos do acesso ao calendário para adicionar seus eventos marcados.</string>
	<key>NSCameraUsageDescription</key>
	<string>Nós precisamos do acesso à sua câmera para tirar fotos para o Feed (Publicação).</string>
	<key>NSHealthShareUsageDescription</key>
	<string>Nós precisamos do acesso ao app saúde para pegar a quantidade de passos diários.</string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>Nós precisamos do acesso ao app saúde para atualizar a quantidade de passos e calorias.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Nós precisamos do acesso ao microfone para a gravação de vídeos para as publicações no feed.</string>
	<key>NSMotionUsageDescription</key>
	<string>Este aplicativo precisa ser capaz de acessar o seu uso de movimento.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Nós precisamos de permissão para salvar a imagem em sua biblioteca de fotos.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Nós precisamos do acesso à sua biblioteca de fotos para publicar e salvar imagens do Feed.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>Nós precisamos do acesso à coleta de dados para aprimorar a sua experiência com o aplicativo, tais como, capturar as falhas, personalização de recursos e melhorias no premium e nutrição.  </string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>O aplicativo precisa acessar o Bluetooth para se conectar a dispositivos.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>O aplicativo precisa acessar o Bluetooth para se conectar a dispositivos.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>O aplicativo precisa acessar sua localização para escanear dispositivos Bluetooth.</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>bluetooth-central</string>
		<string>remote-notification</string>
	</array>
	<key>UIApplicationSupportsAppBadge</key>
	<false/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>WKBackgroundModes</key>
	<array>
		<string>workout-processing</string>
	</array>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
</dict>
</plist>
