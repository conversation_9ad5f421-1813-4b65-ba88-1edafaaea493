@echo off
echo ========================================
echo INSTALACAO APPIUM PARA WINDOWS
echo ========================================

echo.
echo 1. Verificando Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERRO: Node.js nao encontrado!
    echo Baixe e instale: https://nodejs.org/
    pause
    exit /b 1
)

echo.
echo 2. Verificando npm...
npm --version
if %errorlevel% neq 0 (
    echo ERRO: npm nao encontrado!
    pause
    exit /b 1
)

echo.
echo 3. Limpando cache do npm...
npm cache clean --force

echo.
echo 4. Desinstalando Appium anterior...
npm uninstall -g appium

echo.
echo 5. Instalando Appium...
npm install -g appium@latest
if %errorlevel% neq 0 (
    echo ERRO: Falha na instalacao do Appium
    echo Tentando com --force...
    npm install -g appium@latest --force
)

echo.
echo 6. Verificando instalacao do Appium...
appium --version
if %errorlevel% neq 0 (
    echo ERRO: Appium nao foi instalado corretamente
    pause
    exit /b 1
)

echo.
echo 7. Instalando drivers...
appium driver install uiautomator2
appium driver install xcuitest

echo.
echo 8. Listando drivers instalados...
appium driver list

echo.
echo ========================================
echo INSTALACAO CONCLUIDA!
echo ========================================
echo.
echo Para testar, execute:
echo appium --port 4723
echo.
pause
